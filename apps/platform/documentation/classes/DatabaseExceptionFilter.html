<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content class">
                   <div class="content-data">












<ol class="breadcrumb">
  <li class="breadcrumb-item">Classes</li>
  <li class="breadcrumb-item" >DatabaseExceptionFilter</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/filters/database-exception.filter.ts</code>
        </p>




            <p class="comment">
                <h3>Implements</h3>
            </p>
            <p class="comment">
                        <code>ExceptionFilter</code>
            </p>


            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Readonly</span>
                                <a href="#defaultErrorConfig" >defaultErrorConfig</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Readonly</span>
                                <a href="#errorConfigs" >errorConfigs</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Readonly</span>
                                <a href="#postgresErrors" >postgresErrors</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#buildErrorResponse" >buildErrorResponse</a>
                            </li>
                            <li>
                                <a href="#catch" >catch</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#getErrorConfig" >getErrorConfig</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>


            <section data-compodoc="block-properties">
    
    <h3 id="inputs">
        Properties
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="defaultErrorConfig"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Readonly</span>
                        <span ><b>defaultErrorConfig</b></span>
                        <a href="#defaultErrorConfig"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="../miscellaneous/typealiases.html#ErrorConfig" target="_self" >ErrorConfig</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    getMessage: () &#x3D;&gt; ({
      message: &quot;Internal server error&quot;,
    }),
  }</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="83" class="link-to-prism">src/filters/database-exception.filter.ts:83</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="errorConfigs"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Readonly</span>
                        <span ><b>errorConfigs</b></span>
                        <a href="#errorConfigs"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>new Map&lt;Function, ErrorConfig&gt;([
    [
      QueryFailedError,
      {
        status: HttpStatus.BAD_REQUEST,
        getMessage: (error: QueryFailedError) &#x3D;&gt; ({
          message: &quot;Database query failed&quot;,
          detail: error.message,
        }),
      },
    ],
    [
      EntityNotFoundError,
      {
        status: HttpStatus.NOT_FOUND,
        getMessage: (error: EntityNotFoundError) &#x3D;&gt; ({
          message: &quot;Entity not found&quot;,
          detail: error.message,
        }),
      },
    ],
    [
      CustomValidationError,
      {
        status: HttpStatus.BAD_REQUEST,
        getMessage: (error: CustomValidationError) &#x3D;&gt; ({
          message: &quot;Validation error&quot;,
          detail: error.message,
        }),
      },
    ],
  ])</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="49" class="link-to-prism">src/filters/database-exception.filter.ts:49</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="postgresErrors"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Readonly</span>
                        <span ><b>postgresErrors</b></span>
                        <a href="#postgresErrors"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="../miscellaneous/typealiases.html#PostgresErrorCode" target="_self" >PostgresErrorCode</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
    &quot;23505&quot;: {
      status: HttpStatus.CONFLICT,
      message: &quot;This resource already exists&quot;,
    },
    &quot;23503&quot;: {
      status: HttpStatus.CONFLICT,
      message: &quot;The resource you&#x27;re trying to reference does not exist&quot;,
    },
    &quot;23502&quot;: {
      status: HttpStatus.BAD_REQUEST,
      message: &quot;Required field is missing.&quot;,
    },
  }</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="32" class="link-to-prism">src/filters/database-exception.filter.ts:32</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="buildErrorResponse"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>buildErrorResponse</b></span>
                        <a href="#buildErrorResponse"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>buildErrorResponse(exception: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, config: <a href="../undefineds/ErrorConfig.html" target="_self">ErrorConfig</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="139"
                                    class="link-to-prism">src/filters/database-exception.filter.ts:139</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>exception</td>
                                            <td>
                                                            <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>config</td>
                                            <td>
                                                            <code><a href="../miscellaneous/typealiases.html#ErrorConfig" target="_self" >ErrorConfig</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../miscellaneous/typealiases.html#ErrorResponse" target="_self" >{ status: HttpStatus; response: ErrorResponse; }</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="catch"></a>
                    <span class="name">
                        <span ><b>catch</b></span>
                        <a href="#catch"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>catch(exception: <a href="../interfaces/ValidationError.html" target="_self">QueryFailedError | EntityNotFoundError | CustomValidationError</a>, host: ArgumentsHost)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="90"
                                    class="link-to-prism">src/filters/database-exception.filter.ts:90</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>exception</td>
                                            <td>
                                                            <code><a href="../interfaces/ValidationError.html" target="_self" >QueryFailedError | EntityNotFoundError | CustomValidationError</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>host</td>
                                            <td>
                                                        <code>ArgumentsHost</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getErrorConfig"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>getErrorConfig</b></span>
                        <a href="#getErrorConfig"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getErrorConfig(exception: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="112"
                                    class="link-to-prism">src/filters/database-exception.filter.ts:112</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>exception</td>
                                            <td>
                                                            <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../miscellaneous/typealiases.html#ErrorConfig" target="_self" >ErrorConfig</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>





    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpStatus,
} from &quot;@nestjs/common&quot;;
import { CustomValidationError } from &quot;@repo/thena-platform-entities&quot;;
import { FastifyReply } from &quot;fastify&quot;;
import { EntityNotFoundError, QueryFailedError } from &quot;typeorm&quot;;

// Types for error handling configuration
type ErrorResponse &#x3D; {
  message: string;
  detail?: string;
};

type ErrorConfig &#x3D; {
  status: HttpStatus;
  getMessage: (error: any) &#x3D;&gt; ErrorResponse;
};

type PostgresErrorCode &#x3D; {
  [key: string]: {
    status: HttpStatus;
    message: string;
  };
};

@Catch(QueryFailedError, EntityNotFoundError, CustomValidationError)
export class DatabaseExceptionFilter implements ExceptionFilter {
  // PostgreSQL error code mappings
  private readonly postgresErrors: PostgresErrorCode &#x3D; {
    &quot;23505&quot;: {
      status: HttpStatus.CONFLICT,
      message: &quot;This resource already exists&quot;,
    },
    &quot;23503&quot;: {
      status: HttpStatus.CONFLICT,
      message: &quot;The resource you&#x27;re trying to reference does not exist&quot;,
    },
    &quot;23502&quot;: {
      status: HttpStatus.BAD_REQUEST,
      message: &quot;Required field is missing.&quot;,
    },
  };

  // Error type configuration mappings
  // eslint-disable-next-line @typescript-eslint/no-unsafe-function-type
  private readonly errorConfigs &#x3D; new Map&lt;Function, ErrorConfig&gt;([
    [
      QueryFailedError,
      {
        status: HttpStatus.BAD_REQUEST,
        getMessage: (error: QueryFailedError) &#x3D;&gt; ({
          message: &quot;Database query failed&quot;,
          detail: error.message,
        }),
      },
    ],
    [
      EntityNotFoundError,
      {
        status: HttpStatus.NOT_FOUND,
        getMessage: (error: EntityNotFoundError) &#x3D;&gt; ({
          message: &quot;Entity not found&quot;,
          detail: error.message,
        }),
      },
    ],
    [
      CustomValidationError,
      {
        status: HttpStatus.BAD_REQUEST,
        getMessage: (error: CustomValidationError) &#x3D;&gt; ({
          message: &quot;Validation error&quot;,
          detail: error.message,
        }),
      },
    ],
  ]);

  // Default error configuration
  private readonly defaultErrorConfig: ErrorConfig &#x3D; {
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    getMessage: () &#x3D;&gt; ({
      message: &quot;Internal server error&quot;,
    }),
  };

  catch(
    exception: QueryFailedError | EntityNotFoundError | CustomValidationError,
    host: ArgumentsHost,
  ) {
    const ctx &#x3D; host.switchToHttp();
    const response &#x3D; ctx.getResponse&lt;FastifyReply&gt;();

    // Get error configuration based on exception type
    const errorConfig &#x3D; this.getErrorConfig(exception);

    // Get error response
    const error &#x3D; this.buildErrorResponse(exception, errorConfig);

    return response
      .code(error.status)
      .header(&quot;Content-Type&quot;, &quot;application/json&quot;)
      .send({
        ...error.response,
        timestamp: new Date().toISOString(),
      });
  }

  private getErrorConfig(exception: any): ErrorConfig {
    // Find matching error configuration
    for (const [ErrorType, config] of this.errorConfigs.entries()) {
      if (exception instanceof ErrorType) {
        // Handle PostgreSQL specific errors
        if (
          exception instanceof QueryFailedError &amp;&amp;
          exception.driverError &amp;&amp;
          &quot;code&quot; in exception.driverError
        ) {
          const pgError &#x3D; this.postgresErrors[exception.driverError.code];
          if (pgError) {
            return {
              status: pgError.status,
              getMessage: () &#x3D;&gt; ({
                message: pgError.message,
                detail: exception.message,
              }),
            };
          }
        }
        return config;
      }
    }
    return this.defaultErrorConfig;
  }

  private buildErrorResponse(exception: any, config: ErrorConfig) {
    const response &#x3D; config.getMessage(exception);
    return {
      status: config.status,
      response,
    };
  }
}
</code></pre>
    </div>
</div>









                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'class';
            var COMPODOC_CURRENT_PAGE_URL = 'DatabaseExceptionFilter.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
