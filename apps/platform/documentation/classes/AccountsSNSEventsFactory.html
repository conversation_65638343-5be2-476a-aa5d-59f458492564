<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content class">
                   <div class="content-data">












<ol class="breadcrumb">
  <li class="breadcrumb-item">Classes</li>
  <li class="breadcrumb-item" >AccountsSNSEventsFactory</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/accounts/events/accounts-sns-events.factory.ts</code>
        </p>






            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#baseAccountsSNSEvent" >baseAccountsSNSEvent</a>
                            </li>
                            <li>
                                <a href="#createAccountActivityCreatedSNSEvent" >createAccountActivityCreatedSNSEvent</a>
                            </li>
                            <li>
                                <a href="#createAccountActivityDeletedSNSEvent" >createAccountActivityDeletedSNSEvent</a>
                            </li>
                            <li>
                                <a href="#createAccountActivityUpdatedSNSEvent" >createAccountActivityUpdatedSNSEvent</a>
                            </li>
                            <li>
                                <a href="#createAccountCreatedSNSEvent" >createAccountCreatedSNSEvent</a>
                            </li>
                            <li>
                                <a href="#createAccountDeletedSNSEvent" >createAccountDeletedSNSEvent</a>
                            </li>
                            <li>
                                <a href="#createAccountNoteCreatedSNSEvent" >createAccountNoteCreatedSNSEvent</a>
                            </li>
                            <li>
                                <a href="#createAccountNoteDeletedSNSEvent" >createAccountNoteDeletedSNSEvent</a>
                            </li>
                            <li>
                                <a href="#createAccountNoteUpdatedSNSEvent" >createAccountNoteUpdatedSNSEvent</a>
                            </li>
                            <li>
                                <a href="#createAccountRelationshipCreatedSNSEvent" >createAccountRelationshipCreatedSNSEvent</a>
                            </li>
                            <li>
                                <a href="#createAccountRelationshipDeletedSNSEvent" >createAccountRelationshipDeletedSNSEvent</a>
                            </li>
                            <li>
                                <a href="#createAccountRelationshipUpdatedSNSEvent" >createAccountRelationshipUpdatedSNSEvent</a>
                            </li>
                            <li>
                                <a href="#createAccountTaskCreatedSNSEvent" >createAccountTaskCreatedSNSEvent</a>
                            </li>
                            <li>
                                <a href="#createAccountTaskDeletedSNSEvent" >createAccountTaskDeletedSNSEvent</a>
                            </li>
                            <li>
                                <a href="#createAccountTaskUpdatedSNSEvent" >createAccountTaskUpdatedSNSEvent</a>
                            </li>
                            <li>
                                <a href="#createAccountUpdatedSNSEvent" >createAccountUpdatedSNSEvent</a>
                            </li>
                            <li>
                                <a href="#createCustomerContactCreatedSNSEvent" >createCustomerContactCreatedSNSEvent</a>
                            </li>
                            <li>
                                <a href="#createCustomerContactDeletedSNSEvent" >createCustomerContactDeletedSNSEvent</a>
                            </li>
                            <li>
                                <a href="#createCustomerContactUpdatedSNSEvent" >createCustomerContactUpdatedSNSEvent</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>



            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="baseAccountsSNSEvent"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>baseAccountsSNSEvent</b></span>
                        <a href="#baseAccountsSNSEvent"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>baseAccountsSNSEvent(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, payload: T, eventType: AccountEvents)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="29"
                                    class="link-to-prism">src/accounts/events/accounts-sns-events.factory.ts:29</a></div>
                        </td>
                    </tr>

                    <tr>
                        <td class="col-md-4">
                            <b>Type parameters :</b>
                            <ul class="type-parameters">
                                    <li>T</li>
                            </ul>
                        </td>
                    </tr>

            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>payload</td>
                                            <td>
                                                        <code>T</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>eventType</td>
                                            <td>
                                                        <code>AccountEvents</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>SNSEvent&lt;T&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createAccountActivityCreatedSNSEvent"></a>
                    <span class="name">
                        <span ><b>createAccountActivityCreatedSNSEvent</b></span>
                        <a href="#createAccountActivityCreatedSNSEvent"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>createAccountActivityCreatedSNSEvent(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, activity: AccountActivity)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="144"
                                    class="link-to-prism">src/accounts/events/accounts-sns-events.factory.ts:144</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>activity</td>
                                            <td>
                                                        <code>AccountActivity</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>SNSEvent&lt;AccountActivityPayload&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createAccountActivityDeletedSNSEvent"></a>
                    <span class="name">
                        <span ><b>createAccountActivityDeletedSNSEvent</b></span>
                        <a href="#createAccountActivityDeletedSNSEvent"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>createAccountActivityDeletedSNSEvent(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, activity: AccountActivity)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="176"
                                    class="link-to-prism">src/accounts/events/accounts-sns-events.factory.ts:176</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>activity</td>
                                            <td>
                                                        <code>AccountActivity</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>SNSEvent&lt;AccountActivityPayload&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createAccountActivityUpdatedSNSEvent"></a>
                    <span class="name">
                        <span ><b>createAccountActivityUpdatedSNSEvent</b></span>
                        <a href="#createAccountActivityUpdatedSNSEvent"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>createAccountActivityUpdatedSNSEvent(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, previousActivity: AccountActivity, activity: AccountActivity)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="159"
                                    class="link-to-prism">src/accounts/events/accounts-sns-events.factory.ts:159</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>previousActivity</td>
                                            <td>
                                                        <code>AccountActivity</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>activity</td>
                                            <td>
                                                        <code>AccountActivity</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>SNSEvent&lt;AccountActivityPayload&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createAccountCreatedSNSEvent"></a>
                    <span class="name">
                        <span ><b>createAccountCreatedSNSEvent</b></span>
                        <a href="#createAccountCreatedSNSEvent"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>createAccountCreatedSNSEvent(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, account: Account)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="48"
                                    class="link-to-prism">src/accounts/events/accounts-sns-events.factory.ts:48</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>account</td>
                                            <td>
                                                        <code>Account</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>SNSEvent&lt;AccountPayload&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createAccountDeletedSNSEvent"></a>
                    <span class="name">
                        <span ><b>createAccountDeletedSNSEvent</b></span>
                        <a href="#createAccountDeletedSNSEvent"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>createAccountDeletedSNSEvent(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, account: Account)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="80"
                                    class="link-to-prism">src/accounts/events/accounts-sns-events.factory.ts:80</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>account</td>
                                            <td>
                                                        <code>Account</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>SNSEvent&lt;AccountPayload&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createAccountNoteCreatedSNSEvent"></a>
                    <span class="name">
                        <span ><b>createAccountNoteCreatedSNSEvent</b></span>
                        <a href="#createAccountNoteCreatedSNSEvent"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>createAccountNoteCreatedSNSEvent(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, note: AccountNote)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="191"
                                    class="link-to-prism">src/accounts/events/accounts-sns-events.factory.ts:191</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>note</td>
                                            <td>
                                                        <code>AccountNote</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>SNSEvent&lt;AccountNotePayload&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createAccountNoteDeletedSNSEvent"></a>
                    <span class="name">
                        <span ><b>createAccountNoteDeletedSNSEvent</b></span>
                        <a href="#createAccountNoteDeletedSNSEvent"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>createAccountNoteDeletedSNSEvent(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, note: AccountNote)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="223"
                                    class="link-to-prism">src/accounts/events/accounts-sns-events.factory.ts:223</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>note</td>
                                            <td>
                                                        <code>AccountNote</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>SNSEvent&lt;AccountNotePayload&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createAccountNoteUpdatedSNSEvent"></a>
                    <span class="name">
                        <span ><b>createAccountNoteUpdatedSNSEvent</b></span>
                        <a href="#createAccountNoteUpdatedSNSEvent"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>createAccountNoteUpdatedSNSEvent(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, previousNote: AccountNote, note: AccountNote)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="206"
                                    class="link-to-prism">src/accounts/events/accounts-sns-events.factory.ts:206</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>previousNote</td>
                                            <td>
                                                        <code>AccountNote</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>note</td>
                                            <td>
                                                        <code>AccountNote</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>SNSEvent&lt;AccountNotePayload&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createAccountRelationshipCreatedSNSEvent"></a>
                    <span class="name">
                        <span ><b>createAccountRelationshipCreatedSNSEvent</b></span>
                        <a href="#createAccountRelationshipCreatedSNSEvent"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>createAccountRelationshipCreatedSNSEvent(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, relationship: AccountRelationship)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="95"
                                    class="link-to-prism">src/accounts/events/accounts-sns-events.factory.ts:95</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>relationship</td>
                                            <td>
                                                        <code>AccountRelationship</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>SNSEvent&lt;AccountRelationshipPayload&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createAccountRelationshipDeletedSNSEvent"></a>
                    <span class="name">
                        <span ><b>createAccountRelationshipDeletedSNSEvent</b></span>
                        <a href="#createAccountRelationshipDeletedSNSEvent"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>createAccountRelationshipDeletedSNSEvent(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, relationship: AccountRelationship)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="128"
                                    class="link-to-prism">src/accounts/events/accounts-sns-events.factory.ts:128</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>relationship</td>
                                            <td>
                                                        <code>AccountRelationship</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>SNSEvent&lt;AccountRelationshipPayload&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createAccountRelationshipUpdatedSNSEvent"></a>
                    <span class="name">
                        <span ><b>createAccountRelationshipUpdatedSNSEvent</b></span>
                        <a href="#createAccountRelationshipUpdatedSNSEvent"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>createAccountRelationshipUpdatedSNSEvent(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, previousRelationship: AccountRelationship, relationship: AccountRelationship)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="110"
                                    class="link-to-prism">src/accounts/events/accounts-sns-events.factory.ts:110</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>previousRelationship</td>
                                            <td>
                                                        <code>AccountRelationship</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>relationship</td>
                                            <td>
                                                        <code>AccountRelationship</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>SNSEvent&lt;AccountRelationshipPayload&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createAccountTaskCreatedSNSEvent"></a>
                    <span class="name">
                        <span ><b>createAccountTaskCreatedSNSEvent</b></span>
                        <a href="#createAccountTaskCreatedSNSEvent"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>createAccountTaskCreatedSNSEvent(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, task: AccountTask)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="238"
                                    class="link-to-prism">src/accounts/events/accounts-sns-events.factory.ts:238</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>task</td>
                                            <td>
                                                        <code>AccountTask</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>SNSEvent&lt;AccountTaskPayload&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createAccountTaskDeletedSNSEvent"></a>
                    <span class="name">
                        <span ><b>createAccountTaskDeletedSNSEvent</b></span>
                        <a href="#createAccountTaskDeletedSNSEvent"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>createAccountTaskDeletedSNSEvent(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, task: AccountTask)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="270"
                                    class="link-to-prism">src/accounts/events/accounts-sns-events.factory.ts:270</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>task</td>
                                            <td>
                                                        <code>AccountTask</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>SNSEvent&lt;AccountTaskPayload&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createAccountTaskUpdatedSNSEvent"></a>
                    <span class="name">
                        <span ><b>createAccountTaskUpdatedSNSEvent</b></span>
                        <a href="#createAccountTaskUpdatedSNSEvent"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>createAccountTaskUpdatedSNSEvent(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, previousTask: AccountTask, task: AccountTask)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="253"
                                    class="link-to-prism">src/accounts/events/accounts-sns-events.factory.ts:253</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>previousTask</td>
                                            <td>
                                                        <code>AccountTask</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>task</td>
                                            <td>
                                                        <code>AccountTask</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>SNSEvent&lt;AccountTaskPayload&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createAccountUpdatedSNSEvent"></a>
                    <span class="name">
                        <span ><b>createAccountUpdatedSNSEvent</b></span>
                        <a href="#createAccountUpdatedSNSEvent"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>createAccountUpdatedSNSEvent(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, previousAccount: Account, account: Account)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="63"
                                    class="link-to-prism">src/accounts/events/accounts-sns-events.factory.ts:63</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>previousAccount</td>
                                            <td>
                                                        <code>Account</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>account</td>
                                            <td>
                                                        <code>Account</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>SNSEvent&lt;AccountPayload&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createCustomerContactCreatedSNSEvent"></a>
                    <span class="name">
                        <span ><b>createCustomerContactCreatedSNSEvent</b></span>
                        <a href="#createCustomerContactCreatedSNSEvent"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>createCustomerContactCreatedSNSEvent(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, customerContact: <a href="../interfaces/Customer.html" target="_self">CustomerContact</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="285"
                                    class="link-to-prism">src/accounts/events/accounts-sns-events.factory.ts:285</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>customerContact</td>
                                            <td>
                                                            <code><a href="../interfaces/Customer.html" target="_self" >CustomerContact</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>SNSEvent&lt;CustomerContactPayload&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createCustomerContactDeletedSNSEvent"></a>
                    <span class="name">
                        <span ><b>createCustomerContactDeletedSNSEvent</b></span>
                        <a href="#createCustomerContactDeletedSNSEvent"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>createCustomerContactDeletedSNSEvent(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, customerContact: <a href="../interfaces/Customer.html" target="_self">CustomerContact</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="319"
                                    class="link-to-prism">src/accounts/events/accounts-sns-events.factory.ts:319</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>customerContact</td>
                                            <td>
                                                            <code><a href="../interfaces/Customer.html" target="_self" >CustomerContact</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>SNSEvent&lt;CustomerContactPayload&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createCustomerContactUpdatedSNSEvent"></a>
                    <span class="name">
                        <span ><b>createCustomerContactUpdatedSNSEvent</b></span>
                        <a href="#createCustomerContactUpdatedSNSEvent"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>createCustomerContactUpdatedSNSEvent(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, previousCustomerContact: <a href="../interfaces/Customer.html" target="_self">CustomerContact</a>, customerContact: <a href="../interfaces/Customer.html" target="_self">CustomerContact</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="300"
                                    class="link-to-prism">src/accounts/events/accounts-sns-events.factory.ts:300</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>previousCustomerContact</td>
                                            <td>
                                                            <code><a href="../interfaces/Customer.html" target="_self" >CustomerContact</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>customerContact</td>
                                            <td>
                                                            <code><a href="../interfaces/Customer.html" target="_self" >CustomerContact</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>SNSEvent&lt;CustomerContactPayload&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>





    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { AccountEvents } from &quot;@repo/thena-eventbridge&quot;;
import {
  Account,
  AccountActivity,
  AccountNote,
  AccountRelationship,
  AccountTask,
  CustomerContact,
} from &quot;@repo/thena-platform-entities&quot;;
import { v4 as uuidv4 } from &quot;uuid&quot;;
import { CurrentUser } from &quot;../../common/decorators/user.decorator&quot;;
import { AccountActivityResponseDto } from &quot;../dtos/response/account-activity.dto&quot;;
import { AccountNoteResponseDto } from &quot;../dtos/response/account-note.dto&quot;;
import { AccountRelationshipResponseDto } from &quot;../dtos/response/account-relationship.dto&quot;;
import { AccountTaskResponseDto } from &quot;../dtos/response/account-task.dto&quot;;
import { AccountResponseDto } from &quot;../dtos/response/account.dto&quot;;
import { CustomerContactResponseDto } from &quot;../dtos/response/customer-contact.dto&quot;;
import {
  AccountActivityPayload,
  AccountNotePayload,
  AccountPayload,
  AccountRelationshipPayload,
  AccountTaskPayload,
  CustomerContactPayload,
  SNSEvent,
} from &quot;../interfaces/sns-events.interface&quot;;

export class AccountsSNSEventsFactory {
  private baseAccountsSNSEvent&lt;T&gt;(
    user: CurrentUser,
    payload: T,
    eventType: AccountEvents,
  ): SNSEvent&lt;T&gt; {
    return {
      eventId: uuidv4(),
      eventType,
      timestamp: new Date().toISOString(),
      orgId: user.orgUid,
      actor: {
        id: user.uid,
        email: user.email,
        type: user.userType,
      },
      payload,
    };
  }

  createAccountCreatedSNSEvent(
    user: CurrentUser,
    account: Account,
  ): SNSEvent&lt;AccountPayload&gt; {
    const accountPayload &#x3D; {
      account: AccountResponseDto.fromEntity(account),
    } as AccountPayload;

    return this.baseAccountsSNSEvent(
      user,
      accountPayload,
      AccountEvents.ACCOUNT_CREATED,
    );
  }

  createAccountUpdatedSNSEvent(
    user: CurrentUser,
    previousAccount: Account,
    account: Account,
  ): SNSEvent&lt;AccountPayload&gt; {
    const accountPayload &#x3D; {
      account: AccountResponseDto.fromEntity(account),
      previousAccount: AccountResponseDto.fromEntity(previousAccount),
    } as AccountPayload;

    return this.baseAccountsSNSEvent(
      user,
      accountPayload,
      AccountEvents.ACCOUNT_UPDATED,
    );
  }

  createAccountDeletedSNSEvent(
    user: CurrentUser,
    account: Account,
  ): SNSEvent&lt;AccountPayload&gt; {
    const accountPayload &#x3D; {
      previousAccount: AccountResponseDto.fromEntity(account),
    } as AccountPayload;

    return this.baseAccountsSNSEvent(
      user,
      accountPayload,
      AccountEvents.ACCOUNT_DELETED,
    );
  }

  createAccountRelationshipCreatedSNSEvent(
    user: CurrentUser,
    relationship: AccountRelationship,
  ): SNSEvent&lt;AccountRelationshipPayload&gt; {
    const relationshipPayload &#x3D; {
      relationship: AccountRelationshipResponseDto.fromEntity(relationship),
    } as AccountRelationshipPayload;

    return this.baseAccountsSNSEvent(
      user,
      relationshipPayload,
      AccountEvents.ACCOUNT_RELATIONSHIP_CREATED,
    );
  }

  createAccountRelationshipUpdatedSNSEvent(
    user: CurrentUser,
    previousRelationship: AccountRelationship,
    relationship: AccountRelationship,
  ): SNSEvent&lt;AccountRelationshipPayload&gt; {
    const relationshipPayload &#x3D; {
      relationship: AccountRelationshipResponseDto.fromEntity(relationship),
      previousRelationship:
        AccountRelationshipResponseDto.fromEntity(previousRelationship),
    } as AccountRelationshipPayload;

    return this.baseAccountsSNSEvent(
      user,
      relationshipPayload,
      AccountEvents.ACCOUNT_RELATIONSHIP_UPDATED,
    );
  }

  createAccountRelationshipDeletedSNSEvent(
    user: CurrentUser,
    relationship: AccountRelationship,
  ): SNSEvent&lt;AccountRelationshipPayload&gt; {
    const relationshipPayload &#x3D; {
      previousRelationship:
        AccountRelationshipResponseDto.fromEntity(relationship),
    } as AccountRelationshipPayload;

    return this.baseAccountsSNSEvent(
      user,
      relationshipPayload,
      AccountEvents.ACCOUNT_RELATIONSHIP_DELETED,
    );
  }

  createAccountActivityCreatedSNSEvent(
    user: CurrentUser,
    activity: AccountActivity,
  ): SNSEvent&lt;AccountActivityPayload&gt; {
    const activityPayload &#x3D; {
      activity: AccountActivityResponseDto.fromEntity(activity),
    } as AccountActivityPayload;

    return this.baseAccountsSNSEvent(
      user,
      activityPayload,
      AccountEvents.ACCOUNT_ACTIVITY_CREATED,
    );
  }

  createAccountActivityUpdatedSNSEvent(
    user: CurrentUser,
    previousActivity: AccountActivity,
    activity: AccountActivity,
  ): SNSEvent&lt;AccountActivityPayload&gt; {
    const activityPayload &#x3D; {
      activity: AccountActivityResponseDto.fromEntity(activity),
      previousActivity: AccountActivityResponseDto.fromEntity(previousActivity),
    } as AccountActivityPayload;

    return this.baseAccountsSNSEvent(
      user,
      activityPayload,
      AccountEvents.ACCOUNT_ACTIVITY_UPDATED,
    );
  }

  createAccountActivityDeletedSNSEvent(
    user: CurrentUser,
    activity: AccountActivity,
  ): SNSEvent&lt;AccountActivityPayload&gt; {
    const activityPayload &#x3D; {
      previousActivity: AccountActivityResponseDto.fromEntity(activity),
    } as AccountActivityPayload;

    return this.baseAccountsSNSEvent(
      user,
      activityPayload,
      AccountEvents.ACCOUNT_ACTIVITY_DELETED,
    );
  }

  createAccountNoteCreatedSNSEvent(
    user: CurrentUser,
    note: AccountNote,
  ): SNSEvent&lt;AccountNotePayload&gt; {
    const notePayload &#x3D; {
      note: AccountNoteResponseDto.fromEntity(note),
    } as AccountNotePayload;

    return this.baseAccountsSNSEvent(
      user,
      notePayload,
      AccountEvents.ACCOUNT_NOTE_CREATED,
    );
  }

  createAccountNoteUpdatedSNSEvent(
    user: CurrentUser,
    previousNote: AccountNote,
    note: AccountNote,
  ): SNSEvent&lt;AccountNotePayload&gt; {
    const notePayload &#x3D; {
      note: AccountNoteResponseDto.fromEntity(note),
      previousNote: AccountNoteResponseDto.fromEntity(previousNote),
    } as AccountNotePayload;

    return this.baseAccountsSNSEvent(
      user,
      notePayload,
      AccountEvents.ACCOUNT_NOTE_UPDATED,
    );
  }

  createAccountNoteDeletedSNSEvent(
    user: CurrentUser,
    note: AccountNote,
  ): SNSEvent&lt;AccountNotePayload&gt; {
    const notePayload &#x3D; {
      previousNote: AccountNoteResponseDto.fromEntity(note),
    } as AccountNotePayload;

    return this.baseAccountsSNSEvent(
      user,
      notePayload,
      AccountEvents.ACCOUNT_NOTE_DELETED,
    );
  }

  createAccountTaskCreatedSNSEvent(
    user: CurrentUser,
    task: AccountTask,
  ): SNSEvent&lt;AccountTaskPayload&gt; {
    const taskPayload &#x3D; {
      task: AccountTaskResponseDto.fromEntity(task),
    } as AccountTaskPayload;

    return this.baseAccountsSNSEvent(
      user,
      taskPayload,
      AccountEvents.ACCOUNT_TASK_CREATED,
    );
  }

  createAccountTaskUpdatedSNSEvent(
    user: CurrentUser,
    previousTask: AccountTask,
    task: AccountTask,
  ): SNSEvent&lt;AccountTaskPayload&gt; {
    const taskPayload &#x3D; {
      task: AccountTaskResponseDto.fromEntity(task),
      previousTask: AccountTaskResponseDto.fromEntity(previousTask),
    } as AccountTaskPayload;

    return this.baseAccountsSNSEvent(
      user,
      taskPayload,
      AccountEvents.ACCOUNT_TASK_UPDATED,
    );
  }

  createAccountTaskDeletedSNSEvent(
    user: CurrentUser,
    task: AccountTask,
  ): SNSEvent&lt;AccountTaskPayload&gt; {
    const taskPayload &#x3D; {
      previousTask: AccountTaskResponseDto.fromEntity(task),
    } as AccountTaskPayload;

    return this.baseAccountsSNSEvent(
      user,
      taskPayload,
      AccountEvents.ACCOUNT_TASK_DELETED,
    );
  }

  createCustomerContactCreatedSNSEvent(
    user: CurrentUser,
    customerContact: CustomerContact,
  ): SNSEvent&lt;CustomerContactPayload&gt; {
    const customerContactPayload &#x3D; {
      customerContact: CustomerContactResponseDto.fromEntity(customerContact),
    } as CustomerContactPayload;

    return this.baseAccountsSNSEvent(
      user,
      customerContactPayload,
      AccountEvents.CUSTOMER_CONTACT_CREATED,
    );
  }

  createCustomerContactUpdatedSNSEvent(
    user: CurrentUser,
    previousCustomerContact: CustomerContact,
    customerContact: CustomerContact,
  ): SNSEvent&lt;CustomerContactPayload&gt; {
    const customerContactPayload &#x3D; {
      customerContact: CustomerContactResponseDto.fromEntity(customerContact),
      previousCustomerContact: CustomerContactResponseDto.fromEntity(
        previousCustomerContact,
      ),
    } as CustomerContactPayload;

    return this.baseAccountsSNSEvent(
      user,
      customerContactPayload,
      AccountEvents.CUSTOMER_CONTACT_UPDATED,
    );
  }

  createCustomerContactDeletedSNSEvent(
    user: CurrentUser,
    customerContact: CustomerContact,
  ): SNSEvent&lt;CustomerContactPayload&gt; {
    const customerContactPayload &#x3D; {
      previousCustomerContact:
        CustomerContactResponseDto.fromEntity(customerContact),
    } as CustomerContactPayload;

    return this.baseAccountsSNSEvent(
      user,
      customerContactPayload,
      AccountEvents.CUSTOMER_CONTACT_DELETED,
    );
  }
}
</code></pre>
    </div>
</div>









                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'class';
            var COMPODOC_CURRENT_PAGE_URL = 'AccountsSNSEventsFactory.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
