<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content class">
                   <div class="content-data">












<ol class="breadcrumb">
  <li class="breadcrumb-item">Classes</li>
  <li class="breadcrumb-item" >UpdateTicketsBulkResponse</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/tickets/dto/response/ticket-bulk-response.dto.ts</code>
        </p>



            <p class="comment">
                <h3>Extends</h3>
            </p>
            <p class="comment">
                            <code><a href="../classes/ResponseMessage.html" target="_self" >ResponseMessage</a></code>
            </p>



            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                <a href="#data" >data</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                <a href="#message" >message</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                <a href="#status" >status</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                <a href="#timestamp" >timestamp</a>
                            </li>
                        </ul>
                    </td>
                </tr>






        </tbody>
    </table>
</section>


            <section data-compodoc="block-properties">
    
    <h3 id="inputs">
        Properties
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="data"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                        <span ><b>data</b></span>
                        <a href="#data"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="../classes/UpdateTicketsBulkResponseDto.html" target="_self" >UpdateTicketsBulkResponseDto</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty({description: &#x27;The tickets updated&#x27;, type: UpdateTicketsBulkResponseDto})<br />@ValidateNested()<br />@Type(undefined)<br />@Expose()<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="67" class="link-to-prism">src/tickets/dto/response/ticket-bulk-response.dto.ts:67</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="message"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                        <span ><b>message</b></span>
                        <a href="#message"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty({default: &#x27;Success&#x27;, description: &#x27;The message of the response&#x27;})<br />@Expose()<br />
                        </code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Inherited from         <code><a href="../classes/ResponseMessage.html" target="_self" >ResponseMessage</a></code>
</div>
                            </td>
                        </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in         <code><a href="../classes/ResponseMessage.html#source" target="_self" >ResponseMessage:14</a></code>
</div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="status"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                        <span ><b>status</b></span>
                        <a href="#status"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty({default: true, description: &#x27;The status of the response&#x27;})<br />@Expose()<br />
                        </code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Inherited from         <code><a href="../classes/ResponseMessage.html" target="_self" >ResponseMessage</a></code>
</div>
                            </td>
                        </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in         <code><a href="../classes/ResponseMessage.html#source" target="_self" >ResponseMessage:7</a></code>
</div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="timestamp"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                        <span ><b>timestamp</b></span>
                        <a href="#timestamp"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date" target="_blank" >Date</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty({default: &#x27;2024-01-01T00:00:00.000Z&#x27;, description: &#x27;The timestamp of the response&#x27;})<br />@Expose()<br />
                        </code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Inherited from         <code><a href="../classes/ResponseMessage.html" target="_self" >ResponseMessage</a></code>
</div>
                            </td>
                        </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in         <code><a href="../classes/ResponseMessage.html#source" target="_self" >ResponseMessage:21</a></code>
</div>
                        </td>
                    </tr>


        </tbody>
    </table>
</section>







    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { ApiProperty } from &quot;@nestjs/swagger&quot;;
import { Expose, Type } from &quot;class-transformer&quot;;
import { ValidateNested } from &quot;class-validator&quot;;
import { ResponseMessage } from &quot;../../../common/utils/response-dto.utils&quot;;

class TicketBulkResponseDto {
  @ApiProperty({
    description: &quot;The ticket id&quot;,
    type: String,
  })
  id: string;

  @ApiProperty({
    description: &quot;The ticket title&quot;,
    type: String,
  })
  title: string;
}

class SkippedTicketBulkResponseDto {
  @ApiProperty({
    description: &quot;The ticket id&quot;,
    type: String,
  })
  id: string;

  @ApiProperty({
    description: &quot;The reason for skipping the ticket&quot;,
    type: String,
  })
  reason: string;
}

class UpdateTicketsBulkResponseDto {
  @ApiProperty({
    description: &quot;The tickets updated&quot;,
    type: [TicketBulkResponseDto],
  })
  updated: TicketBulkResponseDto[];

  @ApiProperty({
    description: &quot;The tickets skipped&quot;,
    type: [SkippedTicketBulkResponseDto],
  })
  skipped: SkippedTicketBulkResponseDto[];
}

export class GetAllTicketsBulkResponse extends ResponseMessage {
  @ApiProperty({
    description: &quot;The tickets fetched&quot;,
    type: [TicketBulkResponseDto],
  })
  @ValidateNested({ each: true })
  @Type(() &#x3D;&gt; TicketBulkResponseDto)
  @Expose()
  data: TicketBulkResponseDto[];
}

export class UpdateTicketsBulkResponse extends ResponseMessage {
  @ApiProperty({
    description: &quot;The tickets updated&quot;,
    type: UpdateTicketsBulkResponseDto,
  })
  @ValidateNested()
  @Type(() &#x3D;&gt; UpdateTicketsBulkResponseDto)
  @Expose()
  data: UpdateTicketsBulkResponseDto;
}

export class CommonTicketBulkResponse extends ResponseMessage {
  @ApiProperty({
    description: &quot;The response for create/update/delete ticket operations&quot;,
    type: TicketBulkResponseDto,
  })
  @ValidateNested()
  @Type(() &#x3D;&gt; TicketBulkResponseDto)
  @Expose()
  data: TicketBulkResponseDto;
}
</code></pre>
    </div>
</div>









                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'class';
            var COMPODOC_CURRENT_PAGE_URL = 'UpdateTicketsBulkResponse.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
