<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content class">
                   <div class="content-data">












<ol class="breadcrumb">
  <li class="breadcrumb-item">Classes</li>
  <li class="breadcrumb-item" >AccountResponseDto</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/accounts/dtos/response/account.dto.ts</code>
        </p>






            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Optional</span>
                                <a href="#accountOwner" >accountOwner</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Optional</span>
                                <a href="#accountOwnerEmail" >accountOwnerEmail</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Optional</span>
                                <a href="#accountOwnerId" >accountOwnerId</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Optional</span>
                                <a href="#annualRevenue" >annualRevenue</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Optional</span>
                                <a href="#billingAddress" >billingAddress</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#classification" >classification</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#classificationId" >classificationId</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#createdAt" >createdAt</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Optional</span>
                                <a href="#customFieldValues" >customFieldValues</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Optional</span>
                                <a href="#description" >description</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Optional</span>
                                <a href="#employees" >employees</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#health" >health</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#healthId" >healthId</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#id" >id</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#industry" >industry</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#industryId" >industryId</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Optional</span>
                                <a href="#logo" >logo</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#name" >name</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#primaryDomain" >primaryDomain</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Optional</span>
                                <a href="#secondaryDomain" >secondaryDomain</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Optional</span>
                                <a href="#shippingAddress" >shippingAddress</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#source" >source</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#status" >status</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#statusId" >statusId</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#updatedAt" >updatedAt</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Optional</span>
                                <a href="#website" >website</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#fromEntity" >fromEntity</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>


            <section data-compodoc="block-properties">
    
    <h3 id="inputs">
        Properties
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="accountOwner"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Optional</span>
                        <span ><b>accountOwner</b></span>
                        <a href="#accountOwner"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiPropertyOptional({description: &#x27;Name of the account owner&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="57" class="link-to-prism">src/accounts/dtos/response/account.dto.ts:57</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="accountOwnerEmail"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Optional</span>
                        <span ><b>accountOwnerEmail</b></span>
                        <a href="#accountOwnerEmail"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiPropertyOptional({description: &#x27;Email of the account owner&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="65" class="link-to-prism">src/accounts/dtos/response/account.dto.ts:65</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="accountOwnerId"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Optional</span>
                        <span ><b>accountOwnerId</b></span>
                        <a href="#accountOwnerId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiPropertyOptional({description: &#x27;Unique identifier of the account owner&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="62" class="link-to-prism">src/accounts/dtos/response/account.dto.ts:62</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="annualRevenue"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Optional</span>
                        <span ><b>annualRevenue</b></span>
                        <a href="#annualRevenue"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiPropertyOptional({description: &#x27;Annual revenue of the account&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="68" class="link-to-prism">src/accounts/dtos/response/account.dto.ts:68</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="billingAddress"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Optional</span>
                        <span ><b>billingAddress</b></span>
                        <a href="#billingAddress"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiPropertyOptional({description: &#x27;Billing address of the account&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="77" class="link-to-prism">src/accounts/dtos/response/account.dto.ts:77</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="classification"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>classification</b></span>
                        <a href="#classification"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty({description: &#x27;The classification of the account&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="36" class="link-to-prism">src/accounts/dtos/response/account.dto.ts:36</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="classificationId"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>classificationId</b></span>
                        <a href="#classificationId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty({description: &#x27;The identifier of the classification of the account&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="33" class="link-to-prism">src/accounts/dtos/response/account.dto.ts:33</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createdAt"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>createdAt</b></span>
                        <a href="#createdAt"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty({description: &#x27;Creation date of the account&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="88" class="link-to-prism">src/accounts/dtos/response/account.dto.ts:88</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="customFieldValues"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Optional</span>
                        <span ><b>customFieldValues</b></span>
                        <a href="#customFieldValues"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="../classes/ExternalCustomFieldValuesDto.html" target="_self" >ExternalCustomFieldValuesDto[]</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty({description: &#x27;The custom field values&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="85" class="link-to-prism">src/accounts/dtos/response/account.dto.ts:85</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="description"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Optional</span>
                        <span ><b>description</b></span>
                        <a href="#description"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiPropertyOptional({description: &#x27;The description of the account&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="13" class="link-to-prism">src/accounts/dtos/response/account.dto.ts:13</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="employees"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Optional</span>
                        <span ><b>employees</b></span>
                        <a href="#employees"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiPropertyOptional({description: &#x27;Number of employees of the account&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="71" class="link-to-prism">src/accounts/dtos/response/account.dto.ts:71</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="health"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>health</b></span>
                        <a href="#health"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty({description: &#x27;The health of the account&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="42" class="link-to-prism">src/accounts/dtos/response/account.dto.ts:42</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="healthId"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>healthId</b></span>
                        <a href="#healthId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty({description: &#x27;The identifier of the health attribute&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="39" class="link-to-prism">src/accounts/dtos/response/account.dto.ts:39</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="id"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>id</b></span>
                        <a href="#id"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty({description: &#x27;Unique identifier of the account&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="7" class="link-to-prism">src/accounts/dtos/response/account.dto.ts:7</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="industry"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>industry</b></span>
                        <a href="#industry"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty({description: &#x27;The industry of the account&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="48" class="link-to-prism">src/accounts/dtos/response/account.dto.ts:48</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="industryId"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>industryId</b></span>
                        <a href="#industryId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty({description: &#x27;The identifier of the industry attribute&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="45" class="link-to-prism">src/accounts/dtos/response/account.dto.ts:45</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="logo"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Optional</span>
                        <span ><b>logo</b></span>
                        <a href="#logo"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiPropertyOptional({description: &#x27;The URL of the account logo&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="22" class="link-to-prism">src/accounts/dtos/response/account.dto.ts:22</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="name"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>name</b></span>
                        <a href="#name"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty({description: &#x27;Name of the account&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="10" class="link-to-prism">src/accounts/dtos/response/account.dto.ts:10</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="primaryDomain"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>primaryDomain</b></span>
                        <a href="#primaryDomain"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty({description: &#x27;Primary domain of the account&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="51" class="link-to-prism">src/accounts/dtos/response/account.dto.ts:51</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="secondaryDomain"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Optional</span>
                        <span ><b>secondaryDomain</b></span>
                        <a href="#secondaryDomain"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiPropertyOptional({description: &#x27;Secondary domain of the account&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="54" class="link-to-prism">src/accounts/dtos/response/account.dto.ts:54</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="shippingAddress"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Optional</span>
                        <span ><b>shippingAddress</b></span>
                        <a href="#shippingAddress"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiPropertyOptional({description: &#x27;Shipping address of the account&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="80" class="link-to-prism">src/accounts/dtos/response/account.dto.ts:80</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="source"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>source</b></span>
                        <a href="#source"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty({description: &#x27;The source of the account&#x27;, example: &#x27;hubspot&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="19" class="link-to-prism">src/accounts/dtos/response/account.dto.ts:19</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="status"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>status</b></span>
                        <a href="#status"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty({description: &#x27;The status of the account&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="28" class="link-to-prism">src/accounts/dtos/response/account.dto.ts:28</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="statusId"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>statusId</b></span>
                        <a href="#statusId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty({description: &#x27;The identifier of the status of the account&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="25" class="link-to-prism">src/accounts/dtos/response/account.dto.ts:25</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updatedAt"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>updatedAt</b></span>
                        <a href="#updatedAt"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty({description: &#x27;Last update date of the account&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="91" class="link-to-prism">src/accounts/dtos/response/account.dto.ts:91</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="website"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Optional</span>
                        <span ><b>website</b></span>
                        <a href="#website"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiPropertyOptional({description: &#x27;Website of the account&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="74" class="link-to-prism">src/accounts/dtos/response/account.dto.ts:74</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="fromEntity"></a>
                    <span class="name">
                            <span class="modifier">Static</span>
                        <span ><b>fromEntity</b></span>
                        <a href="#fromEntity"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>fromEntity(entity: Account)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="93"
                                    class="link-to-prism">src/accounts/dtos/response/account.dto.ts:93</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>entity</td>
                                            <td>
                                                        <code>Account</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/AccountResponseDto.html" target="_self" >AccountResponseDto</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>





    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { ApiProperty, ApiPropertyOptional } from &quot;@nestjs/swagger&quot;;
import { Account } from &quot;@repo/thena-platform-entities&quot;;
import { ExternalCustomFieldValuesDto } from &quot;../../../custom-field/dto/custom-field-values.dto&quot;;

export class AccountResponseDto {
  @ApiProperty({ description: &quot;Unique identifier of the account&quot; })
  id: string;

  @ApiProperty({ description: &quot;Name of the account&quot; })
  name: string;

  @ApiPropertyOptional({ description: &quot;The description of the account&quot; })
  description?: string;

  @ApiProperty({
    description: &quot;The source of the account&quot;,
    example: &quot;hubspot&quot;,
  })
  source: string;

  @ApiPropertyOptional({ description: &quot;The URL of the account logo&quot; })
  logo?: string;

  @ApiProperty({ description: &quot;The identifier of the status of the account&quot; })
  statusId: string;

  @ApiProperty({ description: &quot;The status of the account&quot; })
  status: string;

  @ApiProperty({
    description: &quot;The identifier of the classification of the account&quot;,
  })
  classificationId: string;

  @ApiProperty({ description: &quot;The classification of the account&quot; })
  classification: string;

  @ApiProperty({ description: &quot;The identifier of the health attribute&quot; })
  healthId: string;

  @ApiProperty({ description: &quot;The health of the account&quot; })
  health: string;

  @ApiProperty({ description: &quot;The identifier of the industry attribute&quot; })
  industryId: string;

  @ApiProperty({ description: &quot;The industry of the account&quot; })
  industry: string;

  @ApiProperty({ description: &quot;Primary domain of the account&quot; })
  primaryDomain: string;

  @ApiPropertyOptional({ description: &quot;Secondary domain of the account&quot; })
  secondaryDomain?: string;

  @ApiPropertyOptional({ description: &quot;Name of the account owner&quot; })
  accountOwner?: string;

  @ApiPropertyOptional({
    description: &quot;Unique identifier of the account owner&quot;,
  })
  accountOwnerId?: string;

  @ApiPropertyOptional({ description: &quot;Email of the account owner&quot; })
  accountOwnerEmail?: string;

  @ApiPropertyOptional({ description: &quot;Annual revenue of the account&quot; })
  annualRevenue?: number;

  @ApiPropertyOptional({ description: &quot;Number of employees of the account&quot; })
  employees?: number;

  @ApiPropertyOptional({ description: &quot;Website of the account&quot; })
  website?: string;

  @ApiPropertyOptional({ description: &quot;Billing address of the account&quot; })
  billingAddress?: string;

  @ApiPropertyOptional({ description: &quot;Shipping address of the account&quot; })
  shippingAddress?: string;

  @ApiProperty({
    description: &quot;The custom field values&quot;,
  })
  customFieldValues?: ExternalCustomFieldValuesDto[];

  @ApiProperty({ description: &quot;Creation date of the account&quot; })
  createdAt: string;

  @ApiProperty({ description: &quot;Last update date of the account&quot; })
  updatedAt: string;

  static fromEntity(entity: Account): AccountResponseDto {
    const dto &#x3D; new AccountResponseDto();

    dto.id &#x3D; entity.uid;
    dto.name &#x3D; entity.name;
    dto.description &#x3D; entity.description;
    dto.source &#x3D; entity.source;
    dto.logo &#x3D; entity.logo;
    dto.statusId &#x3D; entity.statusAttribute?.uid;
    dto.status &#x3D; entity.statusAttribute?.value;
    dto.classificationId &#x3D; entity.classificationAttribute?.uid;
    dto.classification &#x3D; entity.classificationAttribute?.value;
    dto.healthId &#x3D; entity.healthAttribute?.uid;
    dto.health &#x3D; entity.healthAttribute?.value;
    dto.industryId &#x3D; entity.industryAttribute?.uid;
    dto.industry &#x3D; entity.industryAttribute?.value;
    dto.primaryDomain &#x3D; entity.primaryDomain;
    dto.secondaryDomain &#x3D; entity.secondaryDomain;
    dto.accountOwner &#x3D; entity.accountOwner?.name;
    dto.accountOwnerId &#x3D; entity.accountOwner?.uid;
    dto.accountOwnerEmail &#x3D; entity.accountOwner?.email;
    dto.annualRevenue &#x3D; entity.annualRevenue;
    dto.employees &#x3D; entity.employees;
    dto.website &#x3D; entity.website;
    dto.billingAddress &#x3D; entity.billingAddress;
    dto.shippingAddress &#x3D; entity.shippingAddress;
    dto.customFieldValues &#x3D; entity?.customFieldValues?.map((cfv) &#x3D;&gt; ({
      customFieldId: cfv.customField.uid,
      data: cfv.data,
      metadata: cfv.metadata,
    }));
    dto.createdAt &#x3D;
      typeof entity.createdAt &#x3D;&#x3D;&#x3D; &quot;string&quot;
        ? entity.createdAt
        : entity.createdAt.toISOString();
    dto.updatedAt &#x3D;
      typeof entity.updatedAt &#x3D;&#x3D;&#x3D; &quot;string&quot;
        ? entity.updatedAt
        : entity.updatedAt.toISOString();

    return dto;
  }
}
</code></pre>
    </div>
</div>









                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'class';
            var COMPODOC_CURRENT_PAGE_URL = 'AccountResponseDto.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
