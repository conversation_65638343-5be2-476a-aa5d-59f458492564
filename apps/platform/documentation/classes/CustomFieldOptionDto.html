<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content class">
                   <div class="content-data">












<ol class="breadcrumb">
  <li class="breadcrumb-item">Classes</li>
  <li class="breadcrumb-item" >CustomFieldOptionDto</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/custom-field/dto/custom-field.dto.ts</code>
        </p>






            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                <a href="#id" >id</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                <a href="#value" >value</a>
                            </li>
                        </ul>
                    </td>
                </tr>






        </tbody>
    </table>
</section>


            <section data-compodoc="block-properties">
    
    <h3 id="inputs">
        Properties
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="id"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                        <span ><b>id</b></span>
                        <a href="#id"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @IsString()<br />@IsOptional()<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="128" class="link-to-prism">src/custom-field/dto/custom-field.dto.ts:128</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="value"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                        <span ><b>value</b></span>
                        <a href="#value"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty()<br />@IsString()<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="132" class="link-to-prism">src/custom-field/dto/custom-field.dto.ts:132</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
</section>







    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { ApiProperty, ApiPropertyOptional } from &quot;@nestjs/swagger&quot;;
import {
  CustomFieldOption,
  CustomFieldSource,
  CustomFieldType,
} from &quot;@repo/thena-platform-entities&quot;;
import { Type } from &quot;class-transformer&quot;;
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsEnum,
  IsInt,
  IsObject,
  IsOptional,
  IsString,
  Matches,
  MaxLength,
  MinLength,
  ValidateNested,
} from &quot;class-validator&quot;;

class CommonCustomFieldDto {
  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ description: &quot;Whether the custom field is active&quot; })
  isActive?: boolean;
}

export class CreateCustomFieldDto extends CommonCustomFieldDto {
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: &quot;The description of the custom field&quot; })
  description?: string;

  @IsString()
  @MinLength(1)
  @MaxLength(100)
  @Matches(/^[a-zA-Z0-9\s-_]+$/, {
    message:
      &quot;name can only contain alphanumeric characters, spaces, hyphens, and underscores&quot;,
  })
  @ApiProperty({ description: &quot;The name of the custom field&quot; })
  name: string;

  @IsEnum(CustomFieldSource)
  @ApiProperty({ description: &quot;The source of the custom field&quot; })
  source: CustomFieldSource;

  @IsEnum(CustomFieldType)
  @ApiProperty({ description: &quot;The type of the custom field&quot; })
  fieldType: CustomFieldType;

  @IsArray()
  @IsOptional()
  @ApiPropertyOptional({ description: &quot;The options of the custom field&quot; })
  options?: CustomFieldOption[];

  @IsObject()
  @IsOptional()
  @ValidateNested()
  @ApiPropertyOptional({ description: &quot;The metadata of the custom field&quot; })
  metadata?: {
    [key: string]: string | number | boolean | null;
  };

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description: &quot;The placeholder text of the custom field&quot;,
  })
  placeholderText: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: &quot;The hint text of the custom field&quot; })
  hintText: string;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({
    description: &quot;Whether the custom field is mandatory on close&quot;,
  })
  mandatoryOnClose: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({
    description: &quot;Whether the custom field is mandatory on creation&quot;,
  })
  mandatoryOnCreation: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({
    description: &quot;Whether the custom field is visible to customer&quot;,
  })
  visibleToCustomer: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({
    description: &quot;Whether the custom field is editable by customer&quot;,
  })
  editableByCustomer: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({
    description: &quot;Whether the custom field is auto added to all forms&quot;,
  })
  autoAddToAllForms: boolean;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: &quot;The default value of the custom field&quot; })
  defaultValue: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: &quot;The team id of the custom field&quot; })
  teamId?: string;
}

class CustomFieldOptionDto {
  @IsString()
  @IsOptional()
  id: string;

  @ApiProperty()
  @IsString()
  value: string;
}

class UpdateFieldsDto {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @ApiPropertyOptional()
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() &#x3D;&gt; CustomFieldOptionDto)
  options?: CustomFieldOptionDto[];

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  defaultValue?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  regexForValidation?: string;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  mandatoryOnCreation?: boolean;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  mandatoryOnClose?: boolean;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  visibleToCustomer?: boolean;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  editableByCustomer?: boolean;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  auto_add_to_all_forms?: boolean;
}

class CustomFieldUpdateData {
  @ApiProperty()
  @IsString()
  @MinLength(1, { message: &quot;fieldId must be a non-empty string&quot; })
  fieldId: string;

  @ApiProperty()
  @IsInt({ message: &quot;version must be an integer&quot; })
  version: number;

  // @ApiPropertyOptional()
  // @IsBoolean()
  // @IsOptional()
  // deleteFromExistingForms?: boolean;

  @ApiPropertyOptional()
  @ValidateNested()
  @Type(() &#x3D;&gt; UpdateFieldsDto)
  updates: UpdateFieldsDto;
}

export class UpdateCustomFieldDto {
  @ApiProperty({ type: [CustomFieldUpdateData] })
  @ValidateNested()
  @Type(() &#x3D;&gt; CustomFieldUpdateData)
  fields: CustomFieldUpdateData[];
}

class CustomFieldData {
  @ApiProperty()
  name: string;

  @ApiProperty()
  uid: string;

  @ApiProperty()
  organizationId: string;

  @ApiProperty({ enum: CustomFieldSource })
  source: CustomFieldSource;

  @ApiProperty({ enum: CustomFieldType })
  fieldType: CustomFieldType;

  @ApiProperty()
  organization: {
    id: string;
  };

  @ApiProperty({ nullable: true })
  options: CustomFieldOption[] | null;

  @ApiProperty({ nullable: true })
  metadata: Record&lt;string, unknown&gt; | null;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty()
  isActive: boolean;

  @ApiProperty()
  team: {
    id: string;
  };

  @ApiProperty()
  teamId: string;

  @ApiProperty()
  autoAddToAllForms: boolean;

  @ApiProperty()
  hintText: string;

  @ApiProperty()
  placeholderText: string;

  @ApiProperty()
  mandatoryOnCreation: boolean;

  @ApiProperty()
  mandatoryOnClose: boolean;

  @ApiProperty()
  visibleToCustomer: boolean;

  @ApiProperty()
  editableByCustomer: boolean;

  @ApiProperty()
  defaultValue: string;
}

export class BatchCustomFieldResponseDto {
  @ApiProperty({ type: [CustomFieldData] })
  data: CustomFieldData[];

  @ApiProperty()
  status: boolean;

  @ApiProperty()
  message: string;

  @ApiProperty()
  timestamp: Date;
}

export class CustomFieldResponseDto {
  @ApiProperty()
  data: CustomFieldData;

  @ApiProperty()
  status: boolean;

  @ApiProperty()
  message: string;

  @ApiProperty()
  timestamp: Date;
}

export class GetAllCustomFieldsResponse {
  @ApiProperty({ type: [CustomFieldData] })
  data: {
    items: CustomFieldData[];
  };

  @ApiProperty()
  status: boolean;

  @ApiProperty()
  message: string;

  @ApiProperty()
  timestamp: Date;
}

export class CustomFieldTypesData {
  @ApiProperty({ type: [String] })
  text: string[];

  @ApiProperty({ type: [String] })
  numeric: string[];

  @ApiProperty({ type: [String] })
  choice: string[];

  @ApiProperty({ type: [String] })
  date: string[];

  @ApiProperty({ type: [String] })
  user: string[];

  @ApiProperty({ type: [String] })
  specialized: string[];

  @ApiProperty({ type: [String] })
  file: string[];

  @ApiProperty({ type: [String] })
  calculated: string[];

  @ApiProperty({ type: [String] })
  lookup: string[];

  @ApiProperty({ type: [String] })
  geographic: string[];

  @ApiProperty({ type: [String] })
  rating: string[];

  @ApiProperty({ type: [String] })
  toggle: string[];
}

export class GetAllCustomFieldTypesResponse {
  @ApiProperty({ type: CustomFieldTypesData })
  data: CustomFieldTypesData;

  @ApiProperty()
  status: boolean;

  @ApiProperty()
  message: string;

  @ApiProperty()
  timestamp: Date;
}

export class DeleteFieldItemDto {
  @ApiProperty()
  @IsString()
  @MinLength(1, { message: &quot;fieldId must be a non-empty string&quot; })
  fieldId: string;

  @ApiProperty()
  @IsInt({ message: &quot;version must be an integer&quot; })
  version: number;
}

export class DeleteCustomFieldDto {
  @IsArray({ message: &quot;Fields must be an array&quot; })
  @ArrayMinSize(1, { message: &quot;At least one field is required&quot; })
  @ValidateNested({ each: true })
  @Type(() &#x3D;&gt; DeleteFieldItemDto) // Required for nested validation
  @ApiProperty({ type: [DeleteFieldItemDto] })
  fields: DeleteFieldItemDto[];
}
</code></pre>
    </div>
</div>









                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'class';
            var COMPODOC_CURRENT_PAGE_URL = 'CustomFieldOptionDto.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
