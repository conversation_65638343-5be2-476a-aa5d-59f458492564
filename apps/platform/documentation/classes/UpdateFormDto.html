<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content class">
                   <div class="content-data">












<ol class="breadcrumb">
  <li class="breadcrumb-item">Classes</li>
  <li class="breadcrumb-item" >UpdateFormDto</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/forms/dto/form.dto.ts</code>
        </p>






            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                <a href="#formId" >formId</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                <a href="#updates" >updates</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                <a href="#version" >version</a>
                            </li>
                        </ul>
                    </td>
                </tr>






        </tbody>
    </table>
</section>


            <section data-compodoc="block-properties">
    
    <h3 id="inputs">
        Properties
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="formId"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                        <span ><b>formId</b></span>
                        <a href="#formId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @IsString()<br />@MinLength(1, {message: &#x27;formId must be a non-empty string&#x27;})<br />@ApiProperty({description: &#x27;The ID of the form&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="390" class="link-to-prism">src/forms/dto/form.dto.ts:390</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updates"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                        <span ><b>updates</b></span>
                        <a href="#updates"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="../classes/UpdateFormItemDto.html" target="_self" >UpdateFormItemDto</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ValidateNested()<br />@Type(undefined)<br />@ApiProperty({description: &#x27;The updates to the form&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="399" class="link-to-prism">src/forms/dto/form.dto.ts:399</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="version"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                        <span ><b>version</b></span>
                        <a href="#version"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @IsInt({message: &#x27;version must be an integer&#x27;})<br />@ApiProperty({description: &#x27;The version of the form&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="394" class="link-to-prism">src/forms/dto/form.dto.ts:394</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
</section>







    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { ApiProperty, ApiPropertyOptional } from &quot;@nestjs/swagger&quot;;
import {
  FormChannels,
  FormFieldTriggers,
  FormFieldType,
  TargetFieldConditionsRequiredMultipleValues,
  TargetFieldConditionsType,
} from &quot;@repo/thena-platform-entities&quot;;
import { Type } from &quot;class-transformer&quot;;
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  Matches,
  MaxLength,
  MinLength,
  registerDecorator,
  ValidateNested,
  ValidationArguments,
  ValidationOptions,
} from &quot;class-validator&quot;;

export class TargetFieldOption {
  @IsString()
  name?: string;

  @IsString()
  value?: string;
}

class TargetField {
  @IsString()
  @ApiPropertyOptional({ description: &quot;The ID of the target field&quot; })
  id?: string;

  @IsString()
  @ApiPropertyOptional({ description: &quot;The type of the target field&quot; })
  type?: FormFieldTriggers;

  @IsOptional()
  @IsArray()
  @ApiPropertyOptional({ description: &quot;The value of the target field&quot; })
  value?: any;

  @ValidateNested({ each: true })
  @IsOptional()
  @Type(() &#x3D;&gt; TargetFieldOption)
  @ApiPropertyOptional({
    type: [TargetFieldOption],
    description: &quot;The options of the target field&quot;,
  })
  options?: TargetFieldOption[];
}

export class Condition {
  @IsString()
  @ApiProperty({ description: &quot;The ID of the trigger field&quot; })
  triggerFieldId: string;

  @IsNotEmpty()
  @IsArray()
  @IsValidTriggerFieldValue({ message: &quot;Invalid triggerFieldValue&quot; })
  @ApiProperty({ description: &quot;The value of the trigger field&quot; })
  triggerFieldValue?: any;

  @IsEnum(TargetFieldConditionsType)
  @ApiProperty({ description: &quot;The type of the condition&quot; })
  conditionType: TargetFieldConditionsType;

  @ValidateNested({ each: true })
  @Type(() &#x3D;&gt; TargetField)
  @ApiProperty({ type: [TargetField], description: &quot;The target fields&quot; })
  targetFields: TargetField[];

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ description: &quot;Indicates if the form is for vendor&quot; })
  forVendor?: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ description: &quot;Indicates if the form is for user&quot; })
  forUser?: boolean;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ description: &quot;The ID of the team&quot; })
  teamId?: string;
}

// Custom validator function
function IsValidTriggerFieldValue(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: &quot;isValidTriggerFieldValue&quot;,
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any[], args: ValidationArguments) {
          const conditionType &#x3D; (args.object as Condition).conditionType;
          if (
            TargetFieldConditionsRequiredMultipleValues.includes(conditionType)
          ) {
            return Array.isArray(value) &amp;&amp; value.length &gt;&#x3D; 2;
          } else {
            return Array.isArray(value) &amp;&amp; value.length &gt;&#x3D; 1;
          }
        },
        defaultMessage(args: ValidationArguments) {
          const conditionType &#x3D; (args.object as Condition).conditionType;
          if (
            TargetFieldConditionsRequiredMultipleValues.includes(conditionType)
          ) {
            return &#x60;triggerFieldValue must contain at least two elements when conditionType is one of ${TargetFieldConditionsRequiredMultipleValues.join(
              &quot;, &quot;,
            )}&#x60;;
          } else {
            return &quot;triggerFieldValue must contain at least one element when conditionType is not one of the required multiple values&quot;;
          }
        },
      },
    });
  };
}

export class FormFieldDto {
  @IsString()
  @ApiProperty({ description: &quot;The field&quot; })
  field: string;

  @IsEnum(FormFieldType)
  @IsOptional()
  fieldType?: FormFieldType;

  @IsOptional()
  @ApiPropertyOptional({ description: &quot;The default value&quot; })
  defaultValue?: any;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({
    description: &quot;Indicates if the field is mandatory on creation&quot;,
  })
  mandatoryOnCreation?: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({
    description: &quot;Indicates if the field is mandatory on close&quot;,
  })
  mandatoryOnClose?: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({
    description: &quot;Indicates if the field is visible to customer&quot;,
  })
  visibleToCustomer?: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty({
    description: &quot;Indicates if the field is editable by customer&quot;,
  })
  editableByCustomer?: boolean;
}

export class CreateFormDto {
  @IsString()
  @MinLength(3)
  @MaxLength(100)
  @Matches(/^[a-zA-Z0-9\s-_]+$/, {
    message:
      &quot;name can only contain alphanumeric characters, spaces, hyphens, and underscores&quot;,
  })
  @ApiProperty({ description: &quot;The name of the form&quot; })
  name: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: &quot;The description of the form&quot; })
  description?: string;

  // @IsOptional()
  // @IsBoolean({ message: &quot;default must be true or omitted&quot; })
  // @Matches(/^(false)$/, { message: &quot;default cannot be false&quot; })
  // @ApiPropertyOptional({ description: &quot;Indicates if the form is default&quot; })
  // default?: boolean;

  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @ApiPropertyOptional({
    type: [Condition],
    description: &quot;Conditions of the form&quot;,
  })
  @Type(() &#x3D;&gt; Condition)
  conditions?: Condition[];

  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @ApiPropertyOptional({
    type: [FormFieldDto],
    description: &quot;Fields of the form&quot;,
  })
  @Type(() &#x3D;&gt; FormFieldDto)
  fields?: FormFieldDto[];

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ description: &quot;Indicates if the form is active&quot; })
  isActive?: boolean &#x3D; true;

  @IsArray()
  @IsOptional()
  @ApiPropertyOptional({ description: &quot;Channels of the form&quot; })
  @IsEnum(FormChannels, { each: true })
  channels?: FormChannels[];

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: &quot;The ID of the team&quot; })
  teamId?: string;
}

export class FormResponse {
  @ApiProperty()
  uid: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  description: string;

  @ApiProperty({ type: [FormFieldDto] })
  fields: FormFieldDto[];

  @ApiProperty({ type: [Condition] })
  conditions: Condition[];

  @ApiProperty()
  version: number;

  @ApiProperty()
  isActive: boolean;

  @ApiProperty()
  default: boolean;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty()
  channels: FormChannels[];

  @ApiProperty()
  teamId: string;
}

export class FormResponseDto {
  @ApiProperty()
  data: FormResponse;

  @ApiProperty()
  status: boolean;

  @ApiProperty()
  message: string;

  @ApiProperty()
  timestamp: Date;
}

export class FormData {
  @ApiProperty()
  uid: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  description: string;

  @ApiProperty({ type: [FormFieldDto] })
  fields: FormFieldDto[];

  @ApiProperty({ type: [Condition] })
  conditions: Condition[];

  @ApiProperty()
  version: number;

  @ApiProperty()
  isActive: boolean;

  @ApiProperty()
  default: boolean;

  @ApiProperty()
  channels: FormChannels[];
}

export class GetAllFormsResponse {
  @ApiProperty({ type: [FormData] })
  data: {
    items: FormData[];
  };

  @ApiProperty()
  status: boolean;

  @ApiProperty()
  message: string;

  @ApiProperty()
  timestamp: Date;
}

export class DeleteFormItemDto {
  @IsString()
  @MinLength(1, { message: &quot;formId must be a non-empty string&quot; })
  @ApiProperty({ description: &quot;The ID of the form&quot; })
  formId: string;

  @IsInt({ message: &quot;version must be an integer&quot; })
  @ApiProperty({ description: &quot;The version of the form&quot; })
  version: number;
}

export class DeleteFormsDto {
  @IsArray({ message: &quot;Forms must be an array&quot; })
  @ArrayMinSize(1, { message: &quot;At least one form is required&quot; })
  @ValidateNested({ each: true })
  @Type(() &#x3D;&gt; DeleteFormItemDto) // Required for nested validation
  @ApiProperty({ type: [DeleteFormItemDto] })
  forms: DeleteFormItemDto[];
}

export class UpdateFormItemDto {
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: &quot;The name of the form&quot; })
  name?: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: &quot;The description of the form&quot; })
  description?: string;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ description: &quot;Indicates if the form is default&quot; })
  default?: boolean;

  @IsArray()
  @IsOptional()
  @ApiPropertyOptional({ type: [FormFieldDto] })
  @Type(() &#x3D;&gt; FormFieldDto)
  fields?: FormFieldDto[];

  @IsArray()
  @IsOptional()
  @ApiPropertyOptional({ type: [Condition] })
  @ValidateNested({ each: true })
  @Type(() &#x3D;&gt; Condition)
  conditions?: Condition[];

  @IsArray()
  @IsOptional()
  @ApiPropertyOptional({ description: &quot;Channels of the form&quot; })
  @IsEnum(FormChannels, { each: true })
  channels?: FormChannels[];
}

export class UpdateFormDto {
  @IsString()
  @MinLength(1, { message: &quot;formId must be a non-empty string&quot; })
  @ApiProperty({ description: &quot;The ID of the form&quot; })
  formId: string;

  @IsInt({ message: &quot;version must be an integer&quot; })
  @ApiProperty({ description: &quot;The version of the form&quot; })
  version: number;

  @ValidateNested()
  @Type(() &#x3D;&gt; UpdateFormItemDto)
  @ApiProperty({ description: &quot;The updates to the form&quot; })
  updates: UpdateFormItemDto;
}
</code></pre>
    </div>
</div>









                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'class';
            var COMPODOC_CURRENT_PAGE_URL = 'UpdateFormDto.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
