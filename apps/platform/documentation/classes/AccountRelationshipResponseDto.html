<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content class">
                   <div class="content-data">












<ol class="breadcrumb">
  <li class="breadcrumb-item">Classes</li>
  <li class="breadcrumb-item" >AccountRelationshipResponseDto</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/accounts/dtos/response/account-relationship.dto.ts</code>
        </p>






            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier"></span>
                                <a href="#account" >account</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#accountId" >accountId</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#createdAt" >createdAt</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#id" >id</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#relatedAccount" >relatedAccount</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#relatedAccountId" >relatedAccountId</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#relationshipType" >relationshipType</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#updatedAt" >updatedAt</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#fromEntity" >fromEntity</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>


            <section data-compodoc="block-properties">
    
    <h3 id="inputs">
        Properties
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="account"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>account</b></span>
                        <a href="#account"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty({description: &#x27;The name of the primary account of the relationship&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="77" class="link-to-prism">src/accounts/dtos/response/account-relationship.dto.ts:77</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="accountId"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>accountId</b></span>
                        <a href="#accountId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty({description: &#x27;The identifier of the primary account of the relationship&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="72" class="link-to-prism">src/accounts/dtos/response/account-relationship.dto.ts:72</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createdAt"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>createdAt</b></span>
                        <a href="#createdAt"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty({description: &#x27;The creation date of the relationship&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="97" class="link-to-prism">src/accounts/dtos/response/account-relationship.dto.ts:97</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="id"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>id</b></span>
                        <a href="#id"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty({description: &#x27;The identifier of the relationship&#x27;, example: &#x27;AR123&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="67" class="link-to-prism">src/accounts/dtos/response/account-relationship.dto.ts:67</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="relatedAccount"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>relatedAccount</b></span>
                        <a href="#relatedAccount"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty({description: &#x27;The name of the related account of the relationship&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="87" class="link-to-prism">src/accounts/dtos/response/account-relationship.dto.ts:87</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="relatedAccountId"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>relatedAccountId</b></span>
                        <a href="#relatedAccountId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty({description: &#x27;The identifier of the related account of the relationship&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="82" class="link-to-prism">src/accounts/dtos/response/account-relationship.dto.ts:82</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="relationshipType"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>relationshipType</b></span>
                        <a href="#relationshipType"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="../classes/AccountRelationshipTypeResponseDto.html" target="_self" >AccountRelationshipTypeResponseDto</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty({description: &#x27;Relationship type&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="92" class="link-to-prism">src/accounts/dtos/response/account-relationship.dto.ts:92</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updatedAt"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>updatedAt</b></span>
                        <a href="#updatedAt"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty({description: &#x27;Last update date of the relationship&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="102" class="link-to-prism">src/accounts/dtos/response/account-relationship.dto.ts:102</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="fromEntity"></a>
                    <span class="name">
                            <span class="modifier">Static</span>
                        <span ><b>fromEntity</b></span>
                        <a href="#fromEntity"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>fromEntity(entity: AccountRelationship)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="104"
                                    class="link-to-prism">src/accounts/dtos/response/account-relationship.dto.ts:104</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>entity</td>
                                            <td>
                                                        <code>AccountRelationship</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/AccountRelationshipResponseDto.html" target="_self" >AccountRelationshipResponseDto</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>





    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { ApiProperty } from &quot;@nestjs/swagger&quot;;
import {
  AccountRelationship,
  AccountRelationshipType,
} from &quot;@repo/thena-platform-entities&quot;;

export class AccountRelationshipTypeResponseDto {
  @ApiProperty({
    description: &quot;The identifier of the relationship type&quot;,
    example: &quot;ART123&quot;,
  })
  id: string;

  @ApiProperty({
    description: &quot;The name of the relationship type&quot;,
    example: &quot;Parent&quot;,
  })
  name: string;

  @ApiProperty({
    description: &quot;The identifier of the inverse relationship type&quot;,
    example: &quot;ART124&quot;,
  })
  inverseRelationshipId: string;

  @ApiProperty({
    description: &quot;The name of the inverse relationship type&quot;,
    example: &quot;Subsidiary&quot;,
  })
  inverseRelationship: string;

  @ApiProperty({
    description: &quot;The creation date of the relationship type&quot;,
  })
  createdAt: string;

  @ApiProperty({
    description: &quot;Last update date of the relationship type&quot;,
  })
  updatedAt: string;

  static fromEntity(entity: AccountRelationshipType) {
    const dto &#x3D; new AccountRelationshipTypeResponseDto();

    dto.id &#x3D; entity.uid;
    dto.name &#x3D; entity.name;
    dto.inverseRelationshipId &#x3D; entity.inverseRelationship?.uid;
    dto.inverseRelationship &#x3D; entity.inverseRelationship?.name;
    dto.createdAt &#x3D;
      typeof entity.createdAt &#x3D;&#x3D;&#x3D; &quot;string&quot;
        ? entity.createdAt
        : entity.createdAt.toISOString();
    dto.updatedAt &#x3D;
      typeof entity.updatedAt &#x3D;&#x3D;&#x3D; &quot;string&quot;
        ? entity.updatedAt
        : entity.updatedAt.toISOString();

    return dto;
  }
}

export class AccountRelationshipResponseDto {
  @ApiProperty({
    description: &quot;The identifier of the relationship&quot;,
    example: &quot;AR123&quot;,
  })
  id: string;

  @ApiProperty({
    description: &quot;The identifier of the primary account of the relationship&quot;,
  })
  accountId: string;

  @ApiProperty({
    description: &quot;The name of the primary account of the relationship&quot;,
  })
  account: string;

  @ApiProperty({
    description: &quot;The identifier of the related account of the relationship&quot;,
  })
  relatedAccountId: string;

  @ApiProperty({
    description: &quot;The name of the related account of the relationship&quot;,
  })
  relatedAccount: string;

  @ApiProperty({
    description: &quot;Relationship type&quot;,
  })
  relationshipType: AccountRelationshipTypeResponseDto;

  @ApiProperty({
    description: &quot;The creation date of the relationship&quot;,
  })
  createdAt: string;

  @ApiProperty({
    description: &quot;Last update date of the relationship&quot;,
  })
  updatedAt: string;

  static fromEntity(entity: AccountRelationship) {
    // Needs population of account, relatedAccount and relationshipType
    const dto &#x3D; new AccountRelationshipResponseDto();

    dto.id &#x3D; entity.uid;
    dto.accountId &#x3D; entity.account.uid;
    dto.account &#x3D; entity.account.name;
    dto.relatedAccountId &#x3D; entity.relatedAccount.uid;
    dto.relatedAccount &#x3D; entity.relatedAccount.name;
    dto.relationshipType &#x3D; AccountRelationshipTypeResponseDto.fromEntity(
      entity.relationshipType,
    );
    dto.createdAt &#x3D;
      typeof entity.createdAt &#x3D;&#x3D;&#x3D; &quot;string&quot;
        ? entity.createdAt
        : entity.createdAt.toISOString();
    dto.updatedAt &#x3D;
      typeof entity.updatedAt &#x3D;&#x3D;&#x3D; &quot;string&quot;
        ? entity.updatedAt
        : entity.updatedAt.toISOString();

    return dto;
  }
}
</code></pre>
    </div>
</div>









                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'class';
            var COMPODOC_CURRENT_PAGE_URL = 'AccountRelationshipResponseDto.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
