<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content class">
                   <div class="content-data">












<ol class="breadcrumb">
  <li class="breadcrumb-item">Classes</li>
  <li class="breadcrumb-item" >UpdateAccountRelationshipType</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/common/workflows/activities/accounts.activities.ts</code>
        </p>



            <p class="comment">
                <h3>Extends</h3>
            </p>
            <p class="comment">
                        <code>AbstractWorkflowActivity</code>
            </p>



            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Static</span>
                                    <span class="modifier"></span>
                                <a href="#activityName" >activityName</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#connectionDetails" >connectionDetails</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                    <span class="modifier"></span>
                                <a href="#description" >description</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                    <span class="modifier"></span>
                                <a href="#identifier" >identifier</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                    <span class="modifier"></span>
                                <a href="#requestSchema" >requestSchema</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                    <span class="modifier"></span>
                                <a href="#responseSchema" >responseSchema</a>
                            </li>
                        </ul>
                    </td>
                </tr>






        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(configService: <a href="../injectables/ConfigService.html" target="_self">ConfigService</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="510" class="link-to-prism">src/common/workflows/activities/accounts.activities.ts:510</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>configService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/ConfigService.html" target="_self" >ConfigService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-properties">
    
    <h3 id="inputs">
        Properties
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="activityName"></a>
                    <span class="name">
                            <span class="modifier">Static</span>
                            <span class="modifier"></span>
                        <span ><b>activityName</b></span>
                        <a href="#activityName"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&quot;Update account relationship type&quot;</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="515" class="link-to-prism">src/common/workflows/activities/accounts.activities.ts:515</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="connectionDetails"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>connectionDetails</b></span>
                        <a href="#connectionDetails"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME,
      methodName: &quot;UpdateAccountRelationshipType&quot;,
      protoPath: &quot;dist/proto/accounts/accounts.proto&quot;,
    },
  }</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="534" class="link-to-prism">src/common/workflows/activities/accounts.activities.ts:534</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="description"></a>
                    <span class="name">
                            <span class="modifier">Static</span>
                            <span class="modifier"></span>
                        <span ><b>description</b></span>
                        <a href="#description"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&quot;This activity updates an account relationship type&quot;</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="517" class="link-to-prism">src/common/workflows/activities/accounts.activities.ts:517</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="identifier"></a>
                    <span class="name">
                            <span class="modifier">Static</span>
                            <span class="modifier"></span>
                        <span ><b>identifier</b></span>
                        <a href="#identifier"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&quot;accounts:update-account-relationship-type&quot;</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="520" class="link-to-prism">src/common/workflows/activities/accounts.activities.ts:520</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="requestSchema"></a>
                    <span class="name">
                            <span class="modifier">Static</span>
                            <span class="modifier"></span>
                        <span ><b>requestSchema</b></span>
                        <a href="#requestSchema"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
    type: &quot;object&quot;,
    required: [&quot;id&quot;],
    properties: {
      id: { type: &quot;string&quot; },
      name: { type: &quot;string&quot; },
      inverseRelationshipId: { type: &quot;string&quot; },
    },
  }</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="522" class="link-to-prism">src/common/workflows/activities/accounts.activities.ts:522</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="responseSchema"></a>
                    <span class="name">
                            <span class="modifier">Static</span>
                            <span class="modifier"></span>
                        <span ><b>responseSchema</b></span>
                        <a href="#responseSchema"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>ACCOUNT_RELATIONSHIP_TYPE_PAYLOAD_SCHEMA</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="532" class="link-to-prism">src/common/workflows/activities/accounts.activities.ts:532</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
</section>







    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { accounts } from &quot;@repo/shared-proto&quot;;
import { AbstractWorkflowActivity } from &quot;@repo/workflow-engine&quot;;
import { ConfigKeys, ConfigService } from &quot;../../../config/config.service&quot;;
import {
  ACCOUNT_ACTIVITY_PAYLOAD_SCHEMA,
  ACCOUNT_ATTRIBUTE_VALUE_PAYLOAD_SCHEMA,
  ACCOUNT_NOTE_PAYLOAD_SCHEMA,
  ACCOUNT_PAYLOAD_SCHEMA,
  ACCOUNT_RELATIONSHIP_PAYLOAD_SCHEMA,
  ACCOUNT_RELATIONSHIP_TYPE_PAYLOAD_SCHEMA,
  ACCOUNT_TASK_PAYLOAD_SCHEMA,
  CUSTOMER_CONTACT_PAYLOAD_SCHEMA,
} from &quot;../constants/accounts-response.schema&quot;;

// Account Activities &#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;

export class GetAccounts extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Get accounts&quot;;

  static override description &#x3D; &quot;This activity gets accounts&quot;;

  static override identifier &#x3D; &quot;accounts:get-accounts&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    properties: {
      source: { type: &quot;string&quot; },
      status: { type: &quot;string&quot; },
      classification: { type: &quot;string&quot; },
      health: { type: &quot;string&quot; },
      industry: { type: &quot;string&quot; },
      accountOwnerId: { type: &quot;string&quot; },
      page: { type: &quot;number&quot; },
      limit: { type: &quot;number&quot; },
    },
  };

  static override responseSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;data&quot;],
    properties: {
      data: {
        type: &quot;array&quot;,
        items: ACCOUNT_PAYLOAD_SCHEMA,
      },
    },
  };

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNTS_SERVICE_NAME,
      methodName: &quot;GetAccounts&quot;,
      protoPath: &quot;dist/proto/accounts/accounts.proto&quot;,
    },
  };
}

export class GetAccountDetails extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Get account details&quot;;

  static override description &#x3D; &quot;This activity gets account details&quot;;

  static override identifier &#x3D; &quot;accounts:get-account-details&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;id&quot;],
    properties: {
      id: { type: &quot;string&quot; },
    },
  };

  static override responseSchema &#x3D; ACCOUNT_PAYLOAD_SCHEMA;

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNTS_SERVICE_NAME,
      methodName: &quot;GetAccountDetails&quot;,
      protoPath: &quot;dist/proto/accounts/accounts.proto&quot;,
    },
  };
}

export class CreateAccount extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Create account&quot;;

  static override description &#x3D; &quot;This activity creates an account&quot;;

  static override identifier &#x3D; &quot;accounts:create-account&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;name&quot;, &quot;primaryDomain&quot;],
    properties: {
      name: { type: &quot;string&quot; },
      description: { type: &quot;string&quot; },
      source: { type: &quot;string&quot; },
      logo: { type: &quot;string&quot; },
      status: { type: &quot;string&quot; },
      classification: { type: &quot;string&quot; },
      health: { type: &quot;string&quot; },
      industry: { type: &quot;string&quot; },
      primaryDomain: { type: &quot;string&quot; },
      secondaryDomain: { type: &quot;string&quot; },
      accountOwnerId: { type: &quot;string&quot; },
      annualRevenue: { type: &quot;number&quot; },
      employees: { type: &quot;number&quot; },
      website: { type: &quot;string&quot; },
      billingAddress: { type: &quot;string&quot; },
      shippingAddress: { type: &quot;string&quot; },
      customFieldValues: {
        type: &quot;array&quot;,
        items: {
          type: &quot;object&quot;,
          required: [&quot;customFieldId&quot;],
          properties: {
            customFieldId: { type: &quot;string&quot; },
            data: {
              type: &quot;array&quot;,
              items: {
                type: &quot;object&quot;,
                required: [&quot;value&quot;],
                properties: {
                  value: { type: &quot;string&quot; },
                  id: { type: &quot;string&quot; },
                },
              },
            },
            metadata: {
              type: &quot;object&quot;,
              additionalProperties: { type: &quot;string&quot; },
            },
          },
        },
      },
      addExistingUsersToAccountContacts: { type: &quot;boolean&quot; },
    },
  };

  static override responseSchema &#x3D; ACCOUNT_PAYLOAD_SCHEMA;

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNTS_SERVICE_NAME,
      methodName: &quot;CreateAccount&quot;,
      protoPath: &quot;dist/proto/accounts/accounts.proto&quot;,
    },
  };
}

export class UpdateAccount extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Update account&quot;;

  static override description &#x3D; &quot;This activity updates an account&quot;;

  static override identifier &#x3D; &quot;accounts:update-account&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;id&quot;],
    properties: {
      id: { type: &quot;string&quot; },
      name: { type: &quot;string&quot; },
      description: { type: &quot;string&quot; },
      source: { type: &quot;string&quot; },
      logo: { type: &quot;string&quot; },
      status: { type: &quot;string&quot; },
      classification: { type: &quot;string&quot; },
      health: { type: &quot;string&quot; },
      industry: { type: &quot;string&quot; },
      primaryDomain: { type: &quot;string&quot; },
      secondaryDomain: { type: &quot;string&quot; },
      accountOwnerId: { type: &quot;string&quot; },
      annualRevenue: { type: &quot;number&quot; },
      employees: { type: &quot;number&quot; },
      website: { type: &quot;string&quot; },
      billingAddress: { type: &quot;string&quot; },
      shippingAddress: { type: &quot;string&quot; },
      customFieldValues: {
        type: &quot;array&quot;,
        items: {
          type: &quot;object&quot;,
          required: [&quot;customFieldId&quot;],
          properties: {
            customFieldId: { type: &quot;string&quot; },
            data: {
              type: &quot;array&quot;,
              items: {
                type: &quot;object&quot;,
                required: [&quot;value&quot;],
                properties: {
                  value: { type: &quot;string&quot; },
                  id: { type: &quot;string&quot; },
                },
              },
            },
            metadata: {
              type: &quot;object&quot;,
              additionalProperties: { type: &quot;string&quot; },
            },
          },
        },
      },
      addExistingUsersToAccountContacts: { type: &quot;boolean&quot; },
    },
  };

  static override responseSchema &#x3D; ACCOUNT_PAYLOAD_SCHEMA;

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNTS_SERVICE_NAME,
      methodName: &quot;UpdateAccount&quot;,
      protoPath: &quot;dist/proto/accounts/accounts.proto&quot;,
    },
  };
}

export class DeleteAccount extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Delete account&quot;;

  static override description &#x3D; &quot;This activity deletes an account&quot;;

  static override identifier &#x3D; &quot;accounts:delete-account&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;id&quot;],
    properties: {
      id: { type: &quot;string&quot; },
    },
  };

  static override responseSchema &#x3D; {};

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNTS_SERVICE_NAME,
      methodName: &quot;DeleteAccount&quot;,
      protoPath: &quot;dist/proto/accounts/accounts.proto&quot;,
    },
  };
}

// Account Attribute Value Activities &#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;

export class GetAccountAttributeValues extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Get account attribute values&quot;;

  static override description &#x3D; &quot;This activity gets account attribute values&quot;;

  static override identifier &#x3D; &quot;accounts:get-account-attribute-values&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;attribute&quot;],
    properties: {
      attribute: { type: &quot;string&quot; },
    },
  };

  static override responseSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;data&quot;],
    properties: {
      data: {
        type: &quot;array&quot;,
        items: ACCOUNT_ATTRIBUTE_VALUE_PAYLOAD_SCHEMA,
      },
    },
  };

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_ATTRIBUTE_VALUES_SERVICE_NAME,
      methodName: &quot;GetAccountAttributeValues&quot;,
      protoPath: &quot;dist/proto/accounts/accounts.proto&quot;,
    },
  };
}

export class CreateAccountAttributeValue extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Create account attribute value&quot;;

  static override description &#x3D;
    &quot;This activity creates an account attribute value&quot;;

  static override identifier &#x3D; &quot;accounts:create-account-attribute-value&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;attribute&quot;, &quot;value&quot;],
    properties: {
      attribute: { type: &quot;string&quot; },
      value: { type: &quot;string&quot; },
      isDefault: { type: &quot;boolean&quot; },
    },
  };

  static override responseSchema &#x3D; ACCOUNT_ATTRIBUTE_VALUE_PAYLOAD_SCHEMA;

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_ATTRIBUTE_VALUES_SERVICE_NAME,
      methodName: &quot;CreateAccountAttributeValue&quot;,
      protoPath: &quot;dist/proto/accounts/accounts.proto&quot;,
    },
  };
}

export class UpdateAccountAttributeValue extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Update account attribute value&quot;;

  static override description &#x3D;
    &quot;This activity updates an account attribute value&quot;;

  static override identifier &#x3D; &quot;accounts:update-account-attribute-value&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;id&quot;],
    properties: {
      id: { type: &quot;string&quot; },
      value: { type: &quot;string&quot; },
      isDefault: { type: &quot;boolean&quot; },
    },
  };

  static override responseSchema &#x3D; ACCOUNT_ATTRIBUTE_VALUE_PAYLOAD_SCHEMA;

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_ATTRIBUTE_VALUES_SERVICE_NAME,
      methodName: &quot;UpdateAccountAttributeValue&quot;,
      protoPath: &quot;dist/proto/accounts/accounts.proto&quot;,
    },
  };
}

export class DeleteAccountAttributeValue extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Delete account attribute value&quot;;

  static override description &#x3D;
    &quot;This activity deletes an account attribute value&quot;;

  static override identifier &#x3D; &quot;accounts:delete-account-attribute-value&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;id&quot;],
    properties: {
      id: { type: &quot;string&quot; },
      forceDelete: { type: &quot;boolean&quot; },
    },
  };

  static override responseSchema &#x3D; {};

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_ATTRIBUTE_VALUES_SERVICE_NAME,
      methodName: &quot;DeleteAccountAttributeValue&quot;,
      protoPath: &quot;dist/proto/accounts/accounts.proto&quot;,
    },
  };
}

// Account Relationship Type Activities &#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;

export class GetAccountRelationshipTypes extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Get account relationship types&quot;;

  static override description &#x3D; &quot;This activity gets account relationship types&quot;;

  static override identifier &#x3D; &quot;accounts:get-account-relationship-types&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    properties: {
      page: { type: &quot;number&quot; },
      limit: { type: &quot;number&quot; },
    },
  };

  static override responseSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;data&quot;],
    properties: {
      data: {
        type: &quot;array&quot;,
        items: ACCOUNT_RELATIONSHIP_TYPE_PAYLOAD_SCHEMA,
      },
    },
  };

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME,
      methodName: &quot;GetAccountRelationshipTypes&quot;,
      protoPath: &quot;dist/proto/accounts/accounts.proto&quot;,
    },
  };
}

export class CreateAccountRelationshipType extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Create account relationship type&quot;;

  static override description &#x3D;
    &quot;This activity creates an account relationship type&quot;;

  static override identifier &#x3D; &quot;accounts:create-account-relationship-type&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;name&quot;],
    properties: {
      name: { type: &quot;string&quot; },
      inverseRelationshipId: { type: &quot;string&quot; },
    },
  };

  static override responseSchema &#x3D; ACCOUNT_RELATIONSHIP_TYPE_PAYLOAD_SCHEMA;

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME,
      methodName: &quot;CreateAccountRelationshipType&quot;,
      protoPath: &quot;dist/proto/accounts/accounts.proto&quot;,
    },
  };
}

export class UpdateAccountRelationshipType extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Update account relationship type&quot;;

  static override description &#x3D;
    &quot;This activity updates an account relationship type&quot;;

  static override identifier &#x3D; &quot;accounts:update-account-relationship-type&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;id&quot;],
    properties: {
      id: { type: &quot;string&quot; },
      name: { type: &quot;string&quot; },
      inverseRelationshipId: { type: &quot;string&quot; },
    },
  };

  static override responseSchema &#x3D; ACCOUNT_RELATIONSHIP_TYPE_PAYLOAD_SCHEMA;

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME,
      methodName: &quot;UpdateAccountRelationshipType&quot;,
      protoPath: &quot;dist/proto/accounts/accounts.proto&quot;,
    },
  };
}

export class DeleteAccountRelationshipType extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Delete account relationship type&quot;;

  static override description &#x3D;
    &quot;This activity deletes an account relationship type&quot;;

  static override identifier &#x3D; &quot;accounts:delete-account-relationship-type&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;id&quot;],
    properties: {
      id: { type: &quot;string&quot; },
    },
  };

  static override responseSchema &#x3D; {};

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME,
      methodName: &quot;DeleteAccountRelationshipType&quot;,
      protoPath: &quot;dist/proto/accounts/accounts.proto&quot;,
    },
  };
}

// Account Relationships Activities &#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;

export class GetAccountRelationships extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Get account relationships&quot;;

  static override description &#x3D; &quot;This activity gets account relationships&quot;;

  static override identifier &#x3D; &quot;accounts:get-account-relationships&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;accountId&quot;],
    properties: {
      accountId: { type: &quot;string&quot; },
      type: { type: &quot;string&quot; },
      page: { type: &quot;number&quot; },
      limit: { type: &quot;number&quot; },
    },
  };

  static override responseSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;data&quot;],
    properties: {
      data: {
        type: &quot;array&quot;,
        items: ACCOUNT_RELATIONSHIP_PAYLOAD_SCHEMA,
      },
    },
  };

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME,
      methodName: &quot;GetAccountRelationships&quot;,
      protoPath: &quot;dist/proto/accounts/accounts.proto&quot;,
    },
  };
}

export class CreateAccountRelationship extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Create account relationship&quot;;

  static override description &#x3D; &quot;This activity creates an account relationship&quot;;

  static override identifier &#x3D; &quot;accounts:create-account-relationship&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;accountId&quot;, &quot;relatedAccountId&quot;, &quot;type&quot;],
    properties: {
      accountId: { type: &quot;string&quot; },
      relatedAccountId: { type: &quot;string&quot; },
      type: { type: &quot;string&quot; },
    },
  };

  static override responseSchema &#x3D; ACCOUNT_RELATIONSHIP_PAYLOAD_SCHEMA;

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME,
      methodName: &quot;CreateAccountRelationship&quot;,
      protoPath: &quot;dist/proto/accounts/accounts.proto&quot;,
    },
  };
}

export class UpdateAccountRelationship extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Update account relationship&quot;;

  static override description &#x3D; &quot;This activity updates an account relationship&quot;;

  static override identifier &#x3D; &quot;accounts:update-account-relationship&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;id&quot;],
    properties: {
      id: { type: &quot;string&quot; },
      type: { type: &quot;string&quot; },
    },
  };

  static override responseSchema &#x3D; ACCOUNT_RELATIONSHIP_PAYLOAD_SCHEMA;

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME,
      methodName: &quot;UpdateAccountRelationship&quot;,
      protoPath: &quot;dist/proto/accounts/accounts.proto&quot;,
    },
  };
}

export class DeleteAccountRelationship extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Delete account relationship&quot;;

  static override description &#x3D; &quot;This activity deletes an account relationship&quot;;

  static override identifier &#x3D; &quot;accounts:delete-account-relationship&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;id&quot;],
    properties: {
      id: { type: &quot;string&quot; },
    },
  };

  static override responseSchema &#x3D; {};

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME,
      methodName: &quot;DeleteAccountRelationship&quot;,
      protoPath: &quot;dist/proto/accounts/accounts.proto&quot;,
    },
  };
}

// Account Activity Activities &#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;

export class GetAccountActivities extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Get account activities&quot;;

  static override description &#x3D; &quot;This activity gets account activities&quot;;

  static override identifier &#x3D; &quot;accounts:get-account-activities&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    properties: {
      accountId: { type: &quot;string&quot; },
      type: { type: &quot;string&quot; },
      status: { type: &quot;string&quot; },
      page: { type: &quot;number&quot; },
      limit: { type: &quot;number&quot; },
    },
  };

  static override responseSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;data&quot;],
    properties: {
      data: {
        type: &quot;array&quot;,
        items: ACCOUNT_ACTIVITY_PAYLOAD_SCHEMA,
      },
    },
  };

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_ACTIVITIES_SERVICE_NAME,
      methodName: &quot;GetAccountActivities&quot;,
      protoPath: &quot;dist/proto/accounts/accounts.proto&quot;,
    },
  };
}

export class CreateAccountActivity extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Create account activity&quot;;

  static override description &#x3D; &quot;This activity creates an account activity&quot;;

  static override identifier &#x3D; &quot;accounts:create-account-activity&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;accountId&quot;, &quot;activityTimestamp&quot;],
    properties: {
      accountId: { type: &quot;string&quot; },
      activityTimestamp: { type: &quot;string&quot; },
      duration: { type: &quot;number&quot; },
      location: { type: &quot;string&quot; },
      type: { type: &quot;string&quot; },
      status: { type: &quot;string&quot; },
      participants: { type: &quot;array&quot;, items: { type: &quot;string&quot; } },
      attachmentUrls: { type: &quot;array&quot;, items: { type: &quot;string&quot; } },
    },
  };

  static override responseSchema &#x3D; ACCOUNT_ACTIVITY_PAYLOAD_SCHEMA;

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_ACTIVITIES_SERVICE_NAME,
      methodName: &quot;CreateAccountActivity&quot;,
      protoPath: &quot;dist/proto/accounts/accounts.proto&quot;,
    },
  };
}

export class UpdateAccountActivity extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Update account activity&quot;;

  static override description &#x3D; &quot;This activity updates an account activity&quot;;

  static override identifier &#x3D; &quot;accounts:update-account-activity&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;activityId&quot;],
    properties: {
      activityId: { type: &quot;string&quot; },
      activityTimestamp: { type: &quot;string&quot; },
      duration: { type: &quot;number&quot; },
      location: { type: &quot;string&quot; },
      type: { type: &quot;string&quot; },
      status: { type: &quot;string&quot; },
      participants: { type: &quot;array&quot;, items: { type: &quot;string&quot; } },
      attachmentUrls: { type: &quot;array&quot;, items: { type: &quot;string&quot; } },
    },
  };

  static override responseSchema &#x3D; ACCOUNT_ACTIVITY_PAYLOAD_SCHEMA;

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_ACTIVITIES_SERVICE_NAME,
      methodName: &quot;UpdateAccountActivity&quot;,
      protoPath: &quot;dist/proto/accounts/accounts.proto&quot;,
    },
  };
}

export class DeleteAccountActivity extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Delete account activity&quot;;

  static override description &#x3D; &quot;This activity deletes an account activity&quot;;

  static override identifier &#x3D; &quot;accounts:delete-account-activity&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;activityId&quot;],
    properties: {
      activityId: { type: &quot;string&quot; },
    },
  };

  static override responseSchema &#x3D; {};

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_ACTIVITIES_SERVICE_NAME,
      methodName: &quot;DeleteAccountActivity&quot;,
      protoPath: &quot;dist/proto/accounts/accounts.proto&quot;,
    },
  };
}

// Account Notes Activities &#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;

export class GetAccountNotes extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Get account notes&quot;;

  static override description &#x3D; &quot;This activity gets account notes&quot;;

  static override identifier &#x3D; &quot;accounts:get-account-notes&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;accountId&quot;],
    properties: {
      accountId: { type: &quot;string&quot; },
      type: { type: &quot;string&quot; },
      visibility: { type: &quot;string&quot; },
      page: { type: &quot;number&quot; },
      limit: { type: &quot;number&quot; },
    },
  };

  static override responseSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;data&quot;],
    properties: {
      data: {
        type: &quot;array&quot;,
        items: ACCOUNT_NOTE_PAYLOAD_SCHEMA,
      },
    },
  };

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_NOTES_SERVICE_NAME,
      methodName: &quot;GetAccountNotes&quot;,
      protoPath: &quot;dist/proto/accounts/accounts.proto&quot;,
    },
  };
}

export class CreateAccountNote extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Create account note&quot;;

  static override description &#x3D; &quot;This activity creates an account note&quot;;

  static override identifier &#x3D; &quot;accounts:create-account-note&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;accountId&quot;, &quot;content&quot;],
    properties: {
      accountId: { type: &quot;string&quot; },
      content: { type: &quot;string&quot; },
      type: { type: &quot;string&quot; },
      visibility: { type: &quot;string&quot; },
      attachments: {
        type: &quot;array&quot;,
        items: {
          type: &quot;object&quot;,
          required: [&quot;id&quot;, &quot;name&quot;, &quot;url&quot;],
          properties: {
            id: { type: &quot;string&quot; },
            name: { type: &quot;string&quot; },
            url: { type: &quot;string&quot; },
            contentType: { type: &quot;string&quot; },
            size: { type: &quot;number&quot; },
          },
        },
      },
    },
  };

  static override responseSchema &#x3D; ACCOUNT_NOTE_PAYLOAD_SCHEMA;

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_NOTES_SERVICE_NAME,
      methodName: &quot;CreateAccountNote&quot;,
      protoPath: &quot;dist/proto/accounts/accounts.proto&quot;,
    },
  };
}

export class UpdateAccountNote extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Update account note&quot;;

  static override description &#x3D; &quot;This activity updates an account note&quot;;

  static override identifier &#x3D; &quot;accounts:update-account-note&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;id&quot;],
    properties: {
      id: { type: &quot;string&quot; },
      content: { type: &quot;string&quot; },
      type: { type: &quot;string&quot; },
      visibility: { type: &quot;string&quot; },
      attachments: {
        type: &quot;array&quot;,
        items: {
          type: &quot;object&quot;,
          required: [&quot;id&quot;, &quot;name&quot;, &quot;url&quot;],
          properties: {
            id: { type: &quot;string&quot; },
            name: { type: &quot;string&quot; },
            url: { type: &quot;string&quot; },
            contentType: { type: &quot;string&quot; },
            size: { type: &quot;number&quot; },
          },
        },
      },
    },
  };

  static override responseSchema &#x3D; ACCOUNT_NOTE_PAYLOAD_SCHEMA;

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_NOTES_SERVICE_NAME,
      methodName: &quot;UpdateAccountNote&quot;,
      protoPath: &quot;dist/proto/accounts/accounts.proto&quot;,
    },
  };
}

export class DeleteAccountNote extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Delete account note&quot;;

  static override description &#x3D; &quot;This activity deletes an account note&quot;;

  static override identifier &#x3D; &quot;accounts:delete-account-note&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;id&quot;],
    properties: {
      id: { type: &quot;string&quot; },
    },
  };

  static override responseSchema &#x3D; {};

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_NOTES_SERVICE_NAME,
      methodName: &quot;DeleteAccountNote&quot;,
      protoPath: &quot;dist/proto/accounts/accounts.proto&quot;,
    },
  };
}

// Account Task Activities &#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;

export class GetAccountTasks extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Get account tasks&quot;;

  static override description &#x3D; &quot;This activity gets account tasks&quot;;

  static override identifier &#x3D; &quot;accounts:get-account-tasks&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;accountId&quot;],
    properties: {
      accountId: { type: &quot;string&quot; },
      type: { type: &quot;string&quot; },
      status: { type: &quot;string&quot; },
      priority: { type: &quot;string&quot; },
      assigneeId: { type: &quot;string&quot; },
      isActive: { type: &quot;boolean&quot; },
      page: { type: &quot;number&quot; },
      limit: { type: &quot;number&quot; },
    },
  };

  static override responseSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;data&quot;],
    properties: {
      data: {
        type: &quot;array&quot;,
        items: ACCOUNT_TASK_PAYLOAD_SCHEMA,
      },
    },
  };

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_TASKS_SERVICE_NAME,
      methodName: &quot;GetAccountTasks&quot;,
      protoPath: &quot;dist/proto/accounts/accounts.proto&quot;,
    },
  };
}

export class CreateAccountTask extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Create account task&quot;;

  static override description &#x3D; &quot;This activity creates an account task&quot;;

  static override identifier &#x3D; &quot;accounts:create-account-task&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;accountId&quot;, &quot;title&quot;],
    properties: {
      accountId: { type: &quot;string&quot; },
      title: { type: &quot;string&quot; },
      description: { type: &quot;string&quot; },
      assigneeId: { type: &quot;string&quot; },
      type: { type: &quot;string&quot; },
      status: { type: &quot;string&quot; },
      priority: { type: &quot;string&quot; },
      attachments: {
        type: &quot;array&quot;,
        items: {
          type: &quot;object&quot;,
          required: [&quot;id&quot;, &quot;name&quot;, &quot;url&quot;],
          properties: {
            id: { type: &quot;string&quot; },
            name: { type: &quot;string&quot; },
            url: { type: &quot;string&quot; },
            contentType: { type: &quot;string&quot; },
            size: { type: &quot;number&quot; },
          },
        },
      },
    },
  };

  static override responseSchema &#x3D; ACCOUNT_TASK_PAYLOAD_SCHEMA;

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_TASKS_SERVICE_NAME,
      methodName: &quot;CreateAccountTask&quot;,
      protoPath: &quot;dist/proto/accounts/accounts.proto&quot;,
    },
  };
}

export class UpdateAccountTask extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Update account task&quot;;

  static override description &#x3D; &quot;This activity updates an account task&quot;;

  static override identifier &#x3D; &quot;accounts:update-account-task&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;id&quot;],
    properties: {
      id: { type: &quot;string&quot; },
      title: { type: &quot;string&quot; },
      description: { type: &quot;string&quot; },
      assigneeId: { type: &quot;string&quot; },
      type: { type: &quot;string&quot; },
      status: { type: &quot;string&quot; },
      priority: { type: &quot;string&quot; },
      isActive: { type: &quot;boolean&quot; },
      attachments: {
        type: &quot;array&quot;,
        items: {
          type: &quot;object&quot;,
          required: [&quot;id&quot;, &quot;name&quot;, &quot;url&quot;],
          properties: {
            id: { type: &quot;string&quot; },
            name: { type: &quot;string&quot; },
            url: { type: &quot;string&quot; },
            contentType: { type: &quot;string&quot; },
            size: { type: &quot;number&quot; },
          },
        },
      },
    },
  };

  static override responseSchema &#x3D; ACCOUNT_TASK_PAYLOAD_SCHEMA;

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_TASKS_SERVICE_NAME,
      methodName: &quot;UpdateAccountTask&quot;,
      protoPath: &quot;dist/proto/accounts/accounts.proto&quot;,
    },
  };
}

export class DeleteAccountTask extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Delete account task&quot;;

  static override description &#x3D; &quot;This activity deletes an account task&quot;;

  static override identifier &#x3D; &quot;accounts:delete-account-task&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;id&quot;],
    properties: {
      id: { type: &quot;string&quot; },
    },
  };

  static override responseSchema &#x3D; {};

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_TASKS_SERVICE_NAME,
      methodName: &quot;DeleteAccountTask&quot;,
      protoPath: &quot;dist/proto/accounts/accounts.proto&quot;,
    },
  };
}

// Customer Contacts Activities &#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;

export class GetCustomerContacts extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Get customer contacts&quot;;

  static override description &#x3D; &quot;This activity gets customer contacts&quot;;

  static override identifier &#x3D; &quot;accounts:get-customer-contacts&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    properties: {
      accountId: { type: &quot;string&quot; },
      email: { type: &quot;string&quot; },
      contactType: { type: &quot;string&quot; },
      page: { type: &quot;number&quot; },
      limit: { type: &quot;number&quot; },
    },
  };

  static override responseSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;data&quot;],
    properties: {
      data: {
        type: &quot;array&quot;,
        items: CUSTOMER_CONTACT_PAYLOAD_SCHEMA,
      },
    },
  };

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.CUSTOMER_CONTACTS_SERVICE_NAME,
      methodName: &quot;GetCustomerContacts&quot;,
      protoPath: &quot;dist/proto/accounts/accounts.proto&quot;,
    },
  };
}

export class CreateCustomerContact extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Create customer contact&quot;;

  static override description &#x3D; &quot;This activity creates a customer contact&quot;;

  static override identifier &#x3D; &quot;accounts:create-customer-contact&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;firstName&quot;, &quot;email&quot;],
    properties: {
      firstName: { type: &quot;string&quot; },
      lastName: { type: &quot;string&quot; },
      email: { type: &quot;string&quot; },
      phoneNumber: { type: &quot;string&quot; },
      accountIds: {
        type: &quot;array&quot;,
        items: { type: &quot;string&quot; },
      },
      contactType: { type: &quot;string&quot; },
    },
  };

  static override responseSchema &#x3D; CUSTOMER_CONTACT_PAYLOAD_SCHEMA;

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.CUSTOMER_CONTACTS_SERVICE_NAME,
      methodName: &quot;CreateCustomerContact&quot;,
      protoPath: &quot;dist/proto/accounts/accounts.proto&quot;,
    },
  };
}

export class BulkCreateCustomerContacts extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Bulk create customer contacts&quot;;

  static override description &#x3D; &quot;This activity bulk creates customer contacts&quot;;

  static override identifier &#x3D; &quot;accounts:bulk-create-customer-contacts&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;contacts&quot;],
    properties: {
      contacts: {
        type: &quot;array&quot;,
        items: {
          type: &quot;object&quot;,
          required: [&quot;firstName&quot;, &quot;email&quot;],
          properties: {
            firstName: { type: &quot;string&quot; },
            lastName: { type: &quot;string&quot; },
            email: { type: &quot;string&quot; },
            phoneNumber: { type: &quot;string&quot; },
            accountIds: {
              type: &quot;array&quot;,
              items: { type: &quot;string&quot; },
            },
            contactType: { type: &quot;string&quot; },
          },
        },
      },
    },
  };

  static override responseSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;data&quot;],
    properties: {
      data: {
        type: &quot;array&quot;,
        items: CUSTOMER_CONTACT_PAYLOAD_SCHEMA,
      },
    },
  };

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.CUSTOMER_CONTACTS_SERVICE_NAME,
      methodName: &quot;BulkCreateCustomerContacts&quot;,
      protoPath: &quot;dist/proto/accounts/accounts.proto&quot;,
    },
  };
}

export class UpdateCustomerContact extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Update customer contact&quot;;

  static override description &#x3D; &quot;This activity updates a customer contact&quot;;

  static override identifier &#x3D; &quot;accounts:update-customer-contact&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;id&quot;],
    properties: {
      id: { type: &quot;string&quot; },
      firstName: { type: &quot;string&quot; },
      lastName: { type: &quot;string&quot; },
      email: { type: &quot;string&quot; },
      phoneNumber: { type: &quot;string&quot; },
      accountIds: {
        type: &quot;array&quot;,
        items: { type: &quot;string&quot; },
      },
      contactType: { type: &quot;string&quot; },
    },
  };

  static override responseSchema &#x3D; CUSTOMER_CONTACT_PAYLOAD_SCHEMA;

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.CUSTOMER_CONTACTS_SERVICE_NAME,
      methodName: &quot;UpdateCustomerContact&quot;,
      protoPath: &quot;dist/proto/accounts/accounts.proto&quot;,
    },
  };
}

export class DeleteCustomerContact extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Delete customer contact&quot;;

  static override description &#x3D; &quot;This activity deletes a customer contact&quot;;

  static override identifier &#x3D; &quot;accounts:delete-customer-contact&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;id&quot;],
    properties: {
      id: { type: &quot;string&quot; },
    },
  };

  static override responseSchema &#x3D; {};

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.CUSTOMER_CONTACTS_SERVICE_NAME,
      methodName: &quot;DeleteCustomerContact&quot;,
      protoPath: &quot;dist/proto/accounts/accounts.proto&quot;,
    },
  };
}
</code></pre>
    </div>
</div>









                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'class';
            var COMPODOC_CURRENT_PAGE_URL = 'UpdateAccountRelationshipType.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
