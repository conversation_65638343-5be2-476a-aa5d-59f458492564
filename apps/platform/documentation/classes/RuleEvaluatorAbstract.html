<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content class">
                   <div class="content-data">












<ol class="breadcrumb">
  <li class="breadcrumb-item">Classes</li>
  <li class="breadcrumb-item" >RuleEvaluatorAbstract</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/tickets/routing/abstract/rule-evaluator.abstract.ts</code>
        </p>




            <p class="comment">
                <h3>Implements</h3>
            </p>
            <p class="comment">
                            <code><a href="../interfaces/RuleEvaluator.html" target="_self" >RuleEvaluator</a></code>
            </p>


            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Protected</span>
                                <a href="#operators" >operators</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Abstract</span>
                                <a href="#evaluate" >evaluate</a>
                            </li>
                            <li>
                                    <span class="modifier">Protected</span>
                                <a href="#evaluateSingleRule" >evaluateSingleRule</a>
                            </li>
                            <li>
                                    <span class="modifier">Protected</span>
                                <a href="#getTicketFieldValue" >getTicketFieldValue</a>
                            </li>
                            <li>
                                <a href="#initializeDefaultOperators" >initializeDefaultOperators</a>
                            </li>
                            <li>
                                    <span class="modifier">Protected</span>
                                <a href="#requiresValue" >requiresValue</a>
                            </li>
                            <li>
                                <a href="#validateRule" >validateRule</a>
                            </li>
                            <li>
                                    <span class="modifier">Protected</span>
                                <a href="#validateValueType" >validateValueType</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(teamsService: <a href="../injectables/TeamsService.html" target="_self">TeamsService</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="17" class="link-to-prism">src/tickets/routing/abstract/rule-evaluator.abstract.ts:17</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>teamsService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/TeamsService.html" target="_self" >TeamsService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-properties">
    
    <h3 id="inputs">
        Properties
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="operators"></a>
                    <span class="name">
                            <span class="modifier">Protected</span>
                        <span ><b>operators</b></span>
                        <a href="#operators"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="../miscellaneous/typealiases.html#OperatorFunction" target="_self" >Map&lt;string | OperatorFunction&gt;</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>new Map()</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="17" class="link-to-prism">src/tickets/routing/abstract/rule-evaluator.abstract.ts:17</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="evaluate"></a>
                    <span class="name">
                            <span class="modifier">Abstract</span>
                        <span ><b>evaluate</b></span>
                        <a href="#evaluate"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>evaluate(ticket: Ticket, rules: RuleGroup[], user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="60"
                                    class="link-to-prism">src/tickets/routing/abstract/rule-evaluator.abstract.ts:60</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>ticket</td>
                                            <td>
                                                        <code>Ticket</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>rules</td>
                                            <td>
                                                        <code>RuleGroup[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;Team&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="evaluateSingleRule"></a>
                    <span class="name">
                            <span class="modifier">Protected</span>
                        <span ><b>evaluateSingleRule</b></span>
                        <a href="#evaluateSingleRule"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>evaluateSingleRule(ticket: Ticket, rule: Rule)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="140"
                                    class="link-to-prism">src/tickets/routing/abstract/rule-evaluator.abstract.ts:140</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Evaluates a single rule.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>ticket</td>
                                            <td>
                                                        <code>Ticket</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ticket to evaluate the rule on.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>rule</td>
                                            <td>
                                                        <code>Rule</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The rule to evaluate.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                        </div>
                            <div class="io-description">
                                <p>True if the rule is valid, false otherwise.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTicketFieldValue"></a>
                    <span class="name">
                            <span class="modifier">Protected</span>
                        <span ><b>getTicketFieldValue</b></span>
                        <a href="#getTicketFieldValue"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTicketFieldValue(ticket: Ticket, field: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="130"
                                    class="link-to-prism">src/tickets/routing/abstract/rule-evaluator.abstract.ts:130</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Gets the value of a field from the ticket.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>ticket</td>
                                            <td>
                                                        <code>Ticket</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ticket to get the field value from.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>field</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The field to get the value from.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                <p>The value of the field.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="initializeDefaultOperators"></a>
                    <span class="name">
                        <span ><b>initializeDefaultOperators</b></span>
                        <a href="#initializeDefaultOperators"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>initializeDefaultOperators()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="23"
                                    class="link-to-prism">src/tickets/routing/abstract/rule-evaluator.abstract.ts:23</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="requiresValue"></a>
                    <span class="name">
                            <span class="modifier">Protected</span>
                        <span ><b>requiresValue</b></span>
                        <a href="#requiresValue"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>requiresValue(operator?: RuleOperator)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="71"
                                    class="link-to-prism">src/tickets/routing/abstract/rule-evaluator.abstract.ts:71</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Checks if the operator requires a value.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>operator</td>
                                            <td>
                                                        <code>RuleOperator</code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                                    <p>The operator to check.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                        </div>
                            <div class="io-description">
                                <p>True if the operator requires a value, false otherwise.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateRule"></a>
                    <span class="name">
                        <span ><b>validateRule</b></span>
                        <a href="#validateRule"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>validateRule(rule: Rule)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="152"
                                    class="link-to-prism">src/tickets/routing/abstract/rule-evaluator.abstract.ts:152</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>rule</td>
                                            <td>
                                                        <code>Rule</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../interfaces/ValidationResult.html" target="_self" >ValidationResult</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateValueType"></a>
                    <span class="name">
                            <span class="modifier">Protected</span>
                        <span ><b>validateValueType</b></span>
                        <a href="#validateValueType"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateValueType(rule: Rule)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="82"
                                    class="link-to-prism">src/tickets/routing/abstract/rule-evaluator.abstract.ts:82</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Validates the value type for the rule.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>rule</td>
                                            <td>
                                                        <code>Rule</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The rule to validate.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../interfaces/ValidationError.html" target="_self" >ValidationError | null</a></code>

                        </div>
                            <div class="io-description">
                                <p>The validation error if the value type is invalid, null otherwise.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>





    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import type { Rule, RuleGroup } from &quot;@repo/thena-platform-entities&quot;;
import { RuleOperator, Team, Ticket } from &quot;@repo/thena-platform-entities&quot;;
import { CurrentUser } from &quot;../../../common/decorators&quot;;
import { TeamsService } from &quot;../../../teams/services/teams.service&quot;;
import {
  RuleEvaluator,
  ValidationError,
  ValidationResult,
} from &quot;../interfaces&quot;;

export type OperatorFunction&lt;T &#x3D; any&gt; &#x3D; (
  fieldValue: T,
  conditionValue: T,
) &#x3D;&gt; boolean;

export abstract class RuleEvaluatorAbstract implements RuleEvaluator {
  protected operators: Map&lt;string, OperatorFunction&gt; &#x3D; new Map();

  constructor(private readonly teamsService: TeamsService) {
    this.initializeDefaultOperators();
  }

  initializeDefaultOperators(): void {
    this.operators.set(RuleOperator.EQUALS, (f, c) &#x3D;&gt; f &#x3D;&#x3D;&#x3D; c);
    this.operators.set(RuleOperator.NOT_EQUALS, (f, c) &#x3D;&gt; f !&#x3D;&#x3D; c);
    this.operators.set(RuleOperator.CONTAINS, (f, c) &#x3D;&gt;
      String(f).toLowerCase().includes(String(c).toLowerCase()),
    );
    this.operators.set(
      RuleOperator.NOT_CONTAINS,
      (f, c) &#x3D;&gt; !String(f).toLowerCase().includes(String(c).toLowerCase()),
    );
    this.operators.set(RuleOperator.GREATER_THAN, (f, c) &#x3D;&gt; f &gt; c);
    this.operators.set(RuleOperator.LESS_THAN, (f, c) &#x3D;&gt; f &lt; c);
    this.operators.set(
      RuleOperator.IN,
      (f, c) &#x3D;&gt; Array.isArray(c) &amp;&amp; c.includes(f),
    );
    this.operators.set(
      RuleOperator.NOT_IN,
      (f, c) &#x3D;&gt; Array.isArray(c) &amp;&amp; !c.includes(f),
    );
    this.operators.set(RuleOperator.MATCHES, (f, c) &#x3D;&gt; {
      try {
        return new RegExp(c).test(String(f));
      } catch {
        return false;
      }
    });
    this.operators.set(
      RuleOperator.IS_NULL,
      (f) &#x3D;&gt; f &#x3D;&#x3D;&#x3D; null || f &#x3D;&#x3D;&#x3D; undefined,
    );
    this.operators.set(
      RuleOperator.IS_NOT_NULL,
      (f) &#x3D;&gt; f !&#x3D;&#x3D; null &amp;&amp; f !&#x3D;&#x3D; undefined,
    );
  }

  abstract evaluate(
    ticket: Ticket,
    rules: RuleGroup[],
    user: CurrentUser,
  ): Promise&lt;Team&gt;;

  /**
   * Checks if the operator requires a value.
   * @param operator The operator to check.
   * @returns True if the operator requires a value, false otherwise.
   */
  protected requiresValue(operator?: RuleOperator): boolean {
    return ![RuleOperator.IS_NULL, RuleOperator.IS_NOT_NULL].includes(
      operator as RuleOperator,
    );
  }

  /**
   * Validates the value type for the rule.
   * @param rule The rule to validate.
   * @returns The validation error if the value type is invalid, null otherwise.
   */
  protected validateValueType(rule: Rule): ValidationError | null {
    if (!rule.operator) return null;

    switch (rule.operator) {
      case RuleOperator.GREATER_THAN:
      case RuleOperator.LESS_THAN:
        if (typeof rule.value !&#x3D;&#x3D; &quot;number&quot;) {
          return {
            field: &quot;value&quot;,
            code: &quot;INVALID_VALUE_TYPE&quot;,
            message: &#x60;Value must be a number for operator ${rule.operator}&#x60;,
          };
        }
        break;

      case RuleOperator.IN:
      case RuleOperator.NOT_IN:
        if (!Array.isArray(rule.value)) {
          return {
            field: &quot;value&quot;,
            code: &quot;INVALID_VALUE_TYPE&quot;,
            message: &#x60;Value must be an array for operator ${rule.operator}&#x60;,
          };
        }
        break;

      case RuleOperator.MATCHES:
        try {
          new RegExp(rule.value);
        } catch {
          return {
            field: &quot;value&quot;,
            code: &quot;INVALID_REGEX&quot;,
            message: &quot;Value must be a valid regular expression&quot;,
          };
        }
        break;
    }

    return null;
  }

  /**
   * Gets the value of a field from the ticket.
   * @param ticket The ticket to get the field value from.
   * @param field The field to get the value from.
   * @returns The value of the field.
   */
  protected getTicketFieldValue(ticket: Ticket, field: string): any {
    return field.split(&quot;.&quot;).reduce((obj, key) &#x3D;&gt; obj?.[key], ticket);
  }

  /**
   * Evaluates a single rule.
   * @param ticket The ticket to evaluate the rule on.
   * @param rule The rule to evaluate.
   * @returns True if the rule is valid, false otherwise.
   */
  protected evaluateSingleRule(ticket: Ticket, rule: Rule): boolean {
    const ticketValue &#x3D; this.getTicketFieldValue(ticket, rule.field);
    const operator &#x3D; this.operators.get(rule.operator);

    // If the operator is not found, throw an error
    if (!operator) {
      throw new Error(&#x60;Unknown operator: ${rule.operator}&#x60;);
    }

    return operator(ticketValue, rule.value);
  }

  validateRule(rule: Rule): ValidationResult {
    const errors: ValidationError[] &#x3D; [];

    if (!rule) {
      return {
        isValid: false,
        errors: [
          { field: &quot;rule&quot;, code: &quot;required&quot;, message: &quot;Rule is required&quot; },
        ],
      };
    }

    // Operator validation
    if (!rule.operator || !this.operators.has(rule.operator)) {
      errors.push({
        code: &quot;INVALID_OPERATOR&quot;,
        field: &quot;operator&quot;,
        message: &#x60;Operator &#x27;${rule.operator}&#x27; is not supported&#x60;,
      });
    }

    // Field validation
    if (!rule.field) {
      errors.push({
        code: &quot;MISSING_FIELD&quot;,
        field: &quot;field&quot;,
        message: &quot;Field is required&quot;,
      });
    }

    // Value validation
    if (
      this.requiresValue(rule.operator) &amp;&amp;
      (rule.value &#x3D;&#x3D;&#x3D; undefined || rule.value &#x3D;&#x3D;&#x3D; null)
    ) {
      errors.push({
        code: &quot;MISSING_VALUE&quot;,
        field: &quot;value&quot;,
        message: &quot;Value is required&quot;,
      });
    }

    return {
      isValid: errors.length &#x3D;&#x3D;&#x3D; 0,
      errors,
    };
  }
}
</code></pre>
    </div>
</div>









                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'class';
            var COMPODOC_CURRENT_PAGE_URL = 'RuleEvaluatorAbstract.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
