<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content class">
                   <div class="content-data">












<ol class="breadcrumb">
  <li class="breadcrumb-item">Classes</li>
  <li class="breadcrumb-item" >CustomFieldData</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/custom-field/dto/custom-field.dto.ts</code>
        </p>






            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier"></span>
                                <a href="#autoAddToAllForms" >autoAddToAllForms</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#createdAt" >createdAt</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#defaultValue" >defaultValue</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#editableByCustomer" >editableByCustomer</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#fieldType" >fieldType</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#hintText" >hintText</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#isActive" >isActive</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#mandatoryOnClose" >mandatoryOnClose</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#mandatoryOnCreation" >mandatoryOnCreation</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#metadata" >metadata</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#name" >name</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#options" >options</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#organization" >organization</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#organizationId" >organizationId</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#placeholderText" >placeholderText</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#source" >source</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#team" >team</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#teamId" >teamId</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#uid" >uid</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#updatedAt" >updatedAt</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#visibleToCustomer" >visibleToCustomer</a>
                            </li>
                        </ul>
                    </td>
                </tr>






        </tbody>
    </table>
</section>


            <section data-compodoc="block-properties">
    
    <h3 id="inputs">
        Properties
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="autoAddToAllForms"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>autoAddToAllForms</b></span>
                        <a href="#autoAddToAllForms"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty()<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="267" class="link-to-prism">src/custom-field/dto/custom-field.dto.ts:267</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createdAt"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>createdAt</b></span>
                        <a href="#createdAt"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date" target="_blank" >Date</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty()<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="250" class="link-to-prism">src/custom-field/dto/custom-field.dto.ts:250</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="defaultValue"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>defaultValue</b></span>
                        <a href="#defaultValue"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty()<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="288" class="link-to-prism">src/custom-field/dto/custom-field.dto.ts:288</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="editableByCustomer"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>editableByCustomer</b></span>
                        <a href="#editableByCustomer"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty()<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="285" class="link-to-prism">src/custom-field/dto/custom-field.dto.ts:285</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="fieldType"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>fieldType</b></span>
                        <a href="#fieldType"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>CustomFieldType</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty({enum: CustomFieldType})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="236" class="link-to-prism">src/custom-field/dto/custom-field.dto.ts:236</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="hintText"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>hintText</b></span>
                        <a href="#hintText"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty()<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="270" class="link-to-prism">src/custom-field/dto/custom-field.dto.ts:270</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isActive"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>isActive</b></span>
                        <a href="#isActive"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty()<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="256" class="link-to-prism">src/custom-field/dto/custom-field.dto.ts:256</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="mandatoryOnClose"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>mandatoryOnClose</b></span>
                        <a href="#mandatoryOnClose"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty()<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="279" class="link-to-prism">src/custom-field/dto/custom-field.dto.ts:279</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="mandatoryOnCreation"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>mandatoryOnCreation</b></span>
                        <a href="#mandatoryOnCreation"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty()<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="276" class="link-to-prism">src/custom-field/dto/custom-field.dto.ts:276</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="metadata"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>metadata</b></span>
                        <a href="#metadata"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>Record&lt;string | &gt; | null</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty({nullable: true})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="247" class="link-to-prism">src/custom-field/dto/custom-field.dto.ts:247</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="name"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>name</b></span>
                        <a href="#name"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty()<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="224" class="link-to-prism">src/custom-field/dto/custom-field.dto.ts:224</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="options"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>options</b></span>
                        <a href="#options"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>CustomFieldOption[] | null</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty({nullable: true})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="244" class="link-to-prism">src/custom-field/dto/custom-field.dto.ts:244</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="organization"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>organization</b></span>
                        <a href="#organization"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>literal type</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty()<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="239" class="link-to-prism">src/custom-field/dto/custom-field.dto.ts:239</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="organizationId"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>organizationId</b></span>
                        <a href="#organizationId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty()<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="230" class="link-to-prism">src/custom-field/dto/custom-field.dto.ts:230</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="placeholderText"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>placeholderText</b></span>
                        <a href="#placeholderText"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty()<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="273" class="link-to-prism">src/custom-field/dto/custom-field.dto.ts:273</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="source"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>source</b></span>
                        <a href="#source"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>CustomFieldSource</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty({enum: CustomFieldSource})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="233" class="link-to-prism">src/custom-field/dto/custom-field.dto.ts:233</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="team"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>team</b></span>
                        <a href="#team"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>literal type</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty()<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="259" class="link-to-prism">src/custom-field/dto/custom-field.dto.ts:259</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="teamId"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>teamId</b></span>
                        <a href="#teamId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty()<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="264" class="link-to-prism">src/custom-field/dto/custom-field.dto.ts:264</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="uid"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>uid</b></span>
                        <a href="#uid"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty()<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="227" class="link-to-prism">src/custom-field/dto/custom-field.dto.ts:227</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updatedAt"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>updatedAt</b></span>
                        <a href="#updatedAt"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date" target="_blank" >Date</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty()<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="253" class="link-to-prism">src/custom-field/dto/custom-field.dto.ts:253</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="visibleToCustomer"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>visibleToCustomer</b></span>
                        <a href="#visibleToCustomer"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty()<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="282" class="link-to-prism">src/custom-field/dto/custom-field.dto.ts:282</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
</section>







    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { ApiProperty, ApiPropertyOptional } from &quot;@nestjs/swagger&quot;;
import {
  CustomFieldOption,
  CustomFieldSource,
  CustomFieldType,
} from &quot;@repo/thena-platform-entities&quot;;
import { Type } from &quot;class-transformer&quot;;
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsEnum,
  IsInt,
  IsObject,
  IsOptional,
  IsString,
  Matches,
  MaxLength,
  MinLength,
  ValidateNested,
} from &quot;class-validator&quot;;

class CommonCustomFieldDto {
  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ description: &quot;Whether the custom field is active&quot; })
  isActive?: boolean;
}

export class CreateCustomFieldDto extends CommonCustomFieldDto {
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: &quot;The description of the custom field&quot; })
  description?: string;

  @IsString()
  @MinLength(1)
  @MaxLength(100)
  @Matches(/^[a-zA-Z0-9\s-_]+$/, {
    message:
      &quot;name can only contain alphanumeric characters, spaces, hyphens, and underscores&quot;,
  })
  @ApiProperty({ description: &quot;The name of the custom field&quot; })
  name: string;

  @IsEnum(CustomFieldSource)
  @ApiProperty({ description: &quot;The source of the custom field&quot; })
  source: CustomFieldSource;

  @IsEnum(CustomFieldType)
  @ApiProperty({ description: &quot;The type of the custom field&quot; })
  fieldType: CustomFieldType;

  @IsArray()
  @IsOptional()
  @ApiPropertyOptional({ description: &quot;The options of the custom field&quot; })
  options?: CustomFieldOption[];

  @IsObject()
  @IsOptional()
  @ValidateNested()
  @ApiPropertyOptional({ description: &quot;The metadata of the custom field&quot; })
  metadata?: {
    [key: string]: string | number | boolean | null;
  };

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description: &quot;The placeholder text of the custom field&quot;,
  })
  placeholderText: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: &quot;The hint text of the custom field&quot; })
  hintText: string;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({
    description: &quot;Whether the custom field is mandatory on close&quot;,
  })
  mandatoryOnClose: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({
    description: &quot;Whether the custom field is mandatory on creation&quot;,
  })
  mandatoryOnCreation: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({
    description: &quot;Whether the custom field is visible to customer&quot;,
  })
  visibleToCustomer: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({
    description: &quot;Whether the custom field is editable by customer&quot;,
  })
  editableByCustomer: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({
    description: &quot;Whether the custom field is auto added to all forms&quot;,
  })
  autoAddToAllForms: boolean;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: &quot;The default value of the custom field&quot; })
  defaultValue: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: &quot;The team id of the custom field&quot; })
  teamId?: string;
}

class CustomFieldOptionDto {
  @IsString()
  @IsOptional()
  id: string;

  @ApiProperty()
  @IsString()
  value: string;
}

class UpdateFieldsDto {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @ApiPropertyOptional()
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() &#x3D;&gt; CustomFieldOptionDto)
  options?: CustomFieldOptionDto[];

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  defaultValue?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  regexForValidation?: string;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  mandatoryOnCreation?: boolean;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  mandatoryOnClose?: boolean;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  visibleToCustomer?: boolean;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  editableByCustomer?: boolean;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  auto_add_to_all_forms?: boolean;
}

class CustomFieldUpdateData {
  @ApiProperty()
  @IsString()
  @MinLength(1, { message: &quot;fieldId must be a non-empty string&quot; })
  fieldId: string;

  @ApiProperty()
  @IsInt({ message: &quot;version must be an integer&quot; })
  version: number;

  // @ApiPropertyOptional()
  // @IsBoolean()
  // @IsOptional()
  // deleteFromExistingForms?: boolean;

  @ApiPropertyOptional()
  @ValidateNested()
  @Type(() &#x3D;&gt; UpdateFieldsDto)
  updates: UpdateFieldsDto;
}

export class UpdateCustomFieldDto {
  @ApiProperty({ type: [CustomFieldUpdateData] })
  @ValidateNested()
  @Type(() &#x3D;&gt; CustomFieldUpdateData)
  fields: CustomFieldUpdateData[];
}

class CustomFieldData {
  @ApiProperty()
  name: string;

  @ApiProperty()
  uid: string;

  @ApiProperty()
  organizationId: string;

  @ApiProperty({ enum: CustomFieldSource })
  source: CustomFieldSource;

  @ApiProperty({ enum: CustomFieldType })
  fieldType: CustomFieldType;

  @ApiProperty()
  organization: {
    id: string;
  };

  @ApiProperty({ nullable: true })
  options: CustomFieldOption[] | null;

  @ApiProperty({ nullable: true })
  metadata: Record&lt;string, unknown&gt; | null;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty()
  isActive: boolean;

  @ApiProperty()
  team: {
    id: string;
  };

  @ApiProperty()
  teamId: string;

  @ApiProperty()
  autoAddToAllForms: boolean;

  @ApiProperty()
  hintText: string;

  @ApiProperty()
  placeholderText: string;

  @ApiProperty()
  mandatoryOnCreation: boolean;

  @ApiProperty()
  mandatoryOnClose: boolean;

  @ApiProperty()
  visibleToCustomer: boolean;

  @ApiProperty()
  editableByCustomer: boolean;

  @ApiProperty()
  defaultValue: string;
}

export class BatchCustomFieldResponseDto {
  @ApiProperty({ type: [CustomFieldData] })
  data: CustomFieldData[];

  @ApiProperty()
  status: boolean;

  @ApiProperty()
  message: string;

  @ApiProperty()
  timestamp: Date;
}

export class CustomFieldResponseDto {
  @ApiProperty()
  data: CustomFieldData;

  @ApiProperty()
  status: boolean;

  @ApiProperty()
  message: string;

  @ApiProperty()
  timestamp: Date;
}

export class GetAllCustomFieldsResponse {
  @ApiProperty({ type: [CustomFieldData] })
  data: {
    items: CustomFieldData[];
  };

  @ApiProperty()
  status: boolean;

  @ApiProperty()
  message: string;

  @ApiProperty()
  timestamp: Date;
}

export class CustomFieldTypesData {
  @ApiProperty({ type: [String] })
  text: string[];

  @ApiProperty({ type: [String] })
  numeric: string[];

  @ApiProperty({ type: [String] })
  choice: string[];

  @ApiProperty({ type: [String] })
  date: string[];

  @ApiProperty({ type: [String] })
  user: string[];

  @ApiProperty({ type: [String] })
  specialized: string[];

  @ApiProperty({ type: [String] })
  file: string[];

  @ApiProperty({ type: [String] })
  calculated: string[];

  @ApiProperty({ type: [String] })
  lookup: string[];

  @ApiProperty({ type: [String] })
  geographic: string[];

  @ApiProperty({ type: [String] })
  rating: string[];

  @ApiProperty({ type: [String] })
  toggle: string[];
}

export class GetAllCustomFieldTypesResponse {
  @ApiProperty({ type: CustomFieldTypesData })
  data: CustomFieldTypesData;

  @ApiProperty()
  status: boolean;

  @ApiProperty()
  message: string;

  @ApiProperty()
  timestamp: Date;
}

export class DeleteFieldItemDto {
  @ApiProperty()
  @IsString()
  @MinLength(1, { message: &quot;fieldId must be a non-empty string&quot; })
  fieldId: string;

  @ApiProperty()
  @IsInt({ message: &quot;version must be an integer&quot; })
  version: number;
}

export class DeleteCustomFieldDto {
  @IsArray({ message: &quot;Fields must be an array&quot; })
  @ArrayMinSize(1, { message: &quot;At least one field is required&quot; })
  @ValidateNested({ each: true })
  @Type(() &#x3D;&gt; DeleteFieldItemDto) // Required for nested validation
  @ApiProperty({ type: [DeleteFieldItemDto] })
  fields: DeleteFieldItemDto[];
}
</code></pre>
    </div>
</div>









                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'class';
            var COMPODOC_CURRENT_PAGE_URL = 'CustomFieldData.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
