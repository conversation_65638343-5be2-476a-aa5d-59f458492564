<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content class">
                   <div class="content-data">












<ol class="breadcrumb">
  <li class="breadcrumb-item">Classes</li>
  <li class="breadcrumb-item" >TeamInactiveException</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/tags/services/team-tags.service.ts</code>
        </p>



            <p class="comment">
                <h3>Extends</h3>
            </p>
            <p class="comment">
                        <code>BadRequestException</code>
            </p>




            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="26" class="link-to-prism">src/tags/services/team-tags.service.ts:26</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>teamId</td>
                                                  
                                                        <td>
                                                                        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>








    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  BadRequestException,
  ConflictException,
  Inject,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from &quot;@nestjs/common&quot;;
import {
  TagRepository,
  TagsType,
  TeamRepository,
} from &quot;@repo/thena-platform-entities&quot;;
import { CurrentUser } from &quot;../../common/decorators&quot;;
import { Not } from &quot;typeorm&quot;;
import { ValidationService } from &quot;../../common/services/validation.service&quot;;
import { UsersService } from &quot;../../users/services/users.service&quot;;
import { CreateTagDto, UpdateTagDto } from &quot;../dto/tag.dto&quot;;
import {
  CreateTagsResponse,
  DeleteTagResponse,
  GetTagsResponse,
  UpdateTagsResponse,
} from &quot;../interfaces/tag-response.interface&quot;;

export class TeamInactiveException extends BadRequestException {
  constructor(teamId: string) {
    super(&#x60;Team with ID &quot;${teamId}&quot; is inactive&#x60;);
  }
}

export class DuplicateTagException extends ConflictException {
  constructor(tagName: string, teamId: string) {
    super(&#x60;Tag with name &quot;${tagName}&quot; already exists in team &quot;${teamId}&quot;&#x60;);
  }
}

@Injectable()
export class TeamTagsService {
  constructor(
    private tagRepository: TagRepository,
    private teamRepository: TeamRepository,
    @Inject(&quot;ValidationService&quot;)
    private validationService: ValidationService,
    private usersService: UsersService,
  ) {}

  /**
   * Finds all active tags for a specified team
   */
  async findAllByTeam(teamUuid: string): Promise&lt;GetTagsResponse&gt; {
    // Step 1: Input Validation
    this.validationService.validateUlids([
      { id: teamUuid, fieldName: &quot;Team ID&quot; },
    ]);

    // Step 2: Team Verification
    const team &#x3D; await this.teamRepository.findByUuid(teamUuid);
    if (!team) {
      throw new NotFoundException(&#x60;Team with ID &quot;${teamUuid}&quot; not found&#x60;);
    }
    if (!team.isActive) {
      throw new TeamInactiveException(teamUuid);
    }

    // Step 3: Fetch Tags
    const tags &#x3D; await this.tagRepository.findAll({
      where: { teamId: team.id, tagType: TagsType.TICKET },
      order: { createdAt: &quot;DESC&quot; },
    });

    // Step 4: Return Response
    return {
      status: true,
      timestamp: new Date(),
      teamUuid,
      data: {
        count: tags.length,
        items: tags,
      },
      message:
        tags.length &gt; 0
          ? &#x60;Found ${tags.length} tags for team&#x60;
          : &quot;No active tags found for team&quot;,
    };
  }

  /**
   * Creates a new tag for a specified team
   */
  async create(
    user: CurrentUser,
    teamUuid: string,
    createTagDto: CreateTagDto,
  ): Promise&lt;CreateTagsResponse&gt; {
    // Step 1: Input Validation
    this.validationService.validateUlids([
      { id: teamUuid, fieldName: &quot;Team UUID&quot; },
    ]);

    // Step 2: User Context
    if (!user) {
      throw new UnauthorizedException(&quot;User is not authenticated!&quot;);
    }

    // Step 3: Team Verification
    const team &#x3D; await this.teamRepository.findByUuid(teamUuid);
    if (!team) {
      throw new NotFoundException(&#x60;Team with ID &quot;${teamUuid}&quot; not found&#x60;);
    }
    if (!team.isActive) {
      throw new TeamInactiveException(teamUuid);
    }

    // Step 4: Duplicate Check
    const existingTag &#x3D; await this.tagRepository.findByCondition({
      where: {
        organizationId: user.orgId,
        teamId: team.id,
        name: createTagDto.name,
        tagType: TagsType.TICKET,
        isActive: true,
      },
    });

    if (existingTag) {
      throw new DuplicateTagException(createTagDto.name, teamUuid);
    }

    // Step 5: Create and Save Tag
    const tag &#x3D; this.tagRepository.create({
      ...createTagDto,
      team,
      organizationId: user.orgId,
      tagType: TagsType.TICKET,
    });
    const savedTag &#x3D; await this.tagRepository.save(tag);

    // Step 6: Return Response
    return {
      status: true,
      timestamp: new Date(),
      teamUuid,
      data: {
        count: 1,
        items: [savedTag],
      },
      message: &#x60;Successfully created tag &quot;${savedTag.name}&quot;&#x60;,
    };
  }

  /**
   * Updates an existing tag for a specified team
   */
  async update(
    teamUuid: string,
    tagUuid: string,
    updateTagDto: UpdateTagDto,
  ): Promise&lt;UpdateTagsResponse&gt; {
    // Step 1: Input Validation
    this.validationService.validateUlids([
      { id: teamUuid, fieldName: &quot;Team ID&quot; },
      { id: tagUuid, fieldName: &quot;Tag ID&quot; },
    ]);

    // Step 2: Team Verification
    const team &#x3D; await this.teamRepository.findByUuid(teamUuid);
    if (!team || !team.isActive) {
      throw new NotFoundException(
        &#x60;Team with ID &quot;${teamUuid}&quot; not found or inactive&#x60;,
      );
    }

    // Step 3: Tag Verification
    const existingTag &#x3D; await this.tagRepository.findByCondition({
      where: {
        uid: tagUuid,
        teamId: team.id,
        tagType: TagsType.TICKET,
        isActive: true,
      },
      relations: [&quot;team&quot;],
    });

    if (!existingTag) {
      throw new NotFoundException(
        &#x60;Tag with ID &quot;${tagUuid}&quot; not found in team &quot;${teamUuid}&quot;&#x60;,
      );
    }

    // Step 4: Duplicate Check
    if (updateTagDto.name &amp;&amp; updateTagDto.name !&#x3D;&#x3D; existingTag.name) {
      const duplicateTag &#x3D; await this.tagRepository.findByCondition({
        where: {
          teamId: team.id,
          name: updateTagDto.name,
          id: Not(existingTag.id),
          tagType: TagsType.TICKET,
        },
      });

      if (duplicateTag) {
        throw new DuplicateTagException(updateTagDto.name, teamUuid);
      }
    }

    // Step 5: Update Tag
    Object.assign(existingTag, {
      name: updateTagDto.name ?? existingTag.name,
      color: updateTagDto.color ?? existingTag.color,
      description: updateTagDto.description ?? existingTag.description,
    });

    const updatedTag &#x3D; await this.tagRepository.save(existingTag);

    // Step 6: Return Response
    return {
      status: true,
      timestamp: new Date(),
      teamUuid: team.uid,
      data: {
        count: 1,
        items: [updatedTag],
      },
      message: &#x60;Successfully updated tag &quot;${updatedTag.name}&quot;&#x60;,
    };
  }

  /**
   * Removes an existing tag for a specified team
   */
  async remove(teamUuid: string, tagUuid: string): Promise&lt;DeleteTagResponse&gt; {
    // Step 1: Input Validation
    this.validationService.validateUlids([
      { id: teamUuid, fieldName: &quot;Team ID&quot; },
      { id: tagUuid, fieldName: &quot;Tag ID&quot; },
    ]);

    // Step 2: Team Verification
    const team &#x3D; await this.teamRepository.findByUuid(teamUuid);
    if (!team || !team.isActive) {
      throw new NotFoundException(
        &#x60;Team with ID &quot;${teamUuid}&quot; not found or inactive&#x60;,
      );
    }

    // Step 3: Tag Verification
    const existingTag &#x3D; await this.tagRepository.findByCondition({
      where: { uid: tagUuid, teamId: team.id, tagType: TagsType.TICKET },
    });

    if (!existingTag) {
      throw new NotFoundException(
        &#x60;Tag with ID &quot;${tagUuid}&quot; not found in team &quot;${teamUuid}&quot;&#x60;,
      );
    }

    // Step 4: Remove Tag
    await this.tagRepository.remove(existingTag);

    // Step 5: Return Response
    return {
      status: true,
      timestamp: new Date(),
      teamUuid,
      removedTagId: tagUuid,
      message: &#x60;Successfully removed tag &quot;${existingTag.name}&quot;&#x60;,
    };
  }
}
</code></pre>
    </div>
</div>









                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'class';
            var COMPODOC_CURRENT_PAGE_URL = 'TeamInactiveException.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
