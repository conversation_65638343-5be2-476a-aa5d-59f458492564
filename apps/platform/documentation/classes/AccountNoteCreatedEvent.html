<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content class">
                   <div class="content-data">












<ol class="breadcrumb">
  <li class="breadcrumb-item">Classes</li>
  <li class="breadcrumb-item" >AccountNoteCreatedEvent</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/common/workflows/events/accounts.events.ts</code>
        </p>



            <p class="comment">
                <h3>Extends</h3>
            </p>
            <p class="comment">
                        <code>AbstractWorkflowEvent</code>
            </p>



            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Static</span>
                                    <span class="modifier"></span>
                                <a href="#description" >description</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                    <span class="modifier"></span>
                                <a href="#eventName" >eventName</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                    <span class="modifier"></span>
                                <a href="#eventType" >eventType</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                    <span class="modifier"></span>
                                <a href="#schema" >schema</a>
                            </li>
                        </ul>
                    </td>
                </tr>






        </tbody>
    </table>
</section>


            <section data-compodoc="block-properties">
    
    <h3 id="inputs">
        Properties
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="description"></a>
                    <span class="name">
                            <span class="modifier">Static</span>
                            <span class="modifier"></span>
                        <span ><b>description</b></span>
                        <a href="#description"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&quot;This event is triggered when an account note is created&quot;</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="358" class="link-to-prism">src/common/workflows/events/accounts.events.ts:358</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="eventName"></a>
                    <span class="name">
                            <span class="modifier">Static</span>
                            <span class="modifier"></span>
                        <span ><b>eventName</b></span>
                        <a href="#eventName"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&quot;Account note created&quot;</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="354" class="link-to-prism">src/common/workflows/events/accounts.events.ts:354</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="eventType"></a>
                    <span class="name">
                            <span class="modifier">Static</span>
                            <span class="modifier"></span>
                        <span ><b>eventType</b></span>
                        <a href="#eventType"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>AccountEvents.ACCOUNT_NOTE_CREATED</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="356" class="link-to-prism">src/common/workflows/events/accounts.events.ts:356</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="schema"></a>
                    <span class="name">
                            <span class="modifier">Static</span>
                            <span class="modifier"></span>
                        <span ><b>schema</b></span>
                        <a href="#schema"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
    type: &quot;object&quot;,
    required: [&quot;message&quot;, &quot;messageAttributes&quot;],
    properties: {
      message: {
        type: &quot;object&quot;,
        required: [
          &quot;actor&quot;,
          &quot;orgId&quot;,
          &quot;eventId&quot;,
          &quot;payload&quot;,
          &quot;eventType&quot;,
          &quot;timestamp&quot;,
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: &quot;object&quot;,
            properties: {
              note: ACCOUNT_NOTE_PAYLOAD_SCHEMA,
            },
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  }</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="361" class="link-to-prism">src/common/workflows/events/accounts.events.ts:361</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
</section>







    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { AccountEvents } from &quot;@repo/thena-eventbridge&quot;;
import { AbstractWorkflowEvent } from &quot;@repo/workflow-engine&quot;;
import {
  ACCOUNT_ACTIVITY_PAYLOAD_SCHEMA,
  ACCOUNT_NOTE_PAYLOAD_SCHEMA,
  ACCOUNT_PAYLOAD_SCHEMA,
  ACCOUNT_RELATIONSHIP_PAYLOAD_SCHEMA,
  ACCOUNT_TASK_PAYLOAD_SCHEMA,
  COMMON_MESSAGE_PROPERTIES,
  CUSTOMER_CONTACT_PAYLOAD_SCHEMA,
  MESSAGE_ATTRIBUTES_SCHEMA,
} from &quot;../constants&quot;;

// Account Events &#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;

export class AccountCreatedEvent extends AbstractWorkflowEvent {
  static override eventName &#x3D; &quot;Account created&quot;;

  static override eventType &#x3D; AccountEvents.ACCOUNT_CREATED;

  static override description &#x3D;
    &quot;This event is triggered when an account is created&quot;;

  static override schema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;message&quot;, &quot;messageAttributes&quot;],
    properties: {
      message: {
        type: &quot;object&quot;,
        required: [
          &quot;actor&quot;,
          &quot;orgId&quot;,
          &quot;eventId&quot;,
          &quot;payload&quot;,
          &quot;eventType&quot;,
          &quot;timestamp&quot;,
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: &quot;object&quot;,
            properties: {
              account: ACCOUNT_PAYLOAD_SCHEMA,
            },
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };
}

export class AccountUpdatedEvent extends AbstractWorkflowEvent {
  static override eventName &#x3D; &quot;Account updated&quot;;

  static override eventType &#x3D; AccountEvents.ACCOUNT_UPDATED;

  static override description &#x3D;
    &quot;This event is triggered when an account is updated&quot;;

  static override schema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;message&quot;, &quot;messageAttributes&quot;],
    properties: {
      message: {
        type: &quot;object&quot;,
        required: [
          &quot;actor&quot;,
          &quot;orgId&quot;,
          &quot;eventId&quot;,
          &quot;payload&quot;,
          &quot;eventType&quot;,
          &quot;timestamp&quot;,
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: &quot;object&quot;,
            properties: {
              account: ACCOUNT_PAYLOAD_SCHEMA,
              previousAccount: ACCOUNT_PAYLOAD_SCHEMA,
            },
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };
}

export class AccountDeletedEvent extends AbstractWorkflowEvent {
  static override eventName &#x3D; &quot;Account deleted&quot;;
  static override eventType &#x3D; AccountEvents.ACCOUNT_DELETED;
  static override description &#x3D;
    &quot;This event is triggered when an account is deleted&quot;;
  static override schema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;message&quot;, &quot;messageAttributes&quot;],
    properties: {
      message: {
        type: &quot;object&quot;,
        required: [
          &quot;actor&quot;,
          &quot;orgId&quot;,
          &quot;eventId&quot;,
          &quot;payload&quot;,
          &quot;eventType&quot;,
          &quot;timestamp&quot;,
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: &quot;object&quot;,
            properties: {
              previousAccount: ACCOUNT_PAYLOAD_SCHEMA,
            },
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };
}

// Account Relationship Events &#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;

export class AccountRelationshipCreatedEvent extends AbstractWorkflowEvent {
  static override eventName &#x3D; &quot;Account relationship created&quot;;

  static override eventType &#x3D; AccountEvents.ACCOUNT_RELATIONSHIP_CREATED;

  static override description &#x3D;
    &quot;This event is triggered when an account relationship is created&quot;;

  static override schema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;message&quot;, &quot;messageAttributes&quot;],
    properties: {
      message: {
        type: &quot;object&quot;,
        required: [
          &quot;actor&quot;,
          &quot;orgId&quot;,
          &quot;eventId&quot;,
          &quot;payload&quot;,
          &quot;eventType&quot;,
          &quot;timestamp&quot;,
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: &quot;object&quot;,
            properties: {
              relationship: ACCOUNT_RELATIONSHIP_PAYLOAD_SCHEMA,
            },
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };
}

export class AccountRelationshipUpdatedEvent extends AbstractWorkflowEvent {
  static override eventName &#x3D; &quot;Account relationship updated&quot;;

  static override eventType &#x3D; AccountEvents.ACCOUNT_RELATIONSHIP_UPDATED;

  static override description &#x3D;
    &quot;This event is triggered when an account relationship is updated&quot;;

  static override schema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;message&quot;, &quot;messageAttributes&quot;],
    properties: {
      message: {
        type: &quot;object&quot;,
        required: [
          &quot;actor&quot;,
          &quot;orgId&quot;,
          &quot;eventId&quot;,
          &quot;payload&quot;,
          &quot;eventType&quot;,
          &quot;timestamp&quot;,
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: &quot;object&quot;,
            properties: {
              previousRelationship: ACCOUNT_RELATIONSHIP_PAYLOAD_SCHEMA,
              relationship: ACCOUNT_RELATIONSHIP_PAYLOAD_SCHEMA,
            },
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };
}

export class AccountRelationshipDeletedEvent extends AbstractWorkflowEvent {
  static override eventName &#x3D; &quot;Account relationship deleted&quot;;

  static override eventType &#x3D; AccountEvents.ACCOUNT_RELATIONSHIP_DELETED;

  static override description &#x3D;
    &quot;This event is triggered when an account relationship is deleted&quot;;

  static override schema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;message&quot;, &quot;messageAttributes&quot;],
    properties: {
      message: {
        type: &quot;object&quot;,
        required: [
          &quot;actor&quot;,
          &quot;orgId&quot;,
          &quot;eventId&quot;,
          &quot;payload&quot;,
          &quot;eventType&quot;,
          &quot;timestamp&quot;,
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: &quot;object&quot;,
            properties: {
              previousRelationship: ACCOUNT_RELATIONSHIP_PAYLOAD_SCHEMA,
            },
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };
}

export class AccountActivityCreatedEvent extends AbstractWorkflowEvent {
  static override eventName &#x3D; &quot;Account activity created&quot;;

  static override eventType &#x3D; AccountEvents.ACCOUNT_ACTIVITY_CREATED;

  static override description &#x3D;
    &quot;This event is triggered when an account activity is created&quot;;

  static override schema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;message&quot;, &quot;messageAttributes&quot;],
    properties: {
      message: {
        type: &quot;object&quot;,
        required: [
          &quot;actor&quot;,
          &quot;orgId&quot;,
          &quot;eventId&quot;,
          &quot;payload&quot;,
          &quot;eventType&quot;,
          &quot;timestamp&quot;,
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: &quot;object&quot;,
            properties: {
              activity: ACCOUNT_ACTIVITY_PAYLOAD_SCHEMA,
            },
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };
}

export class AccountActivityUpdatedEvent extends AbstractWorkflowEvent {
  static override eventName &#x3D; &quot;Account activity updated&quot;;

  static override eventType &#x3D; AccountEvents.ACCOUNT_ACTIVITY_UPDATED;

  static override description &#x3D;
    &quot;This event is triggered when an account activity is updated&quot;;

  static override schema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;message&quot;, &quot;messageAttributes&quot;],
    properties: {
      message: {
        type: &quot;object&quot;,
        required: [
          &quot;actor&quot;,
          &quot;orgId&quot;,
          &quot;eventId&quot;,
          &quot;payload&quot;,
          &quot;eventType&quot;,
          &quot;timestamp&quot;,
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: &quot;object&quot;,
            properties: {
              previousActivity: ACCOUNT_ACTIVITY_PAYLOAD_SCHEMA,
              activity: ACCOUNT_ACTIVITY_PAYLOAD_SCHEMA,
            },
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };
}

export class AccountActivityDeletedEvent extends AbstractWorkflowEvent {
  static override eventName &#x3D; &quot;Account activity deleted&quot;;

  static override eventType &#x3D; AccountEvents.ACCOUNT_ACTIVITY_DELETED;

  static override description &#x3D;
    &quot;This event is triggered when an account activity is deleted&quot;;

  static override schema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;message&quot;, &quot;messageAttributes&quot;],
    properties: {
      message: {
        type: &quot;object&quot;,
        required: [
          &quot;actor&quot;,
          &quot;orgId&quot;,
          &quot;eventId&quot;,
          &quot;payload&quot;,
          &quot;eventType&quot;,
          &quot;timestamp&quot;,
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: &quot;object&quot;,
            properties: {
              previousActivity: ACCOUNT_ACTIVITY_PAYLOAD_SCHEMA,
            },
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };
}

// Account Note Events &#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;

export class AccountNoteCreatedEvent extends AbstractWorkflowEvent {
  static override eventName &#x3D; &quot;Account note created&quot;;

  static override eventType &#x3D; AccountEvents.ACCOUNT_NOTE_CREATED;

  static override description &#x3D;
    &quot;This event is triggered when an account note is created&quot;;

  static override schema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;message&quot;, &quot;messageAttributes&quot;],
    properties: {
      message: {
        type: &quot;object&quot;,
        required: [
          &quot;actor&quot;,
          &quot;orgId&quot;,
          &quot;eventId&quot;,
          &quot;payload&quot;,
          &quot;eventType&quot;,
          &quot;timestamp&quot;,
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: &quot;object&quot;,
            properties: {
              note: ACCOUNT_NOTE_PAYLOAD_SCHEMA,
            },
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };
}

export class AccountNoteUpdatedEvent extends AbstractWorkflowEvent {
  static override eventName &#x3D; &quot;Account note updated&quot;;

  static override eventType &#x3D; AccountEvents.ACCOUNT_NOTE_UPDATED;

  static override description &#x3D;
    &quot;This event is triggered when an account note is updated&quot;;

  static override schema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;message&quot;, &quot;messageAttributes&quot;],
    properties: {
      message: {
        type: &quot;object&quot;,
        required: [
          &quot;actor&quot;,
          &quot;orgId&quot;,
          &quot;eventId&quot;,
          &quot;payload&quot;,
          &quot;eventType&quot;,
          &quot;timestamp&quot;,
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: &quot;object&quot;,
            properties: {
              previousNote: ACCOUNT_NOTE_PAYLOAD_SCHEMA,
              note: ACCOUNT_NOTE_PAYLOAD_SCHEMA,
            },
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };
}

export class AccountNoteDeletedEvent extends AbstractWorkflowEvent {
  static override eventName &#x3D; &quot;Account note deleted&quot;;

  static override eventType &#x3D; AccountEvents.ACCOUNT_NOTE_DELETED;

  static override description &#x3D;
    &quot;This event is triggered when an account note is deleted&quot;;

  static override schema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;message&quot;, &quot;messageAttributes&quot;],
    properties: {
      message: {
        type: &quot;object&quot;,
        required: [
          &quot;actor&quot;,
          &quot;orgId&quot;,
          &quot;eventId&quot;,
          &quot;payload&quot;,
          &quot;eventType&quot;,
          &quot;timestamp&quot;,
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: &quot;object&quot;,
            properties: {
              previousNote: ACCOUNT_NOTE_PAYLOAD_SCHEMA,
            },
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };
}

// Account Task Events &#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;

export class AccountTaskCreatedEvent extends AbstractWorkflowEvent {
  static override eventName &#x3D; &quot;Account task created&quot;;

  static override eventType &#x3D; AccountEvents.ACCOUNT_TASK_CREATED;

  static override description &#x3D;
    &quot;This event is triggered when an account task is created&quot;;

  static override schema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;message&quot;, &quot;messageAttributes&quot;],
    properties: {
      message: {
        type: &quot;object&quot;,
        required: [
          &quot;actor&quot;,
          &quot;orgId&quot;,
          &quot;eventId&quot;,
          &quot;payload&quot;,
          &quot;eventType&quot;,
          &quot;timestamp&quot;,
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: &quot;object&quot;,
            properties: {
              task: ACCOUNT_TASK_PAYLOAD_SCHEMA,
            },
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };
}

export class AccountTaskUpdatedEvent extends AbstractWorkflowEvent {
  static override eventName &#x3D; &quot;Account task updated&quot;;

  static override eventType &#x3D; AccountEvents.ACCOUNT_TASK_UPDATED;

  static override description &#x3D;
    &quot;This event is triggered when an account task is updated&quot;;

  static override schema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;message&quot;, &quot;messageAttributes&quot;],
    properties: {
      message: {
        type: &quot;object&quot;,
        required: [
          &quot;actor&quot;,
          &quot;orgId&quot;,
          &quot;eventId&quot;,
          &quot;payload&quot;,
          &quot;eventType&quot;,
          &quot;timestamp&quot;,
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: &quot;object&quot;,
            properties: {
              previousTask: ACCOUNT_TASK_PAYLOAD_SCHEMA,
              task: ACCOUNT_TASK_PAYLOAD_SCHEMA,
            },
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };
}

export class AccountTaskDeletedEvent extends AbstractWorkflowEvent {
  static override eventName &#x3D; &quot;Account task deleted&quot;;

  static override eventType &#x3D; AccountEvents.ACCOUNT_TASK_DELETED;

  static override description &#x3D;
    &quot;This event is triggered when an account task is deleted&quot;;

  static override schema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;message&quot;, &quot;messageAttributes&quot;],
    properties: {
      message: {
        type: &quot;object&quot;,
        required: [
          &quot;actor&quot;,
          &quot;orgId&quot;,
          &quot;eventId&quot;,
          &quot;payload&quot;,
          &quot;eventType&quot;,
          &quot;timestamp&quot;,
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: &quot;object&quot;,
            properties: {
              previousTask: ACCOUNT_TASK_PAYLOAD_SCHEMA,
            },
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };
}

// Customer Contact Events &#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;

export class CustomerContactCreatedEvent extends AbstractWorkflowEvent {
  static override eventName &#x3D; &quot;Customer contact created&quot;;

  static override eventType &#x3D; AccountEvents.CUSTOMER_CONTACT_CREATED;

  static override description &#x3D;
    &quot;This event is triggered when a customer contact is created&quot;;

  static override schema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;message&quot;, &quot;messageAttributes&quot;],
    properties: {
      message: {
        type: &quot;object&quot;,
        required: [
          &quot;actor&quot;,
          &quot;orgId&quot;,
          &quot;eventId&quot;,
          &quot;payload&quot;,
          &quot;eventType&quot;,
          &quot;timestamp&quot;,
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: &quot;object&quot;,
            properties: {
              customerContact: CUSTOMER_CONTACT_PAYLOAD_SCHEMA,
            },
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };
}

export class CustomerContactUpdatedEvent extends AbstractWorkflowEvent {
  static override eventName &#x3D; &quot;Customer contact updated&quot;;

  static override eventType &#x3D; AccountEvents.CUSTOMER_CONTACT_UPDATED;

  static override description &#x3D;
    &quot;This event is triggered when a customer contact is updated&quot;;

  static override schema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;message&quot;, &quot;messageAttributes&quot;],
    properties: {
      message: {
        type: &quot;object&quot;,
        required: [
          &quot;actor&quot;,
          &quot;orgId&quot;,
          &quot;eventId&quot;,
          &quot;payload&quot;,
          &quot;eventType&quot;,
          &quot;timestamp&quot;,
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: &quot;object&quot;,
            properties: {
              previousCustomerContact: CUSTOMER_CONTACT_PAYLOAD_SCHEMA,
              customerContact: CUSTOMER_CONTACT_PAYLOAD_SCHEMA,
            },
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };
}

export class CustomerContactDeletedEvent extends AbstractWorkflowEvent {
  static override eventName &#x3D; &quot;Customer contact deleted&quot;;

  static override eventType &#x3D; AccountEvents.CUSTOMER_CONTACT_DELETED;

  static override description &#x3D;
    &quot;This event is triggered when a customer contact is deleted&quot;;

  static override schema &#x3D; {
    type: &quot;object&quot;,
    required: [&quot;message&quot;, &quot;messageAttributes&quot;],
    properties: {
      message: {
        type: &quot;object&quot;,
        required: [
          &quot;actor&quot;,
          &quot;orgId&quot;,
          &quot;eventId&quot;,
          &quot;payload&quot;,
          &quot;eventType&quot;,
          &quot;timestamp&quot;,
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: &quot;object&quot;,
            properties: {
              previousCustomerContact: CUSTOMER_CONTACT_PAYLOAD_SCHEMA,
            },
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };
}
</code></pre>
    </div>
</div>









                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'class';
            var COMPODOC_CURRENT_PAGE_URL = 'AccountNoteCreatedEvent.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
