<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content class">
                   <div class="content-data">












<ol class="breadcrumb">
  <li class="breadcrumb-item">Classes</li>
  <li class="breadcrumb-item" >BusinessHoursConfigDto</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/common/dto/business-hours.dto.ts</code>
        </p>






            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                <a href="#friday" >friday</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                <a href="#monday" >monday</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                <a href="#saturday" >saturday</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                <a href="#sunday" >sunday</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                <a href="#thursday" >thursday</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                <a href="#tuesday" >tuesday</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                <a href="#wednesday" >wednesday</a>
                            </li>
                        </ul>
                    </td>
                </tr>






        </tbody>
    </table>
</section>


            <section data-compodoc="block-properties">
    
    <h3 id="inputs">
        Properties
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="friday"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                        <span ><b>friday</b></span>
                        <a href="#friday"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="../classes/BusinessDayDto.html" target="_self" >BusinessDayDto</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ValidateNested()<br />@Type(undefined)<br />@ApiProperty({description: &#x27;The business hours for Friday&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="82" class="link-to-prism">src/common/dto/business-hours.dto.ts:82</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="monday"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                        <span ><b>monday</b></span>
                        <a href="#monday"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="../classes/BusinessDayDto.html" target="_self" >BusinessDayDto</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ValidateNested()<br />@Type(undefined)<br />@ApiProperty({description: &#x27;The business hours for Monday&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="62" class="link-to-prism">src/common/dto/business-hours.dto.ts:62</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="saturday"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                        <span ><b>saturday</b></span>
                        <a href="#saturday"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="../classes/BusinessDayDto.html" target="_self" >BusinessDayDto</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ValidateNested()<br />@Type(undefined)<br />@ApiProperty({description: &#x27;The business hours for Saturday&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="87" class="link-to-prism">src/common/dto/business-hours.dto.ts:87</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="sunday"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                        <span ><b>sunday</b></span>
                        <a href="#sunday"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="../classes/BusinessDayDto.html" target="_self" >BusinessDayDto</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ValidateNested()<br />@Type(undefined)<br />@ApiProperty({description: &#x27;The business hours for Sunday&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="92" class="link-to-prism">src/common/dto/business-hours.dto.ts:92</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="thursday"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                        <span ><b>thursday</b></span>
                        <a href="#thursday"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="../classes/BusinessDayDto.html" target="_self" >BusinessDayDto</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ValidateNested()<br />@Type(undefined)<br />@ApiProperty({description: &#x27;The business hours for Thursday&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="77" class="link-to-prism">src/common/dto/business-hours.dto.ts:77</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="tuesday"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                        <span ><b>tuesday</b></span>
                        <a href="#tuesday"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="../classes/BusinessDayDto.html" target="_self" >BusinessDayDto</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ValidateNested()<br />@Type(undefined)<br />@ApiProperty({description: &#x27;The business hours for Tuesday&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="67" class="link-to-prism">src/common/dto/business-hours.dto.ts:67</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="wednesday"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                        <span ><b>wednesday</b></span>
                        <a href="#wednesday"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="../classes/BusinessDayDto.html" target="_self" >BusinessDayDto</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ValidateNested()<br />@Type(undefined)<br />@ApiProperty({description: &#x27;The business hours for Wednesday&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="72" class="link-to-prism">src/common/dto/business-hours.dto.ts:72</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
</section>







    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { ApiProperty, ApiPropertyOptional } from &quot;@nestjs/swagger&quot;;
import { TeamUserRoutingStrategy } from &quot;@repo/thena-platform-entities&quot;;
import { Type } from &quot;class-transformer&quot;;
import {
  IsArray,
  IsBoolean,
  IsDefined,
  IsEnum,
  IsOptional,
  IsString,
  IsTimeZone,
  Validate,
  ValidateNested,
} from &quot;class-validator&quot;;
import { IsDateDDMMOrDDMMYYYY } from &quot;../validators/date.validators&quot;;
import {
  IsTimeFormatConstraint,
  IsValidTimeSlotConstraint,
} from &quot;../validators/time.validators&quot;;

// Business Slot Class
export class BusinessSlotDto {
  @IsString()
  @Validate(IsTimeFormatConstraint)
  @ApiProperty({
    description: &quot;Start time in 24-hour format (HH:mm)&quot;,
    example: &quot;10:00&quot;,
  })
  start: string;

  @IsString()
  @Validate(IsTimeFormatConstraint)
  @ApiProperty({
    description: &quot;End time in 24-hour format (HH:mm)&quot;,
    example: &quot;18:00&quot;,
  })
  end: string;
}

// Business Day Class
export class BusinessDayDto {
  @IsBoolean()
  @IsDefined({ message: &quot;The business day must be active or inactive!&quot; })
  @ApiProperty({ description: &quot;Whether the business day is active&quot; })
  isActive: boolean;

  @Validate(IsValidTimeSlotConstraint)
  @Type(() &#x3D;&gt; BusinessSlotDto)
  @ValidateNested({ each: true })
  @ApiProperty({
    description: &quot;The time slots for the business day&quot;,
    type: [BusinessSlotDto],
  })
  slots: BusinessSlotDto[];
}

// Business Hours Config Class
export class BusinessHoursConfigDto {
  @ValidateNested()
  @Type(() &#x3D;&gt; BusinessDayDto)
  @ApiProperty({ description: &quot;The business hours for Monday&quot; })
  monday: BusinessDayDto;

  @ValidateNested()
  @Type(() &#x3D;&gt; BusinessDayDto)
  @ApiProperty({ description: &quot;The business hours for Tuesday&quot; })
  tuesday: BusinessDayDto;

  @ValidateNested()
  @Type(() &#x3D;&gt; BusinessDayDto)
  @ApiProperty({ description: &quot;The business hours for Wednesday&quot; })
  wednesday: BusinessDayDto;

  @ValidateNested()
  @Type(() &#x3D;&gt; BusinessDayDto)
  @ApiProperty({ description: &quot;The business hours for Thursday&quot; })
  thursday: BusinessDayDto;

  @ValidateNested()
  @Type(() &#x3D;&gt; BusinessDayDto)
  @ApiProperty({ description: &quot;The business hours for Friday&quot; })
  friday: BusinessDayDto;

  @ValidateNested()
  @Type(() &#x3D;&gt; BusinessDayDto)
  @ApiProperty({ description: &quot;The business hours for Saturday&quot; })
  saturday: BusinessDayDto;

  @ValidateNested()
  @Type(() &#x3D;&gt; BusinessDayDto)
  @ApiProperty({ description: &quot;The business hours for Sunday&quot; })
  sunday: BusinessDayDto;
}

// Update Team Configurations Class
export class UpdateTimezoneWorkingHoursDto {
  @IsTimeZone()
  @IsOptional()
  @ApiPropertyOptional({ description: &quot;The timezone of the team&quot; })
  timezone?: string;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({
    description: &quot;Whether the routing rules respect the timezone for the teams&quot;,
  })
  routingRespectsTimezone?: boolean;

  @IsOptional()
  @IsArray()
  @IsDateDDMMOrDDMMYYYY({ each: true })
  @ApiPropertyOptional({
    description: &quot;The dates of the team&#x27;s holidays&quot;,
    example: [&quot;25-12&quot;, &quot;25-12-2024&quot;],
    isArray: true,
  })
  holidays?: Array&lt;string&gt;;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({
    description:
      &quot;Whether the routing rules respect the user timezone for the teams&quot;,
  })
  routingRespectsUserTimezone?: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({
    description:
      &quot;Whether the routing rules respect the user availability for the teams&quot;,
  })
  routingRespectsUserAvailability?: boolean;

  @IsOptional()
  @IsEnum(TeamUserRoutingStrategy)
  @ApiPropertyOptional({
    description: &quot;The user routing strategy for the team&quot;,
    enum: TeamUserRoutingStrategy,
  })
  userRoutingStrategy?: TeamUserRoutingStrategy;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({
    description: &quot;Whether the team uses common daily config&quot;,
  })
  commonDailyConfig?: boolean;

  @IsOptional()
  @ValidateNested()
  @Type(() &#x3D;&gt; BusinessSlotDto)
  @ApiPropertyOptional({
    description: &quot;The common slots for the team&quot;,
    example: [{ start: &quot;09:00&quot;, end: &quot;17:00&quot; }],
  })
  commonSlots?: BusinessSlotDto[];

  @IsOptional()
  @ValidateNested()
  @Type(() &#x3D;&gt; BusinessHoursConfigDto)
  @ApiPropertyOptional({
    description: &quot;The business hours of the team&quot;,
    example: {
      monday: {
        isActive: true,
        slots: [{ start: &quot;09:00&quot;, end: &quot;17:00&quot; }],
      },
    },
  })
  dailyConfig?: BusinessHoursConfigDto;
}
</code></pre>
    </div>
</div>









                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'class';
            var COMPODOC_CURRENT_PAGE_URL = 'BusinessHoursConfigDto.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
