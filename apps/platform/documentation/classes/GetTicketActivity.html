<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content class">
                   <div class="content-data">












<ol class="breadcrumb">
  <li class="breadcrumb-item">Classes</li>
  <li class="breadcrumb-item" >GetTicketActivity</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/common/workflows/activities/tickets.activities.ts</code>
        </p>


            <p class="comment">
                <h3>Description</h3>
            </p>
            <p class="comment">
                <p>Get ticket activity</p>

            </p>

            <p class="comment">
                <h3>Extends</h3>
            </p>
            <p class="comment">
                        <code>AbstractWorkflowActivity</code>
            </p>



            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Static</span>
                                    <span class="modifier"></span>
                                <a href="#activityName" >activityName</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#connectionDetails" >connectionDetails</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                    <span class="modifier"></span>
                                <a href="#description" >description</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                    <span class="modifier"></span>
                                <a href="#identifier" >identifier</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                    <span class="modifier"></span>
                                <a href="#requestSchema" >requestSchema</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                    <span class="modifier"></span>
                                <a href="#responseSchema" >responseSchema</a>
                            </li>
                        </ul>
                    </td>
                </tr>






        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(configService: <a href="../injectables/ConfigService.html" target="_self">ConfigService</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="198" class="link-to-prism">src/common/workflows/activities/tickets.activities.ts:198</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>configService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/ConfigService.html" target="_self" >ConfigService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-properties">
    
    <h3 id="inputs">
        Properties
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="activityName"></a>
                    <span class="name">
                            <span class="modifier">Static</span>
                            <span class="modifier"></span>
                        <span ><b>activityName</b></span>
                        <a href="#activityName"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&quot;Get ticket&quot;</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="203" class="link-to-prism">src/common/workflows/activities/tickets.activities.ts:203</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="connectionDetails"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>connectionDetails</b></span>
                        <a href="#connectionDetails"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: tickets.GRPC_TICKETS_V1_PACKAGE_NAME,
      serviceName: tickets.TICKETS_SERVICE_NAME,
      methodName: &quot;GetTicket&quot;,
      protoPath: &quot;dist/proto/tickets/tickets.proto&quot;,
    },
  }</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="219" class="link-to-prism">src/common/workflows/activities/tickets.activities.ts:219</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="description"></a>
                    <span class="name">
                            <span class="modifier">Static</span>
                            <span class="modifier"></span>
                        <span ><b>description</b></span>
                        <a href="#description"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&quot;This activity gets a ticket by ID&quot;</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="205" class="link-to-prism">src/common/workflows/activities/tickets.activities.ts:205</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="identifier"></a>
                    <span class="name">
                            <span class="modifier">Static</span>
                            <span class="modifier"></span>
                        <span ><b>identifier</b></span>
                        <a href="#identifier"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&quot;tickets:get-ticket&quot;</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="207" class="link-to-prism">src/common/workflows/activities/tickets.activities.ts:207</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="requestSchema"></a>
                    <span class="name">
                            <span class="modifier">Static</span>
                            <span class="modifier"></span>
                        <span ><b>requestSchema</b></span>
                        <a href="#requestSchema"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
    type: &quot;object&quot;,
    properties: {
      id: { type: &quot;string&quot; },
    },
    required: [&quot;id&quot;],
  }</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="209" class="link-to-prism">src/common/workflows/activities/tickets.activities.ts:209</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="responseSchema"></a>
                    <span class="name">
                            <span class="modifier">Static</span>
                            <span class="modifier"></span>
                        <span ><b>responseSchema</b></span>
                        <a href="#responseSchema"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>COMMON_TICKET_RESPONSE_SCHEMA</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="217" class="link-to-prism">src/common/workflows/activities/tickets.activities.ts:217</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
</section>







    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { tickets } from &quot;@repo/shared-proto&quot;;
import { AbstractWorkflowActivity } from &quot;@repo/workflow-engine&quot;;
import { ConfigKeys, ConfigService } from &quot;../../../config/config.service&quot;;
import { COMMON_TICKET_RESPONSE_SCHEMA } from &quot;../constants&quot;;

/**
 * Assign ticket activity
 */
export class AssignTicketActivity extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Assign ticket&quot;;

  static override description &#x3D; &quot;This activity assigns a ticket to an agent&quot;;

  static override identifier &#x3D; &quot;tickets:assign-ticket&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    properties: {
      ticketId: { type: &quot;string&quot; },
      agentId: { type: &quot;string&quot; },
    },
    required: [&quot;ticketId&quot;, &quot;agentId&quot;],
  };

  static override responseSchema &#x3D; {
    type: &quot;object&quot;,
    properties: {
      success: { type: &quot;boolean&quot; },
      ticket: COMMON_TICKET_RESPONSE_SCHEMA,
    },
    required: [&quot;success&quot;, &quot;ticket&quot;],
  };

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: tickets.GRPC_TICKETS_V1_PACKAGE_NAME,
      serviceName: tickets.TICKETS_SERVICE_NAME,
      methodName: &quot;AssignTicket&quot;,
      protoPath: &quot;dist/proto/tickets/tickets.proto&quot;,
    },
  };

  static override isCompensable &#x3D; true;

  static override compensationActivity &#x3D; {
    identifier: &quot;tickets:compensate-assign-ticket&quot;,
  };
}

export class CompensateAssignTicketActivity extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Compensate assign ticket&quot;;

  static override description &#x3D;
    &quot;This activity compensates the assign ticket activity&quot;;

  static override identifier &#x3D; &quot;tickets:compensate-assign-ticket&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    properties: {
      executionId: { type: &quot;string&quot; },
    },
    required: [&quot;executionId&quot;],
  };

  static override responseSchema &#x3D; {
    type: &quot;object&quot;,
    properties: {
      success: { type: &quot;boolean&quot; },
      ticket: COMMON_TICKET_RESPONSE_SCHEMA,
    },
    required: [&quot;success&quot;, &quot;ticket&quot;],
  };

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: tickets.GRPC_TICKETS_V1_PACKAGE_NAME,
      serviceName: tickets.TICKETS_SERVICE_NAME,
      methodName: &quot;CompensateAssignTicket&quot;,
      protoPath: &quot;dist/proto/tickets/tickets.proto&quot;,
    },
  };

  static override isCompensable &#x3D; false;
}

/**
 * Create ticket activity
 */
export class CreateTicketActivity extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Create ticket&quot;;

  static override description &#x3D; &quot;This activity creates a ticket&quot;;

  static override identifier &#x3D; &quot;tickets:create-ticket&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    properties: {
      title: { type: &quot;string&quot; },
      requestorEmail: { type: &quot;string&quot; },
      teamId: { type: &quot;string&quot; },
      accountId: { type: &quot;string&quot; },
      assignedAgentId: { type: &quot;string&quot; },
      description: { type: &quot;string&quot; },
      dueDate: { type: &quot;string&quot; },
      statusId: { type: &quot;string&quot; },
      priorityId: { type: &quot;string&quot; },
      typeId: { type: &quot;string&quot; },
      submitterEmail: { type: &quot;string&quot; },
      isPrivate: { type: &quot;boolean&quot; },
      attachmentUrls: { type: &quot;array&quot;, items: { type: &quot;string&quot; } },
    },
    required: [&quot;title&quot;, &quot;requestorEmail&quot;, &quot;teamId&quot;],
  };

  static override responseSchema &#x3D; COMMON_TICKET_RESPONSE_SCHEMA;

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: tickets.GRPC_TICKETS_V1_PACKAGE_NAME,
      serviceName: tickets.TICKETS_SERVICE_NAME,
      methodName: &quot;CreateTicket&quot;,
      protoPath: &quot;dist/proto/tickets/tickets.proto&quot;,
    },
  };
}

/**
 * Update ticket activity
 */
export class UpdateTicketActivity extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Update ticket&quot;;

  static override description &#x3D; &quot;This activity updates a ticket&quot;;

  static override identifier &#x3D; &quot;tickets:update-ticket&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    properties: {
      id: { type: &quot;string&quot; },
      title: { type: &quot;string&quot; },
      teamId: { type: &quot;string&quot; },
      accountId: { type: &quot;string&quot; },
      assignedAgentId: { type: &quot;string&quot; },
      description: { type: &quot;string&quot; },
      dueDate: { type: &quot;string&quot; },
      statusId: { type: &quot;string&quot; },
      priorityId: { type: &quot;string&quot; },
      typeId: { type: &quot;string&quot; },
      submitterEmail: { type: &quot;string&quot; },
      isPrivate: { type: &quot;boolean&quot; },
      attachmentUrls: { type: &quot;array&quot;, items: { type: &quot;string&quot; } },
    },
    required: [&quot;id&quot;],
  };

  static override responseSchema &#x3D; COMMON_TICKET_RESPONSE_SCHEMA;

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: tickets.GRPC_TICKETS_V1_PACKAGE_NAME,
      serviceName: tickets.TICKETS_SERVICE_NAME,
      methodName: &quot;UpdateTicket&quot;,
      protoPath: &quot;dist/proto/tickets/tickets.proto&quot;,
    },
  };
}

/**
 * Get ticket activity
 */
export class GetTicketActivity extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Get ticket&quot;;

  static override description &#x3D; &quot;This activity gets a ticket by ID&quot;;

  static override identifier &#x3D; &quot;tickets:get-ticket&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    properties: {
      id: { type: &quot;string&quot; },
    },
    required: [&quot;id&quot;],
  };

  static override responseSchema &#x3D; COMMON_TICKET_RESPONSE_SCHEMA;

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: tickets.GRPC_TICKETS_V1_PACKAGE_NAME,
      serviceName: tickets.TICKETS_SERVICE_NAME,
      methodName: &quot;GetTicket&quot;,
      protoPath: &quot;dist/proto/tickets/tickets.proto&quot;,
    },
  };
}

/**
 * Get tickets with cursor activity
 */
export class GetTicketsWithCursorActivity extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Get tickets with cursor&quot;;

  static override description &#x3D;
    &quot;This activity gets tickets with cursor pagination&quot;;

  static override identifier &#x3D; &quot;tickets:get-tickets-with-cursor&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    properties: {
      limit: { type: &quot;number&quot; },
      teamId: { type: &quot;string&quot; },
      afterCursor: { type: &quot;string&quot; },
    },
    required: [&quot;limit&quot;],
  };

  static override responseSchema &#x3D; {
    type: &quot;object&quot;,
    properties: {
      cursor: { type: &quot;string&quot; },
      tickets: {
        type: &quot;array&quot;,
        items: COMMON_TICKET_RESPONSE_SCHEMA,
      },
    },
    required: [&quot;cursor&quot;, &quot;tickets&quot;],
  };

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: tickets.GRPC_TICKETS_V1_PACKAGE_NAME,
      serviceName: tickets.TICKETS_SERVICE_NAME,
      methodName: &quot;GetTicketsWithCursor&quot;,
      protoPath: &quot;dist/proto/tickets/tickets.proto&quot;,
    },
  };
}

/**
 * Archive ticket activity
 */
export class ArchiveTicketActivity extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Archive ticket&quot;;

  static override description &#x3D; &quot;This activity archives a ticket&quot;;

  static override identifier &#x3D; &quot;tickets:archive-ticket&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    properties: {
      id: { type: &quot;string&quot; },
    },
    required: [&quot;id&quot;],
  };

  static override responseSchema &#x3D; COMMON_TICKET_RESPONSE_SCHEMA;

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: tickets.GRPC_TICKETS_V1_PACKAGE_NAME,
      serviceName: tickets.TICKETS_SERVICE_NAME,
      methodName: &quot;ArchiveTicket&quot;,
      protoPath: &quot;dist/proto/tickets/tickets.proto&quot;,
    },
  };
}

/**
 * Escalate ticket activity
 */
export class EscalateTicketActivity extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName &#x3D; &quot;Escalate ticket&quot;;

  static override description &#x3D; &quot;This activity escalates a ticket&quot;;

  static override identifier &#x3D; &quot;tickets:escalate-ticket&quot;;

  static override requestSchema &#x3D; {
    type: &quot;object&quot;,
    properties: {
      id: { type: &quot;string&quot; },
      reason: { type: &quot;string&quot; },
      details: { type: &quot;string&quot; },
      impact: { type: &quot;string&quot; },
    },
    required: [&quot;id&quot;, &quot;reason&quot;, &quot;details&quot;, &quot;impact&quot;],
  };

  static override responseSchema &#x3D; COMMON_TICKET_RESPONSE_SCHEMA;

  override connectionDetails &#x3D; {
    transport: &quot;GRPC&quot; as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: tickets.GRPC_TICKETS_V1_PACKAGE_NAME,
      serviceName: tickets.TICKETS_SERVICE_NAME,
      methodName: &quot;EscalateTicket&quot;,
      protoPath: &quot;dist/proto/tickets/tickets.proto&quot;,
    },
  };
}
</code></pre>
    </div>
</div>









                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'class';
            var COMPODOC_CURRENT_PAGE_URL = 'GetTicketActivity.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
