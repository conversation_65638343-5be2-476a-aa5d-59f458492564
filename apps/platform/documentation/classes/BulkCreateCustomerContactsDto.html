<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content class">
                   <div class="content-data">












<ol class="breadcrumb">
  <li class="breadcrumb-item">Classes</li>
  <li class="breadcrumb-item" >BulkCreateCustomerContactsDto</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/accounts/dtos/customer-contact.dto.ts</code>
        </p>






            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Optional</span>
                                <a href="#accountIds" >accountIds</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                <a href="#contacts" >contacts</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Optional</span>
                                <a href="#contactType" >contactType</a>
                            </li>
                        </ul>
                    </td>
                </tr>






        </tbody>
    </table>
</section>


            <section data-compodoc="block-properties">
    
    <h3 id="inputs">
        Properties
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="accountIds"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Optional</span>
                        <span ><b>accountIds</b></span>
                        <a href="#accountIds"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>string[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiPropertyOptional({description: &#x27;Account ID of the customer contacts&#x27;, example: &#x27;A123&#x27;})<br />@IsOptional()<br />@IsArray()<br />@IsString({each: true})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="169" class="link-to-prism">src/accounts/dtos/customer-contact.dto.ts:169</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="contacts"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                        <span ><b>contacts</b></span>
                        <a href="#contacts"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="../interfaces/Customer.html" target="_self" >BulkCreateCustomerContactDetailsDto[]</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiProperty({description: &#x27;Details of customer contacts to create&#x27;})<br />@IsArray()<br />@ValidateNested()<br />@Type(undefined)<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="160" class="link-to-prism">src/accounts/dtos/customer-contact.dto.ts:160</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="contactType"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Optional</span>
                        <span ><b>contactType</b></span>
                        <a href="#contactType"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @ApiPropertyOptional({description: &#x27;Contact type of the customer contacts&#x27;, example: &#x27;CT123&#x27;})<br />@IsOptional()<br />@IsString()<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="177" class="link-to-prism">src/accounts/dtos/customer-contact.dto.ts:177</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
</section>







    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { ApiProperty, ApiPropertyOptional } from &quot;@nestjs/swagger&quot;;
import { Transform, Type } from &quot;class-transformer&quot;;
import {
  IsArray,
  IsEmail,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Max,
  Min,
  ValidateNested,
} from &quot;class-validator&quot;;

export class CreateCustomerContactDto {
  @ApiProperty({
    description: &quot;First name of the customer contact&quot;,
    example: &quot;John&quot;,
  })
  @IsNotEmpty()
  @IsString()
  firstName: string;

  @ApiPropertyOptional({
    description: &quot;Last name of the customer contact&quot;,
    example: &quot;Doe&quot;,
  })
  @IsOptional()
  @IsString()
  lastName?: string;

  @ApiProperty({
    description: &quot;Email of the customer contact&quot;,
    example: &quot;<EMAIL>&quot;,
  })
  @IsNotEmpty()
  @IsEmail()
  @Transform(({ value }) &#x3D;&gt; value.toLowerCase())
  email: string;

  @ApiPropertyOptional({
    description: &quot;Phone number of the customer contact with country code&quot;,
    example: &quot;+**********&quot;,
  })
  @IsOptional()
  phoneNumber?: string;

  @ApiPropertyOptional({
    description:
      &quot;Contact type (UID of the contact type attribute value) of the customer contact to create. (Uses default contact type if not provided)&quot;,
    example: &quot;CT123&quot;,
  })
  @IsOptional()
  @IsString()
  contactType?: string;

  @ApiPropertyOptional({
    description: &quot;Account IDs of the customer contact&quot;,
    example: [&quot;A123&quot;, &quot;A456&quot;],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  accountIds?: string[];
}

export class UpdateCustomerContactDto {
  @ApiPropertyOptional({
    description: &quot;First name of the customer contact&quot;,
    example: &quot;John&quot;,
  })
  @IsOptional()
  @IsString()
  firstName?: string;

  @ApiPropertyOptional({
    description: &quot;Last name of the customer contact&quot;,
    example: &quot;Doe&quot;,
  })
  @IsOptional()
  @IsString()
  lastName?: string;

  @ApiPropertyOptional({
    description: &quot;Email of the customer contact&quot;,
    example: &quot;<EMAIL>&quot;,
  })
  @IsOptional()
  @IsEmail()
  @Transform(({ value }) &#x3D;&gt; value.toLowerCase())
  email?: string;

  @ApiPropertyOptional({
    description: &quot;Phone number of the customer contact with country code&quot;,
    example: &quot;+**********&quot;,
  })
  @IsOptional()
  phoneNumber?: string;

  @ApiPropertyOptional({
    description: &quot;Contact type of the customer contact&quot;,
    example: &quot;CT123&quot;,
  })
  @IsOptional()
  @IsString()
  contactType?: string;

  @ApiPropertyOptional({
    description:
      &quot;Account IDs of the customer contact. This will replace the existing account IDs of the customer contact&quot;,
    example: [&quot;A123&quot;, &quot;A456&quot;],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  accountIds?: string[];
}

class BulkCreateCustomerContactDetailsDto {
  @ApiProperty({
    description: &quot;First name of the customer contact&quot;,
    example: &quot;John&quot;,
  })
  @IsNotEmpty()
  @IsString()
  firstName: string;

  @ApiPropertyOptional({
    description: &quot;Last name of the customer contact&quot;,
    example: &quot;Doe&quot;,
  })
  @IsOptional()
  @IsString()
  lastName?: string;

  @ApiProperty({
    description: &quot;Email of the customer contact&quot;,
    example: &quot;<EMAIL>&quot;,
  })
  @IsNotEmpty()
  @IsEmail()
  @Transform(({ value }) &#x3D;&gt; value.toLowerCase())
  email: string;

  @ApiPropertyOptional({
    description: &quot;Phone number of the customer contact with country code&quot;,
    example: &quot;+**********&quot;,
  })
  @IsOptional()
  phoneNumber?: string;
}

export class BulkCreateCustomerContactsDto {
  @ApiProperty({
    description: &quot;Details of customer contacts to create&quot;,
  })
  @IsArray()
  @ValidateNested()
  @Type(() &#x3D;&gt; BulkCreateCustomerContactDetailsDto)
  contacts: BulkCreateCustomerContactDetailsDto[];

  @ApiPropertyOptional({
    description: &quot;Account ID of the customer contacts&quot;,
    example: &quot;A123&quot;,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  accountIds?: string[];

  @ApiPropertyOptional({
    description: &quot;Contact type of the customer contacts&quot;,
    example: &quot;CT123&quot;,
  })
  @IsOptional()
  @IsString()
  contactType?: string;
}

export class FindAllCustomerContactsDto {
  @ApiPropertyOptional({
    description: &quot;Account ID of the customer contact&quot;,
    example: &quot;A123&quot;,
  })
  @IsOptional()
  @IsString()
  accountId?: string;

  @ApiPropertyOptional({
    description:
      &quot;Contact type of the customer contact to find. (Fetches all contacts satisfying other conditions if not provided)&quot;,
    example: &quot;CT123&quot;,
  })
  @IsOptional()
  @IsString()
  contactType?: string;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) &#x3D;&gt; parseInt(value))
  @Min(0)
  @ApiPropertyOptional({ description: &quot;The page number&quot; })
  page?: number;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) &#x3D;&gt; parseInt(value))
  @Min(1)
  @Max(100)
  @ApiPropertyOptional({ description: &quot;The limit number of contacts to fetch&quot; })
  limit?: number;
}
</code></pre>
    </div>
</div>









                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'class';
            var COMPODOC_CURRENT_PAGE_URL = 'BulkCreateCustomerContactsDto.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
