<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content class">
                   <div class="content-data">












<ol class="breadcrumb">
  <li class="breadcrumb-item">Classes</li>
  <li class="breadcrumb-item" >IsTimeFormatConstraint</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/common/validators/time.validators.ts</code>
        </p>




            <p class="comment">
                <h3>Implements</h3>
            </p>
            <p class="comment">
                        <code>ValidatorConstraintInterface</code>
            </p>


            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#defaultMessage" >defaultMessage</a>
                            </li>
                            <li>
                                <a href="#validate" >validate</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>



            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="defaultMessage"></a>
                    <span class="name">
                        <span ><b>defaultMessage</b></span>
                        <a href="#defaultMessage"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>defaultMessage()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="53"
                                    class="link-to-prism">src/common/validators/time.validators.ts:53</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validate"></a>
                    <span class="name">
                        <span ><b>validate</b></span>
                        <a href="#validate"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>validate(value: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="48"
                                    class="link-to-prism">src/common/validators/time.validators.ts:48</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>value</td>
                                            <td>
                                                            <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise | boolean</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>





    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { BusinessSlot } from &quot;@repo/thena-platform-entities&quot;;
import {
  registerDecorator,
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from &quot;class-validator&quot;;

export const IS_TIMEZONE_OR_EMPTY &#x3D; &quot;isTimezoneOrEmpty&quot;;

@ValidatorConstraint({ name: &quot;hasAllBusinessDays&quot;, async: false })
export class HasAllBusinessDaysConstraint
  implements ValidatorConstraintInterface
{
  private readonly requiredDays &#x3D; [
    &quot;monday&quot;,
    &quot;tuesday&quot;,
    &quot;wednesday&quot;,
    &quot;thursday&quot;,
    &quot;friday&quot;,
    &quot;saturday&quot;,
    &quot;sunday&quot;,
  ];

  validate(value: any): boolean {
    if (!value) return true; // Optional check
    if (typeof value !&#x3D;&#x3D; &quot;object&quot;) return false;

    return this.requiredDays.every((day) &#x3D;&gt;
      Object.hasOwnProperty.call(value, day),
    );
  }

  defaultMessage(args: ValidationArguments): string {
    const missingDays &#x3D; this.requiredDays.filter(
      (day) &#x3D;&gt; !Object.hasOwnProperty.call(args.value || {}, day),
    );

    return &#x60;Business hours must include all days of the week. Missing: ${missingDays.join(
      &quot;, &quot;,
    )}&#x60;;
  }
}

@ValidatorConstraint({ name: &quot;isTimeFormat&quot;, async: false })
export class IsTimeFormatConstraint implements ValidatorConstraintInterface {
  validate(value: any): Promise&lt;boolean&gt; | boolean {
    if (typeof value !&#x3D;&#x3D; &quot;string&quot;) return false;
    return /^([01]\d|2[0-3]):([0-5]\d)$/.test(value);
  }

  defaultMessage(): string {
    return &quot;Time must be in the format (HH:mm)&quot;;
  }
}

@ValidatorConstraint({ name: &quot;isValidTimeSlot&quot;, async: false })
export class IsValidTimeSlotConstraint implements ValidatorConstraintInterface {
  validate(slots: BusinessSlot[], args: ValidationArguments): boolean {
    const businessDay &#x3D; args.object as any;

    // If the day is inactive, allow undefined/null/empty slots
    if (!businessDay.isActive) {
      return true;
    }

    // If the day is active, there must be at least one slot
    if (businessDay.isActive) {
      // If the slots are not an array, return false
      if (!Array.isArray(slots)) return false;

      // If the slots array is empty, return false
      if (slots.length &#x3D;&#x3D;&#x3D; 0) return false;
    }

    if (!Array.isArray(slots)) return false;
    if (slots.length &#x3D;&#x3D;&#x3D; 0) return true; // Empty array is valid for inactive days

    return slots.every((slot) &#x3D;&gt; {
      if (!slot || !slot.start || !slot.end) return false;
      const start &#x3D; this.parseTime(slot.start);
      const end &#x3D; this.parseTime(slot.end);
      if (start &gt;&#x3D; end) return false;

      // Check for overlapping slots
      return slots.every((otherSlot) &#x3D;&gt; {
        if (slot &#x3D;&#x3D;&#x3D; otherSlot) return true;
        const otherStart &#x3D; this.parseTime(otherSlot.start);
        const otherEnd &#x3D; this.parseTime(otherSlot.end);
        return start &gt;&#x3D; otherEnd || end &lt;&#x3D; otherStart;
      });
    });
  }

  private parseTime(time: string): number {
    if (!/^([01]\d|2[0-3]):([0-5]\d)$/.test(time)) return -1;
    const [hours, minutes] &#x3D; time.split(&quot;:&quot;).map(Number);
    return hours * 60 + minutes;
  }

  defaultMessage(): string {
    return &quot;Time slots must be valid and non-overlapping, with start time before end time&quot;;
  }
}
@ValidatorConstraint({ name: &quot;validateActiveDay&quot;, async: false })
export class ValidateActiveDayConstraint
  implements ValidatorConstraintInterface
{
  validate(slot: any, args: ValidationArguments): boolean {
    const businessDay &#x3D; args.object as any;

    if (businessDay.isActive) {
      return !!slot; // Must have a slot if active
    }

    return !slot; // Must not have a slot if inactive
  }

  defaultMessage(args: ValidationArguments): string {
    const businessDay &#x3D; args.object as any;
    if (businessDay.isActive &amp;&amp; !args.value) {
      return &quot;Active business days must have a time slot&quot;;
    }
    if (!businessDay.isActive &amp;&amp; args.value) {
      return &quot;Inactive business days should not have a time slot&quot;;
    }
    return &quot;Invalid business day configuration&quot;;
  }
}

export function IsAfterDate(
  property: string,
  validationOptions?: ValidationOptions,
) {
  return function (object: Record&lt;string, any&gt;, propertyName: string) {
    registerDecorator({
      name: &quot;isAfterDate&quot;,
      target: object.constructor,
      propertyName: propertyName,
      constraints: [property],
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          const [relatedPropertyName] &#x3D; args.constraints;
          const relatedValue &#x3D; (args.object as any)[relatedPropertyName];

          if (!value || !relatedValue) return false;

          const compareDate &#x3D; new Date(value);
          const relatedDate &#x3D; new Date(relatedValue);

          if (isNaN(compareDate.getTime()) || isNaN(relatedDate.getTime())) {
            return false;
          }

          return compareDate &gt;&#x3D; relatedDate;
        },
        defaultMessage(args: ValidationArguments) {
          const [relatedPropertyName] &#x3D; args.constraints;
          return &#x60;${propertyName} must not be before ${relatedPropertyName}&#x60;;
        },
      },
    });
  };
}

export function IsTimezoneOrEmpty(validationOptions?: ValidationOptions) {
  return function (object: Record&lt;string, any&gt;, propertyName: string) {
    registerDecorator({
      name: IS_TIMEZONE_OR_EMPTY,
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any) {
          // Allow empty string
          if (value &#x3D;&#x3D;&#x3D; &quot;UNSET&quot;) return true;

          try {
            // Test if it&#x27;s a valid IANA timezone
            Intl.DateTimeFormat(undefined, { timeZone: value });
            return true;
          } catch (_error) {
            return false;
          }
        },
        defaultMessage() {
          return &quot;Value must be either a valid IANA timezone or an empty string&quot;;
        },
      },
    });
  };
}
</code></pre>
    </div>
</div>









                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'class';
            var COMPODOC_CURRENT_PAGE_URL = 'IsTimeFormatConstraint.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
