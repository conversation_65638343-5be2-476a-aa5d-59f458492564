<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content class">
                   <div class="content-data">












<ol class="breadcrumb">
  <li class="breadcrumb-item">Classes</li>
  <li class="breadcrumb-item" >EscalateTicketBody</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/tickets/dto/ticket.dto.ts</code>
        </p>


            <p class="comment">
                <h3>Description</h3>
            </p>
            <p class="comment">
                <p>Fields for escalating a ticket</p>

            </p>




            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                <a href="#details" >details</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                <a href="#impact" >impact</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                <a href="#reason" >reason</a>
                            </li>
                        </ul>
                    </td>
                </tr>






        </tbody>
    </table>
</section>


            <section data-compodoc="block-properties">
    
    <h3 id="inputs">
        Properties
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="details"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                        <span ><b>details</b></span>
                        <a href="#details"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @IsString()<br />@IsNotEmpty()<br />@ApiProperty({description: &#x27;The details of the escalation&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="151" class="link-to-prism">src/tickets/dto/ticket.dto.ts:151</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="impact"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                        <span ><b>impact</b></span>
                        <a href="#impact"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @IsString()<br />@IsNotEmpty()<br />@ApiProperty({description: &#x27;The impact of the escalation&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="156" class="link-to-prism">src/tickets/dto/ticket.dto.ts:156</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="reason"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                        <span ><b>reason</b></span>
                        <a href="#reason"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <b>Decorators : </b>
                        <br />
                        <code>
                            @IsString()<br />@IsNotEmpty()<br />@ApiProperty({description: &#x27;The reason for the escalation&#x27;})<br />
                        </code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="146" class="link-to-prism">src/tickets/dto/ticket.dto.ts:146</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
</section>







    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { ApiProperty, ApiPropertyOptional } from &quot;@nestjs/swagger&quot;;
import { Type } from &quot;class-transformer&quot;;
import {
  IsArray,
  IsBoolean,
  IsDate,
  IsEmail,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
  ValidateIf,
  ValidateNested,
} from &quot;class-validator&quot;;
import { ExternalCustomFieldValuesDto } from &quot;../../custom-field/dto&quot;;

/**
 * Common fields for creating and updating a ticket
 */
class CommonTicketFields {
  /**
   * The ID of the assigned agent
   */
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: &quot;The ID of the assigned agent&quot; })
  assignedAgentId?: string;

  /**
   * The description of the ticket
   */
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: &quot;The description of the ticket&quot; })
  description?: string;

  /**
   * The due date of the ticket
   */
  @IsDate()
  @IsOptional()
  @ApiPropertyOptional({ description: &quot;The due date of the ticket&quot; })
  dueDate?: Date;

  /**
   * The email of the submitter
   */
  @IsEmail()
  @IsOptional()
  @ApiPropertyOptional({ description: &quot;The email of the submitter&quot; })
  submitterEmail?: string;

  /**
   * The ID of the status
   */
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description:
      &quot;The ID of the status, status id if provided is used over status name&quot;,
  })
  statusId?: string;

  /**
   * The name of the status to match against, we use levenshtein distance to match against the status name
   * in the database
   */
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description: &quot;The name of the status to match against&quot;,
  })
  statusName?: string;

  /**
   * The ID of the priority
   */
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description:
      &quot;The ID of the priority, priority id if provided is used over priority name&quot;,
  })
  priorityId?: string;

  /**
   * The name of the priority to match against, we use levenshtein distance to match against the priority name
   * in the database
   */
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description: &quot;The name of the priority to match against&quot;,
  })
  priorityName?: string;

  /**
   * The metadata of the ticket
   */
  @IsOptional()
  @IsObject()
  @ApiPropertyOptional({
    description: &quot;The metadata of the ticket&quot;,
  })
  metadata?: Record&lt;string, unknown&gt;;

  /**
   * The ID of the type
   */
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: &quot;The ID of the type&quot; })
  typeId?: string;

  /**
   * Whether the ticket is private
   */
  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ description: &quot;Whether the ticket is private&quot; })
  isPrivate?: boolean;

  @IsArray()
  @IsOptional()
  @ApiPropertyOptional({ description: &quot;The attachment URLs&quot; })
  attachmentUrls?: string[];

  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() &#x3D;&gt; ExternalCustomFieldValuesDto)
  @ApiPropertyOptional({
    description: &quot;The custom field values&quot;,
    type: [ExternalCustomFieldValuesDto],
  })
  customFieldValues?: ExternalCustomFieldValuesDto[];
}

/**
 * Fields for escalating a ticket
 */
export class EscalateTicketBody {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: &quot;The reason for the escalation&quot; })
  reason: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: &quot;The details of the escalation&quot; })
  details: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: &quot;The impact of the escalation&quot; })
  impact: string;
}

/**
 * Fields for updating a ticket
 */
export class UpdateTicketBody extends CommonTicketFields {
  /**
   * The title of the ticket
   */
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: &quot;The title of the ticket&quot; })
  title?: string;

  /**
   * The ID of the sub-team
   */
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: &quot;The ID of the sub-team&quot; })
  teamId?: string;

  /**
   * The custom field values
   */
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() &#x3D;&gt; ExternalCustomFieldValuesDto)
  customFieldValues?: ExternalCustomFieldValuesDto[];
}

/**
 * Fields for creating a ticket
 */
export class CreateTicketBody extends CommonTicketFields {
  /**
   * The title of the ticket
   */
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: &quot;The title of the ticket&quot; })
  title: string;

  /**
   * The email of the requestor
   */
  @IsEmail()
  @IsNotEmpty()
  @ApiProperty({ description: &quot;The email of the requestor&quot; })
  requestorEmail: string;

  /**
   * The ID of the account
   */
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: &quot;The ID of the account&quot; })
  accountId?: string;

  /**
   * The ID of the team
   */
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: &quot;The ID of the team&quot; })
  teamId: string;

  /**
   * The ID of the form
   */
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: &quot;The ID of the form&quot; })
  formId?: string;
}

export class AssignTicketBody {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: &quot;The ID of the agent to assign&quot; })
  assignedAgentId: string;
}

export class AssignTeamToTicketBody {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: &quot;The ID of the team to assign&quot; })
  teamId: string;
}

/**
 * Fields for marking or creating a sub-ticket
 */
export class MarkOrCreateSubTicketBody extends CommonTicketFields {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: &quot;The ID of the parent ticket&quot; })
  parentTicketId: string;

  /**
   * The ID of the ticket to mark as a sub-ticket
   */
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description: &quot;The ID of the ticket to mark as a sub-ticket&quot;,
  })
  subTicketId?: string;

  /**
   * The title of the ticket
   */
  @IsString()
  @IsNotEmpty()
  @ValidateIf((o) &#x3D;&gt; !o.subTicketId)
  @ApiPropertyOptional({ description: &quot;The title of the ticket&quot; })
  title?: string;
}

export class MarkDuplicateBody {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: &quot;The ID of the ticket to mark as duplicate&quot; })
  duplicateTicketId: string;

  @IsString()
  @IsNotEmpty()
  @ValidateIf((o) &#x3D;&gt; o.duplicateTicketId !&#x3D;&#x3D; o.duplicateOfTicketId, {
    message: &quot;A ticket cannot be marked as a duplicate of itself&quot;,
  })
  @ApiProperty({ description: &quot;The ID of the ticket that is being duplicated&quot; })
  duplicateOfTicketId: string;
}

export class LinkTicketsBody {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: &quot;The ID of the source ticket&quot; })
  sourceTicketId: string;

  @IsString()
  @IsNotEmpty()
  @ValidateIf((o) &#x3D;&gt; o.sourceTicketId !&#x3D;&#x3D; o.linkedTicketId, {
    message: &quot;A ticket cannot be linked to itself&quot;,
  })
  @ApiProperty({ description: &quot;The ID of the target ticket to link to&quot; })
  linkedTicketId: string;
}
</code></pre>
    </div>
</div>









                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'class';
            var COMPODOC_CURRENT_PAGE_URL = 'EscalateTicketBody.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
