<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content miscellaneous-functions">
                   <div class="content-data">


















<ol class="breadcrumb">
  <li class="breadcrumb-item">Miscellaneous</li>
  <li class="breadcrumb-item">Functions</li>
</ol>

<section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <ul class="index-list">
                        <li>
                            <a href="#bootstrap" title="src/main.ts" ><b>bootstrap</b>&nbsp;&nbsp;&nbsp;(src/.../main.ts)</a>
                        </li>
                        <li>
                            <a href="#constructDailyConfigFromCommonSlots" title="src/common/utils/time-slots.utils.ts" ><b>constructDailyConfigFromCommonSlots</b>&nbsp;&nbsp;&nbsp;(src/.../time-slots.utils.ts)</a>
                        </li>
                        <li>
                            <a href="#generateIdentifier" title="src/common/utils/identifier-generator.utils.ts" ><b>generateIdentifier</b>&nbsp;&nbsp;&nbsp;(src/.../identifier-generator.utils.ts)</a>
                        </li>
                        <li>
                            <a href="#getCommentEventType" title="src/communications/utils/index.ts" ><b>getCommentEventType</b>&nbsp;&nbsp;&nbsp;(src/.../index.ts)</a>
                        </li>
                        <li>
                            <a href="#getMaxLimit" title="src/common/utils/api-helpers.utils.ts" ><b>getMaxLimit</b>&nbsp;&nbsp;&nbsp;(src/.../api-helpers.utils.ts)</a>
                        </li>
                        <li>
                            <a href="#IsAfterDate" title="src/common/validators/time.validators.ts" ><b>IsAfterDate</b>&nbsp;&nbsp;&nbsp;(src/.../time.validators.ts)</a>
                        </li>
                        <li>
                            <a href="#IsDateDDMM" title="src/common/validators/date.validators.ts" ><b>IsDateDDMM</b>&nbsp;&nbsp;&nbsp;(src/.../date.validators.ts)</a>
                        </li>
                        <li>
                            <a href="#IsDateDDMMOrDDMMYYYY" title="src/common/validators/date.validators.ts" ><b>IsDateDDMMOrDDMMYYYY</b>&nbsp;&nbsp;&nbsp;(src/.../date.validators.ts)</a>
                        </li>
                        <li>
                            <a href="#IsDateDDMMYYYY" title="src/common/validators/date.validators.ts" ><b>IsDateDDMMYYYY</b>&nbsp;&nbsp;&nbsp;(src/.../date.validators.ts)</a>
                        </li>
                        <li>
                            <a href="#IsTimezoneOrEmpty" title="src/common/validators/time.validators.ts" ><b>IsTimezoneOrEmpty</b>&nbsp;&nbsp;&nbsp;(src/.../time.validators.ts)</a>
                        </li>
                        <li>
                            <a href="#IsValidTriggerFieldValue" title="src/forms/dto/form.dto.ts" ><b>IsValidTriggerFieldValue</b>&nbsp;&nbsp;&nbsp;(src/.../form.dto.ts)</a>
                        </li>
                        <li>
                            <a href="#SkipAllThrottler" title="src/common/decorators/throttler.decorator.ts" ><b>SkipAllThrottler</b>&nbsp;&nbsp;&nbsp;(src/.../throttler.decorator.ts)</a>
                        </li>
                        <li>
                            <a href="#ThrottleSpecialTier" title="src/common/decorators/throttler.decorator.ts" ><b>ThrottleSpecialTier</b>&nbsp;&nbsp;&nbsp;(src/.../throttler.decorator.ts)</a>
                        </li>
                        <li>
                            <a href="#ThrottleTierFour" title="src/common/decorators/throttler.decorator.ts" ><b>ThrottleTierFour</b>&nbsp;&nbsp;&nbsp;(src/.../throttler.decorator.ts)</a>
                        </li>
                        <li>
                            <a href="#ThrottleTierOne" title="src/common/decorators/throttler.decorator.ts" ><b>ThrottleTierOne</b>&nbsp;&nbsp;&nbsp;(src/.../throttler.decorator.ts)</a>
                        </li>
                        <li>
                            <a href="#ThrottleTierThree" title="src/common/decorators/throttler.decorator.ts" ><b>ThrottleTierThree</b>&nbsp;&nbsp;&nbsp;(src/.../throttler.decorator.ts)</a>
                        </li>
                        <li>
                            <a href="#ThrottleTierTwo" title="src/common/decorators/throttler.decorator.ts" ><b>ThrottleTierTwo</b>&nbsp;&nbsp;&nbsp;(src/.../throttler.decorator.ts)</a>
                        </li>
                        <li>
                            <a href="#updateSLAMetadata" title="src/tickets/utils/sla-metadata.utils.ts" ><b>updateSLAMetadata</b>&nbsp;&nbsp;&nbsp;(src/.../sla-metadata.utils.ts)</a>
                        </li>
                    </ul>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    <h3>src/main.ts</h3>
    <section data-compodoc="block-methods">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="bootstrap"></a>
                    <span class="name">
                        <span ><b>bootstrap</b></span>
                        <a href="#bootstrap"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>bootstrap()</code>
                </td>
            </tr>




        </tbody>
    </table>
</section>    <h3>src/common/utils/time-slots.utils.ts</h3>
    <section data-compodoc="block-methods">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="constructDailyConfigFromCommonSlots"></a>
                    <span class="name">
                        <span ><b>constructDailyConfigFromCommonSlots</b></span>
                        <a href="#constructDailyConfigFromCommonSlots"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>constructDailyConfigFromCommonSlots(commonSlots)</code>
                </td>
            </tr>




            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>commonSlots</td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/BusinessHoursConfigDto.html" target="_self" >BusinessHoursConfigDto</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>    <h3>src/common/utils/identifier-generator.utils.ts</h3>
    <section data-compodoc="block-methods">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="generateIdentifier"></a>
                    <span class="name">
                        <span ><b>generateIdentifier</b></span>
                        <a href="#generateIdentifier"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>generateIdentifier(name: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, length: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>)</code>
                </td>
            </tr>




            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Default value</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>name</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>

                                            <td>
                                            </td>

                                        </tr>
                                        <tr>
                                                <td>length</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>

                                            <td>
                                                    <code>3</code>
                                            </td>

                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>    <h3>src/communications/utils/index.ts</h3>
    <section data-compodoc="block-methods">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getCommentEventType"></a>
                    <span class="name">
                        <span ><b>getCommentEventType</b></span>
                        <a href="#getCommentEventType"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getCommentEventType(entityType: CommentEntityTypes, op: <a href="../undefineds/CommentOp.html" target="_self">CommentOp</a>)</code>
                </td>
            </tr>




            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>entityType</td>
                                            <td>
                                                        <code>CommentEntityTypes</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>op</td>
                                            <td>
                                                            <code><a href="../miscellaneous/enumerations.html#CommentOp" target="_self" >CommentOp</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>    <h3>src/common/utils/api-helpers.utils.ts</h3>
    <section data-compodoc="block-methods">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getMaxLimit"></a>
                    <span class="name">
                        <span ><b>getMaxLimit</b></span>
                        <a href="#getMaxLimit"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getMaxLimit(limit)</code>
                </td>
            </tr>




            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Get the maximum limit for a query</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>limit</td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The limit to check</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>    <h3>src/common/validators/time.validators.ts</h3>
    <section data-compodoc="block-methods">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="IsAfterDate"></a>
                    <span class="name">
                        <span ><b>IsAfterDate</b></span>
                        <a href="#IsAfterDate"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>IsAfterDate(property: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, validationOptions?: ValidationOptions)</code>
                </td>
            </tr>




            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>property</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>validationOptions</td>
                                            <td>
                                                        <code>ValidationOptions</code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="IsTimezoneOrEmpty"></a>
                    <span class="name">
                        <span ><b>IsTimezoneOrEmpty</b></span>
                        <a href="#IsTimezoneOrEmpty"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>IsTimezoneOrEmpty(validationOptions?: ValidationOptions)</code>
                </td>
            </tr>




            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>validationOptions</td>
                                            <td>
                                                        <code>ValidationOptions</code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>    <h3>src/common/validators/date.validators.ts</h3>
    <section data-compodoc="block-methods">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="IsDateDDMM"></a>
                    <span class="name">
                        <span ><b>IsDateDDMM</b></span>
                        <a href="#IsDateDDMM"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>IsDateDDMM(validationOptions?: ValidationOptions)</code>
                </td>
            </tr>




            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Validates that the value is a valid date in DD-MM format</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>validationOptions</td>
                                            <td>
                                                        <code>ValidationOptions</code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                                    <p>The validation options</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="IsDateDDMMOrDDMMYYYY"></a>
                    <span class="name">
                        <span ><b>IsDateDDMMOrDDMMYYYY</b></span>
                        <a href="#IsDateDDMMOrDDMMYYYY"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>IsDateDDMMOrDDMMYYYY(validationOptions?: ValidationOptions)</code>
                </td>
            </tr>




            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Validates that the value is a valid date in DD-MM or DD-MM-YYYY format</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>validationOptions</td>
                                            <td>
                                                        <code>ValidationOptions</code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                                    <p>The validation options</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="IsDateDDMMYYYY"></a>
                    <span class="name">
                        <span ><b>IsDateDDMMYYYY</b></span>
                        <a href="#IsDateDDMMYYYY"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>IsDateDDMMYYYY(validationOptions?: ValidationOptions)</code>
                </td>
            </tr>




            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Validates that the value is a valid date in DD-MM-YYYY format</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>validationOptions</td>
                                            <td>
                                                        <code>ValidationOptions</code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                                    <p>The validation options</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>    <h3>src/forms/dto/form.dto.ts</h3>
    <section data-compodoc="block-methods">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="IsValidTriggerFieldValue"></a>
                    <span class="name">
                        <span ><b>IsValidTriggerFieldValue</b></span>
                        <a href="#IsValidTriggerFieldValue"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>IsValidTriggerFieldValue(validationOptions?: ValidationOptions)</code>
                </td>
            </tr>




            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>validationOptions</td>
                                            <td>
                                                        <code>ValidationOptions</code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>    <h3>src/common/decorators/throttler.decorator.ts</h3>
    <section data-compodoc="block-methods">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="SkipAllThrottler"></a>
                    <span class="name">
                        <span ><b>SkipAllThrottler</b></span>
                        <a href="#SkipAllThrottler"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>SkipAllThrottler()</code>
                </td>
            </tr>




            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Skip throttler for all tiers</p>
</div>

                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ThrottleSpecialTier"></a>
                    <span class="name">
                        <span ><b>ThrottleSpecialTier</b></span>
                        <a href="#ThrottleSpecialTier"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>ThrottleSpecialTier()</code>
                </td>
            </tr>




            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Throttle API rate limit for special tier</p>
</div>

                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ThrottleTierFour"></a>
                    <span class="name">
                        <span ><b>ThrottleTierFour</b></span>
                        <a href="#ThrottleTierFour"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>ThrottleTierFour()</code>
                </td>
            </tr>




            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Throttle API rate limit for tier 4</p>
</div>

                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ThrottleTierOne"></a>
                    <span class="name">
                        <span ><b>ThrottleTierOne</b></span>
                        <a href="#ThrottleTierOne"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>ThrottleTierOne()</code>
                </td>
            </tr>




            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Throttle API rate limit for tier 1</p>
</div>

                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ThrottleTierThree"></a>
                    <span class="name">
                        <span ><b>ThrottleTierThree</b></span>
                        <a href="#ThrottleTierThree"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>ThrottleTierThree()</code>
                </td>
            </tr>




            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Throttle API rate limit for tier 3</p>
</div>

                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ThrottleTierTwo"></a>
                    <span class="name">
                        <span ><b>ThrottleTierTwo</b></span>
                        <a href="#ThrottleTierTwo"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>ThrottleTierTwo()</code>
                </td>
            </tr>




            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Throttle API rate limit for tier 2</p>
</div>

                </td>
            </tr>
        </tbody>
    </table>
</section>    <h3>src/tickets/utils/sla-metadata.utils.ts</h3>
    <section data-compodoc="block-methods">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateSLAMetadata"></a>
                    <span class="name">
                        <span ><b>updateSLAMetadata</b></span>
                        <a href="#updateSLAMetadata"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>updateSLAMetadata(currentMetadata, slaMessage: <a href="../interfaces/SLAMessage.html" target="_self">SLAMessage</a>)</code>
                </td>
            </tr>




            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>currentMetadata</td>
                                            <td>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>slaMessage</td>
                                            <td>
                                                            <code><a href="../interfaces/SLAMessage.html" target="_self" >SLAMessage</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../interfaces/TicketSLAMetadata.html" target="_self" >TicketSLAMetadata</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>


                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'miscellaneous-functions';
            var COMPODOC_CURRENT_PAGE_URL = 'functions.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
