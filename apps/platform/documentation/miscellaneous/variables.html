<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content miscellaneous-variables">
                   <div class="content-data">


















<ol class="breadcrumb">
  <li class="breadcrumb-item">Miscellaneous</li>
  <li class="breadcrumb-item">Variables</li>
</ol>

<section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <ul class="index-list">
                        <li>
                            <a href="#ACCOUNT_ACTIVITY_PAYLOAD_SCHEMA" title="src/common/workflows/constants/accounts-response.schema.ts" ><b>ACCOUNT_ACTIVITY_PAYLOAD_SCHEMA</b>&nbsp;&nbsp;&nbsp;(src/.../accounts-response.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#ACCOUNT_ATTRIBUTE_VALUE_PAYLOAD_SCHEMA" title="src/common/workflows/constants/accounts-response.schema.ts" ><b>ACCOUNT_ATTRIBUTE_VALUE_PAYLOAD_SCHEMA</b>&nbsp;&nbsp;&nbsp;(src/.../accounts-response.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#ACCOUNT_NOTE_PAYLOAD_SCHEMA" title="src/common/workflows/constants/accounts-response.schema.ts" ><b>ACCOUNT_NOTE_PAYLOAD_SCHEMA</b>&nbsp;&nbsp;&nbsp;(src/.../accounts-response.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#ACCOUNT_PAYLOAD_SCHEMA" title="src/common/workflows/constants/accounts-response.schema.ts" ><b>ACCOUNT_PAYLOAD_SCHEMA</b>&nbsp;&nbsp;&nbsp;(src/.../accounts-response.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#ACCOUNT_RELATIONSHIP_PAYLOAD_SCHEMA" title="src/common/workflows/constants/accounts-response.schema.ts" ><b>ACCOUNT_RELATIONSHIP_PAYLOAD_SCHEMA</b>&nbsp;&nbsp;&nbsp;(src/.../accounts-response.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#ACCOUNT_RELATIONSHIP_TYPE_PAYLOAD_SCHEMA" title="src/common/workflows/constants/accounts-response.schema.ts" ><b>ACCOUNT_RELATIONSHIP_TYPE_PAYLOAD_SCHEMA</b>&nbsp;&nbsp;&nbsp;(src/.../accounts-response.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#ACCOUNT_TASK_PAYLOAD_SCHEMA" title="src/common/workflows/constants/accounts-response.schema.ts" ><b>ACCOUNT_TASK_PAYLOAD_SCHEMA</b>&nbsp;&nbsp;&nbsp;(src/.../accounts-response.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#addTeamMemberSchema" title="src/teams/validators/index.ts" ><b>addTeamMemberSchema</b>&nbsp;&nbsp;&nbsp;(src/.../index.ts)</a>
                        </li>
                        <li>
                            <a href="#ALL_SKIP" title="src/common/decorators/throttler.decorator.ts" ><b>ALL_SKIP</b>&nbsp;&nbsp;&nbsp;(src/.../throttler.decorator.ts)</a>
                        </li>
                        <li>
                            <a href="#BLOCK_DURATION_IN_MS" title="src/config/throttler.config.ts" ><b>BLOCK_DURATION_IN_MS</b>&nbsp;&nbsp;&nbsp;(src/.../throttler.config.ts)</a>
                        </li>
                        <li>
                            <a href="#BOT_USER_EMAIL_DOMAIN" title="src/users/constants/users.constants.ts" ><b>BOT_USER_EMAIL_DOMAIN</b>&nbsp;&nbsp;&nbsp;(src/.../users.constants.ts)</a>
                        </li>
                        <li>
                            <a href="#businessDaySchema" title="src/teams/validators/index.ts" ><b>businessDaySchema</b>&nbsp;&nbsp;&nbsp;(src/.../index.ts)</a>
                        </li>
                        <li>
                            <a href="#businessHoursConfigSchema" title="src/teams/validators/index.ts" ><b>businessHoursConfigSchema</b>&nbsp;&nbsp;&nbsp;(src/.../index.ts)</a>
                        </li>
                        <li>
                            <a href="#businessSlotSchema" title="src/teams/validators/index.ts" ><b>businessSlotSchema</b>&nbsp;&nbsp;&nbsp;(src/.../index.ts)</a>
                        </li>
                        <li>
                            <a href="#CACHE_TTL" title="src/common/constants/cache.constants.ts" ><b>CACHE_TTL</b>&nbsp;&nbsp;&nbsp;(src/.../cache.constants.ts)</a>
                        </li>
                        <li>
                            <a href="#Client" title="__mocks__/typesense/typesense.mock.ts" ><b>Client</b>&nbsp;&nbsp;&nbsp;(__mocks__/.../typesense.mock.ts)</a>
                        </li>
                        <li>
                            <a href="#COMMENT_RESPONSE_SCHEMA" title="src/common/workflows/constants/communications-response.schema.ts" ><b>COMMENT_RESPONSE_SCHEMA</b>&nbsp;&nbsp;&nbsp;(src/.../communications-response.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#COMMENT_SNS_PUBLISHER" title="src/communications/constants/comments.constants.ts" ><b>COMMENT_SNS_PUBLISHER</b>&nbsp;&nbsp;&nbsp;(src/.../comments.constants.ts)</a>
                        </li>
                        <li>
                            <a href="#COMMON_MESSAGE_PROPERTIES" title="src/common/workflows/constants/common.schema.ts" ><b>COMMON_MESSAGE_PROPERTIES</b>&nbsp;&nbsp;&nbsp;(src/.../common.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#COMMON_TICKET_RESPONSE_SCHEMA" title="src/common/workflows/constants/tickets-response.schema.ts" ><b>COMMON_TICKET_RESPONSE_SCHEMA</b>&nbsp;&nbsp;&nbsp;(src/.../tickets-response.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#COMMON_TICKETS_EVENT_SCHEMA" title="src/common/workflows/constants/tickets-response.schema.ts" ><b>COMMON_TICKETS_EVENT_SCHEMA</b>&nbsp;&nbsp;&nbsp;(src/.../tickets-response.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#COMMON_USER_RESPONSE_SCHEMA" title="src/common/workflows/constants/common.schema.ts" ><b>COMMON_USER_RESPONSE_SCHEMA</b>&nbsp;&nbsp;&nbsp;(src/.../common.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#ContextUserType" title="__mocks__/aws/sns.mock.ts" ><b>ContextUserType</b>&nbsp;&nbsp;&nbsp;(__mocks__/.../sns.mock.ts)</a>
                        </li>
                        <li>
                            <a href="#createdCustomFields" title="src/forms/form-setup.ts" ><b>createdCustomFields</b>&nbsp;&nbsp;&nbsp;(src/.../form-setup.ts)</a>
                        </li>
                        <li>
                            <a href="#createOrganizationSchema" title="src/organization/validators/index.ts" ><b>createOrganizationSchema</b>&nbsp;&nbsp;&nbsp;(src/.../index.ts)</a>
                        </li>
                        <li>
                            <a href="#CreateRoutingRuleSchema" title="src/teams/validators/index.ts" ><b>CreateRoutingRuleSchema</b>&nbsp;&nbsp;&nbsp;(src/.../index.ts)</a>
                        </li>
                        <li>
                            <a href="#createTeamSchema" title="src/teams/validators/index.ts" ><b>createTeamSchema</b>&nbsp;&nbsp;&nbsp;(src/.../index.ts)</a>
                        </li>
                        <li>
                            <a href="#createTicketValidator" title="src/tickets/controllers/grpc/validators/index.ts" ><b>createTicketValidator</b>&nbsp;&nbsp;&nbsp;(src/.../index.ts)</a>
                        </li>
                        <li>
                            <a href="#CurrentUser" title="src/common/decorators/user.decorator.ts" ><b>CurrentUser</b>&nbsp;&nbsp;&nbsp;(src/.../user.decorator.ts)</a>
                        </li>
                        <li>
                            <a href="#CUSTOMER_CONTACT_PAYLOAD_SCHEMA" title="src/common/workflows/constants/accounts-response.schema.ts" ><b>CUSTOMER_CONTACT_PAYLOAD_SCHEMA</b>&nbsp;&nbsp;&nbsp;(src/.../accounts-response.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#CustomFieldValidatorConstants" title="src/custom-field/constants/constants.ts" ><b>CustomFieldValidatorConstants</b>&nbsp;&nbsp;&nbsp;(src/.../constants.ts)</a>
                        </li>
                        <li>
                            <a href="#EAGER_TICKET_RELATIONS" title="src/tickets/utils/tickets.constants.ts" ><b>EAGER_TICKET_RELATIONS</b>&nbsp;&nbsp;&nbsp;(src/.../tickets.constants.ts)</a>
                        </li>
                        <li>
                            <a href="#EAGERLY_FETCH_RELATIONS" title="src/views/constants/views.constants.ts" ><b>EAGERLY_FETCH_RELATIONS</b>&nbsp;&nbsp;&nbsp;(src/.../views.constants.ts)</a>
                        </li>
                        <li>
                            <a href="#EAGERLY_LOAD_COMMENTS_RELATIONS" title="src/communications/constants/comments.constants.ts" ><b>EAGERLY_LOAD_COMMENTS_RELATIONS</b>&nbsp;&nbsp;&nbsp;(src/.../comments.constants.ts)</a>
                        </li>
                        <li>
                            <a href="#EAGERLY_LOADED_RELATIONS" title="src/teams/constants/teams.constants.ts" ><b>EAGERLY_LOADED_RELATIONS</b>&nbsp;&nbsp;&nbsp;(src/.../teams.constants.ts)</a>
                        </li>
                        <li>
                            <a href="#EAGERLY_LOADED_RELATIONS_FOR_TEAM_MEMBERS" title="src/teams/constants/team-members.constants.ts" ><b>EAGERLY_LOADED_RELATIONS_FOR_TEAM_MEMBERS</b>&nbsp;&nbsp;&nbsp;(src/.../team-members.constants.ts)</a>
                        </li>
                        <li>
                            <a href="#EMBEDDING_CONSTANTS" title="src/config/typesense/constants/embedding.constants.ts" ><b>EMBEDDING_CONSTANTS</b>&nbsp;&nbsp;&nbsp;(src/.../embedding.constants.ts)</a>
                        </li>
                        <li>
                            <a href="#EmittableOrganizationEvents" title="src/organization/events/organization.events.ts" ><b>EmittableOrganizationEvents</b>&nbsp;&nbsp;&nbsp;(src/.../organization.events.ts)</a>
                        </li>
                        <li>
                            <a href="#EmittableTicketEvents" title="src/tickets/events/tickets.events.ts" ><b>EmittableTicketEvents</b>&nbsp;&nbsp;&nbsp;(src/.../tickets.events.ts)</a>
                        </li>
                        <li>
                            <a href="#entities" title="src/config/db.config.ts" ><b>entities</b>&nbsp;&nbsp;&nbsp;(src/.../db.config.ts)</a>
                        </li>
                        <li>
                            <a href="#ERROR_CODES" title="src/constants/error-codes.constants.ts" ><b>ERROR_CODES</b>&nbsp;&nbsp;&nbsp;(src/.../error-codes.constants.ts)</a>
                        </li>
                        <li>
                            <a href="#EXTERNAL_CUSTOM_FIELDS_RESPONSE_SCHEMA" title="src/common/workflows/constants/common.schema.ts" ><b>EXTERNAL_CUSTOM_FIELDS_RESPONSE_SCHEMA</b>&nbsp;&nbsp;&nbsp;(src/.../common.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#EXTERNAL_STORAGE_RESPONSE_SCHEMA" title="src/common/workflows/constants/common.schema.ts" ><b>EXTERNAL_STORAGE_RESPONSE_SCHEMA</b>&nbsp;&nbsp;&nbsp;(src/.../common.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#extractEmailDetails" title="src/common/utils/extract-email-details.ts" ><b>extractEmailDetails</b>&nbsp;&nbsp;&nbsp;(src/.../extract-email-details.ts)</a>
                        </li>
                        <li>
                            <a href="#extractNameFromEmail" title="src/common/utils/extract-email-details.ts" ><b>extractNameFromEmail</b>&nbsp;&nbsp;&nbsp;(src/.../extract-email-details.ts)</a>
                        </li>
                        <li>
                            <a href="#GetObjectRelations" title="src/custom-object/constants/index.ts" ><b>GetObjectRelations</b>&nbsp;&nbsp;&nbsp;(src/.../index.ts)</a>
                        </li>
                        <li>
                            <a href="#getPlatformDBConfig" title="src/config/db.config.ts" ><b>getPlatformDBConfig</b>&nbsp;&nbsp;&nbsp;(src/.../db.config.ts)</a>
                        </li>
                        <li>
                            <a href="#GetTicketsRelations" title="src/tickets/constants/tickets.constants.ts" ><b>GetTicketsRelations</b>&nbsp;&nbsp;&nbsp;(src/.../tickets.constants.ts)</a>
                        </li>
                        <li>
                            <a href="#getTypesenseConfig" title="src/config/typesense.config.ts" ><b>getTypesenseConfig</b>&nbsp;&nbsp;&nbsp;(src/.../typesense.config.ts)</a>
                        </li>
                        <li>
                            <a href="#grpcServerConfig" title="src/grpc.server.config.ts" ><b>grpcServerConfig</b>&nbsp;&nbsp;&nbsp;(src/.../grpc.server.config.ts)</a>
                        </li>
                        <li>
                            <a href="#injectWithOrgId" title="src/utils/test-utils/index.ts" ><b>injectWithOrgId</b>&nbsp;&nbsp;&nbsp;(src/.../index.ts)</a>
                        </li>
                        <li>
                            <a href="#IS_DATE_DDMM" title="src/common/validators/date.validators.ts" ><b>IS_DATE_DDMM</b>&nbsp;&nbsp;&nbsp;(src/.../date.validators.ts)</a>
                        </li>
                        <li>
                            <a href="#IS_DATE_DDMM_OR_DDMMYYYY" title="src/common/validators/date.validators.ts" ><b>IS_DATE_DDMM_OR_DDMMYYYY</b>&nbsp;&nbsp;&nbsp;(src/.../date.validators.ts)</a>
                        </li>
                        <li>
                            <a href="#IS_DATE_DDMMYYYY" title="src/common/validators/date.validators.ts" ><b>IS_DATE_DDMMYYYY</b>&nbsp;&nbsp;&nbsp;(src/.../date.validators.ts)</a>
                        </li>
                        <li>
                            <a href="#IS_PUBLIC_KEY" title="src/auth/decorators/auth.decorator.ts" ><b>IS_PUBLIC_KEY</b>&nbsp;&nbsp;&nbsp;(src/.../auth.decorator.ts)</a>
                        </li>
                        <li>
                            <a href="#IS_TIMEZONE_OR_EMPTY" title="src/common/validators/time.validators.ts" ><b>IS_TIMEZONE_OR_EMPTY</b>&nbsp;&nbsp;&nbsp;(src/.../time.validators.ts)</a>
                        </li>
                        <li>
                            <a href="#isValidDate" title="src/custom-field/utils/helper.ts" ><b>isValidDate</b>&nbsp;&nbsp;&nbsp;(src/.../helper.ts)</a>
                        </li>
                        <li>
                            <a href="#isValidDateTime" title="src/custom-field/utils/helper.ts" ><b>isValidDateTime</b>&nbsp;&nbsp;&nbsp;(src/.../helper.ts)</a>
                        </li>
                        <li>
                            <a href="#isValidEmail" title="src/custom-field/utils/helper.ts" ><b>isValidEmail</b>&nbsp;&nbsp;&nbsp;(src/.../helper.ts)</a>
                        </li>
                        <li>
                            <a href="#isValidNumber" title="src/custom-field/utils/helper.ts" ><b>isValidNumber</b>&nbsp;&nbsp;&nbsp;(src/.../helper.ts)</a>
                        </li>
                        <li>
                            <a href="#isValidPassword" title="src/custom-field/utils/helper.ts" ><b>isValidPassword</b>&nbsp;&nbsp;&nbsp;(src/.../helper.ts)</a>
                        </li>
                        <li>
                            <a href="#isValidPhoneNumber" title="src/custom-field/utils/helper.ts" ><b>isValidPhoneNumber</b>&nbsp;&nbsp;&nbsp;(src/.../helper.ts)</a>
                        </li>
                        <li>
                            <a href="#isValidRegex" title="src/custom-field/utils/helper.ts" ><b>isValidRegex</b>&nbsp;&nbsp;&nbsp;(src/.../helper.ts)</a>
                        </li>
                        <li>
                            <a href="#isValidString" title="src/custom-field/utils/helper.ts" ><b>isValidString</b>&nbsp;&nbsp;&nbsp;(src/.../helper.ts)</a>
                        </li>
                        <li>
                            <a href="#isValidTimeFormat" title="src/teams/validators/index.ts" ><b>isValidTimeFormat</b>&nbsp;&nbsp;&nbsp;(src/.../index.ts)</a>
                        </li>
                        <li>
                            <a href="#isValidUrl" title="src/custom-field/utils/helper.ts" ><b>isValidUrl</b>&nbsp;&nbsp;&nbsp;(src/.../helper.ts)</a>
                        </li>
                        <li>
                            <a href="#jwtConstants" title="src/auth/constants.ts" ><b>jwtConstants</b>&nbsp;&nbsp;&nbsp;(src/.../constants.ts)</a>
                        </li>
                        <li>
                            <a href="#LOG_SPAN_ID" title="src/teams/controllers/teams.controller.ts" ><b>LOG_SPAN_ID</b>&nbsp;&nbsp;&nbsp;(src/.../teams.controller.ts)</a>
                        </li>
                        <li>
                            <a href="#MESSAGE_ATTRIBUTES_SCHEMA" title="src/common/workflows/constants/common.schema.ts" ><b>MESSAGE_ATTRIBUTES_SCHEMA</b>&nbsp;&nbsp;&nbsp;(src/.../common.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#multiChoiceField" title="src/forms/form-setup.ts" ><b>multiChoiceField</b>&nbsp;&nbsp;&nbsp;(src/.../form-setup.ts)</a>
                        </li>
                        <li>
                            <a href="#multiChoiceFieldBody" title="src/forms/form-setup.ts" ><b>multiChoiceFieldBody</b>&nbsp;&nbsp;&nbsp;(src/.../form-setup.ts)</a>
                        </li>
                        <li>
                            <a href="#noop" title="src/organization/sagas/create-org-and-admin.saga.ts" ><b>noop</b>&nbsp;&nbsp;&nbsp;(src/.../create-org-and-admin.saga.ts)</a>
                        </li>
                        <li>
                            <a href="#ORGANIZATION_RESPONSE_SCHEMA" title="src/common/workflows/constants/organization-response.schema.ts" ><b>ORGANIZATION_RESPONSE_SCHEMA</b>&nbsp;&nbsp;&nbsp;(src/.../organization-response.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#phoneNoField" title="src/forms/form-setup.ts" ><b>phoneNoField</b>&nbsp;&nbsp;&nbsp;(src/.../form-setup.ts)</a>
                        </li>
                        <li>
                            <a href="#phoneNoFieldBody" title="src/forms/form-setup.ts" ><b>phoneNoFieldBody</b>&nbsp;&nbsp;&nbsp;(src/.../form-setup.ts)</a>
                        </li>
                        <li>
                            <a href="#POSTGRES_CONFIG" title="src/common/constants/test.constants.ts" ><b>POSTGRES_CONFIG</b>&nbsp;&nbsp;&nbsp;(src/.../test.constants.ts)</a>
                        </li>
                        <li>
                            <a href="#POSTGRES_ERROR_CODES" title="src/common/constants/postgres-errors.constants.ts" ><b>POSTGRES_ERROR_CODES</b>&nbsp;&nbsp;&nbsp;(src/.../postgres-errors.constants.ts)</a>
                        </li>
                        <li>
                            <a href="#priorities" title="src/shared/constants/tickets.constants.ts" ><b>priorities</b>&nbsp;&nbsp;&nbsp;(src/.../tickets.constants.ts)</a>
                        </li>
                        <li>
                            <a href="#Public" title="src/auth/decorators/auth.decorator.ts" ><b>Public</b>&nbsp;&nbsp;&nbsp;(src/.../auth.decorator.ts)</a>
                        </li>
                        <li>
                            <a href="#QueueNames" title="src/constants/queue.constants.ts" ><b>QueueNames</b>&nbsp;&nbsp;&nbsp;(src/.../queue.constants.ts)</a>
                        </li>
                        <li>
                            <a href="#REQUEST_ROUTER_ENGINE" title="src/tickets/routing/providers/request-router.provider.ts" ><b>REQUEST_ROUTER_ENGINE</b>&nbsp;&nbsp;&nbsp;(src/.../request-router.provider.ts)</a>
                        </li>
                        <li>
                            <a href="#RequestRouterProvider" title="src/tickets/routing/providers/request-router.provider.ts" ><b>RequestRouterProvider</b>&nbsp;&nbsp;&nbsp;(src/.../request-router.provider.ts)</a>
                        </li>
                        <li>
                            <a href="#RuleOperatorEnum" title="src/teams/validators/index.ts" ><b>RuleOperatorEnum</b>&nbsp;&nbsp;&nbsp;(src/.../index.ts)</a>
                        </li>
                        <li>
                            <a href="#RuleSchema" title="src/teams/validators/index.ts" ><b>RuleSchema</b>&nbsp;&nbsp;&nbsp;(src/.../index.ts)</a>
                        </li>
                        <li>
                            <a href="#SELECT_FROM_USERS" title="src/users/constants/users.dbconstants.ts" ><b>SELECT_FROM_USERS</b>&nbsp;&nbsp;&nbsp;(src/.../users.dbconstants.ts)</a>
                        </li>
                        <li>
                            <a href="#simpleTextField" title="src/forms/form-setup.ts" ><b>simpleTextField</b>&nbsp;&nbsp;&nbsp;(src/.../form-setup.ts)</a>
                        </li>
                        <li>
                            <a href="#simpleTextFieldBody" title="src/forms/form-setup.ts" ><b>simpleTextFieldBody</b>&nbsp;&nbsp;&nbsp;(src/.../form-setup.ts)</a>
                        </li>
                        <li>
                            <a href="#singleChoiceField" title="src/forms/form-setup.ts" ><b>singleChoiceField</b>&nbsp;&nbsp;&nbsp;(src/.../form-setup.ts)</a>
                        </li>
                        <li>
                            <a href="#singleChoiceFieldBody" title="src/forms/form-setup.ts" ><b>singleChoiceFieldBody</b>&nbsp;&nbsp;&nbsp;(src/.../form-setup.ts)</a>
                        </li>
                        <li>
                            <a href="#SNSModule" title="__mocks__/aws/sns.mock.ts" ><b>SNSModule</b>&nbsp;&nbsp;&nbsp;(__mocks__/.../sns.mock.ts)</a>
                        </li>
                        <li>
                            <a href="#SNSPublisherService" title="__mocks__/aws/sns.mock.ts" ><b>SNSPublisherService</b>&nbsp;&nbsp;&nbsp;(__mocks__/.../sns.mock.ts)</a>
                        </li>
                        <li>
                            <a href="#SNSService" title="__mocks__/aws/sns.mock.ts" ><b>SNSService</b>&nbsp;&nbsp;&nbsp;(__mocks__/.../sns.mock.ts)</a>
                        </li>
                        <li>
                            <a href="#SQSConsumerService" title="__mocks__/aws/sqs.mock.ts" ><b>SQSConsumerService</b>&nbsp;&nbsp;&nbsp;(__mocks__/.../sqs.mock.ts)</a>
                        </li>
                        <li>
                            <a href="#SQSModule" title="__mocks__/aws/sqs.mock.ts" ><b>SQSModule</b>&nbsp;&nbsp;&nbsp;(__mocks__/.../sqs.mock.ts)</a>
                        </li>
                        <li>
                            <a href="#SQSProducerService" title="__mocks__/aws/sqs.mock.ts" ><b>SQSProducerService</b>&nbsp;&nbsp;&nbsp;(__mocks__/.../sqs.mock.ts)</a>
                        </li>
                        <li>
                            <a href="#statuses" title="src/shared/constants/tickets.constants.ts" ><b>statuses</b>&nbsp;&nbsp;&nbsp;(src/.../tickets.constants.ts)</a>
                        </li>
                        <li>
                            <a href="#TEAM_ROLE_MEMBER" title="src/teams/teams.constants.ts" ><b>TEAM_ROLE_MEMBER</b>&nbsp;&nbsp;&nbsp;(src/.../teams.constants.ts)</a>
                        </li>
                        <li>
                            <a href="#TEAM_ROLE_OWNER" title="src/teams/teams.constants.ts" ><b>TEAM_ROLE_OWNER</b>&nbsp;&nbsp;&nbsp;(src/.../teams.constants.ts)</a>
                        </li>
                        <li>
                            <a href="#TEAM_ROUTING_RULES_RELATIONS" title="src/teams/constants/teams.constants.ts" ><b>TEAM_ROUTING_RULES_RELATIONS</b>&nbsp;&nbsp;&nbsp;(src/.../teams.constants.ts)</a>
                        </li>
                        <li>
                            <a href="#THENA_RESTRICTED_FIELDS" title="src/forms/constants/index.ts" ><b>THENA_RESTRICTED_FIELDS</b>&nbsp;&nbsp;&nbsp;(src/.../index.ts)</a>
                        </li>
                        <li>
                            <a href="#THROTTLER_CONFIG" title="src/config/throttler.config.ts" ><b>THROTTLER_CONFIG</b>&nbsp;&nbsp;&nbsp;(src/.../throttler.config.ts)</a>
                        </li>
                        <li>
                            <a href="#THROTTLER_SPECIAL_TIER" title="src/config/throttler.config.ts" ><b>THROTTLER_SPECIAL_TIER</b>&nbsp;&nbsp;&nbsp;(src/.../throttler.config.ts)</a>
                        </li>
                        <li>
                            <a href="#THROTTLER_TIER" title="src/config/throttler.config.ts" ><b>THROTTLER_TIER</b>&nbsp;&nbsp;&nbsp;(src/.../throttler.config.ts)</a>
                        </li>
                        <li>
                            <a href="#THROTTLER_TIER_1" title="src/config/throttler.config.ts" ><b>THROTTLER_TIER_1</b>&nbsp;&nbsp;&nbsp;(src/.../throttler.config.ts)</a>
                        </li>
                        <li>
                            <a href="#THROTTLER_TIER_2" title="src/config/throttler.config.ts" ><b>THROTTLER_TIER_2</b>&nbsp;&nbsp;&nbsp;(src/.../throttler.config.ts)</a>
                        </li>
                        <li>
                            <a href="#THROTTLER_TIER_3" title="src/config/throttler.config.ts" ><b>THROTTLER_TIER_3</b>&nbsp;&nbsp;&nbsp;(src/.../throttler.config.ts)</a>
                        </li>
                        <li>
                            <a href="#THROTTLER_TIER_4" title="src/config/throttler.config.ts" ><b>THROTTLER_TIER_4</b>&nbsp;&nbsp;&nbsp;(src/.../throttler.config.ts)</a>
                        </li>
                        <li>
                            <a href="#TICKET_SNS_PUBLISHER" title="src/tickets/utils/tickets.constants.ts" ><b>TICKET_SNS_PUBLISHER</b>&nbsp;&nbsp;&nbsp;(src/.../tickets.constants.ts)</a>
                        </li>
                        <li>
                            <a href="#ticketEmbeddingsSchema" title="src/config/typesense/constants/embedding.constants.ts" ><b>ticketEmbeddingsSchema</b>&nbsp;&nbsp;&nbsp;(src/.../embedding.constants.ts)</a>
                        </li>
                        <li>
                            <a href="#TicketEvents" title="__mocks__/aws/sns.mock.ts" ><b>TicketEvents</b>&nbsp;&nbsp;&nbsp;(__mocks__/.../sns.mock.ts)</a>
                        </li>
                        <li>
                            <a href="#TicketSelect" title="src/tickets/constants/tickets.constants.ts" ><b>TicketSelect</b>&nbsp;&nbsp;&nbsp;(src/.../tickets.constants.ts)</a>
                        </li>
                        <li>
                            <a href="#TTL_IN_MS" title="src/config/throttler.config.ts" ><b>TTL_IN_MS</b>&nbsp;&nbsp;&nbsp;(src/.../throttler.config.ts)</a>
                        </li>
                        <li>
                            <a href="#types" title="src/shared/constants/tickets.constants.ts" ><b>types</b>&nbsp;&nbsp;&nbsp;(src/.../tickets.constants.ts)</a>
                        </li>
                        <li>
                            <a href="#TYPESENSE_CONSTANTS" title="src/config/typesense/constants/typesense.constants.ts" ><b>TYPESENSE_CONSTANTS</b>&nbsp;&nbsp;&nbsp;(src/.../typesense.constants.ts)</a>
                        </li>
                        <li>
                            <a href="#TypesenseModule" title="__mocks__/typesense/typesense.mock.ts" ><b>TypesenseModule</b>&nbsp;&nbsp;&nbsp;(__mocks__/.../typesense.mock.ts)</a>
                        </li>
                        <li>
                            <a href="#TypesenseService" title="__mocks__/typesense/typesense.mock.ts" ><b>TypesenseService</b>&nbsp;&nbsp;&nbsp;(__mocks__/.../typesense.mock.ts)</a>
                        </li>
                        <li>
                            <a href="#UpdatableThenaRestrictedFieldsProperties" title="src/forms/constants/index.ts" ><b>UpdatableThenaRestrictedFieldsProperties</b>&nbsp;&nbsp;&nbsp;(src/.../index.ts)</a>
                        </li>
                        <li>
                            <a href="#updateOrganizationSchema" title="src/organization/validators/index.ts" ><b>updateOrganizationSchema</b>&nbsp;&nbsp;&nbsp;(src/.../index.ts)</a>
                        </li>
                        <li>
                            <a href="#UpdateRoutingRuleSchema" title="src/teams/validators/index.ts" ><b>UpdateRoutingRuleSchema</b>&nbsp;&nbsp;&nbsp;(src/.../index.ts)</a>
                        </li>
                        <li>
                            <a href="#updateTeamSchema" title="src/teams/validators/index.ts" ><b>updateTeamSchema</b>&nbsp;&nbsp;&nbsp;(src/.../index.ts)</a>
                        </li>
                        <li>
                            <a href="#updateTicketValidator" title="src/tickets/controllers/grpc/validators/index.ts" ><b>updateTicketValidator</b>&nbsp;&nbsp;&nbsp;(src/.../index.ts)</a>
                        </li>
                        <li>
                            <a href="#updateTimezoneWorkingHoursSchema" title="src/teams/validators/index.ts" ><b>updateTimezoneWorkingHoursSchema</b>&nbsp;&nbsp;&nbsp;(src/.../index.ts)</a>
                        </li>
                    </ul>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    <h3>src/common/workflows/constants/accounts-response.schema.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ACCOUNT_ACTIVITY_PAYLOAD_SCHEMA"></a>
                    <span class="name">
                        <span ><b>ACCOUNT_ACTIVITY_PAYLOAD_SCHEMA</b></span>
                        <a href="#ACCOUNT_ACTIVITY_PAYLOAD_SCHEMA"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  type: &quot;object&quot;,
  required: [&quot;id&quot;, &quot;accountId&quot;, &quot;account&quot;, &quot;createdAt&quot;, &quot;updatedAt&quot;],
  properties: {
    id: { type: &quot;string&quot; },
    accountId: { type: &quot;string&quot; },
    account: { type: &quot;string&quot; },
    activityTimestamp: { type: [&quot;string&quot;, &quot;null&quot;] },
    duration: { type: [&quot;number&quot;, &quot;null&quot;] },
    location: { type: [&quot;string&quot;, &quot;null&quot;] },
    type: { type: [&quot;string&quot;, &quot;null&quot;] },
    typeId: { type: [&quot;string&quot;, &quot;null&quot;] },
    status: { type: [&quot;string&quot;, &quot;null&quot;] },
    statusId: { type: [&quot;string&quot;, &quot;null&quot;] },
    participants: { type: &quot;array&quot;, items: { type: &quot;string&quot; } },
    creator: { type: &quot;string&quot; },
    creatorId: { type: &quot;string&quot; },
    creatorEmail: { type: &quot;string&quot; },
    attachments: EXTERNAL_STORAGE_RESPONSE_SCHEMA,
    createdAt: { type: &quot;string&quot; },
    updatedAt: { type: &quot;string&quot; },
  },
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ACCOUNT_ATTRIBUTE_VALUE_PAYLOAD_SCHEMA"></a>
                    <span class="name">
                        <span ><b>ACCOUNT_ATTRIBUTE_VALUE_PAYLOAD_SCHEMA</b></span>
                        <a href="#ACCOUNT_ATTRIBUTE_VALUE_PAYLOAD_SCHEMA"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  type: &quot;object&quot;,
  required: [&quot;id&quot;, &quot;attribute&quot;, &quot;value&quot;, &quot;isDefault&quot;, &quot;createdAt&quot;, &quot;updatedAt&quot;],
  properties: {
    id: { type: &quot;string&quot; },
    attribute: { type: &quot;string&quot; },
    value: { type: &quot;string&quot; },
    isDefault: { type: &quot;boolean&quot; },
    createdAt: { type: &quot;string&quot; },
    updatedAt: { type: &quot;string&quot; },
  },
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ACCOUNT_NOTE_PAYLOAD_SCHEMA"></a>
                    <span class="name">
                        <span ><b>ACCOUNT_NOTE_PAYLOAD_SCHEMA</b></span>
                        <a href="#ACCOUNT_NOTE_PAYLOAD_SCHEMA"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  type: &quot;object&quot;,
  required: [
    &quot;id&quot;,
    &quot;accountId&quot;,
    &quot;account&quot;,
    &quot;content&quot;,
    &quot;visibility&quot;,
    &quot;author&quot;,
    &quot;authorId&quot;,
    &quot;authorEmail&quot;,
    &quot;createdAt&quot;,
    &quot;updatedAt&quot;,
  ],
  properties: {
    id: { type: &quot;string&quot; },
    accountId: { type: &quot;string&quot; },
    account: { type: &quot;string&quot; },
    content: { type: &quot;string&quot; },
    type: { type: [&quot;string&quot;, &quot;null&quot;] },
    typeId: { type: [&quot;string&quot;, &quot;null&quot;] },
    visibility: { type: &quot;string&quot; },
    attachments: EXTERNAL_STORAGE_RESPONSE_SCHEMA,
    author: { type: &quot;string&quot; },
    authorId: { type: &quot;string&quot; },
    authorEmail: { type: &quot;string&quot; },
    createdAt: { type: &quot;string&quot; },
    updatedAt: { type: &quot;string&quot; },
  },
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ACCOUNT_PAYLOAD_SCHEMA"></a>
                    <span class="name">
                        <span ><b>ACCOUNT_PAYLOAD_SCHEMA</b></span>
                        <a href="#ACCOUNT_PAYLOAD_SCHEMA"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  type: &quot;object&quot;,
  required: [&quot;id&quot;, &quot;name&quot;, &quot;primaryDomain&quot;, &quot;createdAt&quot;, &quot;updatedAt&quot;],
  properties: {
    id: { type: &quot;string&quot; },
    name: { type: &quot;string&quot; },
    description: { type: [&quot;string&quot;, &quot;null&quot;] },
    source: { type: [&quot;string&quot;, &quot;null&quot;] },
    logo: { type: [&quot;string&quot;, &quot;null&quot;] },
    statusId: { type: [&quot;string&quot;, &quot;null&quot;] },
    status: { type: [&quot;string&quot;, &quot;null&quot;] },
    classificationId: { type: [&quot;string&quot;, &quot;null&quot;] },
    classification: { type: [&quot;string&quot;, &quot;null&quot;] },
    healthId: { type: [&quot;string&quot;, &quot;null&quot;] },
    health: { type: [&quot;string&quot;, &quot;null&quot;] },
    industryId: { type: [&quot;string&quot;, &quot;null&quot;] },
    industry: { type: [&quot;string&quot;, &quot;null&quot;] },
    primaryDomain: { type: &quot;string&quot; },
    secondaryDomain: { type: [&quot;string&quot;, &quot;null&quot;] },
    accountOwnerId: { type: [&quot;string&quot;, &quot;null&quot;] },
    accountOwnerEmail: { type: [&quot;string&quot;, &quot;null&quot;] },
    annualRevenue: { type: [&quot;number&quot;, &quot;null&quot;] },
    employees: { type: [&quot;number&quot;, &quot;null&quot;] },
    website: { type: [&quot;string&quot;, &quot;null&quot;] },
    billingAddress: { type: [&quot;string&quot;, &quot;null&quot;] },
    shippingAddress: { type: [&quot;string&quot;, &quot;null&quot;] },
    customFieldValues: EXTERNAL_CUSTOM_FIELDS_RESPONSE_SCHEMA,
    createdAt: { type: &quot;string&quot; },
    updatedAt: { type: &quot;string&quot; },
  },
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ACCOUNT_RELATIONSHIP_PAYLOAD_SCHEMA"></a>
                    <span class="name">
                        <span ><b>ACCOUNT_RELATIONSHIP_PAYLOAD_SCHEMA</b></span>
                        <a href="#ACCOUNT_RELATIONSHIP_PAYLOAD_SCHEMA"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  type: &quot;object&quot;,
  required: [
    &quot;id&quot;,
    &quot;accountId&quot;,
    &quot;account&quot;,
    &quot;relatedAccountId&quot;,
    &quot;relatedAccount&quot;,
    &quot;type&quot;,
    &quot;createdAt&quot;,
    &quot;updatedAt&quot;,
  ],
  properties: {
    id: { type: &quot;string&quot; },
    accountId: { type: &quot;string&quot; },
    account: { type: &quot;string&quot; },
    relatedAccountId: { type: &quot;string&quot; },
    relatedAccount: { type: &quot;string&quot; },
    type: { type: &quot;string&quot; },
    createdAt: { type: &quot;string&quot; },
    updatedAt: { type: &quot;string&quot; },
  },
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ACCOUNT_RELATIONSHIP_TYPE_PAYLOAD_SCHEMA"></a>
                    <span class="name">
                        <span ><b>ACCOUNT_RELATIONSHIP_TYPE_PAYLOAD_SCHEMA</b></span>
                        <a href="#ACCOUNT_RELATIONSHIP_TYPE_PAYLOAD_SCHEMA"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  type: &quot;object&quot;,
  required: [&quot;id&quot;, &quot;name&quot;, &quot;createdAt&quot;, &quot;updatedAt&quot;],
  properties: {
    id: { type: &quot;string&quot; },
    name: { type: &quot;string&quot; },
    inverseRelationshipId: { type: [&quot;string&quot;, &quot;null&quot;] },
    inverseRelationship: { type: [&quot;string&quot;, &quot;null&quot;] },
    createdAt: { type: &quot;string&quot; },
    updatedAt: { type: &quot;string&quot; },
  },
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ACCOUNT_TASK_PAYLOAD_SCHEMA"></a>
                    <span class="name">
                        <span ><b>ACCOUNT_TASK_PAYLOAD_SCHEMA</b></span>
                        <a href="#ACCOUNT_TASK_PAYLOAD_SCHEMA"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  type: &quot;object&quot;,
  required: [&quot;id&quot;, &quot;accountId&quot;, &quot;account&quot;, &quot;title&quot;, &quot;createdAt&quot;, &quot;updatedAt&quot;],
  properties: {
    id: { type: &quot;string&quot; },
    accountId: { type: &quot;string&quot; },
    account: { type: &quot;string&quot; },
    activityId: { type: &quot;string&quot; },
    title: { type: &quot;string&quot; },
    description: { type: [&quot;string&quot;, &quot;null&quot;] },
    assigneeId: { type: [&quot;string&quot;, &quot;null&quot;] },
    assignee: { type: [&quot;string&quot;, &quot;null&quot;] },
    creator: { type: &quot;string&quot; },
    creatorId: { type: &quot;string&quot; },
    creatorEmail: { type: &quot;string&quot; },
    type: { type: [&quot;string&quot;, &quot;null&quot;] },
    typeId: { type: [&quot;string&quot;, &quot;null&quot;] },
    status: { type: [&quot;string&quot;, &quot;null&quot;] },
    statusId: { type: [&quot;string&quot;, &quot;null&quot;] },
    priority: { type: [&quot;string&quot;, &quot;null&quot;] },
    priorityId: { type: [&quot;string&quot;, &quot;null&quot;] },
    attachments: EXTERNAL_STORAGE_RESPONSE_SCHEMA,
    createdAt: { type: &quot;string&quot; },
    updatedAt: { type: &quot;string&quot; },
  },
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="CUSTOMER_CONTACT_PAYLOAD_SCHEMA"></a>
                    <span class="name">
                        <span ><b>CUSTOMER_CONTACT_PAYLOAD_SCHEMA</b></span>
                        <a href="#CUSTOMER_CONTACT_PAYLOAD_SCHEMA"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  type: &quot;object&quot;,
  required: [&quot;id&quot;, &quot;firstName&quot;, &quot;email&quot;, &quot;createdAt&quot;, &quot;updatedAt&quot;],
  properties: {
    id: { type: &quot;string&quot; },
    firstName: { type: &quot;string&quot; },
    lastName: { type: [&quot;string&quot;, &quot;null&quot;] },
    email: { type: &quot;string&quot; },
    phoneNumber: { type: [&quot;string&quot;, &quot;null&quot;] },
    accounts: {
      type: &quot;array&quot;,
      items: {
        type: &quot;object&quot;,
        properties: { id: { type: &quot;string&quot; }, name: { type: &quot;string&quot; } },
      },
    },
    contactTypeId: { type: [&quot;string&quot;, &quot;null&quot;] },
    contactType: { type: [&quot;string&quot;, &quot;null&quot;] },
    createdAt: { type: &quot;string&quot; },
    updatedAt: { type: &quot;string&quot; },
  },
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/teams/validators/index.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="addTeamMemberSchema"></a>
                    <span class="name">
                        <span ><b>addTeamMemberSchema</b></span>
                        <a href="#addTeamMemberSchema"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>z.object({
  teamId: z.string(),
  email: z.string().email().optional(),
  userId: z.string().optional(),
  isAdmin: z.boolean().optional(),
})</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="businessDaySchema"></a>
                    <span class="name">
                        <span ><b>businessDaySchema</b></span>
                        <a href="#businessDaySchema"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>z
  .object({
    isActive: z.boolean({
      required_error: &quot;The business day must be active or inactive!&quot;,
    }),
    slots: z.array(businessSlotSchema).optional(),
  })
  .superRefine((obj, ctx) &#x3D;&gt; {
    // If inactive, slots can be undefined or empty
    if (!obj.isActive) {
      return;
    }

    // If active, slots must be present and not empty
    if (!obj.slots || obj.slots.length &#x3D;&#x3D;&#x3D; 0) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: [&quot;slots&quot;],
        message: &quot;Active business days must have at least one time slot&quot;,
      });
      return;
    }

    // Validate that slots don&#x27;t overlap and are in chronological order
    for (let i &#x3D; 1; i &lt; obj.slots.length; i++) {
      const prevEnd &#x3D; obj.slots[i - 1].end;
      const currentStart &#x3D; obj.slots[i].start;
      if (prevEnd &gt;&#x3D; currentStart) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: [&quot;slots&quot;],
          message:
            &quot;Time slots must not overlap and must be in chronological order&quot;,
        });
        return;
      }
    }
  })</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="businessHoursConfigSchema"></a>
                    <span class="name">
                        <span ><b>businessHoursConfigSchema</b></span>
                        <a href="#businessHoursConfigSchema"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>z.object({
  monday: businessDaySchema.optional(),
  tuesday: businessDaySchema.optional(),
  wednesday: businessDaySchema.optional(),
  thursday: businessDaySchema.optional(),
  friday: businessDaySchema.optional(),
  saturday: businessDaySchema.optional(),
  sunday: businessDaySchema.optional(),
})</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="businessSlotSchema"></a>
                    <span class="name">
                        <span ><b>businessSlotSchema</b></span>
                        <a href="#businessSlotSchema"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>z.object({
  start: z.string().refine(isValidTimeFormat, {
    message: &quot;Start time must be in 24-hour format (HH:mm)&quot;,
  }),
  end: z.string().refine(isValidTimeFormat, {
    message: &quot;End time must be in 24-hour format (HH:mm)&quot;,
  }),
})</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="CreateRoutingRuleSchema"></a>
                    <span class="name">
                        <span ><b>CreateRoutingRuleSchema</b></span>
                        <a href="#CreateRoutingRuleSchema"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>z
  .object({
    name: z.string().min(1, &quot;Name is required&quot;),
    description: z.string().optional(),
    teamId: z.string().min(1, &quot;Team ID is required&quot;),
    evaluationOrder: z.number().optional(),
    resultTeamId: z.string().min(1, &quot;Result team ID is required&quot;),
    andRules: z
      .array(RuleSchema)
      .min(1, &quot;At least one AND rule is required&quot;)
      .optional(),
    orRules: z
      .array(RuleSchema)
      .min(1, &quot;At least one OR rule is required&quot;)
      .optional(),
  })
  .refine((data) &#x3D;&gt; data.andRules?.length &gt; 0 || data.orRules?.length &gt; 0, {
    message: &quot;At least one of andRules or orRules must be provided&quot;,
    path: [&quot;rules&quot;], // This will show the error at the rules level
  })</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createTeamSchema"></a>
                    <span class="name">
                        <span ><b>createTeamSchema</b></span>
                        <a href="#createTeamSchema"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>z.object({
  name: z.string().min(6),
  description: z.string().optional(),
  identifier: z.string().optional(),
  parentTeamId: z.string().optional(),
  isPrivate: z.boolean().optional(),
})</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isValidTimeFormat"></a>
                    <span class="name">
                        <span ><b>isValidTimeFormat</b></span>
                        <a href="#isValidTimeFormat"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>(time: string) &#x3D;&gt; {
  const timeRegex &#x3D; /^([0-1][0-9]|2[0-3]):[0-5][0-9]$/;
  return timeRegex.test(time);
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="RuleOperatorEnum"></a>
                    <span class="name">
                        <span ><b>RuleOperatorEnum</b></span>
                        <a href="#RuleOperatorEnum"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>z.enum([
  &quot;equals&quot;,
  &quot;not_equals&quot;,
  &quot;contains&quot;,
  &quot;not_contains&quot;,
  &quot;greater_than&quot;,
  &quot;less_than&quot;,
  &quot;in&quot;,
  &quot;not_in&quot;,
  &quot;matches&quot;,
  &quot;is_null&quot;,
  &quot;is_not_null&quot;,
])</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="RuleSchema"></a>
                    <span class="name">
                        <span ><b>RuleSchema</b></span>
                        <a href="#RuleSchema"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>z.object({
  field: z.string().min(1, &quot;Field is required&quot;),
  operator: RuleOperatorEnum,
  value: z.string().min(1, &quot;Value is required&quot;),
  precedence: z.number().optional(),
})</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="UpdateRoutingRuleSchema"></a>
                    <span class="name">
                        <span ><b>UpdateRoutingRuleSchema</b></span>
                        <a href="#UpdateRoutingRuleSchema"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>z
  .object({
    id: z.string().min(1, &quot;Rule ID is required&quot;),
    name: z.string().min(1, &quot;Name is required&quot;).optional(),
    teamId: z.string().min(1, &quot;Team ID is required&quot;).optional(),
    description: z.string().optional(),
    evaluationOrder: z.number().optional(),
    resultTeamId: z.string().min(1, &quot;Result team ID is required&quot;).optional(),
    andRules: z
      .array(RuleSchema)
      .min(1, &quot;At least one AND rule is required&quot;)
      .optional(),
    orRules: z
      .array(RuleSchema)
      .min(1, &quot;At least one OR rule is required&quot;)
      .optional(),
  })
  .refine(
    (data) &#x3D;&gt; {
      // Only validate rules if they are being updated
      if (data.andRules || data.orRules) {
        return data.andRules?.length &gt; 0 || data.orRules?.length &gt; 0;
      }
      return true;
    },
    {
      message:
        &quot;If updating rules, at least one of andRules or orRules must be provided&quot;,
      path: [&quot;rules&quot;],
    },
  )</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateTeamSchema"></a>
                    <span class="name">
                        <span ><b>updateTeamSchema</b></span>
                        <a href="#updateTeamSchema"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>z.object({
  name: z.string().optional(),
  description: z.string().optional(),
  isPrivate: z.boolean().optional(),
  icon: z.string().optional(),
})</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateTimezoneWorkingHoursSchema"></a>
                    <span class="name">
                        <span ><b>updateTimezoneWorkingHoursSchema</b></span>
                        <a href="#updateTimezoneWorkingHoursSchema"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>z.object({
  timezone: z
    .string()
    .refine((tz) &#x3D;&gt; {
      try {
        Intl.DateTimeFormat(undefined, { timeZone: tz });
        return true;
      } catch (_error) {
        return false;
      }
    }, &quot;Invalid timezone&quot;)
    .optional(),
  routingRespectsTimezone: z.boolean().optional(),
  routingRespectsUserTimezone: z.boolean().optional(),
  routingRespectsUserAvailability: z.boolean().optional(),
  userRoutingStrategy: z.nativeEnum(TeamUserRoutingStrategy).optional(),
  dailyConfig: businessHoursConfigSchema.optional(),
})</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/common/decorators/throttler.decorator.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ALL_SKIP"></a>
                    <span class="name">
                        <span ><b>ALL_SKIP</b></span>
                        <a href="#ALL_SKIP"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  [THROTTLER_TIER_1]: true,
  [THROTTLER_TIER_2]: true,
  [THROTTLER_TIER_3]: true,
  [THROTTLER_TIER_4]: true,
  [THROTTLER_SPECIAL_TIER]: true,
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/config/throttler.config.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="BLOCK_DURATION_IN_MS"></a>
                    <span class="name">
                        <span ><b>BLOCK_DURATION_IN_MS</b></span>
                        <a href="#BLOCK_DURATION_IN_MS"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>10_000</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="THROTTLER_CONFIG"></a>
                    <span class="name">
                        <span ><b>THROTTLER_CONFIG</b></span>
                        <a href="#THROTTLER_CONFIG"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>ThrottlerOptions[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[
  {
    ...THROTTLER_TIER.TIER_1,
    blockDuration: BLOCK_DURATION_IN_MS,
  },
  {
    ...THROTTLER_TIER.TIER_2,
    blockDuration: BLOCK_DURATION_IN_MS,
  },
  {
    ...THROTTLER_TIER.TIER_3,
    blockDuration: BLOCK_DURATION_IN_MS,
  },
  {
    ...THROTTLER_TIER.TIER_4,
    blockDuration: BLOCK_DURATION_IN_MS,
  },
  {
    ...THROTTLER_TIER.SPECIAL_TIER,
    blockDuration: BLOCK_DURATION_IN_MS,
  },
]</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="THROTTLER_SPECIAL_TIER"></a>
                    <span class="name">
                        <span ><b>THROTTLER_SPECIAL_TIER</b></span>
                        <a href="#THROTTLER_SPECIAL_TIER"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&quot;special-tier&quot;</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="THROTTLER_TIER"></a>
                    <span class="name">
                        <span ><b>THROTTLER_TIER</b></span>
                        <a href="#THROTTLER_TIER"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  TIER_1: {
    name: THROTTLER_TIER_1,
    ttl: TTL_IN_MS,
    limit: 3,
  },
  TIER_2: {
    name: THROTTLER_TIER_2,
    ttl: TTL_IN_MS,
    limit: 24,
  },
  TIER_3: {
    name: THROTTLER_TIER_3,
    ttl: TTL_IN_MS,
    limit: 52,
  },
  TIER_4: {
    name: THROTTLER_TIER_4,
    ttl: TTL_IN_MS,
    limit: 112,
  },
  SPECIAL_TIER: {
    name: THROTTLER_SPECIAL_TIER,
    ttl: TTL_IN_MS,
    limit: 250,
  },
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="THROTTLER_TIER_1"></a>
                    <span class="name">
                        <span ><b>THROTTLER_TIER_1</b></span>
                        <a href="#THROTTLER_TIER_1"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&quot;tier-1&quot;</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="THROTTLER_TIER_2"></a>
                    <span class="name">
                        <span ><b>THROTTLER_TIER_2</b></span>
                        <a href="#THROTTLER_TIER_2"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&quot;tier-2&quot;</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="THROTTLER_TIER_3"></a>
                    <span class="name">
                        <span ><b>THROTTLER_TIER_3</b></span>
                        <a href="#THROTTLER_TIER_3"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&quot;tier-3&quot;</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="THROTTLER_TIER_4"></a>
                    <span class="name">
                        <span ><b>THROTTLER_TIER_4</b></span>
                        <a href="#THROTTLER_TIER_4"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&quot;tier-4&quot;</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="TTL_IN_MS"></a>
                    <span class="name">
                        <span ><b>TTL_IN_MS</b></span>
                        <a href="#TTL_IN_MS"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>60_000</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/users/constants/users.constants.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="BOT_USER_EMAIL_DOMAIN"></a>
                    <span class="name">
                        <span ><b>BOT_USER_EMAIL_DOMAIN</b></span>
                        <a href="#BOT_USER_EMAIL_DOMAIN"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&quot;thenaappsplatform.ai&quot;</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/common/constants/cache.constants.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="CACHE_TTL"></a>
                    <span class="name">
                        <span ><b>CACHE_TTL</b></span>
                        <a href="#CACHE_TTL"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  WEEK: 7 * 24 * 60 * 60,
  MONTH: 30 * 24 * 60 * 60,
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>__mocks__/typesense/typesense.mock.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="Client"></a>
                    <span class="name">
                        <span ><b>Client</b></span>
                        <a href="#Client"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>jest.fn().mockImplementation(() &#x3D;&gt; ({
  collections: jest.fn().mockReturnValue({
    documents: jest.fn().mockReturnValue({
      search: jest.fn().mockImplementation((searchParameters) &#x3D;&gt; {
        const hitsPerPage &#x3D; parseInt(searchParameters.hits_per_page) || 20;
        const page &#x3D; parseInt(searchParameters.page) || 1;

        // Generate base results
        const allResults &#x3D;
          searchParameters.q &#x3D;&#x3D;&#x3D; &quot;network&quot;
            ? [
                {
                  document: {
                    title: &quot;Network connectivity issues&quot;,
                    organizationId: global.testOrganization.uid,
                  },
                },
              ]
            : Array(5)
                .fill({})
                .map((_, i) &#x3D;&gt; ({
                  document: {
                    title: &#x60;Test Ticket ${i}&#x60;,
                    organizationId: global.testOrganization.uid,
                  },
                }));

        // Apply pagination
        const start &#x3D; (page - 1) * hitsPerPage;
        const paginatedHits &#x3D; allResults.slice(start, start + hitsPerPage);

        return {
          hits: paginatedHits,
          page,
          found: allResults.length, // Total number of results
          found_docs: hitsPerPage, // Results per page
        };
      }),
      create: jest.fn().mockResolvedValue({ id: &quot;mock-id&quot; }),
      update: jest.fn().mockResolvedValue({ id: &quot;mock-id&quot; }),
      delete: jest.fn().mockResolvedValue({ id: &quot;mock-id&quot; }),
      upsert: jest.fn().mockResolvedValue({ id: &quot;mock-id&quot; }),
    }),
  }),
}))</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="TypesenseModule"></a>
                    <span class="name">
                        <span ><b>TypesenseModule</b></span>
                        <a href="#TypesenseModule"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  forRoot: jest.fn().mockReturnValue({
    module: class {},
    providers: [],
  }),
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="TypesenseService"></a>
                    <span class="name">
                        <span ><b>TypesenseService</b></span>
                        <a href="#TypesenseService"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>jest.fn().mockImplementation(() &#x3D;&gt; ({
  client: new Client(),
}))</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/common/workflows/constants/communications-response.schema.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="COMMENT_RESPONSE_SCHEMA"></a>
                    <span class="name">
                        <span ><b>COMMENT_RESPONSE_SCHEMA</b></span>
                        <a href="#COMMENT_RESPONSE_SCHEMA"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  type: &quot;object&quot;,
  properties: {
    id: { type: &quot;string&quot; },
    content: { type: &quot;string&quot; },
    contentHtml: { type: [&quot;string&quot;, &quot;null&quot;] },
    contentMarkdown: { type: [&quot;string&quot;, &quot;null&quot;] },
    isEdited: { type: &quot;boolean&quot; },
    threadName: { type: &quot;string&quot; },
    commentVisibility: { type: &quot;string&quot; },
    commentType: { type: &quot;string&quot; },
    isPinned: { type: &quot;boolean&quot; },
    sourceEmailId: { type: [&quot;string&quot;, &quot;null&quot;] },
    metadata: {
      type: [&quot;object&quot;, &quot;null&quot;],
      properties: {
        reactions: {
          type: &quot;object&quot;,
          additionalProperties: {
            type: &quot;object&quot;,
            properties: {
              count: { type: &quot;number&quot; },
              users: { type: &quot;array&quot;, items: { type: &quot;string&quot; } },
            },
            required: [&quot;count&quot;, &quot;users&quot;],
          },
        },
        replies: { type: &quot;array&quot;, items: { type: &quot;string&quot; } },
        mentions: { type: &quot;array&quot;, items: { type: &quot;string&quot; } },
        source: { type: &quot;string&quot; },
        integrationMetadata: {
          type: &quot;array&quot;,
          items: {
            type: &quot;object&quot;,
            properties: {
              source: { type: &quot;string&quot; },
              externalId: { type: &quot;string&quot; },
              channelId: { type: &quot;string&quot; },
              threadId: { type: &quot;string&quot; },
            },
            required: [&quot;source&quot;, &quot;externalId&quot;],
          },
        },
      },
      required: [
        &quot;reactions&quot;,
        &quot;replies&quot;,
        &quot;mentions&quot;,
        &quot;source&quot;,
        &quot;integrationMetadata&quot;,
      ],
    },
    parentCommentId: { type: [&quot;string&quot;, &quot;null&quot;] },
    author: { type: &quot;string&quot; },
    authorId: { type: &quot;string&quot; },
    authorUserType: { type: &quot;string&quot; },
    attachments: {
      type: &quot;array&quot;,
      items: {
        type: &quot;object&quot;,
        properties: {
          id: { type: &quot;string&quot; },
          url: { type: &quot;string&quot; },
          name: { type: &quot;string&quot; },
          size: { type: &quot;number&quot; },
          contentType: { type: &quot;string&quot; },
          createdAt: { type: &quot;string&quot; },
        },
        required: [&quot;id&quot;, &quot;url&quot;, &quot;name&quot;, &quot;size&quot;, &quot;contentType&quot;, &quot;createdAt&quot;],
      },
    },
    createdAt: { type: &quot;string&quot; },
    updatedAt: { type: &quot;string&quot; },
  },
  required: [
    &quot;id&quot;,
    &quot;content&quot;,
    &quot;contentHtml&quot;,
    &quot;contentMarkdown&quot;,
    &quot;isEdited&quot;,
    &quot;threadName&quot;,
    &quot;commentVisibility&quot;,
    &quot;commentType&quot;,
    &quot;isPinned&quot;,
    &quot;sourceEmailId&quot;,
    &quot;metadata&quot;,
    &quot;author&quot;,
    &quot;authorId&quot;,
    &quot;authorUserType&quot;,
    &quot;createdAt&quot;,
    &quot;updatedAt&quot;,
  ],
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/communications/constants/comments.constants.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="COMMENT_SNS_PUBLISHER"></a>
                    <span class="name">
                        <span ><b>COMMENT_SNS_PUBLISHER</b></span>
                        <a href="#COMMENT_SNS_PUBLISHER"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&quot;COMMENT_SNS_PUBLISHER&quot;</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="EAGERLY_LOAD_COMMENTS_RELATIONS"></a>
                    <span class="name">
                        <span ><b>EAGERLY_LOAD_COMMENTS_RELATIONS</b></span>
                        <a href="#EAGERLY_LOAD_COMMENTS_RELATIONS"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[
  &quot;author&quot;,
  &quot;parentComment&quot;,
  &quot;ticket&quot;,
  &quot;organization&quot;,
]</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/common/workflows/constants/common.schema.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="COMMON_MESSAGE_PROPERTIES"></a>
                    <span class="name">
                        <span ><b>COMMON_MESSAGE_PROPERTIES</b></span>
                        <a href="#COMMON_MESSAGE_PROPERTIES"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  actor: {
    type: &quot;object&quot;,
    required: [&quot;email&quot;, &quot;id&quot;, &quot;type&quot;],
    properties: {
      id: {
        type: &quot;string&quot;,
      },
      type: {
        type: &quot;string&quot;,
      },
      email: {
        type: &quot;string&quot;,
      },
    },
  },
  orgId: {
    type: &quot;string&quot;,
  },
  teamId: {
    type: &quot;string&quot;,
  },
  eventId: {
    type: &quot;string&quot;,
  },
  eventType: {
    type: &quot;string&quot;,
  },
  timestamp: {
    type: &quot;string&quot;,
  },
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="COMMON_USER_RESPONSE_SCHEMA"></a>
                    <span class="name">
                        <span ><b>COMMON_USER_RESPONSE_SCHEMA</b></span>
                        <a href="#COMMON_USER_RESPONSE_SCHEMA"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  type: &quot;object&quot;,
  properties: {
    id: { type: &quot;string&quot; },
    name: { type: &quot;string&quot; },
    email: { type: &quot;string&quot; },
    userType: { type: &quot;string&quot; },
    status: { type: &quot;string&quot; },
    isActive: { type: &quot;boolean&quot; },
    lastLoginAt: { type: [&quot;string&quot;, &quot;null&quot;] },
    avatarUrl: { type: [&quot;string&quot;, &quot;null&quot;] },
    timezone: { type: [&quot;string&quot;, &quot;null&quot;] },
    createdAt: { type: &quot;string&quot; },
    updatedAt: { type: &quot;string&quot; },
  },
  required: [
    &quot;id&quot;,
    &quot;name&quot;,
    &quot;email&quot;,
    &quot;userType&quot;,
    &quot;status&quot;,
    &quot;isActive&quot;,
    &quot;createdAt&quot;,
    &quot;updatedAt&quot;,
  ],
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="EXTERNAL_CUSTOM_FIELDS_RESPONSE_SCHEMA"></a>
                    <span class="name">
                        <span ><b>EXTERNAL_CUSTOM_FIELDS_RESPONSE_SCHEMA</b></span>
                        <a href="#EXTERNAL_CUSTOM_FIELDS_RESPONSE_SCHEMA"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  type: &quot;array&quot;,
  items: {
    type: &quot;object&quot;,
    required: [&quot;customFieldId&quot;, &quot;data&quot;],
    properties: {
      customFieldId: { type: &quot;string&quot; },
      data: { type: &quot;object&quot; },
      metadata: { type: &quot;object&quot; },
    },
  },
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="EXTERNAL_STORAGE_RESPONSE_SCHEMA"></a>
                    <span class="name">
                        <span ><b>EXTERNAL_STORAGE_RESPONSE_SCHEMA</b></span>
                        <a href="#EXTERNAL_STORAGE_RESPONSE_SCHEMA"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  type: &quot;array&quot;,
  items: {
    type: &quot;object&quot;,
    properties: {
      id: { type: &quot;string&quot; },
      url: { type: &quot;string&quot; },
      name: { type: &quot;string&quot; },
      size: { type: &quot;number&quot; },
      contentType: { type: &quot;string&quot; },
      createdAt: { type: &quot;string&quot; },
    },
  },
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="MESSAGE_ATTRIBUTES_SCHEMA"></a>
                    <span class="name">
                        <span ><b>MESSAGE_ATTRIBUTES_SCHEMA</b></span>
                        <a href="#MESSAGE_ATTRIBUTES_SCHEMA"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  type: &quot;object&quot;,
  required: [
    &quot;event_name&quot;,
    &quot;event_id&quot;,
    &quot;event_timestamp&quot;,
    &quot;context_user_id&quot;,
    &quot;context_user_type&quot;,
    &quot;context_organization_id&quot;,
  ],
  properties: {
    event_id: {
      type: &quot;string&quot;,
    },
    event_name: {
      type: &quot;string&quot;,
    },
    context_user_id: {
      type: &quot;string&quot;,
    },
    event_timestamp: {
      type: &quot;string&quot;,
    },
    context_user_type: {
      type: &quot;string&quot;,
    },
    context_organization_id: {
      type: &quot;string&quot;,
    },
  },
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/common/workflows/constants/tickets-response.schema.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="COMMON_TICKET_RESPONSE_SCHEMA"></a>
                    <span class="name">
                        <span ><b>COMMON_TICKET_RESPONSE_SCHEMA</b></span>
                        <a href="#COMMON_TICKET_RESPONSE_SCHEMA"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  type: &quot;object&quot;,
  properties: {
    id: { type: &quot;string&quot; },
    title: { type: &quot;string&quot; },
    ticketId: { type: &quot;number&quot; },
    description: { type: [&quot;string&quot;, &quot;null&quot;] },
    accountId: { type: [&quot;string&quot;, &quot;null&quot;] },
    account: { type: [&quot;string&quot;, &quot;null&quot;] },
    status: { type: [&quot;string&quot;, &quot;null&quot;] },
    priority: { type: [&quot;string&quot;, &quot;null&quot;] },
    teamId: { type: &quot;string&quot; },
    teamName: { type: &quot;string&quot; },
    isPrivate: { type: &quot;boolean&quot; },
    typeId: { type: [&quot;string&quot;, &quot;null&quot;] },
    type: { type: [&quot;string&quot;, &quot;null&quot;] },
    assignedAgent: { type: [&quot;string&quot;, &quot;null&quot;] },
    assignedAgentId: { type: [&quot;string&quot;, &quot;null&quot;] },
    requestorEmail: { type: &quot;string&quot; },
    submitterEmail: { type: [&quot;string&quot;, &quot;null&quot;] },
    deletedAt: { type: [&quot;string&quot;, &quot;null&quot;] },
    archivedAt: { type: [&quot;string&quot;, &quot;null&quot;] },
    createdAt: { type: &quot;string&quot; },
    updatedAt: { type: &quot;string&quot; },
  },
  required: [&quot;id&quot;, &quot;title&quot;, &quot;ticketId&quot;, &quot;teamId&quot;, &quot;teamName&quot;, &quot;requestorEmail&quot;],
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="COMMON_TICKETS_EVENT_SCHEMA"></a>
                    <span class="name">
                        <span ><b>COMMON_TICKETS_EVENT_SCHEMA</b></span>
                        <a href="#COMMON_TICKETS_EVENT_SCHEMA"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  type: &quot;object&quot;,
  required: [&quot;id&quot;, &quot;title&quot;, &quot;teamId&quot;],
  properties: {
    id: {
      type: &quot;string&quot;,
    },
    tags: {
      type: [&quot;array&quot;],
      items: {
        type: &quot;string&quot;,
      },
    },
    title: {
      type: &quot;string&quot;,
    },
    source: {
      type: [&quot;string&quot;, &quot;null&quot;],
    },
    teamId: {
      type: &quot;string&quot;,
    },
    customer: {
      type: &quot;object&quot;,
      required: [&quot;id&quot;, &quot;email&quot;, &quot;name&quot;],
      properties: {
        id: {
          type: &quot;string&quot;,
        },
        name: {
          type: &quot;string&quot;,
        },
        email: {
          type: &quot;string&quot;,
        },
      },
    },
    statusId: {
      type: [&quot;string&quot;, &quot;null&quot;],
    },
    createdAt: {
      type: &quot;string&quot;,
    },
    assignedTo: {
      type: &quot;string&quot;,
    },
    priorityId: {
      type: [&quot;string&quot;, &quot;null&quot;],
    },
    statusName: {
      type: [&quot;string&quot;, &quot;null&quot;],
    },
    description: {
      type: [&quot;string&quot;, &quot;null&quot;],
    },
    customFields: {
      type: [&quot;array&quot;, &quot;null&quot;],
    },
    priorityName: {
      type: [&quot;string&quot;, &quot;null&quot;],
    },
  },
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>__mocks__/aws/sns.mock.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ContextUserType"></a>
                    <span class="name">
                        <span ><b>ContextUserType</b></span>
                        <a href="#ContextUserType"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  USER: &quot;user&quot;,
  ORGANIZATION: &quot;organization&quot;,
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="SNSModule"></a>
                    <span class="name">
                        <span ><b>SNSModule</b></span>
                        <a href="#SNSModule"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  forRootAsync: jest.fn().mockReturnValue({
    module: class {},
    providers: [],
  }),
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="SNSPublisherService"></a>
                    <span class="name">
                        <span ><b>SNSPublisherService</b></span>
                        <a href="#SNSPublisherService"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>jest.fn().mockImplementation(() &#x3D;&gt; ({
  publishSNSMessage: jest.fn().mockResolvedValue({}),
}))</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="SNSService"></a>
                    <span class="name">
                        <span ><b>SNSService</b></span>
                        <a href="#SNSService"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>jest.fn().mockImplementation(() &#x3D;&gt; ({
  onModuleInit: jest.fn(),
  publish: jest.fn().mockResolvedValue({}),
  snsClient: {
    send: jest.fn().mockResolvedValue({}),
  },
}))</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="TicketEvents"></a>
                    <span class="name">
                        <span ><b>TicketEvents</b></span>
                        <a href="#TicketEvents"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  CREATED: &quot;ticket:created&quot;,
  UPDATED: &quot;ticket:updated&quot;,
  ARCHIVED: &quot;ticket:archived&quot;,
  DELETED: &quot;ticket:deleted&quot;,
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/forms/form-setup.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createdCustomFields"></a>
                    <span class="name">
                        <span ><b>createdCustomFields</b></span>
                        <a href="#createdCustomFields"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="multiChoiceField"></a>
                    <span class="name">
                        <span ><b>multiChoiceField</b></span>
                        <a href="#multiChoiceField"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="multiChoiceFieldBody"></a>
                    <span class="name">
                        <span ><b>multiChoiceFieldBody</b></span>
                        <a href="#multiChoiceFieldBody"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="../classes/CreateCustomFieldDto.html" target="_self" >CreateCustomFieldDto</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  name: &quot;Multi Choice field&quot;,
  fieldType: CustomFieldType.MULTI_CHOICE,
  source: CustomFieldSource.TICKET,
  options: [
    {
      value: &quot;option-1&quot;,
      is_disabled: false,
      order: 1,
      id: &quot;1&quot;,
      platformField: true,
    },
    {
      value: &quot;option-2&quot;,
      is_disabled: false,
      order: 2,
      id: &quot;2&quot;,
      platformField: true,
    },
    {
      value: &quot;option-3&quot;,
      is_disabled: false,
      order: 3,
      id: &quot;3&quot;,
      platformField: true,
    },
    {
      value: &quot;option-4&quot;,
      is_disabled: false,
      order: 4,
      id: &quot;4&quot;,
      platformField: true,
    },
  ],
  placeholderText: &quot;Select multiple options&quot;,
  hintText: &quot;Select multiple options&quot;,
  mandatoryOnCreation: false,
  mandatoryOnClose: false,
  visibleToCustomer: false,
  editableByCustomer: false,
  autoAddToAllForms: false,
  defaultValue: null,
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="phoneNoField"></a>
                    <span class="name">
                        <span ><b>phoneNoField</b></span>
                        <a href="#phoneNoField"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="phoneNoFieldBody"></a>
                    <span class="name">
                        <span ><b>phoneNoFieldBody</b></span>
                        <a href="#phoneNoFieldBody"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="../classes/CreateCustomFieldDto.html" target="_self" >CreateCustomFieldDto</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  name: &quot;Phone Number field&quot;,
  fieldType: CustomFieldType.PHONE_NUMBER,
  source: CustomFieldSource.TICKET,
  placeholderText: &quot;Enter your phone number&quot;,
  hintText: &quot;Enter your phone number&quot;,
  mandatoryOnCreation: false,
  mandatoryOnClose: false,
  visibleToCustomer: false,
  editableByCustomer: false,
  autoAddToAllForms: false,
  defaultValue: null,
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="simpleTextField"></a>
                    <span class="name">
                        <span ><b>simpleTextField</b></span>
                        <a href="#simpleTextField"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="simpleTextFieldBody"></a>
                    <span class="name">
                        <span ><b>simpleTextFieldBody</b></span>
                        <a href="#simpleTextFieldBody"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="../classes/CreateCustomFieldDto.html" target="_self" >CreateCustomFieldDto</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  name: &quot;Simple Text field&quot;,
  fieldType: CustomFieldType.SINGLE_LINE,
  source: CustomFieldSource.TICKET,
  placeholderText: &quot;Enter your text&quot;,
  hintText: &quot;Enter your text&quot;,
  mandatoryOnCreation: false,
  mandatoryOnClose: false,
  visibleToCustomer: false,
  editableByCustomer: false,
  autoAddToAllForms: false,
  defaultValue: null,
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="singleChoiceField"></a>
                    <span class="name">
                        <span ><b>singleChoiceField</b></span>
                        <a href="#singleChoiceField"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="singleChoiceFieldBody"></a>
                    <span class="name">
                        <span ><b>singleChoiceFieldBody</b></span>
                        <a href="#singleChoiceFieldBody"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="../classes/CreateCustomFieldDto.html" target="_self" >CreateCustomFieldDto</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  name: &quot;Single Choice field&quot;,
  fieldType: CustomFieldType.SINGLE_CHOICE,
  source: CustomFieldSource.TICKET,
  options: [
    {
      value: &quot;option-1&quot;,
      is_disabled: false,
      order: 1,
      id: &quot;1&quot;,
      platformField: true,
    },
    {
      value: &quot;option-2&quot;,
      is_disabled: false,
      order: 2,
      id: &quot;2&quot;,
      platformField: true,
    },
    {
      value: &quot;option-3&quot;,
      is_disabled: false,
      order: 3,
      id: &quot;3&quot;,
      platformField: true,
    },
  ],
  placeholderText: &quot;Select an option&quot;,
  hintText: &quot;Select an option&quot;,
  mandatoryOnCreation: false,
  mandatoryOnClose: false,
  visibleToCustomer: false,
  editableByCustomer: false,
  autoAddToAllForms: false,
  defaultValue: null,
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/organization/validators/index.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createOrganizationSchema"></a>
                    <span class="name">
                        <span ><b>createOrganizationSchema</b></span>
                        <a href="#createOrganizationSchema"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>z.object({
  name: z.string().min(3).max(255),
  email: z.string().email(),
  password: z.string().min(8).max(255),
  logoUrl: z.string().optional(),
})</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateOrganizationSchema"></a>
                    <span class="name">
                        <span ><b>updateOrganizationSchema</b></span>
                        <a href="#updateOrganizationSchema"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>z.object({
  name: z.string().min(3).max(255).optional(),
  logoUrl: z.string().optional(),
})</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/tickets/controllers/grpc/validators/index.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createTicketValidator"></a>
                    <span class="name">
                        <span ><b>createTicketValidator</b></span>
                        <a href="#createTicketValidator"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>z.object({
  title: z.string(),
  requestorEmail: z.string().email(),
  teamId: z.string(),
  accountId: z.string().optional(),
  assignedAgentId: z.string().optional(),
  description: z.string().optional(),
  dueDate: z.string().optional(),
  submitterEmail: z.string().optional(),
  statusId: z.string().optional(),
  priorityId: z.string().optional(),
  typeId: z.string().optional(),
  isPrivate: z.boolean().optional(),
  attachmentUrls: z.array(z.string()).optional(),
})</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateTicketValidator"></a>
                    <span class="name">
                        <span ><b>updateTicketValidator</b></span>
                        <a href="#updateTicketValidator"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>z.object({
  title: z.string().optional(),
  teamId: z.string().optional(),
  accountId: z.string().optional(),
  assignedAgentId: z.string().optional(),
  description: z.string().optional(),
  dueDate: z.string().optional(),
  submitterEmail: z.string().optional(),
  statusId: z.string().optional(),
  priorityId: z.string().optional(),
  typeId: z.string().optional(),
  isPrivate: z.boolean().optional(),
  attachmentUrls: z.array(z.string()).optional(),
})</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/common/decorators/user.decorator.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="CurrentUser"></a>
                    <span class="name">
                        <span ><b>CurrentUser</b></span>
                        <a href="#CurrentUser"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>createParamDecorator(
  (_data: unknown, context: ExecutionContext) &#x3D;&gt; {
    const request &#x3D; context.switchToHttp().getRequest&lt;FastifyRequest&gt;();

    // If the user is not authenticated, throw an error
    if (!request.user) {
      throw new UnauthorizedException();
    }

    return request.user as CurrentUser;
  },
)</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/custom-field/constants/constants.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="CustomFieldValidatorConstants"></a>
                    <span class="name">
                        <span ><b>CustomFieldValidatorConstants</b></span>
                        <a href="#CustomFieldValidatorConstants"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{}</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/tickets/utils/tickets.constants.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="EAGER_TICKET_RELATIONS"></a>
                    <span class="name">
                        <span ><b>EAGER_TICKET_RELATIONS</b></span>
                        <a href="#EAGER_TICKET_RELATIONS"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[
  &quot;account&quot;,
  &quot;status&quot;,
  &quot;priority&quot;,
  &quot;assignedAgent&quot;,
  &quot;team&quot;,
  &quot;parentTeam&quot;,
  &quot;type&quot;,
  &quot;form&quot;,
  &quot;customFieldValues&quot;,
  &quot;customFieldValues.customField&quot;,
  &quot;attachments&quot;,
  &quot;organization&quot;,
]</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="TICKET_SNS_PUBLISHER"></a>
                    <span class="name">
                        <span ><b>TICKET_SNS_PUBLISHER</b></span>
                        <a href="#TICKET_SNS_PUBLISHER"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&quot;TICKET_SNS_PUBLISHER&quot;</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/views/constants/views.constants.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="EAGERLY_FETCH_RELATIONS"></a>
                    <span class="name">
                        <span ><b>EAGERLY_FETCH_RELATIONS</b></span>
                        <a href="#EAGERLY_FETCH_RELATIONS"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[&quot;viewsType&quot;, &quot;owner&quot;, &quot;team&quot;]</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/teams/constants/teams.constants.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="EAGERLY_LOADED_RELATIONS"></a>
                    <span class="name">
                        <span ><b>EAGERLY_LOADED_RELATIONS</b></span>
                        <a href="#EAGERLY_LOADED_RELATIONS"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[&quot;teamOwner&quot;, &quot;parentTeam&quot;]</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="TEAM_ROUTING_RULES_RELATIONS"></a>
                    <span class="name">
                        <span ><b>TEAM_ROUTING_RULES_RELATIONS</b></span>
                        <a href="#TEAM_ROUTING_RULES_RELATIONS"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[&quot;team&quot;, &quot;createdBy&quot;]</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/teams/constants/team-members.constants.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="EAGERLY_LOADED_RELATIONS_FOR_TEAM_MEMBERS"></a>
                    <span class="name">
                        <span ><b>EAGERLY_LOADED_RELATIONS_FOR_TEAM_MEMBERS</b></span>
                        <a href="#EAGERLY_LOADED_RELATIONS_FOR_TEAM_MEMBERS"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[
  &quot;user&quot;,
  &quot;invitedBy&quot;,
  &quot;organization&quot;,
  &quot;team&quot;,
]</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/config/typesense/constants/embedding.constants.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="EMBEDDING_CONSTANTS"></a>
                    <span class="name">
                        <span ><b>EMBEDDING_CONSTANTS</b></span>
                        <a href="#EMBEDDING_CONSTANTS"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  DIMENSIONS: 1536,
  MAX_TEXT_LENGTH: 8000,
  WEIGHTS: {
    TITLE: 0.6,
    DESCRIPTION: 0.4,
  },
  THRESHOLDS: {
    DEFAULT: 0.7,
    MIN: 0.5,
    MAX: 0.95,
  },
  LIMITS: {
    DEFAULT: 5,
    MIN: 1,
    MAX: 100,
  },
} as const</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ticketEmbeddingsSchema"></a>
                    <span class="name">
                        <span ><b>ticketEmbeddingsSchema</b></span>
                        <a href="#ticketEmbeddingsSchema"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  name: &#x60;${TYPESENSE_CONSTANTS.COLLECTION_PREFIX}ticket_embeddings&#x60;,
  fields: [
    { name: &quot;ticket_id&quot;, type: &quot;string&quot; },
    {
      name: &quot;title_embedding&quot;,
      type: &quot;float[]&quot;,
      num_dim: EMBEDDING_CONSTANTS.DIMENSIONS,
    },
    {
      name: &quot;description_embedding&quot;,
      type: &quot;float[]&quot;,
      num_dim: EMBEDDING_CONSTANTS.DIMENSIONS,
    },
    {
      name: &quot;combined_embedding&quot;,
      type: &quot;float[]&quot;,
      num_dim: EMBEDDING_CONSTANTS.DIMENSIONS,
    },
    { name: &quot;team_id&quot;, type: &quot;string&quot;, facet: true },
    { name: &quot;organization_id&quot;, type: &quot;string&quot;, facet: true },
    { name: &quot;updated_at&quot;, type: &quot;int64&quot; },
  ],
  default_sorting_field: &quot;updated_at&quot;,
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/organization/events/organization.events.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="EmittableOrganizationEvents"></a>
                    <span class="name">
                        <span ><b>EmittableOrganizationEvents</b></span>
                        <a href="#EmittableOrganizationEvents"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  ORGANIZATION_CREATED: &quot;organization.created&quot;,
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/tickets/events/tickets.events.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="EmittableTicketEvents"></a>
                    <span class="name">
                        <span ><b>EmittableTicketEvents</b></span>
                        <a href="#EmittableTicketEvents"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  TICKET_ASSIGNED: &quot;ticket.assigned&quot;,
  TICKET_UNASSIGNED: &quot;ticket.unassigned&quot;,
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/config/db.config.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="entities"></a>
                    <span class="name">
                        <span ><b>entities</b></span>
                        <a href="#entities"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[
  Account,
  AccountActivity,
  AccountAttributeValue,
  CustomerContact,
  AccountNote,
  AccountRelationship,
  AccountRelationshipType,
  AccountTask,
  AuditLog,
  BusinessHoursConfig,
  Comment,
  CustomField,
  CustomFieldValues,
  CustomObject,
  FormFieldEvents,
  Draft,
  Emojis,
  Mentions,
  Organization,
  OrganizationDomains,
  OrganizationInvitations,
  Reactions,
  Storage,
  Tag,
  Team,
  TeamCapacity,
  TeamConfiguration,
  TeamMember,
  TeamRoutingRules,
  Ticket,
  TicketPriority,
  TicketRelationships,
  TicketStatus,
  TicketTimeLog,
  TicketType,
  TimeOff,
  User,
  UserSkills,
  Views,
  ViewsType,
  ThenaRestrictedField,
  Form,
]</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getPlatformDBConfig"></a>
                    <span class="name">
                        <span ><b>getPlatformDBConfig</b></span>
                        <a href="#getPlatformDBConfig"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>(
  configService: ConfigService,
): TypeOrmModuleOptions &#x3D;&gt; {
  const environment &#x3D; configService.get(ConfigKeys.NODE_ENV);
  const isStaging &#x3D; environment &#x3D;&#x3D;&#x3D; &quot;staging&quot;;

  if (isStaging) {
    return {
      type: &quot;postgres&quot;,
      url: configService.get(ConfigKeys.SUPABASE_STAGING_DB_URL),
      entities,
      synchronize: false, // Disable synchronize in staging
      ssl: {
        rejectUnauthorized: false,
      },
      logging: configService.get(ConfigKeys.NODE_ENV) &#x3D;&#x3D;&#x3D; &quot;development&quot;,
      extra: {
        max: 20, // connection pool max size
        connectionTimeoutMillis: 10000, // 10 seconds
        idleTimeoutMillis: 60000, // 1 minute
      },
    };
  }

  // Development environments configuration
  return {
    type: &quot;postgres&quot;,
    host: configService.get(ConfigKeys.THENA_PLATFORM_DB_HOST),
    port: parseInt(configService.get(ConfigKeys.THENA_PLATFORM_DB_PORT)),
    username: configService.get(ConfigKeys.THENA_PLATFORM_DB_USER),
    password: configService.get(ConfigKeys.THENA_PLATFORM_DB_PASSWORD),
    database: configService.get(ConfigKeys.THENA_PLATFORM_DB_NAME),
    entities,
    synchronize: true,
    logging: true,
    extra: {
      max: 20, // connection pool max size
      connectionTimeoutMillis: 10000, // 10 seconds
      idleTimeoutMillis: 60000, // 1 minute
    },
  };
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/constants/error-codes.constants.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ERROR_CODES"></a>
                    <span class="name">
                        <span ><b>ERROR_CODES</b></span>
                        <a href="#ERROR_CODES"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  // Config
  CONFIG_VALIDATION_ERROR_CODE: &quot;PLT-CFG-001&quot;,

  // Workflows
  UNSUPPORTED_ENGINE_ADAPTER: &quot;PLT-WKF-001&quot;,
  FAILED_TO_SHUTDOWN_WORKFLOW_ENGINE: &quot;PLT-WKF-002&quot;,
  FAILED_TO_START_WORKER: &quot;PLT-WKF-003&quot;,
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/common/utils/extract-email-details.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="extractEmailDetails"></a>
                    <span class="name">
                        <span ><b>extractEmailDetails</b></span>
                        <a href="#extractEmailDetails"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>(email: string) &#x3D;&gt; {
  const { domain, ...rest } &#x3D; parse(email);
  return { domain, ...rest };
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="extractNameFromEmail"></a>
                    <span class="name">
                        <span ><b>extractNameFromEmail</b></span>
                        <a href="#extractNameFromEmail"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>(
  email: string,
): { firstName: string; lastName: string } &#x3D;&gt; {
  if (!email || typeof email !&#x3D;&#x3D; &quot;string&quot;) {
    return { firstName: &quot;&quot;, lastName: &quot;&quot; };
  }

  // Get the local part (before @)
  const localPart &#x3D; email.split(&quot;@&quot;)[0];

  if (!localPart) {
    return { firstName: &quot;&quot;, lastName: &quot;&quot; };
  }

  // Common separators in email names
  const separators &#x3D; [&quot;.&quot;, &quot;_&quot;, &quot;-&quot;];

  // Replace common separators with spaces
  let name &#x3D; localPart;
  separators.forEach((separator) &#x3D;&gt; {
    name &#x3D; name.replace(new RegExp(&quot;\\&quot; + separator, &quot;g&quot;), &quot; &quot;);
  });

  // Capitalize first letter of each word
  name &#x3D; name
    .toLowerCase()
    .split(&quot; &quot;)
    .map((word) &#x3D;&gt; word.charAt(0).toUpperCase() + word.slice(1))
    .join(&quot; &quot;);

  // Handle special cases
  name &#x3D; name
    .replace(/\d+/g, &quot;&quot;) // Remove numbers
    .replace(/\s+/g, &quot; &quot;) // Remove extra spaces
    .trim();

  const firstName &#x3D; name.split(&quot; &quot;)[0];
  const lastName &#x3D; name.split(&quot; &quot;).slice(1).join(&quot; &quot;);

  return { firstName, lastName };
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/custom-object/constants/index.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="GetObjectRelations"></a>
                    <span class="name">
                        <span ><b>GetObjectRelations</b></span>
                        <a href="#GetObjectRelations"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[&quot;team&quot;, &quot;organization&quot;]</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/tickets/constants/tickets.constants.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="GetTicketsRelations"></a>
                    <span class="name">
                        <span ><b>GetTicketsRelations</b></span>
                        <a href="#GetTicketsRelations"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[
  &quot;account&quot;,
  &quot;status&quot;,
  &quot;priority&quot;,
  &quot;assignedAgent&quot;,
  &quot;team&quot;,
  &quot;type&quot;,
  &quot;customFieldValues&quot;,
  &quot;customFieldValues.customField&quot;,
  &quot;organization&quot;,
  &quot;form&quot;,
]</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="TicketSelect"></a>
                    <span class="name">
                        <span ><b>TicketSelect</b></span>
                        <a href="#TicketSelect"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  id: true,
  uid: true,
  title: true,
  ticketId: true,
  deletedAt: true,
  archivedAt: true,
  createdAt: true,
  updatedAt: true,
  isPrivate: true,
  description: true,
  requestorEmail: true,
  submitterEmail: true,
  type: { uid: true, name: true },
  team: { uid: true, name: true },
  account: { uid: true, name: true },
  assignedAgent: { uid: true, name: true },
  status: { name: true, displayName: true },
  priority: { name: true, displayName: true },
  form: { uid: true, name: true },
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/config/typesense.config.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTypesenseConfig"></a>
                    <span class="name">
                        <span ><b>getTypesenseConfig</b></span>
                        <a href="#getTypesenseConfig"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>(configservice: ConfigService): Client &#x3D;&gt; {
  return new Client({
    nodes: [
      {
        host: configservice.get(ConfigKeys.TYPESENSE_HOST),
        port: parseInt(configservice.get(ConfigKeys.TYPESENSE_PORT)),
        protocol: configservice.get(ConfigKeys.TYPESENSE_PROTOCOL),
      },
    ],
    apiKey: configservice.get(ConfigKeys.TYPESENSE_API_KEY),
    connectionTimeoutSeconds: TYPESENSE_CONSTANTS.CONNECTION_TIMEOUT,
  });
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/grpc.server.config.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="grpcServerConfig"></a>
                    <span class="name">
                        <span ><b>grpcServerConfig</b></span>
                        <a href="#grpcServerConfig"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>MicroserviceOptions</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  transport: Transport.GRPC,
  options: {
    url: &quot;0.0.0.0:50051&quot;,
    loader: { keepCase: false },
    package: [
      accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      communication.GRPC_COMMUNICATION_V1_PACKAGE_NAME,
      health.GRPC_HEALTH_V1_PACKAGE_NAME,
      tickets.GRPC_TICKETS_V1_PACKAGE_NAME,
      users.GRPC_USERS_V1_PACKAGE_NAME,
      organization.GRPC_ORGANIZATION_V1_PACKAGE_NAME,
      teams.GRPC_TEAMS_V1_PACKAGE_NAME,
      tags.GRPC_TAGS_V1_PACKAGE_NAME,
    ],
    protoPath: [
      // Health
      resolve(
        require.resolve(&quot;@repo/shared-proto/dist/proto/health/health.proto&quot;),
      ),

      // Tickets
      resolve(
        require.resolve(&quot;@repo/shared-proto/dist/proto/tickets/tickets.proto&quot;),
      ),

      // Users
      resolve(
        require.resolve(&quot;@repo/shared-proto/dist/proto/users/users.proto&quot;),
      ),

      // Accounts
      resolve(
        require.resolve(
          &quot;@repo/shared-proto/dist/proto/accounts/accounts.proto&quot;,
        ),
      ),

      // Tags
      resolve(require.resolve(&quot;@repo/shared-proto/dist/proto/tags/tags.proto&quot;)),

      // Organization
      resolve(
        require.resolve(
          &quot;@repo/shared-proto/dist/proto/organization/organization.proto&quot;,
        ),
      ),

      // Teams
      resolve(
        require.resolve(&quot;@repo/shared-proto/dist/proto/teams/teams.proto&quot;),
      ),

      // Communication
      resolve(
        require.resolve(
          &quot;@repo/shared-proto/dist/proto/communication/communication.proto&quot;,
        ),
      ),
    ],
    onLoadPackageDefinition: (pkg, server) &#x3D;&gt; {
      new ReflectionService(pkg).addToServer(server);
    },
  },
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/utils/test-utils/index.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="injectWithOrgId"></a>
                    <span class="name">
                        <span ><b>injectWithOrgId</b></span>
                        <a href="#injectWithOrgId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>(
  app: NestFastifyApplication,
  options: {
    method: string;
    url: string;
    headers?: Record&lt;string, string&gt;;
    payload?: any;
    query?: Record&lt;string, string&gt;;
    orgId?: string;
  },
): Promise&lt;any&gt; &#x3D;&gt; {
  const { headers &#x3D; {}, orgId, ...restOptions } &#x3D; options;
  return app.inject({
    ...restOptions,
    headers: {
      ...headers,
      &quot;x-org-id&quot;: orgId ?? global.testOrganization.uid,
    },
  } as InjectOptions);
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/common/validators/date.validators.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="IS_DATE_DDMM"></a>
                    <span class="name">
                        <span ><b>IS_DATE_DDMM</b></span>
                        <a href="#IS_DATE_DDMM"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&quot;isDateDDMM&quot;</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="IS_DATE_DDMM_OR_DDMMYYYY"></a>
                    <span class="name">
                        <span ><b>IS_DATE_DDMM_OR_DDMMYYYY</b></span>
                        <a href="#IS_DATE_DDMM_OR_DDMMYYYY"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&quot;isDateDDMMOrDDMMYYYY&quot;</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="IS_DATE_DDMMYYYY"></a>
                    <span class="name">
                        <span ><b>IS_DATE_DDMMYYYY</b></span>
                        <a href="#IS_DATE_DDMMYYYY"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&quot;isDateDDMMYYYY&quot;</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/auth/decorators/auth.decorator.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="IS_PUBLIC_KEY"></a>
                    <span class="name">
                        <span ><b>IS_PUBLIC_KEY</b></span>
                        <a href="#IS_PUBLIC_KEY"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&quot;isPublic&quot;</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="Public"></a>
                    <span class="name">
                        <span ><b>Public</b></span>
                        <a href="#Public"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>() &#x3D;&gt; SetMetadata(IS_PUBLIC_KEY, true)</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/common/validators/time.validators.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="IS_TIMEZONE_OR_EMPTY"></a>
                    <span class="name">
                        <span ><b>IS_TIMEZONE_OR_EMPTY</b></span>
                        <a href="#IS_TIMEZONE_OR_EMPTY"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&quot;isTimezoneOrEmpty&quot;</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/custom-field/utils/helper.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isValidDate"></a>
                    <span class="name">
                        <span ><b>isValidDate</b></span>
                        <a href="#isValidDate"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>(value: any): boolean &#x3D;&gt; {
  const date &#x3D; new Date(value);
  return (
    !Number.isNaN(date.getTime()) &amp;&amp;
    date.getHours() &#x3D;&#x3D;&#x3D; 0 &amp;&amp;
    date.getMinutes() &#x3D;&#x3D;&#x3D; 0 &amp;&amp;
    date.getSeconds() &#x3D;&#x3D;&#x3D; 0 &amp;&amp;
    date.getMilliseconds() &#x3D;&#x3D;&#x3D; 0
  );
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isValidDateTime"></a>
                    <span class="name">
                        <span ><b>isValidDateTime</b></span>
                        <a href="#isValidDateTime"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>(value: any): boolean &#x3D;&gt; {
  const dateTime &#x3D; new Date(value);
  return !Number.isNaN(dateTime.getTime());
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isValidEmail"></a>
                    <span class="name">
                        <span ><b>isValidEmail</b></span>
                        <a href="#isValidEmail"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>(value: string) &#x3D;&gt; {
  if (!isValidString(value)) return false;

  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isValidNumber"></a>
                    <span class="name">
                        <span ><b>isValidNumber</b></span>
                        <a href="#isValidNumber"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>(value: any): boolean &#x3D;&gt; {
  return !Number.isNaN(parseFloat(value)) &amp;&amp; Number.isFinite(value);
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isValidPassword"></a>
                    <span class="name">
                        <span ><b>isValidPassword</b></span>
                        <a href="#isValidPassword"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>(value: string): boolean &#x3D;&gt; {
  const passwordRegex &#x3D;
    /^(?&#x3D;.*[a-z])(?&#x3D;.*[A-Z])(?&#x3D;.*\d)(?&#x3D;.*[@$!%*?&amp;])[A-Za-z\d@$!%*?&amp;]{8,}$/;
  return passwordRegex.test(value);
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isValidPhoneNumber"></a>
                    <span class="name">
                        <span ><b>isValidPhoneNumber</b></span>
                        <a href="#isValidPhoneNumber"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>(value: string): boolean &#x3D;&gt; {
  // Phone number validation rules
  const phoneRegex &#x3D; /^(\+\d{1,2}\s?)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$/;
  return phoneRegex.test(value);
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isValidRegex"></a>
                    <span class="name">
                        <span ><b>isValidRegex</b></span>
                        <a href="#isValidRegex"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>(value: string): boolean &#x3D;&gt; {
  try {
    // eslint-disable-next-line no-new
    new RegExp(value);
    return true;
  } catch (e) {
    Logger.error(e);
    return false;
  }
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isValidString"></a>
                    <span class="name">
                        <span ><b>isValidString</b></span>
                        <a href="#isValidString"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>(value: string, trim &#x3D; true) &#x3D;&gt; {
  if (!value || typeof value !&#x3D;&#x3D; &quot;string&quot;) return false;

  if (trim) value &#x3D; value.trim();

  return value.length &gt; 0;
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isValidUrl"></a>
                    <span class="name">
                        <span ><b>isValidUrl</b></span>
                        <a href="#isValidUrl"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>(string: string): boolean &#x3D;&gt; {
  try {
    // eslint-disable-next-line no-new
    new URL(string);
    return true;
  } catch (err) {
    Logger.error(err);
    return false;
  }
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/auth/constants.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="jwtConstants"></a>
                    <span class="name">
                        <span ><b>jwtConstants</b></span>
                        <a href="#jwtConstants"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  secret:
    &quot;this-is-a-secret-and-i-know-it-should-not-be-hardcoded-but-this-is-a-test-so-yeah&quot;,
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/teams/controllers/teams.controller.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="LOG_SPAN_ID"></a>
                    <span class="name">
                        <span ><b>LOG_SPAN_ID</b></span>
                        <a href="#LOG_SPAN_ID"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&quot;TEAMS_CONTROLLER&quot;</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/organization/sagas/create-org-and-admin.saga.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="noop"></a>
                    <span class="name">
                        <span ><b>noop</b></span>
                        <a href="#noop"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>() &#x3D;&gt; Promise.resolve()</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/common/workflows/constants/organization-response.schema.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ORGANIZATION_RESPONSE_SCHEMA"></a>
                    <span class="name">
                        <span ><b>ORGANIZATION_RESPONSE_SCHEMA</b></span>
                        <a href="#ORGANIZATION_RESPONSE_SCHEMA"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  type: &quot;object&quot;,
  properties: {
    id: { type: &quot;string&quot; },
    orgId: { type: &quot;string&quot; },
    name: { type: &quot;string&quot; },
    logoUrl: { type: [&quot;string&quot;, &quot;null&quot;] },
    isVerified: { type: &quot;boolean&quot; },
    isActive: { type: &quot;boolean&quot; },
    createdAt: { type: &quot;string&quot; },
    updatedAt: { type: &quot;string&quot; },
  },
  required: [&quot;id&quot;, &quot;orgId&quot;, &quot;name&quot;, &quot;createdAt&quot;, &quot;updatedAt&quot;],
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/common/constants/test.constants.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="POSTGRES_CONFIG"></a>
                    <span class="name">
                        <span ><b>POSTGRES_CONFIG</b></span>
                        <a href="#POSTGRES_CONFIG"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  type: &quot;postgres&quot;,
  host: &quot;localhost&quot;,
  port: 54332,
  username: &quot;postgres&quot;,
  password: &quot;postgres&quot;,
  database: &quot;thena_platform_integration_tests&quot;,
} as const</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/common/constants/postgres-errors.constants.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="POSTGRES_ERROR_CODES"></a>
                    <span class="name">
                        <span ><b>POSTGRES_ERROR_CODES</b></span>
                        <a href="#POSTGRES_ERROR_CODES"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  INVALID_TEXT_REPRESENTATION: &quot;22P02&quot;,
  DUPLICATE_KEY_VALUE: &quot;23505&quot;,
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/shared/constants/tickets.constants.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="priorities"></a>
                    <span class="name">
                        <span ><b>priorities</b></span>
                        <a href="#priorities"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[
  {
    name: &quot;Low&quot;,
    description: &quot;Low priority tickets.&quot;,
    default: false,
  },
  {
    name: &quot;Medium&quot;,
    description: &quot;Medium priority tickets.&quot;,
    default: true,
  },
  {
    name: &quot;High&quot;,
    description: &quot;High priority tickets.&quot;,
    default: false,
  },
  {
    name: &quot;Urgent&quot;,
    description: &quot;Urgent priority tickets.&quot;,
    default: false,
  },
]</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="statuses"></a>
                    <span class="name">
                        <span ><b>statuses</b></span>
                        <a href="#statuses"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[
  {
    name: &quot;Open&quot;,
    description: &quot;Tickets that are open and awaiting prioritization.&quot;,
    default: true,
    order: 0,
  },
  {
    name: &quot;In Progress&quot;,
    description: &quot;Tickets that are currently in progress and being worked on.&quot;,
    default: false,
    order: 1,
  },
  {
    name: &quot;On Hold&quot;,
    description: &quot;Tickets that are currently on hold.&quot;,
    default: false,
    order: 2,
  },
  {
    name: &quot;Closed&quot;,
    description: &quot;Tickets that are closed or resolved.&quot;,
    default: false,
    order: 3,
  },
]</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="types"></a>
                    <span class="name">
                        <span ><b>types</b></span>
                        <a href="#types"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[
  {
    name: &quot;Bug&quot;,
    description: &quot;A bug is an error or issue in the code.&quot;,
    default: false,
    icon: &quot;&quot;,
    color: &quot;#db1f61&quot;,
  },
  {
    name: &quot;Feature Request&quot;,
    description: &quot;A feature request is a request for a new feature.&quot;,
    default: false,
    icon: &quot;&quot;,
    color: &quot;#1f74db&quot;,
  },
  {
    name: &quot;Question&quot;,
    description: &quot;A question is a request for information.&quot;,
    default: false,
    icon: &quot;&quot;,
    color: &quot;#1fdb8a&quot;,
  },
  {
    name: &quot;Task&quot;,
    description: &quot;A task is a request for a new task.&quot;,
    default: false,
    icon: &quot;&quot;,
    color: &quot;#db7a1f&quot;,
  },
]</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/constants/queue.constants.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="QueueNames"></a>
                    <span class="name">
                        <span ><b>QueueNames</b></span>
                        <a href="#QueueNames"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  TICKET_SNS_PUBLISHER: &quot;ticket-sns-publisher&quot;,
  ACCOUNTS_SNS_PUBLISHER: &quot;accounts-sns-publisher&quot;,
  COMMENT_SNS_PUBLISHER: &quot;comment-sns-publisher&quot;,
  ORGANIZATION_SNS_PUBLISHER: &quot;organization-sns-publisher&quot;,
} as const</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/tickets/routing/providers/request-router.provider.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="REQUEST_ROUTER_ENGINE"></a>
                    <span class="name">
                        <span ><b>REQUEST_ROUTER_ENGINE</b></span>
                        <a href="#REQUEST_ROUTER_ENGINE"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&quot;REQUEST_ROUTER_ENGINE&quot;</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="RequestRouterProvider"></a>
                    <span class="name">
                        <span ><b>RequestRouterProvider</b></span>
                        <a href="#RequestRouterProvider"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>Provider</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  provide: REQUEST_ROUTER_ENGINE,
  useClass: ThenaRequestRouterEngine,
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/users/constants/users.dbconstants.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="SELECT_FROM_USERS"></a>
                    <span class="name">
                        <span ><b>SELECT_FROM_USERS</b></span>
                        <a href="#SELECT_FROM_USERS"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>FindOptionsSelect&lt;User&gt;</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  uid: true,
  name: true,
  email: true,
  userType: true,
  isActive: true,
  status: true,
  lastLoginAt: true,
  avatarUrl: true,
  timezone: true,
  createdAt: true,
  updatedAt: true,
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>__mocks__/aws/sqs.mock.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="SQSConsumerService"></a>
                    <span class="name">
                        <span ><b>SQSConsumerService</b></span>
                        <a href="#SQSConsumerService"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>jest.fn().mockImplementation(() &#x3D;&gt; ({
  start: jest.fn(),
  stop: jest.fn(),
  onModuleInit: jest.fn(),
  onModuleDestroy: jest.fn(),
  initializeSQSClient: jest.fn(),
  receiveMessages: jest.fn().mockResolvedValue([]),
  deleteMessage: jest.fn().mockResolvedValue({}),
  startConsumer: jest.fn(),
  stopConsumer: jest.fn(),
  poll: jest.fn(),
  processMessage: jest.fn(),
}))</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="SQSModule"></a>
                    <span class="name">
                        <span ><b>SQSModule</b></span>
                        <a href="#SQSModule"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  Producer: jest.fn().mockReturnValue({
    module: class {},
    providers: [],
  }),
  Consumer: jest.fn().mockReturnValue({
    module: class {},
    providers: [],
  }),
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="SQSProducerService"></a>
                    <span class="name">
                        <span ><b>SQSProducerService</b></span>
                        <a href="#SQSProducerService"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>jest.fn().mockImplementation(() &#x3D;&gt; ({
  sendMessage: jest.fn().mockResolvedValue({}),
}))</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/teams/teams.constants.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="TEAM_ROLE_MEMBER"></a>
                    <span class="name">
                        <span ><b>TEAM_ROLE_MEMBER</b></span>
                        <a href="#TEAM_ROLE_MEMBER"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&quot;member&quot;</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="TEAM_ROLE_OWNER"></a>
                    <span class="name">
                        <span ><b>TEAM_ROLE_OWNER</b></span>
                        <a href="#TEAM_ROLE_OWNER"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&quot;owner&quot;</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/forms/constants/index.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="THENA_RESTRICTED_FIELDS"></a>
                    <span class="name">
                        <span ><b>THENA_RESTRICTED_FIELDS</b></span>
                        <a href="#THENA_RESTRICTED_FIELDS"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  STATUS: &quot;status&quot;,
  ASSIGNEE: &quot;assignee&quot;,
  REQUESTOR: &quot;requestor&quot;,
  TITLE: &quot;title&quot;,
  DESCRIPTION: &quot;description&quot;,
  PRIORITY: &quot;priority&quot;,
  ACCOUNT: &quot;account&quot;,
  SUBMITTER: &quot;submitter&quot;,
  TEAM: &quot;team&quot;,
  TYPE: &quot;type&quot;,
}</code>
                    </td>
                </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="UpdatableThenaRestrictedFieldsProperties"></a>
                    <span class="name">
                        <span ><b>UpdatableThenaRestrictedFieldsProperties</b></span>
                        <a href="#UpdatableThenaRestrictedFieldsProperties"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[
  &quot;mandatoryOnCreation&quot;,
  &quot;mandatoryOnClose&quot;,
  &quot;visibleToCustomer&quot;,
  &quot;editableByCustomer&quot;,
]</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>
    <h3>src/config/typesense/constants/typesense.constants.ts</h3>
    <section data-compodoc="block-properties">
    <h3></h3>    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="TYPESENSE_CONSTANTS"></a>
                    <span class="name">
                        <span ><b>TYPESENSE_CONSTANTS</b></span>
                        <a href="#TYPESENSE_CONSTANTS"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
  COLLECTION_NAME: &quot;tickets&quot;,
  CONNECTION_TIMEOUT: 2,
  DEFAULT_PER_PAGE: 20,
  DEFAULT_SORT_BY: &quot;createdAt:desc&quot;,
  COLLECTION_PREFIX: {
    DEVELOPMENT: &quot;dev_&quot;,
    STAGING: &quot;stg_&quot;,
    PRODUCTION: &quot;prod_&quot;,
  },
} as const</code>
                    </td>
                </tr>


        </tbody>
    </table>
</section>



                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'miscellaneous-variables';
            var COMPODOC_CURRENT_PAGE_URL = 'variables.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
