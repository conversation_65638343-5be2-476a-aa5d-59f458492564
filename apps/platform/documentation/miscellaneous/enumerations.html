<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content miscellaneous-enumerations">
                   <div class="content-data">


















<ol class="breadcrumb">
  <li class="breadcrumb-item">Miscellaneous</li>
  <li class="breadcrumb-item">Enumerations</li>
</ol>

<section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <ul class="index-list">
                        <li>
                            <a href="#CommentEvents" title="src/communications/events/comment-events.ts" ><b>CommentEvents</b>&nbsp;&nbsp;&nbsp;(src/.../comment-events.ts)</a>
                        </li>
                        <li>
                            <a href="#CommentOp" title="src/communications/utils/index.ts" ><b>CommentOp</b>&nbsp;&nbsp;&nbsp;(src/.../index.ts)</a>
                        </li>
                        <li>
                            <a href="#ConfigKeys" title="src/config/config.service.ts" ><b>ConfigKeys</b>&nbsp;&nbsp;&nbsp;(src/.../config.service.ts)</a>
                        </li>
                        <li>
                            <a href="#EmittableAccountEvents" title="src/accounts/events/accounts.events.ts" ><b>EmittableAccountEvents</b>&nbsp;&nbsp;&nbsp;(src/.../accounts.events.ts)</a>
                        </li>
                        <li>
                            <a href="#RoutingRuleMatchingCondition" title="src/teams/dto/team-routing.dto.ts" ><b>RoutingRuleMatchingCondition</b>&nbsp;&nbsp;&nbsp;(src/.../team-routing.dto.ts)</a>
                        </li>
                        <li>
                            <a href="#SlaEntityType" title="src/tickets/interfaces/sla.interface.ts" ><b>SlaEntityType</b>&nbsp;&nbsp;&nbsp;(src/.../sla.interface.ts)</a>
                        </li>
                        <li>
                            <a href="#SLAJobStatus" title="src/tickets/interfaces/sla.interface.ts" ><b>SLAJobStatus</b>&nbsp;&nbsp;&nbsp;(src/.../sla.interface.ts)</a>
                        </li>
                        <li>
                            <a href="#SLAMetricType" title="src/tickets/interfaces/sla.interface.ts" ><b>SLAMetricType</b>&nbsp;&nbsp;&nbsp;(src/.../sla.interface.ts)</a>
                        </li>
                        <li>
                            <a href="#SLAMetricType" title="src/tickets/interfaces/sla.interface.ts" ><b>SLAMetricType</b>&nbsp;&nbsp;&nbsp;(src/.../sla.interface.ts)</a>
                        </li>
                        <li>
                            <a href="#TicketRoutingEvent" title="src/tickets/routing/providers/thena-request-router/engine/thena-request-router.engine.ts" ><b>TicketRoutingEvent</b>&nbsp;&nbsp;&nbsp;(src/.../thena-request-router.engine.ts)</a>
                        </li>
                        <li>
                            <a href="#TicketRoutingState" title="src/tickets/routing/providers/thena-request-router/engine/thena-request-router.engine.ts" ><b>TicketRoutingState</b>&nbsp;&nbsp;&nbsp;(src/.../thena-request-router.engine.ts)</a>
                        </li>
                    </ul>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    <h3>src/communications/events/comment-events.ts</h3>
    <section data-compodoc="block-enums">
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="CommentEvents"></a>
                        <span class="name "><b>CommentEvents</b><a href="#CommentEvents"><span class="icon ion-ios-link"></span></a></span>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;COMMENT_CREATED
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>comment.created</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;COMMENT_UPDATED
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>comment.updated</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;COMMENT_DELETED
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>comment.deleted</code>
                            </td>
                        </tr>
            </tbody>
        </table>
</section>
    <h3>src/communications/utils/index.ts</h3>
    <section data-compodoc="block-enums">
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="CommentOp"></a>
                        <span class="name "><b>CommentOp</b><a href="#CommentOp"><span class="icon ion-ios-link"></span></a></span>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;CREATED
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>created</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;UPDATED
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>updated</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;DELETED
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>deleted</code>
                            </td>
                        </tr>
            </tbody>
        </table>
</section>
    <h3>src/config/config.service.ts</h3>
    <section data-compodoc="block-enums">
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="ConfigKeys"></a>
                        <span class="name "><b>ConfigKeys</b><a href="#ConfigKeys"><span class="icon ion-ios-link"></span></a></span>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;APP_TAG
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>APP_TAG</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;SERVICE_TAG
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>SERVICE_TAG</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;NODE_ENV
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>NODE_ENV</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;JWT_SECRET
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>JWT_SECRET</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;THENA_PLATFORM_DB_HOST
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>THENA_PLATFORM_DB_HOST</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;THENA_PLATFORM_DB_PORT
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>THENA_PLATFORM_DB_PORT</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;THENA_PLATFORM_DB_NAME
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>THENA_PLATFORM_DB_NAME</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;THENA_PLATFORM_DB_USER
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>THENA_PLATFORM_DB_USER</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;THENA_PLATFORM_DB_PASSWORD
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>THENA_PLATFORM_DB_PASSWORD</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;SUPABASE_STAGING_DB_URL
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>SUPABASE_STAGING_DB_URL</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;REDIS_HOST
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>REDIS_HOST</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;REDIS_PORT
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>REDIS_PORT</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;REDIS_USERNAME
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>REDIS_USERNAME</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;REDIS_PASSWORD
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>REDIS_PASSWORD</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;AWS_ACCESS_KEY
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>AWS_ACCESS_KEY</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;AWS_SECRET_KEY
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>AWS_SECRET_KEY</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;AWS_REGION
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>AWS_REGION</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;AWS_SQS_QUEUE_URL
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>AWS_SQS_QUEUE_URL</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;AWS_SQS_EMBEDDING_QUEUE_URL
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>AWS_SQS_EMBEDDING_QUEUE_URL</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;AWS_SQS_SLA_QUEUE_URL
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>AWS_SQS_SLA_QUEUE_URL</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;AWS_SQS_SLA_CONSUMER_QUEUE_URL
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>AWS_SQS_SLA_CONSUMER_QUEUE_URL</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;AWS_SNS_TICKET_TOPIC_ARN
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>AWS_SNS_TICKET_TOPIC_ARN</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;AWS_SNS_ACCOUNTS_TOPIC_ARN
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>AWS_SNS_ACCOUNTS_TOPIC_ARN</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;AWS_SNS_ORGANIZATION_TOPIC_ARN
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>AWS_SNS_ORGANIZATION_TOPIC_ARN</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;TYPESENSE_HOST
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>TYPESENSE_HOST</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;TYPESENSE_PORT
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>TYPESENSE_PORT</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;TYPESENSE_PROTOCOL
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>TYPESENSE_PROTOCOL</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;TYPESENSE_API_KEY
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>TYPESENSE_API_KEY</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;TYPESENSE_ADMIN_API_KEY
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>TYPESENSE_ADMIN_API_KEY</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;TYPESENSE_TIMEOUT
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>TYPESENSE_TIMEOUT</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;SENTRY_DSN
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>SENTRY_DSN</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;TICKET_BUCKET
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>TICKET_BUCKET</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;PLATFORM_GRPC_URL
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>PLATFORM_GRPC_URL</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;WORKFLOWS_GRPC_URL
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>WORKFLOWS_GRPC_URL</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;AUTH_GRPC_URL
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>AUTH_GRPC_URL</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;BULL_ADMIN_USER
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>BULL_ADMIN_USER</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;BULL_ADMIN_PASSWORD
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>BULL_ADMIN_PASSWORD</code>
                            </td>
                        </tr>
            </tbody>
        </table>
</section>
    <h3>src/accounts/events/accounts.events.ts</h3>
    <section data-compodoc="block-enums">
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="EmittableAccountEvents"></a>
                        <span class="name "><b>EmittableAccountEvents</b><a href="#EmittableAccountEvents"><span class="icon ion-ios-link"></span></a></span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <div class="io-description"><p>The local events are emitted to the nest js event emitter by the account service
The SNS events are emitted to the SNS topic by the account service</p>
</div>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;ACCOUNT_ATTRIBUTE_VALUE_DELETED
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>account.attribute.value.deleted</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;LINK_CONTACTS_TO_ACCOUNT_BY_EMAIL_DOMAIN
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>account.contacts.link.by.email.domain</code>
                            </td>
                        </tr>
            </tbody>
        </table>
</section>
    <h3>src/teams/dto/team-routing.dto.ts</h3>
    <section data-compodoc="block-enums">
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="RoutingRuleMatchingCondition"></a>
                        <span class="name "><b>RoutingRuleMatchingCondition</b><a href="#RoutingRuleMatchingCondition"><span class="icon ion-ios-link"></span></a></span>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;ANY
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>any</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;ALL
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>all</code>
                            </td>
                        </tr>
            </tbody>
        </table>
</section>
    <h3>src/tickets/interfaces/sla.interface.ts</h3>
    <section data-compodoc="block-enums">
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="SlaEntityType"></a>
                        <span class="name "><b>SlaEntityType</b><a href="#SlaEntityType"><span class="icon ion-ios-link"></span></a></span>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;TICKET
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>ticket</code>
                            </td>
                        </tr>
            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="SLAJobStatus"></a>
                        <span class="name "><b>SLAJobStatus</b><a href="#SLAJobStatus"><span class="icon ion-ios-link"></span></a></span>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;SCHEDULED
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>scheduled</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;RUNNING
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>running</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;ACHIEVED
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>achieved</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;BREACHED
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>breached</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;CANCELLED
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>cancelled</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;PAUSED
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>paused</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;RESUMED
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>resumed</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;NOT_SCHEDULED
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>not_scheduled</code>
                            </td>
                        </tr>
            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="SLAMetricType"></a>
                        <span class="name "><b>SLAMetricType</b><a href="#SLAMetricType"><span class="icon ion-ios-link"></span></a></span>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;RESPONSE_TIME
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>response_time</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;RESOLUTION_TIME
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>resolution_time</code>
                            </td>
                        </tr>
            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="SLAMetricType"></a>
                        <span class="name "><b>SLAMetricType</b><a href="#SLAMetricType"><span class="icon ion-ios-link"></span></a></span>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;FIRST_TIME_RESPONSE
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>first_time_response</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;NEXT_TIME_RESPONSE
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>next_time_response</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;TOTAL_RESOLUTION_TIME
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>total_resolution_time</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;UPDATE_TIME
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>update_time</code>
                            </td>
                        </tr>
            </tbody>
        </table>
</section>
    <h3>src/tickets/routing/providers/thena-request-router/engine/thena-request-router.engine.ts</h3>
    <section data-compodoc="block-enums">
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="TicketRoutingEvent"></a>
                        <span class="name "><b>TicketRoutingEvent</b><a href="#TicketRoutingEvent"><span class="icon ion-ios-link"></span></a></span>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;EVALUATE_TEAM
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>EVALUATE_TEAM</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;ASSIGN_TEAM
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>ASSIGN_TEAM</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;EVALUATE_USER
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>EVALUATE_USER</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;ASSIGN_USER
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>ASSIGN_USER</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;FAIL
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>FAIL</code>
                            </td>
                        </tr>
            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="TicketRoutingState"></a>
                        <span class="name "><b>TicketRoutingState</b><a href="#TicketRoutingState"><span class="icon ion-ios-link"></span></a></span>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;INITIAL
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>INITIAL</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;TEAM_EVALUATED
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>TEAM_EVALUATED</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;TEAM_ASSIGNED
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>TEAM_ASSIGNED</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;USER_EVALUATED
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>USER_EVALUATED</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;USER_ASSIGNED
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>USER_ASSIGNED</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;FAILED
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>FAILED</code>
                            </td>
                        </tr>
            </tbody>
        </table>
</section>



                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'miscellaneous-enumerations';
            var COMPODOC_CURRENT_PAGE_URL = 'enumerations.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
