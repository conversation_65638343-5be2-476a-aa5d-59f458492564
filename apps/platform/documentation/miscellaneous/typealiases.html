<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content miscellaneous-typealiases">
                   <div class="content-data">


















<ol class="breadcrumb">
  <li class="breadcrumb-item">Miscellaneous</li>
  <li class="breadcrumb-item">Type aliases</li>
</ol>

<section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <ul class="index-list">
                        <li>
                            <a href="#CreateOrgAndOrgAdminSagaSkipSteps" title="src/organization/sagas/create-org-and-admin.saga.ts" ><b>CreateOrgAndOrgAdminSagaSkipSteps</b>&nbsp;&nbsp;&nbsp;(src/.../create-org-and-admin.saga.ts)</a>
                        </li>
                        <li>
                            <a href="#ErrorConfig" title="src/filters/database-exception.filter.ts" ><b>ErrorConfig</b>&nbsp;&nbsp;&nbsp;(src/.../database-exception.filter.ts)</a>
                        </li>
                        <li>
                            <a href="#ErrorResponse" title="src/filters/database-exception.filter.ts" ><b>ErrorResponse</b>&nbsp;&nbsp;&nbsp;(src/.../database-exception.filter.ts)</a>
                        </li>
                        <li>
                            <a href="#OperatorFunction" title="src/tickets/routing/abstract/rule-evaluator.abstract.ts" ><b>OperatorFunction</b>&nbsp;&nbsp;&nbsp;(src/.../rule-evaluator.abstract.ts)</a>
                        </li>
                        <li>
                            <a href="#PostgresErrorCode" title="src/filters/database-exception.filter.ts" ><b>PostgresErrorCode</b>&nbsp;&nbsp;&nbsp;(src/.../database-exception.filter.ts)</a>
                        </li>
                        <li>
                            <a href="#StorageType" title="src/storage/types/storage.types.ts" ><b>StorageType</b>&nbsp;&nbsp;&nbsp;(src/.../storage.types.ts)</a>
                        </li>
                    </ul>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    <h3>src/organization/sagas/create-org-and-admin.saga.ts</h3>
    <section data-compodoc="block-typealias">
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="CreateOrgAndOrgAdminSagaSkipSteps"></a>
                    <span class="name "><b>CreateOrgAndOrgAdminSagaSkipSteps</b><a href="#CreateOrgAndOrgAdminSagaSkipSteps"><span class="icon ion-ios-link"></span></a></span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <code>    <code>literal type</code>
</code>
                </td>
            </tr>
        </tbody>
    </table>
</section>    <h3>src/filters/database-exception.filter.ts</h3>
    <section data-compodoc="block-typealias">
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ErrorConfig"></a>
                    <span class="name "><b>ErrorConfig</b><a href="#ErrorConfig"><span class="icon ion-ios-link"></span></a></span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <code>    <code>literal type</code>
</code>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ErrorResponse"></a>
                    <span class="name "><b>ErrorResponse</b><a href="#ErrorResponse"><span class="icon ion-ios-link"></span></a></span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <code>    <code>literal type</code>
</code>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="PostgresErrorCode"></a>
                    <span class="name "><b>PostgresErrorCode</b><a href="#PostgresErrorCode"><span class="icon ion-ios-link"></span></a></span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <code>    <code>literal type</code>
</code>
                </td>
            </tr>
        </tbody>
    </table>
</section>    <h3>src/tickets/routing/abstract/rule-evaluator.abstract.ts</h3>
    <section data-compodoc="block-typealias">
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="OperatorFunction"></a>
                    <span class="name "><b>OperatorFunction</b><a href="#OperatorFunction"><span class="icon ion-ios-link"></span></a></span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <code>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/function" target="_blank" >function</a></code>
</code>
                </td>
            </tr>
        </tbody>
    </table>
</section>    <h3>src/storage/types/storage.types.ts</h3>
    <section data-compodoc="block-typealias">
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="StorageType"></a>
                    <span class="name "><b>StorageType</b><a href="#StorageType"><span class="icon ion-ios-link"></span></a></span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <code>    <code>&quot;local&quot; | &quot;s3&quot;</code>
</code>
                </td>
            </tr>
        </tbody>
    </table>
</section>


                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'miscellaneous-typealiases';
            var COMPODOC_CURRENT_PAGE_URL = 'typealiases.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
