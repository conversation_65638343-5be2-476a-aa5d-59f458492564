<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content interface">
                   <div class="content-data">













<ol class="breadcrumb">
  <li class="breadcrumb-item">Interfaces</li>
  <li class="breadcrumb-item"
  >
  SignedFolderContent</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/storage/interfaces/storage.interface.ts</code>
        </p>




        <section data-compodoc="block-index">
            <h3 id="index">Index</h3>
            <table class="table table-sm table-bordered index-table">
                <tbody>
                    <tr>
                        <td class="col-md-4">
                            <h6><b>Properties</b></h6>
                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <ul class="index-list">
                                <li>
                                        <a href="#key" 
>
                                            key
                                        </a>
                                </li>
                                <li>
                                            <span class="modifier">Optional</span>
                                        <a href="#lastModified" 
>
                                            lastModified
                                        </a>
                                </li>
                                <li>
                                        <a href="#signedUrl" 
>
                                            signedUrl
                                        </a>
                                </li>
                                <li>
                                            <span class="modifier">Optional</span>
                                        <a href="#size" 
>
                                            size
                                        </a>
                                </li>
                            </ul>
                        </td>
                    </tr>
                </tbody>
            </table>
        </section>



            <section data-compodoc="block-properties">
                <h3 id="inputs">Properties</h3>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="key"></a>
                                        <span class="name "><b>key</b>
                                            <a href="#key">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>key:         <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                                        </td>
                                    </tr>





                        </tbody>
                    </table>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="lastModified"></a>
                                        <span class="name "><b>lastModified</b>
                                            <a href="#lastModified">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>lastModified:         <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date" target="_blank" >Date</a></code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date" target="_blank" >Date</a></code>

                                        </td>
                                    </tr>

                                    <tr>
                                        <td class="col-md-4">
                                            <i>Optional</i>
                                        </td>
                                    </tr>




                        </tbody>
                    </table>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="signedUrl"></a>
                                        <span class="name "><b>signedUrl</b>
                                            <a href="#signedUrl">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>signedUrl:         <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                                        </td>
                                    </tr>





                        </tbody>
                    </table>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="size"></a>
                                        <span class="name "><b>size</b>
                                            <a href="#size">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>size:         <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                                        </td>
                                    </tr>

                                    <tr>
                                        <td class="col-md-4">
                                            <i>Optional</i>
                                        </td>
                                    </tr>




                        </tbody>
                    </table>
            </section>
    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Storage } from &quot;@repo/thena-platform-entities&quot;;
import { StorageOptions, StorageType } from &quot;../types/storage.types&quot;;

export interface SignedFolderContent {
  key: string;
  signedUrl: string;
  lastModified?: Date;
  size?: number;
}

export interface GetFolderContentsOptions {
  bucket: string;
  folderPrefix: string;
}
export interface IStorageProvider {
  type: StorageType;

  /**
   * Read file from storage
   * @param path File path
   * @param options Additional options (bucket, encoding, etc.)
   */
  read(path: string, options?: StorageOptions): Promise&lt;any&gt;;

  /**
   * Write content to storage
   * @param path File path
   * @param content Content to write
   * @param options Additional options (bucket, contentType, etc.)
   */
  // eslint-disable-next-line max-len
  write(
    path: string,
    content: any,
    options?: StorageOptions,
    organizationId?: string,
  ): Promise&lt;Storage&gt;;

  /**
   * Delete file from storage
   * @param path File path
   * @param options Additional options (bucket, etc.)
   */
  delete(path: string, options?: StorageOptions): Promise&lt;void&gt;;

  /**
   * List files in directory
   * @param directory Directory path
   * @param options Additional options (bucket, etc.)
   */
  list(directory: string, options?: StorageOptions): Promise&lt;string[]&gt;;

  /**
   * Check if file exists
   * @param path File path
   * @param options Additional options (bucket, etc.)
   */
  exists?(path: string, options?: StorageOptions): Promise&lt;boolean&gt;;

  /**
   * Get metadata of a file
   * @param path File path
   * @param options Additional options (bucket, etc.)
   */
  getMetadata?(
    path: string,
    options?: StorageOptions,
  ): Promise&lt;Record&lt;string, any&gt;&gt;;

  /**
   * Get URL of a file
   * @param path File path
   * @param options Additional options (bucket, etc.)
   */
  getUrl?(path: string, options?: StorageOptions): Promise&lt;string&gt;;

  /**
   * Move file to a new location
   * @param sourceUrl Source URL
   * @param destinationKey Destination key
   * @param bucketName Bucket name
   */
  copyFile?(
    sourceUrl: string,
    destinationKey: string,
    bucketName?: string,
    options?: StorageOptions,
    organizationId?: string,
  ): Promise&lt;Storage&gt;;

  /**
   * Get signed URLs of all files in a folder
   * @param id Folder ID
   */
  getFolderContentsWithSignedUrls?(
    options: GetFolderContentsOptions,
  ): Promise&lt;SignedFolderContent[]&gt;;
}
</code></pre>
    </div>
</div>








                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'interface';
            var COMPODOC_CURRENT_PAGE_URL = 'SignedFolderContent.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
