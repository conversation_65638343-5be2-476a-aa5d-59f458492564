<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content interface">
                   <div class="content-data">













<ol class="breadcrumb">
  <li class="breadcrumb-item">Interfaces</li>
  <li class="breadcrumb-item"
  >
  SearchTicketsResponse</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/tickets/dto/queries.dto.ts</code>
        </p>


            <p class="comment">
                <h3>Description</h3>
            </p>
            <p class="comment">
                <p>Interface representing the response structure for ticket search operations</p>

            </p>


        <section data-compodoc="block-index">
            <h3 id="index">Index</h3>
            <table class="table table-sm table-bordered index-table">
                <tbody>
                    <tr>
                        <td class="col-md-4">
                            <h6><b>Properties</b></h6>
                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <ul class="index-list">
                                <li>
                                        <a href="#hits" 
>
                                            hits
                                        </a>
                                </li>
                                <li>
                                        <a href="#page" 
>
                                            page
                                        </a>
                                </li>
                                <li>
                                        <a href="#perPage" 
>
                                            perPage
                                        </a>
                                </li>
                                <li>
                                        <a href="#totalHits" 
>
                                            totalHits
                                        </a>
                                </li>
                                <li>
                                        <a href="#totalPages" 
>
                                            totalPages
                                        </a>
                                </li>
                            </ul>
                        </td>
                    </tr>
                </tbody>
            </table>
        </section>



            <section data-compodoc="block-properties">
                <h3 id="inputs">Properties</h3>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="hits"></a>
                                        <span class="name "><b>hits</b>
                                            <a href="#hits">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>hits:     <code>Array&lt;literal type&gt;</code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>    <code>Array&lt;literal type&gt;</code>

                                        </td>
                                    </tr>





                            <tr>
                                <td class="col-md-4">
                                    <div class="io-description"><p>Array of search hits containing matched documents and their highlights
Each hit contains the matched document and an array of highlighted matches</p>
</div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="page"></a>
                                        <span class="name "><b>page</b>
                                            <a href="#page">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>page:         <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
</code>
                                    </td>
                                </tr>

                                <tr>
                                    <td class="col-md-4">
                                        <i>Default value : </i><code>1
</code>
                                    </td>
                                </tr>

                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                                        </td>
                                    </tr>





                            <tr>
                                <td class="col-md-4">
                                    <div class="io-description"><p>Current page number in the search results</p>
</div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="perPage"></a>
                                        <span class="name "><b>perPage</b>
                                            <a href="#perPage">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>perPage:         <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
</code>
                                    </td>
                                </tr>

                                <tr>
                                    <td class="col-md-4">
                                        <i>Default value : </i><code>20
</code>
                                    </td>
                                </tr>

                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                                        </td>
                                    </tr>





                            <tr>
                                <td class="col-md-4">
                                    <div class="io-description"><p>Number of results per page</p>
</div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="totalHits"></a>
                                        <span class="name "><b>totalHits</b>
                                            <a href="#totalHits">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>totalHits:         <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                                        </td>
                                    </tr>





                            <tr>
                                <td class="col-md-4">
                                    <div class="io-description"><p>Total number of documents matching the search criteria</p>
</div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="totalPages"></a>
                                        <span class="name "><b>totalPages</b>
                                            <a href="#totalPages">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>totalPages:         <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                                        </td>
                                    </tr>





                            <tr>
                                <td class="col-md-4">
                                    <div class="io-description"><p>Total number of pages available for the search results
Calculated as Math.ceil(totalHits / perPage)</p>
</div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
            </section>
    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { ApiPropertyOptional } from &quot;@nestjs/swagger&quot;;
import { Transform } from &quot;class-transformer&quot;;
import {
  IsBoolean,
  IsNumber,
  IsOptional,
  IsString,
  Max,
  Min,
} from &quot;class-validator&quot;;

export class GetDraftTicketQuery {
  @IsNumber()
  @Min(1, { message: &quot;Page must be greater than or equal to 1&quot; })
  @IsOptional()
  @Transform(({ value }) &#x3D;&gt; parseInt(value))
  @ApiPropertyOptional({
    description: &quot;The page number to fetch tickets by&quot;,
    example: 1,
    default: 1,
    minimum: 1,
  })
  page?: number;

  @IsNumber()
  @Min(1, { message: &quot;Limit must be greater than or equal to 1&quot; })
  @Max(100, { message: &quot;Limit cannot exceed 100&quot; })
  @IsOptional()
  @Transform(({ value }) &#x3D;&gt; parseInt(value))
  @ApiPropertyOptional({
    description: &quot;The limit number of tickets to fetch&quot;,
    example: 10,
    default: 10,
    minimum: 1,
    maximum: 100,
  })
  limit?: number;
}

export class GetTicketQuery {
  @ApiPropertyOptional({
    description: &quot;The ID of the team to filter tickets by&quot;,
    example: &quot;T00M677SAK&quot;,
  })
  @IsString()
  @IsOptional()
  teamId?: string;

  @IsNumber()
  @Min(0, { message: &quot;Page must be greater than or equal to 0&quot; })
  @IsOptional()
  @Transform(({ value }) &#x3D;&gt; parseInt(value))
  @ApiPropertyOptional({
    description: &quot;The page number to fetch tickets by&quot;,
    example: 0,
    default: 0,
    minimum: 0,
  })
  page?: number;

  @IsNumber()
  @Min(1, { message: &quot;Limit must be greater than or equal to 1&quot; })
  @Max(100, { message: &quot;Limit cannot exceed 100&quot; })
  @IsOptional()
  @Transform(({ value }) &#x3D;&gt; parseInt(value))
  @ApiPropertyOptional({
    description: &quot;The limit number of tickets to fetch&quot;,
    example: 10,
    default: 10,
    minimum: 1,
    maximum: 100,
  })
  limit?: number;
}

export class GetTicketRelatedQuery {
  /**
   * Whether to fetch linked tickets.
   */
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) &#x3D;&gt; value &#x3D;&#x3D;&#x3D; &quot;true&quot;)
  @ApiPropertyOptional({ description: &quot;Whether to fetch linked tickets&quot; })
  linked?: boolean;

  /**
   * Whether to fetch subtickets.
   */
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) &#x3D;&gt; value &#x3D;&#x3D;&#x3D; &quot;true&quot;)
  @ApiPropertyOptional({ description: &quot;Whether to fetch subtickets&quot; })
  subtickets?: boolean;

  /**
   * Whether to fetch duplicate tickets.
   */
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) &#x3D;&gt; value &#x3D;&#x3D;&#x3D; &quot;true&quot;)
  @ApiPropertyOptional({ description: &quot;Whether to fetch duplicate tickets&quot; })
  duplicate?: boolean;

  /**
   * The page number to fetch tickets by.
   */
  @IsNumber()
  @IsOptional()
  @ApiPropertyOptional({ description: &quot;The page number to fetch tickets by&quot; })
  page?: number;

  /**
   * The limit of tickets to fetch.
   */
  @IsNumber()
  @IsOptional()
  @ApiPropertyOptional({ description: &quot;The limit of tickets to fetch&quot; })
  limit?: number;
}

export interface AssignTicketQuery {
  unassign?: boolean;
}

export interface SearchTicketsQuery {
  /**
   * The search query to filter tickets by.
   */
  q?: string;

  /**
   * The ID of the team to filter tickets by.
   */
  team_id?: string;

  /**
   * The ID of the organization to filter tickets by.
   */
  organization_id?: string;

  /**
   * The page number to fetch tickets by.
   */
  page?: number;

  /**
   * The limit of tickets to fetch.
   */
  perPage?: number;

  /**
   * The field to sort the tickets by.
   */
  sortBy?: string;

  /**
   * The order to sort the tickets by.
   */
  sortOrder?: &quot;asc&quot; | &quot;desc&quot;;
}

/**
 * Interface representing the response structure for ticket search operations
 * @interface SearchTicketsResponse
 */
export interface SearchTicketsResponse {
  /**
   * Array of search hits containing matched documents and their highlights
   * Each hit contains the matched document and an array of highlighted matches
   */
  hits: Array&lt;{
    /** The matched document containing all ticket data */
    document: any;
    /** Array of highlighted matches found in the document fields */
    highlights: any[];
  }&gt;;

  /**
   * Current page number in the search results
   * @default 1
   */
  page: number;

  /**
   * Number of results per page
   * @default 20
   */
  perPage: number;

  /**
   * Total number of documents matching the search criteria
   */
  totalHits: number;

  /**
   * Total number of pages available for the search results
   * Calculated as Math.ceil(totalHits / perPage)
   */
  totalPages: number;
}
</code></pre>
    </div>
</div>








                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'interface';
            var COMPODOC_CURRENT_PAGE_URL = 'SearchTicketsResponse.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
