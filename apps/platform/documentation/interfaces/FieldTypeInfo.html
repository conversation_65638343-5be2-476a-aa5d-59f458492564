<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content interface">
                   <div class="content-data">













<ol class="breadcrumb">
  <li class="breadcrumb-item">Interfaces</li>
  <li class="breadcrumb-item"
  >
  FieldTypeInfo</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/tickets/services/ticket-annotator.service.ts</code>
        </p>




        <section data-compodoc="block-index">
            <h3 id="index">Index</h3>
            <table class="table table-sm table-bordered index-table">
                <tbody>
                    <tr>
                        <td class="col-md-4">
                            <h6><b>Properties</b></h6>
                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <ul class="index-list">
                                <li>
                                        <a href="#fieldName" 
>
                                            fieldName
                                        </a>
                                </li>
                                <li>
                                        <a href="#isArray" 
>
                                            isArray
                                        </a>
                                </li>
                                <li>
                                        <a href="#isNullable" 
>
                                            isNullable
                                        </a>
                                </li>
                                <li>
                                        <a href="#isRelation" 
>
                                            isRelation
                                        </a>
                                </li>
                                <li>
                                            <span class="modifier">Optional</span>
                                        <a href="#metadata" 
>
                                            metadata
                                        </a>
                                </li>
                                <li>
                                            <span class="modifier">Optional</span>
                                        <a href="#relationEntityName" 
>
                                            relationEntityName
                                        </a>
                                </li>
                                <li>
                                            <span class="modifier">Optional</span>
                                        <a href="#relationType" 
>
                                            relationType
                                        </a>
                                </li>
                                <li>
                                        <a href="#type" 
>
                                            type
                                        </a>
                                </li>
                            </ul>
                        </td>
                    </tr>
                </tbody>
            </table>
        </section>



            <section data-compodoc="block-properties">
                <h3 id="inputs">Properties</h3>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="fieldName"></a>
                                        <span class="name "><b>fieldName</b>
                                            <a href="#fieldName">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>fieldName:         <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                                        </td>
                                    </tr>





                        </tbody>
                    </table>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="isArray"></a>
                                        <span class="name "><b>isArray</b>
                                            <a href="#isArray">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>isArray:         <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                                        </td>
                                    </tr>





                        </tbody>
                    </table>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="isNullable"></a>
                                        <span class="name "><b>isNullable</b>
                                            <a href="#isNullable">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>isNullable:         <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                                        </td>
                                    </tr>





                        </tbody>
                    </table>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="isRelation"></a>
                                        <span class="name "><b>isRelation</b>
                                            <a href="#isRelation">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>isRelation:         <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                                        </td>
                                    </tr>





                        </tbody>
                    </table>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="metadata"></a>
                                        <span class="name "><b>metadata</b>
                                            <a href="#metadata">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>metadata:     <code>literal type</code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>    <code>literal type</code>

                                        </td>
                                    </tr>

                                    <tr>
                                        <td class="col-md-4">
                                            <i>Optional</i>
                                        </td>
                                    </tr>




                        </tbody>
                    </table>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="relationEntityName"></a>
                                        <span class="name "><b>relationEntityName</b>
                                            <a href="#relationEntityName">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>relationEntityName:         <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                                        </td>
                                    </tr>

                                    <tr>
                                        <td class="col-md-4">
                                            <i>Optional</i>
                                        </td>
                                    </tr>




                        </tbody>
                    </table>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="relationType"></a>
                                        <span class="name "><b>relationType</b>
                                            <a href="#relationType">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>relationType:     <code>&quot;ONE_TO_ONE&quot; | &quot;ONE_TO_MANY&quot; | &quot;MANY_TO_ONE&quot; | &quot;MANY_TO_MANY&quot;</code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>    <code>&quot;ONE_TO_ONE&quot; | &quot;ONE_TO_MANY&quot; | &quot;MANY_TO_ONE&quot; | &quot;MANY_TO_MANY&quot;</code>

                                        </td>
                                    </tr>

                                    <tr>
                                        <td class="col-md-4">
                                            <i>Optional</i>
                                        </td>
                                    </tr>




                        </tbody>
                    </table>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="type"></a>
                                        <span class="name "><b>type</b>
                                            <a href="#type">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>type:         <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                                        </td>
                                    </tr>





                        </tbody>
                    </table>
            </section>
    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Injectable, NotFoundException } from &quot;@nestjs/common&quot;;
import { InjectDataSource } from &quot;@nestjs/typeorm&quot;;
import { tickets } from &quot;@repo/shared-proto&quot;;
import {
  Ticket,
  TicketPriority,
  TicketPriorityRepository,
  TicketRepository,
  TicketStatus,
  TicketStatusRepository,
  TicketType,
  TicketTypeRepository,
} from &quot;@repo/thena-platform-entities&quot;;
import &quot;reflect-metadata&quot;;
import { DataSource } from &quot;typeorm&quot;;
import { FieldMetadataService } from &quot;../../common/services/field-metadata.service&quot;;

interface FieldTypeInfo {
  fieldName: string;
  type: string;
  isNullable: boolean;
  isArray: boolean;
  isRelation: boolean;
  relationEntityName?: string;
  relationType?: &quot;ONE_TO_ONE&quot; | &quot;ONE_TO_MANY&quot; | &quot;MANY_TO_ONE&quot; | &quot;MANY_TO_MANY&quot;;
  metadata?: {
    label?: string;
    description?: string;
    required?: boolean;
    system?: boolean;
    slaEnabled?: boolean;
  };
}

@Injectable()
export class TicketAnnotatorService {
  constructor(
    @InjectDataSource()
    private readonly dataSource: DataSource,
    private readonly ticketRepository: TicketRepository,
    private readonly ticketStatusRepository: TicketStatusRepository,
    private readonly ticketTypeRepository: TicketTypeRepository,
    private readonly ticketPriorityRepository: TicketPriorityRepository,
    private readonly fieldMetadataService: FieldMetadataService,
  ) {}

  /**
   * @deprecated
   */
  getEntityFieldTypes(slaEnabled: boolean): FieldTypeInfo[] {
    const entity &#x3D; Ticket;
    const metadata &#x3D; this.dataSource.getMetadata(entity);
    const fields: FieldTypeInfo[] &#x3D; [];

    // Process columns
    metadata.columns.forEach((column) &#x3D;&gt; {
      const fieldMetadataReflect &#x3D; Reflect.getMetadata(
        &quot;fieldMetadata&quot;,
        entity.prototype,
        column.propertyName,
      );

      if (!slaEnabled || fieldMetadataReflect?.slaMetadata?.slaEnabled) {
        let metadata &#x3D; {};
        if (fieldMetadataReflect?.slaMetadata?.slaEnabled) {
          const { slaMetadata, ...rest } &#x3D; fieldMetadataReflect;
          metadata &#x3D; { ...rest, ...slaMetadata };
        }

        fields.push({
          fieldName: column.propertyName,
          type: fieldMetadataReflect?.type || column.type.toString(),
          isNullable: column.isNullable,
          isArray: false,
          isRelation: false,
          metadata,
        });
      }
    });

    // Process relations
    metadata.relations.forEach((relation) &#x3D;&gt; {
      const fieldMetadataReflect &#x3D; Reflect.getMetadata(
        &quot;fieldMetadata&quot;,
        entity.prototype,
        relation.propertyName,
      );

      if (!slaEnabled || fieldMetadataReflect?.slaMetadata?.slaEnabled) {
        let metadata &#x3D; {};
        if (fieldMetadataReflect?.slaMetadata?.slaEnabled) {
          const { slaMetadata, ...rest } &#x3D; fieldMetadataReflect;
          metadata &#x3D; { ...rest, ...slaMetadata };
        }

        // Determine relation type
        let relationType: FieldTypeInfo[&quot;relationType&quot;];
        if (relation.isOneToOne) relationType &#x3D; &quot;ONE_TO_ONE&quot;;
        else if (relation.isOneToMany) relationType &#x3D; &quot;ONE_TO_MANY&quot;;
        else if (relation.isManyToOne) relationType &#x3D; &quot;MANY_TO_ONE&quot;;
        else if (relation.isManyToMany) relationType &#x3D; &quot;MANY_TO_MANY&quot;;

        // Determine if it&#x27;s an array based on relation type
        const isArray &#x3D; relation.isOneToMany || relation.isManyToMany;

        fields.push({
          fieldName: relation.propertyName,
          type: &quot;relation&quot;,
          isNullable: relation.isNullable,
          isArray,
          isRelation: true,
          relationType,
          relationEntityName:
            relation.type instanceof Function
              ? relation.type.name
              : (relation.type as string),
          metadata,
        });
      }
    });

    return fields;
  }

  /**
   * Get field metadata for tickets
   */
  getTicketFieldMetadata(): tickets.GetTicketFieldMetadataResponse {
    return this.fieldMetadataService.getFieldMetadata(Ticket);
  }

  /**
   * Get field metadata for ticket status
   */
  getTicketStatusFieldMetadata(): tickets.GetTicketStatusFieldMetadataResponse {
    return this.fieldMetadataService.getFieldMetadata(TicketStatus);
  }

  /**
   * Get field metadata for ticket type
   */
  getTicketTypeFieldMetadata(): tickets.GetTicketTypeFieldMetadataResponse {
    return this.fieldMetadataService.getFieldMetadata(TicketType);
  }

  /**
   * Get field metadata for ticket priority
   */
  getTicketPriorityFieldMetadata(): tickets.GetTicketPriorityFieldMetadataResponse {
    return this.fieldMetadataService.getFieldMetadata(TicketPriority);
  }

  /**
   * Get ticket data with relations
   */
  async getTicketData(
    ticketId: string,
    relations: string[],
    organizationId: string,
  ): Promise&lt;tickets.GetTicketDataResponse&gt; {
    const ticket &#x3D; await this.ticketRepository.findByCondition({
      where: { uid: ticketId, organizationId },
      relations,
    });

    if (!ticket) {
      throw new NotFoundException(&quot;Ticket not found&quot;);
    }

    return {
      data: JSON.stringify(ticket),
    };
  }

  /**
   * Get ticket status data with relations
   */
  async getTicketStatusData(
    statusId: string,
    relations: string[],
    organizationId: string,
  ): Promise&lt;tickets.GetTicketStatusDataResponse&gt; {
    const status &#x3D; await this.ticketStatusRepository.findByCondition({
      where: { uid: statusId, organizationId },
      relations,
    });

    if (!status) {
      throw new NotFoundException(&quot;Ticket status not found&quot;);
    }

    return {
      data: JSON.stringify(status),
    };
  }

  /**
   * Get ticket type data with relations
   */
  async getTicketTypeData(
    typeId: string,
    relations: string[],
    organizationId: string,
  ): Promise&lt;tickets.GetTicketTypeDataResponse&gt; {
    const type &#x3D; await this.ticketTypeRepository.findByCondition({
      where: { uid: typeId, organizationId },
      relations,
    });

    if (!type) {
      throw new NotFoundException(&quot;Ticket type not found&quot;);
    }

    return {
      data: JSON.stringify(type),
    };
  }

  /**
   * Get ticket priority data with relations
   */
  async getTicketPriorityData(
    priorityId: string,
    relations: string[],
    organizationId: string,
  ): Promise&lt;tickets.GetTicketPriorityDataResponse&gt; {
    const priority &#x3D; await this.ticketPriorityRepository.findByCondition({
      where: { uid: priorityId, organizationId },
      relations,
    });

    if (!priority) {
      throw new NotFoundException(&quot;Ticket priority not found&quot;);
    }

    return {
      data: JSON.stringify(priority),
    };
  }
}
</code></pre>
    </div>
</div>








                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'interface';
            var COMPODOC_CURRENT_PAGE_URL = 'FieldTypeInfo.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
