<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content interface">
                   <div class="content-data">













<ol class="breadcrumb">
  <li class="breadcrumb-item">Interfaces</li>
  <li class="breadcrumb-item"
  >
  CreateOrgAndOrgAdminSagaInput</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/organization/sagas/create-org-and-admin.saga.ts</code>
        </p>




        <section data-compodoc="block-index">
            <h3 id="index">Index</h3>
            <table class="table table-sm table-bordered index-table">
                <tbody>
                    <tr>
                        <td class="col-md-4">
                            <h6><b>Properties</b></h6>
                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <ul class="index-list">
                                <li>
                                        <a href="#authId" 
>
                                            authId
                                        </a>
                                </li>
                                <li>
                                        <a href="#email" 
>
                                            email
                                        </a>
                                </li>
                                <li>
                                        <a href="#identifier" 
>
                                            identifier
                                        </a>
                                </li>
                                <li>
                                        <a href="#logoUrl" 
>
                                            logoUrl
                                        </a>
                                </li>
                                <li>
                                        <a href="#organizationName" 
>
                                            organizationName
                                        </a>
                                </li>
                                <li>
                                        <a href="#password" 
>
                                            password
                                        </a>
                                </li>
                            </ul>
                        </td>
                    </tr>
                </tbody>
            </table>
        </section>



            <section data-compodoc="block-properties">
                <h3 id="inputs">Properties</h3>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="authId"></a>
                                        <span class="name "><b>authId</b>
                                            <a href="#authId">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>authId:         <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                                        </td>
                                    </tr>





                        </tbody>
                    </table>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="email"></a>
                                        <span class="name "><b>email</b>
                                            <a href="#email">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>email:         <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                                        </td>
                                    </tr>





                        </tbody>
                    </table>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="identifier"></a>
                                        <span class="name "><b>identifier</b>
                                            <a href="#identifier">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>identifier:         <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                                        </td>
                                    </tr>





                        </tbody>
                    </table>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="logoUrl"></a>
                                        <span class="name "><b>logoUrl</b>
                                            <a href="#logoUrl">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>logoUrl:         <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                                        </td>
                                    </tr>





                        </tbody>
                    </table>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="organizationName"></a>
                                        <span class="name "><b>organizationName</b>
                                            <a href="#organizationName">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>organizationName:         <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                                        </td>
                                    </tr>





                        </tbody>
                    </table>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="password"></a>
                                        <span class="name "><b>password</b>
                                            <a href="#password">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>password:         <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                                        </td>
                                    </tr>





                        </tbody>
                    </table>
            </section>
    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { InjectQueue } from &quot;@nestjs/bullmq&quot;;
import { Injectable } from &quot;@nestjs/common&quot;;
import { EventEmitter2 } from &quot;@nestjs/event-emitter&quot;;
import { OrganizationEvents } from &quot;@repo/thena-eventbridge&quot;;
import {
  Organization,
  OrganizationDomainsRepository,
  OrganizationRepository,
  TransactionService,
  User,
  UserType,
} from &quot;@repo/thena-platform-entities&quot;;
import { AbstractSaga } from &quot;@repo/thena-shared-libs&quot;;
import { Queue } from &quot;bullmq&quot;;
import tldts from &quot;tldts&quot;;
import { QueueNames } from &quot;../../constants/queue.constants&quot;;
import { UsersService } from &quot;../../users/services/users.service&quot;;
import { OrganizationEventsFactory } from &quot;../events/organization-events.factory&quot;;
import { OrganizationSNSEventsFactory } from &quot;../events/organization-sns-events.factory&quot;;
import { EmittableOrganizationEvents } from &quot;../events/organization.events&quot;;

const noop &#x3D; () &#x3D;&gt; Promise.resolve();

interface CreateOrgAndOrgAdminSagaInput {
  email: string;
  authId: string;
  logoUrl: string;
  password: string;
  identifier: string;
  organizationName: string;
}

type CreateOrgAndOrgAdminSagaSkipSteps &#x3D; {
  createUser: boolean;
};

/**
 * Saga that creates an organization and an organization admin user.
 */
@Injectable()
export class CreateOrgAndOrgAdminSaga extends AbstractSaga&lt;
  { organization: Organization; user: User },
  CreateOrgAndOrgAdminSagaInput,
  CreateOrgAndOrgAdminSagaSkipSteps
&gt; {
  /**
   * Constructor for the CreateOrgAndOrgAdminSaga class.
   * @param data The data for the saga.
   * @param eventEmitter The event emitter.
   * @param organizationEventsFactory The organization events factory.
   * @param transactionService The transaction service.
   * @param organizationRepository The organization repository.
   */
  constructor(
    // Events
    private readonly eventEmitter: EventEmitter2,
    private readonly organizationEventsFactory: OrganizationEventsFactory,

    // Services
    private readonly transactionService: TransactionService,
    private readonly usersService: UsersService,

    // Repositories
    private readonly organizationRepository: OrganizationRepository,
    private readonly organizationDomainsRepository: OrganizationDomainsRepository,

    // SNS Publisher Queue
    @InjectQueue(QueueNames.ORGANIZATION_SNS_PUBLISHER)
    private readonly organizationSNSPublisherQueue: Queue,

    // SNS Event Factory
    private readonly organizationSNSEventsFactory: OrganizationSNSEventsFactory,
  ) {
    super();

    // Setup the steps
    this.setupSteps();
  }

  private setupSteps(): void {
    // Step 1: Create the organization
    this.addStep({
      name: &quot;create-organization&quot;,
      execute: async (_context) &#x3D;&gt; {
        // Create the organization internally in an async transaction
        const organization &#x3D; await this.transactionService.runInTransaction(
          async (txnContext) &#x3D;&gt; {
            const { domain } &#x3D; tldts.parse(this.context.input.email);

            // Generate and validate organization payload
            const organizationPayload &#x3D; this.organizationRepository.create({
              isVerified: false,
              uid: this.context.input.identifier,
              logoUrl: this.context.input.logoUrl,
              name: this.context.input.organizationName,
            });

            // Save the organization
            const savedOrganization &#x3D;
              await this.organizationRepository.saveWithTxn(
                txnContext,
                organizationPayload,
              );

            // Save the organization domain
            await this.organizationDomainsRepository.saveWithTxn(txnContext, {
              domain,
              organization: savedOrganization,
            });

            return savedOrganization;
          },
        );

        // Set the organization in the context
        this.context.data.organization &#x3D; organization;
      },
      compensate: async (_context) &#x3D;&gt; {
        // Delete the org if creating the org admin fails
        await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
          const domains &#x3D; await this.organizationDomainsRepository.findAll({
            where: { organization: { id: this.context.data.organization.id } },
          });

          // Remove the domains
          await this.organizationDomainsRepository.removeManyWithTxn(
            txnContext,
            domains,
          );

          // Remove the organization
          await this.organizationRepository.removeWithTxn(
            txnContext,
            this.context.data.organization,
          );
        });
      },
    });

    // Step 2: Create the organization admin user
    this.addStep({
      name: &quot;create-organization-admin&quot;,
      execute: async (_context) &#x3D;&gt; {
        // If we&#x27;re skipping the user creation, which means supabase already have this user
        // we&#x27;ll just create the public user profile
        let organizationAdminPersona: User;
        if (_context.skipSteps.createUser) {
          const username &#x3D; this.context.input.email.split(&quot;@&quot;)[0];

          // Create the organization admin user persona
          organizationAdminPersona &#x3D; await this.usersService.createUserPersona({
            name: username,
            email: this.context.input.email,
            organizationId: this.context.data.organization.id,
            userType: UserType.ORG_ADMIN,
            authId: this.context.input.authId,
          });
        } else {
          // Create the organization admin user
          organizationAdminPersona &#x3D;
            await this.usersService.createOrganizationAdmin({
              email: this.context.input.email,
              password: this.context.input.password,
              organizationUid: this.context.input.identifier,
            });
        }

        // Set the organization admin persona in the context
        this.context.data.user &#x3D; organizationAdminPersona;
      },
      compensate: noop,
    });

    // Step 3: Emit the organization created event
    this.addStep({
      name: &quot;emit-organization-created-event&quot;,
      execute: async (_context) &#x3D;&gt; {
        // Construct the organization created event
        const organizationCreatedEvent &#x3D;
          this.organizationEventsFactory.createOrganizationCreatedEvent(
            this.context.data.organization,
          );

        // Emit the organization created event
        this.eventEmitter.emit(
          EmittableOrganizationEvents.ORGANIZATION_CREATED,
          organizationCreatedEvent,
        );

        // Publish the organization created event to the SNS queue
        const organizationCreatedEventData &#x3D;
          this.organizationSNSEventsFactory.createOrganizationCreatedSNSEvent(
            {
              uid: this.context.data.user.uid,
              email: this.context.data.user.email,
              userType: this.context.data.user.userType,
              orgUid: this.context.data.organization.uid,
            },
            this.context.data.organization,
          );

        await this.organizationSNSPublisherQueue.add(
          QueueNames.ORGANIZATION_SNS_PUBLISHER,
          {
            event: OrganizationEvents.CREATED,
            eventData: organizationCreatedEventData,
            user: {
              uid: this.context.data.user.uid,
              email: this.context.data.user.email,
              userType: this.context.data.user.userType,
              orgUid: this.context.data.organization.uid,
            },
          },
          {
            attempts: 3,
            backoff: {
              type: &quot;exponential&quot;,
              delay: 1000, // 1 second
            },
          },
        );
      },
      compensate: noop,
    });
  }
}
</code></pre>
    </div>
</div>








                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'interface';
            var COMPODOC_CURRENT_PAGE_URL = 'CreateOrgAndOrgAdminSagaInput.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
