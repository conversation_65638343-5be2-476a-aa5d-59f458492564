<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content interface">
                   <div class="content-data">













<ol class="breadcrumb">
  <li class="breadcrumb-item">Interfaces</li>
  <li class="breadcrumb-item"
  >
  TicketRoutingContext</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/tickets/routing/providers/thena-request-router/engine/thena-request-router.engine.ts</code>
        </p>




        <section data-compodoc="block-index">
            <h3 id="index">Index</h3>
            <table class="table table-sm table-bordered index-table">
                <tbody>
                    <tr>
                        <td class="col-md-4">
                            <h6><b>Properties</b></h6>
                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <ul class="index-list">
                                <li>
                                            <span class="modifier">Optional</span>
                                        <a href="#error" 
>
                                            error
                                        </a>
                                </li>
                                <li>
                                            <span class="modifier">Optional</span>
                                        <a href="#evaluatedTeam" 
>
                                            evaluatedTeam
                                        </a>
                                </li>
                                <li>
                                            <span class="modifier">Optional</span>
                                        <a href="#evaluatedUser" 
>
                                            evaluatedUser
                                        </a>
                                </li>
                                <li>
                                            <span class="modifier">Optional</span>
                                        <a href="#exitEarly" 
>
                                            exitEarly
                                        </a>
                                </li>
                                <li>
                                        <a href="#forTeam" 
>
                                            forTeam
                                        </a>
                                </li>
                                <li>
                                        <a href="#rules" 
>
                                            rules
                                        </a>
                                </li>
                                <li>
                                        <a href="#ticket" 
>
                                            ticket
                                        </a>
                                </li>
                                <li>
                                        <a href="#user" 
>
                                            user
                                        </a>
                                </li>
                            </ul>
                        </td>
                    </tr>
                </tbody>
            </table>
        </section>



            <section data-compodoc="block-properties">
                <h3 id="inputs">Properties</h3>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="error"></a>
                                        <span class="name "><b>error</b>
                                            <a href="#error">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>error:     <code>Error</code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>    <code>Error</code>

                                        </td>
                                    </tr>

                                    <tr>
                                        <td class="col-md-4">
                                            <i>Optional</i>
                                        </td>
                                    </tr>




                        </tbody>
                    </table>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="evaluatedTeam"></a>
                                        <span class="name "><b>evaluatedTeam</b>
                                            <a href="#evaluatedTeam">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>evaluatedTeam:     <code>Team</code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>    <code>Team</code>

                                        </td>
                                    </tr>

                                    <tr>
                                        <td class="col-md-4">
                                            <i>Optional</i>
                                        </td>
                                    </tr>




                        </tbody>
                    </table>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="evaluatedUser"></a>
                                        <span class="name "><b>evaluatedUser</b>
                                            <a href="#evaluatedUser">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>evaluatedUser:     <code>User</code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>    <code>User</code>

                                        </td>
                                    </tr>

                                    <tr>
                                        <td class="col-md-4">
                                            <i>Optional</i>
                                        </td>
                                    </tr>




                        </tbody>
                    </table>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="exitEarly"></a>
                                        <span class="name "><b>exitEarly</b>
                                            <a href="#exitEarly">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>exitEarly:         <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                                        </td>
                                    </tr>

                                    <tr>
                                        <td class="col-md-4">
                                            <i>Optional</i>
                                        </td>
                                    </tr>




                        </tbody>
                    </table>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="forTeam"></a>
                                        <span class="name "><b>forTeam</b>
                                            <a href="#forTeam">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>forTeam:     <code>Team</code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>    <code>Team</code>

                                        </td>
                                    </tr>





                        </tbody>
                    </table>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="rules"></a>
                                        <span class="name "><b>rules</b>
                                            <a href="#rules">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>rules:     <code>RuleGroup[]</code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>    <code>RuleGroup[]</code>

                                        </td>
                                    </tr>





                        </tbody>
                    </table>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="ticket"></a>
                                        <span class="name "><b>ticket</b>
                                            <a href="#ticket">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>ticket:     <code>Ticket</code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>    <code>Ticket</code>

                                        </td>
                                    </tr>





                        </tbody>
                    </table>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="user"></a>
                                        <span class="name "><b>user</b>
                                            <a href="#user">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>user:         <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>        <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>

                                        </td>
                                    </tr>





                        </tbody>
                    </table>
            </section>
    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Inject, Injectable } from &quot;@nestjs/common&quot;;
import { EventEmitter2 } from &quot;@nestjs/event-emitter&quot;;
import { ILogger } from &quot;@repo/nestjs-commons/logger&quot;;
import {
  AuditLog,
  AuditLogEntityType,
  AuditLogOp,
  AuditLogVisibility,
  RuleGroup,
  Team,
  TeamRoutingRule,
  TeamRoutingRulesRepository,
  Ticket,
  TicketRepository,
  TransactionService,
  User,
} from &quot;@repo/thena-platform-entities&quot;;
import { DeepPartial } from &quot;typeorm&quot;;
import { ActivitiesService } from &quot;../../../../../activities/services/activities.service&quot;;
import { CurrentUser } from &quot;../../../../../common/decorators&quot;;
import { TeamsService } from &quot;../../../../../teams/services/teams.service&quot;;
import { RequestRouterEngine } from &quot;../../../interfaces&quot;;
import { ThenaAgentAllocator } from &quot;../agent-allocator&quot;;
import { ThenaRuleEvaluator } from &quot;../rule-evaluator&quot;;

enum TicketRoutingState {
  INITIAL &#x3D; &quot;INITIAL&quot;,
  TEAM_EVALUATED &#x3D; &quot;TEAM_EVALUATED&quot;,
  TEAM_ASSIGNED &#x3D; &quot;TEAM_ASSIGNED&quot;,
  USER_EVALUATED &#x3D; &quot;USER_EVALUATED&quot;,
  USER_ASSIGNED &#x3D; &quot;USER_ASSIGNED&quot;,
  FAILED &#x3D; &quot;FAILED&quot;,
}

enum TicketRoutingEvent {
  EVALUATE_TEAM &#x3D; &quot;EVALUATE_TEAM&quot;,
  ASSIGN_TEAM &#x3D; &quot;ASSIGN_TEAM&quot;,
  EVALUATE_USER &#x3D; &quot;EVALUATE_USER&quot;,
  ASSIGN_USER &#x3D; &quot;ASSIGN_USER&quot;,
  FAIL &#x3D; &quot;FAIL&quot;,
}

// State machine context to hold data
interface TicketRoutingContext {
  forTeam: Team;
  exitEarly?: boolean;
  ticket: Ticket;
  rules: RuleGroup[];
  user: CurrentUser;
  evaluatedTeam?: Team;
  evaluatedUser?: User;
  error?: Error;
}

@Injectable()
export class ThenaRequestRouterEngine implements RequestRouterEngine {
  private spanId: string;

  constructor(
    @Inject(&quot;CustomLogger&quot;)
    private readonly logger: ILogger,
    private readonly eventEmitter: EventEmitter2,

    // Activity service
    private readonly activitiesService: ActivitiesService,

    // Transaction service
    private readonly transactionService: TransactionService,

    // Rule evaluator
    private readonly ruleEvaluator: ThenaRuleEvaluator,

    // Team service
    private readonly teamService: TeamsService,

    // Agent allocator
    private readonly agentAllocator: ThenaAgentAllocator,

    // Team routing rules repository
    private readonly teamRoutingRulesRepository: TeamRoutingRulesRepository,

    // Ticket repository
    private readonly ticketRepository: TicketRepository,
  ) {
    this.spanId &#x3D; &quot;[ThenaRequestRouterEngine]&quot;;
  }

  private async transition(
    currentState: TicketRoutingState,
    event: TicketRoutingEvent,
    context: TicketRoutingContext,
  ) {
    try {
      switch (currentState) {
        // Initial state
        case TicketRoutingState.INITIAL:
          if (event &#x3D;&#x3D;&#x3D; TicketRoutingEvent.EVALUATE_TEAM) {
            // Evaluate the rules
            const team &#x3D; await this.ruleEvaluator.evaluate(
              context.ticket,
              context.rules,
              context.user,
            );

            // If no teams are available, exit early
            if (!team) {
              this.logger.warn(
                &#x60;${this.spanId} No team available for ticket: ${context.ticket.id}&#x60;,
              );

              // Create an audit log
              const auditLog: DeepPartial&lt;AuditLog&gt; &#x3D; {
                team: { id: context.forTeam.id },
                organization: { id: context.user.orgId },
                activityPerformedBy: { id: context.user.sub }, // TODO: Change this to the user who is evaluating the team
                entityId: context.ticket.id,
                entityUid: context.ticket.uid,
                entityType: AuditLogEntityType.TICKET,
                op: AuditLogOp.INFO,
                visibility: AuditLogVisibility.ORGANIZATION,
                activity: &#x60;No available team found for ticket: ${context.ticket.id}&#x60;,
                description: &#x60;No available team found for ticket ${context.ticket.id} based on the routing rules configured for team ${context.forTeam.name} therefore the ticket was not routed to any team.&#x60;,
              };

              // Record the audit log
              await this.activitiesService.recordAuditLog(auditLog);

              context.exitEarly &#x3D; true;
            } else {
              context.evaluatedTeam &#x3D; team;

              // Emit the team evaluated event
              this.eventEmitter.emit(&quot;ticket.team.evaluated&quot;, {
                ticketId: context.ticket.id,
                teamId: team.id,
              });
            }

            return TicketRoutingState.TEAM_EVALUATED;
          }

          break;

        // Team evaluated state
        case TicketRoutingState.TEAM_EVALUATED:
          if (event &#x3D;&#x3D;&#x3D; TicketRoutingEvent.ASSIGN_TEAM) {
            // Update the ticket with the evaluated team
            await this.transactionService.runInTransaction(
              async (txnContext) &#x3D;&gt; {
                const oldTeamId &#x3D; context.ticket.teamId;

                // Update the ticket with the evaluated team
                await this.ticketRepository.updateWithTxn(
                  txnContext,
                  { id: context.ticket.id },
                  { teamId: context.evaluatedTeam.id },
                );

                // Create an audit log
                const auditLog: DeepPartial&lt;AuditLog&gt; &#x3D; {
                  team: { id: context.forTeam.id },
                  organization: { id: context.user.orgId },
                  activityPerformedBy: { id: context.user.sub }, // TODO: Change this to the user who is evaluating the team
                  entityId: context.ticket.id,
                  entityUid: context.ticket.uid,
                  entityType: AuditLogEntityType.TICKET,
                  op: AuditLogOp.UPDATED,
                  visibility: AuditLogVisibility.ORGANIZATION,
                  activity: &#x60;The team was evaluated for ticket ${context.ticket.id}&#x60;,
                  description: &#x60;Based on the routing rules configured for team ${context.forTeam.name}, the team ${context.evaluatedTeam.name} was evaluated for ticket ${context.ticket.id}&#x60;,
                  metadata: {
                    updatedFields: [
                      {
                        field: &quot;teamId&quot;,
                        previousValue: oldTeamId,
                        updatedToValue: context.evaluatedTeam.id,
                      },
                    ],
                  },
                };

                // Record the audit log
                await this.activitiesService.recordAuditLog(
                  auditLog,
                  txnContext,
                );
              },
            );

            // Emit the team assigned event
            this.eventEmitter.emit(&quot;ticket.team.assigned&quot;, {
              ticketId: context.ticket.id,
              teamId: context.evaluatedTeam.id,
            });

            return TicketRoutingState.TEAM_ASSIGNED;
          }
          break;

        // Team assigned state
        case TicketRoutingState.TEAM_ASSIGNED:
          if (event &#x3D;&#x3D;&#x3D; TicketRoutingEvent.EVALUATE_USER) {
            const user &#x3D; await this.agentAllocator.allocate(
              context.evaluatedTeam,
              context.user,
            );

            // If no user is available, exit early
            if (!user) {
              this.logger.warn(
                &#x60;${this.spanId} No user available for ticket: ${context.ticket.id}&#x60;,
              );

              // Create an audit log
              const auditLog: DeepPartial&lt;AuditLog&gt; &#x3D; {
                team: { id: context.forTeam.id },
                organization: { id: context.user.orgId },
                activityPerformedBy: { id: context.user.sub }, // TODO: Change this to the user who is evaluating the team
                entityId: context.ticket.id,
                entityUid: context.ticket.uid,
                entityType: AuditLogEntityType.TICKET,
                op: AuditLogOp.INFO,
                visibility: AuditLogVisibility.ORGANIZATION,
                activity: &#x60;No available user found for ticket: ${context.ticket.id}&#x60;,
                description: &#x60;No available user found for ticket ${context.ticket.id} based on the configured strategy for team ${context.forTeam.name} therefore the ticket was not routed to any user.&#x60;,
              };

              // Record the audit log
              await this.activitiesService.recordAuditLog(auditLog);

              context.exitEarly &#x3D; true;
            } else {
              context.evaluatedUser &#x3D; user;
              this.eventEmitter.emit(&quot;ticket.user.evaluated&quot;, {
                ticketId: context.ticket.id,
                userId: user?.id,
              });
            }

            return TicketRoutingState.USER_EVALUATED;
          }

          break;

        // User evaluated state
        case TicketRoutingState.USER_EVALUATED:
          if (
            event &#x3D;&#x3D;&#x3D; TicketRoutingEvent.ASSIGN_USER &amp;&amp;
            context.evaluatedUser
          ) {
            // Update the ticket with the assigned user
            await this.transactionService.runInTransaction(
              async (txnContext) &#x3D;&gt; {
                const oldAssignedAgentId &#x3D; context.ticket.assignedAgentId;

                // Update the ticket with the evaluated team
                await this.ticketRepository.updateWithTxn(
                  txnContext,
                  { id: context.ticket.id },
                  { assignedAgentId: context.evaluatedUser.id },
                );

                // Create an audit log
                const auditLog: DeepPartial&lt;AuditLog&gt; &#x3D; {
                  team: { id: context.forTeam.id },
                  organization: { id: context.user.orgId },
                  activityPerformedBy: { id: context.user.sub }, // TODO: Change this to the user who is evaluating the team
                  entityId: context.ticket.id,
                  entityUid: context.ticket.uid,
                  entityType: AuditLogEntityType.TICKET,
                  op: AuditLogOp.UPDATED,
                  visibility: AuditLogVisibility.ORGANIZATION,
                  activity: &#x60;The user was assigned to ticket ${context.ticket.id}&#x60;,
                  description: &#x60;Based on the routing strategy configured for team ${context.forTeam.name}, the user ${context.evaluatedUser.name} was assigned to ticket ${context.ticket.id}&#x60;,
                  metadata: {
                    updatedFields: [
                      {
                        field: &quot;assignedAgentId&quot;,
                        previousValue: oldAssignedAgentId,
                        updatedToValue: context.evaluatedUser.id,
                      },
                    ],
                  },
                };

                // Record the audit log
                await this.activitiesService.recordAuditLog(
                  auditLog,
                  txnContext,
                );
              },
            );

            // Emit the user assigned event
            this.eventEmitter.emit(&quot;ticket.user.assigned&quot;, {
              ticketId: context.ticket.id,
              userId: context.evaluatedUser.id,
            });

            return TicketRoutingState.USER_ASSIGNED;
          }

          break;
      }

      // If we get here, either the state transition wasn&#x27;t valid or something failed
      throw new Error(&#x60;Invalid state transition: ${currentState} -&gt; ${event}&#x60;);
    } catch (error) {
      context.error &#x3D; error;

      this.eventEmitter.emit(&quot;ticket.routing.failed&quot;, {
        ticketId: context.ticket.id,
        error: error.message,
        state: currentState,
      });

      return TicketRoutingState.FAILED;
    }
  }

  validateRoutingRules(_rules: TeamRoutingRule) {
    // TODO: Implement validation
  }

  /**
   * Validates the existing routing rules.
   * @param rules The routing rules to validate.
   * @param team The team to validate the routing rules for.
   */
  private validateExistingRoutingRules(
    rules: RuleGroup[],
    team: Team,
  ): boolean {
    // If no routing rules are found, return null
    if (!rules) {
      this.logger.warn(
        &#x60;${this.spanId} No routing rules found for team: ${team.uid}&#x60;,
      );

      return false;
    }

    // If no rule groups are found, return null
    if (rules.length &#x3D;&#x3D;&#x3D; 0) {
      this.logger.warn(
        &#x60;${this.spanId} No rule groups found for team: ${team.uid}&#x60;,
      );

      return false;
    }

    return true;
  }

  async executeRouting(
    ticket: Ticket,
    team: Team,
    user: CurrentUser,
  ): Promise&lt;void&gt; {
    // Find the team routing rules
    const teamsRoutingRules &#x3D; await this.teamRoutingRulesRepository.findAll({
      where: {
        team: { id: team.id },
        organization: { id: user.orgId },
      },
    });

    // Map the rules to the format we need
    const mappedRules: RuleGroup[] &#x3D; teamsRoutingRules.map((rule) &#x3D;&gt; {
      return {
        andRules: rule.andRules,
        orRules: rule.orRules,
        parentTeamId: team.id,
        priority: rule.priority,
        resultTeamId: rule.resultTeamId,
        fallbackTeamId: rule.fallbackTeamId,
      };
    });

    // Validate the existing routing rules if they exist
    const validatedRules &#x3D; this.validateExistingRoutingRules(mappedRules, team);

    // Check if the sub teams exist
    const hasSubTeams &#x3D; await this.teamService.checkExists({
      parentTeamId: team.id,
      organizationId: user.orgId,
    });

    // Start the state machine
    let currentState &#x3D; TicketRoutingState.INITIAL;

    // Create the context
    const context: TicketRoutingContext &#x3D; {
      ticket,
      rules: mappedRules,
      user,
      forTeam: team,
    };

    // If there are no sub teams and the rules are not valid, we need to evaluate the user directly
    if (!hasSubTeams &amp;&amp; !validatedRules) {
      // Set the evaluated team to the current team
      context.evaluatedTeam &#x3D; team;

      // Define transitions for direct user evaluation
      const transitions: [TicketRoutingState, TicketRoutingEvent][] &#x3D; [
        [TicketRoutingState.TEAM_ASSIGNED, TicketRoutingEvent.EVALUATE_USER],
        [TicketRoutingState.USER_EVALUATED, TicketRoutingEvent.ASSIGN_USER],
      ];

      currentState &#x3D; TicketRoutingState.TEAM_ASSIGNED; // Jump directly to this state

      // Execute user evaluation transitions
      for (const [expectedState, event] of transitions) {
        // If the current state is not the expected state, log the error and throw an error
        if (currentState !&#x3D;&#x3D; expectedState) {
          // Log the error
          this.logger.error(
            &#x60;${this.spanId} Unexpected state ${currentState}, expected state ${expectedState}&#x60;,
          );

          // Throw an error
          throw new Error(
            &#x60;${this.spanId} Unexpected state ${currentState}, expected state ${expectedState}&#x60;,
          );
        }

        // If the exit early flag is set, break out of the loop
        if (context.exitEarly) break;

        // Transition to the next state
        currentState &#x3D; await this.transition(currentState, event, context);

        // If the state is failed, throw the error
        if (currentState &#x3D;&#x3D;&#x3D; TicketRoutingState.FAILED) {
          throw context.error;
        }
      }

      return;
    }

    // If the routing rules are not valid, return null
    if (!validatedRules) return null;

    // Define the state transitions
    const transitions: [TicketRoutingState, TicketRoutingEvent][] &#x3D; [
      [TicketRoutingState.INITIAL, TicketRoutingEvent.EVALUATE_TEAM],
      [TicketRoutingState.TEAM_EVALUATED, TicketRoutingEvent.ASSIGN_TEAM],
      [TicketRoutingState.TEAM_ASSIGNED, TicketRoutingEvent.EVALUATE_USER],
      [TicketRoutingState.USER_EVALUATED, TicketRoutingEvent.ASSIGN_USER],
    ];

    // Execute the state transitions
    for (const [expectedState, event] of transitions) {
      // If the current state is not the expected state, log the error and throw an error
      if (currentState !&#x3D;&#x3D; expectedState) {
        // Log the error
        this.logger.error(
          &#x60;${this.spanId} Unexpected state ${currentState}, expected state ${expectedState}&#x60;,
        );

        // Throw an error
        throw new Error(
          &#x60;${this.spanId} Unexpected state ${currentState}, expected state ${expectedState}&#x60;,
        );
      }

      // If the exit early flag is set, break out of the loop
      if (context.exitEarly) break;

      // Transition to the next state
      currentState &#x3D; await this.transition(currentState, event, context);

      // If the state is failed, throw the error
      if (currentState &#x3D;&#x3D;&#x3D; TicketRoutingState.FAILED) {
        throw context.error;
      }
    }
  }
}
</code></pre>
    </div>
</div>








                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'interface';
            var COMPODOC_CURRENT_PAGE_URL = 'TicketRoutingContext.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
