<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content interface">
                   <div class="content-data">













<ol class="breadcrumb">
  <li class="breadcrumb-item">Interfaces</li>
  <li class="breadcrumb-item"
  >
  CombinedTeamConfig</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/teams/services/teams.service.ts</code>
        </p>




        <section data-compodoc="block-index">
            <h3 id="index">Index</h3>
            <table class="table table-sm table-bordered index-table">
                <tbody>
                    <tr>
                        <td class="col-md-4">
                            <h6><b>Properties</b></h6>
                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <ul class="index-list">
                                <li>
                                        <a href="#businessHoursConfig" 
>
                                            businessHoursConfig
                                        </a>
                                </li>
                                <li>
                                        <a href="#teamConfig" 
>
                                            teamConfig
                                        </a>
                                </li>
                                <li>
                                        <a href="#teamId" 
>
                                            teamId
                                        </a>
                                </li>
                            </ul>
                        </td>
                    </tr>
                </tbody>
            </table>
        </section>



            <section data-compodoc="block-properties">
                <h3 id="inputs">Properties</h3>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="businessHoursConfig"></a>
                                        <span class="name "><b>businessHoursConfig</b>
                                            <a href="#businessHoursConfig">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>businessHoursConfig:     <code>BusinessHoursConfig</code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>    <code>BusinessHoursConfig</code>

                                        </td>
                                    </tr>





                        </tbody>
                    </table>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="teamConfig"></a>
                                        <span class="name "><b>teamConfig</b>
                                            <a href="#teamConfig">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>teamConfig:     <code>TeamConfiguration</code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>    <code>TeamConfiguration</code>

                                        </td>
                                    </tr>





                        </tbody>
                    </table>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="teamId"></a>
                                        <span class="name "><b>teamId</b>
                                            <a href="#teamId">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>teamId:         <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                                        </td>
                                    </tr>





                        </tbody>
                    </table>
            </section>
    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  Inject,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  UnauthorizedException,
} from &quot;@nestjs/common&quot;;
import { RedisCacheProvider } from &quot;@repo/nestjs-commons/cache&quot;;
import { ILogger } from &quot;@repo/nestjs-commons/logger&quot;;
import {
  BusinessHoursConfig,
  BusinessHoursConfigRepository,
  CachedBusinessHoursConfigRepository,
  CachedTeamCapacityRepository,
  CachedTeamConfigurationRepository,
  CachedTeamRepository,
  CachedTeamRoutingRulesRepository,
  Team,
  TeamCapacityRepository,
  TeamConfiguration,
  TeamConfigurationRepository,
  TeamMember,
  TeamMemberRepository,
  TeamMemberRole,
  TeamRepository,
  TeamRoutingRules,
  TeamRoutingRulesRepository,
  TransactionContext,
  TransactionService,
  User,
  UserStatus,
  UserType,
} from &quot;@repo/thena-platform-entities&quot;;
import {
  DeepPartial,
  FindOptionsRelations,
  FindOptionsSelect,
  FindOptionsWhere,
  In,
  IsNull,
  Not,
  QueryFailedError,
} from &quot;typeorm&quot;;
import { IdGeneratorUtils } from &quot;../../common&quot;;
import { CACHE_TTL } from &quot;../../common/constants/cache.constants&quot;;
import { POSTGRES_ERROR_CODES } from &quot;../../common/constants/postgres-errors.constants&quot;;
import { CurrentUser } from &quot;../../common/decorators&quot;;
import {
  BusinessDayDto,
  BusinessHoursConfigDto,
  UpdateTimezoneWorkingHoursDto,
} from &quot;../../common/dto&quot;;
import { BusinessHoursValidatorService } from &quot;../../common/services/business-hours-validation.service&quot;;
import { generateIdentifier } from &quot;../../common/utils/identifier-generator.utils&quot;;
import { constructDailyConfigFromCommonSlots } from &quot;../../common/utils/time-slots.utils&quot;;
import { SharedService } from &quot;../../shared/shared.service&quot;;
import { UsersService } from &quot;../../users/services/users.service&quot;;
import { EAGERLY_LOADED_RELATIONS_FOR_TEAM_MEMBERS } from &quot;../constants/team-members.constants&quot;;
import {
  EAGERLY_LOADED_RELATIONS,
  TEAM_ROUTING_RULES_RELATIONS,
} from &quot;../constants/teams.constants&quot;;
import { AddTeamMemberDto } from &quot;../dto/team-member.dto&quot;;
import {
  CreateRoutingRuleGroupDto,
  UpdateRoutingRuleGroupDto,
} from &quot;../dto/team-routing.dto&quot;;
import { CreateTeamDto, UpdateTeamDto } from &quot;../dto/teams.dto&quot;;

export interface CombinedTeamConfig {
  teamId: string;
  teamConfig: TeamConfiguration;
  businessHoursConfig: BusinessHoursConfig;
}

@Injectable()
export class TeamsService {
  constructor(
    @Inject(&quot;CustomLogger&quot;)
    private readonly logger: ILogger,

    // Teams repositories
    private readonly teamRepository: TeamRepository,
    private readonly cachedTeamRepository: CachedTeamRepository,

    // Team members repositories
    private readonly teamMemberRepository: TeamMemberRepository,

    // Team configuration repositories
    private readonly teamConfigurationRepository: TeamConfigurationRepository,
    private readonly cachedTeamConfigurationRepository: CachedTeamConfigurationRepository,

    // Team capacity repositories
    private readonly teamCapacityRepository: TeamCapacityRepository,
    private readonly cachedTeamCapacityRepository: CachedTeamCapacityRepository,

    // Team routing rules repositories
    private readonly teamRoutingRulesRepository: TeamRoutingRulesRepository,
    private readonly cachedTeamRoutingRulesRepository: CachedTeamRoutingRulesRepository,

    // Business hours configuration repositories
    private readonly businessHoursConfigRepository: BusinessHoursConfigRepository,
    private readonly cachedBusinessHoursConfigRepository: CachedBusinessHoursConfigRepository,

    // Injected services
    private readonly cacheProvider: RedisCacheProvider,
    private readonly usersService: UsersService,
    private readonly transactionService: TransactionService,
    private readonly businessHoursValidationService: BusinessHoursValidatorService,
    private readonly sharedService: SharedService,
  ) {}

  /**
   * @internal
   * Finds a team by its team ID.
   * @param teamId The team ID of the team to find.
   * @returns The team.
   */
  findOneByTeamId(
    teamId: string,
    organizationId: string,
    parentTeamId?: string,
    noRelations?: boolean,
  ) {
    const whereClause: FindOptionsWhere&lt;Team&gt; &#x3D; { uid: teamId, organizationId };
    if (parentTeamId) {
      whereClause.parentTeamId &#x3D; parentTeamId;
    }

    return this.cachedTeamRepository.findByCondition({
      where: whereClause,
      relations: noRelations ? [] : EAGERLY_LOADED_RELATIONS,
    });
  }

  /**
   * @internal
   * Checks if a team exists.
   * @param whereClause The where clause to check.
   * @returns Whether the team exists.
   */
  checkExists(whereClause: FindOptionsWhere&lt;Team&gt;) {
    return this.teamRepository.exists({ where: whereClause });
  }

  /**
   * @internal
   * Checks if a user can read a team.
   * @param teamId The team ID.
   * @param user The user.
   * @returns The team and whether the user belongs to the team.
   */
  async canUserReadTeam(teamId: string, user: CurrentUser) {
    // Find the team by its team ID and organization ID
    const team &#x3D; await this.findOneByTeamId(teamId, user.orgId);
    if (!team) {
      throw new NotFoundException(&quot;Team not found!&quot;);
    }

    // Check if the user belongs to the team
    const userBelongsToTeam &#x3D; await this.userBelongsToTeam(
      user.sub,
      team.id,
      user.orgId,
    );

    // If the team is private, perform additional checks
    if (team.isPrivate) {
      // If the user does not belong to the team, throw an error
      if (!userBelongsToTeam) {
        throw new NotFoundException(&quot;Team not found!&quot;);
      }
    }

    return { team, userInTeam: userBelongsToTeam };
  }

  /**
   * @internal
   * Checks if a user can update a team.
   * @param teamId The team ID.
   * @param user The user.
   * @returns The team and whether the user belongs to the team.
   */
  async canUserUpdateTeam(
    teamId: string,
    user: CurrentUser,
    checkOwner: boolean &#x3D; false,
  ) {
    const { team, userInTeam } &#x3D; await this.canUserReadTeam(teamId, user);

    // If the user does not belong to the team, throw an error
    if (!userInTeam) {
      throw new ForbiddenException(
        &quot;You need to be a member of this team to update the resource!&quot;,
      );
    }

    // If the user is checking if they are the team owner, perform the check
    if (checkOwner) {
      // If the user is not the team owner, throw an error
      const isOwner &#x3D; user.sub &#x3D;&#x3D;&#x3D; team.teamOwnerId;
      const isAdmin &#x3D; userInTeam.role &#x3D;&#x3D;&#x3D; TeamMemberRole.ADMIN;
      if (!isOwner &amp;&amp; !isAdmin) {
        throw new ForbiddenException(
          &quot;Only the team owner or admins can perform this action!&quot;,
        );
      }
    }

    return { team, userInTeam };
  }

  async getTeamsByUser(
    user: CurrentUser,
    select?: FindOptionsSelect&lt;TeamMember&gt;,
    relations?: FindOptionsRelations&lt;TeamMember&gt;,
  ) {
    const teams &#x3D; await this.teamMemberRepository.findAll({
      where: { userId: user.sub, organizationId: user.orgId },
      select,
      relations,
    });

    return teams;
  }

  /**
   * @internal
   * Gets a user and team by their IDs and organization ID. This also validates if the user belongs to the team.
   * @param teamId The team ID.
   * @param userId The user ID.
   * @param organizationId The organization ID.
   * @returns The user and team.
   */
  async getUserAndTeam(teamId: string, userId: string, organizationId: string) {
    // Look up the team if it exists
    const team &#x3D; await this.findOneByTeamId(teamId, organizationId);
    if (!team) {
      throw new NotFoundException(&quot;Team not found!&quot;);
    }

    // Look up the team member if it exists
    const teamMember &#x3D; await this.teamMemberRepository.findByCondition({
      where: { teamId: team.id, userId, organizationId },
    });

    // If the user is not found in the team, throw an error
    if (!teamMember) {
      throw new ForbiddenException(&quot;User is not a member of this team!&quot;);
    }

    return { team };
  }

  /**
   * @internal
   * Creates a new team member.
   * @param teamMember The team member data to create.
   * @returns The created team member.
   */
  createTeamMember(
    teamMember: Partial&lt;TeamMember&gt;,
    context?: TransactionContext,
  ): Promise&lt;TeamMember&gt; {
    const newTeamMember &#x3D; this.teamMemberRepository.create(teamMember);
    if (context) {
      return this.teamMemberRepository.saveWithTxn(context, newTeamMember);
    }

    return this.teamMemberRepository.save(newTeamMember);
  }

  /**
   * @internal
   * Finds teams by their public IDs.
   * @param teamIds The team IDs to find.
   * @param organizationId The organization ID.
   * @returns The teams.
   */
  findTeamsByPublicIds(teamIds: string[], organizationId: string) {
    return this.teamRepository.findAll({
      where: { uid: In(teamIds), organizationId },
    });
  }

  /**
   * @private
   * Finds a team by its team ID from the request.
   * @param teamId The team ID of the team to find.
   * @param request The request object.
   * @returns The team.
   */
  private async findTeamFromUser(teamId: string, user: CurrentUser) {
    // Find the team by its team ID and organization ID
    const team &#x3D; await this.findOneByTeamId(teamId, user.orgId);
    return team;
  }

  /**
   * @internal
   * Checks if a user belongs to a team.
   * @param userId The user ID.
   * @param teamId The team ID.
   * @param organizationId The organization ID.
   * @returns Whether the user belongs to the team.
   */
  async userBelongsToTeam(
    userId: string,
    teamId: string,
    organizationId: string,
    options: {
      relations?: FindOptionsRelations&lt;TeamMember&gt;;
    } &#x3D; {},
  ) {
    const teamMember &#x3D; await this.teamMemberRepository.findByCondition({
      where: { organizationId, userId, teamId },
      relations: options.relations,
    });

    return teamMember;
  }

  /**
   * @internal
   * Removes a team member from a team.
   * @param teamMemberId The team member ID to remove.
   * @returns The removed team member.
   */
  async removeTeamMember(teamMemberId: string, teamId: string) {
    // Find the team member to remove
    const teamMemberToRemove &#x3D; await this.teamMemberRepository.findByCondition({
      where: { userId: teamMemberId, teamId },
    });

    // If the team member is not found, throw an error
    if (!teamMemberToRemove) {
      throw new NotFoundException(
        &quot;Team member not found! Or not a member of this team.&quot;,
      );
    }

    // Remove the team member
    const removedTeamMember &#x3D; await this.teamMemberRepository.remove(
      teamMemberToRemove,
    );

    return removedTeamMember;
  }

  /**
   * Finds a team by its team ID.
   * @param teamId The team ID of the team to find.
   * @returns The team.
   */
  async findOneTeamById(teamId: string, user: CurrentUser) {
    const { team } &#x3D; await this.canUserReadTeam(teamId, user);
    return team;
  }

  /**
   * Finds the members of a team.
   * @param teamId The team ID of the team to find the members of.
   * @param request The request object.
   * @returns The team members.
   */
  async findTeamMembers(teamId: string, user: CurrentUser) {
    const { team } &#x3D; await this.canUserReadTeam(teamId, user);

    // Find all team members for the team
    const teamMembers &#x3D; await this.teamMemberRepository.findAll({
      where: { teamId: team.id },
      relations: EAGERLY_LOADED_RELATIONS_FOR_TEAM_MEMBERS,
    });

    return teamMembers;
  }

  /**
   * Finds all active team members for a team.
   * @param teamId The team ID of the team to find the members of.
   * @param user The user making the request.
   * @returns The team members.
   */
  async findActiveTeamMembers(teamId: string, user: CurrentUser) {
    const team &#x3D; await this.findTeamFromUser(teamId, user);
    if (!team) {
      throw new NotFoundException(&quot;Team not found!&quot;);
    }

    // Find all team members for the team
    const members &#x3D; await this.teamMemberRepository.findAll({
      where: {
        teamId: team.id,
        organizationId: user.orgId,
        user: { status: UserStatus.ACTIVE },
      },
      relations: {
        user: { businessHoursConfig: true, userTeamCapacity: true },
      },
    });

    return members;
  }

  /**
   * Finds the team capacity for a team.
   * @param teamId The team ID of the team to find the capacity of.
   * @param user The user making the request.
   * @returns The team capacity.
   */
  async getTeamCapacity(teamId: string, user: CurrentUser) {
    // Find the team by its team ID and organization ID
    const team &#x3D; await this.findOneByTeamId(teamId, user.orgId);
    if (!team) {
      throw new NotFoundException(&quot;Team not found!&quot;);
    }

    // Find the team capacity by its team ID and organization ID
    const teamCapacity &#x3D;
      await this.cachedTeamCapacityRepository.findByCondition({
        where: { team: { id: team.id }, organization: { id: user.orgId } },
      });

    return teamCapacity;
  }

  /**
   * Finds all the sub teams that belong to a parent team
   * @param user Currently logged in user
   * @param parentTeamId UID of the parent team
   * @return The teams
   */
  async findAllSubTeams(user: CurrentUser, parentTeamId: string) {
    // Find the parent team first
    const parentTeam &#x3D; await this.cachedTeamRepository.findByCondition({
      where: { uid: parentTeamId, organizationId: user.orgId },
    });

    // Find the sub-teams
    const subTeams &#x3D; await this.findAllTeams(user, parentTeam.id);
    return subTeams;
  }

  /**
   * Finds all teams for the user
   * @returns The teams.
   */
  async findAllTeams(user: CurrentUser, parentTeamId?: string) {
    // Construct the initial where clause
    let whereClause: FindOptionsWhere&lt;TeamMember&gt; &#x3D; {
      organizationId: user.orgId,
      userId: user.sub,
    };

    // If parent team id is provided add it as a filter
    if (parentTeamId) {
      whereClause &#x3D; {
        ...whereClause,
        team: { parentTeamId, deletedAt: IsNull() },
      };
    }

    // Find all teams for the organization
    const userInTeams &#x3D; await this.teamMemberRepository.findAll({
      where: whereClause,
      relations: {
        team: {
          parentTeam: true,
          teamOwner: true,
        },
      },
    });

    // Format the teams
    const teams &#x3D; userInTeams
      .map((userInTeam) &#x3D;&gt; userInTeam.team)
      .filter(Boolean);

    // Return the teams
    return teams;
  }

  /**
   * Finds all public teams for the organization.
   * @returns The teams.
   */
  async getAllPublicTeams(user: CurrentUser) {
    const teams &#x3D; await this.teamRepository.findAll({
      where: { organizationId: user.orgId, isPrivate: false },
      relations: { parentTeam: true },
    });

    return teams;
  }

  /**
   * Creates a new team.
   * @param createTeamDto The team data to create.
   * @returns The created team.
   */
  async createTeam(createTeamDto: CreateTeamDto, user: CurrentUser) {
    // If the user is not an organization admin, throw an error
    if (user.userType !&#x3D;&#x3D; UserType.ORG_ADMIN) {
      throw new ForbiddenException(
        &quot;Only organization admins can create teams!&quot;,
      );
    }

    try {
      const createdTeam &#x3D; await this.transactionService.runInTransaction(
        async (txnContext) &#x3D;&gt; {
          // Generate a unique identifier for the team
          const teamId &#x3D; this.generateTeamIdentifier();

          // Get the user email from the user object attached to the request
          const userEmail &#x3D; user.email;
          if (!userEmail) {
            throw new UnauthorizedException(&quot;User is not authenticated!&quot;);
          }

          // If the identifier is not provided, generate a unique identifier for the team
          if (!createTeamDto.identifier) {
            createTeamDto.identifier &#x3D; generateIdentifier(createTeamDto.name);
          }

          // If the parent team ID is provided, find the parent team
          let parentTeam: Team | null;
          if (createTeamDto.parentTeamId) {
            parentTeam &#x3D; await this.findOneByTeamId(
              createTeamDto.parentTeamId,
              user.orgId,
            );

            // If the parent team is a sub-team, throw an error
            if (parentTeam?.parentTeamId) {
              throw new BadRequestException(
                &quot;Cannot create a sub-team inside a sub-team!&quot;,
              );
            }

            // If the parent team is not found, throw an error
            if (!parentTeam) {
              throw new NotFoundException(&quot;Parent team not found!&quot;);
            }
          }

          // Create the team
          const newTeam &#x3D; await this.teamRepository.saveWithTxn(txnContext, {
            uid: teamId,
            identifier: createTeamDto.identifier,
            name: createTeamDto.name,
            description: createTeamDto.description,
            teamOwnerId: user.sub,
            organizationId: user.orgId,
            parentTeamId: parentTeam?.id,
            isPrivate: createTeamDto?.isPrivate,
          });

          // Create team&#x27;s business hours config
          const businessHoursConfig &#x3D;
            await this.businessHoursConfigRepository.saveWithTxn(txnContext, {
              teamId: newTeam.id,
              organizationId: user.orgId,
              monday: {
                isActive: true,
                slots: [{ start: &quot;00:00&quot;, end: &quot;23:59&quot; }],
              },
              tuesday: {
                isActive: true,
                slots: [{ start: &quot;00:00&quot;, end: &quot;23:59&quot; }],
              },
              wednesday: {
                isActive: true,
                slots: [{ start: &quot;00:00&quot;, end: &quot;23:59&quot; }],
              },
              thursday: {
                isActive: true,
                slots: [{ start: &quot;00:00&quot;, end: &quot;23:59&quot; }],
              },
              friday: {
                isActive: true,
                slots: [{ start: &quot;00:00&quot;, end: &quot;23:59&quot; }],
              },
              saturday: {
                isActive: true,
                slots: [{ start: &quot;00:00&quot;, end: &quot;23:59&quot; }],
              },
              sunday: {
                isActive: true,
                slots: [{ start: &quot;00:00&quot;, end: &quot;23:59&quot; }],
              },
            });

          // Create team configurations
          const teamConfigurations &#x3D;
            await this.teamConfigurationRepository.saveWithTxn(txnContext, {
              teamId: newTeam.id,
              organizationId: user.orgId,
              businessHoursConfigId: businessHoursConfig.id,
            });

          // Update the team with its configs
          await this.teamRepository.updateWithTxn(
            txnContext,
            { id: newTeam.id },
            { configurationId: teamConfigurations.id },
          );

          // Seed default status, priority and type if the team is not a sub-team
          if (!createTeamDto.parentTeamId) {
            await this.sharedService.seedDefaultStatusPriorityAndType(
              newTeam,
              txnContext,
            );
          }

          // Create the team member and make him the member
          await this.createTeamMember(
            {
              teamId: newTeam.id,
              userId: user.sub,
              isActive: true,
              organizationId: user.orgId,
              role: TeamMemberRole.ADMIN,
            },
            txnContext,
          );

          return newTeam;
        },
      );

      const returnableTeam &#x3D; await this.findOneTeamById(createdTeam.uid, user);
      return returnableTeam;
    } catch (error) {
      if (error instanceof QueryFailedError) {
        if (error.code &#x3D;&#x3D;&#x3D; POSTGRES_ERROR_CODES.DUPLICATE_KEY_VALUE) {
          // If the error is due to a duplicate team name, throw an error
          if ((error as any)?.constraint &#x3D;&#x3D;&#x3D; &quot;unique_team_name_ex_deleted&quot;) {
            throw new ConflictException(&quot;Team name already exists!&quot;);
          }

          // If the error is due to a duplicate team ID, throw an error
          if ((error as any)?.constraint &#x3D;&#x3D;&#x3D; &quot;unique_team_id&quot;) {
            throw new InternalServerErrorException(
              &quot;Something went wrong while creating the team! Please try again.&quot;,
            );
          }
        }
      }

      // Propagate the error
      throw error;
    }
  }

  /**
   * Updates a team.
   * @param teamId The team ID to update.
   * @param updateTeamDto The team data to update.
   * @param user The user making the request.
   * @returns The updated team.
   */
  async updateTeam(
    teamId: string,
    updateTeamDto: UpdateTeamDto,
    user: CurrentUser,
  ) {
    // Find the team by its team ID and organization ID
    const { team } &#x3D; await this.canUserUpdateTeam(teamId, user, true);

    // If the identifier is provided, check if it is unique
    if (updateTeamDto.identifier) {
      const identifierExists &#x3D; await this.teamRepository.exists({
        where: {
          identifier: updateTeamDto.identifier,
          organizationId: user.orgId,
          id: Not(team.id),
        },
      });

      // If the identifier already exists, throw an error
      if (identifierExists) {
        throw new ConflictException(
          &quot;Identifier already exists for another team!&quot;,
        );
      }
    }

    // Update the provided team with the new data
    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      // Update the team
      const updateTeamResult &#x3D; await this.teamRepository.updateWithTxn(
        txnContext,
        { id: team.id },
        {
          name: updateTeamDto.name,
          description: updateTeamDto.description,
          isPrivate: updateTeamDto.isPrivate,
          icon: updateTeamDto.icon,
          color: updateTeamDto.color,
          identifier: updateTeamDto.identifier,
        },
      );

      // Invalidate the query
      await this.cachedTeamRepository.invalidateQuery({
        where: { uid: teamId, organizationId: user.orgId },
        relations: EAGERLY_LOADED_RELATIONS,
      });

      return updateTeamResult;
    });

    // Fetch the returnable team
    const returnableTeam &#x3D; await this.findOneTeamById(teamId, user);
    return returnableTeam;
  }

  /**
   * Deletes a team.
   * @param teamId The team ID to delete.
   * @param request The request object.
   * @returns The deleted team.
   */
  async deleteOneTeam(teamId: string, user: CurrentUser) {
    // Find the team by its team ID and organization ID
    const { team } &#x3D; await this.canUserUpdateTeam(teamId, user, true);

    // Delete team and team configurations in a transaction
    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      const commonOptions &#x3D; {
        id: team.configurationId,
        teamId: team.id,
        organizationId: user.orgId,
      };

      // Delete the business hours config
      await this.cachedBusinessHoursConfigRepository.softDeleteWithTxn(
        txnContext,
        commonOptions,
      );

      // Delete the team configurations
      await this.cachedTeamConfigurationRepository.softDeleteWithTxn(
        txnContext,
        commonOptions,
      );

      // Delete the team members
      await this.teamMemberRepository.softRemoveByConditionTxn(txnContext, {
        where: { teamId: team.id, organizationId: user.orgId },
      });

      // Delete the team
      await this.cachedTeamRepository.softDeleteWithTxn(txnContext, {
        id: team.id,
        uid: team.uid,
        organizationId: user.orgId,
      });

      await this.cachedTeamRepository.invalidateTeamCacheWithKey({
        where: { uid: team.uid, organizationId: user.orgId },
        relations: EAGERLY_LOADED_RELATIONS,
      });
    });
  }

  /**
   * Adds a team member to a team.
   * @param teamId The team ID to add the member to.
   * @param addTeamMemberDto The team member data to add.
   * @param request The request object.
   * @returns The added team member.
   */
  async addMemberToTeam(
    teamId: string,
    addTeamMemberDto: AddTeamMemberDto,
    user: CurrentUser,
  ) {
    let userToAdd: User | null;

    // Find the user by their email address
    if (addTeamMemberDto.email) {
      userToAdd &#x3D; await this.usersService.findOneByEmail(
        addTeamMemberDto.email,
        user.orgId,
      );
    } else if (addTeamMemberDto.userId) {
      userToAdd &#x3D; await this.usersService.findOneByPublicId(
        addTeamMemberDto.userId,
        user.orgId,
      );
    }

    // If the user to add is not found, throw an error
    if (!userToAdd) {
      throw new NotFoundException(&quot;User not found!&quot;);
    }

    // Find the team by its team ID and organization ID
    const team &#x3D; await this.findOneByTeamId(teamId, user.orgId);
    if (!team) {
      throw new NotFoundException(&quot;Team not found!&quot;);
    }

    // Check if the user who is inviting and the user to add belong to the same organization
    const teamOrg &#x3D; team.organizationId;
    if (teamOrg !&#x3D;&#x3D; user.orgId &amp;&amp; teamOrg !&#x3D;&#x3D; userToAdd.organizationId) {
      throw new ForbiddenException(&quot;Invalid foreign organization invite!&quot;);
    }

    // Check if the user who is inviting belongs to the team
    const userBelongsToTeam &#x3D; await this.userBelongsToTeam(
      user.sub,
      team.id,
      user.orgId,
    );

    // Checks to perform if the team is private
    if (team.isPrivate) {
      // User is self-adding to a private team, throw a Not Found Exception
      // to prevent user from checking if the team exists
      if (userToAdd.id &#x3D;&#x3D;&#x3D; user.sub) {
        throw new NotFoundException(&quot;Team not found!&quot;);
      }

      // If the user is not the team owner, throw an error
      if (user.sub !&#x3D;&#x3D; team.teamOwnerId) {
        throw new ForbiddenException(
          &quot;Only the team owner can invite new members to a private team!&quot;,
        );
      }

      // If the user does not belong to the team, throw an error
      if (!userBelongsToTeam) {
        throw new NotFoundException(&quot;Team not found!&quot;);
      }
    }

    // If the user does not belong to the team, throw an error
    if (!userBelongsToTeam &amp;&amp; user.sub !&#x3D;&#x3D; userToAdd.id) {
      throw new ForbiddenException(
        &quot;You need to be a member of this team to invite new members!&quot;,
      );
    }

    // CHECK: If the user is not an admin and is trying to add an admin, throw an error
    // NOTE: Do not check if the user&#x27;s role is MEMBER because you might end up adding
    // more roles but the admin remains admin therefore the NOT check is better
    const isMember &#x3D;
      userBelongsToTeam &amp;&amp; userBelongsToTeam.role !&#x3D;&#x3D; TeamMemberRole.ADMIN;
    if (addTeamMemberDto.isAdmin &amp;&amp; isMember) {
      throw new ForbiddenException(
        &quot;Only admins can add new admins to the team!&quot;,
      );
    }

    try {
      // Create the team member
      const teamMember &#x3D; await this.createTeamMember({
        teamId: team.id,
        userId: userToAdd.id,
        invitedById: user.sub,
        organizationId: user.orgId,
      });

      // If the team has a parent team, add the user to the parent team
      if (team.parentTeamId) {
        await this.createTeamMember({
          teamId: team.parentTeamId,
          userId: userToAdd.id,
          invitedById: user.sub,
          organizationId: user.orgId,
        });
      }

      // Find the team member by its ID
      const returnableTeamMember &#x3D;
        await this.teamMemberRepository.findByCondition({
          where: {
            id: teamMember.id,
            teamId: team.id,
            userId: userToAdd.id,
            organizationId: user.orgId,
          },
          relations: EAGERLY_LOADED_RELATIONS_FOR_TEAM_MEMBERS,
        });

      return returnableTeamMember;
    } catch (error) {
      if (error instanceof QueryFailedError) {
        if (error.code &#x3D;&#x3D;&#x3D; POSTGRES_ERROR_CODES.DUPLICATE_KEY_VALUE) {
          // If the error is due to a duplicate team name, throw an error
          if ((error as any)?.constraint &#x3D;&#x3D;&#x3D; &quot;unique_team_member&quot;) {
            throw new ConflictException(
              &quot;This user is already a member of this team!&quot;,
            );
          }
        }
      }

      throw error;
    }
  }

  /**
   * Removes a team member from a team.
   * @param teamId The team ID to remove the member from.
   * @param memberId The member ID to remove from the team.
   * @param request The request object.
   * @returns The removed team member.
   */
  async removeMemberFromTeam(
    teamId: string,
    memberId: string,
    user: CurrentUser,
  ) {
    // Find the user to remove and the team
    const [userToRemove, team] &#x3D; await Promise.all([
      this.usersService.findOneByPublicId(memberId),
      this.findOneByTeamId(teamId, user.orgId),
    ]);

    // If the user to remove is not found, throw an error
    if (!userToRemove) {
      throw new NotFoundException(&quot;User not found!&quot;);
    }

    // If the team is not found, throw an error
    if (!team) {
      throw new NotFoundException(&quot;Team not found!&quot;);
    }

    // Check if the user belongs to the team
    const userBelongsToTeam &#x3D; await this.userBelongsToTeam(
      user.sub,
      team.id,
      user.orgId,
    );

    // Checks to perform if the team is private
    if (team.isPrivate) {
      // If the user does not belong to the team, throw an error
      if (!userBelongsToTeam) {
        throw new NotFoundException(&quot;Team not found!&quot;);
      }
    }

    // If the user is not the team owner, throw an error
    if (user.sub !&#x3D;&#x3D; team.teamOwnerId &amp;&amp; user.uid !&#x3D;&#x3D; memberId) {
      throw new ForbiddenException(
        &quot;Only the team owner can remove members from the team!&quot;,
      );
    }

    // If the user to remove is the team owner, throw an error
    if (team.teamOwner.uid &#x3D;&#x3D;&#x3D; memberId) {
      // If the user trying to delete the team owner is not the team owner
      if (user.sub !&#x3D;&#x3D; team.teamOwnerId) {
        throw new ForbiddenException(&quot;Cannot remove the team owner!&quot;);
      }

      // If the user is the team owner, throw an error
      throw new ForbiddenException(
        &quot;You are the owner of this team, please archive or delete the team instead.&quot;,
      );
    }

    // Remove the team member
    await this.removeTeamMember(userToRemove.id, team.id);
  }

  /**
   * Gets a team&#x27;s configurations.
   * @param teamId The team ID to get the configurations of.
   * @param user The user making the request.
   * @returns The team configurations.
   */
  async getTeamConfigurations(
    teamId: string,
    user: CurrentUser,
  ): Promise&lt;CombinedTeamConfig&gt; {
    const { team } &#x3D; await this.canUserUpdateTeam(teamId, user, true);

    const lookupClause &#x3D; { where: { teamId: team.id } };
    const [teamConfig, businessHoursConfig] &#x3D; await Promise.all([
      this.cachedTeamConfigurationRepository.findByCondition(lookupClause),
      this.cachedBusinessHoursConfigRepository.findByCondition(lookupClause),
    ]);

    return {
      teamId: team.uid,
      teamConfig,
      businessHoursConfig,
    };
  }

  /**
   * Updates a team&#x27;s configurations and business hours.
   * @param teamId The team ID to update the configurations of.
   * @param updateTeamsDto The team configurations data to update.
   * @param user The user making the request.
   * @returns The updated team configurations.
   */
  async updateTeamConfigurations(
    teamId: string,
    updateTeamsDto: UpdateTimezoneWorkingHoursDto,
    user: CurrentUser,
  ) {
    const {
      holidays,
      timezone,
      dailyConfig,
      commonDailyConfig,
      commonSlots,
      routingRespectsTimezone,
      routingRespectsUserTimezone,
      routingRespectsUserAvailability,
      userRoutingStrategy,
    } &#x3D; updateTeamsDto;

    // Check if the user can update the team
    const { team } &#x3D; await this.canUserUpdateTeam(teamId, user, true);

    const currentTeamConfig &#x3D;
      await this.cachedTeamConfigurationRepository.findByCondition({
        where: { teamId: team.id },
      });

    // If the common daily config is enabled, validate configurations
    if (commonDailyConfig) {
      // If the common slots are not provided, throw an error
      if (!commonSlots) {
        throw new BadRequestException(
          &quot;Common slots are required when common daily config is enabled!&quot;,
        );
      }

      // If the daily config is provided, throw an error
      if (dailyConfig) {
        throw new BadRequestException(
          &quot;Cannot update both common daily config and daily config!&quot;,
        );
      }
    }

    // If the business hours are being updated, validate them
    if (dailyConfig) {
      const tz &#x3D; timezone || currentTeamConfig.timezone || &quot;UTC&quot;;
      const validationResult &#x3D;
        this.businessHoursValidationService.validateBusinessHours(
          dailyConfig,
          tz,
        );

      // If the business hours are invalid, throw an error
      if (!validationResult.isValid) {
        throw new BadRequestException(validationResult.error);
      }
    }

    // Update the team configurations in a transaction
    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      const commonOptions &#x3D; {
        id: team.configurationId,
        teamId: team.id,
        organizationId: user.orgId,
      };

      const commonCacheOptions &#x3D; {
        teamId: team.id,
      };

      // Update the team&#x27;s timezone
      await this.teamConfigurationRepository.updateWithTxn(
        txnContext,
        commonOptions,
        {
          holidays,
          timezone,
          routingRespectsTimezone,
          routingRespectsUserTimezone,
          routingRespectsUserAvailability,
          userRoutingStrategy,
        },
      );

      // Update the business hours config
      if (dailyConfig) {
        await this.businessHoursConfigRepository.updateWithTxn(
          txnContext,
          { organizationId: user.orgId, teamId: team.id },
          { ...dailyConfig, commonDailyConfig: false },
        );

        // Invalidate the business hours cache
        await this.cachedBusinessHoursConfigRepository.invalidateBusinessHoursCache(
          commonCacheOptions,
        );
      }

      // If the common daily config is enabled, update the configurations
      if (commonDailyConfig &amp;&amp; commonSlots) {
        const dailyConfig &#x3D; constructDailyConfigFromCommonSlots(commonSlots);
        await this.businessHoursConfigRepository.updateWithTxn(
          txnContext,
          { organizationId: user.orgId, teamId: team.id },
          { ...dailyConfig, commonDailyConfig },
        );

        // Invalidate the business hours cache
        await this.cachedBusinessHoursConfigRepository.invalidateBusinessHoursCache(
          commonCacheOptions,
        );
      }

      // Invalidate the team configuration cache
      await this.cachedTeamConfigurationRepository.invalidateTeamConfigurationCache(
        commonCacheOptions,
      );
    });

    const lookupClause &#x3D; { where: { teamId: team.id } };
    const [teamConfig, businessHoursConfig] &#x3D; await Promise.all([
      this.cachedTeamConfigurationRepository.findByCondition(lookupClause),
      this.cachedBusinessHoursConfigRepository.findByCondition(lookupClause),
    ]);

    // If the business hours or team configurations are not found, throw an error
    if (!businessHoursConfig || !teamConfig) {
      throw new NotFoundException(
        &quot;Team configurations are in an inconsistent state!&quot;,
      );
    }

    return {
      teamId: team.uid,
      teamConfig,
      businessHoursConfig,
    };
  }

  /**
   * Creates a routing rule for a team.
   * @param teamId The team ID to create the routing rule for.
   * @param user The user making the request.
   * @param createRoutingRuleDto The routing rule data to create.
   * @returns The created routing rule.
   */
  async createRoutingRule(
    teamId: string,
    user: CurrentUser,
    createRoutingRuleDto: CreateRoutingRuleGroupDto,
  ) {
    // Check if the user have permission to create team routing rules
    const { team } &#x3D; await this.canUserUpdateTeam(teamId, user, true);

    // Does sub-teams exist
    const doesSubTeamsExist &#x3D; await this.teamRepository.exists({
      where: {
        parentTeamId: team.id,
        organizationId: user.orgId,
      },
    });

    // If sub-teams do not exist, throw an error since we cannot create routing
    // rules for a team that does not have sub-teams
    if (!doesSubTeamsExist) {
      throw new BadRequestException(
        &quot;Cannot create routing rules for a team that does not have sub-teams!&quot;,
      );
    }

    // Check if the result team exists
    const doesResultTeamExists &#x3D; await this.teamRepository.exists({
      where: {
        uid: createRoutingRuleDto.resultTeamId,
        organizationId: user.orgId,
        parentTeamId: team.id,
      },
    });

    // If the result team does not exist, throw an error
    if (!doesResultTeamExists) {
      throw new NotFoundException(&quot;Result team not found!&quot;);
    }

    // Check if the AND or OR rules are provided
    if (!createRoutingRuleDto.andRules &amp;&amp; !createRoutingRuleDto.orRules) {
      throw new BadRequestException(
        &quot;At least one set of rules are required to create a routing rule!&quot;,
      );
    }

    // Get the last rule
    const previousRules &#x3D; await this.teamRoutingRulesRepository.findAll({
      where: { team: { id: team.id }, organization: { id: user.orgId } },
      order: { priority: &quot;DESC&quot; },
      take: 1,
    });

    // Get the last rule and its priority
    let lastRule: TeamRoutingRules;
    let lastRulePriority &#x3D; 0;
    if (previousRules.length &gt; 0) {
      lastRule &#x3D; previousRules?.[0];
      lastRulePriority &#x3D; lastRule?.priority;
    }

    // Create the routing rules in txn
    const rule &#x3D; await this.transactionService.runInTransaction(
      async (txnContext) &#x3D;&gt; {
        const ruleData: DeepPartial&lt;TeamRoutingRules&gt; &#x3D; {
          name: createRoutingRuleDto.name,
          description: createRoutingRuleDto.description,
          team: { id: team.id },
          organization: { id: user.orgId },
          createdBy: { id: user.sub },
          andRules: createRoutingRuleDto.andRules,
          orRules: createRoutingRuleDto.orRules,
          priority:
            createRoutingRuleDto.evaluationOrder ?? lastRulePriority + 1,
          resultTeamId: createRoutingRuleDto.resultTeamId,
        };

        // Create the routing rule
        const routingRule &#x3D; await this.teamRoutingRulesRepository.saveWithTxn(
          txnContext,
          ruleData,
        );

        return routingRule;
      },
    );

    // Get the returnable rule
    const returnableRule &#x3D;
      await this.cachedTeamRoutingRulesRepository.findByCondition({
        where: { id: rule.id },
        relations: TEAM_ROUTING_RULES_RELATIONS,
      });

    // Invalidate and re-cache the team routing rules
    this.invalidateAndReCacheTeamRoutingRules(team.id, user.orgId);

    // Invalidate the team routing rules cache
    return returnableRule;
  }

  /**
   * Updates a routing rule for a team.
   * @param teamId The team ID to update the routing rule for.
   * @param ruleId The rule ID to update.
   * @param user The user making the request.
   * @param updateRoutingRuleDto The routing rule data to update.
   * @returns The updated routing rule.
   */
  async updateRoutingRule(
    teamId: string,
    ruleId: string,
    user: CurrentUser,
    updateRoutingRuleDto: UpdateRoutingRuleGroupDto,
  ) {
    // Check if the user can update the team
    const { team } &#x3D; await this.canUserUpdateTeam(teamId, user, true);

    // Check if the rule exists
    const rule &#x3D; await this.teamRoutingRulesRepository.exists({
      where: {
        uid: ruleId,
        team: { id: team.id },
        organization: { id: user.orgId },
      },
    });

    // If the rule does not exist, throw an error
    if (!rule) {
      throw new NotFoundException(&quot;Rule not found!&quot;);
    }

    // Check if the result team exists
    if (updateRoutingRuleDto.resultTeamId) {
      const doesResultTeamExists &#x3D; await this.teamRepository.exists({
        where: {
          uid: updateRoutingRuleDto.resultTeamId,
          organizationId: user.orgId,
          parentTeamId: team.id,
        },
      });

      // If the result team does not exist, throw an error
      if (!doesResultTeamExists) {
        throw new NotFoundException(&quot;Result team not found!&quot;);
      }
    }

    // Update the routing rule in a transaction
    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      const update: DeepPartial&lt;TeamRoutingRules&gt; &#x3D; {};

      // Update the name
      if (updateRoutingRuleDto.name) {
        update.name &#x3D; updateRoutingRuleDto.name;
      }

      // Update the description
      if (updateRoutingRuleDto.description) {
        update.description &#x3D; updateRoutingRuleDto.description;
      }

      // Update the result team ID
      if (updateRoutingRuleDto.resultTeamId) {
        update.resultTeamId &#x3D; updateRoutingRuleDto.resultTeamId;
      }

      // Update the evaluation order
      if (updateRoutingRuleDto.evaluationOrder) {
        update.priority &#x3D; updateRoutingRuleDto.evaluationOrder;
      }

      // Update the AND rules
      if (updateRoutingRuleDto.andRules) {
        update.andRules &#x3D; updateRoutingRuleDto.andRules;
      }

      // Update the OR rules
      if (updateRoutingRuleDto.orRules) {
        update.orRules &#x3D; updateRoutingRuleDto.orRules;
      }

      // Update the routing rule
      await this.teamRoutingRulesRepository.updateWithTxn(
        txnContext,
        {
          uid: ruleId,
          team: { id: team.id },
          organization: { id: user.orgId },
        },
        update,
      );

      return update;
    });

    // Get the returnable rule
    const returnableRule &#x3D;
      await this.teamRoutingRulesRepository.findByCondition({
        where: {
          uid: ruleId,
          organization: { id: user.orgId },
          team: { id: team.id },
        },
        relations: TEAM_ROUTING_RULES_RELATIONS,
      });

    // Invalidate and re-cache the team routing rules
    this.invalidateAndReCacheTeamRoutingRules(team.id, user.orgId);

    return returnableRule;
  }

  /**
   * Gets a team&#x27;s routing rules.
   * @param teamId The team ID to get the routing rules of.
   * @param user The user making the request.
   * @returns The team&#x27;s routing rules.
   */
  async getTeamRoutingRules(teamId: string, user: CurrentUser) {
    // Check if the user can update the team
    const { team } &#x3D; await this.canUserUpdateTeam(teamId, user);

    // Get the team routing rules from cache
    const cacheKey &#x3D; &#x60;team-routing-${team.id}-${user.orgId}&#x60;;
    const cachedRoutingRules &#x3D; await this.cacheProvider.get&lt;string&gt;(cacheKey);

    // If the routing rules are cached, return them
    if (cachedRoutingRules) {
      const rules &#x3D; JSON.parse(cachedRoutingRules) as TeamRoutingRules[];
      return rules;
    }

    // If the routing rules are not cached, get them from the database
    const routingRules &#x3D; await this.teamRoutingRulesRepository.findAll({
      where: { team: { id: team.id }, organization: { id: user.orgId } },
      relations: TEAM_ROUTING_RULES_RELATIONS,
    });

    try {
      // Cache the routing rules
      await this.cacheProvider.set(
        cacheKey,
        JSON.stringify(routingRules),
        CACHE_TTL.MONTH * 3,
      );
    } catch (error) {
      this.logger.error(
        &#x60;Failed to cache team routing rules for team ${team.id}, error: ${error.message}&#x60;,
      );
    }

    // Return the routing rules
    return routingRules;
  }

  /**
   * Deletes a routing rule for a team.
   * @param teamId The team ID to delete the routing rule for.
   * @param ruleId The rule ID to delete.
   * @param user The user making the request.
   */
  async deleteRoutingRule(teamId: string, ruleId: string, user: CurrentUser) {
    // Check if the user can update the team
    const { team } &#x3D; await this.canUserUpdateTeam(teamId, user, true);

    // Check if the rule exists
    const rule &#x3D; await this.teamRoutingRulesRepository.findByCondition({
      where: {
        uid: ruleId,
        team: { id: team.id },
        organization: { id: user.orgId },
      },
    });

    // If the rule does not exist, throw an error
    if (!rule) {
      throw new NotFoundException(&quot;Rule not found!&quot;);
    }

    // Delete the rule
    await this.teamRoutingRulesRepository.remove(rule);

    // Invalidate and re-cache the team routing rules
    await this.invalidateAndReCacheTeamRoutingRules(team.id, user.orgId);
  }

  /**
   * Generates the business hours updates for a team.
   * @param businessHours The business hours configurations to update.
   * @returns The business hours updates.
   */
  private generateBusinessHoursUpdates(businessHours: BusinessHoursConfigDto) {
    const updateClause &#x3D; {};

    for (const day in businessHours) {
      const dayProperty: BusinessDayDto &#x3D; businessHours[day];

      // If the day is active, update the business hours
      if (dayProperty.isActive) {
        updateClause[day] &#x3D; dayProperty.slots;
      } else {
        updateClause[day] &#x3D; null;
      }
    }

    return updateClause;
  }

  /**
   * Invalidates and re-caches the team routing rules.
   * @param teamId The team ID to invalidate and re-cache the routing rules for.
   * @param orgId The organization ID to invalidate and re-cache the routing rules for.
   */
  private async invalidateAndReCacheTeamRoutingRules(
    teamId: string,
    orgId: string,
  ) {
    try {
      // Generate the cache key
      const cacheKey &#x3D; &#x60;team-routing-${teamId}-${orgId}&#x60;;

      // Purge the cache
      await this.cacheProvider.del(cacheKey);

      // Get the routing rules
      const routingRules &#x3D; await this.teamRoutingRulesRepository.findAll({
        where: { team: { id: teamId }, organization: { id: orgId } },
        relations: TEAM_ROUTING_RULES_RELATIONS,
      });

      // Cache the routing rules
      await this.cacheProvider.set(
        cacheKey,
        JSON.stringify(routingRules),
        CACHE_TTL.WEEK * 2,
      );
    } catch (error) {
      this.logger.error(
        &#x60;Failed to purge and rec-ache team routing rules for team ${teamId}, error: ${error.message}&#x60;,
      );
    }
  }

  /**
   * Generates a team identifier.
   * @returns The generated team identifier.
   */
  private generateTeamIdentifier(): string {
    return IdGeneratorUtils.generate(&quot;T&quot;);
  }
}
</code></pre>
    </div>
</div>








                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'interface';
            var COMPODOC_CURRENT_PAGE_URL = 'CombinedTeamConfig.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
