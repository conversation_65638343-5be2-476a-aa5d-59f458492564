<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content module">
                   <div class="content-data">



<ol class="breadcrumb">
    <li class="breadcrumb-item">Modules</li>
    <li class="breadcrumb-item" >UtilsModule</li>
</ol>

<div class="text-center module-graph-container">
    <div id="module-graph-svg">
        <?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: dependencies Pages: 1 -->
<svg width="504pt" height="284pt"
 viewBox="0.00 0.00 504.00 284.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 280)">
<title>dependencies</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-280 500,-280 500,4 -4,4"/>
<text text-anchor="start" x="227.01" y="-42.4" font-family="Times-12" font-weight="bold" font-size="14.00">Legend</text>
<polygon fill="#ffffb3" stroke="transparent" points="14,-10 14,-30 34,-30 34,-10 14,-10"/>
<text text-anchor="start" x="37.63" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Declarations</text>
<polygon fill="#8dd3c7" stroke="transparent" points="127,-10 127,-30 147,-30 147,-10 127,-10"/>
<text text-anchor="start" x="150.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Module</text>
<polygon fill="#80b1d3" stroke="transparent" points="213,-10 213,-30 233,-30 233,-10 213,-10"/>
<text text-anchor="start" x="236.78" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Bootstrap</text>
<polygon fill="#fdb462" stroke="transparent" points="310,-10 310,-30 330,-30 330,-10 310,-10"/>
<text text-anchor="start" x="333.67" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Providers</text>
<polygon fill="#fb8072" stroke="transparent" points="406,-10 406,-30 426,-30 426,-10 406,-10"/>
<text text-anchor="start" x="429.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Exports</text>
<g id="clust1" class="cluster">
<title>cluster_UtilsModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="25,-70 25,-268 471,-268 471,-70 25,-70"/>
</g>
<g id="clust4" class="cluster">
<title>cluster_UtilsModule_exports</title>
<polygon fill="none" stroke="black" points="33,-208 33,-260 463,-260 463,-208 33,-208"/>
</g>
<g id="clust3" class="cluster">
<title>cluster_UtilsModule_imports</title>
<polygon fill="none" stroke="black" points="220,-78 220,-130 334,-130 334,-78 220,-78"/>
</g>
<!-- ConfigModule -->
<g id="node1" class="node">
<title>ConfigModule</title>
<polygon fill="#8dd3c7" stroke="black" points="326.44,-122 323.44,-126 302.44,-126 299.44,-122 227.56,-122 227.56,-86 326.44,-86 326.44,-122"/>
<text text-anchor="middle" x="277" y="-99.8" font-family="Times,serif" font-size="14.00">ConfigModule</text>
</g>
<!-- UtilsModule -->
<g id="node2" class="node">
<title>UtilsModule</title>
<polygon fill="#8dd3c7" stroke="black" points="320.27,-187 317.27,-191 296.27,-191 293.27,-187 233.73,-187 233.73,-151 320.27,-151 320.27,-187"/>
<text text-anchor="middle" x="277" y="-164.8" font-family="Times,serif" font-size="14.00">UtilsModule</text>
</g>
<!-- ConfigModule&#45;&gt;UtilsModule -->
<g id="edge1" class="edge">
<title>ConfigModule&#45;&gt;UtilsModule</title>
<path fill="none" stroke="black" d="M277,-122.11C277,-122.11 277,-140.99 277,-140.99"/>
<polygon fill="black" stroke="black" points="273.5,-140.99 277,-150.99 280.5,-140.99 273.5,-140.99"/>
</g>
<!-- ...Object.values(utils)  -->
<g id="node3" class="node">
<title>...Object.values(utils) </title>
<polygon fill="#fb8072" stroke="black" points="455.02,-252 314.98,-252 314.98,-216 455.02,-216 455.02,-252"/>
<text text-anchor="middle" x="385" y="-229.8" font-family="Times,serif" font-size="14.00">...Object.values(utils) </text>
</g>
<!-- UtilsModule&#45;&gt;...Object.values(utils)  -->
<g id="edge2" class="edge">
<title>UtilsModule&#45;&gt;...Object.values(utils) </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M306.17,-187.11C306.17,-206.34 306.17,-234 306.17,-234 306.17,-234 307.04,-234 307.04,-234"/>
<polygon fill="black" stroke="black" points="304.9,-237.5 314.9,-234 304.9,-230.5 304.9,-237.5"/>
</g>
<!-- AUTH_GRPC_SERVICE_URL_TOKEN  -->
<g id="node4" class="node">
<title>AUTH_GRPC_SERVICE_URL_TOKEN </title>
<polygon fill="#fb8072" stroke="black" points="297.18,-252 40.82,-252 40.82,-216 297.18,-216 297.18,-252"/>
<text text-anchor="middle" x="169" y="-229.8" font-family="Times,serif" font-size="14.00">AUTH_GRPC_SERVICE_URL_TOKEN </text>
</g>
<!-- UtilsModule&#45;&gt;AUTH_GRPC_SERVICE_URL_TOKEN  -->
<g id="edge3" class="edge">
<title>UtilsModule&#45;&gt;AUTH_GRPC_SERVICE_URL_TOKEN </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M265.48,-187.11C265.48,-187.11 265.48,-205.99 265.48,-205.99"/>
<polygon fill="black" stroke="black" points="261.98,-205.99 265.48,-215.99 268.98,-205.99 261.98,-205.99"/>
</g>
</g>
</svg>

    </div>
    <i id="fullscreen" class="icon ion-ios-resize module-graph-fullscreen-btn" aria-hidden="true"></i>
    <div class="btn-group size-buttons">
        <button id="zoom-in" class="btn btn-default btn-sm">Zoom in</button>
        <button id="reset" class="btn btn-default btn-sm">Reset</button>
        <button id="zoom-out" class="btn btn-default btn-sm">Zoom out</button>
    </div>
</div>
<script src="../js/libs/svg-pan-zoom.min.js"></script>
<script src="../js/svg-pan-zoom.controls.js"></script>

<ul class="nav nav-tabs" role="tablist">
    <li class="nav-item">
        <a href="#info" 
            class="nav-link"
            class="nav-link active"
            role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
    </li>
    <li class="nav-item">
        <a href="#source" 
            class="nav-link"
            
            role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
    </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">

        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/common/utils.module.ts</code>
        </p>





        <div class="container-fluid module">
            <div class="row">
                <div class="col-sm-3">
                    <h3>Imports<a href="https://angular.io/api/core/NgModule#imports" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module imports"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../modules/ConfigModule.html">ConfigModule</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Exports<a href="https://angular.io/api/core/NgModule#exports" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module exports"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../s/...Object.values(utils).html">...Object.values(utils)</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/AUTH_GRPC_SERVICE_URL_TOKEN.html">AUTH_GRPC_SERVICE_URL_TOKEN</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>


    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Global, Module } from &quot;@nestjs/common&quot;;
import { AUTH_GRPC_SERVICE_URL_TOKEN } from &quot;@repo/nestjs-commons/guards&quot;;
import { ConfigModule } from &quot;../config/config.module&quot;;
import { ConfigKeys, ConfigService } from &quot;../config/config.service&quot;;
import * as utils from &quot;./utils&quot;;

@Global()
@Module({
  imports: [ConfigModule],
  providers: [
    ...Object.values(utils),
    {
      provide: AUTH_GRPC_SERVICE_URL_TOKEN,
      useFactory: (configService: ConfigService) &#x3D;&gt;
        configService.get(ConfigKeys.AUTH_GRPC_URL),
      inject: [ConfigService],
    },
  ],
  exports: [...Object.values(utils), AUTH_GRPC_SERVICE_URL_TOKEN],
})
export class UtilsModule {}
</code></pre>
    </div>
</div>

















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'module';
            var COMPODOC_CURRENT_PAGE_URL = 'UtilsModule.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
