<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: dependencies Pages: 1 -->
<svg width="526pt" height="284pt"
 viewBox="0.00 0.00 526.00 284.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 280)">
<title>dependencies</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-280 522,-280 522,4 -4,4"/>
<text text-anchor="start" x="238.01" y="-42.4" font-family="Times-12" font-weight="bold" font-size="14.00">Legend</text>
<polygon fill="#ffffb3" stroke="transparent" points="25,-10 25,-30 45,-30 45,-10 25,-10"/>
<text text-anchor="start" x="48.63" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Declarations</text>
<polygon fill="#8dd3c7" stroke="transparent" points="138,-10 138,-30 158,-30 158,-10 138,-10"/>
<text text-anchor="start" x="161.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Module</text>
<polygon fill="#80b1d3" stroke="transparent" points="224,-10 224,-30 244,-30 244,-10 224,-10"/>
<text text-anchor="start" x="247.78" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Bootstrap</text>
<polygon fill="#fdb462" stroke="transparent" points="321,-10 321,-30 341,-30 341,-10 321,-10"/>
<text text-anchor="start" x="344.67" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Providers</text>
<polygon fill="#fb8072" stroke="transparent" points="417,-10 417,-30 437,-30 437,-10 417,-10"/>
<text text-anchor="start" x="440.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Exports</text>
<g id="clust1" class="cluster">
<title>cluster_StorageModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="8,-70 8,-268 510,-268 510,-70 8,-70"/>
</g>
<g id="clust6" class="cluster">
<title>cluster_StorageModule_providers</title>
<polygon fill="none" stroke="black" points="138,-78 138,-130 502,-130 502,-78 138,-78"/>
</g>
<g id="clust4" class="cluster">
<title>cluster_StorageModule_exports</title>
<polygon fill="none" stroke="black" points="155,-208 155,-260 275,-260 275,-208 155,-208"/>
</g>
<g id="clust3" class="cluster">
<title>cluster_StorageModule_imports</title>
<polygon fill="none" stroke="black" points="16,-78 16,-130 130,-130 130,-78 16,-78"/>
</g>
<!-- ConfigModule -->
<g id="node1" class="node">
<title>ConfigModule</title>
<polygon fill="#8dd3c7" stroke="black" points="122.44,-122 119.44,-126 98.44,-126 95.44,-122 23.56,-122 23.56,-86 122.44,-86 122.44,-122"/>
<text text-anchor="middle" x="73" y="-99.8" font-family="Times,serif" font-size="14.00">ConfigModule</text>
</g>
<!-- StorageModule -->
<g id="node2" class="node">
<title>StorageModule</title>
<polygon fill="#8dd3c7" stroke="black" points="266.31,-187 263.31,-191 242.31,-191 239.31,-187 163.69,-187 163.69,-151 266.31,-151 266.31,-187"/>
<text text-anchor="middle" x="215" y="-164.8" font-family="Times,serif" font-size="14.00">StorageModule</text>
</g>
<!-- ConfigModule&#45;&gt;StorageModule -->
<g id="edge1" class="edge">
<title>ConfigModule&#45;&gt;StorageModule</title>
<path fill="none" stroke="black" d="M73,-122.11C73,-141.34 73,-169 73,-169 73,-169 153.73,-169 153.73,-169"/>
<polygon fill="black" stroke="black" points="153.73,-172.5 163.73,-169 153.73,-165.5 153.73,-172.5"/>
</g>
<!-- StorageService  -->
<g id="node3" class="node">
<title>StorageService </title>
<polygon fill="#fb8072" stroke="black" points="267.24,-252 162.76,-252 162.76,-216 267.24,-216 267.24,-252"/>
<text text-anchor="middle" x="215" y="-229.8" font-family="Times,serif" font-size="14.00">StorageService </text>
</g>
<!-- StorageModule&#45;&gt;StorageService  -->
<g id="edge2" class="edge">
<title>StorageModule&#45;&gt;StorageService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M215,-187.11C215,-187.11 215,-205.99 215,-205.99"/>
<polygon fill="black" stroke="black" points="211.5,-205.99 215,-215.99 218.5,-205.99 211.5,-205.99"/>
</g>
<!-- LocalStorageProvider -->
<g id="node4" class="node">
<title>LocalStorageProvider</title>
<ellipse fill="#fdb462" stroke="black" cx="398" cy="-104" rx="95.55" ry="18"/>
<text text-anchor="middle" x="398" y="-99.8" font-family="Times,serif" font-size="14.00">LocalStorageProvider</text>
</g>
<!-- LocalStorageProvider&#45;&gt;StorageModule -->
<g id="edge3" class="edge">
<title>LocalStorageProvider&#45;&gt;StorageModule</title>
<path fill="none" stroke="black" d="M398,-122.11C398,-141.34 398,-169 398,-169 398,-169 276.46,-169 276.46,-169"/>
<polygon fill="black" stroke="black" points="276.46,-165.5 266.46,-169 276.46,-172.5 276.46,-165.5"/>
</g>
<!-- StorageService -->
<g id="node5" class="node">
<title>StorageService</title>
<ellipse fill="#fdb462" stroke="black" cx="215" cy="-104" rx="69.48" ry="18"/>
<text text-anchor="middle" x="215" y="-99.8" font-family="Times,serif" font-size="14.00">StorageService</text>
</g>
<!-- StorageService&#45;&gt;StorageModule -->
<g id="edge4" class="edge">
<title>StorageService&#45;&gt;StorageModule</title>
<path fill="none" stroke="black" d="M215,-122.11C215,-122.11 215,-140.99 215,-140.99"/>
<polygon fill="black" stroke="black" points="211.5,-140.99 215,-150.99 218.5,-140.99 211.5,-140.99"/>
</g>
</g>
</svg>
