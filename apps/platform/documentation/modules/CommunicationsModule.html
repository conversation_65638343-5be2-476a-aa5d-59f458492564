<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content module">
                   <div class="content-data">



<ol class="breadcrumb">
    <li class="breadcrumb-item">Modules</li>
    <li class="breadcrumb-item" >CommunicationsModule</li>
</ol>

<div class="text-center module-graph-container">
    <div id="module-graph-svg">
        <?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: dependencies Pages: 1 -->
<svg width="1691pt" height="284pt"
 viewBox="0.00 0.00 1691.00 284.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 280)">
<title>dependencies</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-280 1687,-280 1687,4 -4,4"/>
<text text-anchor="start" x="820.51" y="-42.4" font-family="Times-12" font-weight="bold" font-size="14.00">Legend</text>
<polygon fill="#ffffb3" stroke="transparent" points="607.5,-10 607.5,-30 627.5,-30 627.5,-10 607.5,-10"/>
<text text-anchor="start" x="631.13" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Declarations</text>
<polygon fill="#8dd3c7" stroke="transparent" points="720.5,-10 720.5,-30 740.5,-30 740.5,-10 720.5,-10"/>
<text text-anchor="start" x="744.23" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Module</text>
<polygon fill="#80b1d3" stroke="transparent" points="806.5,-10 806.5,-30 826.5,-30 826.5,-10 806.5,-10"/>
<text text-anchor="start" x="830.28" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Bootstrap</text>
<polygon fill="#fdb462" stroke="transparent" points="903.5,-10 903.5,-30 923.5,-30 923.5,-10 903.5,-10"/>
<text text-anchor="start" x="927.17" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Providers</text>
<polygon fill="#fb8072" stroke="transparent" points="999.5,-10 999.5,-30 1019.5,-30 1019.5,-10 999.5,-10"/>
<text text-anchor="start" x="1023.23" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Exports</text>
<g id="clust1" class="cluster">
<title>cluster_CommunicationsModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="8,-70 8,-268 1675,-268 1675,-70 8,-70"/>
</g>
<g id="clust3" class="cluster">
<title>cluster_CommunicationsModule_imports</title>
<polygon fill="none" stroke="black" points="936,-78 936,-130 1667,-130 1667,-78 936,-78"/>
</g>
<g id="clust6" class="cluster">
<title>cluster_CommunicationsModule_providers</title>
<polygon fill="none" stroke="black" points="16,-78 16,-130 928,-130 928,-78 16,-78"/>
</g>
<g id="clust4" class="cluster">
<title>cluster_CommunicationsModule_exports</title>
<polygon fill="none" stroke="black" points="964,-208 964,-260 1138,-260 1138,-208 964,-208"/>
</g>
<!-- AccountsModule -->
<g id="node1" class="node">
<title>AccountsModule</title>
<polygon fill="#8dd3c7" stroke="black" points="1659.42,-122 1656.42,-126 1635.42,-126 1632.42,-122 1546.58,-122 1546.58,-86 1659.42,-86 1659.42,-122"/>
<text text-anchor="middle" x="1603" y="-99.8" font-family="Times,serif" font-size="14.00">AccountsModule</text>
</g>
<!-- CommunicationsModule -->
<g id="node7" class="node">
<title>CommunicationsModule</title>
<polygon fill="#8dd3c7" stroke="black" points="1128.71,-187 1125.71,-191 1104.71,-191 1101.71,-187 973.29,-187 973.29,-151 1128.71,-151 1128.71,-187"/>
<text text-anchor="middle" x="1051" y="-164.8" font-family="Times,serif" font-size="14.00">CommunicationsModule</text>
</g>
<!-- AccountsModule&#45;&gt;CommunicationsModule -->
<g id="edge1" class="edge">
<title>AccountsModule&#45;&gt;CommunicationsModule</title>
<path fill="none" stroke="black" d="M1603,-122.01C1603,-144.49 1603,-180 1603,-180 1603,-180 1138.63,-180 1138.63,-180"/>
<polygon fill="black" stroke="black" points="1138.63,-176.5 1128.63,-180 1138.63,-183.5 1138.63,-176.5"/>
</g>
<!-- ActivitiesModule -->
<g id="node2" class="node">
<title>ActivitiesModule</title>
<polygon fill="#8dd3c7" stroke="black" points="1528.98,-122 1525.98,-126 1504.98,-126 1501.98,-122 1415.02,-122 1415.02,-86 1528.98,-86 1528.98,-122"/>
<text text-anchor="middle" x="1472" y="-99.8" font-family="Times,serif" font-size="14.00">ActivitiesModule</text>
</g>
<!-- ActivitiesModule&#45;&gt;CommunicationsModule -->
<g id="edge2" class="edge">
<title>ActivitiesModule&#45;&gt;CommunicationsModule</title>
<path fill="none" stroke="black" d="M1472,-122.13C1472,-142.57 1472,-173 1472,-173 1472,-173 1138.71,-173 1138.71,-173"/>
<polygon fill="black" stroke="black" points="1138.71,-169.5 1128.71,-173 1138.71,-176.5 1138.71,-169.5"/>
</g>
<!-- AuthModule -->
<g id="node3" class="node">
<title>AuthModule</title>
<polygon fill="#8dd3c7" stroke="black" points="1396.55,-122 1393.55,-126 1372.55,-126 1369.55,-122 1309.45,-122 1309.45,-86 1396.55,-86 1396.55,-122"/>
<text text-anchor="middle" x="1353" y="-99.8" font-family="Times,serif" font-size="14.00">AuthModule</text>
</g>
<!-- AuthModule&#45;&gt;CommunicationsModule -->
<g id="edge3" class="edge">
<title>AuthModule&#45;&gt;CommunicationsModule</title>
<path fill="none" stroke="black" d="M1353,-122.27C1353,-140.56 1353,-166 1353,-166 1353,-166 1138.77,-166 1138.77,-166"/>
<polygon fill="black" stroke="black" points="1138.77,-162.5 1128.77,-166 1138.77,-169.5 1138.77,-162.5"/>
</g>
<!-- CommonModule -->
<g id="node4" class="node">
<title>CommonModule</title>
<polygon fill="#8dd3c7" stroke="black" points="1290.67,-122 1287.67,-126 1266.67,-126 1263.67,-122 1179.33,-122 1179.33,-86 1290.67,-86 1290.67,-122"/>
<text text-anchor="middle" x="1235" y="-99.8" font-family="Times,serif" font-size="14.00">CommonModule</text>
</g>
<!-- CommonModule&#45;&gt;CommunicationsModule -->
<g id="edge4" class="edge">
<title>CommonModule&#45;&gt;CommunicationsModule</title>
<path fill="none" stroke="black" d="M1235,-122.01C1235,-138.05 1235,-159 1235,-159 1235,-159 1138.77,-159 1138.77,-159"/>
<polygon fill="black" stroke="black" points="1138.77,-155.5 1128.77,-159 1138.77,-162.5 1138.77,-155.5"/>
</g>
<!-- StorageModule -->
<g id="node5" class="node">
<title>StorageModule</title>
<polygon fill="#8dd3c7" stroke="black" points="1161.31,-122 1158.31,-126 1137.31,-126 1134.31,-122 1058.69,-122 1058.69,-86 1161.31,-86 1161.31,-122"/>
<text text-anchor="middle" x="1110" y="-99.8" font-family="Times,serif" font-size="14.00">StorageModule</text>
</g>
<!-- StorageModule&#45;&gt;CommunicationsModule -->
<g id="edge5" class="edge">
<title>StorageModule&#45;&gt;CommunicationsModule</title>
<path fill="none" stroke="black" d="M1093.73,-122.11C1093.73,-122.11 1093.73,-140.99 1093.73,-140.99"/>
<polygon fill="black" stroke="black" points="1090.23,-140.99 1093.73,-150.99 1097.23,-140.99 1090.23,-140.99"/>
</g>
<!-- TeamsModule -->
<g id="node6" class="node">
<title>TeamsModule</title>
<polygon fill="#8dd3c7" stroke="black" points="1040.37,-122 1037.37,-126 1016.37,-126 1013.37,-122 943.63,-122 943.63,-86 1040.37,-86 1040.37,-122"/>
<text text-anchor="middle" x="992" y="-99.8" font-family="Times,serif" font-size="14.00">TeamsModule</text>
</g>
<!-- TeamsModule&#45;&gt;CommunicationsModule -->
<g id="edge6" class="edge">
<title>TeamsModule&#45;&gt;CommunicationsModule</title>
<path fill="none" stroke="black" d="M1006.91,-122.11C1006.91,-122.11 1006.91,-140.99 1006.91,-140.99"/>
<polygon fill="black" stroke="black" points="1003.41,-140.99 1006.91,-150.99 1010.41,-140.99 1003.41,-140.99"/>
</g>
<!-- CommunicationsService  -->
<g id="node8" class="node">
<title>CommunicationsService </title>
<polygon fill="#fb8072" stroke="black" points="1129.64,-252 972.36,-252 972.36,-216 1129.64,-216 1129.64,-252"/>
<text text-anchor="middle" x="1051" y="-229.8" font-family="Times,serif" font-size="14.00">CommunicationsService </text>
</g>
<!-- CommunicationsModule&#45;&gt;CommunicationsService  -->
<g id="edge7" class="edge">
<title>CommunicationsModule&#45;&gt;CommunicationsService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1051,-187.11C1051,-187.11 1051,-205.99 1051,-205.99"/>
<polygon fill="black" stroke="black" points="1047.5,-205.99 1051,-215.99 1054.5,-205.99 1047.5,-205.99"/>
</g>
<!-- CommentSnsConsumer -->
<g id="node9" class="node">
<title>CommentSnsConsumer</title>
<ellipse fill="#fdb462" stroke="black" cx="817" cy="-104" rx="102.55" ry="18"/>
<text text-anchor="middle" x="817" y="-99.8" font-family="Times,serif" font-size="14.00">CommentSnsConsumer</text>
</g>
<!-- CommentSnsConsumer&#45;&gt;CommunicationsModule -->
<g id="edge8" class="edge">
<title>CommentSnsConsumer&#45;&gt;CommunicationsModule</title>
<path fill="none" stroke="black" d="M817,-122.01C817,-138.05 817,-159 817,-159 817,-159 963.08,-159 963.08,-159"/>
<polygon fill="black" stroke="black" points="963.08,-162.5 973.08,-159 963.08,-155.5 963.08,-162.5"/>
</g>
<!-- CommentsActionService -->
<g id="node10" class="node">
<title>CommentsActionService</title>
<ellipse fill="#fdb462" stroke="black" cx="588" cy="-104" rx="108.24" ry="18"/>
<text text-anchor="middle" x="588" y="-99.8" font-family="Times,serif" font-size="14.00">CommentsActionService</text>
</g>
<!-- CommentsActionService&#45;&gt;CommunicationsModule -->
<g id="edge9" class="edge">
<title>CommentsActionService&#45;&gt;CommunicationsModule</title>
<path fill="none" stroke="black" d="M588,-122.27C588,-140.56 588,-166 588,-166 588,-166 963.24,-166 963.24,-166"/>
<polygon fill="black" stroke="black" points="963.24,-169.5 973.24,-166 963.24,-162.5 963.24,-169.5"/>
</g>
<!-- CommunicationsService -->
<g id="node11" class="node">
<title>CommunicationsService</title>
<ellipse fill="#fdb462" stroke="black" cx="356" cy="-104" rx="105.96" ry="18"/>
<text text-anchor="middle" x="356" y="-99.8" font-family="Times,serif" font-size="14.00">CommunicationsService</text>
</g>
<!-- CommunicationsService&#45;&gt;CommunicationsModule -->
<g id="edge10" class="edge">
<title>CommunicationsService&#45;&gt;CommunicationsModule</title>
<path fill="none" stroke="black" d="M356,-122.13C356,-142.57 356,-173 356,-173 356,-173 963.3,-173 963.3,-173"/>
<polygon fill="black" stroke="black" points="963.3,-176.5 973.3,-173 963.3,-169.5 963.3,-176.5"/>
</g>
<!-- ReactionsActionService -->
<g id="node12" class="node">
<title>ReactionsActionService</title>
<ellipse fill="#fdb462" stroke="black" cx="128" cy="-104" rx="104.21" ry="18"/>
<text text-anchor="middle" x="128" y="-99.8" font-family="Times,serif" font-size="14.00">ReactionsActionService</text>
</g>
<!-- ReactionsActionService&#45;&gt;CommunicationsModule -->
<g id="edge11" class="edge">
<title>ReactionsActionService&#45;&gt;CommunicationsModule</title>
<path fill="none" stroke="black" d="M128,-122.01C128,-144.49 128,-180 128,-180 128,-180 963.1,-180 963.1,-180"/>
<polygon fill="black" stroke="black" points="963.1,-183.5 973.1,-180 963.1,-176.5 963.1,-183.5"/>
</g>
</g>
</svg>

    </div>
    <i id="fullscreen" class="icon ion-ios-resize module-graph-fullscreen-btn" aria-hidden="true"></i>
    <div class="btn-group size-buttons">
        <button id="zoom-in" class="btn btn-default btn-sm">Zoom in</button>
        <button id="reset" class="btn btn-default btn-sm">Reset</button>
        <button id="zoom-out" class="btn btn-default btn-sm">Zoom out</button>
    </div>
</div>
<script src="../js/libs/svg-pan-zoom.min.js"></script>
<script src="../js/svg-pan-zoom.controls.js"></script>

<ul class="nav nav-tabs" role="tablist">
    <li class="nav-item">
        <a href="#info" 
            class="nav-link"
            class="nav-link active"
            role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
    </li>
    <li class="nav-item">
        <a href="#source" 
            class="nav-link"
            
            role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
    </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">

        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/communications/communications.module.ts</code>
        </p>





        <div class="container-fluid module">
            <div class="row">
                <div class="col-sm-3">
                    <h3>Providers<a href="https://angular.io/api/core/NgModule#providers" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module providers"><span class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../injectables/CommentSnsConsumer.html">CommentSnsConsumer</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/CommentsActionService.html">CommentsActionService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/CommunicationsService.html">CommunicationsService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/ReactionsActionService.html">ReactionsActionService</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Controllers<a href="https://docs.nestjs.com/controllers" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module controllers"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../controllers/CommunicationsController.html">CommunicationsController</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../controllers/CommentsActionController.html">CommentsActionController</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../controllers/ReactionsActionController.html">ReactionsActionController</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../controllers/CommentsGrpcController.html">CommentsGrpcController</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../controllers/ReactionsGrpcController.html">ReactionsGrpcController</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Imports<a href="https://angular.io/api/core/NgModule#imports" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module imports"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../modules/AccountsModule.html">AccountsModule</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../modules/ActivitiesModule.html">ActivitiesModule</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../modules/AuthModule.html">AuthModule</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../modules/CommonModule.html">CommonModule</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../modules/StorageModule.html">StorageModule</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../modules/TeamsModule.html">TeamsModule</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Exports<a href="https://angular.io/api/core/NgModule#exports" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module exports"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../injectables/CommunicationsService.html">CommunicationsService</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>


    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { BullModule } from &quot;@nestjs/bullmq&quot;;
import { forwardRef, Module } from &quot;@nestjs/common&quot;;
import { TypeOrmModule } from &quot;@nestjs/typeorm&quot;;
import { SQSProducerService } from &quot;@repo/nestjs-commons/aws-utils/sqs&quot;;
import { SentryService } from &quot;@repo/nestjs-commons/filters&quot;;
import { ILogger } from &quot;@repo/nestjs-commons/logger&quot;;
import { SNSPublisherService } from &quot;@repo/thena-eventbridge&quot;;
import {
  Comment,
  CommentProcessorService,
  CommentRepository,
  Emojis,
  EmojisRepository,
  Mentions,
  MentionsRepository,
  Reactions,
  ReactionsRepository,
  TransactionService,
} from &quot;@repo/thena-platform-entities&quot;;
import { AccountsModule } from &quot;../accounts/accounts.module&quot;;
import { ActivitiesModule } from &quot;../activities/activities.module&quot;;
import { AuthModule } from &quot;../auth/auth.module&quot;;
import { CommonModule } from &quot;../common/common.module&quot;;
import { ConfigModule } from &quot;../config/config.module&quot;;
import { ConfigKeys, ConfigService } from &quot;../config/config.service&quot;;
import { QueueNames } from &quot;../constants/queue.constants&quot;;
import { StorageModule } from &quot;../storage/module/storage.module&quot;;
import { TeamsModule } from &quot;../teams/teams.module&quot;;
import { TicketsModule } from &quot;../tickets/tickets.module&quot;;
import { COMMENT_SNS_PUBLISHER } from &quot;./constants/comments.constants&quot;;
import { CommentsActionController } from &quot;./controllers/comments.action.controller&quot;;
import { CommunicationsController } from &quot;./controllers/communications.controller&quot;;
import { CommentsGrpcController } from &quot;./controllers/grpc/comments-grpc.controller&quot;;
import { ReactionsGrpcController } from &quot;./controllers/grpc/reactions-grpc.controller&quot;;
import { ReactionsActionController } from &quot;./controllers/reactions.action.controller&quot;;
import { CommentSnsConsumer } from &quot;./processors/comment.sns-publish.processor&quot;;
import { CommentsActionService } from &quot;./services/comments.action.service&quot;;
import { CommunicationsService } from &quot;./services/communications.service&quot;;
import { ReactionsActionService } from &quot;./services/reactions.action.service&quot;;

@Module({
  imports: [
    AuthModule,
    TypeOrmModule.forFeature([
      Comment,
      CommentRepository,
      Mentions,
      MentionsRepository,
      Reactions,
      ReactionsRepository,
      Emojis,
      EmojisRepository,
    ]),
    CommonModule,
    TeamsModule,
    ActivitiesModule,
    StorageModule,
    forwardRef(() &#x3D;&gt; TicketsModule),
    AccountsModule,
    BullModule.registerQueueAsync({
      name: QueueNames.COMMENT_SNS_PUBLISHER,
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) &#x3D;&gt; ({
        redis: {
          host: configService.get(ConfigKeys.REDIS_HOST),
          port: Number(configService.get(ConfigKeys.REDIS_PORT)),
          password: configService.get(ConfigKeys.REDIS_PASSWORD),
          username: configService.get(ConfigKeys.REDIS_USERNAME),
        },
        defaultJobOptions: {
          removeOnComplete: {
            age: 24 * 3600, // keep completed jobs for 24 hours
            count: 1000, // keep last 1000 completed jobs
          },
          removeOnFail: {
            age: 24 * 3600, // keep up to 24 hours
          },
        },
      }),
    }),
  ],
  controllers: [
    CommunicationsController,
    CommentsActionController,
    ReactionsActionController,

    CommentsGrpcController,
    ReactionsGrpcController,
  ],
  providers: [
    TransactionService,
    CommentProcessorService,
    CommunicationsService,
    CommentRepository,
    MentionsRepository,
    CommentsActionService,
    ReactionsActionService,
    ReactionsRepository,
    EmojisRepository,
    CommentSnsConsumer,
    {
      provide: COMMENT_SNS_PUBLISHER,
      useFactory: (
        configService: ConfigService,
        sentryService: SentryService,
        loggerService: ILogger,
      ) &#x3D;&gt; {
        return new SNSPublisherService(
          {
            topicArn: configService.get(ConfigKeys.AWS_SNS_TICKET_TOPIC_ARN),
            region: configService.get(ConfigKeys.AWS_REGION),
            credentials: {
              accessKeyId: configService.get(ConfigKeys.AWS_ACCESS_KEY),
              secretAccessKey: configService.get(ConfigKeys.AWS_SECRET_KEY),
            },
          },
          sentryService,
          loggerService,
        );
      },
      inject: [ConfigService, &quot;Sentry&quot;, &quot;CustomLogger&quot;],
    },
    {
      provide: &quot;COMMUNICATIONS_SQS_PRODUCER&quot;,
      useFactory: (
        configService: ConfigService,
        sentryService: SentryService,
        loggerService: ILogger,
      ) &#x3D;&gt; {
        return new SQSProducerService(
          {
            region: configService.get(ConfigKeys.AWS_REGION),
            queueUrl: configService.get(ConfigKeys.AWS_SQS_SLA_QUEUE_URL),
            credentials: {
              accessKeyId: configService.get(ConfigKeys.AWS_ACCESS_KEY),
              secretAccessKey: configService.get(ConfigKeys.AWS_SECRET_KEY),
            },
          },
          sentryService,
          loggerService,
        );
      },
      inject: [ConfigService, &quot;Sentry&quot;, &quot;CustomLogger&quot;],
    },
    {
      provide: &quot;COMMUNICATIONS_SNS_PUBLISHER&quot;,
      useFactory: (
        configService: ConfigService,
        sentryService: SentryService,
        loggerService: ILogger,
      ) &#x3D;&gt; {
        return new SNSPublisherService(
          {
            topicArn: configService.get(ConfigKeys.AWS_SNS_TICKET_TOPIC_ARN),
            region: configService.get(ConfigKeys.AWS_REGION),
            credentials: {
              accessKeyId: configService.get(ConfigKeys.AWS_ACCESS_KEY),
              secretAccessKey: configService.get(ConfigKeys.AWS_SECRET_KEY),
            },
          },
          sentryService,
          loggerService,
        );
      },
      inject: [ConfigService, &quot;Sentry&quot;, &quot;CustomLogger&quot;],
    },
  ],
  exports: [CommunicationsService],
})
export class CommunicationsModule {}
</code></pre>
    </div>
</div>

















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'module';
            var COMPODOC_CURRENT_PAGE_URL = 'CommunicationsModule.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
