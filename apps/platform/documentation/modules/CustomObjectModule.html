<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content module">
                   <div class="content-data">



<ol class="breadcrumb">
    <li class="breadcrumb-item">Modules</li>
    <li class="breadcrumb-item" >CustomObjectModule</li>
</ol>

<div class="text-center module-graph-container">
    <div id="module-graph-svg">
        <?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: dependencies Pages: 1 -->
<svg width="931pt" height="284pt"
 viewBox="0.00 0.00 931.00 284.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 280)">
<title>dependencies</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-280 927,-280 927,4 -4,4"/>
<text text-anchor="start" x="440.51" y="-42.4" font-family="Times-12" font-weight="bold" font-size="14.00">Legend</text>
<polygon fill="#ffffb3" stroke="transparent" points="227.5,-10 227.5,-30 247.5,-30 247.5,-10 227.5,-10"/>
<text text-anchor="start" x="251.13" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Declarations</text>
<polygon fill="#8dd3c7" stroke="transparent" points="340.5,-10 340.5,-30 360.5,-30 360.5,-10 340.5,-10"/>
<text text-anchor="start" x="364.23" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Module</text>
<polygon fill="#80b1d3" stroke="transparent" points="426.5,-10 426.5,-30 446.5,-30 446.5,-10 426.5,-10"/>
<text text-anchor="start" x="450.28" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Bootstrap</text>
<polygon fill="#fdb462" stroke="transparent" points="523.5,-10 523.5,-30 543.5,-30 543.5,-10 523.5,-10"/>
<text text-anchor="start" x="547.17" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Providers</text>
<polygon fill="#fb8072" stroke="transparent" points="619.5,-10 619.5,-30 639.5,-30 639.5,-10 619.5,-10"/>
<text text-anchor="start" x="643.23" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Exports</text>
<g id="clust1" class="cluster">
<title>cluster_CustomObjectModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="8,-70 8,-268 915,-268 915,-70 8,-70"/>
</g>
<g id="clust3" class="cluster">
<title>cluster_CustomObjectModule_imports</title>
<polygon fill="none" stroke="black" points="514,-78 514,-130 907,-130 907,-78 514,-78"/>
</g>
<g id="clust4" class="cluster">
<title>cluster_CustomObjectModule_exports</title>
<polygon fill="none" stroke="black" points="491,-208 491,-260 649,-260 649,-208 491,-208"/>
</g>
<g id="clust6" class="cluster">
<title>cluster_CustomObjectModule_providers</title>
<polygon fill="none" stroke="black" points="16,-78 16,-130 506,-130 506,-78 16,-78"/>
</g>
<!-- CommonModule -->
<g id="node1" class="node">
<title>CommonModule</title>
<polygon fill="#8dd3c7" stroke="black" points="898.67,-122 895.67,-126 874.67,-126 871.67,-122 787.33,-122 787.33,-86 898.67,-86 898.67,-122"/>
<text text-anchor="middle" x="843" y="-99.8" font-family="Times,serif" font-size="14.00">CommonModule</text>
</g>
<!-- CustomObjectModule -->
<g id="node4" class="node">
<title>CustomObjectModule</title>
<polygon fill="#8dd3c7" stroke="black" points="640.43,-187 637.43,-191 616.43,-191 613.43,-187 499.57,-187 499.57,-151 640.43,-151 640.43,-187"/>
<text text-anchor="middle" x="570" y="-164.8" font-family="Times,serif" font-size="14.00">CustomObjectModule</text>
</g>
<!-- CommonModule&#45;&gt;CustomObjectModule -->
<g id="edge1" class="edge">
<title>CommonModule&#45;&gt;CustomObjectModule</title>
<path fill="none" stroke="black" d="M843,-122.11C843,-141.34 843,-169 843,-169 843,-169 650.3,-169 650.3,-169"/>
<polygon fill="black" stroke="black" points="650.3,-165.5 640.3,-169 650.3,-172.5 650.3,-165.5"/>
</g>
<!-- OrganizationModule -->
<g id="node2" class="node">
<title>OrganizationModule</title>
<polygon fill="#8dd3c7" stroke="black" points="769.13,-122 766.13,-126 745.13,-126 742.13,-122 636.87,-122 636.87,-86 769.13,-86 769.13,-122"/>
<text text-anchor="middle" x="703" y="-99.8" font-family="Times,serif" font-size="14.00">OrganizationModule</text>
</g>
<!-- OrganizationModule&#45;&gt;CustomObjectModule -->
<g id="edge2" class="edge">
<title>OrganizationModule&#45;&gt;CustomObjectModule</title>
<path fill="none" stroke="black" d="M636.81,-104C631.17,-104 627.56,-104 627.56,-104 627.56,-104 627.56,-140.89 627.56,-140.89"/>
<polygon fill="black" stroke="black" points="624.06,-140.89 627.56,-150.89 631.06,-140.89 624.06,-140.89"/>
</g>
<!-- TeamsModule -->
<g id="node3" class="node">
<title>TeamsModule</title>
<polygon fill="#8dd3c7" stroke="black" points="618.37,-122 615.37,-126 594.37,-126 591.37,-122 521.63,-122 521.63,-86 618.37,-86 618.37,-122"/>
<text text-anchor="middle" x="570" y="-99.8" font-family="Times,serif" font-size="14.00">TeamsModule</text>
</g>
<!-- TeamsModule&#45;&gt;CustomObjectModule -->
<g id="edge3" class="edge">
<title>TeamsModule&#45;&gt;CustomObjectModule</title>
<path fill="none" stroke="black" d="M570,-122.11C570,-122.11 570,-140.99 570,-140.99"/>
<polygon fill="black" stroke="black" points="566.5,-140.99 570,-150.99 573.5,-140.99 566.5,-140.99"/>
</g>
<!-- CustomObjectService  -->
<g id="node5" class="node">
<title>CustomObjectService </title>
<polygon fill="#fb8072" stroke="black" points="641.36,-252 498.64,-252 498.64,-216 641.36,-216 641.36,-252"/>
<text text-anchor="middle" x="570" y="-229.8" font-family="Times,serif" font-size="14.00">CustomObjectService </text>
</g>
<!-- CustomObjectModule&#45;&gt;CustomObjectService  -->
<g id="edge4" class="edge">
<title>CustomObjectModule&#45;&gt;CustomObjectService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M570,-187.11C570,-187.11 570,-205.99 570,-205.99"/>
<polygon fill="black" stroke="black" points="566.5,-205.99 570,-215.99 573.5,-205.99 566.5,-205.99"/>
</g>
<!-- CustomObjectService -->
<g id="node6" class="node">
<title>CustomObjectService</title>
<ellipse fill="#fdb462" stroke="black" cx="402" cy="-104" rx="95.56" ry="18"/>
<text text-anchor="middle" x="402" y="-99.8" font-family="Times,serif" font-size="14.00">CustomObjectService</text>
</g>
<!-- CustomObjectService&#45;&gt;CustomObjectModule -->
<g id="edge5" class="edge">
<title>CustomObjectService&#45;&gt;CustomObjectModule</title>
<path fill="none" stroke="black" d="M497.78,-104C505.66,-104 510.68,-104 510.68,-104 510.68,-104 510.68,-140.89 510.68,-140.89"/>
<polygon fill="black" stroke="black" points="507.18,-140.89 510.68,-150.89 514.18,-140.89 507.18,-140.89"/>
</g>
<!-- CustomObjectValidatorService -->
<g id="node7" class="node">
<title>CustomObjectValidatorService</title>
<ellipse fill="#fdb462" stroke="black" cx="156" cy="-104" rx="132.5" ry="18"/>
<text text-anchor="middle" x="156" y="-99.8" font-family="Times,serif" font-size="14.00">CustomObjectValidatorService</text>
</g>
<!-- CustomObjectValidatorService&#45;&gt;CustomObjectModule -->
<g id="edge6" class="edge">
<title>CustomObjectValidatorService&#45;&gt;CustomObjectModule</title>
<path fill="none" stroke="black" d="M156,-122.11C156,-141.34 156,-169 156,-169 156,-169 489.77,-169 489.77,-169"/>
<polygon fill="black" stroke="black" points="489.77,-172.5 499.77,-169 489.77,-165.5 489.77,-172.5"/>
</g>
</g>
</svg>

    </div>
    <i id="fullscreen" class="icon ion-ios-resize module-graph-fullscreen-btn" aria-hidden="true"></i>
    <div class="btn-group size-buttons">
        <button id="zoom-in" class="btn btn-default btn-sm">Zoom in</button>
        <button id="reset" class="btn btn-default btn-sm">Reset</button>
        <button id="zoom-out" class="btn btn-default btn-sm">Zoom out</button>
    </div>
</div>
<script src="../js/libs/svg-pan-zoom.min.js"></script>
<script src="../js/svg-pan-zoom.controls.js"></script>

<ul class="nav nav-tabs" role="tablist">
    <li class="nav-item">
        <a href="#info" 
            class="nav-link"
            class="nav-link active"
            role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
    </li>
    <li class="nav-item">
        <a href="#source" 
            class="nav-link"
            
            role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
    </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">

        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/custom-object/custom-object.module.ts</code>
        </p>





        <div class="container-fluid module">
            <div class="row">
                <div class="col-sm-3">
                    <h3>Providers<a href="https://angular.io/api/core/NgModule#providers" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module providers"><span class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../injectables/CustomObjectService.html">CustomObjectService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/CustomObjectValidatorService.html">CustomObjectValidatorService</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Controllers<a href="https://docs.nestjs.com/controllers" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module controllers"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../controllers/CustomObjectController.html">CustomObjectController</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Imports<a href="https://angular.io/api/core/NgModule#imports" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module imports"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../modules/CommonModule.html">CommonModule</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../modules/OrganizationModule.html">OrganizationModule</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../modules/TeamsModule.html">TeamsModule</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Exports<a href="https://angular.io/api/core/NgModule#exports" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module exports"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../injectables/CustomObjectService.html">CustomObjectService</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>


    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Module } from &quot;@nestjs/common&quot;;
import { TypeOrmModule } from &quot;@nestjs/typeorm&quot;;
import {
  CustomObject,
  CustomObjectRepository,
} from &quot;@repo/thena-platform-entities&quot;;
import { CommonModule } from &quot;../common/common.module&quot;;
import { OrganizationModule } from &quot;../organization/organization.module&quot;;
import { TeamsModule } from &quot;../teams/teams.module&quot;;
import { CustomObjectController } from &quot;./controllers/custom-object.controller&quot;;
import { CustomObjectService } from &quot;./services/custom-object.service&quot;;
import { CustomObjectValidatorService } from &quot;./validators/custom-object.validator&quot;;

@Module({
  imports: [
    CommonModule,
    TypeOrmModule.forFeature([CustomObject, CustomObjectRepository]),
    OrganizationModule,
    TeamsModule,
  ],
  controllers: [CustomObjectController],
  providers: [
    CustomObjectService,
    CustomObjectRepository,
    CustomObjectValidatorService,
  ],
  exports: [CustomObjectService],
})
export class CustomObjectModule {}
</code></pre>
    </div>
</div>

















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'module';
            var COMPODOC_CURRENT_PAGE_URL = 'CustomObjectModule.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
