<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: dependencies Pages: 1 -->
<svg width="931pt" height="284pt"
 viewBox="0.00 0.00 931.00 284.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 280)">
<title>dependencies</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-280 927,-280 927,4 -4,4"/>
<text text-anchor="start" x="440.51" y="-42.4" font-family="Times-12" font-weight="bold" font-size="14.00">Legend</text>
<polygon fill="#ffffb3" stroke="transparent" points="227.5,-10 227.5,-30 247.5,-30 247.5,-10 227.5,-10"/>
<text text-anchor="start" x="251.13" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Declarations</text>
<polygon fill="#8dd3c7" stroke="transparent" points="340.5,-10 340.5,-30 360.5,-30 360.5,-10 340.5,-10"/>
<text text-anchor="start" x="364.23" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Module</text>
<polygon fill="#80b1d3" stroke="transparent" points="426.5,-10 426.5,-30 446.5,-30 446.5,-10 426.5,-10"/>
<text text-anchor="start" x="450.28" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Bootstrap</text>
<polygon fill="#fdb462" stroke="transparent" points="523.5,-10 523.5,-30 543.5,-30 543.5,-10 523.5,-10"/>
<text text-anchor="start" x="547.17" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Providers</text>
<polygon fill="#fb8072" stroke="transparent" points="619.5,-10 619.5,-30 639.5,-30 639.5,-10 619.5,-10"/>
<text text-anchor="start" x="643.23" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Exports</text>
<g id="clust1" class="cluster">
<title>cluster_CustomObjectModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="8,-70 8,-268 915,-268 915,-70 8,-70"/>
</g>
<g id="clust3" class="cluster">
<title>cluster_CustomObjectModule_imports</title>
<polygon fill="none" stroke="black" points="514,-78 514,-130 907,-130 907,-78 514,-78"/>
</g>
<g id="clust4" class="cluster">
<title>cluster_CustomObjectModule_exports</title>
<polygon fill="none" stroke="black" points="491,-208 491,-260 649,-260 649,-208 491,-208"/>
</g>
<g id="clust6" class="cluster">
<title>cluster_CustomObjectModule_providers</title>
<polygon fill="none" stroke="black" points="16,-78 16,-130 506,-130 506,-78 16,-78"/>
</g>
<!-- CommonModule -->
<g id="node1" class="node">
<title>CommonModule</title>
<polygon fill="#8dd3c7" stroke="black" points="898.67,-122 895.67,-126 874.67,-126 871.67,-122 787.33,-122 787.33,-86 898.67,-86 898.67,-122"/>
<text text-anchor="middle" x="843" y="-99.8" font-family="Times,serif" font-size="14.00">CommonModule</text>
</g>
<!-- CustomObjectModule -->
<g id="node4" class="node">
<title>CustomObjectModule</title>
<polygon fill="#8dd3c7" stroke="black" points="640.43,-187 637.43,-191 616.43,-191 613.43,-187 499.57,-187 499.57,-151 640.43,-151 640.43,-187"/>
<text text-anchor="middle" x="570" y="-164.8" font-family="Times,serif" font-size="14.00">CustomObjectModule</text>
</g>
<!-- CommonModule&#45;&gt;CustomObjectModule -->
<g id="edge1" class="edge">
<title>CommonModule&#45;&gt;CustomObjectModule</title>
<path fill="none" stroke="black" d="M843,-122.11C843,-141.34 843,-169 843,-169 843,-169 650.3,-169 650.3,-169"/>
<polygon fill="black" stroke="black" points="650.3,-165.5 640.3,-169 650.3,-172.5 650.3,-165.5"/>
</g>
<!-- OrganizationModule -->
<g id="node2" class="node">
<title>OrganizationModule</title>
<polygon fill="#8dd3c7" stroke="black" points="769.13,-122 766.13,-126 745.13,-126 742.13,-122 636.87,-122 636.87,-86 769.13,-86 769.13,-122"/>
<text text-anchor="middle" x="703" y="-99.8" font-family="Times,serif" font-size="14.00">OrganizationModule</text>
</g>
<!-- OrganizationModule&#45;&gt;CustomObjectModule -->
<g id="edge2" class="edge">
<title>OrganizationModule&#45;&gt;CustomObjectModule</title>
<path fill="none" stroke="black" d="M636.81,-104C631.17,-104 627.56,-104 627.56,-104 627.56,-104 627.56,-140.89 627.56,-140.89"/>
<polygon fill="black" stroke="black" points="624.06,-140.89 627.56,-150.89 631.06,-140.89 624.06,-140.89"/>
</g>
<!-- TeamsModule -->
<g id="node3" class="node">
<title>TeamsModule</title>
<polygon fill="#8dd3c7" stroke="black" points="618.37,-122 615.37,-126 594.37,-126 591.37,-122 521.63,-122 521.63,-86 618.37,-86 618.37,-122"/>
<text text-anchor="middle" x="570" y="-99.8" font-family="Times,serif" font-size="14.00">TeamsModule</text>
</g>
<!-- TeamsModule&#45;&gt;CustomObjectModule -->
<g id="edge3" class="edge">
<title>TeamsModule&#45;&gt;CustomObjectModule</title>
<path fill="none" stroke="black" d="M570,-122.11C570,-122.11 570,-140.99 570,-140.99"/>
<polygon fill="black" stroke="black" points="566.5,-140.99 570,-150.99 573.5,-140.99 566.5,-140.99"/>
</g>
<!-- CustomObjectService  -->
<g id="node5" class="node">
<title>CustomObjectService </title>
<polygon fill="#fb8072" stroke="black" points="641.36,-252 498.64,-252 498.64,-216 641.36,-216 641.36,-252"/>
<text text-anchor="middle" x="570" y="-229.8" font-family="Times,serif" font-size="14.00">CustomObjectService </text>
</g>
<!-- CustomObjectModule&#45;&gt;CustomObjectService  -->
<g id="edge4" class="edge">
<title>CustomObjectModule&#45;&gt;CustomObjectService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M570,-187.11C570,-187.11 570,-205.99 570,-205.99"/>
<polygon fill="black" stroke="black" points="566.5,-205.99 570,-215.99 573.5,-205.99 566.5,-205.99"/>
</g>
<!-- CustomObjectService -->
<g id="node6" class="node">
<title>CustomObjectService</title>
<ellipse fill="#fdb462" stroke="black" cx="402" cy="-104" rx="95.56" ry="18"/>
<text text-anchor="middle" x="402" y="-99.8" font-family="Times,serif" font-size="14.00">CustomObjectService</text>
</g>
<!-- CustomObjectService&#45;&gt;CustomObjectModule -->
<g id="edge5" class="edge">
<title>CustomObjectService&#45;&gt;CustomObjectModule</title>
<path fill="none" stroke="black" d="M497.78,-104C505.66,-104 510.68,-104 510.68,-104 510.68,-104 510.68,-140.89 510.68,-140.89"/>
<polygon fill="black" stroke="black" points="507.18,-140.89 510.68,-150.89 514.18,-140.89 507.18,-140.89"/>
</g>
<!-- CustomObjectValidatorService -->
<g id="node7" class="node">
<title>CustomObjectValidatorService</title>
<ellipse fill="#fdb462" stroke="black" cx="156" cy="-104" rx="132.5" ry="18"/>
<text text-anchor="middle" x="156" y="-99.8" font-family="Times,serif" font-size="14.00">CustomObjectValidatorService</text>
</g>
<!-- CustomObjectValidatorService&#45;&gt;CustomObjectModule -->
<g id="edge6" class="edge">
<title>CustomObjectValidatorService&#45;&gt;CustomObjectModule</title>
<path fill="none" stroke="black" d="M156,-122.11C156,-141.34 156,-169 156,-169 156,-169 489.77,-169 489.77,-169"/>
<polygon fill="black" stroke="black" points="489.77,-172.5 499.77,-169 489.77,-165.5 489.77,-172.5"/>
</g>
</g>
</svg>
