<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: dependencies Pages: 1 -->
<svg width="504pt" height="211pt"
 viewBox="0.00 0.00 504.00 211.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 207)">
<title>dependencies</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-207 500,-207 500,4 -4,4"/>
<text text-anchor="start" x="227.01" y="-42.4" font-family="Times-12" font-weight="bold" font-size="14.00">Legend</text>
<polygon fill="#ffffb3" stroke="transparent" points="14,-10 14,-30 34,-30 34,-10 14,-10"/>
<text text-anchor="start" x="37.63" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Declarations</text>
<polygon fill="#8dd3c7" stroke="transparent" points="127,-10 127,-30 147,-30 147,-10 127,-10"/>
<text text-anchor="start" x="150.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Module</text>
<polygon fill="#80b1d3" stroke="transparent" points="213,-10 213,-30 233,-30 233,-10 213,-10"/>
<text text-anchor="start" x="236.78" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Bootstrap</text>
<polygon fill="#fdb462" stroke="transparent" points="310,-10 310,-30 330,-30 330,-10 310,-10"/>
<text text-anchor="start" x="333.67" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Providers</text>
<polygon fill="#fb8072" stroke="transparent" points="406,-10 406,-30 426,-30 426,-10 406,-10"/>
<text text-anchor="start" x="429.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Exports</text>
<g id="clust1" class="cluster">
<title>cluster_ConfigModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="96,-70 96,-195 400,-195 400,-70 96,-70"/>
</g>
<g id="clust4" class="cluster">
<title>cluster_ConfigModule_exports</title>
<polygon fill="none" stroke="black" points="104,-135 104,-187 392,-187 392,-135 104,-135"/>
</g>
<!-- ConfigService  -->
<g id="node1" class="node">
<title>ConfigService </title>
<polygon fill="#fb8072" stroke="black" points="384.37,-179 283.63,-179 283.63,-143 384.37,-143 384.37,-179"/>
<text text-anchor="middle" x="334" y="-156.8" font-family="Times,serif" font-size="14.00">ConfigService </text>
</g>
<!-- TYPESENSE_CLIENT  -->
<g id="node2" class="node">
<title>TYPESENSE_CLIENT </title>
<polygon fill="#fb8072" stroke="black" points="266.03,-179 111.97,-179 111.97,-143 266.03,-143 266.03,-179"/>
<text text-anchor="middle" x="189" y="-156.8" font-family="Times,serif" font-size="14.00">TYPESENSE_CLIENT </text>
</g>
<!-- ConfigModule -->
<g id="node3" class="node">
<title>ConfigModule</title>
<polygon fill="#8dd3c7" stroke="black" points="310.44,-114 307.44,-118 286.44,-118 283.44,-114 211.56,-114 211.56,-78 310.44,-78 310.44,-114"/>
<text text-anchor="middle" x="261" y="-91.8" font-family="Times,serif" font-size="14.00">ConfigModule</text>
</g>
<!-- ConfigModule&#45;&gt;ConfigService  -->
<g id="edge1" class="edge">
<title>ConfigModule&#45;&gt;ConfigService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M297.02,-114.11C297.02,-114.11 297.02,-132.99 297.02,-132.99"/>
<polygon fill="black" stroke="black" points="293.52,-132.99 297.02,-142.99 300.52,-132.99 293.52,-132.99"/>
</g>
<!-- ConfigModule&#45;&gt;TYPESENSE_CLIENT  -->
<g id="edge2" class="edge">
<title>ConfigModule&#45;&gt;TYPESENSE_CLIENT </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M238.9,-114.11C238.9,-114.11 238.9,-132.99 238.9,-132.99"/>
<polygon fill="black" stroke="black" points="235.4,-132.99 238.9,-142.99 242.4,-132.99 235.4,-132.99"/>
</g>
</g>
</svg>
