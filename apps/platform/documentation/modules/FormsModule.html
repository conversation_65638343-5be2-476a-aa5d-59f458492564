<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content module">
                   <div class="content-data">



<ol class="breadcrumb">
    <li class="breadcrumb-item">Modules</li>
    <li class="breadcrumb-item" >FormsModule</li>
</ol>

<div class="text-center module-graph-container">
    <div id="module-graph-svg">
        <?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: dependencies Pages: 1 -->
<svg width="1133pt" height="284pt"
 viewBox="0.00 0.00 1133.00 284.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 280)">
<title>dependencies</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-280 1129,-280 1129,4 -4,4"/>
<text text-anchor="start" x="541.51" y="-42.4" font-family="Times-12" font-weight="bold" font-size="14.00">Legend</text>
<polygon fill="#ffffb3" stroke="transparent" points="328.5,-10 328.5,-30 348.5,-30 348.5,-10 328.5,-10"/>
<text text-anchor="start" x="352.13" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Declarations</text>
<polygon fill="#8dd3c7" stroke="transparent" points="441.5,-10 441.5,-30 461.5,-30 461.5,-10 441.5,-10"/>
<text text-anchor="start" x="465.23" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Module</text>
<polygon fill="#80b1d3" stroke="transparent" points="527.5,-10 527.5,-30 547.5,-30 547.5,-10 527.5,-10"/>
<text text-anchor="start" x="551.28" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Bootstrap</text>
<polygon fill="#fdb462" stroke="transparent" points="624.5,-10 624.5,-30 644.5,-30 644.5,-10 624.5,-10"/>
<text text-anchor="start" x="648.17" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Providers</text>
<polygon fill="#fb8072" stroke="transparent" points="720.5,-10 720.5,-30 740.5,-30 740.5,-10 720.5,-10"/>
<text text-anchor="start" x="744.23" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Exports</text>
<g id="clust1" class="cluster">
<title>cluster_FormsModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="8,-70 8,-268 1117,-268 1117,-70 8,-70"/>
</g>
<g id="clust4" class="cluster">
<title>cluster_FormsModule_exports</title>
<polygon fill="none" stroke="black" points="470,-208 470,-260 746,-260 746,-208 470,-208"/>
</g>
<g id="clust3" class="cluster">
<title>cluster_FormsModule_imports</title>
<polygon fill="none" stroke="black" points="566,-78 566,-130 1109,-130 1109,-78 566,-78"/>
</g>
<g id="clust6" class="cluster">
<title>cluster_FormsModule_providers</title>
<polygon fill="none" stroke="black" points="16,-78 16,-130 558,-130 558,-78 16,-78"/>
</g>
<!-- CommonModule -->
<g id="node1" class="node">
<title>CommonModule</title>
<polygon fill="#8dd3c7" stroke="black" points="1100.67,-122 1097.67,-126 1076.67,-126 1073.67,-122 989.33,-122 989.33,-86 1100.67,-86 1100.67,-122"/>
<text text-anchor="middle" x="1045" y="-99.8" font-family="Times,serif" font-size="14.00">CommonModule</text>
</g>
<!-- FormsModule -->
<g id="node5" class="node">
<title>FormsModule</title>
<polygon fill="#8dd3c7" stroke="black" points="669.83,-187 666.83,-191 645.83,-191 642.83,-187 574.17,-187 574.17,-151 669.83,-151 669.83,-187"/>
<text text-anchor="middle" x="622" y="-164.8" font-family="Times,serif" font-size="14.00">FormsModule</text>
</g>
<!-- CommonModule&#45;&gt;FormsModule -->
<g id="edge1" class="edge">
<title>CommonModule&#45;&gt;FormsModule</title>
<path fill="none" stroke="black" d="M1045,-122.29C1045,-144.21 1045,-178 1045,-178 1045,-178 679.7,-178 679.7,-178"/>
<polygon fill="black" stroke="black" points="679.7,-174.5 669.7,-178 679.7,-181.5 679.7,-174.5"/>
</g>
<!-- CustomFieldModule -->
<g id="node2" class="node">
<title>CustomFieldModule</title>
<polygon fill="#8dd3c7" stroke="black" points="970.89,-122 967.89,-126 946.89,-126 943.89,-122 839.11,-122 839.11,-86 970.89,-86 970.89,-122"/>
<text text-anchor="middle" x="905" y="-99.8" font-family="Times,serif" font-size="14.00">CustomFieldModule</text>
</g>
<!-- CustomFieldModule&#45;&gt;FormsModule -->
<g id="edge2" class="edge">
<title>CustomFieldModule&#45;&gt;FormsModule</title>
<path fill="none" stroke="black" d="M905,-122.11C905,-141.34 905,-169 905,-169 905,-169 679.69,-169 679.69,-169"/>
<polygon fill="black" stroke="black" points="679.69,-165.5 669.69,-169 679.69,-172.5 679.69,-165.5"/>
</g>
<!-- OrganizationModule -->
<g id="node3" class="node">
<title>OrganizationModule</title>
<polygon fill="#8dd3c7" stroke="black" points="821.13,-122 818.13,-126 797.13,-126 794.13,-122 688.87,-122 688.87,-86 821.13,-86 821.13,-122"/>
<text text-anchor="middle" x="755" y="-99.8" font-family="Times,serif" font-size="14.00">OrganizationModule</text>
</g>
<!-- OrganizationModule&#45;&gt;FormsModule -->
<g id="edge3" class="edge">
<title>OrganizationModule&#45;&gt;FormsModule</title>
<path fill="none" stroke="black" d="M713.29,-122.03C713.29,-138.4 713.29,-160 713.29,-160 713.29,-160 679.79,-160 679.79,-160"/>
<polygon fill="black" stroke="black" points="679.79,-156.5 669.79,-160 679.79,-163.5 679.79,-156.5"/>
</g>
<!-- TeamsModule -->
<g id="node4" class="node">
<title>TeamsModule</title>
<polygon fill="#8dd3c7" stroke="black" points="670.37,-122 667.37,-126 646.37,-126 643.37,-122 573.63,-122 573.63,-86 670.37,-86 670.37,-122"/>
<text text-anchor="middle" x="622" y="-99.8" font-family="Times,serif" font-size="14.00">TeamsModule</text>
</g>
<!-- TeamsModule&#45;&gt;FormsModule -->
<g id="edge4" class="edge">
<title>TeamsModule&#45;&gt;FormsModule</title>
<path fill="none" stroke="black" d="M622,-122.11C622,-122.11 622,-140.99 622,-140.99"/>
<polygon fill="black" stroke="black" points="618.5,-140.99 622,-150.99 625.5,-140.99 618.5,-140.99"/>
</g>
<!-- FormService  -->
<g id="node6" class="node">
<title>FormService </title>
<polygon fill="#fb8072" stroke="black" points="737.81,-252 646.19,-252 646.19,-216 737.81,-216 737.81,-252"/>
<text text-anchor="middle" x="692" y="-229.8" font-family="Times,serif" font-size="14.00">FormService </text>
</g>
<!-- FormsModule&#45;&gt;FormService  -->
<g id="edge5" class="edge">
<title>FormsModule&#45;&gt;FormService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M657.88,-187.11C657.88,-187.11 657.88,-205.99 657.88,-205.99"/>
<polygon fill="black" stroke="black" points="654.38,-205.99 657.88,-215.99 661.38,-205.99 654.38,-205.99"/>
</g>
<!-- FormsValidatorService  -->
<g id="node7" class="node">
<title>FormsValidatorService </title>
<polygon fill="#fb8072" stroke="black" points="628.12,-252 477.88,-252 477.88,-216 628.12,-216 628.12,-252"/>
<text text-anchor="middle" x="553" y="-229.8" font-family="Times,serif" font-size="14.00">FormsValidatorService </text>
</g>
<!-- FormsModule&#45;&gt;FormsValidatorService  -->
<g id="edge6" class="edge">
<title>FormsModule&#45;&gt;FormsValidatorService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M601.2,-187.11C601.2,-187.11 601.2,-205.99 601.2,-205.99"/>
<polygon fill="black" stroke="black" points="597.7,-205.99 601.2,-215.99 604.7,-205.99 597.7,-205.99"/>
</g>
<!-- FormService -->
<g id="node8" class="node">
<title>FormService</title>
<ellipse fill="#fdb462" stroke="black" cx="489" cy="-104" rx="60.83" ry="18"/>
<text text-anchor="middle" x="489" y="-99.8" font-family="Times,serif" font-size="14.00">FormService</text>
</g>
<!-- FormService&#45;&gt;FormsModule -->
<g id="edge7" class="edge">
<title>FormService&#45;&gt;FormsModule</title>
<path fill="none" stroke="black" d="M513.93,-120.5C513.93,-137.02 513.93,-160 513.93,-160 513.93,-160 564.28,-160 564.28,-160"/>
<polygon fill="black" stroke="black" points="564.28,-163.5 574.28,-160 564.28,-156.5 564.28,-163.5"/>
</g>
<!-- FormSetupService -->
<g id="node9" class="node">
<title>FormSetupService</title>
<ellipse fill="#fdb462" stroke="black" cx="327" cy="-104" rx="82.82" ry="18"/>
<text text-anchor="middle" x="327" y="-99.8" font-family="Times,serif" font-size="14.00">FormSetupService</text>
</g>
<!-- FormSetupService&#45;&gt;FormsModule -->
<g id="edge8" class="edge">
<title>FormSetupService&#45;&gt;FormsModule</title>
<path fill="none" stroke="black" d="M327,-122.11C327,-141.34 327,-169 327,-169 327,-169 564.28,-169 564.28,-169"/>
<polygon fill="black" stroke="black" points="564.28,-172.5 574.28,-169 564.28,-165.5 564.28,-172.5"/>
</g>
<!-- FormsValidatorService -->
<g id="node10" class="node">
<title>FormsValidatorService</title>
<ellipse fill="#fdb462" stroke="black" cx="125" cy="-104" rx="101.28" ry="18"/>
<text text-anchor="middle" x="125" y="-99.8" font-family="Times,serif" font-size="14.00">FormsValidatorService</text>
</g>
<!-- FormsValidatorService&#45;&gt;FormsModule -->
<g id="edge9" class="edge">
<title>FormsValidatorService&#45;&gt;FormsModule</title>
<path fill="none" stroke="black" d="M125,-122.29C125,-144.21 125,-178 125,-178 125,-178 563.89,-178 563.89,-178"/>
<polygon fill="black" stroke="black" points="563.89,-181.5 573.89,-178 563.89,-174.5 563.89,-181.5"/>
</g>
</g>
</svg>

    </div>
    <i id="fullscreen" class="icon ion-ios-resize module-graph-fullscreen-btn" aria-hidden="true"></i>
    <div class="btn-group size-buttons">
        <button id="zoom-in" class="btn btn-default btn-sm">Zoom in</button>
        <button id="reset" class="btn btn-default btn-sm">Reset</button>
        <button id="zoom-out" class="btn btn-default btn-sm">Zoom out</button>
    </div>
</div>
<script src="../js/libs/svg-pan-zoom.min.js"></script>
<script src="../js/svg-pan-zoom.controls.js"></script>

<ul class="nav nav-tabs" role="tablist">
    <li class="nav-item">
        <a href="#info" 
            class="nav-link"
            class="nav-link active"
            role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
    </li>
    <li class="nav-item">
        <a href="#source" 
            class="nav-link"
            
            role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
    </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">

        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/forms/forms.module.ts</code>
        </p>





        <div class="container-fluid module">
            <div class="row">
                <div class="col-sm-3">
                    <h3>Providers<a href="https://angular.io/api/core/NgModule#providers" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module providers"><span class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../injectables/FormService.html">FormService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/FormSetupService.html">FormSetupService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/FormsValidatorService.html">FormsValidatorService</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Controllers<a href="https://docs.nestjs.com/controllers" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module controllers"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../controllers/FormsController.html">FormsController</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Imports<a href="https://angular.io/api/core/NgModule#imports" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module imports"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../modules/CommonModule.html">CommonModule</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../modules/CustomFieldModule.html">CustomFieldModule</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../modules/OrganizationModule.html">OrganizationModule</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../modules/TeamsModule.html">TeamsModule</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Exports<a href="https://angular.io/api/core/NgModule#exports" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module exports"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../injectables/FormService.html">FormService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/FormsValidatorService.html">FormsValidatorService</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>


    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Module } from &quot;@nestjs/common&quot;;
import { TypeOrmModule } from &quot;@nestjs/typeorm&quot;;
import {
  Form,
  FormFieldEventRepository,
  FormFieldEvents,
  FormRepository,
  TransactionService,
} from &quot;@repo/thena-platform-entities&quot;;
import { CommonModule } from &quot;../common/common.module&quot;;
import { CustomFieldModule } from &quot;../custom-field/custom-field.module&quot;;
import { OrganizationModule } from &quot;../organization/organization.module&quot;;
import { TeamsModule } from &quot;../teams/teams.module&quot;;
import { FormsController } from &quot;./controllers/form.controller&quot;;
import { FormSetupService } from &quot;./form-setup&quot;;
import { FormsListeners } from &quot;./listeners/forms.listeners&quot;;
import { FormService } from &quot;./services/form.service&quot;;
import { FormsValidatorService } from &quot;./validators/form.validator&quot;;

@Module({
  imports: [
    CommonModule,
    TypeOrmModule.forFeature([
      Form,
      FormRepository,
      FormFieldEvents,
      FormFieldEventRepository,
    ]),
    OrganizationModule,
    CustomFieldModule,
    TeamsModule,
  ],
  controllers: [FormsController],
  providers: [
    FormsValidatorService,
    FormService,
    FormRepository,
    FormFieldEventRepository,
    TransactionService,
    FormsListeners,
    FormSetupService,
  ],
  exports: [FormService, FormsValidatorService],
})
export class FormsModule {}
</code></pre>
    </div>
</div>

















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'module';
            var COMPODOC_CURRENT_PAGE_URL = 'FormsModule.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
