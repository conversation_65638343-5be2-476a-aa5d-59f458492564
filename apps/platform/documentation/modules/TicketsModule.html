<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content module">
                   <div class="content-data">



<ol class="breadcrumb">
    <li class="breadcrumb-item">Modules</li>
    <li class="breadcrumb-item" >TicketsModule</li>
</ol>

<div class="text-center module-graph-container">
    <div id="module-graph-svg">
        <?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: dependencies Pages: 1 -->
<svg width="4663pt" height="284pt"
 viewBox="0.00 0.00 4663.00 284.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 280)">
<title>dependencies</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-280 4659,-280 4659,4 -4,4"/>
<text text-anchor="start" x="2306.51" y="-42.4" font-family="Times-12" font-weight="bold" font-size="14.00">Legend</text>
<polygon fill="#ffffb3" stroke="transparent" points="2093.5,-10 2093.5,-30 2113.5,-30 2113.5,-10 2093.5,-10"/>
<text text-anchor="start" x="2117.13" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Declarations</text>
<polygon fill="#8dd3c7" stroke="transparent" points="2206.5,-10 2206.5,-30 2226.5,-30 2226.5,-10 2206.5,-10"/>
<text text-anchor="start" x="2230.23" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Module</text>
<polygon fill="#80b1d3" stroke="transparent" points="2292.5,-10 2292.5,-30 2312.5,-30 2312.5,-10 2292.5,-10"/>
<text text-anchor="start" x="2316.28" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Bootstrap</text>
<polygon fill="#fdb462" stroke="transparent" points="2389.5,-10 2389.5,-30 2409.5,-30 2409.5,-10 2389.5,-10"/>
<text text-anchor="start" x="2413.17" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Providers</text>
<polygon fill="#fb8072" stroke="transparent" points="2485.5,-10 2485.5,-30 2505.5,-30 2505.5,-10 2485.5,-10"/>
<text text-anchor="start" x="2509.23" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Exports</text>
<g id="clust1" class="cluster">
<title>cluster_TicketsModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="8,-70 8,-268 4647,-268 4647,-70 8,-70"/>
</g>
<g id="clust4" class="cluster">
<title>cluster_TicketsModule_exports</title>
<polygon fill="none" stroke="black" points="1851,-208 1851,-260 3801,-260 3801,-208 1851,-208"/>
</g>
<g id="clust3" class="cluster">
<title>cluster_TicketsModule_imports</title>
<polygon fill="none" stroke="black" points="3524,-78 3524,-130 4639,-130 4639,-78 3524,-78"/>
</g>
<g id="clust6" class="cluster">
<title>cluster_TicketsModule_providers</title>
<polygon fill="none" stroke="black" points="16,-78 16,-130 3516,-130 3516,-78 16,-78"/>
</g>
<!-- AccountsModule -->
<g id="node1" class="node">
<title>AccountsModule</title>
<polygon fill="#8dd3c7" stroke="black" points="4631.42,-122 4628.42,-126 4607.42,-126 4604.42,-122 4518.58,-122 4518.58,-86 4631.42,-86 4631.42,-122"/>
<text text-anchor="middle" x="4575" y="-99.8" font-family="Times,serif" font-size="14.00">AccountsModule</text>
</g>
<!-- TicketsModule -->
<g id="node10" class="node">
<title>TicketsModule</title>
<polygon fill="#8dd3c7" stroke="black" points="2797.26,-187 2794.26,-191 2773.26,-191 2770.26,-187 2696.74,-187 2696.74,-151 2797.26,-151 2797.26,-187"/>
<text text-anchor="middle" x="2747" y="-164.8" font-family="Times,serif" font-size="14.00">TicketsModule</text>
</g>
<!-- AccountsModule&#45;&gt;TicketsModule -->
<g id="edge1" class="edge">
<title>AccountsModule&#45;&gt;TicketsModule</title>
<path fill="none" stroke="black" d="M4575,-122.28C4575,-143.32 4575,-175 4575,-175 4575,-175 2807.32,-175 2807.32,-175"/>
<polygon fill="black" stroke="black" points="2807.32,-171.5 2797.32,-175 2807.32,-178.5 2807.32,-171.5"/>
</g>
<!-- ActivitiesModule -->
<g id="node2" class="node">
<title>ActivitiesModule</title>
<polygon fill="#8dd3c7" stroke="black" points="4500.98,-122 4497.98,-126 4476.98,-126 4473.98,-122 4387.02,-122 4387.02,-86 4500.98,-86 4500.98,-122"/>
<text text-anchor="middle" x="4444" y="-99.8" font-family="Times,serif" font-size="14.00">ActivitiesModule</text>
</g>
<!-- ActivitiesModule&#45;&gt;TicketsModule -->
<g id="edge2" class="edge">
<title>ActivitiesModule&#45;&gt;TicketsModule</title>
<path fill="none" stroke="black" d="M4444,-122.13C4444,-142.57 4444,-173 4444,-173 4444,-173 2807.33,-173 2807.33,-173"/>
<polygon fill="black" stroke="black" points="2807.33,-169.5 2797.33,-173 2807.33,-176.5 2807.33,-169.5"/>
</g>
<!-- CommonModule -->
<g id="node3" class="node">
<title>CommonModule</title>
<polygon fill="#8dd3c7" stroke="black" points="4368.67,-122 4365.67,-126 4344.67,-126 4341.67,-122 4257.33,-122 4257.33,-86 4368.67,-86 4368.67,-122"/>
<text text-anchor="middle" x="4313" y="-99.8" font-family="Times,serif" font-size="14.00">CommonModule</text>
</g>
<!-- CommonModule&#45;&gt;TicketsModule -->
<g id="edge3" class="edge">
<title>CommonModule&#45;&gt;TicketsModule</title>
<path fill="none" stroke="black" d="M4313,-122.31C4313,-142.15 4313,-171 4313,-171 4313,-171 2807.33,-171 2807.33,-171"/>
<polygon fill="black" stroke="black" points="2807.33,-167.5 2797.33,-171 2807.33,-174.5 2807.33,-167.5"/>
</g>
<!-- ConfigModule -->
<g id="node4" class="node">
<title>ConfigModule</title>
<polygon fill="#8dd3c7" stroke="black" points="4239.44,-122 4236.44,-126 4215.44,-126 4212.44,-122 4140.56,-122 4140.56,-86 4239.44,-86 4239.44,-122"/>
<text text-anchor="middle" x="4190" y="-99.8" font-family="Times,serif" font-size="14.00">ConfigModule</text>
</g>
<!-- ConfigModule&#45;&gt;TicketsModule -->
<g id="edge4" class="edge">
<title>ConfigModule&#45;&gt;TicketsModule</title>
<path fill="none" stroke="black" d="M4190,-122.11C4190,-141.34 4190,-169 4190,-169 4190,-169 2807.51,-169 2807.51,-169"/>
<polygon fill="black" stroke="black" points="2807.51,-165.5 2797.51,-169 2807.51,-172.5 2807.51,-165.5"/>
</g>
<!-- CustomFieldModule -->
<g id="node5" class="node">
<title>CustomFieldModule</title>
<polygon fill="#8dd3c7" stroke="black" points="4122.89,-122 4119.89,-126 4098.89,-126 4095.89,-122 3991.11,-122 3991.11,-86 4122.89,-86 4122.89,-122"/>
<text text-anchor="middle" x="4057" y="-99.8" font-family="Times,serif" font-size="14.00">CustomFieldModule</text>
</g>
<!-- CustomFieldModule&#45;&gt;TicketsModule -->
<g id="edge5" class="edge">
<title>CustomFieldModule&#45;&gt;TicketsModule</title>
<path fill="none" stroke="black" d="M4057,-122.22C4057,-140.83 4057,-167 4057,-167 4057,-167 2807.55,-167 2807.55,-167"/>
<polygon fill="black" stroke="black" points="2807.55,-163.5 2797.55,-167 2807.55,-170.5 2807.55,-163.5"/>
</g>
<!-- FormsModule -->
<g id="node6" class="node">
<title>FormsModule</title>
<polygon fill="#8dd3c7" stroke="black" points="3972.83,-122 3969.83,-126 3948.83,-126 3945.83,-122 3877.17,-122 3877.17,-86 3972.83,-86 3972.83,-122"/>
<text text-anchor="middle" x="3925" y="-99.8" font-family="Times,serif" font-size="14.00">FormsModule</text>
</g>
<!-- FormsModule&#45;&gt;TicketsModule -->
<g id="edge6" class="edge">
<title>FormsModule&#45;&gt;TicketsModule</title>
<path fill="none" stroke="black" d="M3925,-122.3C3925,-140.27 3925,-165 3925,-165 3925,-165 2807.62,-165 2807.62,-165"/>
<polygon fill="black" stroke="black" points="2807.62,-161.5 2797.62,-165 2807.62,-168.5 2807.62,-161.5"/>
</g>
<!-- StorageModule -->
<g id="node7" class="node">
<title>StorageModule</title>
<polygon fill="#8dd3c7" stroke="black" points="3859.31,-122 3856.31,-126 3835.31,-126 3832.31,-122 3756.69,-122 3756.69,-86 3859.31,-86 3859.31,-122"/>
<text text-anchor="middle" x="3808" y="-99.8" font-family="Times,serif" font-size="14.00">StorageModule</text>
</g>
<!-- StorageModule&#45;&gt;TicketsModule -->
<g id="edge7" class="edge">
<title>StorageModule&#45;&gt;TicketsModule</title>
<path fill="none" stroke="black" d="M3774.79,-122.02C3774.79,-139.37 3774.79,-163 3774.79,-163 3774.79,-163 2807.27,-163 2807.27,-163"/>
<polygon fill="black" stroke="black" points="2807.27,-159.5 2797.27,-163 2807.27,-166.5 2807.27,-159.5"/>
</g>
<!-- TeamsModule -->
<g id="node8" class="node">
<title>TeamsModule</title>
<polygon fill="#8dd3c7" stroke="black" points="3738.37,-122 3735.37,-126 3714.37,-126 3711.37,-122 3641.63,-122 3641.63,-86 3738.37,-86 3738.37,-122"/>
<text text-anchor="middle" x="3690" y="-99.8" font-family="Times,serif" font-size="14.00">TeamsModule</text>
</g>
<!-- TeamsModule&#45;&gt;TicketsModule -->
<g id="edge8" class="edge">
<title>TeamsModule&#45;&gt;TicketsModule</title>
<path fill="none" stroke="black" d="M3690,-122.04C3690,-138.73 3690,-161 3690,-161 3690,-161 2807.37,-161 2807.37,-161"/>
<polygon fill="black" stroke="black" points="2807.37,-157.5 2797.37,-161 2807.37,-164.5 2807.37,-157.5"/>
</g>
<!-- UsersModule -->
<g id="node9" class="node">
<title>UsersModule</title>
<polygon fill="#8dd3c7" stroke="black" points="3623.92,-122 3620.92,-126 3599.92,-126 3596.92,-122 3532.08,-122 3532.08,-86 3623.92,-86 3623.92,-122"/>
<text text-anchor="middle" x="3578" y="-99.8" font-family="Times,serif" font-size="14.00">UsersModule</text>
</g>
<!-- UsersModule&#45;&gt;TicketsModule -->
<g id="edge9" class="edge">
<title>UsersModule&#45;&gt;TicketsModule</title>
<path fill="none" stroke="black" d="M3552.82,-122.01C3552.82,-138.05 3552.82,-159 3552.82,-159 3552.82,-159 2807.41,-159 2807.41,-159"/>
<polygon fill="black" stroke="black" points="2807.41,-155.5 2797.41,-159 2807.41,-162.5 2807.41,-155.5"/>
</g>
<!-- CachedTicketPriorityRepository  -->
<g id="node11" class="node">
<title>CachedTicketPriorityRepository </title>
<polygon fill="#fb8072" stroke="black" points="3792.96,-252 3591.04,-252 3591.04,-216 3792.96,-216 3792.96,-252"/>
<text text-anchor="middle" x="3692" y="-229.8" font-family="Times,serif" font-size="14.00">CachedTicketPriorityRepository </text>
</g>
<!-- TicketsModule&#45;&gt;CachedTicketPriorityRepository  -->
<g id="edge10" class="edge">
<title>TicketsModule&#45;&gt;CachedTicketPriorityRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2797.3,-177C2981.35,-177 3607.49,-177 3607.49,-177 3607.49,-177 3607.49,-205.96 3607.49,-205.96"/>
<polygon fill="black" stroke="black" points="3603.99,-205.96 3607.49,-215.96 3610.99,-205.96 3603.99,-205.96"/>
</g>
<!-- CachedTicketRepository  -->
<g id="node12" class="node">
<title>CachedTicketRepository </title>
<polygon fill="#fb8072" stroke="black" points="3573.18,-252 3414.82,-252 3414.82,-216 3573.18,-216 3573.18,-252"/>
<text text-anchor="middle" x="3494" y="-229.8" font-family="Times,serif" font-size="14.00">CachedTicketRepository </text>
</g>
<!-- TicketsModule&#45;&gt;CachedTicketRepository  -->
<g id="edge11" class="edge">
<title>TicketsModule&#45;&gt;CachedTicketRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2797.5,-179C2960.35,-179 3461.12,-179 3461.12,-179 3461.12,-179 3461.12,-205.99 3461.12,-205.99"/>
<polygon fill="black" stroke="black" points="3457.62,-205.99 3461.12,-215.99 3464.62,-205.99 3457.62,-205.99"/>
</g>
<!-- CachedTicketStatusRepository  -->
<g id="node13" class="node">
<title>CachedTicketStatusRepository </title>
<polygon fill="#fb8072" stroke="black" points="3396.4,-252 3203.6,-252 3203.6,-216 3396.4,-216 3396.4,-252"/>
<text text-anchor="middle" x="3300" y="-229.8" font-family="Times,serif" font-size="14.00">CachedTicketStatusRepository </text>
</g>
<!-- TicketsModule&#45;&gt;CachedTicketStatusRepository  -->
<g id="edge12" class="edge">
<title>TicketsModule&#45;&gt;CachedTicketStatusRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2797.46,-181C2925.67,-181 3253.89,-181 3253.89,-181 3253.89,-181 3253.89,-205.76 3253.89,-205.76"/>
<polygon fill="black" stroke="black" points="3250.39,-205.76 3253.89,-215.76 3257.39,-205.76 3250.39,-205.76"/>
</g>
<!-- CachedTicketTypeRepository  -->
<g id="node14" class="node">
<title>CachedTicketTypeRepository </title>
<polygon fill="#fb8072" stroke="black" points="3185.95,-252 2998.05,-252 2998.05,-216 3185.95,-216 3185.95,-252"/>
<text text-anchor="middle" x="3092" y="-229.8" font-family="Times,serif" font-size="14.00">CachedTicketTypeRepository </text>
</g>
<!-- TicketsModule&#45;&gt;CachedTicketTypeRepository  -->
<g id="edge13" class="edge">
<title>TicketsModule&#45;&gt;CachedTicketTypeRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2797.57,-183C2883.09,-183 3047.09,-183 3047.09,-183 3047.09,-183 3047.09,-205.88 3047.09,-205.88"/>
<polygon fill="black" stroke="black" points="3043.59,-205.88 3047.09,-215.88 3050.59,-205.88 3043.59,-205.88"/>
</g>
<!-- TicketDraftService  -->
<g id="node15" class="node">
<title>TicketDraftService </title>
<polygon fill="#fb8072" stroke="black" points="2980.27,-252 2853.73,-252 2853.73,-216 2980.27,-216 2980.27,-252"/>
<text text-anchor="middle" x="2917" y="-229.8" font-family="Times,serif" font-size="14.00">TicketDraftService </text>
</g>
<!-- TicketsModule&#45;&gt;TicketDraftService  -->
<g id="edge14" class="edge">
<title>TicketsModule&#45;&gt;TicketDraftService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2797.43,-185C2854.31,-185 2938.95,-185 2938.95,-185 2938.95,-185 2938.95,-205.75 2938.95,-205.75"/>
<polygon fill="black" stroke="black" points="2935.45,-205.75 2938.95,-215.75 2942.45,-205.75 2935.45,-205.75"/>
</g>
<!-- TicketPriorityActionService  -->
<g id="node16" class="node">
<title>TicketPriorityActionService </title>
<polygon fill="#fb8072" stroke="black" points="2836.12,-252 2657.88,-252 2657.88,-216 2836.12,-216 2836.12,-252"/>
<text text-anchor="middle" x="2747" y="-229.8" font-family="Times,serif" font-size="14.00">TicketPriorityActionService </text>
</g>
<!-- TicketsModule&#45;&gt;TicketPriorityActionService  -->
<g id="edge15" class="edge">
<title>TicketsModule&#45;&gt;TicketPriorityActionService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2747,-187.11C2747,-187.11 2747,-205.99 2747,-205.99"/>
<polygon fill="black" stroke="black" points="2743.5,-205.99 2747,-215.99 2750.5,-205.99 2743.5,-205.99"/>
</g>
<!-- TicketRepository  -->
<g id="node17" class="node">
<title>TicketRepository </title>
<polygon fill="#fb8072" stroke="black" points="2640.2,-252 2523.8,-252 2523.8,-216 2640.2,-216 2640.2,-252"/>
<text text-anchor="middle" x="2582" y="-229.8" font-family="Times,serif" font-size="14.00">TicketRepository </text>
</g>
<!-- TicketsModule&#45;&gt;TicketRepository  -->
<g id="edge16" class="edge">
<title>TicketsModule&#45;&gt;TicketRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2696.74,-185C2654.09,-185 2599.76,-185 2599.76,-185 2599.76,-185 2599.76,-205.75 2599.76,-205.75"/>
<polygon fill="black" stroke="black" points="2596.26,-205.75 2599.76,-215.75 2603.26,-205.75 2596.26,-205.75"/>
</g>
<!-- TicketStatusActionService  -->
<g id="node18" class="node">
<title>TicketStatusActionService </title>
<polygon fill="#fb8072" stroke="black" points="2505.57,-252 2336.43,-252 2336.43,-216 2505.57,-216 2505.57,-252"/>
<text text-anchor="middle" x="2421" y="-229.8" font-family="Times,serif" font-size="14.00">TicketStatusActionService </text>
</g>
<!-- TicketsModule&#45;&gt;TicketStatusActionService  -->
<g id="edge17" class="edge">
<title>TicketsModule&#45;&gt;TicketStatusActionService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2696.47,-183C2616.07,-183 2467.98,-183 2467.98,-183 2467.98,-183 2467.98,-205.88 2467.98,-205.88"/>
<polygon fill="black" stroke="black" points="2464.48,-205.88 2467.98,-215.88 2471.48,-205.88 2464.48,-205.88"/>
</g>
<!-- TicketTypeActionService  -->
<g id="node19" class="node">
<title>TicketTypeActionService </title>
<polygon fill="#fb8072" stroke="black" points="2318.11,-252 2153.89,-252 2153.89,-216 2318.11,-216 2318.11,-252"/>
<text text-anchor="middle" x="2236" y="-229.8" font-family="Times,serif" font-size="14.00">TicketTypeActionService </text>
</g>
<!-- TicketsModule&#45;&gt;TicketTypeActionService  -->
<g id="edge18" class="edge">
<title>TicketsModule&#45;&gt;TicketTypeActionService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2696.58,-181C2575.83,-181 2280.01,-181 2280.01,-181 2280.01,-181 2280.01,-205.76 2280.01,-205.76"/>
<polygon fill="black" stroke="black" points="2276.51,-205.76 2280.01,-215.76 2283.51,-205.76 2276.51,-205.76"/>
</g>
<!-- TicketValidationService  -->
<g id="node20" class="node">
<title>TicketValidationService </title>
<polygon fill="#fb8072" stroke="black" points="2136.34,-252 1979.66,-252 1979.66,-216 2136.34,-216 2136.34,-252"/>
<text text-anchor="middle" x="2058" y="-229.8" font-family="Times,serif" font-size="14.00">TicketValidationService </text>
</g>
<!-- TicketsModule&#45;&gt;TicketValidationService  -->
<g id="edge19" class="edge">
<title>TicketsModule&#45;&gt;TicketValidationService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2696.47,-179C2537.79,-179 2059.86,-179 2059.86,-179 2059.86,-179 2059.86,-205.99 2059.86,-205.99"/>
<polygon fill="black" stroke="black" points="2056.36,-205.99 2059.86,-215.99 2063.36,-205.99 2056.36,-205.99"/>
</g>
<!-- TicketsService  -->
<g id="node21" class="node">
<title>TicketsService </title>
<polygon fill="#fb8072" stroke="black" points="1961.19,-252 1858.81,-252 1858.81,-216 1961.19,-216 1961.19,-252"/>
<text text-anchor="middle" x="1910" y="-229.8" font-family="Times,serif" font-size="14.00">TicketsService </text>
</g>
<!-- TicketsModule&#45;&gt;TicketsService  -->
<g id="edge20" class="edge">
<title>TicketsModule&#45;&gt;TicketsService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2696.64,-177C2518.26,-177 1927.11,-177 1927.11,-177 1927.11,-177 1927.11,-205.96 1927.11,-205.96"/>
<polygon fill="black" stroke="black" points="1923.61,-205.96 1927.11,-215.96 1930.61,-205.96 1923.61,-205.96"/>
</g>
<!-- RoundRobinStrategy -->
<g id="node22" class="node">
<title>RoundRobinStrategy</title>
<ellipse fill="#fdb462" stroke="black" cx="3415" cy="-104" rx="92.66" ry="18"/>
<text text-anchor="middle" x="3415" y="-99.8" font-family="Times,serif" font-size="14.00">RoundRobinStrategy</text>
</g>
<!-- RoundRobinStrategy&#45;&gt;TicketsModule -->
<g id="edge21" class="edge">
<title>RoundRobinStrategy&#45;&gt;TicketsModule</title>
<path fill="none" stroke="black" d="M3359.44,-118.48C3359.44,-134.17 3359.44,-157 3359.44,-157 3359.44,-157 2807.38,-157 2807.38,-157"/>
<polygon fill="black" stroke="black" points="2807.38,-153.5 2797.38,-157 2807.38,-160.5 2807.38,-153.5"/>
</g>
<!-- ThenaAgentAllocator -->
<g id="node23" class="node">
<title>ThenaAgentAllocator</title>
<ellipse fill="#fdb462" stroke="black" cx="3209" cy="-104" rx="95.47" ry="18"/>
<text text-anchor="middle" x="3209" y="-99.8" font-family="Times,serif" font-size="14.00">ThenaAgentAllocator</text>
</g>
<!-- ThenaAgentAllocator&#45;&gt;TicketsModule -->
<g id="edge22" class="edge">
<title>ThenaAgentAllocator&#45;&gt;TicketsModule</title>
<path fill="none" stroke="black" d="M3149.75,-118.21C3149.75,-133.3 3149.75,-155 3149.75,-155 3149.75,-155 2807.6,-155 2807.6,-155"/>
<polygon fill="black" stroke="black" points="2807.6,-151.5 2797.6,-155 2807.6,-158.5 2807.6,-151.5"/>
</g>
<!-- ThenaRequestRouterEngine -->
<g id="node24" class="node">
<title>ThenaRequestRouterEngine</title>
<ellipse fill="#fdb462" stroke="black" cx="2976" cy="-104" rx="119.83" ry="18"/>
<text text-anchor="middle" x="2976" y="-99.8" font-family="Times,serif" font-size="14.00">ThenaRequestRouterEngine</text>
</g>
<!-- ThenaRequestRouterEngine&#45;&gt;TicketsModule -->
<g id="edge23" class="edge">
<title>ThenaRequestRouterEngine&#45;&gt;TicketsModule</title>
<path fill="none" stroke="black" d="M2897.52,-117.65C2897.52,-132.15 2897.52,-153 2897.52,-153 2897.52,-153 2807.54,-153 2807.54,-153"/>
<polygon fill="black" stroke="black" points="2807.54,-149.5 2797.54,-153 2807.54,-156.5 2807.54,-149.5"/>
</g>
<!-- ThenaRuleEvaluator -->
<g id="node25" class="node">
<title>ThenaRuleEvaluator</title>
<ellipse fill="#fdb462" stroke="black" cx="2747" cy="-104" rx="90.89" ry="18"/>
<text text-anchor="middle" x="2747" y="-99.8" font-family="Times,serif" font-size="14.00">ThenaRuleEvaluator</text>
</g>
<!-- ThenaRuleEvaluator&#45;&gt;TicketsModule -->
<g id="edge24" class="edge">
<title>ThenaRuleEvaluator&#45;&gt;TicketsModule</title>
<path fill="none" stroke="black" d="M2747,-122.11C2747,-122.11 2747,-140.99 2747,-140.99"/>
<polygon fill="black" stroke="black" points="2743.5,-140.99 2747,-150.99 2750.5,-140.99 2743.5,-140.99"/>
</g>
<!-- TicketAnnotatorService -->
<g id="node26" class="node">
<title>TicketAnnotatorService</title>
<ellipse fill="#fdb462" stroke="black" cx="2534" cy="-104" rx="103.63" ry="18"/>
<text text-anchor="middle" x="2534" y="-99.8" font-family="Times,serif" font-size="14.00">TicketAnnotatorService</text>
</g>
<!-- TicketAnnotatorService&#45;&gt;TicketsModule -->
<g id="edge25" class="edge">
<title>TicketAnnotatorService&#45;&gt;TicketsModule</title>
<path fill="none" stroke="black" d="M2561.7,-121.41C2561.7,-135.5 2561.7,-153 2561.7,-153 2561.7,-153 2686.37,-153 2686.37,-153"/>
<polygon fill="black" stroke="black" points="2686.37,-156.5 2696.37,-153 2686.37,-149.5 2686.37,-156.5"/>
</g>
<!-- TicketDraftService -->
<g id="node27" class="node">
<title>TicketDraftService</title>
<ellipse fill="#fdb462" stroke="black" cx="2327" cy="-104" rx="85.06" ry="18"/>
<text text-anchor="middle" x="2327" y="-99.8" font-family="Times,serif" font-size="14.00">TicketDraftService</text>
</g>
<!-- TicketDraftService&#45;&gt;TicketsModule -->
<g id="edge26" class="edge">
<title>TicketDraftService&#45;&gt;TicketsModule</title>
<path fill="none" stroke="black" d="M2374.12,-119.03C2374.12,-134.07 2374.12,-155 2374.12,-155 2374.12,-155 2686.71,-155 2686.71,-155"/>
<polygon fill="black" stroke="black" points="2686.71,-158.5 2696.71,-155 2686.71,-151.5 2686.71,-158.5"/>
</g>
<!-- TicketPriorityActionService -->
<g id="node28" class="node">
<title>TicketPriorityActionService</title>
<ellipse fill="#fdb462" stroke="black" cx="2104" cy="-104" rx="120.4" ry="18"/>
<text text-anchor="middle" x="2104" y="-99.8" font-family="Times,serif" font-size="14.00">TicketPriorityActionService</text>
</g>
<!-- TicketPriorityActionService&#45;&gt;TicketsModule -->
<g id="edge27" class="edge">
<title>TicketPriorityActionService&#45;&gt;TicketsModule</title>
<path fill="none" stroke="black" d="M2189.2,-116.83C2189.2,-132.52 2189.2,-157 2189.2,-157 2189.2,-157 2686.51,-157 2686.51,-157"/>
<polygon fill="black" stroke="black" points="2686.51,-160.5 2696.51,-157 2686.51,-153.5 2686.51,-160.5"/>
</g>
<!-- TicketSearchService -->
<g id="node29" class="node">
<title>TicketSearchService</title>
<ellipse fill="#fdb462" stroke="black" cx="1875" cy="-104" rx="90.86" ry="18"/>
<text text-anchor="middle" x="1875" y="-99.8" font-family="Times,serif" font-size="14.00">TicketSearchService</text>
</g>
<!-- TicketSearchService&#45;&gt;TicketsModule -->
<g id="edge28" class="edge">
<title>TicketSearchService&#45;&gt;TicketsModule</title>
<path fill="none" stroke="black" d="M1892.89,-121.71C1892.89,-137.78 1892.89,-159 1892.89,-159 1892.89,-159 2686.44,-159 2686.44,-159"/>
<polygon fill="black" stroke="black" points="2686.45,-162.5 2696.44,-159 2686.44,-155.5 2686.45,-162.5"/>
</g>
<!-- TicketSnsPublisherConsumer -->
<g id="node30" class="node">
<title>TicketSnsPublisherConsumer</title>
<ellipse fill="#fdb462" stroke="black" cx="1640" cy="-104" rx="125.66" ry="18"/>
<text text-anchor="middle" x="1640" y="-99.8" font-family="Times,serif" font-size="14.00">TicketSnsPublisherConsumer</text>
</g>
<!-- TicketSnsPublisherConsumer&#45;&gt;TicketsModule -->
<g id="edge29" class="edge">
<title>TicketSnsPublisherConsumer&#45;&gt;TicketsModule</title>
<path fill="none" stroke="black" d="M1640,-122.04C1640,-138.73 1640,-161 1640,-161 1640,-161 2686.56,-161 2686.56,-161"/>
<polygon fill="black" stroke="black" points="2686.56,-164.5 2696.56,-161 2686.56,-157.5 2686.56,-164.5"/>
</g>
<!-- TicketStatusActionService -->
<g id="node31" class="node">
<title>TicketStatusActionService</title>
<ellipse fill="#fdb462" stroke="black" cx="1382" cy="-104" rx="114.6" ry="18"/>
<text text-anchor="middle" x="1382" y="-99.8" font-family="Times,serif" font-size="14.00">TicketStatusActionService</text>
</g>
<!-- TicketStatusActionService&#45;&gt;TicketsModule -->
<g id="edge30" class="edge">
<title>TicketStatusActionService&#45;&gt;TicketsModule</title>
<path fill="none" stroke="black" d="M1382,-122.02C1382,-139.37 1382,-163 1382,-163 1382,-163 2686.39,-163 2686.39,-163"/>
<polygon fill="black" stroke="black" points="2686.39,-166.5 2696.39,-163 2686.39,-159.5 2686.39,-166.5"/>
</g>
<!-- TicketTypeActionService -->
<g id="node32" class="node">
<title>TicketTypeActionService</title>
<ellipse fill="#fdb462" stroke="black" cx="1139" cy="-104" rx="110.57" ry="18"/>
<text text-anchor="middle" x="1139" y="-99.8" font-family="Times,serif" font-size="14.00">TicketTypeActionService</text>
</g>
<!-- TicketTypeActionService&#45;&gt;TicketsModule -->
<g id="edge31" class="edge">
<title>TicketTypeActionService&#45;&gt;TicketsModule</title>
<path fill="none" stroke="black" d="M1139,-122.3C1139,-140.27 1139,-165 1139,-165 1139,-165 2686.67,-165 2686.67,-165"/>
<polygon fill="black" stroke="black" points="2686.67,-168.5 2696.67,-165 2686.67,-161.5 2686.67,-168.5"/>
</g>
<!-- TicketValidationService -->
<g id="node33" class="node">
<title>TicketValidationService</title>
<ellipse fill="#fdb462" stroke="black" cx="905" cy="-104" rx="105.35" ry="18"/>
<text text-anchor="middle" x="905" y="-99.8" font-family="Times,serif" font-size="14.00">TicketValidationService</text>
</g>
<!-- TicketValidationService&#45;&gt;TicketsModule -->
<g id="edge32" class="edge">
<title>TicketValidationService&#45;&gt;TicketsModule</title>
<path fill="none" stroke="black" d="M905,-122.22C905,-140.83 905,-167 905,-167 905,-167 2686.29,-167 2686.29,-167"/>
<polygon fill="black" stroke="black" points="2686.29,-170.5 2696.29,-167 2686.29,-163.5 2686.29,-170.5"/>
</g>
<!-- TicketsBulkActionService -->
<g id="node34" class="node">
<title>TicketsBulkActionService</title>
<ellipse fill="#fdb462" stroke="black" cx="668" cy="-104" rx="113.46" ry="18"/>
<text text-anchor="middle" x="668" y="-99.8" font-family="Times,serif" font-size="14.00">TicketsBulkActionService</text>
</g>
<!-- TicketsBulkActionService&#45;&gt;TicketsModule -->
<g id="edge33" class="edge">
<title>TicketsBulkActionService&#45;&gt;TicketsModule</title>
<path fill="none" stroke="black" d="M668,-122.11C668,-141.34 668,-169 668,-169 668,-169 2686.64,-169 2686.64,-169"/>
<polygon fill="black" stroke="black" points="2686.64,-172.5 2696.64,-169 2686.64,-165.5 2686.64,-172.5"/>
</g>
<!-- TicketsEventsFactory -->
<g id="node35" class="node">
<title>TicketsEventsFactory</title>
<ellipse fill="#fdb462" stroke="black" cx="441" cy="-104" rx="95.48" ry="18"/>
<text text-anchor="middle" x="441" y="-99.8" font-family="Times,serif" font-size="14.00">TicketsEventsFactory</text>
</g>
<!-- TicketsEventsFactory&#45;&gt;TicketsModule -->
<g id="edge34" class="edge">
<title>TicketsEventsFactory&#45;&gt;TicketsModule</title>
<path fill="none" stroke="black" d="M441,-122.31C441,-142.15 441,-171 441,-171 441,-171 2686.69,-171 2686.69,-171"/>
<polygon fill="black" stroke="black" points="2686.69,-174.5 2696.69,-171 2686.69,-167.5 2686.69,-174.5"/>
</g>
<!-- TicketsListeners -->
<g id="node36" class="node">
<title>TicketsListeners</title>
<ellipse fill="#fdb462" stroke="black" cx="253" cy="-104" rx="74.71" ry="18"/>
<text text-anchor="middle" x="253" y="-99.8" font-family="Times,serif" font-size="14.00">TicketsListeners</text>
</g>
<!-- TicketsListeners&#45;&gt;TicketsModule -->
<g id="edge35" class="edge">
<title>TicketsListeners&#45;&gt;TicketsModule</title>
<path fill="none" stroke="black" d="M253,-122.13C253,-142.57 253,-173 253,-173 253,-173 2686.62,-173 2686.62,-173"/>
<polygon fill="black" stroke="black" points="2686.62,-176.5 2696.62,-173 2686.62,-169.5 2686.62,-176.5"/>
</g>
<!-- TicketsService -->
<g id="node37" class="node">
<title>TicketsService</title>
<ellipse fill="#fdb462" stroke="black" cx="92" cy="-104" rx="68.33" ry="18"/>
<text text-anchor="middle" x="92" y="-99.8" font-family="Times,serif" font-size="14.00">TicketsService</text>
</g>
<!-- TicketsService&#45;&gt;TicketsModule -->
<g id="edge36" class="edge">
<title>TicketsService&#45;&gt;TicketsModule</title>
<path fill="none" stroke="black" d="M92,-122.28C92,-143.32 92,-175 92,-175 92,-175 2686.63,-175 2686.63,-175"/>
<polygon fill="black" stroke="black" points="2686.63,-178.5 2696.63,-175 2686.63,-171.5 2686.63,-178.5"/>
</g>
</g>
</svg>

    </div>
    <i id="fullscreen" class="icon ion-ios-resize module-graph-fullscreen-btn" aria-hidden="true"></i>
    <div class="btn-group size-buttons">
        <button id="zoom-in" class="btn btn-default btn-sm">Zoom in</button>
        <button id="reset" class="btn btn-default btn-sm">Reset</button>
        <button id="zoom-out" class="btn btn-default btn-sm">Zoom out</button>
    </div>
</div>
<script src="../js/libs/svg-pan-zoom.min.js"></script>
<script src="../js/svg-pan-zoom.controls.js"></script>

<ul class="nav nav-tabs" role="tablist">
    <li class="nav-item">
        <a href="#info" 
            class="nav-link"
            class="nav-link active"
            role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
    </li>
    <li class="nav-item">
        <a href="#source" 
            class="nav-link"
            
            role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
    </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">

        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/tickets/tickets.module.ts</code>
        </p>





        <div class="container-fluid module">
            <div class="row">
                <div class="col-sm-3">
                    <h3>Providers<a href="https://angular.io/api/core/NgModule#providers" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module providers"><span class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../injectables/RoundRobinStrategy.html">RoundRobinStrategy</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/ThenaAgentAllocator.html">ThenaAgentAllocator</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/ThenaRequestRouterEngine.html">ThenaRequestRouterEngine</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/ThenaRuleEvaluator.html">ThenaRuleEvaluator</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/TicketAnnotatorService.html">TicketAnnotatorService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/TicketDraftService.html">TicketDraftService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/TicketPriorityActionService.html">TicketPriorityActionService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/TicketSearchService.html">TicketSearchService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/TicketSnsPublisherConsumer.html">TicketSnsPublisherConsumer</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/TicketStatusActionService.html">TicketStatusActionService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/TicketTypeActionService.html">TicketTypeActionService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/TicketValidationService.html">TicketValidationService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/TicketsBulkActionService.html">TicketsBulkActionService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/TicketsEventsFactory.html">TicketsEventsFactory</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/TicketsListeners.html">TicketsListeners</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/TicketsService.html">TicketsService</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Controllers<a href="https://docs.nestjs.com/controllers" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module controllers"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../controllers/TicketsController.html">TicketsController</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../controllers/TicketsGrpcController.html">TicketsGrpcController</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../controllers/TicketBulkActionController.html">TicketBulkActionController</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../controllers/TicketTypeActionController.html">TicketTypeActionController</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../controllers/TicketStatusActionController.html">TicketStatusActionController</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../controllers/TicketPriorityActionController.html">TicketPriorityActionController</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../controllers/TicketSearchController.html">TicketSearchController</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../controllers/TicketDraftController.html">TicketDraftController</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Imports<a href="https://angular.io/api/core/NgModule#imports" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module imports"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../modules/AccountsModule.html">AccountsModule</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../modules/ActivitiesModule.html">ActivitiesModule</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../modules/CommonModule.html">CommonModule</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../modules/ConfigModule.html">ConfigModule</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../modules/CustomFieldModule.html">CustomFieldModule</a>
                        </li>
                        <li class="list-group-item">
                            FormsModule
                        </li>
                        <li class="list-group-item">
                            <a href="../modules/StorageModule.html">StorageModule</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../modules/TeamsModule.html">TeamsModule</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../modules/UsersModule.html">UsersModule</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Exports<a href="https://angular.io/api/core/NgModule#exports" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module exports"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../s/CachedTicketPriorityRepository.html">CachedTicketPriorityRepository</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../s/CachedTicketRepository.html">CachedTicketRepository</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../s/CachedTicketStatusRepository.html">CachedTicketStatusRepository</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../s/CachedTicketTypeRepository.html">CachedTicketTypeRepository</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/TicketDraftService.html">TicketDraftService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/TicketPriorityActionService.html">TicketPriorityActionService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../s/TicketRepository.html">TicketRepository</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/TicketStatusActionService.html">TicketStatusActionService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/TicketTypeActionService.html">TicketTypeActionService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/TicketValidationService.html">TicketValidationService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/TicketsService.html">TicketsService</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>


    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { BullModule } from &quot;@nestjs/bullmq&quot;;
import { forwardRef, Module } from &quot;@nestjs/common&quot;;
import { TypeOrmModule } from &quot;@nestjs/typeorm&quot;;
import {
  SQSConsumerService,
  SQSProducerService,
} from &quot;@repo/nestjs-commons/aws-utils/sqs&quot;;
import { SentryService } from &quot;@repo/nestjs-commons/filters&quot;;
import {
  ApiKeyGrpcStrategy,
  AuthenticationGrpcClient,
  BearerTokenGrpcStrategy,
  GrpcAuthGuard,
  UserOrgInternalGrpcStrategy,
} from &quot;@repo/nestjs-commons/guards&quot;;
import { ILogger } from &quot;@repo/nestjs-commons/logger&quot;;
import { GrpcClient } from &quot;@repo/nestjs-commons/providers&quot;;
import { SNSPublisherService } from &quot;@repo/thena-eventbridge&quot;;
import {
  CachedDraftsRepository,
  CachedTicketPriorityRepository,
  CachedTicketRelationshipsRepository,
  CachedTicketRepository,
  CachedTicketStatusRepository,
  CachedTicketTypeRepository,
  CustomField,
  CustomFieldRepository,
  Draft,
  DraftRepository,
  TeamRoutingRules,
  TeamRoutingRulesRepository,
  Ticket,
  TicketPriority,
  TicketPriorityRepository,
  TicketRelationships,
  TicketRelationshipsRepository,
  TicketRepository,
  TicketStatus,
  TicketStatusRepository,
  TicketTimeLog,
  TicketTimeLogRepository,
  TicketType,
  TicketTypeRepository,
  TransactionService,
} from &quot;@repo/thena-platform-entities&quot;;
import { AccountsModule } from &quot;../accounts/accounts.module&quot;;
import { ActivitiesModule } from &quot;../activities/activities.module&quot;;
import { CommonModule } from &quot;../common/common.module&quot;;
import { CommunicationsModule } from &quot;../communications/communications.module&quot;;
import { ConfigModule } from &quot;../config/config.module&quot;;
import { ConfigKeys, ConfigService } from &quot;../config/config.service&quot;;
import { QueueNames } from &quot;../constants/queue.constants&quot;;
import { CustomFieldModule } from &quot;../custom-field/custom-field.module&quot;;
import { FormsModule } from &quot;../forms/forms.module&quot;;
import { StorageModule } from &quot;../storage/module/storage.module&quot;;
import { TeamsModule } from &quot;../teams/teams.module&quot;;
import { UsersModule } from &quot;../users/users.module&quot;;
import { TicketsGrpcController } from &quot;./controllers/grpc/tickets-grpc.controller&quot;;
import { TicketBulkActionController } from &quot;./controllers/ticket-bulk.action.controller&quot;;
import { TicketDraftController } from &quot;./controllers/ticket-draft.action.controller&quot;;
import { TicketPriorityActionController } from &quot;./controllers/ticket-priority.action.controller&quot;;
import { TicketSearchController } from &quot;./controllers/ticket-search.action.controller&quot;;
import { TicketStatusActionController } from &quot;./controllers/ticket-status.action.controller&quot;;
import { TicketTypeActionController } from &quot;./controllers/ticket-type.action.controller&quot;;
import { TicketsController } from &quot;./controllers/tickets.controller&quot;;
import { TicketsEventsFactory } from &quot;./events/tickets-events.factory&quot;;
import { TicketsListeners } from &quot;./listeners/tickets.listeners&quot;;
import { TicketSnsPublisherConsumer } from &quot;./processors/ticket.sns-publish.processor&quot;;
import {
  ThenaAgentAllocator,
  ThenaRequestRouterEngine,
  ThenaRuleEvaluator,
} from &quot;./routing&quot;;
import { RequestRouterProvider } from &quot;./routing/providers/request-router.provider&quot;;
import { RoundRobinStrategy } from &quot;./routing/providers/thena-request-router/agent-allocator/strategies&quot;;
import { TicketAnnotatorService } from &quot;./services/ticket-annotator.service&quot;;
import { TicketsBulkActionService } from &quot;./services/ticket-bulk.action.service&quot;;
import { TicketDraftService } from &quot;./services/ticket-draft.action.service&quot;;
import { TicketPriorityActionService } from &quot;./services/ticket-priority.action.service&quot;;
import { TicketSearchService } from &quot;./services/ticket-search.action.service&quot;;
import { TicketStatusActionService } from &quot;./services/ticket-status.action.service&quot;;
import { TicketTypeActionService } from &quot;./services/ticket-type.action.service&quot;;
import { TicketValidationService } from &quot;./services/ticket-validation.service&quot;;
import { TicketsService } from &quot;./services/tickets.service&quot;;
import { TICKET_SNS_PUBLISHER } from &quot;./utils/tickets.constants&quot;;

@Module({
  imports: [
    ConfigModule,
    CommonModule,
    ActivitiesModule,
    BullModule.registerQueueAsync({
      name: QueueNames.TICKET_SNS_PUBLISHER,
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) &#x3D;&gt; ({
        redis: {
          host: configService.get(ConfigKeys.REDIS_HOST),
          port: Number(configService.get(ConfigKeys.REDIS_PORT)),
          password: configService.get(ConfigKeys.REDIS_PASSWORD),
          username: configService.get(ConfigKeys.REDIS_USERNAME),
        },
        defaultJobOptions: {
          removeOnComplete: {
            age: 24 * 3600, // keep completed jobs for 24 hours
            count: 1000, // keep last 1000 completed jobs
          },
          removeOnFail: {
            age: 24 * 3600, // keep up to 24 hours
          },
        },
      }),
    }),
    TypeOrmModule.forFeature([
      // Tickets
      Ticket,
      TicketRepository,

      // Ticket Status
      TicketStatus,
      TicketStatusRepository,

      // Ticket Priority
      TicketPriority,
      TicketPriorityRepository,

      // Ticket Type
      TicketType,
      TicketTypeRepository,

      // Ticket Relationships
      TicketRelationships,
      TicketRelationshipsRepository,

      // Custom Field
      CustomField,
      CustomFieldRepository,
      // Ticket Drafts [This is a generic entity for all draft types]
      Draft,
      DraftRepository,

      // Team Routing Rules
      TeamRoutingRules,
      TeamRoutingRulesRepository,

      // Ticket Time Logs
      TicketTimeLog,
      TicketTimeLogRepository,
    ]),
    forwardRef(() &#x3D;&gt; CommunicationsModule),
    UsersModule,
    TeamsModule,
    AccountsModule,
    CustomFieldModule,
    StorageModule,
    FormsModule,
  ],
  controllers: [
    TicketsController,
    TicketsGrpcController,
    TicketBulkActionController,
    TicketTypeActionController,
    TicketStatusActionController,
    TicketPriorityActionController,
    TicketSearchController,
    TicketDraftController,
  ],
  providers: [
    // Bull service
    TicketSnsPublisherConsumer,

    // Transaction Service
    TransactionService,

    // Auth GRPC
    ApiKeyGrpcStrategy,
    BearerTokenGrpcStrategy,
    AuthenticationGrpcClient,
    GrpcAuthGuard,
    UserOrgInternalGrpcStrategy,

    // Request Router
    RequestRouterProvider,
    ThenaRequestRouterEngine,
    ThenaRuleEvaluator,
    ThenaAgentAllocator,
    TeamRoutingRulesRepository,
    RoundRobinStrategy,

    // Tickets
    TicketsService,
    TicketRepository,
    TicketsBulkActionService,
    CachedTicketRepository,

    // Ticket Relationships
    TicketRelationshipsRepository,
    CachedTicketRelationshipsRepository,

    // Ticket Status
    TicketStatusActionService,
    TicketStatusRepository,
    CachedTicketStatusRepository,

    // Ticket Priority
    TicketPriorityActionService,
    TicketPriorityRepository,
    CachedTicketPriorityRepository,

    // Ticket Type
    TicketTypeActionService,
    TicketTypeRepository,
    CachedTicketTypeRepository,

    // Ticket Draft
    TicketDraftService,
    DraftRepository,
    CachedDraftsRepository,

    // Ticket Time Logs
    TicketTimeLogRepository,

    // Ticket Search - uses typesense
    TicketSearchService,

    // Ticket Validation
    TicketValidationService,

    // SNS Publisher
    {
      provide: TICKET_SNS_PUBLISHER,
      useFactory: (
        configService: ConfigService,
        sentryService: SentryService,
        loggerService: ILogger,
      ) &#x3D;&gt; {
        return new SNSPublisherService(
          {
            topicArn: configService.get(ConfigKeys.AWS_SNS_TICKET_TOPIC_ARN),
            region: configService.get(ConfigKeys.AWS_REGION),
            credentials: {
              accessKeyId: configService.get(ConfigKeys.AWS_ACCESS_KEY),
              secretAccessKey: configService.get(ConfigKeys.AWS_SECRET_KEY),
            },
          },
          sentryService,
          loggerService,
        );
      },
      inject: [ConfigService, &quot;Sentry&quot;, &quot;CustomLogger&quot;],
    },
    {
      provide: &quot;SLA_SQS_PRODUCER&quot;,
      useFactory: (
        configService: ConfigService,
        sentryService: SentryService,
        loggerService: ILogger,
      ) &#x3D;&gt; {
        return new SQSProducerService(
          {
            region: configService.get(ConfigKeys.AWS_REGION),
            queueUrl: configService.get(ConfigKeys.AWS_SQS_SLA_QUEUE_URL),
            credentials: {
              accessKeyId: configService.get(ConfigKeys.AWS_ACCESS_KEY),
              secretAccessKey: configService.get(ConfigKeys.AWS_SECRET_KEY),
            },
          },
          sentryService,
          loggerService,
        );
      },
      inject: [ConfigService, &quot;Sentry&quot;, &quot;CustomLogger&quot;],
    },
    {
      provide: &quot;SLA_SQS_CONSUMER&quot;,
      useFactory: (
        configService: ConfigService,
        sentryService: SentryService,
        loggerService: ILogger,
      ) &#x3D;&gt; {
        return new SQSConsumerService(
          {
            region: configService.get(ConfigKeys.AWS_REGION),
            queueUrl: configService.get(
              ConfigKeys.AWS_SQS_SLA_CONSUMER_QUEUE_URL,
            ),
            credentials: {
              accessKeyId: configService.get(ConfigKeys.AWS_ACCESS_KEY),
              secretAccessKey: configService.get(ConfigKeys.AWS_SECRET_KEY),
            },
          },
          sentryService,
          loggerService,
        );
      },
      inject: [ConfigService, &quot;Sentry&quot;, &quot;CustomLogger&quot;],
    },
    // Base services
    TicketAnnotatorService,

    // Tickets Listeners and events factory
    TicketsListeners,
    TicketsEventsFactory,

    // Grpc Client
    GrpcClient,
  ],
  exports: [
    TicketsService,
    TicketStatusActionService,
    TicketPriorityActionService,
    TicketTypeActionService,
    TicketDraftService,
    TicketValidationService,
    TicketRepository,
    CachedTicketPriorityRepository,
    CachedTicketStatusRepository,
    CachedTicketTypeRepository,
    CachedTicketRepository,
  ],
})
export class TicketsModule {}
</code></pre>
    </div>
</div>

















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'module';
            var COMPODOC_CURRENT_PAGE_URL = 'TicketsModule.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
