<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: dependencies Pages: 1 -->
<svg width="504pt" height="211pt"
 viewBox="0.00 0.00 504.00 211.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 207)">
<title>dependencies</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-207 500,-207 500,4 -4,4"/>
<text text-anchor="start" x="227.01" y="-42.4" font-family="Times-12" font-weight="bold" font-size="14.00">Legend</text>
<polygon fill="#ffffb3" stroke="transparent" points="14,-10 14,-30 34,-30 34,-10 14,-10"/>
<text text-anchor="start" x="37.63" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Declarations</text>
<polygon fill="#8dd3c7" stroke="transparent" points="127,-10 127,-30 147,-30 147,-10 127,-10"/>
<text text-anchor="start" x="150.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Module</text>
<polygon fill="#80b1d3" stroke="transparent" points="213,-10 213,-30 233,-30 233,-10 213,-10"/>
<text text-anchor="start" x="236.78" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Bootstrap</text>
<polygon fill="#fdb462" stroke="transparent" points="310,-10 310,-30 330,-30 330,-10 310,-10"/>
<text text-anchor="start" x="333.67" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Providers</text>
<polygon fill="#fb8072" stroke="transparent" points="406,-10 406,-30 426,-30 426,-10 406,-10"/>
<text text-anchor="start" x="429.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Exports</text>
<g id="clust1" class="cluster">
<title>cluster_HealthModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="98,-70 98,-195 398,-195 398,-70 98,-70"/>
</g>
<g id="clust6" class="cluster">
<title>cluster_HealthModule_providers</title>
<polygon fill="none" stroke="black" points="242,-78 242,-130 390,-130 390,-78 242,-78"/>
</g>
<g id="clust3" class="cluster">
<title>cluster_HealthModule_imports</title>
<polygon fill="none" stroke="black" points="106,-78 106,-130 234,-130 234,-78 106,-78"/>
</g>
<!-- CommonModule -->
<g id="node1" class="node">
<title>CommonModule</title>
<polygon fill="#8dd3c7" stroke="black" points="225.67,-122 222.67,-126 201.67,-126 198.67,-122 114.33,-122 114.33,-86 225.67,-86 225.67,-122"/>
<text text-anchor="middle" x="170" y="-99.8" font-family="Times,serif" font-size="14.00">CommonModule</text>
</g>
<!-- HealthModule -->
<g id="node2" class="node">
<title>HealthModule</title>
<polygon fill="#8dd3c7" stroke="black" points="291.37,-187 288.37,-191 267.37,-191 264.37,-187 194.63,-187 194.63,-151 291.37,-151 291.37,-187"/>
<text text-anchor="middle" x="243" y="-164.8" font-family="Times,serif" font-size="14.00">HealthModule</text>
</g>
<!-- CommonModule&#45;&gt;HealthModule -->
<g id="edge1" class="edge">
<title>CommonModule&#45;&gt;HealthModule</title>
<path fill="none" stroke="black" d="M210.2,-122.11C210.2,-122.11 210.2,-140.99 210.2,-140.99"/>
<polygon fill="black" stroke="black" points="206.7,-140.99 210.2,-150.99 213.7,-140.99 206.7,-140.99"/>
</g>
<!-- HealthService -->
<g id="node3" class="node">
<title>HealthService</title>
<ellipse fill="#fdb462" stroke="black" cx="316" cy="-104" rx="65.97" ry="18"/>
<text text-anchor="middle" x="316" y="-99.8" font-family="Times,serif" font-size="14.00">HealthService</text>
</g>
<!-- HealthService&#45;&gt;HealthModule -->
<g id="edge2" class="edge">
<title>HealthService&#45;&gt;HealthModule</title>
<path fill="none" stroke="black" d="M270.85,-117.15C270.85,-117.15 270.85,-140.69 270.85,-140.69"/>
<polygon fill="black" stroke="black" points="267.35,-140.69 270.85,-150.69 274.35,-140.69 267.35,-140.69"/>
</g>
</g>
</svg>
