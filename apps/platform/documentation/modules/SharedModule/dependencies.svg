<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: dependencies Pages: 1 -->
<svg width="1886pt" height="284pt"
 viewBox="0.00 0.00 1886.00 284.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 280)">
<title>dependencies</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-280 1882,-280 1882,4 -4,4"/>
<text text-anchor="start" x="918.01" y="-42.4" font-family="Times-12" font-weight="bold" font-size="14.00">Legend</text>
<polygon fill="#ffffb3" stroke="transparent" points="705,-10 705,-30 725,-30 725,-10 705,-10"/>
<text text-anchor="start" x="728.63" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Declarations</text>
<polygon fill="#8dd3c7" stroke="transparent" points="818,-10 818,-30 838,-30 838,-10 818,-10"/>
<text text-anchor="start" x="841.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Module</text>
<polygon fill="#80b1d3" stroke="transparent" points="904,-10 904,-30 924,-30 924,-10 904,-10"/>
<text text-anchor="start" x="927.78" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Bootstrap</text>
<polygon fill="#fdb462" stroke="transparent" points="1001,-10 1001,-30 1021,-30 1021,-10 1001,-10"/>
<text text-anchor="start" x="1024.67" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Providers</text>
<polygon fill="#fb8072" stroke="transparent" points="1097,-10 1097,-30 1117,-30 1117,-10 1097,-10"/>
<text text-anchor="start" x="1120.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Exports</text>
<g id="clust1" class="cluster">
<title>cluster_SharedModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="8,-70 8,-268 1870,-268 1870,-70 8,-70"/>
</g>
<g id="clust4" class="cluster">
<title>cluster_SharedModule_exports</title>
<polygon fill="none" stroke="black" points="16,-208 16,-260 1862,-260 1862,-208 16,-208"/>
</g>
<g id="clust6" class="cluster">
<title>cluster_SharedModule_providers</title>
<polygon fill="none" stroke="black" points="869,-78 869,-130 1019,-130 1019,-78 869,-78"/>
</g>
<!-- CachedTeamRepository  -->
<g id="node1" class="node">
<title>CachedTeamRepository </title>
<polygon fill="#fb8072" stroke="black" points="1854.29,-252 1699.71,-252 1699.71,-216 1854.29,-216 1854.29,-252"/>
<text text-anchor="middle" x="1777" y="-229.8" font-family="Times,serif" font-size="14.00">CachedTeamRepository </text>
</g>
<!-- FormFieldEventRepository  -->
<g id="node2" class="node">
<title>FormFieldEventRepository </title>
<polygon fill="#fb8072" stroke="black" points="1681.21,-252 1508.79,-252 1508.79,-216 1681.21,-216 1681.21,-252"/>
<text text-anchor="middle" x="1595" y="-229.8" font-family="Times,serif" font-size="14.00">FormFieldEventRepository </text>
</g>
<!-- FormRepository  -->
<g id="node3" class="node">
<title>FormRepository </title>
<polygon fill="#fb8072" stroke="black" points="1490.77,-252 1379.23,-252 1379.23,-216 1490.77,-216 1490.77,-252"/>
<text text-anchor="middle" x="1435" y="-229.8" font-family="Times,serif" font-size="14.00">FormRepository </text>
</g>
<!-- OrganizationRepository  -->
<g id="node4" class="node">
<title>OrganizationRepository </title>
<polygon fill="#fb8072" stroke="black" points="1361.02,-252 1206.98,-252 1206.98,-216 1361.02,-216 1361.02,-252"/>
<text text-anchor="middle" x="1284" y="-229.8" font-family="Times,serif" font-size="14.00">OrganizationRepository </text>
</g>
<!-- SharedService  -->
<g id="node5" class="node">
<title>SharedService </title>
<polygon fill="#fb8072" stroke="black" points="1189.35,-252 1088.65,-252 1088.65,-216 1189.35,-216 1189.35,-252"/>
<text text-anchor="middle" x="1139" y="-229.8" font-family="Times,serif" font-size="14.00">SharedService </text>
</g>
<!-- TagRepository  -->
<g id="node6" class="node">
<title>TagRepository </title>
<polygon fill="#fb8072" stroke="black" points="1070.21,-252 967.79,-252 967.79,-216 1070.21,-216 1070.21,-252"/>
<text text-anchor="middle" x="1019" y="-229.8" font-family="Times,serif" font-size="14.00">TagRepository </text>
</g>
<!-- TeamMemberRepository  -->
<g id="node7" class="node">
<title>TeamMemberRepository </title>
<polygon fill="#fb8072" stroke="black" points="950.24,-252 789.76,-252 789.76,-216 950.24,-216 950.24,-252"/>
<text text-anchor="middle" x="870" y="-229.8" font-family="Times,serif" font-size="14.00">TeamMemberRepository </text>
</g>
<!-- TeamRepository  -->
<g id="node8" class="node">
<title>TeamRepository </title>
<polygon fill="#fb8072" stroke="black" points="771.31,-252 658.69,-252 658.69,-216 771.31,-216 771.31,-252"/>
<text text-anchor="middle" x="715" y="-229.8" font-family="Times,serif" font-size="14.00">TeamRepository </text>
</g>
<!-- TicketPriorityRepository  -->
<g id="node9" class="node">
<title>TicketPriorityRepository </title>
<polygon fill="#fb8072" stroke="black" points="640.98,-252 481.02,-252 481.02,-216 640.98,-216 640.98,-252"/>
<text text-anchor="middle" x="561" y="-229.8" font-family="Times,serif" font-size="14.00">TicketPriorityRepository </text>
</g>
<!-- TicketStatusRepository  -->
<g id="node10" class="node">
<title>TicketStatusRepository </title>
<polygon fill="#fb8072" stroke="black" points="463.42,-252 312.58,-252 312.58,-216 463.42,-216 463.42,-252"/>
<text text-anchor="middle" x="388" y="-229.8" font-family="Times,serif" font-size="14.00">TicketStatusRepository </text>
</g>
<!-- TicketTypeRepository  -->
<g id="node11" class="node">
<title>TicketTypeRepository </title>
<polygon fill="#fb8072" stroke="black" points="294.97,-252 149.03,-252 149.03,-216 294.97,-216 294.97,-252"/>
<text text-anchor="middle" x="222" y="-229.8" font-family="Times,serif" font-size="14.00">TicketTypeRepository </text>
</g>
<!-- UserRepository  -->
<g id="node12" class="node">
<title>UserRepository </title>
<polygon fill="#fb8072" stroke="black" points="131.86,-252 24.14,-252 24.14,-216 131.86,-216 131.86,-252"/>
<text text-anchor="middle" x="78" y="-229.8" font-family="Times,serif" font-size="14.00">UserRepository </text>
</g>
<!-- SharedModule -->
<g id="node13" class="node">
<title>SharedModule</title>
<polygon fill="#8dd3c7" stroke="black" points="993.42,-187 990.42,-191 969.42,-191 966.42,-187 894.58,-187 894.58,-151 993.42,-151 993.42,-187"/>
<text text-anchor="middle" x="944" y="-164.8" font-family="Times,serif" font-size="14.00">SharedModule</text>
</g>
<!-- SharedModule&#45;&gt;CachedTeamRepository  -->
<g id="edge1" class="edge">
<title>SharedModule&#45;&gt;CachedTeamRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M993.31,-157C1172.25,-157 1777,-157 1777,-157 1777,-157 1777,-205.75 1777,-205.75"/>
<polygon fill="black" stroke="black" points="1773.5,-205.75 1777,-215.75 1780.5,-205.75 1773.5,-205.75"/>
</g>
<!-- SharedModule&#45;&gt;FormFieldEventRepository  -->
<g id="edge2" class="edge">
<title>SharedModule&#45;&gt;FormFieldEventRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M993.47,-163C1145.39,-163 1595,-163 1595,-163 1595,-163 1595,-205.72 1595,-205.72"/>
<polygon fill="black" stroke="black" points="1591.5,-205.72 1595,-215.72 1598.5,-205.72 1591.5,-205.72"/>
</g>
<!-- SharedModule&#45;&gt;FormRepository  -->
<g id="edge3" class="edge">
<title>SharedModule&#45;&gt;FormRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M993.33,-169C1117.84,-169 1435,-169 1435,-169 1435,-169 1435,-205.89 1435,-205.89"/>
<polygon fill="black" stroke="black" points="1431.5,-205.89 1435,-215.89 1438.5,-205.89 1431.5,-205.89"/>
</g>
<!-- SharedModule&#45;&gt;OrganizationRepository  -->
<g id="edge4" class="edge">
<title>SharedModule&#45;&gt;OrganizationRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M993.44,-175C1087.74,-175 1284,-175 1284,-175 1284,-175 1284,-205.98 1284,-205.98"/>
<polygon fill="black" stroke="black" points="1280.5,-205.98 1284,-215.98 1287.5,-205.98 1280.5,-205.98"/>
</g>
<!-- SharedModule&#45;&gt;SharedService  -->
<g id="edge5" class="edge">
<title>SharedModule&#45;&gt;SharedService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M993.46,-181C1051.23,-181 1139,-181 1139,-181 1139,-181 1139,-205.76 1139,-205.76"/>
<polygon fill="black" stroke="black" points="1135.5,-205.76 1139,-215.76 1142.5,-205.76 1135.5,-205.76"/>
</g>
<!-- SharedModule&#45;&gt;TagRepository  -->
<g id="edge6" class="edge">
<title>SharedModule&#45;&gt;TagRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M980.43,-187.11C980.43,-187.11 980.43,-205.99 980.43,-205.99"/>
<polygon fill="black" stroke="black" points="976.93,-205.99 980.43,-215.99 983.93,-205.99 976.93,-205.99"/>
</g>
<!-- SharedModule&#45;&gt;TeamMemberRepository  -->
<g id="edge7" class="edge">
<title>SharedModule&#45;&gt;TeamMemberRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M922.45,-187.11C922.45,-187.11 922.45,-205.99 922.45,-205.99"/>
<polygon fill="black" stroke="black" points="918.95,-205.99 922.45,-215.99 925.95,-205.99 918.95,-205.99"/>
</g>
<!-- SharedModule&#45;&gt;TeamRepository  -->
<g id="edge8" class="edge">
<title>SharedModule&#45;&gt;TeamRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M894.59,-181C827.27,-181 715,-181 715,-181 715,-181 715,-205.76 715,-205.76"/>
<polygon fill="black" stroke="black" points="711.5,-205.76 715,-215.76 718.5,-205.76 711.5,-205.76"/>
</g>
<!-- SharedModule&#45;&gt;TicketPriorityRepository  -->
<g id="edge9" class="edge">
<title>SharedModule&#45;&gt;TicketPriorityRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M894.71,-175C791.27,-175 561,-175 561,-175 561,-175 561,-205.98 561,-205.98"/>
<polygon fill="black" stroke="black" points="557.5,-205.98 561,-215.98 564.5,-205.98 557.5,-205.98"/>
</g>
<!-- SharedModule&#45;&gt;TicketStatusRepository  -->
<g id="edge10" class="edge">
<title>SharedModule&#45;&gt;TicketStatusRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M894.66,-169C758.58,-169 388,-169 388,-169 388,-169 388,-205.89 388,-205.89"/>
<polygon fill="black" stroke="black" points="384.5,-205.89 388,-215.89 391.5,-205.89 384.5,-205.89"/>
</g>
<!-- SharedModule&#45;&gt;TicketTypeRepository  -->
<g id="edge11" class="edge">
<title>SharedModule&#45;&gt;TicketTypeRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M894.66,-163C731.85,-163 222,-163 222,-163 222,-163 222,-205.72 222,-205.72"/>
<polygon fill="black" stroke="black" points="218.5,-205.72 222,-215.72 225.5,-205.72 218.5,-205.72"/>
</g>
<!-- SharedModule&#45;&gt;UserRepository  -->
<g id="edge12" class="edge">
<title>SharedModule&#45;&gt;UserRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M894.64,-157C711.04,-157 78,-157 78,-157 78,-157 78,-205.75 78,-205.75"/>
<polygon fill="black" stroke="black" points="74.5,-205.75 78,-215.75 81.5,-205.75 74.5,-205.75"/>
</g>
<!-- SharedService -->
<g id="node14" class="node">
<title>SharedService</title>
<ellipse fill="#fdb462" stroke="black" cx="944" cy="-104" rx="66.61" ry="18"/>
<text text-anchor="middle" x="944" y="-99.8" font-family="Times,serif" font-size="14.00">SharedService</text>
</g>
<!-- SharedService&#45;&gt;SharedModule -->
<g id="edge13" class="edge">
<title>SharedService&#45;&gt;SharedModule</title>
<path fill="none" stroke="black" d="M944,-122.11C944,-122.11 944,-140.99 944,-140.99"/>
<polygon fill="black" stroke="black" points="940.5,-140.99 944,-150.99 947.5,-140.99 940.5,-140.99"/>
</g>
</g>
</svg>
