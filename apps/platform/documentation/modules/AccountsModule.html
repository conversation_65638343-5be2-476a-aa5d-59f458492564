<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content module">
                   <div class="content-data">



<ol class="breadcrumb">
    <li class="breadcrumb-item">Modules</li>
    <li class="breadcrumb-item" >AccountsModule</li>
</ol>

<div class="text-center module-graph-container">
    <div id="module-graph-svg">
        <?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (********.0004)
 -->
<!-- Title: dependencies Pages: 1 -->
<svg width="4202pt" height="284pt"
 viewBox="0.00 0.00 4202.00 284.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 280)">
<title>dependencies</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-280 4198,-280 4198,4 -4,4"/>
<text text-anchor="start" x="2076.01" y="-42.4" font-family="Times-12" font-weight="bold" font-size="14.00">Legend</text>
<polygon fill="#ffffb3" stroke="transparent" points="1863,-10 1863,-30 1883,-30 1883,-10 1863,-10"/>
<text text-anchor="start" x="1886.63" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Declarations</text>
<polygon fill="#8dd3c7" stroke="transparent" points="1976,-10 1976,-30 1996,-30 1996,-10 1976,-10"/>
<text text-anchor="start" x="1999.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Module</text>
<polygon fill="#80b1d3" stroke="transparent" points="2062,-10 2062,-30 2082,-30 2082,-10 2062,-10"/>
<text text-anchor="start" x="2085.78" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Bootstrap</text>
<polygon fill="#fdb462" stroke="transparent" points="2159,-10 2159,-30 2179,-30 2179,-10 2159,-10"/>
<text text-anchor="start" x="2182.67" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Providers</text>
<polygon fill="#fb8072" stroke="transparent" points="2255,-10 2255,-30 2275,-30 2275,-10 2255,-10"/>
<text text-anchor="start" x="2278.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Exports</text>
<g id="clust1" class="cluster">
<title>cluster_AccountsModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="8,-70 8,-268 4186,-268 4186,-70 8,-70"/>
</g>
<g id="clust3" class="cluster">
<title>cluster_AccountsModule_imports</title>
<polygon fill="none" stroke="black" points="3306,-78 3306,-130 4178,-130 4178,-78 3306,-78"/>
</g>
<g id="clust4" class="cluster">
<title>cluster_AccountsModule_exports</title>
<polygon fill="none" stroke="black" points="1706,-208 1706,-260 3315,-260 3315,-208 1706,-208"/>
</g>
<g id="clust6" class="cluster">
<title>cluster_AccountsModule_providers</title>
<polygon fill="none" stroke="black" points="16,-78 16,-130 3298,-130 3298,-78 16,-78"/>
</g>
<!-- ActivitiesModule -->
<g id="node1" class="node">
<title>ActivitiesModule</title>
<polygon fill="#8dd3c7" stroke="black" points="4169.98,-122 4166.98,-126 4145.98,-126 4142.98,-122 4056.02,-122 4056.02,-86 4169.98,-86 4169.98,-122"/>
<text text-anchor="middle" x="4113" y="-99.8" font-family="Times,serif" font-size="14.00">ActivitiesModule</text>
</g>
<!-- AccountsModule -->
<g id="node8" class="node">
<title>AccountsModule</title>
<polygon fill="#8dd3c7" stroke="black" points="2526.42,-187 2523.42,-191 2502.42,-191 2499.42,-187 2413.58,-187 2413.58,-151 2526.42,-151 2526.42,-187"/>
<text text-anchor="middle" x="2470" y="-164.8" font-family="Times,serif" font-size="14.00">AccountsModule</text>
</g>
<!-- ActivitiesModule&#45;&gt;AccountsModule -->
<g id="edge1" class="edge">
<title>ActivitiesModule&#45;&gt;AccountsModule</title>
<path fill="none" stroke="black" d="M4113,-122.17C4113,-143.5 4113,-176 4113,-176 4113,-176 2536.56,-176 2536.56,-176"/>
<polygon fill="black" stroke="black" points="2536.56,-172.5 2526.56,-176 2536.56,-179.5 2536.56,-172.5"/>
</g>
<!-- CommonModule -->
<g id="node2" class="node">
<title>CommonModule</title>
<polygon fill="#8dd3c7" stroke="black" points="4037.67,-122 4034.67,-126 4013.67,-126 4010.67,-122 3926.33,-122 3926.33,-86 4037.67,-86 4037.67,-122"/>
<text text-anchor="middle" x="3982" y="-99.8" font-family="Times,serif" font-size="14.00">CommonModule</text>
</g>
<!-- CommonModule&#45;&gt;AccountsModule -->
<g id="edge2" class="edge">
<title>CommonModule&#45;&gt;AccountsModule</title>
<path fill="none" stroke="black" d="M3982,-122.03C3982,-142.77 3982,-174 3982,-174 3982,-174 2536.51,-174 2536.51,-174"/>
<polygon fill="black" stroke="black" points="2536.51,-170.5 2526.51,-174 2536.51,-177.5 2536.51,-170.5"/>
</g>
<!-- ConfigModule -->
<g id="node3" class="node">
<title>ConfigModule</title>
<polygon fill="#8dd3c7" stroke="black" points="3908.44,-122 3905.44,-126 3884.44,-126 3881.44,-122 3809.56,-122 3809.56,-86 3908.44,-86 3908.44,-122"/>
<text text-anchor="middle" x="3859" y="-99.8" font-family="Times,serif" font-size="14.00">ConfigModule</text>
</g>
<!-- ConfigModule&#45;&gt;AccountsModule -->
<g id="edge3" class="edge">
<title>ConfigModule&#45;&gt;AccountsModule</title>
<path fill="none" stroke="black" d="M3859,-122.31C3859,-142.15 3859,-171 3859,-171 3859,-171 2536.6,-171 2536.6,-171"/>
<polygon fill="black" stroke="black" points="2536.6,-167.5 2526.6,-171 2536.6,-174.5 2536.6,-167.5"/>
</g>
<!-- CustomFieldModule -->
<g id="node4" class="node">
<title>CustomFieldModule</title>
<polygon fill="#8dd3c7" stroke="black" points="3791.89,-122 3788.89,-126 3767.89,-126 3764.89,-122 3660.11,-122 3660.11,-86 3791.89,-86 3791.89,-122"/>
<text text-anchor="middle" x="3726" y="-99.8" font-family="Times,serif" font-size="14.00">CustomFieldModule</text>
</g>
<!-- CustomFieldModule&#45;&gt;AccountsModule -->
<g id="edge4" class="edge">
<title>CustomFieldModule&#45;&gt;AccountsModule</title>
<path fill="none" stroke="black" d="M3726,-122.17C3726,-141.09 3726,-168 3726,-168 3726,-168 2536.41,-168 2536.41,-168"/>
<polygon fill="black" stroke="black" points="2536.41,-164.5 2526.41,-168 2536.41,-171.5 2536.41,-164.5"/>
</g>
<!-- SharedModule -->
<g id="node5" class="node">
<title>SharedModule</title>
<polygon fill="#8dd3c7" stroke="black" points="3642.42,-122 3639.42,-126 3618.42,-126 3615.42,-122 3543.58,-122 3543.58,-86 3642.42,-86 3642.42,-122"/>
<text text-anchor="middle" x="3593" y="-99.8" font-family="Times,serif" font-size="14.00">SharedModule</text>
</g>
<!-- SharedModule&#45;&gt;AccountsModule -->
<g id="edge5" class="edge">
<title>SharedModule&#45;&gt;AccountsModule</title>
<path fill="none" stroke="black" d="M3593,-122.3C3593,-140.27 3593,-165 3593,-165 3593,-165 2536.46,-165 2536.46,-165"/>
<polygon fill="black" stroke="black" points="2536.46,-161.5 2526.46,-165 2536.46,-168.5 2536.46,-161.5"/>
</g>
<!-- StorageModule -->
<g id="node6" class="node">
<title>StorageModule</title>
<polygon fill="#8dd3c7" stroke="black" points="3526.31,-122 3523.31,-126 3502.31,-126 3499.31,-122 3423.69,-122 3423.69,-86 3526.31,-86 3526.31,-122"/>
<text text-anchor="middle" x="3475" y="-99.8" font-family="Times,serif" font-size="14.00">StorageModule</text>
</g>
<!-- StorageModule&#45;&gt;AccountsModule -->
<g id="edge6" class="edge">
<title>StorageModule&#45;&gt;AccountsModule</title>
<path fill="none" stroke="black" d="M3475,-122.02C3475,-139.37 3475,-163 3475,-163 3475,-163 2536.55,-163 2536.55,-163"/>
<polygon fill="black" stroke="black" points="2536.55,-159.5 2526.55,-163 2536.55,-166.5 2536.55,-159.5"/>
</g>
<!-- UsersModule -->
<g id="node7" class="node">
<title>UsersModule</title>
<polygon fill="#8dd3c7" stroke="black" points="3405.92,-122 3402.92,-126 3381.92,-126 3378.92,-122 3314.08,-122 3314.08,-86 3405.92,-86 3405.92,-122"/>
<text text-anchor="middle" x="3360" y="-99.8" font-family="Times,serif" font-size="14.00">UsersModule</text>
</g>
<!-- UsersModule&#45;&gt;AccountsModule -->
<g id="edge7" class="edge">
<title>UsersModule&#45;&gt;AccountsModule</title>
<path fill="none" stroke="black" d="M3360,-122.03C3360,-138.4 3360,-160 3360,-160 3360,-160 2536.35,-160 2536.35,-160"/>
<polygon fill="black" stroke="black" points="2536.35,-156.5 2526.35,-160 2536.35,-163.5 2536.35,-156.5"/>
</g>
<!-- AccountActivityActionService  -->
<g id="node9" class="node">
<title>AccountActivityActionService </title>
<polygon fill="#fb8072" stroke="black" points="3307.39,-252 3114.61,-252 3114.61,-216 3307.39,-216 3307.39,-252"/>
<text text-anchor="middle" x="3211" y="-229.8" font-family="Times,serif" font-size="14.00">AccountActivityActionService </text>
</g>
<!-- AccountsModule&#45;&gt;AccountActivityActionService  -->
<g id="edge8" class="edge">
<title>AccountsModule&#45;&gt;AccountActivityActionService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2526.23,-179C2697.66,-179 3202.1,-179 3202.1,-179 3202.1,-179 3202.1,-205.99 3202.1,-205.99"/>
<polygon fill="black" stroke="black" points="3198.6,-205.99 3202.1,-215.99 3205.6,-205.99 3198.6,-205.99"/>
</g>
<!-- AccountAnnotatorService  -->
<g id="node10" class="node">
<title>AccountAnnotatorService </title>
<polygon fill="#fb8072" stroke="black" points="3096.67,-252 2931.33,-252 2931.33,-216 3096.67,-216 3096.67,-252"/>
<text text-anchor="middle" x="3014" y="-229.8" font-family="Times,serif" font-size="14.00">AccountAnnotatorService </text>
</g>
<!-- AccountsModule&#45;&gt;AccountAnnotatorService  -->
<g id="edge9" class="edge">
<title>AccountsModule&#45;&gt;AccountAnnotatorService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2526.5,-182C2657.53,-182 2971.01,-182 2971.01,-182 2971.01,-182 2971.01,-205.81 2971.01,-205.81"/>
<polygon fill="black" stroke="black" points="2967.51,-205.81 2971.01,-215.81 2974.51,-205.81 2967.51,-205.81"/>
</g>
<!-- AccountAttributeValueActionService  -->
<g id="node11" class="node">
<title>AccountAttributeValueActionService </title>
<polygon fill="#fb8072" stroke="black" points="2913.48,-252 2682.52,-252 2682.52,-216 2913.48,-216 2913.48,-252"/>
<text text-anchor="middle" x="2798" y="-229.8" font-family="Times,serif" font-size="14.00">AccountAttributeValueActionService </text>
</g>
<!-- AccountsModule&#45;&gt;AccountAttributeValueActionService  -->
<g id="edge10" class="edge">
<title>AccountsModule&#45;&gt;AccountAttributeValueActionService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2526.43,-185C2601.74,-185 2725.71,-185 2725.71,-185 2725.71,-185 2725.71,-205.75 2725.71,-205.75"/>
<polygon fill="black" stroke="black" points="2722.21,-205.75 2725.71,-215.75 2729.21,-205.75 2722.21,-205.75"/>
</g>
<!-- AccountNoteActionService  -->
<g id="node12" class="node">
<title>AccountNoteActionService </title>
<polygon fill="#fb8072" stroke="black" points="2664.22,-252 2489.78,-252 2489.78,-216 2664.22,-216 2664.22,-252"/>
<text text-anchor="middle" x="2577" y="-229.8" font-family="Times,serif" font-size="14.00">AccountNoteActionService </text>
</g>
<!-- AccountsModule&#45;&gt;AccountNoteActionService  -->
<g id="edge11" class="edge">
<title>AccountsModule&#45;&gt;AccountNoteActionService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2508.05,-187.11C2508.05,-187.11 2508.05,-205.99 2508.05,-205.99"/>
<polygon fill="black" stroke="black" points="2504.55,-205.99 2508.05,-215.99 2511.55,-205.99 2504.55,-205.99"/>
</g>
<!-- AccountRelationshipActionService  -->
<g id="node13" class="node">
<title>AccountRelationshipActionService </title>
<polygon fill="#fb8072" stroke="black" points="2471.78,-252 2254.22,-252 2254.22,-216 2471.78,-216 2471.78,-252"/>
<text text-anchor="middle" x="2363" y="-229.8" font-family="Times,serif" font-size="14.00">AccountRelationshipActionService </text>
</g>
<!-- AccountsModule&#45;&gt;AccountRelationshipActionService  -->
<g id="edge12" class="edge">
<title>AccountsModule&#45;&gt;AccountRelationshipActionService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2442.84,-187.11C2442.84,-187.11 2442.84,-205.99 2442.84,-205.99"/>
<polygon fill="black" stroke="black" points="2439.34,-205.99 2442.84,-215.99 2446.34,-205.99 2439.34,-205.99"/>
</g>
<!-- AccountTaskActionService  -->
<g id="node14" class="node">
<title>AccountTaskActionService </title>
<polygon fill="#fb8072" stroke="black" points="2236.22,-252 2061.78,-252 2061.78,-216 2236.22,-216 2236.22,-252"/>
<text text-anchor="middle" x="2149" y="-229.8" font-family="Times,serif" font-size="14.00">AccountTaskActionService </text>
</g>
<!-- AccountsModule&#45;&gt;AccountTaskActionService  -->
<g id="edge13" class="edge">
<title>AccountsModule&#45;&gt;AccountTaskActionService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2413.5,-185C2341.91,-185 2227.73,-185 2227.73,-185 2227.73,-185 2227.73,-205.75 2227.73,-205.75"/>
<polygon fill="black" stroke="black" points="2224.23,-205.75 2227.73,-215.75 2231.23,-205.75 2224.23,-205.75"/>
</g>
<!-- AccountsService  -->
<g id="node15" class="node">
<title>AccountsService </title>
<polygon fill="#fb8072" stroke="black" points="2044.35,-252 1929.65,-252 1929.65,-216 2044.35,-216 2044.35,-252"/>
<text text-anchor="middle" x="1987" y="-229.8" font-family="Times,serif" font-size="14.00">AccountsService </text>
</g>
<!-- AccountsModule&#45;&gt;AccountsService  -->
<g id="edge14" class="edge">
<title>AccountsModule&#45;&gt;AccountsService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2413.47,-182C2289.6,-182 2005.21,-182 2005.21,-182 2005.21,-182 2005.21,-205.81 2005.21,-205.81"/>
<polygon fill="black" stroke="black" points="2001.71,-205.81 2005.21,-215.81 2008.71,-205.81 2001.71,-205.81"/>
</g>
<!-- CustomerContactActionService  -->
<g id="node16" class="node">
<title>CustomerContactActionService </title>
<polygon fill="#fb8072" stroke="black" points="1911.57,-252 1714.43,-252 1714.43,-216 1911.57,-216 1911.57,-252"/>
<text text-anchor="middle" x="1813" y="-229.8" font-family="Times,serif" font-size="14.00">CustomerContactActionService </text>
</g>
<!-- AccountsModule&#45;&gt;CustomerContactActionService  -->
<g id="edge15" class="edge">
<title>AccountsModule&#45;&gt;CustomerContactActionService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2413.7,-179C2254.58,-179 1813,-179 1813,-179 1813,-179 1813,-205.99 1813,-205.99"/>
<polygon fill="black" stroke="black" points="1809.5,-205.99 1813,-215.99 1816.5,-205.99 1809.5,-205.99"/>
</g>
<!-- AccountActivityActionService -->
<g id="node17" class="node">
<title>AccountActivityActionService</title>
<ellipse fill="#fdb462" stroke="black" cx="3159" cy="-104" rx="130.78" ry="18"/>
<text text-anchor="middle" x="3159" y="-99.8" font-family="Times,serif" font-size="14.00">AccountActivityActionService</text>
</g>
<!-- AccountActivityActionService&#45;&gt;AccountsModule -->
<g id="edge16" class="edge">
<title>AccountActivityActionService&#45;&gt;AccountsModule</title>
<path fill="none" stroke="black" d="M3062.6,-116.29C3062.6,-131.95 3062.6,-157 3062.6,-157 3062.6,-157 2536.28,-157 2536.28,-157"/>
<polygon fill="black" stroke="black" points="2536.28,-153.5 2526.28,-157 2536.28,-160.5 2536.28,-153.5"/>
</g>
<!-- AccountAnnotatorService -->
<g id="node18" class="node">
<title>AccountAnnotatorService</title>
<ellipse fill="#fdb462" stroke="black" cx="2899" cy="-104" rx="111.72" ry="18"/>
<text text-anchor="middle" x="2899" y="-99.8" font-family="Times,serif" font-size="14.00">AccountAnnotatorService</text>
</g>
<!-- AccountAnnotatorService&#45;&gt;AccountsModule -->
<g id="edge17" class="edge">
<title>AccountAnnotatorService&#45;&gt;AccountsModule</title>
<path fill="none" stroke="black" d="M2850.31,-120.37C2850.31,-134.95 2850.31,-154 2850.31,-154 2850.31,-154 2536.53,-154 2536.53,-154"/>
<polygon fill="black" stroke="black" points="2536.53,-150.5 2526.53,-154 2536.53,-157.5 2536.53,-150.5"/>
</g>
<!-- AccountAttributeValueActionService -->
<g id="node19" class="node">
<title>AccountAttributeValueActionService</title>
<ellipse fill="#fdb462" stroke="black" cx="2612" cy="-104" rx="156.82" ry="18"/>
<text text-anchor="middle" x="2612" y="-99.8" font-family="Times,serif" font-size="14.00">AccountAttributeValueActionService</text>
</g>
<!-- AccountAttributeValueActionService&#45;&gt;AccountsModule -->
<g id="edge18" class="edge">
<title>AccountAttributeValueActionService&#45;&gt;AccountsModule</title>
<path fill="none" stroke="black" d="M2490.65,-115.62C2490.65,-115.62 2490.65,-140.85 2490.65,-140.85"/>
<polygon fill="black" stroke="black" points="2487.15,-140.85 2490.65,-150.85 2494.15,-140.85 2487.15,-140.85"/>
</g>
<!-- AccountCommonService -->
<g id="node20" class="node">
<title>AccountCommonService</title>
<ellipse fill="#fdb462" stroke="black" cx="2328" cy="-104" rx="108.81" ry="18"/>
<text text-anchor="middle" x="2328" y="-99.8" font-family="Times,serif" font-size="14.00">AccountCommonService</text>
</g>
<!-- AccountCommonService&#45;&gt;AccountsModule -->
<g id="edge19" class="edge">
<title>AccountCommonService&#45;&gt;AccountsModule</title>
<path fill="none" stroke="black" d="M2425.22,-112.23C2425.22,-112.23 2425.22,-140.76 2425.22,-140.76"/>
<polygon fill="black" stroke="black" points="2421.72,-140.76 2425.22,-150.76 2428.72,-140.76 2421.72,-140.76"/>
</g>
<!-- AccountNoteActionService -->
<g id="node21" class="node">
<title>AccountNoteActionService</title>
<ellipse fill="#fdb462" stroke="black" cx="2084" cy="-104" rx="117.52" ry="18"/>
<text text-anchor="middle" x="2084" y="-99.8" font-family="Times,serif" font-size="14.00">AccountNoteActionService</text>
</g>
<!-- AccountNoteActionService&#45;&gt;AccountsModule -->
<g id="edge20" class="edge">
<title>AccountNoteActionService&#45;&gt;AccountsModule</title>
<path fill="none" stroke="black" d="M2131.82,-120.65C2131.82,-135.19 2131.82,-154 2131.82,-154 2131.82,-154 2403.78,-154 2403.78,-154"/>
<polygon fill="black" stroke="black" points="2403.78,-157.5 2413.78,-154 2403.78,-150.5 2403.78,-157.5"/>
</g>
<!-- AccountRelationshipActionService -->
<g id="node22" class="node">
<title>AccountRelationshipActionService</title>
<ellipse fill="#fdb462" stroke="black" cx="1800" cy="-104" rx="147.61" ry="18"/>
<text text-anchor="middle" x="1800" y="-99.8" font-family="Times,serif" font-size="14.00">AccountRelationshipActionService</text>
</g>
<!-- AccountRelationshipActionService&#45;&gt;AccountsModule -->
<g id="edge21" class="edge">
<title>AccountRelationshipActionService&#45;&gt;AccountsModule</title>
<path fill="none" stroke="black" d="M1938.82,-110.5C1938.82,-124.65 1938.82,-157 1938.82,-157 1938.82,-157 2403.66,-157 2403.66,-157"/>
<polygon fill="black" stroke="black" points="2403.66,-160.5 2413.66,-157 2403.66,-153.5 2403.66,-160.5"/>
</g>
<!-- AccountTaskActionService -->
<g id="node23" class="node">
<title>AccountTaskActionService</title>
<ellipse fill="#fdb462" stroke="black" cx="1516" cy="-104" rx="117.52" ry="18"/>
<text text-anchor="middle" x="1516" y="-99.8" font-family="Times,serif" font-size="14.00">AccountTaskActionService</text>
</g>
<!-- AccountTaskActionService&#45;&gt;AccountsModule -->
<g id="edge22" class="edge">
<title>AccountTaskActionService&#45;&gt;AccountsModule</title>
<path fill="none" stroke="black" d="M1516,-122.03C1516,-138.4 1516,-160 1516,-160 1516,-160 2403.53,-160 2403.53,-160"/>
<polygon fill="black" stroke="black" points="2403.53,-163.5 2413.53,-160 2403.53,-156.5 2403.53,-163.5"/>
</g>
<!-- AccountsEventsFactory -->
<g id="node24" class="node">
<title>AccountsEventsFactory</title>
<ellipse fill="#fdb462" stroke="black" cx="1277" cy="-104" rx="103.07" ry="18"/>
<text text-anchor="middle" x="1277" y="-99.8" font-family="Times,serif" font-size="14.00">AccountsEventsFactory</text>
</g>
<!-- AccountsEventsFactory&#45;&gt;AccountsModule -->
<g id="edge23" class="edge">
<title>AccountsEventsFactory&#45;&gt;AccountsModule</title>
<path fill="none" stroke="black" d="M1277,-122.02C1277,-139.37 1277,-163 1277,-163 1277,-163 2403.66,-163 2403.66,-163"/>
<polygon fill="black" stroke="black" points="2403.66,-166.5 2413.66,-163 2403.66,-159.5 2403.66,-166.5"/>
</g>
<!-- AccountsListeners -->
<g id="node25" class="node">
<title>AccountsListeners</title>
<ellipse fill="#fdb462" stroke="black" cx="1073" cy="-104" rx="82.8" ry="18"/>
<text text-anchor="middle" x="1073" y="-99.8" font-family="Times,serif" font-size="14.00">AccountsListeners</text>
</g>
<!-- AccountsListeners&#45;&gt;AccountsModule -->
<g id="edge24" class="edge">
<title>AccountsListeners&#45;&gt;AccountsModule</title>
<path fill="none" stroke="black" d="M1073,-122.3C1073,-140.27 1073,-165 1073,-165 1073,-165 2403.51,-165 2403.51,-165"/>
<polygon fill="black" stroke="black" points="2403.51,-168.5 2413.51,-165 2403.51,-161.5 2403.51,-168.5"/>
</g>
<!-- AccountsSNSPublisher -->
<g id="node26" class="node">
<title>AccountsSNSPublisher</title>
<ellipse fill="#fdb462" stroke="black" cx="870" cy="-104" rx="101.88" ry="18"/>
<text text-anchor="middle" x="870" y="-99.8" font-family="Times,serif" font-size="14.00">AccountsSNSPublisher</text>
</g>
<!-- AccountsSNSPublisher&#45;&gt;AccountsModule -->
<g id="edge25" class="edge">
<title>AccountsSNSPublisher&#45;&gt;AccountsModule</title>
<path fill="none" stroke="black" d="M870,-122.17C870,-141.09 870,-168 870,-168 870,-168 2403.53,-168 2403.53,-168"/>
<polygon fill="black" stroke="black" points="2403.53,-171.5 2413.53,-168 2403.53,-164.5 2403.53,-171.5"/>
</g>
<!-- AccountsService -->
<g id="node27" class="node">
<title>AccountsService</title>
<ellipse fill="#fdb462" stroke="black" cx="674" cy="-104" rx="76.42" ry="18"/>
<text text-anchor="middle" x="674" y="-99.8" font-family="Times,serif" font-size="14.00">AccountsService</text>
</g>
<!-- AccountsService&#45;&gt;AccountsModule -->
<g id="edge26" class="edge">
<title>AccountsService&#45;&gt;AccountsModule</title>
<path fill="none" stroke="black" d="M674,-122.31C674,-142.15 674,-171 674,-171 674,-171 2403.78,-171 2403.78,-171"/>
<polygon fill="black" stroke="black" points="2403.78,-174.5 2413.78,-171 2403.78,-167.5 2403.78,-174.5"/>
</g>
<!-- CustomerContactActionService -->
<g id="node28" class="node">
<title>CustomerContactActionService</title>
<ellipse fill="#fdb462" stroke="black" cx="446" cy="-104" rx="133.72" ry="18"/>
<text text-anchor="middle" x="446" y="-99.8" font-family="Times,serif" font-size="14.00">CustomerContactActionService</text>
</g>
<!-- CustomerContactActionService&#45;&gt;AccountsModule -->
<g id="edge27" class="edge">
<title>CustomerContactActionService&#45;&gt;AccountsModule</title>
<path fill="none" stroke="black" d="M446,-122.03C446,-142.77 446,-174 446,-174 446,-174 2403.75,-174 2403.75,-174"/>
<polygon fill="black" stroke="black" points="2403.75,-177.5 2413.75,-174 2403.75,-170.5 2403.75,-177.5"/>
</g>
<!-- CustomerContactsIngestService -->
<g id="node29" class="node">
<title>CustomerContactsIngestService</title>
<ellipse fill="#fdb462" stroke="black" cx="159" cy="-104" rx="134.87" ry="18"/>
<text text-anchor="middle" x="159" y="-99.8" font-family="Times,serif" font-size="14.00">CustomerContactsIngestService</text>
</g>
<!-- CustomerContactsIngestService&#45;&gt;AccountsModule -->
<g id="edge28" class="edge">
<title>CustomerContactsIngestService&#45;&gt;AccountsModule</title>
<path fill="none" stroke="black" d="M159,-122.17C159,-143.5 159,-176 159,-176 159,-176 2403.73,-176 2403.73,-176"/>
<polygon fill="black" stroke="black" points="2403.73,-179.5 2413.73,-176 2403.73,-172.5 2403.73,-179.5"/>
</g>
</g>
</svg>

    </div>
    <i id="fullscreen" class="icon ion-ios-resize module-graph-fullscreen-btn" aria-hidden="true"></i>
    <div class="btn-group size-buttons">
        <button id="zoom-in" class="btn btn-default btn-sm">Zoom in</button>
        <button id="reset" class="btn btn-default btn-sm">Reset</button>
        <button id="zoom-out" class="btn btn-default btn-sm">Zoom out</button>
    </div>
</div>
<script src="../js/libs/svg-pan-zoom.min.js"></script>
<script src="../js/svg-pan-zoom.controls.js"></script>

<ul class="nav nav-tabs" role="tablist">
    <li class="nav-item">
        <a href="#info" 
            class="nav-link"
            class="nav-link active"
            role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
    </li>
    <li class="nav-item">
        <a href="#source" 
            class="nav-link"
            
            role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
    </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">

        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/accounts/accounts.module.ts</code>
        </p>





        <div class="container-fluid module">
            <div class="row">
                <div class="col-sm-3">
                    <h3>Providers<a href="https://angular.io/api/core/NgModule#providers" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module providers"><span class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../injectables/AccountActivityActionService.html">AccountActivityActionService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/AccountAnnotatorService.html">AccountAnnotatorService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/AccountAttributeValueActionService.html">AccountAttributeValueActionService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/AccountCommonService.html">AccountCommonService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/AccountNoteActionService.html">AccountNoteActionService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/AccountRelationshipActionService.html">AccountRelationshipActionService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/AccountTaskActionService.html">AccountTaskActionService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/AccountsEventsFactory.html">AccountsEventsFactory</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/AccountsListeners.html">AccountsListeners</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/AccountsSNSPublisher.html">AccountsSNSPublisher</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/AccountsService.html">AccountsService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/CustomerContactActionService.html">CustomerContactActionService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/CustomerContactsIngestService.html">CustomerContactsIngestService</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Controllers<a href="https://docs.nestjs.com/controllers" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module controllers"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../controllers/AccountsController.html">AccountsController</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../controllers/AccountAttributeValueActionController.html">AccountAttributeValueActionController</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../controllers/AccountRelationshipActionController.html">AccountRelationshipActionController</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../controllers/CustomerContactActionController.html">CustomerContactActionController</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../controllers/AccountActivityActionController.html">AccountActivityActionController</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../controllers/AccountNoteActionController.html">AccountNoteActionController</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../controllers/AccountTaskActionController.html">AccountTaskActionController</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../controllers/CustomerContactsIngestController.html">CustomerContactsIngestController</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../controllers/AccountAnnotatorGrpcController.html">AccountAnnotatorGrpcController</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../controllers/AccountsGrpcController.html">AccountsGrpcController</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../controllers/AccountRelationshipsGrpcController.html">AccountRelationshipsGrpcController</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../controllers/AccountActivitiesGrpcController.html">AccountActivitiesGrpcController</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../controllers/AccountNotesGrpcController.html">AccountNotesGrpcController</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../controllers/AccountTasksGrpcController.html">AccountTasksGrpcController</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../controllers/AccountAttributeValuesGrpcController.html">AccountAttributeValuesGrpcController</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Imports<a href="https://angular.io/api/core/NgModule#imports" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module imports"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../modules/ActivitiesModule.html">ActivitiesModule</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../modules/CommonModule.html">CommonModule</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../modules/ConfigModule.html">ConfigModule</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../modules/CustomFieldModule.html">CustomFieldModule</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../modules/SharedModule.html">SharedModule</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../modules/StorageModule.html">StorageModule</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../modules/UsersModule.html">UsersModule</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Exports<a href="https://angular.io/api/core/NgModule#exports" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module exports"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../injectables/AccountActivityActionService.html">AccountActivityActionService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/AccountAnnotatorService.html">AccountAnnotatorService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/AccountAttributeValueActionService.html">AccountAttributeValueActionService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/AccountNoteActionService.html">AccountNoteActionService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/AccountRelationshipActionService.html">AccountRelationshipActionService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/AccountTaskActionService.html">AccountTaskActionService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/AccountsService.html">AccountsService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/CustomerContactActionService.html">CustomerContactActionService</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>


    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { BullModule } from &quot;@nestjs/bullmq&quot;;
import { Module } from &quot;@nestjs/common&quot;;
import { TypeOrmModule } from &quot;@nestjs/typeorm&quot;;
import { SentryService } from &quot;@repo/nestjs-commons/filters&quot;;
import {
  ApiKeyGrpcStrategy,
  AuthenticationGrpcClient,
  BearerTokenGrpcStrategy,
  GrpcAuthGuard,
  UserOrgInternalGrpcStrategy,
} from &quot;@repo/nestjs-commons/guards&quot;;
import { ILogger } from &quot;@repo/nestjs-commons/logger&quot;;
import { SNSPublisherService } from &quot;@repo/thena-eventbridge&quot;;
import {
  Account,
  AccountActivity,
  AccountActivityRepository,
  AccountAttributeValue,
  AccountAttributeValueRepository,
  AccountNote,
  AccountNoteRepository,
  AccountRelationship,
  AccountRelationshipRepository,
  AccountRelationshipType,
  AccountRelationshipTypeRepository,
  AccountRepository,
  AccountTask,
  AccountTaskRepository,
  CachedAccountActivityRepository,
  CachedAccountAttributeValueRepository,
  CachedAccountNoteRepository,
  CachedAccountRelationshipRepository,
  CachedAccountRelationshipTypeRepository,
  CachedAccountRepository,
  CachedAccountTaskRepository,
  CachedCustomerContactRepository,
  CustomerContact,
  CustomerContactRepository,
  TransactionService,
} from &quot;@repo/thena-platform-entities&quot;;
import { ActivitiesModule } from &quot;../activities/activities.module&quot;;
import { CommonModule } from &quot;../common/common.module&quot;;
import { ConfigModule } from &quot;../config/config.module&quot;;
import { ConfigKeys, ConfigService } from &quot;../config/config.service&quot;;
import { QueueNames } from &quot;../constants/queue.constants&quot;;
import { CustomFieldModule } from &quot;../custom-field/custom-field.module&quot;;
import { SharedModule } from &quot;../shared/shared.module&quot;;
import { StorageModule } from &quot;../storage/module/storage.module&quot;;
import { UsersModule } from &quot;../users/users.module&quot;;
import { AccountActivityActionController } from &quot;./controllers/account-activity.action.controller&quot;;
import { AccountAttributeValueActionController } from &quot;./controllers/account-attribute-value.action.controller&quot;;
import { AccountNoteActionController } from &quot;./controllers/account-note.action.controller&quot;;
import { AccountRelationshipActionController } from &quot;./controllers/account-relationship.action.controller&quot;;
import { AccountTaskActionController } from &quot;./controllers/account-task.action.controller&quot;;
import { AccountsController } from &quot;./controllers/accounts.controller&quot;;
import { CustomerContactActionController } from &quot;./controllers/customer-contact.action.controller&quot;;
import { CustomerContactsIngestController } from &quot;./controllers/customer-contacts-ingest.controller&quot;;
import { AccountActivitiesGrpcController } from &quot;./controllers/grpc/account-activities.grpc.controller&quot;;
import { AccountAnnotatorGrpcController } from &quot;./controllers/grpc/account-annotator.grpc.controller&quot;;
import { AccountAttributeValuesGrpcController } from &quot;./controllers/grpc/account-attribute-values.grpc.controller&quot;;
import { AccountNotesGrpcController } from &quot;./controllers/grpc/account-notes.grpc.controller&quot;;
import { AccountRelationshipsGrpcController } from &quot;./controllers/grpc/account-relationships.grpc.controller&quot;;
import { AccountTasksGrpcController } from &quot;./controllers/grpc/account-tasks.grpc.controller&quot;;
import { AccountsGrpcController } from &quot;./controllers/grpc/accounts.grpc.controller&quot;;
import { AccountsEventsFactory } from &quot;./events/accounts-events.factory&quot;;
import { AccountsSNSEventsFactory } from &quot;./events/accounts-sns-events.factory&quot;;
import { AccountsListeners } from &quot;./listeners/accounts.listeners&quot;;
import { AccountsSNSPublisher } from &quot;./processors/sns-publisher.processor&quot;;
import { AccountActivityActionService } from &quot;./services/account-activity.action.service&quot;;
import { AccountAnnotatorService } from &quot;./services/account-annotator.service&quot;;
import { AccountAttributeValueActionService } from &quot;./services/account-attribute-value.action.service&quot;;
import { AccountCommonService } from &quot;./services/account-commons.service&quot;;
import { AccountNoteActionService } from &quot;./services/account-note.action.service&quot;;
import { AccountRelationshipActionService } from &quot;./services/account-relationship.action.service&quot;;
import { AccountTaskActionService } from &quot;./services/account-task.action.service&quot;;
import { AccountsService } from &quot;./services/accounts.service&quot;;
import { CustomerContactActionService } from &quot;./services/customer-contact.action.service&quot;;
import { CustomerContactsIngestService } from &quot;./services/customer-contacts-ingest.service&quot;;

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Account,
      CustomerContact,
      AccountAttributeValue,
      AccountRelationship,
      AccountRelationshipType,
      AccountActivity,
      AccountTask,
      AccountNote,

      AccountRepository,
      CustomerContactRepository,
      AccountAttributeValueRepository,
      AccountRelationshipRepository,
      AccountRelationshipTypeRepository,
      AccountActivityRepository,
      AccountNoteRepository,
      AccountTaskRepository,
    ]),
    ConfigModule,
    CommonModule,
    UsersModule,
    ActivitiesModule,
    CustomFieldModule,
    StorageModule,
    BullModule.registerQueueAsync({
      name: QueueNames.ACCOUNTS_SNS_PUBLISHER,
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) &#x3D;&gt; ({
        redis: {
          host: configService.get(ConfigKeys.REDIS_HOST),
          port: Number(configService.get(ConfigKeys.REDIS_PORT)),
          password: configService.get(ConfigKeys.REDIS_PASSWORD),
          username: configService.get(ConfigKeys.REDIS_USERNAME),
        },
        defaultJobOptions: {
          removeOnComplete: {
            age: 24 * 3600, // keep completed jobs for 24 hours
            count: 1000, // keep last 1000 completed jobs
          },
          removeOnFail: {
            age: 24 * 3600, // keep up to 24 hours
          },
        },
      }),
    }),
    SharedModule,
  ],
  controllers: [
    AccountsController,
    AccountAttributeValueActionController,
    AccountRelationshipActionController,
    CustomerContactActionController,
    AccountActivityActionController,
    AccountNoteActionController,
    AccountTaskActionController,
    CustomerContactsIngestController,

    // gRPC controllers
    AccountAnnotatorGrpcController,
    AccountsGrpcController,
    AccountRelationshipsGrpcController,
    AccountActivitiesGrpcController,
    AccountNotesGrpcController,
    AccountTasksGrpcController,
    AccountAttributeValuesGrpcController,
  ],
  providers: [
    // SNS publisher
    {
      provide: &quot;ACCOUNTS_SNS_PUBLISHER&quot;,
      useFactory: (
        configService: ConfigService,
        sentryService: SentryService,
        loggerService: ILogger,
      ) &#x3D;&gt; {
        return new SNSPublisherService(
          {
            topicArn: configService.get(ConfigKeys.AWS_SNS_ACCOUNTS_TOPIC_ARN),
            region: configService.get(ConfigKeys.AWS_REGION),
            credentials: {
              accessKeyId: configService.get(ConfigKeys.AWS_ACCESS_KEY),
              secretAccessKey: configService.get(ConfigKeys.AWS_SECRET_KEY),
            },
          },
          sentryService,
          loggerService,
        );
      },
      inject: [ConfigService, &quot;Sentry&quot;, &quot;CustomLogger&quot;],
    },

    // Services
    AccountsService,
    AccountCommonService,
    AccountAttributeValueActionService,
    AccountRelationshipActionService,
    CustomerContactActionService,
    AccountActivityActionService,
    AccountNoteActionService,
    AccountTaskActionService,
    AccountAnnotatorService,
    CustomerContactsIngestService,

    // Repositories
    TransactionService,
    AccountRepository,
    CustomerContactRepository,
    AccountAttributeValueRepository,
    AccountRelationshipRepository,
    AccountRelationshipTypeRepository,
    AccountActivityRepository,
    AccountNoteRepository,
    AccountTaskRepository,

    CachedAccountRepository,
    CachedCustomerContactRepository,
    CachedAccountAttributeValueRepository,
    CachedAccountRelationshipRepository,
    CachedAccountRelationshipTypeRepository,
    CachedAccountActivityRepository,
    CachedAccountNoteRepository,
    CachedAccountTaskRepository,

    // Events
    AccountsEventsFactory,
    AccountsSNSEventsFactory,

    // Listeners
    AccountsListeners,
    AccountsSNSPublisher,

    // gRPC Auth guard
    AuthenticationGrpcClient,
    GrpcAuthGuard,
    ApiKeyGrpcStrategy,
    BearerTokenGrpcStrategy,
    UserOrgInternalGrpcStrategy,
  ],
  exports: [
    // Services
    AccountsService,
    AccountAttributeValueActionService,
    AccountRelationshipActionService,
    CustomerContactActionService,
    AccountActivityActionService,
    AccountNoteActionService,
    AccountTaskActionService,
    AccountAnnotatorService,
  ],
})
export class AccountsModule {}
</code></pre>
    </div>
</div>

















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'module';
            var COMPODOC_CURRENT_PAGE_URL = 'AccountsModule.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
