<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content module">
                   <div class="content-data">



<ol class="breadcrumb">
    <li class="breadcrumb-item">Modules</li>
    <li class="breadcrumb-item" >CommonModule</li>
</ol>

<div class="text-center module-graph-container">
    <div id="module-graph-svg">
        <?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: dependencies Pages: 1 -->
<svg width="925pt" height="284pt"
 viewBox="0.00 0.00 925.00 284.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 280)">
<title>dependencies</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-280 921,-280 921,4 -4,4"/>
<text text-anchor="start" x="437.51" y="-42.4" font-family="Times-12" font-weight="bold" font-size="14.00">Legend</text>
<polygon fill="#ffffb3" stroke="transparent" points="224.5,-10 224.5,-30 244.5,-30 244.5,-10 224.5,-10"/>
<text text-anchor="start" x="248.13" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Declarations</text>
<polygon fill="#8dd3c7" stroke="transparent" points="337.5,-10 337.5,-30 357.5,-30 357.5,-10 337.5,-10"/>
<text text-anchor="start" x="361.23" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Module</text>
<polygon fill="#80b1d3" stroke="transparent" points="423.5,-10 423.5,-30 443.5,-30 443.5,-10 423.5,-10"/>
<text text-anchor="start" x="447.28" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Bootstrap</text>
<polygon fill="#fdb462" stroke="transparent" points="520.5,-10 520.5,-30 540.5,-30 540.5,-10 520.5,-10"/>
<text text-anchor="start" x="544.17" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Providers</text>
<polygon fill="#fb8072" stroke="transparent" points="616.5,-10 616.5,-30 636.5,-30 636.5,-10 616.5,-10"/>
<text text-anchor="start" x="640.23" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Exports</text>
<g id="clust1" class="cluster">
<title>cluster_CommonModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="8,-70 8,-268 909,-268 909,-70 8,-70"/>
</g>
<g id="clust4" class="cluster">
<title>cluster_CommonModule_exports</title>
<polygon fill="none" stroke="black" points="16,-208 16,-260 901,-260 901,-208 16,-208"/>
</g>
<g id="clust3" class="cluster">
<title>cluster_CommonModule_imports</title>
<polygon fill="none" stroke="black" points="739,-78 739,-130 853,-130 853,-78 739,-78"/>
</g>
<g id="clust6" class="cluster">
<title>cluster_CommonModule_providers</title>
<polygon fill="none" stroke="black" points="22,-78 22,-130 731,-130 731,-78 22,-78"/>
</g>
<!-- ConfigModule -->
<g id="node1" class="node">
<title>ConfigModule</title>
<polygon fill="#8dd3c7" stroke="black" points="845.44,-122 842.44,-126 821.44,-126 818.44,-122 746.56,-122 746.56,-86 845.44,-86 845.44,-122"/>
<text text-anchor="middle" x="796" y="-99.8" font-family="Times,serif" font-size="14.00">ConfigModule</text>
</g>
<!-- CommonModule -->
<g id="node2" class="node">
<title>CommonModule</title>
<polygon fill="#8dd3c7" stroke="black" points="477.67,-187 474.67,-191 453.67,-191 450.67,-187 366.33,-187 366.33,-151 477.67,-151 477.67,-187"/>
<text text-anchor="middle" x="422" y="-164.8" font-family="Times,serif" font-size="14.00">CommonModule</text>
</g>
<!-- ConfigModule&#45;&gt;CommonModule -->
<g id="edge1" class="edge">
<title>ConfigModule&#45;&gt;CommonModule</title>
<path fill="none" stroke="black" d="M796,-122.27C796,-140.56 796,-166 796,-166 796,-166 487.99,-166 487.99,-166"/>
<polygon fill="black" stroke="black" points="487.99,-162.5 477.99,-166 487.99,-169.5 487.99,-162.5"/>
</g>
<!-- CUSTOM_LOGGER_TOKEN  -->
<g id="node3" class="node">
<title>CUSTOM_LOGGER_TOKEN </title>
<polygon fill="#fb8072" stroke="black" points="893.02,-252 696.98,-252 696.98,-216 893.02,-216 893.02,-252"/>
<text text-anchor="middle" x="795" y="-229.8" font-family="Times,serif" font-size="14.00">CUSTOM_LOGGER_TOKEN </text>
</g>
<!-- CommonModule&#45;&gt;CUSTOM_LOGGER_TOKEN  -->
<g id="edge2" class="edge">
<title>CommonModule&#45;&gt;CUSTOM_LOGGER_TOKEN </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M477.85,-173C561.56,-173 709.88,-173 709.88,-173 709.88,-173 709.88,-205.7 709.88,-205.7"/>
<polygon fill="black" stroke="black" points="706.38,-205.7 709.88,-215.7 713.38,-205.7 706.38,-205.7"/>
</g>
<!-- FieldMetadataService  -->
<g id="node4" class="node">
<title>FieldMetadataService </title>
<polygon fill="#fb8072" stroke="black" points="679.34,-252 536.66,-252 536.66,-216 679.34,-216 679.34,-252"/>
<text text-anchor="middle" x="608" y="-229.8" font-family="Times,serif" font-size="14.00">FieldMetadataService </text>
</g>
<!-- CommonModule&#45;&gt;FieldMetadataService  -->
<g id="edge3" class="edge">
<title>CommonModule&#45;&gt;FieldMetadataService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M477.93,-180C540.05,-180 631.72,-180 631.72,-180 631.72,-180 631.72,-205.72 631.72,-205.72"/>
<polygon fill="black" stroke="black" points="628.22,-205.72 631.72,-215.72 635.22,-205.72 628.22,-205.72"/>
</g>
<!-- SENTRY_SERVICE_TOKEN  -->
<g id="node5" class="node">
<title>SENTRY_SERVICE_TOKEN </title>
<polygon fill="#fb8072" stroke="black" points="518.69,-252 325.31,-252 325.31,-216 518.69,-216 518.69,-252"/>
<text text-anchor="middle" x="422" y="-229.8" font-family="Times,serif" font-size="14.00">SENTRY_SERVICE_TOKEN </text>
</g>
<!-- CommonModule&#45;&gt;SENTRY_SERVICE_TOKEN  -->
<g id="edge4" class="edge">
<title>CommonModule&#45;&gt;SENTRY_SERVICE_TOKEN </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M422,-187.11C422,-187.11 422,-205.99 422,-205.99"/>
<polygon fill="black" stroke="black" points="418.5,-205.99 422,-215.99 425.5,-205.99 418.5,-205.99"/>
</g>
<!-- ValidationService  -->
<g id="node6" class="node">
<title>ValidationService </title>
<polygon fill="#fb8072" stroke="black" points="307.08,-252 186.92,-252 186.92,-216 307.08,-216 307.08,-252"/>
<text text-anchor="middle" x="247" y="-229.8" font-family="Times,serif" font-size="14.00">ValidationService </text>
</g>
<!-- CommonModule&#45;&gt;ValidationService  -->
<g id="edge5" class="edge">
<title>CommonModule&#45;&gt;ValidationService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M366.23,-178C320.25,-178 262.55,-178 262.55,-178 262.55,-178 262.55,-205.97 262.55,-205.97"/>
<polygon fill="black" stroke="black" points="259.05,-205.97 262.55,-215.97 266.05,-205.97 259.05,-205.97"/>
</g>
<!-- WorkflowsGrpcClient  -->
<g id="node7" class="node">
<title>WorkflowsGrpcClient </title>
<polygon fill="#fb8072" stroke="black" points="168.18,-252 23.82,-252 23.82,-216 168.18,-216 168.18,-252"/>
<text text-anchor="middle" x="96" y="-229.8" font-family="Times,serif" font-size="14.00">WorkflowsGrpcClient </text>
</g>
<!-- CommonModule&#45;&gt;WorkflowsGrpcClient  -->
<g id="edge6" class="edge">
<title>CommonModule&#45;&gt;WorkflowsGrpcClient </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M366.1,-169C273.63,-169 98.94,-169 98.94,-169 98.94,-169 98.94,-205.89 98.94,-205.89"/>
<polygon fill="black" stroke="black" points="95.44,-205.89 98.94,-215.89 102.44,-205.89 95.44,-205.89"/>
</g>
<!-- FieldMetadataService -->
<g id="node8" class="node">
<title>FieldMetadataService</title>
<ellipse fill="#fdb462" stroke="black" cx="627" cy="-104" rx="95.54" ry="18"/>
<text text-anchor="middle" x="627" y="-99.8" font-family="Times,serif" font-size="14.00">FieldMetadataService</text>
</g>
<!-- FieldMetadataService&#45;&gt;CommonModule -->
<g id="edge7" class="edge">
<title>FieldMetadataService&#45;&gt;CommonModule</title>
<path fill="none" stroke="black" d="M584.28,-120.2C584.28,-136.43 584.28,-159 584.28,-159 584.28,-159 487.85,-159 487.85,-159"/>
<polygon fill="black" stroke="black" points="487.85,-155.5 477.85,-159 487.85,-162.5 487.85,-155.5"/>
</g>
<!-- WorkflowsGrpcClient -->
<g id="node9" class="node">
<title>WorkflowsGrpcClient</title>
<ellipse fill="#fdb462" stroke="black" cx="416" cy="-104" rx="97.27" ry="18"/>
<text text-anchor="middle" x="416" y="-99.8" font-family="Times,serif" font-size="14.00">WorkflowsGrpcClient</text>
</g>
<!-- WorkflowsGrpcClient&#45;&gt;CommonModule -->
<g id="edge8" class="edge">
<title>WorkflowsGrpcClient&#45;&gt;CommonModule</title>
<path fill="none" stroke="black" d="M422,-122.11C422,-122.11 422,-140.99 422,-140.99"/>
<polygon fill="black" stroke="black" points="418.5,-140.99 422,-150.99 425.5,-140.99 418.5,-140.99"/>
</g>
<!-- WorkflowsRegistrySyncService -->
<g id="node10" class="node">
<title>WorkflowsRegistrySyncService</title>
<ellipse fill="#fdb462" stroke="black" cx="165" cy="-104" rx="135.43" ry="18"/>
<text text-anchor="middle" x="165" y="-99.8" font-family="Times,serif" font-size="14.00">WorkflowsRegistrySyncService</text>
</g>
<!-- WorkflowsRegistrySyncService&#45;&gt;CommonModule -->
<g id="edge9" class="edge">
<title>WorkflowsRegistrySyncService&#45;&gt;CommonModule</title>
<path fill="none" stroke="black" d="M224.63,-120.2C224.63,-136.74 224.63,-160 224.63,-160 224.63,-160 356.23,-160 356.23,-160"/>
<polygon fill="black" stroke="black" points="356.23,-163.5 366.23,-160 356.23,-156.5 356.23,-163.5"/>
</g>
</g>
</svg>

    </div>
    <i id="fullscreen" class="icon ion-ios-resize module-graph-fullscreen-btn" aria-hidden="true"></i>
    <div class="btn-group size-buttons">
        <button id="zoom-in" class="btn btn-default btn-sm">Zoom in</button>
        <button id="reset" class="btn btn-default btn-sm">Reset</button>
        <button id="zoom-out" class="btn btn-default btn-sm">Zoom out</button>
    </div>
</div>
<script src="../js/libs/svg-pan-zoom.min.js"></script>
<script src="../js/svg-pan-zoom.controls.js"></script>

<ul class="nav nav-tabs" role="tablist">
    <li class="nav-item">
        <a href="#info" 
            class="nav-link"
            class="nav-link active"
            role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
    </li>
    <li class="nav-item">
        <a href="#source" 
            class="nav-link"
            
            role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
    </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">

        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/common/common.module.ts</code>
        </p>





        <div class="container-fluid module">
            <div class="row">
                <div class="col-sm-3">
                    <h3>Providers<a href="https://angular.io/api/core/NgModule#providers" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module providers"><span class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../injectables/FieldMetadataService.html">FieldMetadataService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/WorkflowsGrpcClient.html">WorkflowsGrpcClient</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/WorkflowsRegistrySyncService.html">WorkflowsRegistrySyncService</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Imports<a href="https://angular.io/api/core/NgModule#imports" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module imports"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../modules/ConfigModule.html">ConfigModule</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Exports<a href="https://angular.io/api/core/NgModule#exports" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module exports"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../s/CUSTOM_LOGGER_TOKEN.html">CUSTOM_LOGGER_TOKEN</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/FieldMetadataService.html">FieldMetadataService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/SENTRY_SERVICE_TOKEN.html">SENTRY_SERVICE_TOKEN</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/ValidationService.html">ValidationService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../s/WorkflowsGrpcClient.html">WorkflowsGrpcClient</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>


    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Module } from &quot;@nestjs/common&quot;;
import {
  SENTRY_SERVICE_TOKEN,
  SentryService,
} from &quot;@repo/nestjs-commons/filters&quot;;
import {
  CUSTOM_LOGGER_TOKEN,
  ILogger,
  PinoLoggerService,
} from &quot;@repo/nestjs-commons/logger&quot;;
import { ConfigModule } from &quot;../config/config.module&quot;;
import { ConfigKeys, ConfigService } from &quot;../config/config.service&quot;;
import { WorkflowsGrpcClient } from &quot;./grpc/workflows.client.grpc&quot;;
import { FieldMetadataService } from &quot;./services/field-metadata.service&quot;;
import { ValidationService } from &quot;./services/validation.service&quot;;
import { WorkflowsRegistrySyncService } from &quot;./workflows/registry-sync.service&quot;;

@Module({
  imports: [ConfigModule],
  providers: [
    // gRPC Clients
    WorkflowsGrpcClient,

    // Services
    FieldMetadataService,
    WorkflowsRegistrySyncService,

    // System services
    {
      provide: CUSTOM_LOGGER_TOKEN,
      useFactory: (configService: ConfigService) &#x3D;&gt; {
        const appTag &#x3D; configService.get(ConfigKeys.APP_TAG);
        const serviceTag &#x3D; configService.get(ConfigKeys.SERVICE_TAG);
        return new PinoLoggerService(appTag, serviceTag);
      },
      inject: [ConfigService],
    },
    {
      provide: &quot;ValidationService&quot;,
      useClass: ValidationService,
    },
    {
      provide: SENTRY_SERVICE_TOKEN,
      useFactory: (configService: ConfigService, logger: ILogger) &#x3D;&gt; {
        const sentryDsn &#x3D; configService.get(ConfigKeys.SENTRY_DSN);
        const appEnv &#x3D; configService.get(ConfigKeys.NODE_ENV);
        const appTag &#x3D; configService.get(ConfigKeys.APP_TAG);
        const serviceTag &#x3D; configService.get(ConfigKeys.SERVICE_TAG);
        return new SentryService(logger, sentryDsn, appEnv, appTag, serviceTag);
      },
      inject: [ConfigService, CUSTOM_LOGGER_TOKEN],
    },
  ],
  exports: [
    CUSTOM_LOGGER_TOKEN,
    &quot;ValidationService&quot;,
    SENTRY_SERVICE_TOKEN,

    // gRPC Clients
    WorkflowsGrpcClient,

    // Services
    FieldMetadataService,
  ],
})
export class CommonModule {}
</code></pre>
    </div>
</div>

















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'module';
            var COMPODOC_CURRENT_PAGE_URL = 'CommonModule.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
