<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: dependencies Pages: 1 -->
<svg width="504pt" height="284pt"
 viewBox="0.00 0.00 504.00 284.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 280)">
<title>dependencies</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-280 500,-280 500,4 -4,4"/>
<text text-anchor="start" x="227.01" y="-42.4" font-family="Times-12" font-weight="bold" font-size="14.00">Legend</text>
<polygon fill="#ffffb3" stroke="transparent" points="14,-10 14,-30 34,-30 34,-10 14,-10"/>
<text text-anchor="start" x="37.63" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Declarations</text>
<polygon fill="#8dd3c7" stroke="transparent" points="127,-10 127,-30 147,-30 147,-10 127,-10"/>
<text text-anchor="start" x="150.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Module</text>
<polygon fill="#80b1d3" stroke="transparent" points="213,-10 213,-30 233,-30 233,-10 213,-10"/>
<text text-anchor="start" x="236.78" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Bootstrap</text>
<polygon fill="#fdb462" stroke="transparent" points="310,-10 310,-30 330,-30 330,-10 310,-10"/>
<text text-anchor="start" x="333.67" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Providers</text>
<polygon fill="#fb8072" stroke="transparent" points="406,-10 406,-30 426,-30 426,-10 406,-10"/>
<text text-anchor="start" x="429.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Exports</text>
<g id="clust1" class="cluster">
<title>cluster_SwaggerModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="158,-70 158,-268 338,-268 338,-70 158,-70"/>
</g>
<g id="clust4" class="cluster">
<title>cluster_SwaggerModule_exports</title>
<polygon fill="none" stroke="black" points="185,-208 185,-260 311,-260 311,-208 185,-208"/>
</g>
<g id="clust6" class="cluster">
<title>cluster_SwaggerModule_providers</title>
<polygon fill="none" stroke="black" points="166,-78 166,-130 330,-130 330,-78 166,-78"/>
</g>
<!-- SwaggerService  -->
<g id="node1" class="node">
<title>SwaggerService </title>
<polygon fill="#fb8072" stroke="black" points="303.46,-252 192.54,-252 192.54,-216 303.46,-216 303.46,-252"/>
<text text-anchor="middle" x="248" y="-229.8" font-family="Times,serif" font-size="14.00">SwaggerService </text>
</g>
<!-- SwaggerModule -->
<g id="node2" class="node">
<title>SwaggerModule</title>
<polygon fill="#8dd3c7" stroke="black" points="302.03,-187 299.03,-191 278.03,-191 275.03,-187 193.97,-187 193.97,-151 302.03,-151 302.03,-187"/>
<text text-anchor="middle" x="248" y="-164.8" font-family="Times,serif" font-size="14.00">SwaggerModule</text>
</g>
<!-- SwaggerModule&#45;&gt;SwaggerService  -->
<g id="edge1" class="edge">
<title>SwaggerModule&#45;&gt;SwaggerService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M248,-187.11C248,-187.11 248,-205.99 248,-205.99"/>
<polygon fill="black" stroke="black" points="244.5,-205.99 248,-215.99 251.5,-205.99 244.5,-205.99"/>
</g>
<!-- SwaggerService -->
<g id="node3" class="node">
<title>SwaggerService</title>
<ellipse fill="#fdb462" stroke="black" cx="248" cy="-104" rx="73.55" ry="18"/>
<text text-anchor="middle" x="248" y="-99.8" font-family="Times,serif" font-size="14.00">SwaggerService</text>
</g>
<!-- SwaggerService&#45;&gt;SwaggerModule -->
<g id="edge2" class="edge">
<title>SwaggerService&#45;&gt;SwaggerModule</title>
<path fill="none" stroke="black" d="M248,-122.11C248,-122.11 248,-140.99 248,-140.99"/>
<polygon fill="black" stroke="black" points="244.5,-140.99 248,-150.99 251.5,-140.99 244.5,-140.99"/>
</g>
</g>
</svg>
