<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content module">
                   <div class="content-data">



<ol class="breadcrumb">
    <li class="breadcrumb-item">Modules</li>
    <li class="breadcrumb-item" >StorageModule</li>
</ol>

<div class="text-center module-graph-container">
    <div id="module-graph-svg">
        <?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: dependencies Pages: 1 -->
<svg width="526pt" height="284pt"
 viewBox="0.00 0.00 526.00 284.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 280)">
<title>dependencies</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-280 522,-280 522,4 -4,4"/>
<text text-anchor="start" x="238.01" y="-42.4" font-family="Times-12" font-weight="bold" font-size="14.00">Legend</text>
<polygon fill="#ffffb3" stroke="transparent" points="25,-10 25,-30 45,-30 45,-10 25,-10"/>
<text text-anchor="start" x="48.63" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Declarations</text>
<polygon fill="#8dd3c7" stroke="transparent" points="138,-10 138,-30 158,-30 158,-10 138,-10"/>
<text text-anchor="start" x="161.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Module</text>
<polygon fill="#80b1d3" stroke="transparent" points="224,-10 224,-30 244,-30 244,-10 224,-10"/>
<text text-anchor="start" x="247.78" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Bootstrap</text>
<polygon fill="#fdb462" stroke="transparent" points="321,-10 321,-30 341,-30 341,-10 321,-10"/>
<text text-anchor="start" x="344.67" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Providers</text>
<polygon fill="#fb8072" stroke="transparent" points="417,-10 417,-30 437,-30 437,-10 417,-10"/>
<text text-anchor="start" x="440.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Exports</text>
<g id="clust1" class="cluster">
<title>cluster_StorageModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="8,-70 8,-268 510,-268 510,-70 8,-70"/>
</g>
<g id="clust6" class="cluster">
<title>cluster_StorageModule_providers</title>
<polygon fill="none" stroke="black" points="138,-78 138,-130 502,-130 502,-78 138,-78"/>
</g>
<g id="clust4" class="cluster">
<title>cluster_StorageModule_exports</title>
<polygon fill="none" stroke="black" points="155,-208 155,-260 275,-260 275,-208 155,-208"/>
</g>
<g id="clust3" class="cluster">
<title>cluster_StorageModule_imports</title>
<polygon fill="none" stroke="black" points="16,-78 16,-130 130,-130 130,-78 16,-78"/>
</g>
<!-- ConfigModule -->
<g id="node1" class="node">
<title>ConfigModule</title>
<polygon fill="#8dd3c7" stroke="black" points="122.44,-122 119.44,-126 98.44,-126 95.44,-122 23.56,-122 23.56,-86 122.44,-86 122.44,-122"/>
<text text-anchor="middle" x="73" y="-99.8" font-family="Times,serif" font-size="14.00">ConfigModule</text>
</g>
<!-- StorageModule -->
<g id="node2" class="node">
<title>StorageModule</title>
<polygon fill="#8dd3c7" stroke="black" points="266.31,-187 263.31,-191 242.31,-191 239.31,-187 163.69,-187 163.69,-151 266.31,-151 266.31,-187"/>
<text text-anchor="middle" x="215" y="-164.8" font-family="Times,serif" font-size="14.00">StorageModule</text>
</g>
<!-- ConfigModule&#45;&gt;StorageModule -->
<g id="edge1" class="edge">
<title>ConfigModule&#45;&gt;StorageModule</title>
<path fill="none" stroke="black" d="M73,-122.11C73,-141.34 73,-169 73,-169 73,-169 153.73,-169 153.73,-169"/>
<polygon fill="black" stroke="black" points="153.73,-172.5 163.73,-169 153.73,-165.5 153.73,-172.5"/>
</g>
<!-- StorageService  -->
<g id="node3" class="node">
<title>StorageService </title>
<polygon fill="#fb8072" stroke="black" points="267.24,-252 162.76,-252 162.76,-216 267.24,-216 267.24,-252"/>
<text text-anchor="middle" x="215" y="-229.8" font-family="Times,serif" font-size="14.00">StorageService </text>
</g>
<!-- StorageModule&#45;&gt;StorageService  -->
<g id="edge2" class="edge">
<title>StorageModule&#45;&gt;StorageService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M215,-187.11C215,-187.11 215,-205.99 215,-205.99"/>
<polygon fill="black" stroke="black" points="211.5,-205.99 215,-215.99 218.5,-205.99 211.5,-205.99"/>
</g>
<!-- LocalStorageProvider -->
<g id="node4" class="node">
<title>LocalStorageProvider</title>
<ellipse fill="#fdb462" stroke="black" cx="398" cy="-104" rx="95.55" ry="18"/>
<text text-anchor="middle" x="398" y="-99.8" font-family="Times,serif" font-size="14.00">LocalStorageProvider</text>
</g>
<!-- LocalStorageProvider&#45;&gt;StorageModule -->
<g id="edge3" class="edge">
<title>LocalStorageProvider&#45;&gt;StorageModule</title>
<path fill="none" stroke="black" d="M398,-122.11C398,-141.34 398,-169 398,-169 398,-169 276.46,-169 276.46,-169"/>
<polygon fill="black" stroke="black" points="276.46,-165.5 266.46,-169 276.46,-172.5 276.46,-165.5"/>
</g>
<!-- StorageService -->
<g id="node5" class="node">
<title>StorageService</title>
<ellipse fill="#fdb462" stroke="black" cx="215" cy="-104" rx="69.48" ry="18"/>
<text text-anchor="middle" x="215" y="-99.8" font-family="Times,serif" font-size="14.00">StorageService</text>
</g>
<!-- StorageService&#45;&gt;StorageModule -->
<g id="edge4" class="edge">
<title>StorageService&#45;&gt;StorageModule</title>
<path fill="none" stroke="black" d="M215,-122.11C215,-122.11 215,-140.99 215,-140.99"/>
<polygon fill="black" stroke="black" points="211.5,-140.99 215,-150.99 218.5,-140.99 211.5,-140.99"/>
</g>
</g>
</svg>

    </div>
    <i id="fullscreen" class="icon ion-ios-resize module-graph-fullscreen-btn" aria-hidden="true"></i>
    <div class="btn-group size-buttons">
        <button id="zoom-in" class="btn btn-default btn-sm">Zoom in</button>
        <button id="reset" class="btn btn-default btn-sm">Reset</button>
        <button id="zoom-out" class="btn btn-default btn-sm">Zoom out</button>
    </div>
</div>
<script src="../js/libs/svg-pan-zoom.min.js"></script>
<script src="../js/svg-pan-zoom.controls.js"></script>

<ul class="nav nav-tabs" role="tablist">
    <li class="nav-item">
        <a href="#info" 
            class="nav-link"
            class="nav-link active"
            role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
    </li>
    <li class="nav-item">
        <a href="#source" 
            class="nav-link"
            
            role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
    </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">

        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/storage/module/storage.module.ts</code>
        </p>





        <div class="container-fluid module">
            <div class="row">
                <div class="col-sm-3">
                    <h3>Providers<a href="https://angular.io/api/core/NgModule#providers" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module providers"><span class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../injectables/LocalStorageProvider.html">LocalStorageProvider</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/StorageService.html">StorageService</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Controllers<a href="https://docs.nestjs.com/controllers" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module controllers"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../controllers/StorageController.html">StorageController</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Imports<a href="https://angular.io/api/core/NgModule#imports" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module imports"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../modules/ConfigModule.html">ConfigModule</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Exports<a href="https://angular.io/api/core/NgModule#exports" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module exports"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../injectables/StorageService.html">StorageService</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>


    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Module } from &quot;@nestjs/common&quot;;
import { TypeOrmModule } from &quot;@nestjs/typeorm&quot;;
import { Storage, StorageRepository } from &quot;@repo/thena-platform-entities&quot;;
import { ConfigModule } from &quot;../../config/config.module&quot;;
import { ConfigKeys, ConfigService } from &quot;../../config/config.service&quot;;
import { StorageController } from &quot;../controller/storage.controller&quot;;
import { LocalStorageProvider } from &quot;../providers/local-storage.provider&quot;;
import { S3StorageProvider } from &quot;../providers/s3-storage.provider&quot;;
import { StorageService } from &quot;../services/storage-service&quot;;

@Module({
  imports: [ConfigModule, TypeOrmModule.forFeature([Storage])],
  controllers: [StorageController],
  providers: [
    StorageService,
    LocalStorageProvider,
    StorageRepository,
    {
      provide: &quot;S3_STORAGE_PROVIDER&quot;,
      useFactory: (
        configService: ConfigService,
        storageRepository: StorageRepository,
      ) &#x3D;&gt; {
        return new S3StorageProvider(
          {
            accessKeyId: configService.get(ConfigKeys.AWS_ACCESS_KEY),
            secretAccessKey: configService.get(ConfigKeys.AWS_SECRET_KEY),
          },
          configService.get(ConfigKeys.AWS_REGION),
          storageRepository,
        );
      },
      inject: [ConfigService, StorageRepository],
    },
    {
      provide: &quot;STORAGE_CONFIG&quot;,
      useFactory: (configService: ConfigService) &#x3D;&gt; ({
        local: {
          basePath: &quot;./data/storage&quot;,
        },
        s3: {
          defaultBucket: configService.get(ConfigKeys.TICKET_BUCKET),
        },
      }),
      inject: [ConfigService],
    },
  ],
  exports: [StorageService],
})
export class StorageModule {}
</code></pre>
    </div>
</div>

















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'module';
            var COMPODOC_CURRENT_PAGE_URL = 'StorageModule.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
