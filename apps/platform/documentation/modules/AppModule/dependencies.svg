<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: dependencies Pages: 1 -->
<svg width="2672pt" height="211pt"
 viewBox="0.00 0.00 2672.00 211.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 207)">
<title>dependencies</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-207 2668,-207 2668,4 -4,4"/>
<text text-anchor="start" x="1311.01" y="-42.4" font-family="Times-12" font-weight="bold" font-size="14.00">Legend</text>
<polygon fill="#ffffb3" stroke="transparent" points="1098,-10 1098,-30 1118,-30 1118,-10 1098,-10"/>
<text text-anchor="start" x="1121.63" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Declarations</text>
<polygon fill="#8dd3c7" stroke="transparent" points="1211,-10 1211,-30 1231,-30 1231,-10 1211,-10"/>
<text text-anchor="start" x="1234.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Module</text>
<polygon fill="#80b1d3" stroke="transparent" points="1297,-10 1297,-30 1317,-30 1317,-10 1297,-10"/>
<text text-anchor="start" x="1320.78" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Bootstrap</text>
<polygon fill="#fdb462" stroke="transparent" points="1394,-10 1394,-30 1414,-30 1414,-10 1394,-10"/>
<text text-anchor="start" x="1417.67" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Providers</text>
<polygon fill="#fb8072" stroke="transparent" points="1490,-10 1490,-30 1510,-30 1510,-10 1490,-10"/>
<text text-anchor="start" x="1513.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Exports</text>
<g id="clust1" class="cluster">
<title>cluster_AppModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="8,-70 8,-195 2656,-195 2656,-70 8,-70"/>
</g>
<g id="clust3" class="cluster">
<title>cluster_AppModule_imports</title>
<polygon fill="none" stroke="black" points="16,-78 16,-130 2648,-130 2648,-78 16,-78"/>
</g>
<!-- AccountsModule -->
<g id="node1" class="node">
<title>AccountsModule</title>
<polygon fill="#8dd3c7" stroke="black" points="2640.42,-122 2637.42,-126 2616.42,-126 2613.42,-122 2527.58,-122 2527.58,-86 2640.42,-86 2640.42,-122"/>
<text text-anchor="middle" x="2584" y="-99.8" font-family="Times,serif" font-size="14.00">AccountsModule</text>
</g>
<!-- AppModule -->
<g id="node22" class="node">
<title>AppModule</title>
<polygon fill="#8dd3c7" stroke="black" points="1288.66,-187 1285.66,-191 1264.66,-191 1261.66,-187 1205.34,-187 1205.34,-151 1288.66,-151 1288.66,-187"/>
<text text-anchor="middle" x="1247" y="-164.8" font-family="Times,serif" font-size="14.00">AppModule</text>
</g>
<!-- AccountsModule&#45;&gt;AppModule -->
<g id="edge1" class="edge">
<title>AccountsModule&#45;&gt;AppModule</title>
<path fill="none" stroke="black" d="M2584,-122.15C2584,-145.76 2584,-184 2584,-184 2584,-184 1298.85,-184 1298.85,-184"/>
<polygon fill="black" stroke="black" points="1298.85,-180.5 1288.85,-184 1298.85,-187.5 1298.85,-180.5"/>
</g>
<!-- ActivitiesModule -->
<g id="node2" class="node">
<title>ActivitiesModule</title>
<polygon fill="#8dd3c7" stroke="black" points="2509.98,-122 2506.98,-126 2485.98,-126 2482.98,-122 2396.02,-122 2396.02,-86 2509.98,-86 2509.98,-122"/>
<text text-anchor="middle" x="2453" y="-99.8" font-family="Times,serif" font-size="14.00">ActivitiesModule</text>
</g>
<!-- ActivitiesModule&#45;&gt;AppModule -->
<g id="edge2" class="edge">
<title>ActivitiesModule&#45;&gt;AppModule</title>
<path fill="none" stroke="black" d="M2453,-122.25C2453,-145.02 2453,-181 2453,-181 2453,-181 1298.86,-181 1298.86,-181"/>
<polygon fill="black" stroke="black" points="1298.86,-177.5 1288.86,-181 1298.86,-184.5 1298.86,-177.5"/>
</g>
<!-- AuthModule -->
<g id="node3" class="node">
<title>AuthModule</title>
<polygon fill="#8dd3c7" stroke="black" points="2377.55,-122 2374.55,-126 2353.55,-126 2350.55,-122 2290.45,-122 2290.45,-86 2377.55,-86 2377.55,-122"/>
<text text-anchor="middle" x="2334" y="-99.8" font-family="Times,serif" font-size="14.00">AuthModule</text>
</g>
<!-- AuthModule&#45;&gt;AppModule -->
<g id="edge3" class="edge">
<title>AuthModule&#45;&gt;AppModule</title>
<path fill="none" stroke="black" d="M2334,-122.29C2334,-144.21 2334,-178 2334,-178 2334,-178 1298.95,-178 1298.95,-178"/>
<polygon fill="black" stroke="black" points="1298.95,-174.5 1288.95,-178 1298.95,-181.5 1298.95,-174.5"/>
</g>
<!-- BullBoardModule -->
<g id="node4" class="node">
<title>BullBoardModule</title>
<polygon fill="#8dd3c7" stroke="black" points="2271.88,-122 2268.88,-126 2247.88,-126 2244.88,-122 2154.12,-122 2154.12,-86 2271.88,-86 2271.88,-122"/>
<text text-anchor="middle" x="2213" y="-99.8" font-family="Times,serif" font-size="14.00">BullBoardModule</text>
</g>
<!-- BullBoardModule&#45;&gt;AppModule -->
<g id="edge4" class="edge">
<title>BullBoardModule&#45;&gt;AppModule</title>
<path fill="none" stroke="black" d="M2213,-122.03C2213,-142.77 2213,-174 2213,-174 2213,-174 1298.82,-174 1298.82,-174"/>
<polygon fill="black" stroke="black" points="1298.82,-170.5 1288.82,-174 1298.82,-177.5 1298.82,-170.5"/>
</g>
<!-- CommonModule -->
<g id="node5" class="node">
<title>CommonModule</title>
<polygon fill="#8dd3c7" stroke="black" points="2135.67,-122 2132.67,-126 2111.67,-126 2108.67,-122 2024.33,-122 2024.33,-86 2135.67,-86 2135.67,-122"/>
<text text-anchor="middle" x="2080" y="-99.8" font-family="Times,serif" font-size="14.00">CommonModule</text>
</g>
<!-- CommonModule&#45;&gt;AppModule -->
<g id="edge5" class="edge">
<title>CommonModule&#45;&gt;AppModule</title>
<path fill="none" stroke="black" d="M2080,-122.31C2080,-142.15 2080,-171 2080,-171 2080,-171 1298.88,-171 1298.88,-171"/>
<polygon fill="black" stroke="black" points="1298.88,-167.5 1288.88,-171 1298.88,-174.5 1298.88,-167.5"/>
</g>
<!-- CommunicationsModule -->
<g id="node6" class="node">
<title>CommunicationsModule</title>
<polygon fill="#8dd3c7" stroke="black" points="2006.71,-122 2003.71,-126 1982.71,-126 1979.71,-122 1851.29,-122 1851.29,-86 2006.71,-86 2006.71,-122"/>
<text text-anchor="middle" x="1929" y="-99.8" font-family="Times,serif" font-size="14.00">CommunicationsModule</text>
</g>
<!-- CommunicationsModule&#45;&gt;AppModule -->
<g id="edge6" class="edge">
<title>CommunicationsModule&#45;&gt;AppModule</title>
<path fill="none" stroke="black" d="M1929,-122.17C1929,-141.09 1929,-168 1929,-168 1929,-168 1298.89,-168 1298.89,-168"/>
<polygon fill="black" stroke="black" points="1298.89,-164.5 1288.89,-168 1298.89,-171.5 1298.89,-164.5"/>
</g>
<!-- ConfigModule -->
<g id="node7" class="node">
<title>ConfigModule</title>
<polygon fill="#8dd3c7" stroke="black" points="1833.44,-122 1830.44,-126 1809.44,-126 1806.44,-122 1734.56,-122 1734.56,-86 1833.44,-86 1833.44,-122"/>
<text text-anchor="middle" x="1784" y="-99.8" font-family="Times,serif" font-size="14.00">ConfigModule</text>
</g>
<!-- ConfigModule&#45;&gt;AppModule -->
<g id="edge7" class="edge">
<title>ConfigModule&#45;&gt;AppModule</title>
<path fill="none" stroke="black" d="M1784,-122.3C1784,-140.27 1784,-165 1784,-165 1784,-165 1298.69,-165 1298.69,-165"/>
<polygon fill="black" stroke="black" points="1298.69,-161.5 1288.69,-165 1298.69,-168.5 1298.69,-161.5"/>
</g>
<!-- CustomFieldModule -->
<g id="node8" class="node">
<title>CustomFieldModule</title>
<polygon fill="#8dd3c7" stroke="black" points="1716.89,-122 1713.89,-126 1692.89,-126 1689.89,-122 1585.11,-122 1585.11,-86 1716.89,-86 1716.89,-122"/>
<text text-anchor="middle" x="1651" y="-99.8" font-family="Times,serif" font-size="14.00">CustomFieldModule</text>
</g>
<!-- CustomFieldModule&#45;&gt;AppModule -->
<g id="edge8" class="edge">
<title>CustomFieldModule&#45;&gt;AppModule</title>
<path fill="none" stroke="black" d="M1651,-122.04C1651,-138.73 1651,-161 1651,-161 1651,-161 1298.71,-161 1298.71,-161"/>
<polygon fill="black" stroke="black" points="1298.71,-157.5 1288.71,-161 1298.71,-164.5 1298.71,-157.5"/>
</g>
<!-- CustomObjectModule -->
<g id="node9" class="node">
<title>CustomObjectModule</title>
<polygon fill="#8dd3c7" stroke="black" points="1567.43,-122 1564.43,-126 1543.43,-126 1540.43,-122 1426.57,-122 1426.57,-86 1567.43,-86 1567.43,-122"/>
<text text-anchor="middle" x="1497" y="-99.8" font-family="Times,serif" font-size="14.00">CustomObjectModule</text>
</g>
<!-- CustomObjectModule&#45;&gt;AppModule -->
<g id="edge9" class="edge">
<title>CustomObjectModule&#45;&gt;AppModule</title>
<path fill="none" stroke="black" d="M1497,-122.28C1497,-137.95 1497,-158 1497,-158 1497,-158 1298.84,-158 1298.84,-158"/>
<polygon fill="black" stroke="black" points="1298.84,-154.5 1288.84,-158 1298.84,-161.5 1298.84,-154.5"/>
</g>
<!-- FormsModule -->
<g id="node10" class="node">
<title>FormsModule</title>
<polygon fill="#8dd3c7" stroke="black" points="1408.83,-122 1405.83,-126 1384.83,-126 1381.83,-122 1313.17,-122 1313.17,-86 1408.83,-86 1408.83,-122"/>
<text text-anchor="middle" x="1361" y="-99.8" font-family="Times,serif" font-size="14.00">FormsModule</text>
</g>
<!-- FormsModule&#45;&gt;AppModule -->
<g id="edge10" class="edge">
<title>FormsModule&#45;&gt;AppModule</title>
<path fill="none" stroke="black" d="M1361,-122.12C1361,-136.78 1361,-155 1361,-155 1361,-155 1298.8,-155 1298.8,-155"/>
<polygon fill="black" stroke="black" points="1298.8,-151.5 1288.8,-155 1298.8,-158.5 1298.8,-151.5"/>
</g>
<!-- HealthModule -->
<g id="node11" class="node">
<title>HealthModule</title>
<polygon fill="#8dd3c7" stroke="black" points="1295.37,-122 1292.37,-126 1271.37,-126 1268.37,-122 1198.63,-122 1198.63,-86 1295.37,-86 1295.37,-122"/>
<text text-anchor="middle" x="1247" y="-99.8" font-family="Times,serif" font-size="14.00">HealthModule</text>
</g>
<!-- HealthModule&#45;&gt;AppModule -->
<g id="edge11" class="edge">
<title>HealthModule&#45;&gt;AppModule</title>
<path fill="none" stroke="black" d="M1247,-122.11C1247,-122.11 1247,-140.99 1247,-140.99"/>
<polygon fill="black" stroke="black" points="1243.5,-140.99 1247,-150.99 1250.5,-140.99 1243.5,-140.99"/>
</g>
<!-- OrganizationModule -->
<g id="node12" class="node">
<title>OrganizationModule</title>
<polygon fill="#8dd3c7" stroke="black" points="1180.13,-122 1177.13,-126 1156.13,-126 1153.13,-122 1047.87,-122 1047.87,-86 1180.13,-86 1180.13,-122"/>
<text text-anchor="middle" x="1114" y="-99.8" font-family="Times,serif" font-size="14.00">OrganizationModule</text>
</g>
<!-- OrganizationModule&#45;&gt;AppModule -->
<g id="edge12" class="edge">
<title>OrganizationModule&#45;&gt;AppModule</title>
<path fill="none" stroke="black" d="M1114,-122.12C1114,-136.78 1114,-155 1114,-155 1114,-155 1195.28,-155 1195.28,-155"/>
<polygon fill="black" stroke="black" points="1195.28,-158.5 1205.28,-155 1195.28,-151.5 1195.28,-158.5"/>
</g>
<!-- SharedModule -->
<g id="node13" class="node">
<title>SharedModule</title>
<polygon fill="#8dd3c7" stroke="black" points="1029.42,-122 1026.42,-126 1005.42,-126 1002.42,-122 930.58,-122 930.58,-86 1029.42,-86 1029.42,-122"/>
<text text-anchor="middle" x="980" y="-99.8" font-family="Times,serif" font-size="14.00">SharedModule</text>
</g>
<!-- SharedModule&#45;&gt;AppModule -->
<g id="edge13" class="edge">
<title>SharedModule&#45;&gt;AppModule</title>
<path fill="none" stroke="black" d="M980,-122.28C980,-137.95 980,-158 980,-158 980,-158 1195.28,-158 1195.28,-158"/>
<polygon fill="black" stroke="black" points="1195.28,-161.5 1205.28,-158 1195.28,-154.5 1195.28,-161.5"/>
</g>
<!-- StorageModule -->
<g id="node14" class="node">
<title>StorageModule</title>
<polygon fill="#8dd3c7" stroke="black" points="913.31,-122 910.31,-126 889.31,-126 886.31,-122 810.69,-122 810.69,-86 913.31,-86 913.31,-122"/>
<text text-anchor="middle" x="862" y="-99.8" font-family="Times,serif" font-size="14.00">StorageModule</text>
</g>
<!-- StorageModule&#45;&gt;AppModule -->
<g id="edge14" class="edge">
<title>StorageModule&#45;&gt;AppModule</title>
<path fill="none" stroke="black" d="M862,-122.04C862,-138.73 862,-161 862,-161 862,-161 1195.07,-161 1195.07,-161"/>
<polygon fill="black" stroke="black" points="1195.07,-164.5 1205.07,-161 1195.07,-157.5 1195.07,-164.5"/>
</g>
<!-- SwaggerModule -->
<g id="node15" class="node">
<title>SwaggerModule</title>
<polygon fill="#8dd3c7" stroke="black" points="793.03,-122 790.03,-126 769.03,-126 766.03,-122 684.97,-122 684.97,-86 793.03,-86 793.03,-122"/>
<text text-anchor="middle" x="739" y="-99.8" font-family="Times,serif" font-size="14.00">SwaggerModule</text>
</g>
<!-- SwaggerModule&#45;&gt;AppModule -->
<g id="edge15" class="edge">
<title>SwaggerModule&#45;&gt;AppModule</title>
<path fill="none" stroke="black" d="M739,-122.3C739,-140.27 739,-165 739,-165 739,-165 1195,-165 1195,-165"/>
<polygon fill="black" stroke="black" points="1195,-168.5 1205,-165 1195,-161.5 1195,-168.5"/>
</g>
<!-- TagModule -->
<g id="node16" class="node">
<title>TagModule</title>
<polygon fill="#8dd3c7" stroke="black" points="666.82,-122 663.82,-126 642.82,-126 639.82,-122 585.18,-122 585.18,-86 666.82,-86 666.82,-122"/>
<text text-anchor="middle" x="626" y="-99.8" font-family="Times,serif" font-size="14.00">TagModule</text>
</g>
<!-- TagModule&#45;&gt;AppModule -->
<g id="edge16" class="edge">
<title>TagModule&#45;&gt;AppModule</title>
<path fill="none" stroke="black" d="M626,-122.17C626,-141.09 626,-168 626,-168 626,-168 1195.29,-168 1195.29,-168"/>
<polygon fill="black" stroke="black" points="1195.29,-171.5 1205.29,-168 1195.29,-164.5 1195.29,-171.5"/>
</g>
<!-- TeamsModule -->
<g id="node17" class="node">
<title>TeamsModule</title>
<polygon fill="#8dd3c7" stroke="black" points="567.37,-122 564.37,-126 543.37,-126 540.37,-122 470.63,-122 470.63,-86 567.37,-86 567.37,-122"/>
<text text-anchor="middle" x="519" y="-99.8" font-family="Times,serif" font-size="14.00">TeamsModule</text>
</g>
<!-- TeamsModule&#45;&gt;AppModule -->
<g id="edge17" class="edge">
<title>TeamsModule&#45;&gt;AppModule</title>
<path fill="none" stroke="black" d="M519,-122.31C519,-142.15 519,-171 519,-171 519,-171 1195.24,-171 1195.24,-171"/>
<polygon fill="black" stroke="black" points="1195.24,-174.5 1205.24,-171 1195.24,-167.5 1195.24,-174.5"/>
</g>
<!-- TicketsModule -->
<g id="node18" class="node">
<title>TicketsModule</title>
<polygon fill="#8dd3c7" stroke="black" points="452.26,-122 449.26,-126 428.26,-126 425.26,-122 351.74,-122 351.74,-86 452.26,-86 452.26,-122"/>
<text text-anchor="middle" x="402" y="-99.8" font-family="Times,serif" font-size="14.00">TicketsModule</text>
</g>
<!-- TicketsModule&#45;&gt;AppModule -->
<g id="edge18" class="edge">
<title>TicketsModule&#45;&gt;AppModule</title>
<path fill="none" stroke="black" d="M402,-122.03C402,-142.77 402,-174 402,-174 402,-174 1195.1,-174 1195.1,-174"/>
<polygon fill="black" stroke="black" points="1195.1,-177.5 1205.1,-174 1195.1,-170.5 1195.1,-177.5"/>
</g>
<!-- UsersModule -->
<g id="node19" class="node">
<title>UsersModule</title>
<polygon fill="#8dd3c7" stroke="black" points="333.92,-122 330.92,-126 309.92,-126 306.92,-122 242.08,-122 242.08,-86 333.92,-86 333.92,-122"/>
<text text-anchor="middle" x="288" y="-99.8" font-family="Times,serif" font-size="14.00">UsersModule</text>
</g>
<!-- UsersModule&#45;&gt;AppModule -->
<g id="edge19" class="edge">
<title>UsersModule&#45;&gt;AppModule</title>
<path fill="none" stroke="black" d="M288,-122.29C288,-144.21 288,-178 288,-178 288,-178 1195.18,-178 1195.18,-178"/>
<polygon fill="black" stroke="black" points="1195.18,-181.5 1205.18,-178 1195.18,-174.5 1195.18,-181.5"/>
</g>
<!-- UtilsModule -->
<g id="node20" class="node">
<title>UtilsModule</title>
<polygon fill="#8dd3c7" stroke="black" points="224.27,-122 221.27,-126 200.27,-126 197.27,-122 137.73,-122 137.73,-86 224.27,-86 224.27,-122"/>
<text text-anchor="middle" x="181" y="-99.8" font-family="Times,serif" font-size="14.00">UtilsModule</text>
</g>
<!-- UtilsModule&#45;&gt;AppModule -->
<g id="edge20" class="edge">
<title>UtilsModule&#45;&gt;AppModule</title>
<path fill="none" stroke="black" d="M181,-122.25C181,-145.02 181,-181 181,-181 181,-181 1195.21,-181 1195.21,-181"/>
<polygon fill="black" stroke="black" points="1195.21,-184.5 1205.21,-181 1195.21,-177.5 1195.21,-184.5"/>
</g>
<!-- ViewsModule -->
<g id="node21" class="node">
<title>ViewsModule</title>
<polygon fill="#8dd3c7" stroke="black" points="119.81,-122 116.81,-126 95.81,-126 92.81,-122 24.19,-122 24.19,-86 119.81,-86 119.81,-122"/>
<text text-anchor="middle" x="72" y="-99.8" font-family="Times,serif" font-size="14.00">ViewsModule</text>
</g>
<!-- ViewsModule&#45;&gt;AppModule -->
<g id="edge21" class="edge">
<title>ViewsModule&#45;&gt;AppModule</title>
<path fill="none" stroke="black" d="M72,-122.15C72,-145.76 72,-184 72,-184 72,-184 1195.18,-184 1195.18,-184"/>
<polygon fill="black" stroke="black" points="1195.18,-187.5 1205.18,-184 1195.18,-180.5 1195.18,-187.5"/>
</g>
</g>
</svg>
