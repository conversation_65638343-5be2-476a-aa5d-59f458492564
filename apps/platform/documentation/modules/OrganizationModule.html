<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content module">
                   <div class="content-data">



<ol class="breadcrumb">
    <li class="breadcrumb-item">Modules</li>
    <li class="breadcrumb-item" >OrganizationModule</li>
</ol>

<div class="text-center module-graph-container">
    <div id="module-graph-svg">
        <?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: dependencies Pages: 1 -->
<svg width="1547pt" height="284pt"
 viewBox="0.00 0.00 1547.00 284.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 280)">
<title>dependencies</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-280 1543,-280 1543,4 -4,4"/>
<text text-anchor="start" x="748.51" y="-42.4" font-family="Times-12" font-weight="bold" font-size="14.00">Legend</text>
<polygon fill="#ffffb3" stroke="transparent" points="535.5,-10 535.5,-30 555.5,-30 555.5,-10 535.5,-10"/>
<text text-anchor="start" x="559.13" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Declarations</text>
<polygon fill="#8dd3c7" stroke="transparent" points="648.5,-10 648.5,-30 668.5,-30 668.5,-10 648.5,-10"/>
<text text-anchor="start" x="672.23" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Module</text>
<polygon fill="#80b1d3" stroke="transparent" points="734.5,-10 734.5,-30 754.5,-30 754.5,-10 734.5,-10"/>
<text text-anchor="start" x="758.28" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Bootstrap</text>
<polygon fill="#fdb462" stroke="transparent" points="831.5,-10 831.5,-30 851.5,-30 851.5,-10 831.5,-10"/>
<text text-anchor="start" x="855.17" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Providers</text>
<polygon fill="#fb8072" stroke="transparent" points="927.5,-10 927.5,-30 947.5,-30 947.5,-10 927.5,-10"/>
<text text-anchor="start" x="951.23" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Exports</text>
<g id="clust1" class="cluster">
<title>cluster_OrganizationModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="8,-70 8,-268 1531,-268 1531,-70 8,-70"/>
</g>
<g id="clust6" class="cluster">
<title>cluster_OrganizationModule_providers</title>
<polygon fill="none" stroke="black" points="262,-78 262,-130 1523,-130 1523,-78 262,-78"/>
</g>
<g id="clust4" class="cluster">
<title>cluster_OrganizationModule_exports</title>
<polygon fill="none" stroke="black" points="428,-208 428,-260 750,-260 750,-208 428,-208"/>
</g>
<g id="clust3" class="cluster">
<title>cluster_OrganizationModule_imports</title>
<polygon fill="none" stroke="black" points="16,-78 16,-130 254,-130 254,-78 16,-78"/>
</g>
<!-- CommonModule -->
<g id="node1" class="node">
<title>CommonModule</title>
<polygon fill="#8dd3c7" stroke="black" points="245.67,-122 242.67,-126 221.67,-126 218.67,-122 134.33,-122 134.33,-86 245.67,-86 245.67,-122"/>
<text text-anchor="middle" x="190" y="-99.8" font-family="Times,serif" font-size="14.00">CommonModule</text>
</g>
<!-- OrganizationModule -->
<g id="node3" class="node">
<title>OrganizationModule</title>
<polygon fill="#8dd3c7" stroke="black" points="650.13,-187 647.13,-191 626.13,-191 623.13,-187 517.87,-187 517.87,-151 650.13,-151 650.13,-187"/>
<text text-anchor="middle" x="584" y="-164.8" font-family="Times,serif" font-size="14.00">OrganizationModule</text>
</g>
<!-- CommonModule&#45;&gt;OrganizationModule -->
<g id="edge1" class="edge">
<title>CommonModule&#45;&gt;OrganizationModule</title>
<path fill="none" stroke="black" d="M190,-122.11C190,-141.34 190,-169 190,-169 190,-169 507.61,-169 507.61,-169"/>
<polygon fill="black" stroke="black" points="507.61,-172.5 517.61,-169 507.61,-165.5 507.61,-172.5"/>
</g>
<!-- UsersModule -->
<g id="node2" class="node">
<title>UsersModule</title>
<polygon fill="#8dd3c7" stroke="black" points="115.92,-122 112.92,-126 91.92,-126 88.92,-122 24.08,-122 24.08,-86 115.92,-86 115.92,-122"/>
<text text-anchor="middle" x="70" y="-99.8" font-family="Times,serif" font-size="14.00">UsersModule</text>
</g>
<!-- UsersModule&#45;&gt;OrganizationModule -->
<g id="edge2" class="edge">
<title>UsersModule&#45;&gt;OrganizationModule</title>
<path fill="none" stroke="black" d="M70,-122.29C70,-144.21 70,-178 70,-178 70,-178 507.86,-178 507.86,-178"/>
<polygon fill="black" stroke="black" points="507.86,-181.5 517.86,-178 507.86,-174.5 507.86,-181.5"/>
</g>
<!-- OrganizationRepository  -->
<g id="node4" class="node">
<title>OrganizationRepository </title>
<polygon fill="#fb8072" stroke="black" points="742.02,-252 587.98,-252 587.98,-216 742.02,-216 742.02,-252"/>
<text text-anchor="middle" x="665" y="-229.8" font-family="Times,serif" font-size="14.00">OrganizationRepository </text>
</g>
<!-- OrganizationModule&#45;&gt;OrganizationRepository  -->
<g id="edge3" class="edge">
<title>OrganizationModule&#45;&gt;OrganizationRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M619.15,-187.11C619.15,-187.11 619.15,-205.99 619.15,-205.99"/>
<polygon fill="black" stroke="black" points="615.65,-205.99 619.15,-215.99 622.65,-205.99 615.65,-205.99"/>
</g>
<!-- OrganizationService  -->
<g id="node5" class="node">
<title>OrganizationService </title>
<polygon fill="#fb8072" stroke="black" points="570.06,-252 435.94,-252 435.94,-216 570.06,-216 570.06,-252"/>
<text text-anchor="middle" x="503" y="-229.8" font-family="Times,serif" font-size="14.00">OrganizationService </text>
</g>
<!-- OrganizationModule&#45;&gt;OrganizationService  -->
<g id="edge4" class="edge">
<title>OrganizationModule&#45;&gt;OrganizationService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M543.98,-187.11C543.98,-187.11 543.98,-205.99 543.98,-205.99"/>
<polygon fill="black" stroke="black" points="540.48,-205.99 543.98,-215.99 547.48,-205.99 540.48,-205.99"/>
</g>
<!-- CreateOrgAndOrgAdminSaga -->
<g id="node6" class="node">
<title>CreateOrgAndOrgAdminSaga</title>
<ellipse fill="#fdb462" stroke="black" cx="1387" cy="-104" rx="128.47" ry="18"/>
<text text-anchor="middle" x="1387" y="-99.8" font-family="Times,serif" font-size="14.00">CreateOrgAndOrgAdminSaga</text>
</g>
<!-- CreateOrgAndOrgAdminSaga&#45;&gt;OrganizationModule -->
<g id="edge5" class="edge">
<title>CreateOrgAndOrgAdminSaga&#45;&gt;OrganizationModule</title>
<path fill="none" stroke="black" d="M1387,-122.29C1387,-144.21 1387,-178 1387,-178 1387,-178 660.4,-178 660.4,-178"/>
<polygon fill="black" stroke="black" points="660.4,-174.5 650.4,-178 660.4,-181.5 660.4,-174.5"/>
</g>
<!-- OrganizationEventsFactory -->
<g id="node7" class="node">
<title>OrganizationEventsFactory</title>
<ellipse fill="#fdb462" stroke="black" cx="1123" cy="-104" rx="117.45" ry="18"/>
<text text-anchor="middle" x="1123" y="-99.8" font-family="Times,serif" font-size="14.00">OrganizationEventsFactory</text>
</g>
<!-- OrganizationEventsFactory&#45;&gt;OrganizationModule -->
<g id="edge6" class="edge">
<title>OrganizationEventsFactory&#45;&gt;OrganizationModule</title>
<path fill="none" stroke="black" d="M1123,-122.11C1123,-141.34 1123,-169 1123,-169 1123,-169 660.36,-169 660.36,-169"/>
<polygon fill="black" stroke="black" points="660.36,-165.5 650.36,-169 660.36,-172.5 660.36,-165.5"/>
</g>
<!-- OrganizationSNSEventsFactory -->
<g id="node8" class="node">
<title>OrganizationSNSEventsFactory</title>
<ellipse fill="#fdb462" stroke="black" cx="853" cy="-104" rx="134.87" ry="18"/>
<text text-anchor="middle" x="853" y="-99.8" font-family="Times,serif" font-size="14.00">OrganizationSNSEventsFactory</text>
</g>
<!-- OrganizationSNSEventsFactory&#45;&gt;OrganizationModule -->
<g id="edge7" class="edge">
<title>OrganizationSNSEventsFactory&#45;&gt;OrganizationModule</title>
<path fill="none" stroke="black" d="M730.04,-111.79C730.04,-127.17 730.04,-160 730.04,-160 730.04,-160 660.18,-160 660.18,-160"/>
<polygon fill="black" stroke="black" points="660.18,-156.5 650.18,-160 660.18,-163.5 660.18,-156.5"/>
</g>
<!-- OrganizationSNSPublisher -->
<g id="node9" class="node">
<title>OrganizationSNSPublisher</title>
<ellipse fill="#fdb462" stroke="black" cx="584" cy="-104" rx="115.77" ry="18"/>
<text text-anchor="middle" x="584" y="-99.8" font-family="Times,serif" font-size="14.00">OrganizationSNSPublisher</text>
</g>
<!-- OrganizationSNSPublisher&#45;&gt;OrganizationModule -->
<g id="edge8" class="edge">
<title>OrganizationSNSPublisher&#45;&gt;OrganizationModule</title>
<path fill="none" stroke="black" d="M584,-122.11C584,-122.11 584,-140.99 584,-140.99"/>
<polygon fill="black" stroke="black" points="580.5,-140.99 584,-150.99 587.5,-140.99 580.5,-140.99"/>
</g>
<!-- OrganizationService -->
<g id="node10" class="node">
<title>OrganizationService</title>
<ellipse fill="#fdb462" stroke="black" cx="360" cy="-104" rx="90.31" ry="18"/>
<text text-anchor="middle" x="360" y="-99.8" font-family="Times,serif" font-size="14.00">OrganizationService</text>
</g>
<!-- OrganizationService&#45;&gt;OrganizationModule -->
<g id="edge9" class="edge">
<title>OrganizationService&#45;&gt;OrganizationModule</title>
<path fill="none" stroke="black" d="M443.06,-111.32C443.06,-126.5 443.06,-160 443.06,-160 443.06,-160 507.66,-160 507.66,-160"/>
<polygon fill="black" stroke="black" points="507.66,-163.5 517.66,-160 507.66,-156.5 507.66,-163.5"/>
</g>
</g>
</svg>

    </div>
    <i id="fullscreen" class="icon ion-ios-resize module-graph-fullscreen-btn" aria-hidden="true"></i>
    <div class="btn-group size-buttons">
        <button id="zoom-in" class="btn btn-default btn-sm">Zoom in</button>
        <button id="reset" class="btn btn-default btn-sm">Reset</button>
        <button id="zoom-out" class="btn btn-default btn-sm">Zoom out</button>
    </div>
</div>
<script src="../js/libs/svg-pan-zoom.min.js"></script>
<script src="../js/svg-pan-zoom.controls.js"></script>

<ul class="nav nav-tabs" role="tablist">
    <li class="nav-item">
        <a href="#info" 
            class="nav-link"
            class="nav-link active"
            role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
    </li>
    <li class="nav-item">
        <a href="#source" 
            class="nav-link"
            
            role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
    </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">

        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/organization/organization.module.ts</code>
        </p>





        <div class="container-fluid module">
            <div class="row">
                <div class="col-sm-3">
                    <h3>Providers<a href="https://angular.io/api/core/NgModule#providers" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module providers"><span class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../injectables/CreateOrgAndOrgAdminSaga.html">CreateOrgAndOrgAdminSaga</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/OrganizationEventsFactory.html">OrganizationEventsFactory</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/OrganizationSNSEventsFactory.html">OrganizationSNSEventsFactory</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/OrganizationSNSPublisher.html">OrganizationSNSPublisher</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/OrganizationService.html">OrganizationService</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Controllers<a href="https://docs.nestjs.com/controllers" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module controllers"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../controllers/OrganizationController.html">OrganizationController</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../controllers/OrganizationGrpcController.html">OrganizationGrpcController</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Imports<a href="https://angular.io/api/core/NgModule#imports" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module imports"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../modules/CommonModule.html">CommonModule</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../modules/UsersModule.html">UsersModule</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Exports<a href="https://angular.io/api/core/NgModule#exports" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module exports"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../s/OrganizationRepository.html">OrganizationRepository</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/OrganizationService.html">OrganizationService</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>


    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { BullModule } from &quot;@nestjs/bullmq&quot;;
import { Module } from &quot;@nestjs/common&quot;;
import { TypeOrmModule } from &quot;@nestjs/typeorm&quot;;
import { SentryService } from &quot;@repo/nestjs-commons/filters&quot;;
import {
  ApiKeyGrpcStrategy,
  AuthenticationGrpcClient,
  BearerTokenGrpcStrategy,
  GrpcAuthGuard,
  UserOrgInternalGrpcStrategy,
} from &quot;@repo/nestjs-commons/guards&quot;;
import { ILogger } from &quot;@repo/nestjs-commons/logger&quot;;
import { CircuitBreaker } from &quot;@repo/nestjs-commons/providers&quot;;
import { SNSPublisherService } from &quot;@repo/thena-eventbridge&quot;;
import {
  Organization,
  OrganizationDomains,
  OrganizationDomainsRepository,
  OrganizationInvitations,
  OrganizationInvitationsRepository,
  OrganizationRepository,
  TransactionService,
} from &quot;@repo/thena-platform-entities&quot;;
import { ConfigModule } from &quot;src/config/config.module&quot;;
import { ConfigKeys, ConfigService } from &quot;src/config/config.service&quot;;
import { QueueNames } from &quot;src/constants/queue.constants&quot;;
import { CommonModule } from &quot;../common/common.module&quot;;
import { UsersModule } from &quot;../users/users.module&quot;;
import { OrganizationGrpcController } from &quot;./controllers/organization-grpc.controller&quot;;
import { OrganizationController } from &quot;./controllers/organization.controller&quot;;
import { OrganizationEventsFactory } from &quot;./events/organization-events.factory&quot;;
import { OrganizationSNSEventsFactory } from &quot;./events/organization-sns-events.factory&quot;;
import { OrganizationSNSPublisher } from &quot;./processors/sns-publisher.processor&quot;;
import { CreateOrgAndOrgAdminSaga } from &quot;./sagas&quot;;
import { OrganizationService } from &quot;./services/organization.service&quot;;

@Module({
  imports: [
    CommonModule,
    TypeOrmModule.forFeature([
      Organization,
      OrganizationRepository,
      OrganizationDomains,
      OrganizationDomainsRepository,
      OrganizationInvitations,
      OrganizationInvitationsRepository,
    ]),
    UsersModule,
    BullModule.registerQueueAsync({
      name: QueueNames.ORGANIZATION_SNS_PUBLISHER,
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) &#x3D;&gt; ({
        redis: {
          host: configService.get(ConfigKeys.REDIS_HOST),
          port: Number(configService.get(ConfigKeys.REDIS_PORT)),
          password: configService.get(ConfigKeys.REDIS_PASSWORD),
          username: configService.get(ConfigKeys.REDIS_USERNAME),
        },
        defaultJobOptions: {
          removeOnComplete: {
            age: 24 * 3600, // keep completed jobs for 24 hours
            count: 1000, // keep last 1000 completed jobs
          },
          removeOnFail: {
            age: 24 * 3600, // keep up to 24 hours
          },
        },
      }),
    }),
  ],
  controllers: [OrganizationController, OrganizationGrpcController],
  providers: [
    // SNS publisher
    {
      provide: &quot;ORGANIZATION_SNS_PUBLISHER&quot;,
      useFactory: (
        configService: ConfigService,
        sentryService: SentryService,
        loggerService: ILogger,
      ) &#x3D;&gt; {
        return new SNSPublisherService(
          {
            topicArn: configService.get(
              ConfigKeys.AWS_SNS_ORGANIZATION_TOPIC_ARN,
            ),
            region: configService.get(ConfigKeys.AWS_REGION),
            credentials: {
              accessKeyId: configService.get(ConfigKeys.AWS_ACCESS_KEY),
              secretAccessKey: configService.get(ConfigKeys.AWS_SECRET_KEY),
            },
          },
          sentryService,
          loggerService,
        );
      },
      inject: [ConfigService, &quot;Sentry&quot;, &quot;CustomLogger&quot;],
    },

    // Services
    TransactionService,

    // Auth GRPC
    ApiKeyGrpcStrategy,
    BearerTokenGrpcStrategy,
    GrpcAuthGuard,
    UserOrgInternalGrpcStrategy,
    AuthenticationGrpcClient,

    // Sagas
    CreateOrgAndOrgAdminSaga,

    // Core
    OrganizationService,
    OrganizationRepository,
    OrganizationDomainsRepository,
    OrganizationInvitationsRepository,
    {
      provide: CircuitBreaker,
      useFactory: () &#x3D;&gt; {
        return new CircuitBreaker({
          failureThreshold: 0.3,
          resetTimeout: 15_000,
          halfOpenRetries: 3,
        });
      },
    },

    // Event Factories
    OrganizationEventsFactory,
    OrganizationSNSEventsFactory,

    // Listeners
    OrganizationSNSPublisher,
  ],
  exports: [OrganizationService, OrganizationRepository],
})
export class OrganizationModule {}
</code></pre>
    </div>
</div>

















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'module';
            var COMPODOC_CURRENT_PAGE_URL = 'OrganizationModule.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
