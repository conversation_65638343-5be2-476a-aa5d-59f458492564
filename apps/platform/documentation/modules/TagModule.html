<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content module">
                   <div class="content-data">



<ol class="breadcrumb">
    <li class="breadcrumb-item">Modules</li>
    <li class="breadcrumb-item" >TagModule</li>
</ol>

<div class="text-center module-graph-container">
    <div id="module-graph-svg">
        <?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: dependencies Pages: 1 -->
<svg width="1101pt" height="284pt"
 viewBox="0.00 0.00 1101.00 284.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 280)">
<title>dependencies</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-280 1097,-280 1097,4 -4,4"/>
<text text-anchor="start" x="525.51" y="-42.4" font-family="Times-12" font-weight="bold" font-size="14.00">Legend</text>
<polygon fill="#ffffb3" stroke="transparent" points="312.5,-10 312.5,-30 332.5,-30 332.5,-10 312.5,-10"/>
<text text-anchor="start" x="336.13" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Declarations</text>
<polygon fill="#8dd3c7" stroke="transparent" points="425.5,-10 425.5,-30 445.5,-30 445.5,-10 425.5,-10"/>
<text text-anchor="start" x="449.23" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Module</text>
<polygon fill="#80b1d3" stroke="transparent" points="511.5,-10 511.5,-30 531.5,-30 531.5,-10 511.5,-10"/>
<text text-anchor="start" x="535.28" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Bootstrap</text>
<polygon fill="#fdb462" stroke="transparent" points="608.5,-10 608.5,-30 628.5,-30 628.5,-10 608.5,-10"/>
<text text-anchor="start" x="632.17" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Providers</text>
<polygon fill="#fb8072" stroke="transparent" points="704.5,-10 704.5,-30 724.5,-30 724.5,-10 704.5,-10"/>
<text text-anchor="start" x="728.23" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Exports</text>
<g id="clust1" class="cluster">
<title>cluster_TagModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="8,-70 8,-268 1085,-268 1085,-70 8,-70"/>
</g>
<g id="clust6" class="cluster">
<title>cluster_TagModule_providers</title>
<polygon fill="none" stroke="black" points="380,-78 380,-130 1077,-130 1077,-78 380,-78"/>
</g>
<g id="clust3" class="cluster">
<title>cluster_TagModule_imports</title>
<polygon fill="none" stroke="black" points="16,-78 16,-130 372,-130 372,-78 16,-78"/>
</g>
<g id="clust4" class="cluster">
<title>cluster_TagModule_exports</title>
<polygon fill="none" stroke="black" points="201,-208 201,-260 740,-260 740,-208 201,-208"/>
</g>
<!-- CommonModule -->
<g id="node1" class="node">
<title>CommonModule</title>
<polygon fill="#8dd3c7" stroke="black" points="363.67,-122 360.67,-126 339.67,-126 336.67,-122 252.33,-122 252.33,-86 363.67,-86 363.67,-122"/>
<text text-anchor="middle" x="308" y="-99.8" font-family="Times,serif" font-size="14.00">CommonModule</text>
</g>
<!-- TagModule -->
<g id="node4" class="node">
<title>TagModule</title>
<polygon fill="#8dd3c7" stroke="black" points="508.82,-187 505.82,-191 484.82,-191 481.82,-187 427.18,-187 427.18,-151 508.82,-151 508.82,-187"/>
<text text-anchor="middle" x="468" y="-164.8" font-family="Times,serif" font-size="14.00">TagModule</text>
</g>
<!-- CommonModule&#45;&gt;TagModule -->
<g id="edge1" class="edge">
<title>CommonModule&#45;&gt;TagModule</title>
<path fill="none" stroke="black" d="M355.28,-122.01C355.28,-138.05 355.28,-159 355.28,-159 355.28,-159 417.3,-159 417.3,-159"/>
<polygon fill="black" stroke="black" points="417.3,-162.5 427.3,-159 417.3,-155.5 417.3,-162.5"/>
</g>
<!-- TicketsModule -->
<g id="node2" class="node">
<title>TicketsModule</title>
<polygon fill="#8dd3c7" stroke="black" points="234.26,-122 231.26,-126 210.26,-126 207.26,-122 133.74,-122 133.74,-86 234.26,-86 234.26,-122"/>
<text text-anchor="middle" x="184" y="-99.8" font-family="Times,serif" font-size="14.00">TicketsModule</text>
</g>
<!-- TicketsModule&#45;&gt;TagModule -->
<g id="edge2" class="edge">
<title>TicketsModule&#45;&gt;TagModule</title>
<path fill="none" stroke="black" d="M221.94,-122.27C221.94,-140.56 221.94,-166 221.94,-166 221.94,-166 417.09,-166 417.09,-166"/>
<polygon fill="black" stroke="black" points="417.09,-169.5 427.09,-166 417.09,-162.5 417.09,-169.5"/>
</g>
<!-- UsersModule -->
<g id="node3" class="node">
<title>UsersModule</title>
<polygon fill="#8dd3c7" stroke="black" points="115.92,-122 112.92,-126 91.92,-126 88.92,-122 24.08,-122 24.08,-86 115.92,-86 115.92,-122"/>
<text text-anchor="middle" x="70" y="-99.8" font-family="Times,serif" font-size="14.00">UsersModule</text>
</g>
<!-- UsersModule&#45;&gt;TagModule -->
<g id="edge3" class="edge">
<title>UsersModule&#45;&gt;TagModule</title>
<path fill="none" stroke="black" d="M70,-122.13C70,-142.57 70,-173 70,-173 70,-173 417.28,-173 417.28,-173"/>
<polygon fill="black" stroke="black" points="417.28,-176.5 427.28,-173 417.28,-169.5 417.28,-176.5"/>
</g>
<!-- TagAnnotatorService  -->
<g id="node5" class="node">
<title>TagAnnotatorService </title>
<polygon fill="#fb8072" stroke="black" points="732.01,-252 591.99,-252 591.99,-216 732.01,-216 732.01,-252"/>
<text text-anchor="middle" x="662" y="-229.8" font-family="Times,serif" font-size="14.00">TagAnnotatorService </text>
</g>
<!-- TagModule&#45;&gt;TagAnnotatorService  -->
<g id="edge4" class="edge">
<title>TagModule&#45;&gt;TagAnnotatorService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M509.05,-180C565.29,-180 659.37,-180 659.37,-180 659.37,-180 659.37,-205.72 659.37,-205.72"/>
<polygon fill="black" stroke="black" points="655.87,-205.72 659.37,-215.72 662.87,-205.72 655.87,-205.72"/>
</g>
<!-- TagsService  -->
<g id="node6" class="node">
<title>TagsService </title>
<polygon fill="#fb8072" stroke="black" points="574.19,-252 485.81,-252 485.81,-216 574.19,-216 574.19,-252"/>
<text text-anchor="middle" x="530" y="-229.8" font-family="Times,serif" font-size="14.00">TagsService </text>
</g>
<!-- TagModule&#45;&gt;TagsService  -->
<g id="edge5" class="edge">
<title>TagModule&#45;&gt;TagsService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M497.16,-187.11C497.16,-187.11 497.16,-205.99 497.16,-205.99"/>
<polygon fill="black" stroke="black" points="493.66,-205.99 497.16,-215.99 500.66,-205.99 493.66,-205.99"/>
</g>
<!-- TeamTagsService  -->
<g id="node7" class="node">
<title>TeamTagsService </title>
<polygon fill="#fb8072" stroke="black" points="467.06,-252 346.94,-252 346.94,-216 467.06,-216 467.06,-252"/>
<text text-anchor="middle" x="407" y="-229.8" font-family="Times,serif" font-size="14.00">TeamTagsService </text>
</g>
<!-- TagModule&#45;&gt;TeamTagsService  -->
<g id="edge6" class="edge">
<title>TagModule&#45;&gt;TeamTagsService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M447.31,-187.11C447.31,-187.11 447.31,-205.99 447.31,-205.99"/>
<polygon fill="black" stroke="black" points="443.81,-205.99 447.31,-215.99 450.81,-205.99 443.81,-205.99"/>
</g>
<!-- TicketTagService  -->
<g id="node8" class="node">
<title>TicketTagService </title>
<polygon fill="#fb8072" stroke="black" points="328.51,-252 209.49,-252 209.49,-216 328.51,-216 328.51,-252"/>
<text text-anchor="middle" x="269" y="-229.8" font-family="Times,serif" font-size="14.00">TicketTagService </text>
</g>
<!-- TagModule&#45;&gt;TicketTagService  -->
<g id="edge7" class="edge">
<title>TagModule&#45;&gt;TicketTagService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M427.24,-180C374.79,-180 290.34,-180 290.34,-180 290.34,-180 290.34,-205.72 290.34,-205.72"/>
<polygon fill="black" stroke="black" points="286.84,-205.72 290.34,-215.72 293.84,-205.72 286.84,-205.72"/>
</g>
<!-- TagAnnotatorService -->
<g id="node9" class="node">
<title>TagAnnotatorService</title>
<ellipse fill="#fdb462" stroke="black" cx="975" cy="-104" rx="94.32" ry="18"/>
<text text-anchor="middle" x="975" y="-99.8" font-family="Times,serif" font-size="14.00">TagAnnotatorService</text>
</g>
<!-- TagAnnotatorService&#45;&gt;TagModule -->
<g id="edge8" class="edge">
<title>TagAnnotatorService&#45;&gt;TagModule</title>
<path fill="none" stroke="black" d="M975,-122.13C975,-142.57 975,-173 975,-173 975,-173 519.06,-173 519.06,-173"/>
<polygon fill="black" stroke="black" points="519.06,-169.5 509.06,-173 519.06,-176.5 519.06,-169.5"/>
</g>
<!-- TagsService -->
<g id="node10" class="node">
<title>TagsService</title>
<ellipse fill="#fdb462" stroke="black" cx="804" cy="-104" rx="58.53" ry="18"/>
<text text-anchor="middle" x="804" y="-99.8" font-family="Times,serif" font-size="14.00">TagsService</text>
</g>
<!-- TagsService&#45;&gt;TagModule -->
<g id="edge9" class="edge">
<title>TagsService&#45;&gt;TagModule</title>
<path fill="none" stroke="black" d="M804,-122.27C804,-140.56 804,-166 804,-166 804,-166 518.86,-166 518.86,-166"/>
<polygon fill="black" stroke="black" points="518.86,-162.5 508.86,-166 518.86,-169.5 518.86,-162.5"/>
</g>
<!-- TeamTagsService -->
<g id="node11" class="node">
<title>TeamTagsService</title>
<ellipse fill="#fdb462" stroke="black" cx="646" cy="-104" rx="80.99" ry="18"/>
<text text-anchor="middle" x="646" y="-99.8" font-family="Times,serif" font-size="14.00">TeamTagsService</text>
</g>
<!-- TeamTagsService&#45;&gt;TagModule -->
<g id="edge10" class="edge">
<title>TeamTagsService&#45;&gt;TagModule</title>
<path fill="none" stroke="black" d="M569.8,-110.31C569.8,-124.76 569.8,-159 569.8,-159 569.8,-159 518.82,-159 518.82,-159"/>
<polygon fill="black" stroke="black" points="518.82,-155.5 508.82,-159 518.82,-162.5 518.82,-155.5"/>
</g>
<!-- TicketTagService -->
<g id="node12" class="node">
<title>TicketTagService</title>
<ellipse fill="#fdb462" stroke="black" cx="468" cy="-104" rx="79.85" ry="18"/>
<text text-anchor="middle" x="468" y="-99.8" font-family="Times,serif" font-size="14.00">TicketTagService</text>
</g>
<!-- TicketTagService&#45;&gt;TagModule -->
<g id="edge11" class="edge">
<title>TicketTagService&#45;&gt;TagModule</title>
<path fill="none" stroke="black" d="M468,-122.11C468,-122.11 468,-140.99 468,-140.99"/>
<polygon fill="black" stroke="black" points="464.5,-140.99 468,-150.99 471.5,-140.99 464.5,-140.99"/>
</g>
</g>
</svg>

    </div>
    <i id="fullscreen" class="icon ion-ios-resize module-graph-fullscreen-btn" aria-hidden="true"></i>
    <div class="btn-group size-buttons">
        <button id="zoom-in" class="btn btn-default btn-sm">Zoom in</button>
        <button id="reset" class="btn btn-default btn-sm">Reset</button>
        <button id="zoom-out" class="btn btn-default btn-sm">Zoom out</button>
    </div>
</div>
<script src="../js/libs/svg-pan-zoom.min.js"></script>
<script src="../js/svg-pan-zoom.controls.js"></script>

<ul class="nav nav-tabs" role="tablist">
    <li class="nav-item">
        <a href="#info" 
            class="nav-link"
            class="nav-link active"
            role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
    </li>
    <li class="nav-item">
        <a href="#source" 
            class="nav-link"
            
            role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
    </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">

        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/tags/tags.module.ts</code>
        </p>





        <div class="container-fluid module">
            <div class="row">
                <div class="col-sm-3">
                    <h3>Providers<a href="https://angular.io/api/core/NgModule#providers" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module providers"><span class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../injectables/TagAnnotatorService.html">TagAnnotatorService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/TagsService.html">TagsService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/TeamTagsService.html">TeamTagsService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/TicketTagService.html">TicketTagService</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Controllers<a href="https://docs.nestjs.com/controllers" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module controllers"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../controllers/TeamsTagsController.html">TeamsTagsController</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../controllers/TicketTagsController.html">TicketTagsController</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../controllers/TagsController.html">TagsController</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../controllers/TagsGrpcController.html">TagsGrpcController</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Imports<a href="https://angular.io/api/core/NgModule#imports" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module imports"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../modules/CommonModule.html">CommonModule</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../modules/TicketsModule.html">TicketsModule</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../modules/UsersModule.html">UsersModule</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Exports<a href="https://angular.io/api/core/NgModule#exports" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module exports"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../injectables/TagAnnotatorService.html">TagAnnotatorService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/TagsService.html">TagsService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/TeamTagsService.html">TeamTagsService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/TicketTagService.html">TicketTagService</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>


    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Module } from &quot;@nestjs/common&quot;;
import { TypeOrmModule } from &quot;@nestjs/typeorm&quot;;
import {
  ApiKeyGrpcStrategy,
  AuthenticationGrpcClient,
  BearerTokenGrpcStrategy,
  GrpcAuthGuard,
  UserOrgInternalGrpcStrategy,
} from &quot;@repo/nestjs-commons/guards&quot;;
import {
  Tag,
  TagRepository,
  Team,
  TeamRepository,
  Ticket,
  TicketRepository,
  TransactionService,
} from &quot;@repo/thena-platform-entities&quot;;
import { CommonModule } from &quot;../common/common.module&quot;;
import { TicketsModule } from &quot;../tickets/tickets.module&quot;;
import { UsersModule } from &quot;../users/users.module&quot;;
import { TagsGrpcController } from &quot;./controllers/grpc/tags-grpc.controller&quot;;
import { TagsController } from &quot;./controllers/tags.controller&quot;;
import { TeamsTagsController } from &quot;./controllers/team-tags.controller&quot;;
import { TicketTagsController } from &quot;./controllers/ticket-tags.controller&quot;;
import { TagAnnotatorService } from &quot;./services/tag-annotator.service&quot;;
import { TagsService } from &quot;./services/tags.service&quot;;
import { TeamTagsService } from &quot;./services/team-tags.service&quot;;
import { TicketTagService } from &quot;./services/ticket-tags.service&quot;;

@Module({
  imports: [
    CommonModule,
    TypeOrmModule.forFeature([
      Tag,
      TagRepository,
      Team,
      TeamRepository,
      Ticket,
      TicketRepository,
    ]),
    UsersModule,
    TicketsModule,
  ],
  controllers: [
    TeamsTagsController,
    TicketTagsController,
    TagsController,
    TagsGrpcController,
  ],
  providers: [
    AuthenticationGrpcClient,
    GrpcAuthGuard,
    ApiKeyGrpcStrategy,
    BearerTokenGrpcStrategy,
    UserOrgInternalGrpcStrategy,
    TransactionService,
    TagsService,
    TeamTagsService,
    TicketTagService,
    TagRepository,
    TeamRepository,
    TagAnnotatorService,
  ],
  exports: [
    TagsService,
    TeamTagsService,
    TicketTagService,
    TagAnnotatorService,
  ],
})
export class TagModule {}
</code></pre>
    </div>
</div>

















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'module';
            var COMPODOC_CURRENT_PAGE_URL = 'TagModule.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
