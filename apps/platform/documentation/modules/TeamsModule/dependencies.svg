<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: dependencies Pages: 1 -->
<svg width="1208pt" height="284pt"
 viewBox="0.00 0.00 1208.00 284.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 280)">
<title>dependencies</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-280 1204,-280 1204,4 -4,4"/>
<text text-anchor="start" x="579.01" y="-42.4" font-family="Times-12" font-weight="bold" font-size="14.00">Legend</text>
<polygon fill="#ffffb3" stroke="transparent" points="366,-10 366,-30 386,-30 386,-10 366,-10"/>
<text text-anchor="start" x="389.63" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Declarations</text>
<polygon fill="#8dd3c7" stroke="transparent" points="479,-10 479,-30 499,-30 499,-10 479,-10"/>
<text text-anchor="start" x="502.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Module</text>
<polygon fill="#80b1d3" stroke="transparent" points="565,-10 565,-30 585,-30 585,-10 565,-10"/>
<text text-anchor="start" x="588.78" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Bootstrap</text>
<polygon fill="#fdb462" stroke="transparent" points="662,-10 662,-30 682,-30 682,-10 662,-10"/>
<text text-anchor="start" x="685.67" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Providers</text>
<polygon fill="#fb8072" stroke="transparent" points="758,-10 758,-30 778,-30 778,-10 758,-10"/>
<text text-anchor="start" x="781.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Exports</text>
<g id="clust1" class="cluster">
<title>cluster_TeamsModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="8,-70 8,-268 1192,-268 1192,-70 8,-70"/>
</g>
<g id="clust4" class="cluster">
<title>cluster_TeamsModule_exports</title>
<polygon fill="none" stroke="black" points="324,-208 324,-260 606,-260 606,-208 324,-208"/>
</g>
<g id="clust6" class="cluster">
<title>cluster_TeamsModule_providers</title>
<polygon fill="none" stroke="black" points="378,-78 378,-130 1184,-130 1184,-78 378,-78"/>
</g>
<g id="clust3" class="cluster">
<title>cluster_TeamsModule_imports</title>
<polygon fill="none" stroke="black" points="16,-78 16,-130 370,-130 370,-78 16,-78"/>
</g>
<!-- CommonModule -->
<g id="node1" class="node">
<title>CommonModule</title>
<polygon fill="#8dd3c7" stroke="black" points="361.67,-122 358.67,-126 337.67,-126 334.67,-122 250.33,-122 250.33,-86 361.67,-86 361.67,-122"/>
<text text-anchor="middle" x="306" y="-99.8" font-family="Times,serif" font-size="14.00">CommonModule</text>
</g>
<!-- TeamsModule -->
<g id="node4" class="node">
<title>TeamsModule</title>
<polygon fill="#8dd3c7" stroke="black" points="500.37,-187 497.37,-191 476.37,-191 473.37,-187 403.63,-187 403.63,-151 500.37,-151 500.37,-187"/>
<text text-anchor="middle" x="452" y="-164.8" font-family="Times,serif" font-size="14.00">TeamsModule</text>
</g>
<!-- CommonModule&#45;&gt;TeamsModule -->
<g id="edge1" class="edge">
<title>CommonModule&#45;&gt;TeamsModule</title>
<path fill="none" stroke="black" d="M346.72,-122.03C346.72,-138.4 346.72,-160 346.72,-160 346.72,-160 393.37,-160 393.37,-160"/>
<polygon fill="black" stroke="black" points="393.37,-163.5 403.37,-160 393.37,-156.5 393.37,-163.5"/>
</g>
<!-- SharedModule -->
<g id="node2" class="node">
<title>SharedModule</title>
<polygon fill="#8dd3c7" stroke="black" points="232.42,-122 229.42,-126 208.42,-126 205.42,-122 133.58,-122 133.58,-86 232.42,-86 232.42,-122"/>
<text text-anchor="middle" x="183" y="-99.8" font-family="Times,serif" font-size="14.00">SharedModule</text>
</g>
<!-- SharedModule&#45;&gt;TeamsModule -->
<g id="edge2" class="edge">
<title>SharedModule&#45;&gt;TeamsModule</title>
<path fill="none" stroke="black" d="M183,-122.11C183,-141.34 183,-169 183,-169 183,-169 393.6,-169 393.6,-169"/>
<polygon fill="black" stroke="black" points="393.6,-172.5 403.6,-169 393.6,-165.5 393.6,-172.5"/>
</g>
<!-- UsersModule -->
<g id="node3" class="node">
<title>UsersModule</title>
<polygon fill="#8dd3c7" stroke="black" points="115.92,-122 112.92,-126 91.92,-126 88.92,-122 24.08,-122 24.08,-86 115.92,-86 115.92,-122"/>
<text text-anchor="middle" x="70" y="-99.8" font-family="Times,serif" font-size="14.00">UsersModule</text>
</g>
<!-- UsersModule&#45;&gt;TeamsModule -->
<g id="edge3" class="edge">
<title>UsersModule&#45;&gt;TeamsModule</title>
<path fill="none" stroke="black" d="M70,-122.29C70,-144.21 70,-178 70,-178 70,-178 393.62,-178 393.62,-178"/>
<polygon fill="black" stroke="black" points="393.62,-181.5 403.62,-178 393.62,-174.5 393.62,-181.5"/>
</g>
<!-- TeamAnnotatorService  -->
<g id="node5" class="node">
<title>TeamAnnotatorService </title>
<polygon fill="#fb8072" stroke="black" points="598.11,-252 447.89,-252 447.89,-216 598.11,-216 598.11,-252"/>
<text text-anchor="middle" x="523" y="-229.8" font-family="Times,serif" font-size="14.00">TeamAnnotatorService </text>
</g>
<!-- TeamsModule&#45;&gt;TeamAnnotatorService  -->
<g id="edge4" class="edge">
<title>TeamsModule&#45;&gt;TeamAnnotatorService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M474.19,-187.11C474.19,-187.11 474.19,-205.99 474.19,-205.99"/>
<polygon fill="black" stroke="black" points="470.69,-205.99 474.19,-215.99 477.69,-205.99 470.69,-205.99"/>
</g>
<!-- TeamsService  -->
<g id="node6" class="node">
<title>TeamsService </title>
<polygon fill="#fb8072" stroke="black" points="430.29,-252 331.71,-252 331.71,-216 430.29,-216 430.29,-252"/>
<text text-anchor="middle" x="381" y="-229.8" font-family="Times,serif" font-size="14.00">TeamsService </text>
</g>
<!-- TeamsModule&#45;&gt;TeamsService  -->
<g id="edge5" class="edge">
<title>TeamsModule&#45;&gt;TeamsService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M416.98,-187.11C416.98,-187.11 416.98,-205.99 416.98,-205.99"/>
<polygon fill="black" stroke="black" points="413.48,-205.99 416.98,-215.99 420.48,-205.99 413.48,-205.99"/>
</g>
<!-- BusinessHoursValidatorService -->
<g id="node7" class="node">
<title>BusinessHoursValidatorService</title>
<ellipse fill="#fdb462" stroke="black" cx="1042" cy="-104" rx="134.28" ry="18"/>
<text text-anchor="middle" x="1042" y="-99.8" font-family="Times,serif" font-size="14.00">BusinessHoursValidatorService</text>
</g>
<!-- BusinessHoursValidatorService&#45;&gt;TeamsModule -->
<g id="edge6" class="edge">
<title>BusinessHoursValidatorService&#45;&gt;TeamsModule</title>
<path fill="none" stroke="black" d="M1042,-122.29C1042,-144.21 1042,-178 1042,-178 1042,-178 510.53,-178 510.53,-178"/>
<polygon fill="black" stroke="black" points="510.53,-174.5 500.53,-178 510.53,-181.5 510.53,-174.5"/>
</g>
<!-- SharedService -->
<g id="node8" class="node">
<title>SharedService</title>
<ellipse fill="#fdb462" stroke="black" cx="823" cy="-104" rx="66.61" ry="18"/>
<text text-anchor="middle" x="823" y="-99.8" font-family="Times,serif" font-size="14.00">SharedService</text>
</g>
<!-- SharedService&#45;&gt;TeamsModule -->
<g id="edge7" class="edge">
<title>SharedService&#45;&gt;TeamsModule</title>
<path fill="none" stroke="black" d="M823,-122.11C823,-141.34 823,-169 823,-169 823,-169 510.5,-169 510.5,-169"/>
<polygon fill="black" stroke="black" points="510.5,-165.5 500.5,-169 510.5,-172.5 510.5,-165.5"/>
</g>
<!-- TeamAnnotatorService -->
<g id="node9" class="node">
<title>TeamAnnotatorService</title>
<ellipse fill="#fdb462" stroke="black" cx="637" cy="-104" rx="101.26" ry="18"/>
<text text-anchor="middle" x="637" y="-99.8" font-family="Times,serif" font-size="14.00">TeamAnnotatorService</text>
</g>
<!-- TeamAnnotatorService&#45;&gt;TeamsModule -->
<g id="edge8" class="edge">
<title>TeamAnnotatorService&#45;&gt;TeamsModule</title>
<path fill="none" stroke="black" d="M566.96,-116.99C566.96,-133.53 566.96,-160 566.96,-160 566.96,-160 510.42,-160 510.42,-160"/>
<polygon fill="black" stroke="black" points="510.42,-156.5 500.42,-160 510.42,-163.5 510.42,-156.5"/>
</g>
<!-- TeamsService -->
<g id="node10" class="node">
<title>TeamsService</title>
<ellipse fill="#fdb462" stroke="black" cx="452" cy="-104" rx="65.97" ry="18"/>
<text text-anchor="middle" x="452" y="-99.8" font-family="Times,serif" font-size="14.00">TeamsService</text>
</g>
<!-- TeamsService&#45;&gt;TeamsModule -->
<g id="edge9" class="edge">
<title>TeamsService&#45;&gt;TeamsModule</title>
<path fill="none" stroke="black" d="M452,-122.11C452,-122.11 452,-140.99 452,-140.99"/>
<polygon fill="black" stroke="black" points="448.5,-140.99 452,-150.99 455.5,-140.99 448.5,-140.99"/>
</g>
</g>
</svg>
