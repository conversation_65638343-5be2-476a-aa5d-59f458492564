<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content module">
                   <div class="content-data">



<ol class="breadcrumb">
    <li class="breadcrumb-item">Modules</li>
    <li class="breadcrumb-item" >TeamsModule</li>
</ol>

<div class="text-center module-graph-container">
    <div id="module-graph-svg">
        <?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: dependencies Pages: 1 -->
<svg width="1208pt" height="284pt"
 viewBox="0.00 0.00 1208.00 284.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 280)">
<title>dependencies</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-280 1204,-280 1204,4 -4,4"/>
<text text-anchor="start" x="579.01" y="-42.4" font-family="Times-12" font-weight="bold" font-size="14.00">Legend</text>
<polygon fill="#ffffb3" stroke="transparent" points="366,-10 366,-30 386,-30 386,-10 366,-10"/>
<text text-anchor="start" x="389.63" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Declarations</text>
<polygon fill="#8dd3c7" stroke="transparent" points="479,-10 479,-30 499,-30 499,-10 479,-10"/>
<text text-anchor="start" x="502.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Module</text>
<polygon fill="#80b1d3" stroke="transparent" points="565,-10 565,-30 585,-30 585,-10 565,-10"/>
<text text-anchor="start" x="588.78" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Bootstrap</text>
<polygon fill="#fdb462" stroke="transparent" points="662,-10 662,-30 682,-30 682,-10 662,-10"/>
<text text-anchor="start" x="685.67" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Providers</text>
<polygon fill="#fb8072" stroke="transparent" points="758,-10 758,-30 778,-30 778,-10 758,-10"/>
<text text-anchor="start" x="781.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Exports</text>
<g id="clust1" class="cluster">
<title>cluster_TeamsModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="8,-70 8,-268 1192,-268 1192,-70 8,-70"/>
</g>
<g id="clust4" class="cluster">
<title>cluster_TeamsModule_exports</title>
<polygon fill="none" stroke="black" points="324,-208 324,-260 606,-260 606,-208 324,-208"/>
</g>
<g id="clust6" class="cluster">
<title>cluster_TeamsModule_providers</title>
<polygon fill="none" stroke="black" points="378,-78 378,-130 1184,-130 1184,-78 378,-78"/>
</g>
<g id="clust3" class="cluster">
<title>cluster_TeamsModule_imports</title>
<polygon fill="none" stroke="black" points="16,-78 16,-130 370,-130 370,-78 16,-78"/>
</g>
<!-- CommonModule -->
<g id="node1" class="node">
<title>CommonModule</title>
<polygon fill="#8dd3c7" stroke="black" points="361.67,-122 358.67,-126 337.67,-126 334.67,-122 250.33,-122 250.33,-86 361.67,-86 361.67,-122"/>
<text text-anchor="middle" x="306" y="-99.8" font-family="Times,serif" font-size="14.00">CommonModule</text>
</g>
<!-- TeamsModule -->
<g id="node4" class="node">
<title>TeamsModule</title>
<polygon fill="#8dd3c7" stroke="black" points="500.37,-187 497.37,-191 476.37,-191 473.37,-187 403.63,-187 403.63,-151 500.37,-151 500.37,-187"/>
<text text-anchor="middle" x="452" y="-164.8" font-family="Times,serif" font-size="14.00">TeamsModule</text>
</g>
<!-- CommonModule&#45;&gt;TeamsModule -->
<g id="edge1" class="edge">
<title>CommonModule&#45;&gt;TeamsModule</title>
<path fill="none" stroke="black" d="M346.72,-122.03C346.72,-138.4 346.72,-160 346.72,-160 346.72,-160 393.37,-160 393.37,-160"/>
<polygon fill="black" stroke="black" points="393.37,-163.5 403.37,-160 393.37,-156.5 393.37,-163.5"/>
</g>
<!-- SharedModule -->
<g id="node2" class="node">
<title>SharedModule</title>
<polygon fill="#8dd3c7" stroke="black" points="232.42,-122 229.42,-126 208.42,-126 205.42,-122 133.58,-122 133.58,-86 232.42,-86 232.42,-122"/>
<text text-anchor="middle" x="183" y="-99.8" font-family="Times,serif" font-size="14.00">SharedModule</text>
</g>
<!-- SharedModule&#45;&gt;TeamsModule -->
<g id="edge2" class="edge">
<title>SharedModule&#45;&gt;TeamsModule</title>
<path fill="none" stroke="black" d="M183,-122.11C183,-141.34 183,-169 183,-169 183,-169 393.6,-169 393.6,-169"/>
<polygon fill="black" stroke="black" points="393.6,-172.5 403.6,-169 393.6,-165.5 393.6,-172.5"/>
</g>
<!-- UsersModule -->
<g id="node3" class="node">
<title>UsersModule</title>
<polygon fill="#8dd3c7" stroke="black" points="115.92,-122 112.92,-126 91.92,-126 88.92,-122 24.08,-122 24.08,-86 115.92,-86 115.92,-122"/>
<text text-anchor="middle" x="70" y="-99.8" font-family="Times,serif" font-size="14.00">UsersModule</text>
</g>
<!-- UsersModule&#45;&gt;TeamsModule -->
<g id="edge3" class="edge">
<title>UsersModule&#45;&gt;TeamsModule</title>
<path fill="none" stroke="black" d="M70,-122.29C70,-144.21 70,-178 70,-178 70,-178 393.62,-178 393.62,-178"/>
<polygon fill="black" stroke="black" points="393.62,-181.5 403.62,-178 393.62,-174.5 393.62,-181.5"/>
</g>
<!-- TeamAnnotatorService  -->
<g id="node5" class="node">
<title>TeamAnnotatorService </title>
<polygon fill="#fb8072" stroke="black" points="598.11,-252 447.89,-252 447.89,-216 598.11,-216 598.11,-252"/>
<text text-anchor="middle" x="523" y="-229.8" font-family="Times,serif" font-size="14.00">TeamAnnotatorService </text>
</g>
<!-- TeamsModule&#45;&gt;TeamAnnotatorService  -->
<g id="edge4" class="edge">
<title>TeamsModule&#45;&gt;TeamAnnotatorService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M474.19,-187.11C474.19,-187.11 474.19,-205.99 474.19,-205.99"/>
<polygon fill="black" stroke="black" points="470.69,-205.99 474.19,-215.99 477.69,-205.99 470.69,-205.99"/>
</g>
<!-- TeamsService  -->
<g id="node6" class="node">
<title>TeamsService </title>
<polygon fill="#fb8072" stroke="black" points="430.29,-252 331.71,-252 331.71,-216 430.29,-216 430.29,-252"/>
<text text-anchor="middle" x="381" y="-229.8" font-family="Times,serif" font-size="14.00">TeamsService </text>
</g>
<!-- TeamsModule&#45;&gt;TeamsService  -->
<g id="edge5" class="edge">
<title>TeamsModule&#45;&gt;TeamsService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M416.98,-187.11C416.98,-187.11 416.98,-205.99 416.98,-205.99"/>
<polygon fill="black" stroke="black" points="413.48,-205.99 416.98,-215.99 420.48,-205.99 413.48,-205.99"/>
</g>
<!-- BusinessHoursValidatorService -->
<g id="node7" class="node">
<title>BusinessHoursValidatorService</title>
<ellipse fill="#fdb462" stroke="black" cx="1042" cy="-104" rx="134.28" ry="18"/>
<text text-anchor="middle" x="1042" y="-99.8" font-family="Times,serif" font-size="14.00">BusinessHoursValidatorService</text>
</g>
<!-- BusinessHoursValidatorService&#45;&gt;TeamsModule -->
<g id="edge6" class="edge">
<title>BusinessHoursValidatorService&#45;&gt;TeamsModule</title>
<path fill="none" stroke="black" d="M1042,-122.29C1042,-144.21 1042,-178 1042,-178 1042,-178 510.53,-178 510.53,-178"/>
<polygon fill="black" stroke="black" points="510.53,-174.5 500.53,-178 510.53,-181.5 510.53,-174.5"/>
</g>
<!-- SharedService -->
<g id="node8" class="node">
<title>SharedService</title>
<ellipse fill="#fdb462" stroke="black" cx="823" cy="-104" rx="66.61" ry="18"/>
<text text-anchor="middle" x="823" y="-99.8" font-family="Times,serif" font-size="14.00">SharedService</text>
</g>
<!-- SharedService&#45;&gt;TeamsModule -->
<g id="edge7" class="edge">
<title>SharedService&#45;&gt;TeamsModule</title>
<path fill="none" stroke="black" d="M823,-122.11C823,-141.34 823,-169 823,-169 823,-169 510.5,-169 510.5,-169"/>
<polygon fill="black" stroke="black" points="510.5,-165.5 500.5,-169 510.5,-172.5 510.5,-165.5"/>
</g>
<!-- TeamAnnotatorService -->
<g id="node9" class="node">
<title>TeamAnnotatorService</title>
<ellipse fill="#fdb462" stroke="black" cx="637" cy="-104" rx="101.26" ry="18"/>
<text text-anchor="middle" x="637" y="-99.8" font-family="Times,serif" font-size="14.00">TeamAnnotatorService</text>
</g>
<!-- TeamAnnotatorService&#45;&gt;TeamsModule -->
<g id="edge8" class="edge">
<title>TeamAnnotatorService&#45;&gt;TeamsModule</title>
<path fill="none" stroke="black" d="M566.96,-116.99C566.96,-133.53 566.96,-160 566.96,-160 566.96,-160 510.42,-160 510.42,-160"/>
<polygon fill="black" stroke="black" points="510.42,-156.5 500.42,-160 510.42,-163.5 510.42,-156.5"/>
</g>
<!-- TeamsService -->
<g id="node10" class="node">
<title>TeamsService</title>
<ellipse fill="#fdb462" stroke="black" cx="452" cy="-104" rx="65.97" ry="18"/>
<text text-anchor="middle" x="452" y="-99.8" font-family="Times,serif" font-size="14.00">TeamsService</text>
</g>
<!-- TeamsService&#45;&gt;TeamsModule -->
<g id="edge9" class="edge">
<title>TeamsService&#45;&gt;TeamsModule</title>
<path fill="none" stroke="black" d="M452,-122.11C452,-122.11 452,-140.99 452,-140.99"/>
<polygon fill="black" stroke="black" points="448.5,-140.99 452,-150.99 455.5,-140.99 448.5,-140.99"/>
</g>
</g>
</svg>

    </div>
    <i id="fullscreen" class="icon ion-ios-resize module-graph-fullscreen-btn" aria-hidden="true"></i>
    <div class="btn-group size-buttons">
        <button id="zoom-in" class="btn btn-default btn-sm">Zoom in</button>
        <button id="reset" class="btn btn-default btn-sm">Reset</button>
        <button id="zoom-out" class="btn btn-default btn-sm">Zoom out</button>
    </div>
</div>
<script src="../js/libs/svg-pan-zoom.min.js"></script>
<script src="../js/svg-pan-zoom.controls.js"></script>

<ul class="nav nav-tabs" role="tablist">
    <li class="nav-item">
        <a href="#info" 
            class="nav-link"
            class="nav-link active"
            role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
    </li>
    <li class="nav-item">
        <a href="#source" 
            class="nav-link"
            
            role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
    </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">

        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/teams/teams.module.ts</code>
        </p>





        <div class="container-fluid module">
            <div class="row">
                <div class="col-sm-3">
                    <h3>Providers<a href="https://angular.io/api/core/NgModule#providers" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module providers"><span class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../injectables/BusinessHoursValidatorService.html">BusinessHoursValidatorService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/SharedService.html">SharedService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/TeamAnnotatorService.html">TeamAnnotatorService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/TeamsService.html">TeamsService</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Controllers<a href="https://docs.nestjs.com/controllers" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module controllers"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../controllers/TeamsController.html">TeamsController</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../controllers/TeamsGrpcController.html">TeamsGrpcController</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Imports<a href="https://angular.io/api/core/NgModule#imports" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module imports"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../modules/CommonModule.html">CommonModule</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../modules/SharedModule.html">SharedModule</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../modules/UsersModule.html">UsersModule</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Exports<a href="https://angular.io/api/core/NgModule#exports" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module exports"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../injectables/TeamAnnotatorService.html">TeamAnnotatorService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/TeamsService.html">TeamsService</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>


    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Module } from &quot;@nestjs/common&quot;;
import { TypeOrmModule } from &quot;@nestjs/typeorm&quot;;
import { RedisCacheProvider } from &quot;@repo/nestjs-commons/cache&quot;;
import {
  ApiKeyGrpcStrategy,
  AuthenticationGrpcClient,
  BearerTokenGrpcStrategy,
  GrpcAuthGuard,
  UserOrgInternalGrpcStrategy,
} from &quot;@repo/nestjs-commons/guards&quot;;
import {
  BusinessHoursConfig,
  BusinessHoursConfigRepository,
  CachedBusinessHoursConfigRepository,
  CachedTeamCapacityRepository,
  CachedTeamConfigurationRepository,
  CachedTeamRepository,
  CachedTeamRoutingRulesRepository,
  Team,
  TeamCapacity,
  TeamCapacityRepository,
  TeamConfiguration,
  TeamConfigurationRepository,
  TeamMember,
  TeamMemberRepository,
  TeamRepository,
  TeamRoutingRules,
  TeamRoutingRulesRepository,
  TransactionService,
} from &quot;@repo/thena-platform-entities&quot;;
import { CommonModule } from &quot;../common/common.module&quot;;
import { BusinessHoursValidatorService } from &quot;../common/services/business-hours-validation.service&quot;;
import { SharedModule } from &quot;../shared/shared.module&quot;;
import { SharedService } from &quot;../shared/shared.service&quot;;
import { UsersModule } from &quot;../users/users.module&quot;;
import { TeamsGrpcController } from &quot;./controllers/teams-grpc.controller&quot;;
import { TeamsController } from &quot;./controllers/teams.controller&quot;;
import { TeamAnnotatorService } from &quot;./services/team-annotator.service&quot;;
import { TeamsService } from &quot;./services/teams.service&quot;;

@Module({
  imports: [
    CommonModule,
    SharedModule,
    TypeOrmModule.forFeature([
      Team,
      TeamRepository,
      TeamConfiguration,
      TeamConfigurationRepository,
      TeamMember,
      TeamMemberRepository,
      BusinessHoursConfig,
      BusinessHoursConfigRepository,
      TeamCapacity,
      TeamCapacityRepository,
      TeamRoutingRules,
      TeamRoutingRulesRepository,
    ]),
    UsersModule,
  ],
  controllers: [TeamsController, TeamsGrpcController],
  providers: [
    GrpcAuthGuard,
    ApiKeyGrpcStrategy,
    BearerTokenGrpcStrategy,
    UserOrgInternalGrpcStrategy,
    AuthenticationGrpcClient,
    TransactionService,
    BusinessHoursValidatorService,
    TeamsService,
    TeamRepository,
    CachedTeamRepository,
    TeamMemberRepository,
    TeamConfigurationRepository,
    CachedTeamConfigurationRepository,
    TeamCapacityRepository,
    CachedTeamCapacityRepository,
    TeamRoutingRulesRepository,
    SharedService,
    CachedTeamRoutingRulesRepository,
    BusinessHoursConfigRepository,
    CachedBusinessHoursConfigRepository,
    RedisCacheProvider,
    TeamAnnotatorService,
  ],
  exports: [TeamsService, TeamAnnotatorService],
})
export class TeamsModule {}
</code></pre>
    </div>
</div>

















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'module';
            var COMPODOC_CURRENT_PAGE_URL = 'TeamsModule.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
