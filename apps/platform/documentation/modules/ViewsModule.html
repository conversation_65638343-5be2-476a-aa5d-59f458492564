<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content module">
                   <div class="content-data">



<ol class="breadcrumb">
    <li class="breadcrumb-item">Modules</li>
    <li class="breadcrumb-item" >ViewsModule</li>
</ol>

<div class="text-center module-graph-container">
    <div id="module-graph-svg">
        <?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: dependencies Pages: 1 -->
<svg width="630pt" height="284pt"
 viewBox="0.00 0.00 630.00 284.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 280)">
<title>dependencies</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-280 626,-280 626,4 -4,4"/>
<text text-anchor="start" x="290.01" y="-42.4" font-family="Times-12" font-weight="bold" font-size="14.00">Legend</text>
<polygon fill="#ffffb3" stroke="transparent" points="77,-10 77,-30 97,-30 97,-10 77,-10"/>
<text text-anchor="start" x="100.63" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Declarations</text>
<polygon fill="#8dd3c7" stroke="transparent" points="190,-10 190,-30 210,-30 210,-10 190,-10"/>
<text text-anchor="start" x="213.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Module</text>
<polygon fill="#80b1d3" stroke="transparent" points="276,-10 276,-30 296,-30 296,-10 276,-10"/>
<text text-anchor="start" x="299.78" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Bootstrap</text>
<polygon fill="#fdb462" stroke="transparent" points="373,-10 373,-30 393,-30 393,-10 373,-10"/>
<text text-anchor="start" x="396.67" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Providers</text>
<polygon fill="#fb8072" stroke="transparent" points="469,-10 469,-30 489,-30 489,-10 469,-10"/>
<text text-anchor="start" x="492.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Exports</text>
<g id="clust1" class="cluster">
<title>cluster_ViewsModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="8,-70 8,-268 614,-268 614,-70 8,-70"/>
</g>
<g id="clust4" class="cluster">
<title>cluster_ViewsModule_exports</title>
<polygon fill="none" stroke="black" points="129,-208 129,-260 540,-260 540,-208 129,-208"/>
</g>
<g id="clust3" class="cluster">
<title>cluster_ViewsModule_imports</title>
<polygon fill="none" stroke="black" points="364,-78 364,-130 606,-130 606,-78 364,-78"/>
</g>
<g id="clust6" class="cluster">
<title>cluster_ViewsModule_providers</title>
<polygon fill="none" stroke="black" points="16,-78 16,-130 356,-130 356,-78 16,-78"/>
</g>
<!-- CommonModule -->
<g id="node1" class="node">
<title>CommonModule</title>
<polygon fill="#8dd3c7" stroke="black" points="597.67,-122 594.67,-126 573.67,-126 570.67,-122 486.33,-122 486.33,-86 597.67,-86 597.67,-122"/>
<text text-anchor="middle" x="542" y="-99.8" font-family="Times,serif" font-size="14.00">CommonModule</text>
</g>
<!-- ViewsModule -->
<g id="node3" class="node">
<title>ViewsModule</title>
<polygon fill="#8dd3c7" stroke="black" points="396.81,-187 393.81,-191 372.81,-191 369.81,-187 301.19,-187 301.19,-151 396.81,-151 396.81,-187"/>
<text text-anchor="middle" x="349" y="-164.8" font-family="Times,serif" font-size="14.00">ViewsModule</text>
</g>
<!-- CommonModule&#45;&gt;ViewsModule -->
<g id="edge1" class="edge">
<title>CommonModule&#45;&gt;ViewsModule</title>
<path fill="none" stroke="black" d="M509.26,-122.02C509.26,-139.37 509.26,-163 509.26,-163 509.26,-163 407.08,-163 407.08,-163"/>
<polygon fill="black" stroke="black" points="407.08,-159.5 397.08,-163 407.08,-166.5 407.08,-159.5"/>
</g>
<!-- TeamsModule -->
<g id="node2" class="node">
<title>TeamsModule</title>
<polygon fill="#8dd3c7" stroke="black" points="468.37,-122 465.37,-126 444.37,-126 441.37,-122 371.63,-122 371.63,-86 468.37,-86 468.37,-122"/>
<text text-anchor="middle" x="420" y="-99.8" font-family="Times,serif" font-size="14.00">TeamsModule</text>
</g>
<!-- TeamsModule&#45;&gt;ViewsModule -->
<g id="edge2" class="edge">
<title>TeamsModule&#45;&gt;ViewsModule</title>
<path fill="none" stroke="black" d="M384.11,-122.11C384.11,-122.11 384.11,-140.99 384.11,-140.99"/>
<polygon fill="black" stroke="black" points="380.61,-140.99 384.11,-150.99 387.61,-140.99 380.61,-140.99"/>
</g>
<!-- ViewsRepository  -->
<g id="node4" class="node">
<title>ViewsRepository </title>
<polygon fill="#fb8072" stroke="black" points="532.2,-252 415.8,-252 415.8,-216 532.2,-216 532.2,-252"/>
<text text-anchor="middle" x="474" y="-229.8" font-family="Times,serif" font-size="14.00">ViewsRepository </text>
</g>
<!-- ViewsModule&#45;&gt;ViewsRepository  -->
<g id="edge3" class="edge">
<title>ViewsModule&#45;&gt;ViewsRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M396.88,-175C419.68,-175 442.04,-175 442.04,-175 442.04,-175 442.04,-205.98 442.04,-205.98"/>
<polygon fill="black" stroke="black" points="438.54,-205.98 442.04,-215.98 445.54,-205.98 438.54,-205.98"/>
</g>
<!-- ViewsService  -->
<g id="node5" class="node">
<title>ViewsService </title>
<polygon fill="#fb8072" stroke="black" points="397.74,-252 300.26,-252 300.26,-216 397.74,-216 397.74,-252"/>
<text text-anchor="middle" x="349" y="-229.8" font-family="Times,serif" font-size="14.00">ViewsService </text>
</g>
<!-- ViewsModule&#45;&gt;ViewsService  -->
<g id="edge4" class="edge">
<title>ViewsModule&#45;&gt;ViewsService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M349,-187.11C349,-187.11 349,-205.99 349,-205.99"/>
<polygon fill="black" stroke="black" points="345.5,-205.99 349,-215.99 352.5,-205.99 345.5,-205.99"/>
</g>
<!-- ViewsTypeRepository  -->
<g id="node6" class="node">
<title>ViewsTypeRepository </title>
<polygon fill="#fb8072" stroke="black" points="282.97,-252 137.03,-252 137.03,-216 282.97,-216 282.97,-252"/>
<text text-anchor="middle" x="210" y="-229.8" font-family="Times,serif" font-size="14.00">ViewsTypeRepository </text>
</g>
<!-- ViewsModule&#45;&gt;ViewsTypeRepository  -->
<g id="edge5" class="edge">
<title>ViewsModule&#45;&gt;ViewsTypeRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M301.21,-175C276.12,-175 250.54,-175 250.54,-175 250.54,-175 250.54,-205.98 250.54,-205.98"/>
<polygon fill="black" stroke="black" points="247.04,-205.98 250.54,-215.98 254.04,-205.98 247.04,-205.98"/>
</g>
<!-- ViewsService -->
<g id="node7" class="node">
<title>ViewsService</title>
<ellipse fill="#fdb462" stroke="black" cx="283" cy="-104" rx="64.82" ry="18"/>
<text text-anchor="middle" x="283" y="-99.8" font-family="Times,serif" font-size="14.00">ViewsService</text>
</g>
<!-- ViewsService&#45;&gt;ViewsModule -->
<g id="edge6" class="edge">
<title>ViewsService&#45;&gt;ViewsModule</title>
<path fill="none" stroke="black" d="M324.5,-118.1C324.5,-118.1 324.5,-140.72 324.5,-140.72"/>
<polygon fill="black" stroke="black" points="321,-140.72 324.5,-150.72 328,-140.72 321,-140.72"/>
</g>
<!-- ViewsTypesService -->
<g id="node8" class="node">
<title>ViewsTypesService</title>
<ellipse fill="#fdb462" stroke="black" cx="112" cy="-104" rx="88.02" ry="18"/>
<text text-anchor="middle" x="112" y="-99.8" font-family="Times,serif" font-size="14.00">ViewsTypesService</text>
</g>
<!-- ViewsTypesService&#45;&gt;ViewsModule -->
<g id="edge7" class="edge">
<title>ViewsTypesService&#45;&gt;ViewsModule</title>
<path fill="none" stroke="black" d="M168.76,-117.98C168.76,-135.43 168.76,-163 168.76,-163 168.76,-163 291.17,-163 291.17,-163"/>
<polygon fill="black" stroke="black" points="291.17,-166.5 301.17,-163 291.17,-159.5 291.17,-166.5"/>
</g>
</g>
</svg>

    </div>
    <i id="fullscreen" class="icon ion-ios-resize module-graph-fullscreen-btn" aria-hidden="true"></i>
    <div class="btn-group size-buttons">
        <button id="zoom-in" class="btn btn-default btn-sm">Zoom in</button>
        <button id="reset" class="btn btn-default btn-sm">Reset</button>
        <button id="zoom-out" class="btn btn-default btn-sm">Zoom out</button>
    </div>
</div>
<script src="../js/libs/svg-pan-zoom.min.js"></script>
<script src="../js/svg-pan-zoom.controls.js"></script>

<ul class="nav nav-tabs" role="tablist">
    <li class="nav-item">
        <a href="#info" 
            class="nav-link"
            class="nav-link active"
            role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
    </li>
    <li class="nav-item">
        <a href="#source" 
            class="nav-link"
            
            role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
    </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">

        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/views/views.module.ts</code>
        </p>





        <div class="container-fluid module">
            <div class="row">
                <div class="col-sm-3">
                    <h3>Providers<a href="https://angular.io/api/core/NgModule#providers" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module providers"><span class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../injectables/ViewsService.html">ViewsService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/ViewsTypesService.html">ViewsTypesService</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Controllers<a href="https://docs.nestjs.com/controllers" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module controllers"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../controllers/ViewsController.html">ViewsController</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../controllers/ViewsTypesController.html">ViewsTypesController</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Imports<a href="https://angular.io/api/core/NgModule#imports" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module imports"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../modules/CommonModule.html">CommonModule</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../modules/TeamsModule.html">TeamsModule</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Exports<a href="https://angular.io/api/core/NgModule#exports" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module exports"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../s/ViewsRepository.html">ViewsRepository</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/ViewsService.html">ViewsService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../s/ViewsTypeRepository.html">ViewsTypeRepository</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>


    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Module } from &quot;@nestjs/common&quot;;
import { TypeOrmModule } from &quot;@nestjs/typeorm&quot;;
import {
  Views,
  ViewsRepository,
  ViewsType,
  ViewsTypeRepository,
} from &quot;@repo/thena-platform-entities&quot;;
import { CommonModule } from &quot;../common/common.module&quot;;
import { TeamsModule } from &quot;../teams/teams.module&quot;;
import { ViewsTypesController } from &quot;./controllers/view-types.controller&quot;;
import { ViewsController } from &quot;./controllers/views.controller&quot;;
import { ViewsTypesService } from &quot;./services/views-types.service&quot;;
import { ViewsService } from &quot;./services/views.service&quot;;

@Module({
  imports: [
    CommonModule,
    TypeOrmModule.forFeature([
      Views,
      ViewsRepository,
      ViewsType,
      ViewsTypeRepository,
    ]),
    TeamsModule,
  ],
  controllers: [ViewsController, ViewsTypesController],
  providers: [
    ViewsService,
    ViewsRepository,
    ViewsTypeRepository,
    ViewsTypesService,
  ],
  exports: [ViewsService, ViewsRepository, ViewsTypeRepository],
})
export class ViewsModule {}
</code></pre>
    </div>
</div>

















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'module';
            var COMPODOC_CURRENT_PAGE_URL = 'ViewsModule.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
