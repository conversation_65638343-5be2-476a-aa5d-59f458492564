<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: dependencies Pages: 1 -->
<svg width="1691pt" height="284pt"
 viewBox="0.00 0.00 1691.00 284.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 280)">
<title>dependencies</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-280 1687,-280 1687,4 -4,4"/>
<text text-anchor="start" x="820.51" y="-42.4" font-family="Times-12" font-weight="bold" font-size="14.00">Legend</text>
<polygon fill="#ffffb3" stroke="transparent" points="607.5,-10 607.5,-30 627.5,-30 627.5,-10 607.5,-10"/>
<text text-anchor="start" x="631.13" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Declarations</text>
<polygon fill="#8dd3c7" stroke="transparent" points="720.5,-10 720.5,-30 740.5,-30 740.5,-10 720.5,-10"/>
<text text-anchor="start" x="744.23" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Module</text>
<polygon fill="#80b1d3" stroke="transparent" points="806.5,-10 806.5,-30 826.5,-30 826.5,-10 806.5,-10"/>
<text text-anchor="start" x="830.28" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Bootstrap</text>
<polygon fill="#fdb462" stroke="transparent" points="903.5,-10 903.5,-30 923.5,-30 923.5,-10 903.5,-10"/>
<text text-anchor="start" x="927.17" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Providers</text>
<polygon fill="#fb8072" stroke="transparent" points="999.5,-10 999.5,-30 1019.5,-30 1019.5,-10 999.5,-10"/>
<text text-anchor="start" x="1023.23" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Exports</text>
<g id="clust1" class="cluster">
<title>cluster_CommunicationsModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="8,-70 8,-268 1675,-268 1675,-70 8,-70"/>
</g>
<g id="clust3" class="cluster">
<title>cluster_CommunicationsModule_imports</title>
<polygon fill="none" stroke="black" points="936,-78 936,-130 1667,-130 1667,-78 936,-78"/>
</g>
<g id="clust6" class="cluster">
<title>cluster_CommunicationsModule_providers</title>
<polygon fill="none" stroke="black" points="16,-78 16,-130 928,-130 928,-78 16,-78"/>
</g>
<g id="clust4" class="cluster">
<title>cluster_CommunicationsModule_exports</title>
<polygon fill="none" stroke="black" points="964,-208 964,-260 1138,-260 1138,-208 964,-208"/>
</g>
<!-- AccountsModule -->
<g id="node1" class="node">
<title>AccountsModule</title>
<polygon fill="#8dd3c7" stroke="black" points="1659.42,-122 1656.42,-126 1635.42,-126 1632.42,-122 1546.58,-122 1546.58,-86 1659.42,-86 1659.42,-122"/>
<text text-anchor="middle" x="1603" y="-99.8" font-family="Times,serif" font-size="14.00">AccountsModule</text>
</g>
<!-- CommunicationsModule -->
<g id="node7" class="node">
<title>CommunicationsModule</title>
<polygon fill="#8dd3c7" stroke="black" points="1128.71,-187 1125.71,-191 1104.71,-191 1101.71,-187 973.29,-187 973.29,-151 1128.71,-151 1128.71,-187"/>
<text text-anchor="middle" x="1051" y="-164.8" font-family="Times,serif" font-size="14.00">CommunicationsModule</text>
</g>
<!-- AccountsModule&#45;&gt;CommunicationsModule -->
<g id="edge1" class="edge">
<title>AccountsModule&#45;&gt;CommunicationsModule</title>
<path fill="none" stroke="black" d="M1603,-122.01C1603,-144.49 1603,-180 1603,-180 1603,-180 1138.63,-180 1138.63,-180"/>
<polygon fill="black" stroke="black" points="1138.63,-176.5 1128.63,-180 1138.63,-183.5 1138.63,-176.5"/>
</g>
<!-- ActivitiesModule -->
<g id="node2" class="node">
<title>ActivitiesModule</title>
<polygon fill="#8dd3c7" stroke="black" points="1528.98,-122 1525.98,-126 1504.98,-126 1501.98,-122 1415.02,-122 1415.02,-86 1528.98,-86 1528.98,-122"/>
<text text-anchor="middle" x="1472" y="-99.8" font-family="Times,serif" font-size="14.00">ActivitiesModule</text>
</g>
<!-- ActivitiesModule&#45;&gt;CommunicationsModule -->
<g id="edge2" class="edge">
<title>ActivitiesModule&#45;&gt;CommunicationsModule</title>
<path fill="none" stroke="black" d="M1472,-122.13C1472,-142.57 1472,-173 1472,-173 1472,-173 1138.71,-173 1138.71,-173"/>
<polygon fill="black" stroke="black" points="1138.71,-169.5 1128.71,-173 1138.71,-176.5 1138.71,-169.5"/>
</g>
<!-- AuthModule -->
<g id="node3" class="node">
<title>AuthModule</title>
<polygon fill="#8dd3c7" stroke="black" points="1396.55,-122 1393.55,-126 1372.55,-126 1369.55,-122 1309.45,-122 1309.45,-86 1396.55,-86 1396.55,-122"/>
<text text-anchor="middle" x="1353" y="-99.8" font-family="Times,serif" font-size="14.00">AuthModule</text>
</g>
<!-- AuthModule&#45;&gt;CommunicationsModule -->
<g id="edge3" class="edge">
<title>AuthModule&#45;&gt;CommunicationsModule</title>
<path fill="none" stroke="black" d="M1353,-122.27C1353,-140.56 1353,-166 1353,-166 1353,-166 1138.77,-166 1138.77,-166"/>
<polygon fill="black" stroke="black" points="1138.77,-162.5 1128.77,-166 1138.77,-169.5 1138.77,-162.5"/>
</g>
<!-- CommonModule -->
<g id="node4" class="node">
<title>CommonModule</title>
<polygon fill="#8dd3c7" stroke="black" points="1290.67,-122 1287.67,-126 1266.67,-126 1263.67,-122 1179.33,-122 1179.33,-86 1290.67,-86 1290.67,-122"/>
<text text-anchor="middle" x="1235" y="-99.8" font-family="Times,serif" font-size="14.00">CommonModule</text>
</g>
<!-- CommonModule&#45;&gt;CommunicationsModule -->
<g id="edge4" class="edge">
<title>CommonModule&#45;&gt;CommunicationsModule</title>
<path fill="none" stroke="black" d="M1235,-122.01C1235,-138.05 1235,-159 1235,-159 1235,-159 1138.77,-159 1138.77,-159"/>
<polygon fill="black" stroke="black" points="1138.77,-155.5 1128.77,-159 1138.77,-162.5 1138.77,-155.5"/>
</g>
<!-- StorageModule -->
<g id="node5" class="node">
<title>StorageModule</title>
<polygon fill="#8dd3c7" stroke="black" points="1161.31,-122 1158.31,-126 1137.31,-126 1134.31,-122 1058.69,-122 1058.69,-86 1161.31,-86 1161.31,-122"/>
<text text-anchor="middle" x="1110" y="-99.8" font-family="Times,serif" font-size="14.00">StorageModule</text>
</g>
<!-- StorageModule&#45;&gt;CommunicationsModule -->
<g id="edge5" class="edge">
<title>StorageModule&#45;&gt;CommunicationsModule</title>
<path fill="none" stroke="black" d="M1093.73,-122.11C1093.73,-122.11 1093.73,-140.99 1093.73,-140.99"/>
<polygon fill="black" stroke="black" points="1090.23,-140.99 1093.73,-150.99 1097.23,-140.99 1090.23,-140.99"/>
</g>
<!-- TeamsModule -->
<g id="node6" class="node">
<title>TeamsModule</title>
<polygon fill="#8dd3c7" stroke="black" points="1040.37,-122 1037.37,-126 1016.37,-126 1013.37,-122 943.63,-122 943.63,-86 1040.37,-86 1040.37,-122"/>
<text text-anchor="middle" x="992" y="-99.8" font-family="Times,serif" font-size="14.00">TeamsModule</text>
</g>
<!-- TeamsModule&#45;&gt;CommunicationsModule -->
<g id="edge6" class="edge">
<title>TeamsModule&#45;&gt;CommunicationsModule</title>
<path fill="none" stroke="black" d="M1006.91,-122.11C1006.91,-122.11 1006.91,-140.99 1006.91,-140.99"/>
<polygon fill="black" stroke="black" points="1003.41,-140.99 1006.91,-150.99 1010.41,-140.99 1003.41,-140.99"/>
</g>
<!-- CommunicationsService  -->
<g id="node8" class="node">
<title>CommunicationsService </title>
<polygon fill="#fb8072" stroke="black" points="1129.64,-252 972.36,-252 972.36,-216 1129.64,-216 1129.64,-252"/>
<text text-anchor="middle" x="1051" y="-229.8" font-family="Times,serif" font-size="14.00">CommunicationsService </text>
</g>
<!-- CommunicationsModule&#45;&gt;CommunicationsService  -->
<g id="edge7" class="edge">
<title>CommunicationsModule&#45;&gt;CommunicationsService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1051,-187.11C1051,-187.11 1051,-205.99 1051,-205.99"/>
<polygon fill="black" stroke="black" points="1047.5,-205.99 1051,-215.99 1054.5,-205.99 1047.5,-205.99"/>
</g>
<!-- CommentSnsConsumer -->
<g id="node9" class="node">
<title>CommentSnsConsumer</title>
<ellipse fill="#fdb462" stroke="black" cx="817" cy="-104" rx="102.55" ry="18"/>
<text text-anchor="middle" x="817" y="-99.8" font-family="Times,serif" font-size="14.00">CommentSnsConsumer</text>
</g>
<!-- CommentSnsConsumer&#45;&gt;CommunicationsModule -->
<g id="edge8" class="edge">
<title>CommentSnsConsumer&#45;&gt;CommunicationsModule</title>
<path fill="none" stroke="black" d="M817,-122.01C817,-138.05 817,-159 817,-159 817,-159 963.08,-159 963.08,-159"/>
<polygon fill="black" stroke="black" points="963.08,-162.5 973.08,-159 963.08,-155.5 963.08,-162.5"/>
</g>
<!-- CommentsActionService -->
<g id="node10" class="node">
<title>CommentsActionService</title>
<ellipse fill="#fdb462" stroke="black" cx="588" cy="-104" rx="108.24" ry="18"/>
<text text-anchor="middle" x="588" y="-99.8" font-family="Times,serif" font-size="14.00">CommentsActionService</text>
</g>
<!-- CommentsActionService&#45;&gt;CommunicationsModule -->
<g id="edge9" class="edge">
<title>CommentsActionService&#45;&gt;CommunicationsModule</title>
<path fill="none" stroke="black" d="M588,-122.27C588,-140.56 588,-166 588,-166 588,-166 963.24,-166 963.24,-166"/>
<polygon fill="black" stroke="black" points="963.24,-169.5 973.24,-166 963.24,-162.5 963.24,-169.5"/>
</g>
<!-- CommunicationsService -->
<g id="node11" class="node">
<title>CommunicationsService</title>
<ellipse fill="#fdb462" stroke="black" cx="356" cy="-104" rx="105.96" ry="18"/>
<text text-anchor="middle" x="356" y="-99.8" font-family="Times,serif" font-size="14.00">CommunicationsService</text>
</g>
<!-- CommunicationsService&#45;&gt;CommunicationsModule -->
<g id="edge10" class="edge">
<title>CommunicationsService&#45;&gt;CommunicationsModule</title>
<path fill="none" stroke="black" d="M356,-122.13C356,-142.57 356,-173 356,-173 356,-173 963.3,-173 963.3,-173"/>
<polygon fill="black" stroke="black" points="963.3,-176.5 973.3,-173 963.3,-169.5 963.3,-176.5"/>
</g>
<!-- ReactionsActionService -->
<g id="node12" class="node">
<title>ReactionsActionService</title>
<ellipse fill="#fdb462" stroke="black" cx="128" cy="-104" rx="104.21" ry="18"/>
<text text-anchor="middle" x="128" y="-99.8" font-family="Times,serif" font-size="14.00">ReactionsActionService</text>
</g>
<!-- ReactionsActionService&#45;&gt;CommunicationsModule -->
<g id="edge11" class="edge">
<title>ReactionsActionService&#45;&gt;CommunicationsModule</title>
<path fill="none" stroke="black" d="M128,-122.01C128,-144.49 128,-180 128,-180 128,-180 963.1,-180 963.1,-180"/>
<polygon fill="black" stroke="black" points="963.1,-183.5 973.1,-180 963.1,-176.5 963.1,-183.5"/>
</g>
</g>
</svg>
