<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: dependencies Pages: 1 -->
<svg width="630pt" height="284pt"
 viewBox="0.00 0.00 630.00 284.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 280)">
<title>dependencies</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-280 626,-280 626,4 -4,4"/>
<text text-anchor="start" x="290.01" y="-42.4" font-family="Times-12" font-weight="bold" font-size="14.00">Legend</text>
<polygon fill="#ffffb3" stroke="transparent" points="77,-10 77,-30 97,-30 97,-10 77,-10"/>
<text text-anchor="start" x="100.63" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Declarations</text>
<polygon fill="#8dd3c7" stroke="transparent" points="190,-10 190,-30 210,-30 210,-10 190,-10"/>
<text text-anchor="start" x="213.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Module</text>
<polygon fill="#80b1d3" stroke="transparent" points="276,-10 276,-30 296,-30 296,-10 276,-10"/>
<text text-anchor="start" x="299.78" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Bootstrap</text>
<polygon fill="#fdb462" stroke="transparent" points="373,-10 373,-30 393,-30 393,-10 373,-10"/>
<text text-anchor="start" x="396.67" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Providers</text>
<polygon fill="#fb8072" stroke="transparent" points="469,-10 469,-30 489,-30 489,-10 469,-10"/>
<text text-anchor="start" x="492.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Exports</text>
<g id="clust1" class="cluster">
<title>cluster_ViewsModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="8,-70 8,-268 614,-268 614,-70 8,-70"/>
</g>
<g id="clust4" class="cluster">
<title>cluster_ViewsModule_exports</title>
<polygon fill="none" stroke="black" points="129,-208 129,-260 540,-260 540,-208 129,-208"/>
</g>
<g id="clust3" class="cluster">
<title>cluster_ViewsModule_imports</title>
<polygon fill="none" stroke="black" points="364,-78 364,-130 606,-130 606,-78 364,-78"/>
</g>
<g id="clust6" class="cluster">
<title>cluster_ViewsModule_providers</title>
<polygon fill="none" stroke="black" points="16,-78 16,-130 356,-130 356,-78 16,-78"/>
</g>
<!-- CommonModule -->
<g id="node1" class="node">
<title>CommonModule</title>
<polygon fill="#8dd3c7" stroke="black" points="597.67,-122 594.67,-126 573.67,-126 570.67,-122 486.33,-122 486.33,-86 597.67,-86 597.67,-122"/>
<text text-anchor="middle" x="542" y="-99.8" font-family="Times,serif" font-size="14.00">CommonModule</text>
</g>
<!-- ViewsModule -->
<g id="node3" class="node">
<title>ViewsModule</title>
<polygon fill="#8dd3c7" stroke="black" points="396.81,-187 393.81,-191 372.81,-191 369.81,-187 301.19,-187 301.19,-151 396.81,-151 396.81,-187"/>
<text text-anchor="middle" x="349" y="-164.8" font-family="Times,serif" font-size="14.00">ViewsModule</text>
</g>
<!-- CommonModule&#45;&gt;ViewsModule -->
<g id="edge1" class="edge">
<title>CommonModule&#45;&gt;ViewsModule</title>
<path fill="none" stroke="black" d="M509.26,-122.02C509.26,-139.37 509.26,-163 509.26,-163 509.26,-163 407.08,-163 407.08,-163"/>
<polygon fill="black" stroke="black" points="407.08,-159.5 397.08,-163 407.08,-166.5 407.08,-159.5"/>
</g>
<!-- TeamsModule -->
<g id="node2" class="node">
<title>TeamsModule</title>
<polygon fill="#8dd3c7" stroke="black" points="468.37,-122 465.37,-126 444.37,-126 441.37,-122 371.63,-122 371.63,-86 468.37,-86 468.37,-122"/>
<text text-anchor="middle" x="420" y="-99.8" font-family="Times,serif" font-size="14.00">TeamsModule</text>
</g>
<!-- TeamsModule&#45;&gt;ViewsModule -->
<g id="edge2" class="edge">
<title>TeamsModule&#45;&gt;ViewsModule</title>
<path fill="none" stroke="black" d="M384.11,-122.11C384.11,-122.11 384.11,-140.99 384.11,-140.99"/>
<polygon fill="black" stroke="black" points="380.61,-140.99 384.11,-150.99 387.61,-140.99 380.61,-140.99"/>
</g>
<!-- ViewsRepository  -->
<g id="node4" class="node">
<title>ViewsRepository </title>
<polygon fill="#fb8072" stroke="black" points="532.2,-252 415.8,-252 415.8,-216 532.2,-216 532.2,-252"/>
<text text-anchor="middle" x="474" y="-229.8" font-family="Times,serif" font-size="14.00">ViewsRepository </text>
</g>
<!-- ViewsModule&#45;&gt;ViewsRepository  -->
<g id="edge3" class="edge">
<title>ViewsModule&#45;&gt;ViewsRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M396.88,-175C419.68,-175 442.04,-175 442.04,-175 442.04,-175 442.04,-205.98 442.04,-205.98"/>
<polygon fill="black" stroke="black" points="438.54,-205.98 442.04,-215.98 445.54,-205.98 438.54,-205.98"/>
</g>
<!-- ViewsService  -->
<g id="node5" class="node">
<title>ViewsService </title>
<polygon fill="#fb8072" stroke="black" points="397.74,-252 300.26,-252 300.26,-216 397.74,-216 397.74,-252"/>
<text text-anchor="middle" x="349" y="-229.8" font-family="Times,serif" font-size="14.00">ViewsService </text>
</g>
<!-- ViewsModule&#45;&gt;ViewsService  -->
<g id="edge4" class="edge">
<title>ViewsModule&#45;&gt;ViewsService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M349,-187.11C349,-187.11 349,-205.99 349,-205.99"/>
<polygon fill="black" stroke="black" points="345.5,-205.99 349,-215.99 352.5,-205.99 345.5,-205.99"/>
</g>
<!-- ViewsTypeRepository  -->
<g id="node6" class="node">
<title>ViewsTypeRepository </title>
<polygon fill="#fb8072" stroke="black" points="282.97,-252 137.03,-252 137.03,-216 282.97,-216 282.97,-252"/>
<text text-anchor="middle" x="210" y="-229.8" font-family="Times,serif" font-size="14.00">ViewsTypeRepository </text>
</g>
<!-- ViewsModule&#45;&gt;ViewsTypeRepository  -->
<g id="edge5" class="edge">
<title>ViewsModule&#45;&gt;ViewsTypeRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M301.21,-175C276.12,-175 250.54,-175 250.54,-175 250.54,-175 250.54,-205.98 250.54,-205.98"/>
<polygon fill="black" stroke="black" points="247.04,-205.98 250.54,-215.98 254.04,-205.98 247.04,-205.98"/>
</g>
<!-- ViewsService -->
<g id="node7" class="node">
<title>ViewsService</title>
<ellipse fill="#fdb462" stroke="black" cx="283" cy="-104" rx="64.82" ry="18"/>
<text text-anchor="middle" x="283" y="-99.8" font-family="Times,serif" font-size="14.00">ViewsService</text>
</g>
<!-- ViewsService&#45;&gt;ViewsModule -->
<g id="edge6" class="edge">
<title>ViewsService&#45;&gt;ViewsModule</title>
<path fill="none" stroke="black" d="M324.5,-118.1C324.5,-118.1 324.5,-140.72 324.5,-140.72"/>
<polygon fill="black" stroke="black" points="321,-140.72 324.5,-150.72 328,-140.72 321,-140.72"/>
</g>
<!-- ViewsTypesService -->
<g id="node8" class="node">
<title>ViewsTypesService</title>
<ellipse fill="#fdb462" stroke="black" cx="112" cy="-104" rx="88.02" ry="18"/>
<text text-anchor="middle" x="112" y="-99.8" font-family="Times,serif" font-size="14.00">ViewsTypesService</text>
</g>
<!-- ViewsTypesService&#45;&gt;ViewsModule -->
<g id="edge7" class="edge">
<title>ViewsTypesService&#45;&gt;ViewsModule</title>
<path fill="none" stroke="black" d="M168.76,-117.98C168.76,-135.43 168.76,-163 168.76,-163 168.76,-163 291.17,-163 291.17,-163"/>
<polygon fill="black" stroke="black" points="291.17,-166.5 301.17,-163 291.17,-159.5 291.17,-166.5"/>
</g>
</g>
</svg>
