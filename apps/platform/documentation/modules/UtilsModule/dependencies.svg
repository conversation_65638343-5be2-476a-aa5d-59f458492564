<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: dependencies Pages: 1 -->
<svg width="504pt" height="284pt"
 viewBox="0.00 0.00 504.00 284.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 280)">
<title>dependencies</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-280 500,-280 500,4 -4,4"/>
<text text-anchor="start" x="227.01" y="-42.4" font-family="Times-12" font-weight="bold" font-size="14.00">Legend</text>
<polygon fill="#ffffb3" stroke="transparent" points="14,-10 14,-30 34,-30 34,-10 14,-10"/>
<text text-anchor="start" x="37.63" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Declarations</text>
<polygon fill="#8dd3c7" stroke="transparent" points="127,-10 127,-30 147,-30 147,-10 127,-10"/>
<text text-anchor="start" x="150.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Module</text>
<polygon fill="#80b1d3" stroke="transparent" points="213,-10 213,-30 233,-30 233,-10 213,-10"/>
<text text-anchor="start" x="236.78" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Bootstrap</text>
<polygon fill="#fdb462" stroke="transparent" points="310,-10 310,-30 330,-30 330,-10 310,-10"/>
<text text-anchor="start" x="333.67" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Providers</text>
<polygon fill="#fb8072" stroke="transparent" points="406,-10 406,-30 426,-30 426,-10 406,-10"/>
<text text-anchor="start" x="429.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Exports</text>
<g id="clust1" class="cluster">
<title>cluster_UtilsModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="25,-70 25,-268 471,-268 471,-70 25,-70"/>
</g>
<g id="clust4" class="cluster">
<title>cluster_UtilsModule_exports</title>
<polygon fill="none" stroke="black" points="33,-208 33,-260 463,-260 463,-208 33,-208"/>
</g>
<g id="clust3" class="cluster">
<title>cluster_UtilsModule_imports</title>
<polygon fill="none" stroke="black" points="220,-78 220,-130 334,-130 334,-78 220,-78"/>
</g>
<!-- ConfigModule -->
<g id="node1" class="node">
<title>ConfigModule</title>
<polygon fill="#8dd3c7" stroke="black" points="326.44,-122 323.44,-126 302.44,-126 299.44,-122 227.56,-122 227.56,-86 326.44,-86 326.44,-122"/>
<text text-anchor="middle" x="277" y="-99.8" font-family="Times,serif" font-size="14.00">ConfigModule</text>
</g>
<!-- UtilsModule -->
<g id="node2" class="node">
<title>UtilsModule</title>
<polygon fill="#8dd3c7" stroke="black" points="320.27,-187 317.27,-191 296.27,-191 293.27,-187 233.73,-187 233.73,-151 320.27,-151 320.27,-187"/>
<text text-anchor="middle" x="277" y="-164.8" font-family="Times,serif" font-size="14.00">UtilsModule</text>
</g>
<!-- ConfigModule&#45;&gt;UtilsModule -->
<g id="edge1" class="edge">
<title>ConfigModule&#45;&gt;UtilsModule</title>
<path fill="none" stroke="black" d="M277,-122.11C277,-122.11 277,-140.99 277,-140.99"/>
<polygon fill="black" stroke="black" points="273.5,-140.99 277,-150.99 280.5,-140.99 273.5,-140.99"/>
</g>
<!-- ...Object.values(utils)  -->
<g id="node3" class="node">
<title>...Object.values(utils) </title>
<polygon fill="#fb8072" stroke="black" points="455.02,-252 314.98,-252 314.98,-216 455.02,-216 455.02,-252"/>
<text text-anchor="middle" x="385" y="-229.8" font-family="Times,serif" font-size="14.00">...Object.values(utils) </text>
</g>
<!-- UtilsModule&#45;&gt;...Object.values(utils)  -->
<g id="edge2" class="edge">
<title>UtilsModule&#45;&gt;...Object.values(utils) </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M306.17,-187.11C306.17,-206.34 306.17,-234 306.17,-234 306.17,-234 307.04,-234 307.04,-234"/>
<polygon fill="black" stroke="black" points="304.9,-237.5 314.9,-234 304.9,-230.5 304.9,-237.5"/>
</g>
<!-- AUTH_GRPC_SERVICE_URL_TOKEN  -->
<g id="node4" class="node">
<title>AUTH_GRPC_SERVICE_URL_TOKEN </title>
<polygon fill="#fb8072" stroke="black" points="297.18,-252 40.82,-252 40.82,-216 297.18,-216 297.18,-252"/>
<text text-anchor="middle" x="169" y="-229.8" font-family="Times,serif" font-size="14.00">AUTH_GRPC_SERVICE_URL_TOKEN </text>
</g>
<!-- UtilsModule&#45;&gt;AUTH_GRPC_SERVICE_URL_TOKEN  -->
<g id="edge3" class="edge">
<title>UtilsModule&#45;&gt;AUTH_GRPC_SERVICE_URL_TOKEN </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M265.48,-187.11C265.48,-187.11 265.48,-205.99 265.48,-205.99"/>
<polygon fill="black" stroke="black" points="261.98,-205.99 265.48,-215.99 268.98,-205.99 261.98,-205.99"/>
</g>
</g>
</svg>
