<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: dependencies Pages: 1 -->
<svg width="504pt" height="284pt"
 viewBox="0.00 0.00 504.00 284.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 280)">
<title>dependencies</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-280 500,-280 500,4 -4,4"/>
<text text-anchor="start" x="227.01" y="-42.4" font-family="Times-12" font-weight="bold" font-size="14.00">Legend</text>
<polygon fill="#ffffb3" stroke="transparent" points="14,-10 14,-30 34,-30 34,-10 14,-10"/>
<text text-anchor="start" x="37.63" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Declarations</text>
<polygon fill="#8dd3c7" stroke="transparent" points="127,-10 127,-30 147,-30 147,-10 127,-10"/>
<text text-anchor="start" x="150.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Module</text>
<polygon fill="#80b1d3" stroke="transparent" points="213,-10 213,-30 233,-30 233,-10 213,-10"/>
<text text-anchor="start" x="236.78" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Bootstrap</text>
<polygon fill="#fdb462" stroke="transparent" points="310,-10 310,-30 330,-30 330,-10 310,-10"/>
<text text-anchor="start" x="333.67" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Providers</text>
<polygon fill="#fb8072" stroke="transparent" points="406,-10 406,-30 426,-30 426,-10 406,-10"/>
<text text-anchor="start" x="429.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Exports</text>
<g id="clust1" class="cluster">
<title>cluster_ActivitiesModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="86,-70 86,-268 410,-268 410,-70 86,-70"/>
</g>
<g id="clust3" class="cluster">
<title>cluster_ActivitiesModule_imports</title>
<polygon fill="none" stroke="black" points="274,-78 274,-130 402,-130 402,-78 274,-78"/>
</g>
<g id="clust4" class="cluster">
<title>cluster_ActivitiesModule_exports</title>
<polygon fill="none" stroke="black" points="192,-208 192,-260 324,-260 324,-208 192,-208"/>
</g>
<g id="clust6" class="cluster">
<title>cluster_ActivitiesModule_providers</title>
<polygon fill="none" stroke="black" points="94,-78 94,-130 266,-130 266,-78 94,-78"/>
</g>
<!-- CommonModule -->
<g id="node1" class="node">
<title>CommonModule</title>
<polygon fill="#8dd3c7" stroke="black" points="393.67,-122 390.67,-126 369.67,-126 366.67,-122 282.33,-122 282.33,-86 393.67,-86 393.67,-122"/>
<text text-anchor="middle" x="338" y="-99.8" font-family="Times,serif" font-size="14.00">CommonModule</text>
</g>
<!-- ActivitiesModule -->
<g id="node2" class="node">
<title>ActivitiesModule</title>
<polygon fill="#8dd3c7" stroke="black" points="314.98,-187 311.98,-191 290.98,-191 287.98,-187 201.02,-187 201.02,-151 314.98,-151 314.98,-187"/>
<text text-anchor="middle" x="258" y="-164.8" font-family="Times,serif" font-size="14.00">ActivitiesModule</text>
</g>
<!-- CommonModule&#45;&gt;ActivitiesModule -->
<g id="edge1" class="edge">
<title>CommonModule&#45;&gt;ActivitiesModule</title>
<path fill="none" stroke="black" d="M298.58,-122.11C298.58,-122.11 298.58,-140.99 298.58,-140.99"/>
<polygon fill="black" stroke="black" points="295.08,-140.99 298.58,-150.99 302.08,-140.99 295.08,-140.99"/>
</g>
<!-- ActivitiesService  -->
<g id="node3" class="node">
<title>ActivitiesService </title>
<polygon fill="#fb8072" stroke="black" points="315.91,-252 200.09,-252 200.09,-216 315.91,-216 315.91,-252"/>
<text text-anchor="middle" x="258" y="-229.8" font-family="Times,serif" font-size="14.00">ActivitiesService </text>
</g>
<!-- ActivitiesModule&#45;&gt;ActivitiesService  -->
<g id="edge2" class="edge">
<title>ActivitiesModule&#45;&gt;ActivitiesService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M258,-187.11C258,-187.11 258,-205.99 258,-205.99"/>
<polygon fill="black" stroke="black" points="254.5,-205.99 258,-215.99 261.5,-205.99 254.5,-205.99"/>
</g>
<!-- ActivitiesService -->
<g id="node4" class="node">
<title>ActivitiesService</title>
<ellipse fill="#fdb462" stroke="black" cx="180" cy="-104" rx="77.58" ry="18"/>
<text text-anchor="middle" x="180" y="-99.8" font-family="Times,serif" font-size="14.00">ActivitiesService</text>
</g>
<!-- ActivitiesService&#45;&gt;ActivitiesModule -->
<g id="edge3" class="edge">
<title>ActivitiesService&#45;&gt;ActivitiesModule</title>
<path fill="none" stroke="black" d="M229.27,-118.1C229.27,-118.1 229.27,-140.72 229.27,-140.72"/>
<polygon fill="black" stroke="black" points="225.77,-140.72 229.27,-150.72 232.77,-140.72 225.77,-140.72"/>
</g>
</g>
</svg>
