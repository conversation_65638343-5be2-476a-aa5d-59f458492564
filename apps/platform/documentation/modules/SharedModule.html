<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content module">
                   <div class="content-data">



<ol class="breadcrumb">
    <li class="breadcrumb-item">Modules</li>
    <li class="breadcrumb-item" >SharedModule</li>
</ol>

<div class="text-center module-graph-container">
    <div id="module-graph-svg">
        <?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: dependencies Pages: 1 -->
<svg width="1886pt" height="284pt"
 viewBox="0.00 0.00 1886.00 284.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 280)">
<title>dependencies</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-280 1882,-280 1882,4 -4,4"/>
<text text-anchor="start" x="918.01" y="-42.4" font-family="Times-12" font-weight="bold" font-size="14.00">Legend</text>
<polygon fill="#ffffb3" stroke="transparent" points="705,-10 705,-30 725,-30 725,-10 705,-10"/>
<text text-anchor="start" x="728.63" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Declarations</text>
<polygon fill="#8dd3c7" stroke="transparent" points="818,-10 818,-30 838,-30 838,-10 818,-10"/>
<text text-anchor="start" x="841.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Module</text>
<polygon fill="#80b1d3" stroke="transparent" points="904,-10 904,-30 924,-30 924,-10 904,-10"/>
<text text-anchor="start" x="927.78" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Bootstrap</text>
<polygon fill="#fdb462" stroke="transparent" points="1001,-10 1001,-30 1021,-30 1021,-10 1001,-10"/>
<text text-anchor="start" x="1024.67" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Providers</text>
<polygon fill="#fb8072" stroke="transparent" points="1097,-10 1097,-30 1117,-30 1117,-10 1097,-10"/>
<text text-anchor="start" x="1120.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Exports</text>
<g id="clust1" class="cluster">
<title>cluster_SharedModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="8,-70 8,-268 1870,-268 1870,-70 8,-70"/>
</g>
<g id="clust4" class="cluster">
<title>cluster_SharedModule_exports</title>
<polygon fill="none" stroke="black" points="16,-208 16,-260 1862,-260 1862,-208 16,-208"/>
</g>
<g id="clust6" class="cluster">
<title>cluster_SharedModule_providers</title>
<polygon fill="none" stroke="black" points="869,-78 869,-130 1019,-130 1019,-78 869,-78"/>
</g>
<!-- CachedTeamRepository  -->
<g id="node1" class="node">
<title>CachedTeamRepository </title>
<polygon fill="#fb8072" stroke="black" points="1854.29,-252 1699.71,-252 1699.71,-216 1854.29,-216 1854.29,-252"/>
<text text-anchor="middle" x="1777" y="-229.8" font-family="Times,serif" font-size="14.00">CachedTeamRepository </text>
</g>
<!-- FormFieldEventRepository  -->
<g id="node2" class="node">
<title>FormFieldEventRepository </title>
<polygon fill="#fb8072" stroke="black" points="1681.21,-252 1508.79,-252 1508.79,-216 1681.21,-216 1681.21,-252"/>
<text text-anchor="middle" x="1595" y="-229.8" font-family="Times,serif" font-size="14.00">FormFieldEventRepository </text>
</g>
<!-- FormRepository  -->
<g id="node3" class="node">
<title>FormRepository </title>
<polygon fill="#fb8072" stroke="black" points="1490.77,-252 1379.23,-252 1379.23,-216 1490.77,-216 1490.77,-252"/>
<text text-anchor="middle" x="1435" y="-229.8" font-family="Times,serif" font-size="14.00">FormRepository </text>
</g>
<!-- OrganizationRepository  -->
<g id="node4" class="node">
<title>OrganizationRepository </title>
<polygon fill="#fb8072" stroke="black" points="1361.02,-252 1206.98,-252 1206.98,-216 1361.02,-216 1361.02,-252"/>
<text text-anchor="middle" x="1284" y="-229.8" font-family="Times,serif" font-size="14.00">OrganizationRepository </text>
</g>
<!-- SharedService  -->
<g id="node5" class="node">
<title>SharedService </title>
<polygon fill="#fb8072" stroke="black" points="1189.35,-252 1088.65,-252 1088.65,-216 1189.35,-216 1189.35,-252"/>
<text text-anchor="middle" x="1139" y="-229.8" font-family="Times,serif" font-size="14.00">SharedService </text>
</g>
<!-- TagRepository  -->
<g id="node6" class="node">
<title>TagRepository </title>
<polygon fill="#fb8072" stroke="black" points="1070.21,-252 967.79,-252 967.79,-216 1070.21,-216 1070.21,-252"/>
<text text-anchor="middle" x="1019" y="-229.8" font-family="Times,serif" font-size="14.00">TagRepository </text>
</g>
<!-- TeamMemberRepository  -->
<g id="node7" class="node">
<title>TeamMemberRepository </title>
<polygon fill="#fb8072" stroke="black" points="950.24,-252 789.76,-252 789.76,-216 950.24,-216 950.24,-252"/>
<text text-anchor="middle" x="870" y="-229.8" font-family="Times,serif" font-size="14.00">TeamMemberRepository </text>
</g>
<!-- TeamRepository  -->
<g id="node8" class="node">
<title>TeamRepository </title>
<polygon fill="#fb8072" stroke="black" points="771.31,-252 658.69,-252 658.69,-216 771.31,-216 771.31,-252"/>
<text text-anchor="middle" x="715" y="-229.8" font-family="Times,serif" font-size="14.00">TeamRepository </text>
</g>
<!-- TicketPriorityRepository  -->
<g id="node9" class="node">
<title>TicketPriorityRepository </title>
<polygon fill="#fb8072" stroke="black" points="640.98,-252 481.02,-252 481.02,-216 640.98,-216 640.98,-252"/>
<text text-anchor="middle" x="561" y="-229.8" font-family="Times,serif" font-size="14.00">TicketPriorityRepository </text>
</g>
<!-- TicketStatusRepository  -->
<g id="node10" class="node">
<title>TicketStatusRepository </title>
<polygon fill="#fb8072" stroke="black" points="463.42,-252 312.58,-252 312.58,-216 463.42,-216 463.42,-252"/>
<text text-anchor="middle" x="388" y="-229.8" font-family="Times,serif" font-size="14.00">TicketStatusRepository </text>
</g>
<!-- TicketTypeRepository  -->
<g id="node11" class="node">
<title>TicketTypeRepository </title>
<polygon fill="#fb8072" stroke="black" points="294.97,-252 149.03,-252 149.03,-216 294.97,-216 294.97,-252"/>
<text text-anchor="middle" x="222" y="-229.8" font-family="Times,serif" font-size="14.00">TicketTypeRepository </text>
</g>
<!-- UserRepository  -->
<g id="node12" class="node">
<title>UserRepository </title>
<polygon fill="#fb8072" stroke="black" points="131.86,-252 24.14,-252 24.14,-216 131.86,-216 131.86,-252"/>
<text text-anchor="middle" x="78" y="-229.8" font-family="Times,serif" font-size="14.00">UserRepository </text>
</g>
<!-- SharedModule -->
<g id="node13" class="node">
<title>SharedModule</title>
<polygon fill="#8dd3c7" stroke="black" points="993.42,-187 990.42,-191 969.42,-191 966.42,-187 894.58,-187 894.58,-151 993.42,-151 993.42,-187"/>
<text text-anchor="middle" x="944" y="-164.8" font-family="Times,serif" font-size="14.00">SharedModule</text>
</g>
<!-- SharedModule&#45;&gt;CachedTeamRepository  -->
<g id="edge1" class="edge">
<title>SharedModule&#45;&gt;CachedTeamRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M993.31,-157C1172.25,-157 1777,-157 1777,-157 1777,-157 1777,-205.75 1777,-205.75"/>
<polygon fill="black" stroke="black" points="1773.5,-205.75 1777,-215.75 1780.5,-205.75 1773.5,-205.75"/>
</g>
<!-- SharedModule&#45;&gt;FormFieldEventRepository  -->
<g id="edge2" class="edge">
<title>SharedModule&#45;&gt;FormFieldEventRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M993.47,-163C1145.39,-163 1595,-163 1595,-163 1595,-163 1595,-205.72 1595,-205.72"/>
<polygon fill="black" stroke="black" points="1591.5,-205.72 1595,-215.72 1598.5,-205.72 1591.5,-205.72"/>
</g>
<!-- SharedModule&#45;&gt;FormRepository  -->
<g id="edge3" class="edge">
<title>SharedModule&#45;&gt;FormRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M993.33,-169C1117.84,-169 1435,-169 1435,-169 1435,-169 1435,-205.89 1435,-205.89"/>
<polygon fill="black" stroke="black" points="1431.5,-205.89 1435,-215.89 1438.5,-205.89 1431.5,-205.89"/>
</g>
<!-- SharedModule&#45;&gt;OrganizationRepository  -->
<g id="edge4" class="edge">
<title>SharedModule&#45;&gt;OrganizationRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M993.44,-175C1087.74,-175 1284,-175 1284,-175 1284,-175 1284,-205.98 1284,-205.98"/>
<polygon fill="black" stroke="black" points="1280.5,-205.98 1284,-215.98 1287.5,-205.98 1280.5,-205.98"/>
</g>
<!-- SharedModule&#45;&gt;SharedService  -->
<g id="edge5" class="edge">
<title>SharedModule&#45;&gt;SharedService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M993.46,-181C1051.23,-181 1139,-181 1139,-181 1139,-181 1139,-205.76 1139,-205.76"/>
<polygon fill="black" stroke="black" points="1135.5,-205.76 1139,-215.76 1142.5,-205.76 1135.5,-205.76"/>
</g>
<!-- SharedModule&#45;&gt;TagRepository  -->
<g id="edge6" class="edge">
<title>SharedModule&#45;&gt;TagRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M980.43,-187.11C980.43,-187.11 980.43,-205.99 980.43,-205.99"/>
<polygon fill="black" stroke="black" points="976.93,-205.99 980.43,-215.99 983.93,-205.99 976.93,-205.99"/>
</g>
<!-- SharedModule&#45;&gt;TeamMemberRepository  -->
<g id="edge7" class="edge">
<title>SharedModule&#45;&gt;TeamMemberRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M922.45,-187.11C922.45,-187.11 922.45,-205.99 922.45,-205.99"/>
<polygon fill="black" stroke="black" points="918.95,-205.99 922.45,-215.99 925.95,-205.99 918.95,-205.99"/>
</g>
<!-- SharedModule&#45;&gt;TeamRepository  -->
<g id="edge8" class="edge">
<title>SharedModule&#45;&gt;TeamRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M894.59,-181C827.27,-181 715,-181 715,-181 715,-181 715,-205.76 715,-205.76"/>
<polygon fill="black" stroke="black" points="711.5,-205.76 715,-215.76 718.5,-205.76 711.5,-205.76"/>
</g>
<!-- SharedModule&#45;&gt;TicketPriorityRepository  -->
<g id="edge9" class="edge">
<title>SharedModule&#45;&gt;TicketPriorityRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M894.71,-175C791.27,-175 561,-175 561,-175 561,-175 561,-205.98 561,-205.98"/>
<polygon fill="black" stroke="black" points="557.5,-205.98 561,-215.98 564.5,-205.98 557.5,-205.98"/>
</g>
<!-- SharedModule&#45;&gt;TicketStatusRepository  -->
<g id="edge10" class="edge">
<title>SharedModule&#45;&gt;TicketStatusRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M894.66,-169C758.58,-169 388,-169 388,-169 388,-169 388,-205.89 388,-205.89"/>
<polygon fill="black" stroke="black" points="384.5,-205.89 388,-215.89 391.5,-205.89 384.5,-205.89"/>
</g>
<!-- SharedModule&#45;&gt;TicketTypeRepository  -->
<g id="edge11" class="edge">
<title>SharedModule&#45;&gt;TicketTypeRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M894.66,-163C731.85,-163 222,-163 222,-163 222,-163 222,-205.72 222,-205.72"/>
<polygon fill="black" stroke="black" points="218.5,-205.72 222,-215.72 225.5,-205.72 218.5,-205.72"/>
</g>
<!-- SharedModule&#45;&gt;UserRepository  -->
<g id="edge12" class="edge">
<title>SharedModule&#45;&gt;UserRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M894.64,-157C711.04,-157 78,-157 78,-157 78,-157 78,-205.75 78,-205.75"/>
<polygon fill="black" stroke="black" points="74.5,-205.75 78,-215.75 81.5,-205.75 74.5,-205.75"/>
</g>
<!-- SharedService -->
<g id="node14" class="node">
<title>SharedService</title>
<ellipse fill="#fdb462" stroke="black" cx="944" cy="-104" rx="66.61" ry="18"/>
<text text-anchor="middle" x="944" y="-99.8" font-family="Times,serif" font-size="14.00">SharedService</text>
</g>
<!-- SharedService&#45;&gt;SharedModule -->
<g id="edge13" class="edge">
<title>SharedService&#45;&gt;SharedModule</title>
<path fill="none" stroke="black" d="M944,-122.11C944,-122.11 944,-140.99 944,-140.99"/>
<polygon fill="black" stroke="black" points="940.5,-140.99 944,-150.99 947.5,-140.99 940.5,-140.99"/>
</g>
</g>
</svg>

    </div>
    <i id="fullscreen" class="icon ion-ios-resize module-graph-fullscreen-btn" aria-hidden="true"></i>
    <div class="btn-group size-buttons">
        <button id="zoom-in" class="btn btn-default btn-sm">Zoom in</button>
        <button id="reset" class="btn btn-default btn-sm">Reset</button>
        <button id="zoom-out" class="btn btn-default btn-sm">Zoom out</button>
    </div>
</div>
<script src="../js/libs/svg-pan-zoom.min.js"></script>
<script src="../js/svg-pan-zoom.controls.js"></script>

<ul class="nav nav-tabs" role="tablist">
    <li class="nav-item">
        <a href="#info" 
            class="nav-link"
            class="nav-link active"
            role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
    </li>
    <li class="nav-item">
        <a href="#source" 
            class="nav-link"
            
            role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
    </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">

        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/shared/shared.module.ts</code>
        </p>





        <div class="container-fluid module">
            <div class="row">
                <div class="col-sm-3">
                    <h3>Providers<a href="https://angular.io/api/core/NgModule#providers" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module providers"><span class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../injectables/SharedService.html">SharedService</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Exports<a href="https://angular.io/api/core/NgModule#exports" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module exports"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../s/CachedTeamRepository.html">CachedTeamRepository</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../s/FormFieldEventRepository.html">FormFieldEventRepository</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../s/FormRepository.html">FormRepository</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../s/OrganizationRepository.html">OrganizationRepository</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/SharedService.html">SharedService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../s/TagRepository.html">TagRepository</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../s/TeamMemberRepository.html">TeamMemberRepository</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../s/TeamRepository.html">TeamRepository</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../s/TicketPriorityRepository.html">TicketPriorityRepository</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../s/TicketStatusRepository.html">TicketStatusRepository</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../s/TicketTypeRepository.html">TicketTypeRepository</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../s/UserRepository.html">UserRepository</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>


    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Module } from &quot;@nestjs/common&quot;;
import { TypeOrmModule } from &quot;@nestjs/typeorm&quot;;
import {
  CachedTeamRepository,
  CachedUserRepository,
  Form,
  FormFieldEventRepository,
  FormFieldEvents,
  FormRepository,
  Organization,
  OrganizationRepository,
  Tag,
  TagRepository,
  Team,
  TeamMember,
  TeamMemberRepository,
  TeamRepository,
  TicketPriority,
  TicketPriorityRepository,
  TicketStatus,
  TicketStatusRepository,
  TicketType,
  TicketTypeRepository,
  TransactionService,
  User,
  UserRepository,
} from &quot;@repo/thena-platform-entities&quot;;
import { SharedService } from &quot;./shared.service&quot;;

@Module({
  imports: [
    TypeOrmModule.forFeature([
      User,
      UserRepository,
      Tag,
      TagRepository,
      Team,
      TeamRepository,
      TeamMember,
      TeamMemberRepository,
      TicketPriority,
      TicketPriorityRepository,
      TicketStatus,
      TicketStatusRepository,
      TicketType,
      TicketTypeRepository,
      Organization,
      OrganizationRepository,
      Form,
      FormRepository,
      FormFieldEvents,
      FormFieldEventRepository,
    ]),
  ],
  providers: [
    SharedService,
    TransactionService,

    // Tickets
    TicketPriorityRepository,
    TicketStatusRepository,
    TicketTypeRepository,

    // Tags
    TagRepository,

    // Users
    UserRepository,
    CachedUserRepository,

    // Teams
    TeamRepository,
    TeamMemberRepository,
    CachedTeamRepository,

    // Organization
    OrganizationRepository,

    // Forms
    FormRepository,
    FormFieldEventRepository,
  ],
  exports: [
    SharedService,
    TagRepository,
    UserRepository,
    TeamMemberRepository,
    TeamRepository,
    CachedTeamRepository,

    // Ticket utility repositories
    TicketStatusRepository,
    TicketPriorityRepository,
    TicketTypeRepository,

    // Organization
    OrganizationRepository,

    FormRepository,
    FormFieldEventRepository,
  ],
})
export class SharedModule {}
</code></pre>
    </div>
</div>

















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'module';
            var COMPODOC_CURRENT_PAGE_URL = 'SharedModule.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
