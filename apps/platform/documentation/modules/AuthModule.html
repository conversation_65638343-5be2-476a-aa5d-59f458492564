<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content module">
                   <div class="content-data">



<ol class="breadcrumb">
    <li class="breadcrumb-item">Modules</li>
    <li class="breadcrumb-item" >AuthModule</li>
</ol>

<div class="text-center module-graph-container">
    <div id="module-graph-svg">
        <?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: dependencies Pages: 1 -->
<svg width="1029pt" height="284pt"
 viewBox="0.00 0.00 1029.00 284.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 280)">
<title>dependencies</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-280 1025,-280 1025,4 -4,4"/>
<text text-anchor="start" x="489.51" y="-42.4" font-family="Times-12" font-weight="bold" font-size="14.00">Legend</text>
<polygon fill="#ffffb3" stroke="transparent" points="276.5,-10 276.5,-30 296.5,-30 296.5,-10 276.5,-10"/>
<text text-anchor="start" x="300.13" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Declarations</text>
<polygon fill="#8dd3c7" stroke="transparent" points="389.5,-10 389.5,-30 409.5,-30 409.5,-10 389.5,-10"/>
<text text-anchor="start" x="413.23" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Module</text>
<polygon fill="#80b1d3" stroke="transparent" points="475.5,-10 475.5,-30 495.5,-30 495.5,-10 475.5,-10"/>
<text text-anchor="start" x="499.28" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Bootstrap</text>
<polygon fill="#fdb462" stroke="transparent" points="572.5,-10 572.5,-30 592.5,-30 592.5,-10 572.5,-10"/>
<text text-anchor="start" x="596.17" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Providers</text>
<polygon fill="#fb8072" stroke="transparent" points="668.5,-10 668.5,-30 688.5,-30 688.5,-10 668.5,-10"/>
<text text-anchor="start" x="692.23" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Exports</text>
<g id="clust1" class="cluster">
<title>cluster_AuthModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="8,-70 8,-268 1013,-268 1013,-70 8,-70"/>
</g>
<g id="clust4" class="cluster">
<title>cluster_AuthModule_exports</title>
<polygon fill="none" stroke="black" points="16,-208 16,-260 1005,-260 1005,-208 16,-208"/>
</g>
<g id="clust3" class="cluster">
<title>cluster_AuthModule_imports</title>
<polygon fill="none" stroke="black" points="383,-78 383,-130 888,-130 888,-78 383,-78"/>
</g>
<g id="clust6" class="cluster">
<title>cluster_AuthModule_providers</title>
<polygon fill="none" stroke="black" points="241,-78 241,-130 375,-130 375,-78 241,-78"/>
</g>
<!-- CommonModule -->
<g id="node1" class="node">
<title>CommonModule</title>
<polygon fill="#8dd3c7" stroke="black" points="879.67,-122 876.67,-126 855.67,-126 852.67,-122 768.33,-122 768.33,-86 879.67,-86 879.67,-122"/>
<text text-anchor="middle" x="824" y="-99.8" font-family="Times,serif" font-size="14.00">CommonModule</text>
</g>
<!-- AuthModule -->
<g id="node5" class="node">
<title>AuthModule</title>
<polygon fill="#8dd3c7" stroke="black" points="610.55,-187 607.55,-191 586.55,-191 583.55,-187 523.45,-187 523.45,-151 610.55,-151 610.55,-187"/>
<text text-anchor="middle" x="567" y="-164.8" font-family="Times,serif" font-size="14.00">AuthModule</text>
</g>
<!-- CommonModule&#45;&gt;AuthModule -->
<g id="edge1" class="edge">
<title>CommonModule&#45;&gt;AuthModule</title>
<path fill="none" stroke="black" d="M804.7,-122.27C804.7,-140.56 804.7,-166 804.7,-166 804.7,-166 620.6,-166 620.6,-166"/>
<polygon fill="black" stroke="black" points="620.6,-162.5 610.6,-166 620.6,-169.5 620.6,-162.5"/>
</g>
<!-- ConfigModule -->
<g id="node2" class="node">
<title>ConfigModule</title>
<polygon fill="#8dd3c7" stroke="black" points="750.44,-122 747.44,-126 726.44,-126 723.44,-122 651.56,-122 651.56,-86 750.44,-86 750.44,-122"/>
<text text-anchor="middle" x="701" y="-99.8" font-family="Times,serif" font-size="14.00">ConfigModule</text>
</g>
<!-- ConfigModule&#45;&gt;AuthModule -->
<g id="edge2" class="edge">
<title>ConfigModule&#45;&gt;AuthModule</title>
<path fill="none" stroke="black" d="M668.26,-122.01C668.26,-138.05 668.26,-159 668.26,-159 668.26,-159 620.82,-159 620.82,-159"/>
<polygon fill="black" stroke="black" points="620.82,-155.5 610.82,-159 620.82,-162.5 620.82,-155.5"/>
</g>
<!-- OrganizationModule -->
<g id="node3" class="node">
<title>OrganizationModule</title>
<polygon fill="#8dd3c7" stroke="black" points="633.13,-122 630.13,-126 609.13,-126 606.13,-122 500.87,-122 500.87,-86 633.13,-86 633.13,-122"/>
<text text-anchor="middle" x="567" y="-99.8" font-family="Times,serif" font-size="14.00">OrganizationModule</text>
</g>
<!-- OrganizationModule&#45;&gt;AuthModule -->
<g id="edge3" class="edge">
<title>OrganizationModule&#45;&gt;AuthModule</title>
<path fill="none" stroke="black" d="M567,-122.11C567,-122.11 567,-140.99 567,-140.99"/>
<polygon fill="black" stroke="black" points="563.5,-140.99 567,-150.99 570.5,-140.99 563.5,-140.99"/>
</g>
<!-- UsersModule -->
<g id="node4" class="node">
<title>UsersModule</title>
<polygon fill="#8dd3c7" stroke="black" points="482.92,-122 479.92,-126 458.92,-126 455.92,-122 391.08,-122 391.08,-86 482.92,-86 482.92,-122"/>
<text text-anchor="middle" x="437" y="-99.8" font-family="Times,serif" font-size="14.00">UsersModule</text>
</g>
<!-- UsersModule&#45;&gt;AuthModule -->
<g id="edge4" class="edge">
<title>UsersModule&#45;&gt;AuthModule</title>
<path fill="none" stroke="black" d="M446.55,-122.01C446.55,-138.05 446.55,-159 446.55,-159 446.55,-159 513.18,-159 513.18,-159"/>
<polygon fill="black" stroke="black" points="513.18,-162.5 523.18,-159 513.18,-155.5 513.18,-162.5"/>
</g>
<!-- ApiKeyAuthStrategy  -->
<g id="node6" class="node">
<title>ApiKeyAuthStrategy </title>
<polygon fill="#fb8072" stroke="black" points="997.47,-252 858.53,-252 858.53,-216 997.47,-216 997.47,-252"/>
<text text-anchor="middle" x="928" y="-229.8" font-family="Times,serif" font-size="14.00">ApiKeyAuthStrategy </text>
</g>
<!-- AuthModule&#45;&gt;ApiKeyAuthStrategy  -->
<g id="edge5" class="edge">
<title>AuthModule&#45;&gt;ApiKeyAuthStrategy </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M610.63,-173C694.35,-173 869.3,-173 869.3,-173 869.3,-173 869.3,-205.7 869.3,-205.7"/>
<polygon fill="black" stroke="black" points="865.8,-205.7 869.3,-215.7 872.8,-205.7 865.8,-205.7"/>
</g>
<!-- ApiKeyGrpcStrategy  -->
<g id="node7" class="node">
<title>ApiKeyGrpcStrategy </title>
<polygon fill="#fb8072" stroke="black" points="841.45,-252 702.55,-252 702.55,-216 841.45,-216 841.45,-252"/>
<text text-anchor="middle" x="772" y="-229.8" font-family="Times,serif" font-size="14.00">ApiKeyGrpcStrategy </text>
</g>
<!-- AuthModule&#45;&gt;ApiKeyGrpcStrategy  -->
<g id="edge6" class="edge">
<title>AuthModule&#45;&gt;ApiKeyGrpcStrategy </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M610.58,-180C657.81,-180 726.5,-180 726.5,-180 726.5,-180 726.5,-205.72 726.5,-205.72"/>
<polygon fill="black" stroke="black" points="723,-205.72 726.5,-215.72 730,-205.72 723,-205.72"/>
</g>
<!-- AuthService  -->
<g id="node8" class="node">
<title>AuthService </title>
<polygon fill="#fb8072" stroke="black" points="684.98,-252 595.02,-252 595.02,-216 684.98,-216 684.98,-252"/>
<text text-anchor="middle" x="640" y="-229.8" font-family="Times,serif" font-size="14.00">AuthService </text>
</g>
<!-- AuthModule&#45;&gt;AuthService  -->
<g id="edge7" class="edge">
<title>AuthModule&#45;&gt;AuthService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M603.02,-187.11C603.02,-187.11 603.02,-205.99 603.02,-205.99"/>
<polygon fill="black" stroke="black" points="599.52,-205.99 603.02,-215.99 606.52,-205.99 599.52,-205.99"/>
</g>
<!-- BearerTokenGrpcStrategy  -->
<g id="node9" class="node">
<title>BearerTokenGrpcStrategy </title>
<polygon fill="#fb8072" stroke="black" points="577.7,-252 410.3,-252 410.3,-216 577.7,-216 577.7,-252"/>
<text text-anchor="middle" x="494" y="-229.8" font-family="Times,serif" font-size="14.00">BearerTokenGrpcStrategy </text>
</g>
<!-- AuthModule&#45;&gt;BearerTokenGrpcStrategy  -->
<g id="edge8" class="edge">
<title>AuthModule&#45;&gt;BearerTokenGrpcStrategy </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M550.41,-187.11C550.41,-187.11 550.41,-205.99 550.41,-205.99"/>
<polygon fill="black" stroke="black" points="546.91,-205.99 550.41,-215.99 553.91,-205.99 546.91,-205.99"/>
</g>
<!-- BearerTokenHttpStrategy  -->
<g id="node10" class="node">
<title>BearerTokenHttpStrategy </title>
<polygon fill="#fb8072" stroke="black" points="392.11,-252 227.89,-252 227.89,-216 392.11,-216 392.11,-252"/>
<text text-anchor="middle" x="310" y="-229.8" font-family="Times,serif" font-size="14.00">BearerTokenHttpStrategy </text>
</g>
<!-- AuthModule&#45;&gt;BearerTokenHttpStrategy  -->
<g id="edge9" class="edge">
<title>AuthModule&#45;&gt;BearerTokenHttpStrategy </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M522.99,-180C467.51,-180 379.3,-180 379.3,-180 379.3,-180 379.3,-205.72 379.3,-205.72"/>
<polygon fill="black" stroke="black" points="375.8,-205.72 379.3,-215.72 382.8,-205.72 375.8,-205.72"/>
</g>
<!-- UserOrgInternalGrpcStrategy  -->
<g id="node11" class="node">
<title>UserOrgInternalGrpcStrategy </title>
<polygon fill="#fb8072" stroke="black" points="209.86,-252 24.14,-252 24.14,-216 209.86,-216 209.86,-252"/>
<text text-anchor="middle" x="117" y="-229.8" font-family="Times,serif" font-size="14.00">UserOrgInternalGrpcStrategy </text>
</g>
<!-- AuthModule&#45;&gt;UserOrgInternalGrpcStrategy  -->
<g id="edge10" class="edge">
<title>AuthModule&#45;&gt;UserOrgInternalGrpcStrategy </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M523.44,-173C410.51,-173 117,-173 117,-173 117,-173 117,-205.7 117,-205.7"/>
<polygon fill="black" stroke="black" points="113.5,-205.7 117,-215.7 120.5,-205.7 113.5,-205.7"/>
</g>
<!-- AuthService -->
<g id="node12" class="node">
<title>AuthService</title>
<ellipse fill="#fdb462" stroke="black" cx="308" cy="-104" rx="59.11" ry="18"/>
<text text-anchor="middle" x="308" y="-99.8" font-family="Times,serif" font-size="14.00">AuthService</text>
</g>
<!-- AuthService&#45;&gt;AuthModule -->
<g id="edge11" class="edge">
<title>AuthService&#45;&gt;AuthModule</title>
<path fill="none" stroke="black" d="M308,-122.27C308,-140.56 308,-166 308,-166 308,-166 513.36,-166 513.36,-166"/>
<polygon fill="black" stroke="black" points="513.36,-169.5 523.36,-166 513.36,-162.5 513.36,-169.5"/>
</g>
</g>
</svg>

    </div>
    <i id="fullscreen" class="icon ion-ios-resize module-graph-fullscreen-btn" aria-hidden="true"></i>
    <div class="btn-group size-buttons">
        <button id="zoom-in" class="btn btn-default btn-sm">Zoom in</button>
        <button id="reset" class="btn btn-default btn-sm">Reset</button>
        <button id="zoom-out" class="btn btn-default btn-sm">Zoom out</button>
    </div>
</div>
<script src="../js/libs/svg-pan-zoom.min.js"></script>
<script src="../js/svg-pan-zoom.controls.js"></script>

<ul class="nav nav-tabs" role="tablist">
    <li class="nav-item">
        <a href="#info" 
            class="nav-link"
            class="nav-link active"
            role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
    </li>
    <li class="nav-item">
        <a href="#source" 
            class="nav-link"
            
            role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
    </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">

        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/auth/auth.module.ts</code>
        </p>





        <div class="container-fluid module">
            <div class="row">
                <div class="col-sm-3">
                    <h3>Providers<a href="https://angular.io/api/core/NgModule#providers" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module providers"><span class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../injectables/AuthService.html">AuthService</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Controllers<a href="https://docs.nestjs.com/controllers" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module controllers"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../controllers/AuthController.html">AuthController</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Imports<a href="https://angular.io/api/core/NgModule#imports" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module imports"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../modules/CommonModule.html">CommonModule</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../modules/ConfigModule.html">ConfigModule</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../modules/OrganizationModule.html">OrganizationModule</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../modules/UsersModule.html">UsersModule</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Exports<a href="https://angular.io/api/core/NgModule#exports" target="_blank" rel="noopener noreferrer"
                            title="Official documentation about module exports"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../s/ApiKeyAuthStrategy.html">ApiKeyAuthStrategy</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../s/ApiKeyGrpcStrategy.html">ApiKeyGrpcStrategy</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/AuthService.html">AuthService</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../s/BearerTokenGrpcStrategy.html">BearerTokenGrpcStrategy</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../s/BearerTokenHttpStrategy.html">BearerTokenHttpStrategy</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../s/UserOrgInternalGrpcStrategy.html">UserOrgInternalGrpcStrategy</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>


    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Module } from &quot;@nestjs/common&quot;;
import { APP_GUARD } from &quot;@nestjs/core&quot;;
import { JwtModule } from &quot;@nestjs/jwt&quot;;
import { ThrottlerGuard } from &quot;@nestjs/throttler&quot;;
import {
  ApiKeyAuthStrategy,
  ApiKeyGrpcStrategy,
  AUTH_GRPC_SERVICE_URL_TOKEN,
  AuthenticationGrpcClient,
  AuthGuard,
  BearerTokenGrpcStrategy,
  BearerTokenHttpStrategy,
  UserOrgInternalGrpcStrategy,
} from &quot;@repo/nestjs-commons/guards&quot;;
import { CommonModule } from &quot;../common/common.module&quot;;
import { ConfigModule } from &quot;../config/config.module&quot;;
import { ConfigKeys, ConfigService } from &quot;../config/config.service&quot;;
import { OrganizationModule } from &quot;../organization/organization.module&quot;;
import { UsersModule } from &quot;../users/users.module&quot;;
import { AuthController } from &quot;./auth.controller&quot;;
import { AuthService } from &quot;./auth.service&quot;;

@Module({
  imports: [
    UsersModule,
    OrganizationModule,
    ConfigModule,
    CommonModule,
    JwtModule.registerAsync({
      global: true,
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) &#x3D;&gt; ({
        secret: configService.get(ConfigKeys.JWT_SECRET),
        signOptions: { expiresIn: &quot;12h&quot; },
      }),
    }),
  ],
  controllers: [AuthController],
  providers: [
    ApiKeyAuthStrategy,
    BearerTokenHttpStrategy,
    ApiKeyGrpcStrategy,
    BearerTokenGrpcStrategy,
    UserOrgInternalGrpcStrategy,
    AuthService,
    {
      provide: AUTH_GRPC_SERVICE_URL_TOKEN,
      useFactory: (configService: ConfigService) &#x3D;&gt;
        configService.get(ConfigKeys.AUTH_GRPC_URL),
      inject: [ConfigService],
    },
    {
      provide: APP_GUARD,
      useClass: ThrottlerGuard,
    },
    {
      // Globally apply the auth guard to all routes
      provide: APP_GUARD,
      useClass: AuthGuard,
    },

    // Authentication Grpc Client
    AuthenticationGrpcClient,
  ],
  exports: [
    AuthService,
    ApiKeyAuthStrategy,
    BearerTokenHttpStrategy,
    ApiKeyGrpcStrategy,
    BearerTokenGrpcStrategy,
    UserOrgInternalGrpcStrategy,
  ],
})
export class AuthModule {}
</code></pre>
    </div>
</div>

















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'module';
            var COMPODOC_CURRENT_PAGE_URL = 'AuthModule.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
