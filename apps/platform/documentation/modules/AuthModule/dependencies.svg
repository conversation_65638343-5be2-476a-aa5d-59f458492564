<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: dependencies Pages: 1 -->
<svg width="1029pt" height="284pt"
 viewBox="0.00 0.00 1029.00 284.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 280)">
<title>dependencies</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-280 1025,-280 1025,4 -4,4"/>
<text text-anchor="start" x="489.51" y="-42.4" font-family="Times-12" font-weight="bold" font-size="14.00">Legend</text>
<polygon fill="#ffffb3" stroke="transparent" points="276.5,-10 276.5,-30 296.5,-30 296.5,-10 276.5,-10"/>
<text text-anchor="start" x="300.13" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Declarations</text>
<polygon fill="#8dd3c7" stroke="transparent" points="389.5,-10 389.5,-30 409.5,-30 409.5,-10 389.5,-10"/>
<text text-anchor="start" x="413.23" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Module</text>
<polygon fill="#80b1d3" stroke="transparent" points="475.5,-10 475.5,-30 495.5,-30 495.5,-10 475.5,-10"/>
<text text-anchor="start" x="499.28" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Bootstrap</text>
<polygon fill="#fdb462" stroke="transparent" points="572.5,-10 572.5,-30 592.5,-30 592.5,-10 572.5,-10"/>
<text text-anchor="start" x="596.17" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Providers</text>
<polygon fill="#fb8072" stroke="transparent" points="668.5,-10 668.5,-30 688.5,-30 688.5,-10 668.5,-10"/>
<text text-anchor="start" x="692.23" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Exports</text>
<g id="clust1" class="cluster">
<title>cluster_AuthModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="8,-70 8,-268 1013,-268 1013,-70 8,-70"/>
</g>
<g id="clust4" class="cluster">
<title>cluster_AuthModule_exports</title>
<polygon fill="none" stroke="black" points="16,-208 16,-260 1005,-260 1005,-208 16,-208"/>
</g>
<g id="clust3" class="cluster">
<title>cluster_AuthModule_imports</title>
<polygon fill="none" stroke="black" points="383,-78 383,-130 888,-130 888,-78 383,-78"/>
</g>
<g id="clust6" class="cluster">
<title>cluster_AuthModule_providers</title>
<polygon fill="none" stroke="black" points="241,-78 241,-130 375,-130 375,-78 241,-78"/>
</g>
<!-- CommonModule -->
<g id="node1" class="node">
<title>CommonModule</title>
<polygon fill="#8dd3c7" stroke="black" points="879.67,-122 876.67,-126 855.67,-126 852.67,-122 768.33,-122 768.33,-86 879.67,-86 879.67,-122"/>
<text text-anchor="middle" x="824" y="-99.8" font-family="Times,serif" font-size="14.00">CommonModule</text>
</g>
<!-- AuthModule -->
<g id="node5" class="node">
<title>AuthModule</title>
<polygon fill="#8dd3c7" stroke="black" points="610.55,-187 607.55,-191 586.55,-191 583.55,-187 523.45,-187 523.45,-151 610.55,-151 610.55,-187"/>
<text text-anchor="middle" x="567" y="-164.8" font-family="Times,serif" font-size="14.00">AuthModule</text>
</g>
<!-- CommonModule&#45;&gt;AuthModule -->
<g id="edge1" class="edge">
<title>CommonModule&#45;&gt;AuthModule</title>
<path fill="none" stroke="black" d="M804.7,-122.27C804.7,-140.56 804.7,-166 804.7,-166 804.7,-166 620.6,-166 620.6,-166"/>
<polygon fill="black" stroke="black" points="620.6,-162.5 610.6,-166 620.6,-169.5 620.6,-162.5"/>
</g>
<!-- ConfigModule -->
<g id="node2" class="node">
<title>ConfigModule</title>
<polygon fill="#8dd3c7" stroke="black" points="750.44,-122 747.44,-126 726.44,-126 723.44,-122 651.56,-122 651.56,-86 750.44,-86 750.44,-122"/>
<text text-anchor="middle" x="701" y="-99.8" font-family="Times,serif" font-size="14.00">ConfigModule</text>
</g>
<!-- ConfigModule&#45;&gt;AuthModule -->
<g id="edge2" class="edge">
<title>ConfigModule&#45;&gt;AuthModule</title>
<path fill="none" stroke="black" d="M668.26,-122.01C668.26,-138.05 668.26,-159 668.26,-159 668.26,-159 620.82,-159 620.82,-159"/>
<polygon fill="black" stroke="black" points="620.82,-155.5 610.82,-159 620.82,-162.5 620.82,-155.5"/>
</g>
<!-- OrganizationModule -->
<g id="node3" class="node">
<title>OrganizationModule</title>
<polygon fill="#8dd3c7" stroke="black" points="633.13,-122 630.13,-126 609.13,-126 606.13,-122 500.87,-122 500.87,-86 633.13,-86 633.13,-122"/>
<text text-anchor="middle" x="567" y="-99.8" font-family="Times,serif" font-size="14.00">OrganizationModule</text>
</g>
<!-- OrganizationModule&#45;&gt;AuthModule -->
<g id="edge3" class="edge">
<title>OrganizationModule&#45;&gt;AuthModule</title>
<path fill="none" stroke="black" d="M567,-122.11C567,-122.11 567,-140.99 567,-140.99"/>
<polygon fill="black" stroke="black" points="563.5,-140.99 567,-150.99 570.5,-140.99 563.5,-140.99"/>
</g>
<!-- UsersModule -->
<g id="node4" class="node">
<title>UsersModule</title>
<polygon fill="#8dd3c7" stroke="black" points="482.92,-122 479.92,-126 458.92,-126 455.92,-122 391.08,-122 391.08,-86 482.92,-86 482.92,-122"/>
<text text-anchor="middle" x="437" y="-99.8" font-family="Times,serif" font-size="14.00">UsersModule</text>
</g>
<!-- UsersModule&#45;&gt;AuthModule -->
<g id="edge4" class="edge">
<title>UsersModule&#45;&gt;AuthModule</title>
<path fill="none" stroke="black" d="M446.55,-122.01C446.55,-138.05 446.55,-159 446.55,-159 446.55,-159 513.18,-159 513.18,-159"/>
<polygon fill="black" stroke="black" points="513.18,-162.5 523.18,-159 513.18,-155.5 513.18,-162.5"/>
</g>
<!-- ApiKeyAuthStrategy  -->
<g id="node6" class="node">
<title>ApiKeyAuthStrategy </title>
<polygon fill="#fb8072" stroke="black" points="997.47,-252 858.53,-252 858.53,-216 997.47,-216 997.47,-252"/>
<text text-anchor="middle" x="928" y="-229.8" font-family="Times,serif" font-size="14.00">ApiKeyAuthStrategy </text>
</g>
<!-- AuthModule&#45;&gt;ApiKeyAuthStrategy  -->
<g id="edge5" class="edge">
<title>AuthModule&#45;&gt;ApiKeyAuthStrategy </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M610.63,-173C694.35,-173 869.3,-173 869.3,-173 869.3,-173 869.3,-205.7 869.3,-205.7"/>
<polygon fill="black" stroke="black" points="865.8,-205.7 869.3,-215.7 872.8,-205.7 865.8,-205.7"/>
</g>
<!-- ApiKeyGrpcStrategy  -->
<g id="node7" class="node">
<title>ApiKeyGrpcStrategy </title>
<polygon fill="#fb8072" stroke="black" points="841.45,-252 702.55,-252 702.55,-216 841.45,-216 841.45,-252"/>
<text text-anchor="middle" x="772" y="-229.8" font-family="Times,serif" font-size="14.00">ApiKeyGrpcStrategy </text>
</g>
<!-- AuthModule&#45;&gt;ApiKeyGrpcStrategy  -->
<g id="edge6" class="edge">
<title>AuthModule&#45;&gt;ApiKeyGrpcStrategy </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M610.58,-180C657.81,-180 726.5,-180 726.5,-180 726.5,-180 726.5,-205.72 726.5,-205.72"/>
<polygon fill="black" stroke="black" points="723,-205.72 726.5,-215.72 730,-205.72 723,-205.72"/>
</g>
<!-- AuthService  -->
<g id="node8" class="node">
<title>AuthService </title>
<polygon fill="#fb8072" stroke="black" points="684.98,-252 595.02,-252 595.02,-216 684.98,-216 684.98,-252"/>
<text text-anchor="middle" x="640" y="-229.8" font-family="Times,serif" font-size="14.00">AuthService </text>
</g>
<!-- AuthModule&#45;&gt;AuthService  -->
<g id="edge7" class="edge">
<title>AuthModule&#45;&gt;AuthService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M603.02,-187.11C603.02,-187.11 603.02,-205.99 603.02,-205.99"/>
<polygon fill="black" stroke="black" points="599.52,-205.99 603.02,-215.99 606.52,-205.99 599.52,-205.99"/>
</g>
<!-- BearerTokenGrpcStrategy  -->
<g id="node9" class="node">
<title>BearerTokenGrpcStrategy </title>
<polygon fill="#fb8072" stroke="black" points="577.7,-252 410.3,-252 410.3,-216 577.7,-216 577.7,-252"/>
<text text-anchor="middle" x="494" y="-229.8" font-family="Times,serif" font-size="14.00">BearerTokenGrpcStrategy </text>
</g>
<!-- AuthModule&#45;&gt;BearerTokenGrpcStrategy  -->
<g id="edge8" class="edge">
<title>AuthModule&#45;&gt;BearerTokenGrpcStrategy </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M550.41,-187.11C550.41,-187.11 550.41,-205.99 550.41,-205.99"/>
<polygon fill="black" stroke="black" points="546.91,-205.99 550.41,-215.99 553.91,-205.99 546.91,-205.99"/>
</g>
<!-- BearerTokenHttpStrategy  -->
<g id="node10" class="node">
<title>BearerTokenHttpStrategy </title>
<polygon fill="#fb8072" stroke="black" points="392.11,-252 227.89,-252 227.89,-216 392.11,-216 392.11,-252"/>
<text text-anchor="middle" x="310" y="-229.8" font-family="Times,serif" font-size="14.00">BearerTokenHttpStrategy </text>
</g>
<!-- AuthModule&#45;&gt;BearerTokenHttpStrategy  -->
<g id="edge9" class="edge">
<title>AuthModule&#45;&gt;BearerTokenHttpStrategy </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M522.99,-180C467.51,-180 379.3,-180 379.3,-180 379.3,-180 379.3,-205.72 379.3,-205.72"/>
<polygon fill="black" stroke="black" points="375.8,-205.72 379.3,-215.72 382.8,-205.72 375.8,-205.72"/>
</g>
<!-- UserOrgInternalGrpcStrategy  -->
<g id="node11" class="node">
<title>UserOrgInternalGrpcStrategy </title>
<polygon fill="#fb8072" stroke="black" points="209.86,-252 24.14,-252 24.14,-216 209.86,-216 209.86,-252"/>
<text text-anchor="middle" x="117" y="-229.8" font-family="Times,serif" font-size="14.00">UserOrgInternalGrpcStrategy </text>
</g>
<!-- AuthModule&#45;&gt;UserOrgInternalGrpcStrategy  -->
<g id="edge10" class="edge">
<title>AuthModule&#45;&gt;UserOrgInternalGrpcStrategy </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M523.44,-173C410.51,-173 117,-173 117,-173 117,-173 117,-205.7 117,-205.7"/>
<polygon fill="black" stroke="black" points="113.5,-205.7 117,-215.7 120.5,-205.7 113.5,-205.7"/>
</g>
<!-- AuthService -->
<g id="node12" class="node">
<title>AuthService</title>
<ellipse fill="#fdb462" stroke="black" cx="308" cy="-104" rx="59.11" ry="18"/>
<text text-anchor="middle" x="308" y="-99.8" font-family="Times,serif" font-size="14.00">AuthService</text>
</g>
<!-- AuthService&#45;&gt;AuthModule -->
<g id="edge11" class="edge">
<title>AuthService&#45;&gt;AuthModule</title>
<path fill="none" stroke="black" d="M308,-122.27C308,-140.56 308,-166 308,-166 308,-166 513.36,-166 513.36,-166"/>
<polygon fill="black" stroke="black" points="513.36,-169.5 523.36,-166 513.36,-162.5 513.36,-169.5"/>
</g>
</g>
</svg>
