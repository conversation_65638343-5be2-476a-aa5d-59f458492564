<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: dependencies Pages: 1 -->
<svg width="1688pt" height="284pt"
 viewBox="0.00 0.00 1688.00 284.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 280)">
<title>dependencies</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-280 1684,-280 1684,4 -4,4"/>
<text text-anchor="start" x="819.01" y="-42.4" font-family="Times-12" font-weight="bold" font-size="14.00">Legend</text>
<polygon fill="#ffffb3" stroke="transparent" points="606,-10 606,-30 626,-30 626,-10 606,-10"/>
<text text-anchor="start" x="629.63" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Declarations</text>
<polygon fill="#8dd3c7" stroke="transparent" points="719,-10 719,-30 739,-30 739,-10 719,-10"/>
<text text-anchor="start" x="742.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Module</text>
<polygon fill="#80b1d3" stroke="transparent" points="805,-10 805,-30 825,-30 825,-10 805,-10"/>
<text text-anchor="start" x="828.78" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Bootstrap</text>
<polygon fill="#fdb462" stroke="transparent" points="902,-10 902,-30 922,-30 922,-10 902,-10"/>
<text text-anchor="start" x="925.67" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Providers</text>
<polygon fill="#fb8072" stroke="transparent" points="998,-10 998,-30 1018,-30 1018,-10 998,-10"/>
<text text-anchor="start" x="1021.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Exports</text>
<g id="clust1" class="cluster">
<title>cluster_CustomFieldModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="8,-70 8,-268 1672,-268 1672,-70 8,-70"/>
</g>
<g id="clust3" class="cluster">
<title>cluster_CustomFieldModule_imports</title>
<polygon fill="none" stroke="black" points="1154,-78 1154,-130 1664,-130 1664,-78 1154,-78"/>
</g>
<g id="clust4" class="cluster">
<title>cluster_CustomFieldModule_exports</title>
<polygon fill="none" stroke="black" points="650,-208 650,-260 1392,-260 1392,-208 650,-208"/>
</g>
<g id="clust6" class="cluster">
<title>cluster_CustomFieldModule_providers</title>
<polygon fill="none" stroke="black" points="16,-78 16,-130 1146,-130 1146,-78 16,-78"/>
</g>
<!-- CommonModule -->
<g id="node1" class="node">
<title>CommonModule</title>
<polygon fill="#8dd3c7" stroke="black" points="1655.67,-122 1652.67,-126 1631.67,-126 1628.67,-122 1544.33,-122 1544.33,-86 1655.67,-86 1655.67,-122"/>
<text text-anchor="middle" x="1600" y="-99.8" font-family="Times,serif" font-size="14.00">CommonModule</text>
</g>
<!-- CustomFieldModule -->
<g id="node5" class="node">
<title>CustomFieldModule</title>
<polygon fill="#8dd3c7" stroke="black" points="1113.89,-187 1110.89,-191 1089.89,-191 1086.89,-187 982.11,-187 982.11,-151 1113.89,-151 1113.89,-187"/>
<text text-anchor="middle" x="1048" y="-164.8" font-family="Times,serif" font-size="14.00">CustomFieldModule</text>
</g>
<!-- CommonModule&#45;&gt;CustomFieldModule -->
<g id="edge1" class="edge">
<title>CommonModule&#45;&gt;CustomFieldModule</title>
<path fill="none" stroke="black" d="M1600,-122.28C1600,-143.32 1600,-175 1600,-175 1600,-175 1124.04,-175 1124.04,-175"/>
<polygon fill="black" stroke="black" points="1124.04,-171.5 1114.04,-175 1124.04,-178.5 1124.04,-171.5"/>
</g>
<!-- OrganizationModule -->
<g id="node2" class="node">
<title>OrganizationModule</title>
<polygon fill="#8dd3c7" stroke="black" points="1526.13,-122 1523.13,-126 1502.13,-126 1499.13,-122 1393.87,-122 1393.87,-86 1526.13,-86 1526.13,-122"/>
<text text-anchor="middle" x="1460" y="-99.8" font-family="Times,serif" font-size="14.00">OrganizationModule</text>
</g>
<!-- OrganizationModule&#45;&gt;CustomFieldModule -->
<g id="edge2" class="edge">
<title>OrganizationModule&#45;&gt;CustomFieldModule</title>
<path fill="none" stroke="black" d="M1460,-122.11C1460,-141.34 1460,-169 1460,-169 1460,-169 1124.19,-169 1124.19,-169"/>
<polygon fill="black" stroke="black" points="1124.19,-165.5 1114.19,-169 1124.19,-172.5 1124.19,-165.5"/>
</g>
<!-- SharedModule -->
<g id="node3" class="node">
<title>SharedModule</title>
<polygon fill="#8dd3c7" stroke="black" points="1375.42,-122 1372.42,-126 1351.42,-126 1348.42,-122 1276.58,-122 1276.58,-86 1375.42,-86 1375.42,-122"/>
<text text-anchor="middle" x="1326" y="-99.8" font-family="Times,serif" font-size="14.00">SharedModule</text>
</g>
<!-- SharedModule&#45;&gt;CustomFieldModule -->
<g id="edge3" class="edge">
<title>SharedModule&#45;&gt;CustomFieldModule</title>
<path fill="none" stroke="black" d="M1326,-122.02C1326,-139.37 1326,-163 1326,-163 1326,-163 1124.23,-163 1124.23,-163"/>
<polygon fill="black" stroke="black" points="1124.23,-159.5 1114.23,-163 1124.23,-166.5 1124.23,-159.5"/>
</g>
<!-- TeamsModule -->
<g id="node4" class="node">
<title>TeamsModule</title>
<polygon fill="#8dd3c7" stroke="black" points="1258.37,-122 1255.37,-126 1234.37,-126 1231.37,-122 1161.63,-122 1161.63,-86 1258.37,-86 1258.37,-122"/>
<text text-anchor="middle" x="1210" y="-99.8" font-family="Times,serif" font-size="14.00">TeamsModule</text>
</g>
<!-- TeamsModule&#45;&gt;CustomFieldModule -->
<g id="edge4" class="edge">
<title>TeamsModule&#45;&gt;CustomFieldModule</title>
<path fill="none" stroke="black" d="M1196.96,-122.24C1196.96,-137.57 1196.96,-157 1196.96,-157 1196.96,-157 1124.2,-157 1124.2,-157"/>
<polygon fill="black" stroke="black" points="1124.2,-153.5 1114.2,-157 1124.2,-160.5 1124.2,-153.5"/>
</g>
<!-- CustomFieldService  -->
<g id="node6" class="node">
<title>CustomFieldService </title>
<polygon fill="#fb8072" stroke="black" points="1383.82,-252 1250.18,-252 1250.18,-216 1383.82,-216 1383.82,-252"/>
<text text-anchor="middle" x="1317" y="-229.8" font-family="Times,serif" font-size="14.00">CustomFieldService </text>
</g>
<!-- CustomFieldModule&#45;&gt;CustomFieldService  -->
<g id="edge5" class="edge">
<title>CustomFieldModule&#45;&gt;CustomFieldService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1114.11,-181C1174.44,-181 1254.26,-181 1254.26,-181 1254.26,-181 1254.26,-205.76 1254.26,-205.76"/>
<polygon fill="black" stroke="black" points="1250.76,-205.76 1254.26,-215.76 1257.76,-205.76 1250.76,-205.76"/>
</g>
<!-- CustomFieldValuesService  -->
<g id="node7" class="node">
<title>CustomFieldValuesService </title>
<polygon fill="#fb8072" stroke="black" points="1232.19,-252 1059.81,-252 1059.81,-216 1232.19,-216 1232.19,-252"/>
<text text-anchor="middle" x="1146" y="-229.8" font-family="Times,serif" font-size="14.00">CustomFieldValuesService </text>
</g>
<!-- CustomFieldModule&#45;&gt;CustomFieldValuesService  -->
<g id="edge6" class="edge">
<title>CustomFieldModule&#45;&gt;CustomFieldValuesService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1086.8,-187.11C1086.8,-187.11 1086.8,-205.99 1086.8,-205.99"/>
<polygon fill="black" stroke="black" points="1083.3,-205.99 1086.8,-215.99 1090.3,-205.99 1083.3,-205.99"/>
</g>
<!-- CustomFieldvalidatorService  -->
<g id="node8" class="node">
<title>CustomFieldvalidatorService </title>
<polygon fill="#fb8072" stroke="black" points="1041.58,-252 858.42,-252 858.42,-216 1041.58,-216 1041.58,-252"/>
<text text-anchor="middle" x="950" y="-229.8" font-family="Times,serif" font-size="14.00">CustomFieldvalidatorService </text>
</g>
<!-- CustomFieldModule&#45;&gt;CustomFieldvalidatorService  -->
<g id="edge7" class="edge">
<title>CustomFieldModule&#45;&gt;CustomFieldvalidatorService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1011.92,-187.11C1011.92,-187.11 1011.92,-205.99 1011.92,-205.99"/>
<polygon fill="black" stroke="black" points="1008.42,-205.99 1011.92,-215.99 1015.42,-205.99 1008.42,-205.99"/>
</g>
<!-- ThenaRestrictedFieldService  -->
<g id="node9" class="node">
<title>ThenaRestrictedFieldService </title>
<polygon fill="#fb8072" stroke="black" points="840,-252 658,-252 658,-216 840,-216 840,-252"/>
<text text-anchor="middle" x="749" y="-229.8" font-family="Times,serif" font-size="14.00">ThenaRestrictedFieldService </text>
</g>
<!-- CustomFieldModule&#45;&gt;ThenaRestrictedFieldService  -->
<g id="edge8" class="edge">
<title>CustomFieldModule&#45;&gt;ThenaRestrictedFieldService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M981.82,-181C900.49,-181 773.15,-181 773.15,-181 773.15,-181 773.15,-205.76 773.15,-205.76"/>
<polygon fill="black" stroke="black" points="769.65,-205.76 773.15,-215.76 776.65,-205.76 769.65,-205.76"/>
</g>
<!-- CustomFieldService -->
<g id="node10" class="node">
<title>CustomFieldService</title>
<ellipse fill="#fdb462" stroke="black" cx="1048" cy="-104" rx="89.78" ry="18"/>
<text text-anchor="middle" x="1048" y="-99.8" font-family="Times,serif" font-size="14.00">CustomFieldService</text>
</g>
<!-- CustomFieldService&#45;&gt;CustomFieldModule -->
<g id="edge9" class="edge">
<title>CustomFieldService&#45;&gt;CustomFieldModule</title>
<path fill="none" stroke="black" d="M1048,-122.11C1048,-122.11 1048,-140.99 1048,-140.99"/>
<polygon fill="black" stroke="black" points="1044.5,-140.99 1048,-150.99 1051.5,-140.99 1044.5,-140.99"/>
</g>
<!-- CustomFieldValuesService -->
<g id="node11" class="node">
<title>CustomFieldValuesService</title>
<ellipse fill="#fdb462" stroke="black" cx="823" cy="-104" rx="116.91" ry="18"/>
<text text-anchor="middle" x="823" y="-99.8" font-family="Times,serif" font-size="14.00">CustomFieldValuesService</text>
</g>
<!-- CustomFieldValuesService&#45;&gt;CustomFieldModule -->
<g id="edge10" class="edge">
<title>CustomFieldValuesService&#45;&gt;CustomFieldModule</title>
<path fill="none" stroke="black" d="M898.96,-117.93C898.96,-133.63 898.96,-157 898.96,-157 898.96,-157 971.76,-157 971.76,-157"/>
<polygon fill="black" stroke="black" points="971.76,-160.5 981.76,-157 971.76,-153.5 971.76,-160.5"/>
</g>
<!-- CustomFieldvalidatorService -->
<g id="node12" class="node">
<title>CustomFieldvalidatorService</title>
<ellipse fill="#fdb462" stroke="black" cx="564" cy="-104" rx="124.43" ry="18"/>
<text text-anchor="middle" x="564" y="-99.8" font-family="Times,serif" font-size="14.00">CustomFieldvalidatorService</text>
</g>
<!-- CustomFieldvalidatorService&#45;&gt;CustomFieldModule -->
<g id="edge11" class="edge">
<title>CustomFieldvalidatorService&#45;&gt;CustomFieldModule</title>
<path fill="none" stroke="black" d="M673.11,-112.71C673.11,-129.12 673.11,-163 673.11,-163 673.11,-163 971.84,-163 971.84,-163"/>
<polygon fill="black" stroke="black" points="971.84,-166.5 981.84,-163 971.84,-159.5 971.84,-166.5"/>
</g>
<!-- SharedService -->
<g id="node13" class="node">
<title>SharedService</title>
<ellipse fill="#fdb462" stroke="black" cx="355" cy="-104" rx="66.61" ry="18"/>
<text text-anchor="middle" x="355" y="-99.8" font-family="Times,serif" font-size="14.00">SharedService</text>
</g>
<!-- SharedService&#45;&gt;CustomFieldModule -->
<g id="edge12" class="edge">
<title>SharedService&#45;&gt;CustomFieldModule</title>
<path fill="none" stroke="black" d="M355,-122.11C355,-141.34 355,-169 355,-169 355,-169 971.87,-169 971.87,-169"/>
<polygon fill="black" stroke="black" points="971.87,-172.5 981.87,-169 971.87,-165.5 971.87,-172.5"/>
</g>
<!-- ThenaRestrictedFieldService -->
<g id="node14" class="node">
<title>ThenaRestrictedFieldService</title>
<ellipse fill="#fdb462" stroke="black" cx="147" cy="-104" rx="123.25" ry="18"/>
<text text-anchor="middle" x="147" y="-99.8" font-family="Times,serif" font-size="14.00">ThenaRestrictedFieldService</text>
</g>
<!-- ThenaRestrictedFieldService&#45;&gt;CustomFieldModule -->
<g id="edge13" class="edge">
<title>ThenaRestrictedFieldService&#45;&gt;CustomFieldModule</title>
<path fill="none" stroke="black" d="M147,-122.28C147,-143.32 147,-175 147,-175 147,-175 971.75,-175 971.75,-175"/>
<polygon fill="black" stroke="black" points="971.75,-178.5 981.75,-175 971.75,-171.5 971.75,-178.5"/>
</g>
</g>
</svg>
