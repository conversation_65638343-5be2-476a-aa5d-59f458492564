<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: dependencies Pages: 1 -->
<svg width="1379pt" height="284pt"
 viewBox="0.00 0.00 1379.00 284.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 280)">
<title>dependencies</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-280 1375,-280 1375,4 -4,4"/>
<text text-anchor="start" x="664.51" y="-42.4" font-family="Times-12" font-weight="bold" font-size="14.00">Legend</text>
<polygon fill="#ffffb3" stroke="transparent" points="451.5,-10 451.5,-30 471.5,-30 471.5,-10 451.5,-10"/>
<text text-anchor="start" x="475.13" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Declarations</text>
<polygon fill="#8dd3c7" stroke="transparent" points="564.5,-10 564.5,-30 584.5,-30 584.5,-10 564.5,-10"/>
<text text-anchor="start" x="588.23" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Module</text>
<polygon fill="#80b1d3" stroke="transparent" points="650.5,-10 650.5,-30 670.5,-30 670.5,-10 650.5,-10"/>
<text text-anchor="start" x="674.28" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Bootstrap</text>
<polygon fill="#fdb462" stroke="transparent" points="747.5,-10 747.5,-30 767.5,-30 767.5,-10 747.5,-10"/>
<text text-anchor="start" x="771.17" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Providers</text>
<polygon fill="#fb8072" stroke="transparent" points="843.5,-10 843.5,-30 863.5,-30 863.5,-10 843.5,-10"/>
<text text-anchor="start" x="867.23" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Exports</text>
<g id="clust1" class="cluster">
<title>cluster_UsersModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="8,-70 8,-268 1363,-268 1363,-70 8,-70"/>
</g>
<g id="clust3" class="cluster">
<title>cluster_UsersModule_imports</title>
<polygon fill="none" stroke="black" points="995,-78 995,-130 1355,-130 1355,-78 995,-78"/>
</g>
<g id="clust6" class="cluster">
<title>cluster_UsersModule_providers</title>
<polygon fill="none" stroke="black" points="16,-78 16,-130 987,-130 987,-78 16,-78"/>
</g>
<g id="clust4" class="cluster">
<title>cluster_UsersModule_exports</title>
<polygon fill="none" stroke="black" points="543,-208 543,-260 983,-260 983,-208 543,-208"/>
</g>
<!-- CommonModule -->
<g id="node1" class="node">
<title>CommonModule</title>
<polygon fill="#8dd3c7" stroke="black" points="1346.67,-122 1343.67,-126 1322.67,-126 1319.67,-122 1235.33,-122 1235.33,-86 1346.67,-86 1346.67,-122"/>
<text text-anchor="middle" x="1291" y="-99.8" font-family="Times,serif" font-size="14.00">CommonModule</text>
</g>
<!-- UsersModule -->
<g id="node4" class="node">
<title>UsersModule</title>
<polygon fill="#8dd3c7" stroke="black" points="780.92,-187 777.92,-191 756.92,-191 753.92,-187 689.08,-187 689.08,-151 780.92,-151 780.92,-187"/>
<text text-anchor="middle" x="735" y="-164.8" font-family="Times,serif" font-size="14.00">UsersModule</text>
</g>
<!-- CommonModule&#45;&gt;UsersModule -->
<g id="edge1" class="edge">
<title>CommonModule&#45;&gt;UsersModule</title>
<path fill="none" stroke="black" d="M1291,-122.13C1291,-142.57 1291,-173 1291,-173 1291,-173 790.97,-173 790.97,-173"/>
<polygon fill="black" stroke="black" points="790.97,-169.5 780.97,-173 790.97,-176.5 790.97,-169.5"/>
</g>
<!-- ConfigModule -->
<g id="node2" class="node">
<title>ConfigModule</title>
<polygon fill="#8dd3c7" stroke="black" points="1217.44,-122 1214.44,-126 1193.44,-126 1190.44,-122 1118.56,-122 1118.56,-86 1217.44,-86 1217.44,-122"/>
<text text-anchor="middle" x="1168" y="-99.8" font-family="Times,serif" font-size="14.00">ConfigModule</text>
</g>
<!-- ConfigModule&#45;&gt;UsersModule -->
<g id="edge2" class="edge">
<title>ConfigModule&#45;&gt;UsersModule</title>
<path fill="none" stroke="black" d="M1168,-122.27C1168,-140.56 1168,-166 1168,-166 1168,-166 790.93,-166 790.93,-166"/>
<polygon fill="black" stroke="black" points="790.93,-162.5 780.93,-166 790.93,-169.5 790.93,-162.5"/>
</g>
<!-- SharedModule -->
<g id="node3" class="node">
<title>SharedModule</title>
<polygon fill="#8dd3c7" stroke="black" points="1101.42,-122 1098.42,-126 1077.42,-126 1074.42,-122 1002.58,-122 1002.58,-86 1101.42,-86 1101.42,-122"/>
<text text-anchor="middle" x="1052" y="-99.8" font-family="Times,serif" font-size="14.00">SharedModule</text>
</g>
<!-- SharedModule&#45;&gt;UsersModule -->
<g id="edge3" class="edge">
<title>SharedModule&#45;&gt;UsersModule</title>
<path fill="none" stroke="black" d="M1052,-122.01C1052,-138.05 1052,-159 1052,-159 1052,-159 790.76,-159 790.76,-159"/>
<polygon fill="black" stroke="black" points="790.76,-155.5 780.76,-159 790.76,-162.5 790.76,-155.5"/>
</g>
<!-- CachedUserRepository  -->
<g id="node5" class="node">
<title>CachedUserRepository </title>
<polygon fill="#fb8072" stroke="black" points="974.84,-252 825.16,-252 825.16,-216 974.84,-216 974.84,-252"/>
<text text-anchor="middle" x="900" y="-229.8" font-family="Times,serif" font-size="14.00">CachedUserRepository </text>
</g>
<!-- UsersModule&#45;&gt;CachedUserRepository  -->
<g id="edge4" class="edge">
<title>UsersModule&#45;&gt;CachedUserRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M780.96,-180C829.78,-180 900,-180 900,-180 900,-180 900,-205.72 900,-205.72"/>
<polygon fill="black" stroke="black" points="896.5,-205.72 900,-215.72 903.5,-205.72 896.5,-205.72"/>
</g>
<!-- UserAnnotatorService  -->
<g id="node6" class="node">
<title>UserAnnotatorService </title>
<polygon fill="#fb8072" stroke="black" points="807.17,-252 662.83,-252 662.83,-216 807.17,-216 807.17,-252"/>
<text text-anchor="middle" x="735" y="-229.8" font-family="Times,serif" font-size="14.00">UserAnnotatorService </text>
</g>
<!-- UsersModule&#45;&gt;UserAnnotatorService  -->
<g id="edge5" class="edge">
<title>UsersModule&#45;&gt;UserAnnotatorService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M735,-187.11C735,-187.11 735,-205.99 735,-205.99"/>
<polygon fill="black" stroke="black" points="731.5,-205.99 735,-215.99 738.5,-205.99 731.5,-205.99"/>
</g>
<!-- UsersService  -->
<g id="node7" class="node">
<title>UsersService </title>
<polygon fill="#fb8072" stroke="black" points="644.85,-252 551.15,-252 551.15,-216 644.85,-216 644.85,-252"/>
<text text-anchor="middle" x="598" y="-229.8" font-family="Times,serif" font-size="14.00">UsersService </text>
</g>
<!-- UsersModule&#45;&gt;UsersService  -->
<g id="edge6" class="edge">
<title>UsersModule&#45;&gt;UsersService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M689.21,-180C650.72,-180 601.93,-180 601.93,-180 601.93,-180 601.93,-205.72 601.93,-205.72"/>
<polygon fill="black" stroke="black" points="598.43,-205.72 601.93,-215.72 605.43,-205.72 598.43,-205.72"/>
</g>
<!-- BusinessHoursValidatorService -->
<g id="node8" class="node">
<title>BusinessHoursValidatorService</title>
<ellipse fill="#fdb462" stroke="black" cx="845" cy="-104" rx="134.28" ry="18"/>
<text text-anchor="middle" x="845" y="-99.8" font-family="Times,serif" font-size="14.00">BusinessHoursValidatorService</text>
</g>
<!-- BusinessHoursValidatorService&#45;&gt;UsersModule -->
<g id="edge7" class="edge">
<title>BusinessHoursValidatorService&#45;&gt;UsersModule</title>
<path fill="none" stroke="black" d="M745.66,-116.23C745.66,-116.23 745.66,-140.68 745.66,-140.68"/>
<polygon fill="black" stroke="black" points="742.16,-140.68 745.66,-150.68 749.16,-140.68 742.16,-140.68"/>
</g>
<!-- SharedService -->
<g id="node9" class="node">
<title>SharedService</title>
<ellipse fill="#fdb462" stroke="black" cx="626" cy="-104" rx="66.61" ry="18"/>
<text text-anchor="middle" x="626" y="-99.8" font-family="Times,serif" font-size="14.00">SharedService</text>
</g>
<!-- SharedService&#45;&gt;UsersModule -->
<g id="edge8" class="edge">
<title>SharedService&#45;&gt;UsersModule</title>
<path fill="none" stroke="black" d="M693.02,-104C698.34,-104 701.71,-104 701.71,-104 701.71,-104 701.71,-140.89 701.71,-140.89"/>
<polygon fill="black" stroke="black" points="698.21,-140.89 701.71,-150.89 705.21,-140.89 698.21,-140.89"/>
</g>
<!-- UserAnnotatorService -->
<g id="node10" class="node">
<title>UserAnnotatorService</title>
<ellipse fill="#fdb462" stroke="black" cx="444" cy="-104" rx="97.25" ry="18"/>
<text text-anchor="middle" x="444" y="-99.8" font-family="Times,serif" font-size="14.00">UserAnnotatorService</text>
</g>
<!-- UserAnnotatorService&#45;&gt;UsersModule -->
<g id="edge9" class="edge">
<title>UserAnnotatorService&#45;&gt;UsersModule</title>
<path fill="none" stroke="black" d="M444,-122.01C444,-138.05 444,-159 444,-159 444,-159 679.21,-159 679.21,-159"/>
<polygon fill="black" stroke="black" points="679.21,-162.5 689.21,-159 679.21,-155.5 679.21,-162.5"/>
</g>
<!-- UsersGrpcService -->
<g id="node11" class="node">
<title>UsersGrpcService</title>
<ellipse fill="#fdb462" stroke="black" cx="247" cy="-104" rx="81.06" ry="18"/>
<text text-anchor="middle" x="247" y="-99.8" font-family="Times,serif" font-size="14.00">UsersGrpcService</text>
</g>
<!-- UsersGrpcService&#45;&gt;UsersModule -->
<g id="edge10" class="edge">
<title>UsersGrpcService&#45;&gt;UsersModule</title>
<path fill="none" stroke="black" d="M247,-122.27C247,-140.56 247,-166 247,-166 247,-166 679.09,-166 679.09,-166"/>
<polygon fill="black" stroke="black" points="679.09,-169.5 689.09,-166 679.09,-162.5 679.09,-169.5"/>
</g>
<!-- UsersService -->
<g id="node12" class="node">
<title>UsersService</title>
<ellipse fill="#fdb462" stroke="black" cx="86" cy="-104" rx="61.95" ry="18"/>
<text text-anchor="middle" x="86" y="-99.8" font-family="Times,serif" font-size="14.00">UsersService</text>
</g>
<!-- UsersService&#45;&gt;UsersModule -->
<g id="edge11" class="edge">
<title>UsersService&#45;&gt;UsersModule</title>
<path fill="none" stroke="black" d="M86,-122.13C86,-142.57 86,-173 86,-173 86,-173 679.11,-173 679.11,-173"/>
<polygon fill="black" stroke="black" points="679.11,-176.5 689.11,-173 679.1,-169.5 679.11,-176.5"/>
</g>
</g>
</svg>
