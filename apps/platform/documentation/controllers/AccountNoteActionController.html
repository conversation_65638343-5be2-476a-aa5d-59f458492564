<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content controller">
                   <div class="content-data">





<ol class="breadcrumb">
  <li class="breadcrumb-item">Controllers</li>
  <li class="breadcrumb-item" >AccountNoteActionController</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/accounts/controllers/account-note.action.controller.ts</code>
        </p>

            <p class="comment">
                <h3>Prefix</h3>
            </p>
            <p class="comment">
                <code>v1/accounts/notes</code>
            </p>






            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#createAccountNote" >createAccountNote</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#deleteAccountNote" >deleteAccountNote</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getAccountNotes" >getAccountNotes</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#removeNoteAttachment" >removeNoteAttachment</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#updateAccountNote" >updateAccountNote</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createAccountNote"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>createAccountNote</b></span>
                        <a href="#createAccountNote"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createAccountNote(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, createAccountNoteDto: <a href="../classes/CreateAccountNoteDto.html" target="_self">CreateAccountNoteDto</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Post()<br />@ApiResponseMessage(&#x27;Account note created successfully!&#x27;)<br />@ApiCreateEndpoint({summary: &#x27;Creates an account note&#x27;, responseType: AccountNoteResponseDto})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="93"
                                    class="link-to-prism">src/accounts/controllers/account-note.action.controller.ts:93</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>createAccountNoteDto</td>
                                            <td>
                                                            <code><a href="../classes/CreateAccountNoteDto.html" target="_self" >CreateAccountNoteDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/AccountNoteResponseDto.html" target="_self" >Promise&lt;AccountNoteResponseDto&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteAccountNote"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>deleteAccountNote</b></span>
                        <a href="#deleteAccountNote"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteAccountNote(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, noteId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Delete(&#x27;:noteId&#x27;)<br />@ApiResponseMessage(&#x27;Account note deleted successfully!&#x27;)<br />@ApiDeleteEndpoint({summary: &#x27;Deletes an account note&#x27;})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="177"
                                    class="link-to-prism">src/accounts/controllers/account-note.action.controller.ts:177</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>noteId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;void&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getAccountNotes"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getAccountNotes</b></span>
                        <a href="#getAccountNotes"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getAccountNotes(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, findAccountNoteDto: <a href="../classes/FindAccountNoteDto.html" target="_self">FindAccountNoteDto</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get()<br />@ApiResponseMessage(&#x27;Account notes fetched successfully!&#x27;)<br />@ApiGetEndpoint({summary: &#x27;Fetches all account notes by account ID or by note ID&#x27;, responseType: AccountNoteResponseDto})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="54"
                                    class="link-to-prism">src/accounts/controllers/account-note.action.controller.ts:54</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>findAccountNoteDto</td>
                                            <td>
                                                            <code><a href="../classes/FindAccountNoteDto.html" target="_self" >FindAccountNoteDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/AccountNoteResponseDto.html" target="_self" >Promise&lt;AccountNoteResponseDto[]&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="removeNoteAttachment"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>removeNoteAttachment</b></span>
                        <a href="#removeNoteAttachment"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>removeNoteAttachment(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, noteId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, attachmentId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Delete(&#x27;:noteId/attachments/:attachmentId&#x27;)<br />@ApiResponseMessage(&#x27;Attachment removed successfully!&#x27;)<br />@ApiDeleteEndpoint({summary: &#x27;Removes an attachment from an account note&#x27;})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="214"
                                    class="link-to-prism">src/accounts/controllers/account-note.action.controller.ts:214</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>noteId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>attachmentId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;void&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateAccountNote"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>updateAccountNote</b></span>
                        <a href="#updateAccountNote"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateAccountNote(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, noteId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, updateAccountNoteDto: <a href="../classes/UpdateAccountNoteDto.html" target="_self">UpdateAccountNoteDto</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Put(&#x27;:noteId&#x27;)<br />@ApiResponseMessage(&#x27;Account note updated successfully!&#x27;)<br />@ApiUpdateEndpoint({summary: &#x27;Updates an account note&#x27;, responseType: AccountNoteResponseDto})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="132"
                                    class="link-to-prism">src/accounts/controllers/account-note.action.controller.ts:132</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>noteId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>updateAccountNoteDto</td>
                                            <td>
                                                            <code><a href="../classes/UpdateAccountNoteDto.html" target="_self" >UpdateAccountNoteDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/AccountNoteResponseDto.html" target="_self" >Promise&lt;AccountNoteResponseDto&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  Inject,
  InternalServerErrorException,
  Param,
  Post,
  Put,
  Query,
  UseInterceptors,
} from &quot;@nestjs/common&quot;;
import { ApiTags } from &quot;@nestjs/swagger&quot;;
import {
  ApiCreateEndpoint,
  ApiDeleteEndpoint,
  ApiGetEndpoint,
  ApiResponseMessage,
  ApiUpdateEndpoint,
  ResponseTransformInterceptor,
} from &quot;@repo/nestjs-commons/decorators&quot;;
import { SentryService } from &quot;@repo/nestjs-commons/filters&quot;;
import { ILogger } from &quot;@repo/nestjs-commons/logger&quot;;
import { CurrentUser } from &quot;../../common/decorators/user.decorator&quot;;
import {
  CreateAccountNoteDto,
  FindAccountNoteDto,
  UpdateAccountNoteDto,
} from &quot;../dtos/account-note.dto&quot;;
import { AccountNoteResponseDto } from &quot;../dtos/response/account-note.dto&quot;;
import { AccountNoteActionService } from &quot;../services/account-note.action.service&quot;;

@ApiTags(&quot;Accounts&quot;)
@Controller(&quot;v1/accounts/notes&quot;)
@UseInterceptors(ResponseTransformInterceptor)
export class AccountNoteActionController {
  private readonly logSpanId &#x3D; &quot;[AccountNoteActionController]&quot;;

  constructor(
    private readonly accountNoteActionService: AccountNoteActionService,
    @Inject(&quot;Sentry&quot;) private readonly sentryService: SentryService,
    @Inject(&quot;CustomLogger&quot;) private readonly logger: ILogger,
  ) {}

  @Get()
  @ApiResponseMessage(&quot;Account notes fetched successfully!&quot;)
  @ApiGetEndpoint({
    summary: &quot;Fetches all account notes by account ID or by note ID&quot;,
    responseType: AccountNoteResponseDto,
  })
  async getAccountNotes(
    @CurrentUser() user: CurrentUser,
    @Query() findAccountNoteDto: FindAccountNoteDto,
  ): Promise&lt;AccountNoteResponseDto[]&gt; {
    try {
      const notes &#x3D; await this.accountNoteActionService.findAllAccountNotes(
        user,
        findAccountNoteDto,
      );

      return notes.map(AccountNoteResponseDto.fromEntity);
    } catch (error) {
      this.logger.error(
        &#x60;${this.logSpanId} Error encountered while fetching account notes. &gt; Error message: ${error.message}&#x60;,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: &quot;ACCOUNTS_NOTE_CONTROLLER&quot;,
        fn: &quot;getAccountNotes&quot;,
        organizationId: user.orgUid,
        query: findAccountNoteDto,
        user,
      });

      throw new InternalServerErrorException(&quot;Something went wrong!&quot;);
    }
  }

  @Post()
  @ApiResponseMessage(&quot;Account note created successfully!&quot;)
  @ApiCreateEndpoint({
    summary: &quot;Creates an account note&quot;,
    responseType: AccountNoteResponseDto,
  })
  async createAccountNote(
    @CurrentUser() user: CurrentUser,
    @Body() createAccountNoteDto: CreateAccountNoteDto,
  ): Promise&lt;AccountNoteResponseDto&gt; {
    try {
      const createdNote &#x3D; await this.accountNoteActionService.createAccountNote(
        user,
        createAccountNoteDto,
      );

      return AccountNoteResponseDto.fromEntity(createdNote);
    } catch (error) {
      this.logger.error(
        &#x60;${this.logSpanId} Error encountered while creating account note. &gt; Error message: ${error.message}&#x60;,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: &quot;ACCOUNTS_NOTE_CONTROLLER&quot;,
        fn: &quot;createAccountNote&quot;,
        organizationId: user.orgUid,
        body: createAccountNoteDto,
        user,
      });

      throw new InternalServerErrorException(&quot;Something went wrong!&quot;);
    }
  }

  @Put(&quot;:noteId&quot;)
  @ApiResponseMessage(&quot;Account note updated successfully!&quot;)
  @ApiUpdateEndpoint({
    summary: &quot;Updates an account note&quot;,
    responseType: AccountNoteResponseDto,
  })
  async updateAccountNote(
    @CurrentUser() user: CurrentUser,
    @Param(&quot;noteId&quot;) noteId: string,
    @Body() updateAccountNoteDto: UpdateAccountNoteDto,
  ): Promise&lt;AccountNoteResponseDto&gt; {
    if (!noteId) {
      throw new BadRequestException(&quot;Note ID is required!&quot;);
    }

    try {
      const updatedNote &#x3D; await this.accountNoteActionService.updateAccountNote(
        user,
        noteId,
        updateAccountNoteDto,
      );

      return AccountNoteResponseDto.fromEntity(updatedNote);
    } catch (error) {
      this.logger.error(
        &#x60;${this.logSpanId} Error encountered while updating account note. &gt; Error message: ${error.message}&#x60;,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: &quot;ACCOUNTS_NOTE_CONTROLLER&quot;,
        fn: &quot;updateAccountNote&quot;,
        organizationId: user.orgUid,
        params: { noteId },
        body: updateAccountNoteDto,
        user,
      });

      throw new InternalServerErrorException(&quot;Something went wrong!&quot;);
    }
  }

  @Delete(&quot;:noteId&quot;)
  @ApiResponseMessage(&quot;Account note deleted successfully!&quot;)
  @ApiDeleteEndpoint({
    summary: &quot;Deletes an account note&quot;,
  })
  async deleteAccountNote(
    @CurrentUser() user: CurrentUser,
    @Param(&quot;noteId&quot;) noteId: string,
  ): Promise&lt;void&gt; {
    if (!noteId) {
      throw new BadRequestException(&quot;Note ID is required!&quot;);
    }

    try {
      await this.accountNoteActionService.deleteAccountNote(user, noteId);
    } catch (error) {
      this.logger.error(
        &#x60;${this.logSpanId} Error encountered while deleting account note. &gt; Error message: ${error.message}&#x60;,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: &quot;ACCOUNTS_NOTE_CONTROLLER&quot;,
        fn: &quot;deleteAccountNote&quot;,
        organizationId: user.orgUid,
        params: { noteId },
        user,
      });

      throw new InternalServerErrorException(&quot;Something went wrong!&quot;);
    }
  }

  @Delete(&quot;:noteId/attachments/:attachmentId&quot;)
  @ApiResponseMessage(&quot;Attachment removed successfully!&quot;)
  @ApiDeleteEndpoint({
    summary: &quot;Removes an attachment from an account note&quot;,
  })
  async removeNoteAttachment(
    @CurrentUser() user: CurrentUser,
    @Param(&quot;noteId&quot;) noteId: string,
    @Param(&quot;attachmentId&quot;) attachmentId: string,
  ): Promise&lt;void&gt; {
    if (!noteId) {
      throw new BadRequestException(&quot;Note ID is required!&quot;);
    }

    if (!attachmentId) {
      throw new BadRequestException(&quot;Attachment ID is required!&quot;);
    }

    try {
      await this.accountNoteActionService.removeNoteAttachment(
        user,
        noteId,
        attachmentId,
      );
    } catch (error) {
      this.logger.error(
        &#x60;${this.logSpanId} Error encountered while removing note attachment. &gt; Error message: ${error.message}&#x60;,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: &quot;ACCOUNTS_NOTE_CONTROLLER&quot;,
        fn: &quot;removeNoteAttachment&quot;,
        organizationId: user.orgUid,
        params: { noteId, attachmentId },
        user,
      });

      throw new InternalServerErrorException(&quot;Something went wrong!&quot;);
    }
  }
}
</code></pre>
    </div>
</div>
















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'controller';
            var COMPODOC_CURRENT_PAGE_URL = 'AccountNoteActionController.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
