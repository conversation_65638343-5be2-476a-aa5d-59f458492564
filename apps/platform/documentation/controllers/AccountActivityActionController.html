<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content controller">
                   <div class="content-data">





<ol class="breadcrumb">
  <li class="breadcrumb-item">Controllers</li>
  <li class="breadcrumb-item" >AccountActivityActionController</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/accounts/controllers/account-activity.action.controller.ts</code>
        </p>

            <p class="comment">
                <h3>Prefix</h3>
            </p>
            <p class="comment">
                <code>v1/accounts/activities</code>
            </p>






            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#createAccountActivity" >createAccountActivity</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#deleteAccountActivity" >deleteAccountActivity</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getAccountActivities" >getAccountActivities</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#removeActivityAttachment" >removeActivityAttachment</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#updateAccountActivity" >updateAccountActivity</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createAccountActivity"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>createAccountActivity</b></span>
                        <a href="#createAccountActivity"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createAccountActivity(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, createAccountActivityDto: <a href="../classes/CreateAccountActivityDto.html" target="_self">CreateAccountActivityDto</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Post()<br />@ApiResponseMessage(&#x27;Account activity created successfully!&#x27;)<br />@ApiCreateEndpoint({summary: &#x27;Creates an account activity&#x27;, responseType: AccountActivityResponseDto})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="94"
                                    class="link-to-prism">src/accounts/controllers/account-activity.action.controller.ts:94</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>createAccountActivityDto</td>
                                            <td>
                                                            <code><a href="../classes/CreateAccountActivityDto.html" target="_self" >CreateAccountActivityDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/AccountActivityResponseDto.html" target="_self" >Promise&lt;AccountActivityResponseDto&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteAccountActivity"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>deleteAccountActivity</b></span>
                        <a href="#deleteAccountActivity"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteAccountActivity(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, activityId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Delete(&#x27;:activityId&#x27;)<br />@ApiResponseMessage(&#x27;Account activity deleted successfully!&#x27;)<br />@ApiDeleteEndpoint({summary: &#x27;Deletes an account activity&#x27;})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="180"
                                    class="link-to-prism">src/accounts/controllers/account-activity.action.controller.ts:180</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>activityId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;void&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getAccountActivities"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getAccountActivities</b></span>
                        <a href="#getAccountActivities"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getAccountActivities(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, findAccountActivityDto: <a href="../classes/FindAccountActivityDto.html" target="_self">FindAccountActivityDto</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get()<br />@ApiResponseMessage(&#x27;Account activities fetched successfully!&#x27;)<br />@ApiGetEndpoint({summary: &#x27;Fetches all account activities&#x27;, responseType: AccountActivityResponseDto})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="54"
                                    class="link-to-prism">src/accounts/controllers/account-activity.action.controller.ts:54</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>findAccountActivityDto</td>
                                            <td>
                                                            <code><a href="../classes/FindAccountActivityDto.html" target="_self" >FindAccountActivityDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/AccountActivityResponseDto.html" target="_self" >Promise&lt;AccountActivityResponseDto[]&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="removeActivityAttachment"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>removeActivityAttachment</b></span>
                        <a href="#removeActivityAttachment"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>removeActivityAttachment(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, activityId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, attachmentId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Delete(&#x27;:activityId/attachments/:attachmentId&#x27;)<br />@ApiResponseMessage(&#x27;Attachment removed successfully!&#x27;)<br />@ApiDeleteEndpoint({summary: &#x27;Removes an attachment from an account activity&#x27;})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="220"
                                    class="link-to-prism">src/accounts/controllers/account-activity.action.controller.ts:220</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>activityId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>attachmentId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;void&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateAccountActivity"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>updateAccountActivity</b></span>
                        <a href="#updateAccountActivity"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateAccountActivity(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, activityId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, updateAccountActivityDto: <a href="../classes/UpdateAccountActivityDto.html" target="_self">UpdateAccountActivityDto</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Put(&#x27;:activityId&#x27;)<br />@ApiResponseMessage(&#x27;Account activity updated successfully!&#x27;)<br />@ApiUpdateEndpoint({summary: &#x27;Updates an account activity&#x27;, responseType: AccountActivityResponseDto})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="134"
                                    class="link-to-prism">src/accounts/controllers/account-activity.action.controller.ts:134</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>activityId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>updateAccountActivityDto</td>
                                            <td>
                                                            <code><a href="../classes/UpdateAccountActivityDto.html" target="_self" >UpdateAccountActivityDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/AccountActivityResponseDto.html" target="_self" >Promise&lt;AccountActivityResponseDto&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  Inject,
  InternalServerErrorException,
  Param,
  Post,
  Put,
  Query,
  UseInterceptors,
} from &quot;@nestjs/common&quot;;
import { ApiTags } from &quot;@nestjs/swagger&quot;;
import {
  ApiCreateEndpoint,
  ApiDeleteEndpoint,
  ApiGetEndpoint,
  ApiResponseMessage,
  ApiUpdateEndpoint,
  ResponseTransformInterceptor,
} from &quot;@repo/nestjs-commons/decorators&quot;;
import { SentryService } from &quot;@repo/nestjs-commons/filters&quot;;
import { ILogger } from &quot;@repo/nestjs-commons/logger&quot;;
import { CurrentUser } from &quot;../../common/decorators/user.decorator&quot;;
import {
  CreateAccountActivityDto,
  FindAccountActivityDto,
  UpdateAccountActivityDto,
} from &quot;../dtos/account-activity.dto&quot;;
import { AccountActivityResponseDto } from &quot;../dtos/response/account-activity.dto&quot;;
import { AccountActivityActionService } from &quot;../services/account-activity.action.service&quot;;

@ApiTags(&quot;Accounts&quot;)
@Controller(&quot;v1/accounts/activities&quot;)
@UseInterceptors(ResponseTransformInterceptor)
export class AccountActivityActionController {
  private readonly logSpanId &#x3D; &quot;[AccountActivityActionController]&quot;;

  constructor(
    private readonly accountActivityActionService: AccountActivityActionService,
    @Inject(&quot;Sentry&quot;) private readonly sentryService: SentryService,
    @Inject(&quot;CustomLogger&quot;) private readonly logger: ILogger,
  ) {}

  @Get()
  @ApiResponseMessage(&quot;Account activities fetched successfully!&quot;)
  @ApiGetEndpoint({
    summary: &quot;Fetches all account activities&quot;,
    responseType: AccountActivityResponseDto,
  })
  async getAccountActivities(
    @CurrentUser() user: CurrentUser,
    @Query() findAccountActivityDto: FindAccountActivityDto,
  ): Promise&lt;AccountActivityResponseDto[]&gt; {
    try {
      const activities &#x3D;
        await this.accountActivityActionService.findAccountActivities(
          user,
          findAccountActivityDto,
        );

      return activities.map(AccountActivityResponseDto.fromEntity);
    } catch (error) {
      this.logger.error(
        &#x60;${this.logSpanId} Error encountered while fetching account activities. &gt; Error message: ${error.message}&#x60;,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: &quot;ACCOUNTS_ACTIVITY_CONTROLLER&quot;,
        fn: &quot;getAccountActivities&quot;,
        organizationId: user.orgUid,
        query: findAccountActivityDto,
        user,
      });

      throw new InternalServerErrorException(&quot;Something went wrong!&quot;);
    }
  }

  @Post()
  @ApiResponseMessage(&quot;Account activity created successfully!&quot;)
  @ApiCreateEndpoint({
    summary: &quot;Creates an account activity&quot;,
    responseType: AccountActivityResponseDto,
  })
  async createAccountActivity(
    @CurrentUser() user: CurrentUser,
    @Body() createAccountActivityDto: CreateAccountActivityDto,
  ): Promise&lt;AccountActivityResponseDto&gt; {
    try {
      const createdActivity &#x3D;
        await this.accountActivityActionService.createAccountActivity(
          user,
          createAccountActivityDto,
        );

      return AccountActivityResponseDto.fromEntity(createdActivity);
    } catch (error) {
      this.logger.error(
        &#x60;${this.logSpanId} Error encountered while creating account activity. &gt; Error message: ${error.message}&#x60;,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: &quot;ACCOUNTS_ACTIVITY_CONTROLLER&quot;,
        fn: &quot;createAccountActivity&quot;,
        organizationId: user.orgUid,
        body: createAccountActivityDto,
        user,
      });

      throw new InternalServerErrorException(&quot;Something went wrong!&quot;);
    }
  }

  @Put(&quot;:activityId&quot;)
  @ApiResponseMessage(&quot;Account activity updated successfully!&quot;)
  @ApiUpdateEndpoint({
    summary: &quot;Updates an account activity&quot;,
    responseType: AccountActivityResponseDto,
  })
  async updateAccountActivity(
    @CurrentUser() user: CurrentUser,
    @Param(&quot;activityId&quot;) activityId: string,
    @Body() updateAccountActivityDto: UpdateAccountActivityDto,
  ): Promise&lt;AccountActivityResponseDto&gt; {
    if (!activityId) {
      throw new BadRequestException(&quot;Activity ID is required!&quot;);
    }

    try {
      const updatedActivity &#x3D;
        await this.accountActivityActionService.updateAccountActivity(
          user,
          activityId,
          updateAccountActivityDto,
        );

      return AccountActivityResponseDto.fromEntity(updatedActivity);
    } catch (error) {
      this.logger.error(
        &#x60;${this.logSpanId} Error encountered while updating account activity. &gt; Error message: ${error.message}&#x60;,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: &quot;ACCOUNTS_ACTIVITY_CONTROLLER&quot;,
        fn: &quot;updateAccountActivity&quot;,
        organizationId: user.orgUid,
        params: { activityId },
        body: updateAccountActivityDto,
        user,
      });

      throw new InternalServerErrorException(&quot;Something went wrong!&quot;);
    }
  }

  @Delete(&quot;:activityId&quot;)
  @ApiResponseMessage(&quot;Account activity deleted successfully!&quot;)
  @ApiDeleteEndpoint({
    summary: &quot;Deletes an account activity&quot;,
  })
  async deleteAccountActivity(
    @CurrentUser() user: CurrentUser,
    @Param(&quot;activityId&quot;) activityId: string,
  ): Promise&lt;void&gt; {
    if (!activityId) {
      throw new BadRequestException(&quot;Activity ID is required!&quot;);
    }

    try {
      await this.accountActivityActionService.deleteAccountActivity(
        user,
        activityId,
      );
    } catch (error) {
      this.logger.error(
        &#x60;${this.logSpanId} Error encountered while deleting account activity. &gt; Error message: ${error.message}&#x60;,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: &quot;ACCOUNTS_ACTIVITY_CONTROLLER&quot;,
        fn: &quot;deleteAccountActivity&quot;,
        organizationId: user.orgUid,
        params: { activityId },
        user,
      });

      throw new InternalServerErrorException(&quot;Something went wrong!&quot;);
    }
  }

  @Delete(&quot;:activityId/attachments/:attachmentId&quot;)
  @ApiResponseMessage(&quot;Attachment removed successfully!&quot;)
  @ApiDeleteEndpoint({
    summary: &quot;Removes an attachment from an account activity&quot;,
  })
  async removeActivityAttachment(
    @CurrentUser() user: CurrentUser,
    @Param(&quot;activityId&quot;) activityId: string,
    @Param(&quot;attachmentId&quot;) attachmentId: string,
  ): Promise&lt;void&gt; {
    if (!activityId) {
      throw new BadRequestException(&quot;Activity ID is required!&quot;);
    }

    if (!attachmentId) {
      throw new BadRequestException(&quot;Attachment ID is required!&quot;);
    }

    try {
      await this.accountActivityActionService.removeActivityAttachment(
        user,
        activityId,
        attachmentId,
      );
    } catch (error) {
      this.logger.error(
        &#x60;${this.logSpanId} Error encountered while removing activity attachment. &gt; Error message: ${error.message}&#x60;,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: &quot;ACCOUNTS_ACTIVITY_CONTROLLER&quot;,
        fn: &quot;removeActivityAttachment&quot;,
        organizationId: user.orgUid,
        params: { activityId, attachmentId },
        user,
      });

      throw new InternalServerErrorException(&quot;Something went wrong!&quot;);
    }
  }
}
</code></pre>
    </div>
</div>
















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'controller';
            var COMPODOC_CURRENT_PAGE_URL = 'AccountActivityActionController.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
