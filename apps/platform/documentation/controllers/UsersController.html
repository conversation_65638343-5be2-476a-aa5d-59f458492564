<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content controller">
                   <div class="content-data">





<ol class="breadcrumb">
  <li class="breadcrumb-item">Controllers</li>
  <li class="breadcrumb-item" >UsersController</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/users/controllers/users.controller.ts</code>
        </p>

            <p class="comment">
                <h3>Prefix</h3>
            </p>
            <p class="comment">
                <code>v1/users</code>
            </p>






            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#listUsers" >listUsers</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#updateBusinessHours" >updateBusinessHours</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#updateUserAvailability" >updateUserAvailability</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="listUsers"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>listUsers</b></span>
                        <a href="#listUsers"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>listUsers(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get(&#x27;/list&#x27;)<br />@ApiResponseMessage(&#x27;Users fetched successfully!&#x27;)<br />@ApiGetEndpoint({summary: &#x27;Fetch all users&#x27;, responseType: CommonUserConfigurationsResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="48"
                                    class="link-to-prism">src/users/controllers/users.controller.ts:48</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateBusinessHours"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>updateBusinessHours</b></span>
                        <a href="#updateBusinessHours"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateBusinessHours(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, body: <a href="../classes/UpdateTimezoneWorkingHoursDto.html" target="_self">UpdateTimezoneWorkingHoursDto</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Patch(&#x27;/business-hours&#x27;)<br />@ApiBody({type: UpdateTimezoneWorkingHoursDto})<br />@ApiResponseMessage(&#x27;Working hours updated successfully!&#x27;)<br />@ApiUpdateEndpoint({summary: &#x27;Update your working hours!&#x27;, responseType: CommonUserConfigurationsResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="68"
                                    class="link-to-prism">src/users/controllers/users.controller.ts:68</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>body</td>
                                            <td>
                                                            <code><a href="../classes/UpdateTimezoneWorkingHoursDto.html" target="_self" >UpdateTimezoneWorkingHoursDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateUserAvailability"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>updateUserAvailability</b></span>
                        <a href="#updateUserAvailability"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateUserAvailability(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, body: <a href="../classes/UpdateUserAvailabilityDto.html" target="_self">UpdateUserAvailabilityDto</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Patch(&#x27;/availability&#x27;)<br />@ApiBody({type: UpdateUserAvailabilityDto})<br />@ApiResponseMessage(&#x27;Availability updated successfully!&#x27;)<br />@ApiUpdateEndpoint({summary: &#x27;Update your availability&#x27;, responseType: UpdateUserAvailabilityResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="83"
                                    class="link-to-prism">src/users/controllers/users.controller.ts:83</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>body</td>
                                            <td>
                                                            <code><a href="../classes/UpdateUserAvailabilityDto.html" target="_self" >UpdateUserAvailabilityDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  Body,
  Controller,
  Get,
  Inject,
  InternalServerErrorException,
  Patch,
  UseInterceptors,
} from &quot;@nestjs/common&quot;;
import { ApiBody, ApiTags } from &quot;@nestjs/swagger&quot;;
import {
  ApiGetEndpoint,
  ApiResponseMessage,
  ApiUpdateEndpoint,
  ResponseTransformInterceptor,
} from &quot;@repo/nestjs-commons/decorators&quot;;
import { CUSTOM_LOGGER_TOKEN, ILogger } from &quot;@repo/nestjs-commons/logger&quot;;
import { GENERIC_ERROR_MESSAGES } from &quot;@repo/thena-shared-libs&quot;;
import { CurrentUser } from &quot;../../common/decorators&quot;;
import { UpdateTimezoneWorkingHoursDto } from &quot;../../common/dto&quot;;
import {
  CommonUserConfigurationsResponse,
  UpdateUserAvailabilityResponse,
} from &quot;../dto/response&quot;;
import { UpdateUserAvailabilityDto } from &quot;../dto/users.dto&quot;;
import { UsersService } from &quot;../services/users.service&quot;;
import {
  UserConfigurationsResponseDto,
  UserResponseDto,
} from &quot;../transformers&quot;;

@ApiTags(&quot;Users&quot;)
@Controller(&quot;v1/users&quot;)
@UseInterceptors(ResponseTransformInterceptor)
export class UsersController {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN)
    private readonly logger: ILogger,
    private readonly usersService: UsersService,
  ) {}

  @Get(&quot;/list&quot;)
  @ApiResponseMessage(&quot;Users fetched successfully!&quot;)
  @ApiGetEndpoint({
    summary: &quot;Fetch all users&quot;,
    responseType: CommonUserConfigurationsResponse,
  })
  async listUsers(@CurrentUser() user: CurrentUser) {
    try {
      const users &#x3D; await this.usersService.listUsers(user);
      return users.map((u) &#x3D;&gt; UserResponseDto.fromEntity(u));
    } catch (error) {
      this.logger.error(&#x60;Error fetching users: ${error.message}&#x60;, error?.stack);

      throw new InternalServerErrorException(
        GENERIC_ERROR_MESSAGES.SOMETHING_WENT_WRONG,
      );
    }
  }

  @Patch(&quot;/business-hours&quot;)
  @ApiBody({ type: UpdateTimezoneWorkingHoursDto })
  @ApiResponseMessage(&quot;Working hours updated successfully!&quot;)
  @ApiUpdateEndpoint({
    summary: &quot;Update your working hours!&quot;,
    responseType: CommonUserConfigurationsResponse,
  })
  async updateBusinessHours(
    @CurrentUser() user: CurrentUser,
    @Body() body: UpdateTimezoneWorkingHoursDto,
  ) {
    const result &#x3D; await this.usersService.updateBusinessHours(user, body);
    return UserConfigurationsResponseDto.fromEntity(result);
  }

  @Patch(&quot;/availability&quot;)
  @ApiBody({ type: UpdateUserAvailabilityDto })
  @ApiResponseMessage(&quot;Availability updated successfully!&quot;)
  @ApiUpdateEndpoint({
    summary: &quot;Update your availability&quot;,
    responseType: UpdateUserAvailabilityResponse,
  })
  async updateUserAvailability(
    @CurrentUser() user: CurrentUser,
    @Body() body: UpdateUserAvailabilityDto,
  ) {
    // Update the user availability
    await this.usersService.updateUserAvailability(user, body.availability);
    return { availability: body.availability };
  }
}
</code></pre>
    </div>
</div>
















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'controller';
            var COMPODOC_CURRENT_PAGE_URL = 'UsersController.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
