<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content controller">
                   <div class="content-data">





<ol class="breadcrumb">
  <li class="breadcrumb-item">Controllers</li>
  <li class="breadcrumb-item" >CommentsGrpcController</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/communications/controllers/grpc/comments-grpc.controller.ts</code>
        </p>

            <p class="comment">
                <h3>Prefix</h3>
            </p>
            <p class="comment">
                <code>v1/comments</code>
            </p>






            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#commentTypeToGRPC" >commentTypeToGRPC</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#commentVisibilityToGRPC" >commentVisibilityToGRPC</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#createComment" >createComment</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#deleteComment" >deleteComment</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getComment" >getComment</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getCommentsOnAnEntity" >getCommentsOnAnEntity</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getCommentThreads" >getCommentThreads</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#gRPCToCommentEntityType" >gRPCToCommentEntityType</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#gRPCToCommentType" >gRPCToCommentType</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#gRPCToCommentVisibility" >gRPCToCommentVisibility</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#updateComment" >updateComment</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="commentTypeToGRPC"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>commentTypeToGRPC</b></span>
                        <a href="#commentTypeToGRPC"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>commentTypeToGRPC(commentType: CommentType)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="81"
                                    class="link-to-prism">src/communications/controllers/grpc/comments-grpc.controller.ts:81</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>commentType</td>
                                            <td>
                                                        <code>CommentType</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>communication.CommentType</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="commentVisibilityToGRPC"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>commentVisibilityToGRPC</b></span>
                        <a href="#commentVisibilityToGRPC"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>commentVisibilityToGRPC(commentVisibility: CommentVisibility)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="55"
                                    class="link-to-prism">src/communications/controllers/grpc/comments-grpc.controller.ts:55</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>commentVisibility</td>
                                            <td>
                                                        <code>CommentVisibility</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>communication.CommentVisibility</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createComment"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>createComment</b></span>
                        <a href="#createComment"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createComment(request: <a href="../interfaces/CreateComment.html" target="_self">communication.CreateCommentRequest</a>, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(communication.COMMUNICATION_SERVICE_NAME, &#x27;CreateCommentOnAnEntity&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="98"
                                    class="link-to-prism">src/communications/controllers/grpc/comments-grpc.controller.ts:98</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../interfaces/CreateComment.html" target="_self" >communication.CreateCommentRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;communication.CommentResponse&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteComment"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>deleteComment</b></span>
                        <a href="#deleteComment"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteComment(request: communication.DeleteCommentRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(communication.COMMUNICATION_SERVICE_NAME, &#x27;DeleteComment&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="258"
                                    class="link-to-prism">src/communications/controllers/grpc/comments-grpc.controller.ts:258</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                        <code>communication.DeleteCommentRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;communication.Empty&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getComment"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getComment</b></span>
                        <a href="#getComment"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getComment(request: communication.GetCommentRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(communication.COMMUNICATION_SERVICE_NAME, &#x27;GetComment&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="179"
                                    class="link-to-prism">src/communications/controllers/grpc/comments-grpc.controller.ts:179</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                        <code>communication.GetCommentRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;communication.CommentResponse&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getCommentsOnAnEntity"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getCommentsOnAnEntity</b></span>
                        <a href="#getCommentsOnAnEntity"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getCommentsOnAnEntity(request: communication.GetCommentsRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(communication.COMMUNICATION_SERVICE_NAME, &#x27;GetCommentsOnAnEntity&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="144"
                                    class="link-to-prism">src/communications/controllers/grpc/comments-grpc.controller.ts:144</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                        <code>communication.GetCommentsRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;communication.GetCommentsResponse&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getCommentThreads"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getCommentThreads</b></span>
                        <a href="#getCommentThreads"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getCommentThreads(request: communication.GetCommentThreadsRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(communication.COMMUNICATION_SERVICE_NAME, &#x27;GetCommentThreads&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="202"
                                    class="link-to-prism">src/communications/controllers/grpc/comments-grpc.controller.ts:202</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                        <code>communication.GetCommentThreadsRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;communication.GetCommentThreadsResponse&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="gRPCToCommentEntityType"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>gRPCToCommentEntityType</b></span>
                        <a href="#gRPCToCommentEntityType"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>gRPCToCommentEntityType(entityType: communication.CommentEntityTypes)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="25"
                                    class="link-to-prism">src/communications/controllers/grpc/comments-grpc.controller.ts:25</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>entityType</td>
                                            <td>
                                                        <code>communication.CommentEntityTypes</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>CommentEntityTypes</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="gRPCToCommentType"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>gRPCToCommentType</b></span>
                        <a href="#gRPCToCommentType"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>gRPCToCommentType(commentType: communication.CommentType)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="68"
                                    class="link-to-prism">src/communications/controllers/grpc/comments-grpc.controller.ts:68</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>commentType</td>
                                            <td>
                                                        <code>communication.CommentType</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>CommentType</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="gRPCToCommentVisibility"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>gRPCToCommentVisibility</b></span>
                        <a href="#gRPCToCommentVisibility"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>gRPCToCommentVisibility(commentVisibility: communication.CommentVisibility)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="42"
                                    class="link-to-prism">src/communications/controllers/grpc/comments-grpc.controller.ts:42</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>commentVisibility</td>
                                            <td>
                                                        <code>communication.CommentVisibility</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>CommentVisibility</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateComment"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>updateComment</b></span>
                        <a href="#updateComment"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateComment(request: communication.UpdateCommentRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(communication.COMMUNICATION_SERVICE_NAME, &#x27;UpdateComment&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="230"
                                    class="link-to-prism">src/communications/controllers/grpc/comments-grpc.controller.ts:230</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                        <code>communication.UpdateCommentRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;communication.CommentResponse&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Metadata } from &quot;@grpc/grpc-js&quot;;
import { Controller, UseGuards } from &quot;@nestjs/common&quot;;
import { GrpcMethod } from &quot;@nestjs/microservices&quot;;
import { GrpcAuthGuard } from &quot;@repo/nestjs-commons/guards&quot;;
import { extractUserMetadata } from &quot;@repo/nestjs-commons/utils&quot;;
import { communication } from &quot;@repo/shared-proto&quot;;
import {
  CommentEntityTypes,
  CommentType,
  CommentVisibility,
} from &quot;@repo/thena-platform-entities&quot;;
import { CreateCommentDto } from &quot;src/common/dto/comments.dto&quot;;
import { CommentResponseDto } from &quot;src/communications/transformers/comment-response.transformer&quot;;
import { CommentsActionService } from &quot;../../services/comments.action.service&quot;;
import { CommunicationsService } from &quot;../../services/communications.service&quot;;

@Controller(&quot;v1/comments&quot;)
@UseGuards(GrpcAuthGuard)
export class CommentsGrpcController {
  constructor(
    private readonly communicationsService: CommunicationsService,
    private readonly commentsService: CommentsActionService,
  ) {}

  private gRPCToCommentEntityType(
    entityType: communication.CommentEntityTypes,
  ): CommentEntityTypes {
    switch (entityType) {
      case communication.CommentEntityTypes.TICKET:
        return CommentEntityTypes.TICKET;
      case communication.CommentEntityTypes.ACCOUNT_ACTIVITY:
        return CommentEntityTypes.ACCOUNT_ACTIVITY;
      case communication.CommentEntityTypes.ACCOUNT_NOTE:
        return CommentEntityTypes.ACCOUNT_NOTE;
      case communication.CommentEntityTypes.ACCOUNT_TASK:
        return CommentEntityTypes.ACCOUNT_TASK;
      default:
        throw new Error(&quot;Invalid comment entity type&quot;);
    }
  }

  private gRPCToCommentVisibility(
    commentVisibility: communication.CommentVisibility,
  ): CommentVisibility {
    switch (commentVisibility) {
      case communication.CommentVisibility.PUBLIC:
        return CommentVisibility.PUBLIC;
      case communication.CommentVisibility.PRIVATE:
        return CommentVisibility.PRIVATE;
      default:
        throw new Error(&quot;Invalid comment visibility&quot;);
    }
  }

  private commentVisibilityToGRPC(
    commentVisibility: CommentVisibility,
  ): communication.CommentVisibility {
    switch (commentVisibility) {
      case CommentVisibility.PUBLIC:
        return communication.CommentVisibility.PUBLIC;
      case CommentVisibility.PRIVATE:
        return communication.CommentVisibility.PRIVATE;
      default:
        throw new Error(&quot;Invalid comment visibility&quot;);
    }
  }

  private gRPCToCommentType(
    commentType: communication.CommentType,
  ): CommentType {
    switch (commentType) {
      case communication.CommentType.COMMENT:
        return CommentType.COMMENT;
      case communication.CommentType.NOTE:
        return CommentType.NOTE;
      default:
        throw new Error(&quot;Invalid comment type&quot;);
    }
  }

  private commentTypeToGRPC(
    commentType: CommentType,
  ): communication.CommentType {
    switch (commentType) {
      case CommentType.COMMENT:
        return communication.CommentType.COMMENT;
      case CommentType.NOTE:
        return communication.CommentType.NOTE;
      default:
        throw new Error(&quot;Invalid comment type&quot;);
    }
  }

  @GrpcMethod(
    communication.COMMUNICATION_SERVICE_NAME,
    &quot;CreateCommentOnAnEntity&quot;,
  )
  async createComment(
    request: communication.CreateCommentRequest,
    metadata: Metadata,
  ): Promise&lt;communication.CommentResponse&gt; {
    const user &#x3D; extractUserMetadata(metadata);

    const entity &#x3D; await this.communicationsService.getEntity(
      user,
      this.gRPCToCommentEntityType(request.entityType),
      request.entityId,
    );

    // Form create comment dto
    const createCommentDto: CreateCommentDto &#x3D; {
      content: request.content,
      parentCommentId: request.parentCommentId,
      commentVisibility:
        this.gRPCToCommentVisibility(request.commentVisibility) ??
        CommentVisibility.PRIVATE,
      commentType:
        this.gRPCToCommentType(request.commentType) ?? CommentType.NOTE,
      threadName: request.threadName,
      metadata: JSON.parse(request.metadata || &quot;{}&quot;),
      attachmentUrls: request.attachmentUrls,
    };

    // Create the comment
    const comment &#x3D; await this.communicationsService.createCommentOnAnEntity(
      entity,
      this.gRPCToCommentEntityType(request.entityType),
      createCommentDto,
      user,
    );

    const commentResponse &#x3D; CommentResponseDto.fromEntity(comment);

    return {
      ...commentResponse,
      commentVisibility: this.commentVisibilityToGRPC(
        commentResponse.commentVisibility,
      ),
      commentType: this.commentTypeToGRPC(commentResponse.commentType),
    };
  }

  @GrpcMethod(communication.COMMUNICATION_SERVICE_NAME, &quot;GetCommentsOnAnEntity&quot;)
  async getCommentsOnAnEntity(
    request: communication.GetCommentsRequest,
    metadata: Metadata,
  ): Promise&lt;communication.GetCommentsResponse&gt; {
    const user &#x3D; extractUserMetadata(metadata);

    const entity &#x3D; await this.communicationsService.getEntity(
      user,
      this.gRPCToCommentEntityType(request.entityType),
      request.entityId,
    );

    const comments &#x3D; await this.communicationsService.getCommentsForAnEntity(
      entity,
      this.gRPCToCommentEntityType(request.entityType),
      request,
      user,
    );

    const commentsResponses &#x3D; comments.results.map(
      CommentResponseDto.fromEntity,
    );

    return {
      comments: commentsResponses.map((comment) &#x3D;&gt; ({
        ...comment,
        commentVisibility: this.commentVisibilityToGRPC(
          comment.commentVisibility,
        ),
        commentType: this.commentTypeToGRPC(comment.commentType),
      })),
    };
  }

  @GrpcMethod(communication.COMMUNICATION_SERVICE_NAME, &quot;GetComment&quot;)
  async getComment(
    request: communication.GetCommentRequest,
    metadata: Metadata,
  ): Promise&lt;communication.CommentResponse&gt; {
    const user &#x3D; extractUserMetadata(metadata);

    const comment &#x3D; await this.commentsService.getPopulatedCommentById(
      request.commentId,
      user.orgId,
    );

    const commentResponse &#x3D; CommentResponseDto.fromEntity(comment);

    return {
      ...commentResponse,
      commentVisibility: this.commentVisibilityToGRPC(
        commentResponse.commentVisibility,
      ),
      commentType: this.commentTypeToGRPC(commentResponse.commentType),
    };
  }

  @GrpcMethod(communication.COMMUNICATION_SERVICE_NAME, &quot;GetCommentThreads&quot;)
  async getCommentThreads(
    request: communication.GetCommentThreadsRequest,
    metadata: Metadata,
  ): Promise&lt;communication.GetCommentThreadsResponse&gt; {
    const user &#x3D; extractUserMetadata(metadata);

    const comments &#x3D; await this.commentsService.getCommentThreads(
      request.commentId,
      request,
      user,
    );

    const commentsResponses &#x3D; comments.results.map(
      CommentResponseDto.fromEntity,
    );

    return {
      comments: commentsResponses.map((comment) &#x3D;&gt; ({
        ...comment,
        commentVisibility: this.commentVisibilityToGRPC(
          comment.commentVisibility,
        ),
        commentType: this.commentTypeToGRPC(comment.commentType),
      })),
    };
  }

  @GrpcMethod(communication.COMMUNICATION_SERVICE_NAME, &quot;UpdateComment&quot;)
  async updateComment(
    request: communication.UpdateCommentRequest,
    metadata: Metadata,
  ): Promise&lt;communication.CommentResponse&gt; {
    const user &#x3D; extractUserMetadata(metadata);

    const comment &#x3D; await this.commentsService.updateComment(
      request.commentId,
      {
        content: request.content,
        threadName: request.threadName,
        attachments: request.attachments,
      },
      user,
    );

    const commentResponse &#x3D; CommentResponseDto.fromEntity(comment);

    return {
      ...commentResponse,
      commentVisibility: this.commentVisibilityToGRPC(
        commentResponse.commentVisibility,
      ),
      commentType: this.commentTypeToGRPC(commentResponse.commentType),
    };
  }

  @GrpcMethod(communication.COMMUNICATION_SERVICE_NAME, &quot;DeleteComment&quot;)
  async deleteComment(
    request: communication.DeleteCommentRequest,
    metadata: Metadata,
  ): Promise&lt;communication.Empty&gt; {
    const user &#x3D; extractUserMetadata(metadata);

    await this.commentsService.deleteComment(request.commentId, user);

    return {};
  }
}
</code></pre>
    </div>
</div>
















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'controller';
            var COMPODOC_CURRENT_PAGE_URL = 'CommentsGrpcController.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
