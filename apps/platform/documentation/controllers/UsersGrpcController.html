<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content controller">
                   <div class="content-data">





<ol class="breadcrumb">
  <li class="breadcrumb-item">Controllers</li>
  <li class="breadcrumb-item" >UsersGrpcController</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/users/controllers/grpc/users-grpc.controller.ts</code>
        </p>

            <p class="comment">
                <h3>Prefix</h3>
            </p>
            <p class="comment">
                <code>v1/users</code>
            </p>






            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#createBotUser" >createBotUser</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getTimeOffData" >getTimeOffData</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                <a href="#getTimeOffFieldMetadata" >getTimeOffFieldMetadata</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getUserBusinessHoursConfigData" >getUserBusinessHoursConfigData</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                <a href="#getUserBusinessHoursConfigFieldMetadata" >getUserBusinessHoursConfigFieldMetadata</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getUserData" >getUserData</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                <a href="#getUserFieldMetadata" >getUserFieldMetadata</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getUserSkillsData" >getUserSkillsData</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                <a href="#getUserSkillsFieldMetadata" >getUserSkillsFieldMetadata</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#rollbackBotInstallationUserCreation" >rollbackBotInstallationUserCreation</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createBotUser"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>createBotUser</b></span>
                        <a href="#createBotUser"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createBotUser(data: users.CreateBotInstallationUserRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(GrpcAuthGuard)<br />@GrpcMethod(users.BOT_INSTALLATION_USER_SERVICE_NAME, &#x27;CreateBotInstallationUser&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="26"
                                    class="link-to-prism">src/users/controllers/grpc/users-grpc.controller.ts:26</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>data</td>
                                            <td>
                                                        <code>users.CreateBotInstallationUserRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTimeOffData"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getTimeOffData</b></span>
                        <a href="#getTimeOffData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTimeOffData(request: users.GetTimeOffDataRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(users.USER_ANNOTATOR_SERVICE_NAME, &#x27;GetTimeOffData&#x27;)<br />@UseGuards(GrpcAuthGuard)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="192"
                                    class="link-to-prism">src/users/controllers/grpc/users-grpc.controller.ts:192</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                        <code>users.GetTimeOffDataRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;users.GetTimeOffDataResponse&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTimeOffFieldMetadata"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                        <span ><b>getTimeOffFieldMetadata</b></span>
                        <a href="#getTimeOffFieldMetadata"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTimeOffFieldMetadata()</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(users.USER_ANNOTATOR_SERVICE_NAME, &#x27;GetTimeOffFieldMetadata&#x27;)<br />@UseGuards(GrpcAuthGuard)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="125"
                                    class="link-to-prism">src/users/controllers/grpc/users-grpc.controller.ts:125</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>    <code>users.GetTimeOffFieldMetadataResponse</code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getUserBusinessHoursConfigData"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getUserBusinessHoursConfigData</b></span>
                        <a href="#getUserBusinessHoursConfigData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getUserBusinessHoursConfigData(request: users.GetUserBusinessHoursConfigDataRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(users.USER_ANNOTATOR_SERVICE_NAME, &#x27;GetUserBusinessHoursConfigData&#x27;)<br />@UseGuards(GrpcAuthGuard)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="174"
                                    class="link-to-prism">src/users/controllers/grpc/users-grpc.controller.ts:174</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                        <code>users.GetUserBusinessHoursConfigDataRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;users.GetUserBusinessHoursConfigDataResponse&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getUserBusinessHoursConfigFieldMetadata"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                        <span ><b>getUserBusinessHoursConfigFieldMetadata</b></span>
                        <a href="#getUserBusinessHoursConfigFieldMetadata"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getUserBusinessHoursConfigFieldMetadata()</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(users.USER_ANNOTATOR_SERVICE_NAME, &#x27;GetUserBusinessHoursConfigFieldMetadata&#x27;)<br />@UseGuards(GrpcAuthGuard)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="115"
                                    class="link-to-prism">src/users/controllers/grpc/users-grpc.controller.ts:115</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>    <code>users.GetUserBusinessHoursConfigFieldMetadataResponse</code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getUserData"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getUserData</b></span>
                        <a href="#getUserData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getUserData(request: users.GetUserDataRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(users.USER_ANNOTATOR_SERVICE_NAME, &#x27;GetUserData&#x27;)<br />@UseGuards(GrpcAuthGuard)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="135"
                                    class="link-to-prism">src/users/controllers/grpc/users-grpc.controller.ts:135</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                        <code>users.GetUserDataRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;users.GetUserDataResponse&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getUserFieldMetadata"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                        <span ><b>getUserFieldMetadata</b></span>
                        <a href="#getUserFieldMetadata"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getUserFieldMetadata()</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(users.USER_ANNOTATOR_SERVICE_NAME, &#x27;GetUserFieldMetadata&#x27;)<br />@UseGuards(GrpcAuthGuard)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="92"
                                    class="link-to-prism">src/users/controllers/grpc/users-grpc.controller.ts:92</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>    <code>users.GetUserFieldMetadataResponse</code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getUserSkillsData"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getUserSkillsData</b></span>
                        <a href="#getUserSkillsData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getUserSkillsData(request: users.GetUserSkillsDataRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(users.USER_ANNOTATOR_SERVICE_NAME, &#x27;GetUserSkillsData&#x27;)<br />@UseGuards(GrpcAuthGuard)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="153"
                                    class="link-to-prism">src/users/controllers/grpc/users-grpc.controller.ts:153</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                        <code>users.GetUserSkillsDataRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;users.GetUserSkillsDataResponse&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getUserSkillsFieldMetadata"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                        <span ><b>getUserSkillsFieldMetadata</b></span>
                        <a href="#getUserSkillsFieldMetadata"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getUserSkillsFieldMetadata()</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(users.USER_ANNOTATOR_SERVICE_NAME, &#x27;GetUserSkillsFieldMetadata&#x27;)<br />@UseGuards(GrpcAuthGuard)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="102"
                                    class="link-to-prism">src/users/controllers/grpc/users-grpc.controller.ts:102</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>    <code>users.GetUserSkillsFieldMetadataResponse</code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="rollbackBotInstallationUserCreation"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>rollbackBotInstallationUserCreation</b></span>
                        <a href="#rollbackBotInstallationUserCreation"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>rollbackBotInstallationUserCreation(data: users.RollbackBotInstallationUserRequest)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(users.BOT_INSTALLATION_USER_SERVICE_NAME, &#x27;RollbackBotInstallationUserCreation&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="69"
                                    class="link-to-prism">src/users/controllers/grpc/users-grpc.controller.ts:69</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>data</td>
                                            <td>
                                                        <code>users.RollbackBotInstallationUserRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Metadata } from &quot;@grpc/grpc-js&quot;;
import { Controller, Inject, UseGuards } from &quot;@nestjs/common&quot;;
import { GrpcMethod } from &quot;@nestjs/microservices&quot;;
import { handleRpcError } from &quot;@repo/nestjs-commons/errors&quot;;
import { GrpcAuthGuard } from &quot;@repo/nestjs-commons/guards&quot;;
import { CUSTOM_LOGGER_TOKEN, ILogger } from &quot;@repo/nestjs-commons/logger&quot;;
import { extractUserMetadata } from &quot;@repo/nestjs-commons/utils&quot;;
import { users } from &quot;@repo/shared-proto&quot;;
import { UsersGrpcService } from &quot;../../services/grpc/users-grpc.service&quot;;
import { UserAnnotatorService } from &quot;../../services/user-annotator.service&quot;;

@Controller(&quot;v1/users&quot;)
export class UsersGrpcController {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN)
    private readonly logger: ILogger,
    private readonly usersGrpcService: UsersGrpcService,
    private readonly userAnnotatorService: UserAnnotatorService,
  ) {}

  @UseGuards(GrpcAuthGuard)
  @GrpcMethod(
    users.BOT_INSTALLATION_USER_SERVICE_NAME,
    &quot;CreateBotInstallationUser&quot;,
  )
  async createBotUser(
    data: users.CreateBotInstallationUserRequest,
    metadata: Metadata,
  ) {
    try {
      const userMetadata &#x3D; extractUserMetadata(metadata);

      // Create the bot user
      const { user, teams, appKey, appSecretKey } &#x3D;
        await this.usersGrpcService.createBotUser(data, userMetadata.token);

      // Create the response
      const response: users.CreateBotInstallationUserResponse &#x3D; {
        id: user.id,
        uid: user.uid,
        name: user.name,
        transactionId: user.uid,
        userType: users.UserType.BOT_USER,
        organizationId: user.organizationId,
        teamIds: teams.map((team) &#x3D;&gt; team.uid),
        accessToken: &quot;&quot;,
        refreshToken: &quot;&quot;,
        appKey,
        appSecretKey,
      };

      return response;
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          &#x60;Error encountered while creating bot user: ${error.message}&#x60;,
          error?.stack,
        );
      }

      handleRpcError(error);
    }
  }

  @GrpcMethod(
    users.BOT_INSTALLATION_USER_SERVICE_NAME,
    &quot;RollbackBotInstallationUserCreation&quot;,
  )
  async rollbackBotInstallationUserCreation(
    data: users.RollbackBotInstallationUserRequest,
  ) {
    try {
      // Rollback the bot installation user creation
      await this.usersGrpcService.rollbackBotInstallation(data);

      // Create the response
      const response: users.RollbackBotInstallationUserResponse &#x3D; {
        success: true,
        message: &quot;Bot installation user creation rolled back successfully!&quot;,
        teamIds: data.teamIds,
        status: users.RollbackStatus.ROLLBACK_STATUS_SUCCESS,
      };

      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(users.USER_ANNOTATOR_SERVICE_NAME, &quot;GetUserFieldMetadata&quot;)
  @UseGuards(GrpcAuthGuard)
  getUserFieldMetadata(): users.GetUserFieldMetadataResponse {
    try {
      return this.userAnnotatorService.getUserFieldMetadata();
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(users.USER_ANNOTATOR_SERVICE_NAME, &quot;GetUserSkillsFieldMetadata&quot;)
  @UseGuards(GrpcAuthGuard)
  getUserSkillsFieldMetadata(): users.GetUserSkillsFieldMetadataResponse {
    try {
      return this.userAnnotatorService.getUserSkillsFieldMetadata();
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    users.USER_ANNOTATOR_SERVICE_NAME,
    &quot;GetUserBusinessHoursConfigFieldMetadata&quot;,
  )
  @UseGuards(GrpcAuthGuard)
  getUserBusinessHoursConfigFieldMetadata(): users.GetUserBusinessHoursConfigFieldMetadataResponse {
    try {
      return this.userAnnotatorService.getUserBusinessHoursConfigFieldMetadata();
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(users.USER_ANNOTATOR_SERVICE_NAME, &quot;GetTimeOffFieldMetadata&quot;)
  @UseGuards(GrpcAuthGuard)
  getTimeOffFieldMetadata(): users.GetTimeOffFieldMetadataResponse {
    try {
      return this.userAnnotatorService.getTimeOffFieldMetadata();
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(users.USER_ANNOTATOR_SERVICE_NAME, &quot;GetUserData&quot;)
  @UseGuards(GrpcAuthGuard)
  async getUserData(
    request: users.GetUserDataRequest,
    metadata: Metadata,
  ): Promise&lt;users.GetUserDataResponse&gt; {
    try {
      const { orgId } &#x3D; extractUserMetadata(metadata);
      return await this.userAnnotatorService.getUserData(
        request.userId,
        request.relations,
        orgId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(users.USER_ANNOTATOR_SERVICE_NAME, &quot;GetUserSkillsData&quot;)
  @UseGuards(GrpcAuthGuard)
  async getUserSkillsData(
    request: users.GetUserSkillsDataRequest,
    metadata: Metadata,
  ): Promise&lt;users.GetUserSkillsDataResponse&gt; {
    try {
      const { orgId } &#x3D; extractUserMetadata(metadata);
      return await this.userAnnotatorService.getUserSkillsData(
        request.userId,
        request.relations,
        orgId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    users.USER_ANNOTATOR_SERVICE_NAME,
    &quot;GetUserBusinessHoursConfigData&quot;,
  )
  @UseGuards(GrpcAuthGuard)
  async getUserBusinessHoursConfigData(
    request: users.GetUserBusinessHoursConfigDataRequest,
    metadata: Metadata,
  ): Promise&lt;users.GetUserBusinessHoursConfigDataResponse&gt; {
    try {
      const { orgId } &#x3D; extractUserMetadata(metadata);
      return await this.userAnnotatorService.getUserBusinessHoursConfigData(
        request.userId,
        request.relations,
        orgId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(users.USER_ANNOTATOR_SERVICE_NAME, &quot;GetTimeOffData&quot;)
  @UseGuards(GrpcAuthGuard)
  async getTimeOffData(
    request: users.GetTimeOffDataRequest,
    metadata: Metadata,
  ): Promise&lt;users.GetTimeOffDataResponse&gt; {
    try {
      const { orgId } &#x3D; extractUserMetadata(metadata);
      return await this.userAnnotatorService.getTimeOffData(
        request.userId,
        request.relations,
        orgId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }
}
</code></pre>
    </div>
</div>
















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'controller';
            var COMPODOC_CURRENT_PAGE_URL = 'UsersGrpcController.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
