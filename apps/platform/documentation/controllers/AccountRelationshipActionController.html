<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content controller">
                   <div class="content-data">





<ol class="breadcrumb">
  <li class="breadcrumb-item">Controllers</li>
  <li class="breadcrumb-item" >AccountRelationshipActionController</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/accounts/controllers/account-relationship.action.controller.ts</code>
        </p>

            <p class="comment">
                <h3>Prefix</h3>
            </p>
            <p class="comment">
                <code>v1/accounts/relationships</code>
            </p>






            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#createAccountRelationship" >createAccountRelationship</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#createAccountRelationshipType" >createAccountRelationshipType</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#deleteAccountRelationship" >deleteAccountRelationship</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#deleteAccountRelationshipType" >deleteAccountRelationshipType</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#findAllAccountRelationships" >findAllAccountRelationships</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#findAllAccountRelationshipTypes" >findAllAccountRelationshipTypes</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#updateAccountRelationship" >updateAccountRelationship</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#updateAccountRelationshipType" >updateAccountRelationshipType</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createAccountRelationship"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>createAccountRelationship</b></span>
                        <a href="#createAccountRelationship"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createAccountRelationship(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, createDto: <a href="../classes/CreateAccountRelationshipDto.html" target="_self">CreateAccountRelationshipDto</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Post(&#x27;&#x27;)<br />@ApiResponseMessage(&#x27;Account relationship created successfully!&#x27;)<br />@ApiCreateEndpoint({summary: &#x27;Create an account relationship&#x27;, responseType: AccountRelationshipResponseDto})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="225"
                                    class="link-to-prism">src/accounts/controllers/account-relationship.action.controller.ts:225</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>createDto</td>
                                            <td>
                                                            <code><a href="../classes/CreateAccountRelationshipDto.html" target="_self" >CreateAccountRelationshipDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/AccountRelationshipResponseDto.html" target="_self" >Promise&lt;AccountRelationshipResponseDto&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createAccountRelationshipType"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>createAccountRelationshipType</b></span>
                        <a href="#createAccountRelationshipType"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createAccountRelationshipType(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, createDto: <a href="../classes/CreateAccountRelationshipTypeDto.html" target="_self">CreateAccountRelationshipTypeDto</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Post(&#x27;/types&#x27;)<br />@ApiResponseMessage(&#x27;Account relationship type created successfully!&#x27;)<br />@ApiCreateEndpoint({summary: &#x27;Create an account relationship type&#x27;, responseType: AccountRelationshipTypeResponseDto})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="61"
                                    class="link-to-prism">src/accounts/controllers/account-relationship.action.controller.ts:61</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>createDto</td>
                                            <td>
                                                            <code><a href="../classes/CreateAccountRelationshipTypeDto.html" target="_self" >CreateAccountRelationshipTypeDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/AccountRelationshipTypeResponseDto.html" target="_self" >Promise&lt;AccountRelationshipTypeResponseDto&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteAccountRelationship"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>deleteAccountRelationship</b></span>
                        <a href="#deleteAccountRelationship"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteAccountRelationship(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Delete(&#x27;:id&#x27;)<br />@ApiResponseMessage(&#x27;Account relationship deleted successfully!&#x27;)<br />@ApiDeleteEndpoint({summary: &#x27;Delete an account relationship&#x27;})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="352"
                                    class="link-to-prism">src/accounts/controllers/account-relationship.action.controller.ts:352</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;void&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteAccountRelationshipType"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>deleteAccountRelationshipType</b></span>
                        <a href="#deleteAccountRelationshipType"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteAccountRelationshipType(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Delete(&#x27;/types/:id&#x27;)<br />@ApiResponseMessage(&#x27;Account relationship type deleted successfully!&#x27;)<br />@ApiDeleteEndpoint({summary: &#x27;Delete an account relationship type&#x27;})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="183"
                                    class="link-to-prism">src/accounts/controllers/account-relationship.action.controller.ts:183</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;void&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAllAccountRelationships"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>findAllAccountRelationships</b></span>
                        <a href="#findAllAccountRelationships"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAllAccountRelationships(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, findAllRelationshipsDto: <a href="../classes/FindAccountRelationshipDto.html" target="_self">FindAccountRelationshipDto</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get(&#x27;&#x27;)<br />@ApiResponseMessage(&#x27;Account relationships fetched successfully!&#x27;)<br />@ApiGetEndpoint({summary: &#x27;Get all account relationships&#x27;, responseType: undefined})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="265"
                                    class="link-to-prism">src/accounts/controllers/account-relationship.action.controller.ts:265</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>findAllRelationshipsDto</td>
                                            <td>
                                                            <code><a href="../classes/FindAccountRelationshipDto.html" target="_self" >FindAccountRelationshipDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/AccountRelationshipResponseDto.html" target="_self" >Promise&lt;AccountRelationshipResponseDto[]&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAllAccountRelationshipTypes"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>findAllAccountRelationshipTypes</b></span>
                        <a href="#findAllAccountRelationshipTypes"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAllAccountRelationshipTypes(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get(&#x27;/types&#x27;)<br />@ApiResponseMessage(&#x27;Account relationship types fetched successfully!&#x27;)<br />@ApiGetEndpoint({summary: &#x27;Get all account relationship types&#x27;, responseType: undefined})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="101"
                                    class="link-to-prism">src/accounts/controllers/account-relationship.action.controller.ts:101</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/AccountRelationshipTypeResponseDto.html" target="_self" >Promise&lt;AccountRelationshipTypeResponseDto[]&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateAccountRelationship"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>updateAccountRelationship</b></span>
                        <a href="#updateAccountRelationship"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateAccountRelationship(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, updateDto: <a href="../classes/UpdateAccountRelationshipDto.html" target="_self">UpdateAccountRelationshipDto</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Put(&#x27;:id&#x27;)<br />@ApiResponseMessage(&#x27;Account relationship updated successfully!&#x27;)<br />@ApiUpdateEndpoint({summary: &#x27;Update an account relationship&#x27;, responseType: AccountRelationshipResponseDto})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="307"
                                    class="link-to-prism">src/accounts/controllers/account-relationship.action.controller.ts:307</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>updateDto</td>
                                            <td>
                                                            <code><a href="../classes/UpdateAccountRelationshipDto.html" target="_self" >UpdateAccountRelationshipDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/AccountRelationshipResponseDto.html" target="_self" >Promise&lt;AccountRelationshipResponseDto&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateAccountRelationshipType"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>updateAccountRelationshipType</b></span>
                        <a href="#updateAccountRelationshipType"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateAccountRelationshipType(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, updateDto: <a href="../classes/UpdateAccountRelationshipTypeDto.html" target="_self">UpdateAccountRelationshipTypeDto</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Put(&#x27;/types/:id&#x27;)<br />@ApiResponseMessage(&#x27;Account relationship type updated successfully!&#x27;)<br />@ApiUpdateEndpoint({summary: &#x27;Update an account relationship type&#x27;, responseType: AccountRelationshipTypeResponseDto})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="136"
                                    class="link-to-prism">src/accounts/controllers/account-relationship.action.controller.ts:136</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>updateDto</td>
                                            <td>
                                                            <code><a href="../classes/UpdateAccountRelationshipTypeDto.html" target="_self" >UpdateAccountRelationshipTypeDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/AccountRelationshipTypeResponseDto.html" target="_self" >Promise&lt;AccountRelationshipTypeResponseDto&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  Inject,
  InternalServerErrorException,
  Param,
  Post,
  Put,
  Query,
  UseInterceptors,
} from &quot;@nestjs/common&quot;;
import { ApiTags } from &quot;@nestjs/swagger&quot;;
import {
  ApiCreateEndpoint,
  ApiDeleteEndpoint,
  ApiGetEndpoint,
  ApiResponseMessage,
  ApiUpdateEndpoint,
  ResponseTransformInterceptor,
} from &quot;@repo/nestjs-commons/decorators&quot;;
import { SentryService } from &quot;@repo/nestjs-commons/filters&quot;;
import { ILogger } from &quot;@repo/nestjs-commons/logger&quot;;
import { SkipAllThrottler } from &quot;../../common/decorators/throttler.decorator&quot;;
import { CurrentUser } from &quot;../../common/decorators/user.decorator&quot;;
import {
  CreateAccountRelationshipDto,
  CreateAccountRelationshipTypeDto,
  FindAccountRelationshipDto,
  UpdateAccountRelationshipDto,
  UpdateAccountRelationshipTypeDto,
} from &quot;../dtos/account-relationship.dto&quot;;
import {
  AccountRelationshipResponseDto,
  AccountRelationshipTypeResponseDto,
} from &quot;../dtos/response/account-relationship.dto&quot;;
import { AccountRelationshipActionService } from &quot;../services/account-relationship.action.service&quot;;

@ApiTags(&quot;Accounts&quot;)
@Controller(&quot;v1/accounts/relationships&quot;)
@SkipAllThrottler()
@UseInterceptors(ResponseTransformInterceptor)
export class AccountRelationshipActionController {
  private readonly logSpanId &#x3D; &quot;[AccountRelationshipActionController]&quot;;

  constructor(
    private readonly accountRelationshipActionService: AccountRelationshipActionService,
    @Inject(&quot;Sentry&quot;) private readonly sentryService: SentryService,
    @Inject(&quot;CustomLogger&quot;) private readonly logger: ILogger,
  ) {}

  @Post(&quot;/types&quot;)
  @ApiResponseMessage(&quot;Account relationship type created successfully!&quot;)
  @ApiCreateEndpoint({
    summary: &quot;Create an account relationship type&quot;,
    responseType: AccountRelationshipTypeResponseDto,
  })
  async createAccountRelationshipType(
    @CurrentUser() user: CurrentUser,
    @Body() createDto: CreateAccountRelationshipTypeDto,
  ): Promise&lt;AccountRelationshipTypeResponseDto&gt; {
    try {
      const relationshipType &#x3D;
        await this.accountRelationshipActionService.createAccountRelationshipType(
          user,
          createDto,
        );

      return AccountRelationshipTypeResponseDto.fromEntity(relationshipType);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        &#x60;${this.logSpanId} Error encountered while creating relationship type. &gt; Error message: ${error.message}&#x60;,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: &quot;ACCOUNT_RELATIONSHIP_CONTROLLER&quot;,
        fn: &quot;createAccountRelationshipType&quot;,
        organizationId: user.orgUid,
        body: createDto,
        user,
      });

      throw new InternalServerErrorException(&quot;Something went wrong!&quot;);
    }
  }

  @Get(&quot;/types&quot;)
  @ApiResponseMessage(&quot;Account relationship types fetched successfully!&quot;)
  @ApiGetEndpoint({
    summary: &quot;Get all account relationship types&quot;,
    responseType: [AccountRelationshipTypeResponseDto],
  })
  async findAllAccountRelationshipTypes(
    @CurrentUser() user: CurrentUser,
  ): Promise&lt;AccountRelationshipTypeResponseDto[]&gt; {
    try {
      const relationshipTypes &#x3D;
        await this.accountRelationshipActionService.findAllAccountRelationshipTypes(
          user.orgId,
        );

      return relationshipTypes.map((type) &#x3D;&gt;
        AccountRelationshipTypeResponseDto.fromEntity(type),
      );
    } catch (error) {
      this.logger.error(
        &#x60;${this.logSpanId} Error encountered while fetching relationship types. &gt; Error message: ${error.message}&#x60;,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: &quot;ACCOUNT_RELATIONSHIP_CONTROLLER&quot;,
        fn: &quot;findAllAccountRelationshipTypes&quot;,
        organizationId: user.orgUid,
        user,
      });

      throw new InternalServerErrorException(&quot;Something went wrong!&quot;);
    }
  }

  @Put(&quot;/types/:id&quot;)
  @ApiResponseMessage(&quot;Account relationship type updated successfully!&quot;)
  @ApiUpdateEndpoint({
    summary: &quot;Update an account relationship type&quot;,
    responseType: AccountRelationshipTypeResponseDto,
  })
  async updateAccountRelationshipType(
    @CurrentUser() user: CurrentUser,
    @Param(&quot;id&quot;) id: string,
    @Body() updateDto: UpdateAccountRelationshipTypeDto,
  ): Promise&lt;AccountRelationshipTypeResponseDto&gt; {
    if (!id)
      throw new BadRequestException(
        &quot;Account relationship type ID is required!&quot;,
      );

    try {
      const relationshipType &#x3D;
        await this.accountRelationshipActionService.updateAccountRelationshipType(
          user,
          id,
          updateDto,
        );

      return AccountRelationshipTypeResponseDto.fromEntity(relationshipType);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        &#x60;${this.logSpanId} Error encountered while updating relationship type. &gt; Error message: ${error.message}&#x60;,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: &quot;ACCOUNT_RELATIONSHIP_CONTROLLER&quot;,
        fn: &quot;updateAccountRelationshipType&quot;,
        organizationId: user.orgUid,
        params: { uid: id },
        body: updateDto,
        user,
      });

      throw new InternalServerErrorException(&quot;Something went wrong!&quot;);
    }
  }

  @Delete(&quot;/types/:id&quot;)
  @ApiResponseMessage(&quot;Account relationship type deleted successfully!&quot;)
  @ApiDeleteEndpoint({
    summary: &quot;Delete an account relationship type&quot;,
  })
  async deleteAccountRelationshipType(
    @CurrentUser() user: CurrentUser,
    @Param(&quot;id&quot;) id: string,
  ): Promise&lt;void&gt; {
    if (!id)
      throw new BadRequestException(
        &quot;Account relationship type ID is required!&quot;,
      );

    try {
      await this.accountRelationshipActionService.deleteAccountRelationshipType(
        user,
        id,
      );
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        &#x60;${this.logSpanId} Error encountered while deleting relationship type. &gt; Error message: ${error.message}&#x60;,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: &quot;ACCOUNT_RELATIONSHIP_CONTROLLER&quot;,
        fn: &quot;deleteAccountRelationshipType&quot;,
        organizationId: user.orgUid,
        params: { uid: id },
        user,
      });

      throw new InternalServerErrorException(&quot;Something went wrong!&quot;);
    }
  }

  @Post(&quot;&quot;)
  @ApiResponseMessage(&quot;Account relationship created successfully!&quot;)
  @ApiCreateEndpoint({
    summary: &quot;Create an account relationship&quot;,
    responseType: AccountRelationshipResponseDto,
  })
  async createAccountRelationship(
    @CurrentUser() user: CurrentUser,
    @Body() createDto: CreateAccountRelationshipDto,
  ): Promise&lt;AccountRelationshipResponseDto&gt; {
    try {
      const relationship &#x3D;
        await this.accountRelationshipActionService.createAccountRelationship(
          user,
          createDto,
        );

      return AccountRelationshipResponseDto.fromEntity(relationship);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        &#x60;${this.logSpanId} Error encountered while creating relationship. &gt; Error message: ${error.message}&#x60;,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: &quot;ACCOUNT_RELATIONSHIP_CONTROLLER&quot;,
        fn: &quot;createAccountRelationship&quot;,
        organizationId: user.orgUid,
        body: createDto,
        user,
      });

      throw new InternalServerErrorException(&quot;Something went wrong!&quot;);
    }
  }

  @Get(&quot;&quot;)
  @ApiResponseMessage(&quot;Account relationships fetched successfully!&quot;)
  @ApiGetEndpoint({
    summary: &quot;Get all account relationships&quot;,
    responseType: [AccountRelationshipResponseDto],
  })
  async findAllAccountRelationships(
    @CurrentUser() user: CurrentUser,
    @Query() findAllRelationshipsDto: FindAccountRelationshipDto,
  ): Promise&lt;AccountRelationshipResponseDto[]&gt; {
    try {
      const relationships &#x3D;
        await this.accountRelationshipActionService.findAllAccountRelationships(
          user,
          findAllRelationshipsDto,
        );

      return relationships.map((relationship) &#x3D;&gt;
        AccountRelationshipResponseDto.fromEntity(relationship),
      );
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        &#x60;${this.logSpanId} Error encountered while fetching relationships. &gt; Error message: ${error.message}&#x60;,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: &quot;ACCOUNT_RELATIONSHIP_CONTROLLER&quot;,
        fn: &quot;findAllAccountRelationships&quot;,
        organizationId: user.orgUid,
        query: findAllRelationshipsDto,
        user,
      });

      throw new InternalServerErrorException(&quot;Something went wrong!&quot;);
    }
  }

  @Put(&quot;:id&quot;)
  @ApiResponseMessage(&quot;Account relationship updated successfully!&quot;)
  @ApiUpdateEndpoint({
    summary: &quot;Update an account relationship&quot;,
    responseType: AccountRelationshipResponseDto,
  })
  async updateAccountRelationship(
    @CurrentUser() user: CurrentUser,
    @Param(&quot;id&quot;) id: string,
    @Body() updateDto: UpdateAccountRelationshipDto,
  ): Promise&lt;AccountRelationshipResponseDto&gt; {
    if (!id)
      throw new BadRequestException(&quot;Account relationship ID is required!&quot;);

    try {
      const relationship &#x3D;
        await this.accountRelationshipActionService.updateAccountRelationship(
          user,
          id,
          updateDto,
        );

      return AccountRelationshipResponseDto.fromEntity(relationship);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        &#x60;${this.logSpanId} Error encountered while updating relationship. &gt; Error message: ${error.message}&#x60;,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: &quot;ACCOUNT_RELATIONSHIP_CONTROLLER&quot;,
        fn: &quot;updateAccountRelationship&quot;,
        organizationId: user.orgUid,
        params: { uid: id },
        body: updateDto,
        user,
      });

      throw new InternalServerErrorException(&quot;Something went wrong!&quot;);
    }
  }

  @Delete(&quot;:id&quot;)
  @ApiResponseMessage(&quot;Account relationship deleted successfully!&quot;)
  @ApiDeleteEndpoint({
    summary: &quot;Delete an account relationship&quot;,
  })
  async deleteAccountRelationship(
    @CurrentUser() user: CurrentUser,
    @Param(&quot;id&quot;) id: string,
  ): Promise&lt;void&gt; {
    if (!id)
      throw new BadRequestException(&quot;Account relationship ID is required!&quot;);

    try {
      await this.accountRelationshipActionService.deleteAccountRelationship(
        user,
        id,
      );
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        &#x60;${this.logSpanId} Error encountered while deleting relationship. &gt; Error message: ${error.message}&#x60;,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: &quot;ACCOUNT_RELATIONSHIP_CONTROLLER&quot;,
        fn: &quot;deleteAccountRelationship&quot;,
        organizationId: user.orgUid,
        params: { uid: id },
        user,
      });

      throw new InternalServerErrorException(&quot;Something went wrong!&quot;);
    }
  }
}
</code></pre>
    </div>
</div>
















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'controller';
            var COMPODOC_CURRENT_PAGE_URL = 'AccountRelationshipActionController.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
