<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content controller">
                   <div class="content-data">





<ol class="breadcrumb">
  <li class="breadcrumb-item">Controllers</li>
  <li class="breadcrumb-item" >TicketsGrpcController</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/tickets/controllers/grpc/tickets-grpc.controller.ts</code>
        </p>

            <p class="comment">
                <h3>Prefix</h3>
            </p>
            <p class="comment">
                <code>v1/tickets</code>
            </p>






            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#archiveTicket" >archiveTicket</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#assignTicket" >assignTicket</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#createTicket" >createTicket</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#escalateTicket" >escalateTicket</a>
                            </li>
                            <li>
                                <a href="#formatTicketResponse" >formatTicketResponse</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getTicket" >getTicket</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getTicketData" >getTicketData</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#getTicketFieldMetadata" >getTicketFieldMetadata</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getTicketPriorityData" >getTicketPriorityData</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#getTicketPriorityFieldMetadata" >getTicketPriorityFieldMetadata</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getTicketStatusData" >getTicketStatusData</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#getTicketStatusFieldMetadata" >getTicketStatusFieldMetadata</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getTicketsWithCursor" >getTicketsWithCursor</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getTicketTypeData" >getTicketTypeData</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#getTicketTypeFieldMetadata" >getTicketTypeFieldMetadata</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#updateTicket" >updateTicket</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#validateAndFetchTeam" >validateAndFetchTeam</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="archiveTicket"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>archiveTicket</b></span>
                        <a href="#archiveTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>archiveTicket(data: tickets.ArchiveTicketRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(tickets.TICKETS_SERVICE_NAME, &#x27;ArchiveTicket&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="229"
                                    class="link-to-prism">src/tickets/controllers/grpc/tickets-grpc.controller.ts:229</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>data</td>
                                            <td>
                                                        <code>tickets.ArchiveTicketRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="assignTicket"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>assignTicket</b></span>
                        <a href="#assignTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>assignTicket(data: tickets.AssignTicketRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(tickets.TICKETS_SERVICE_NAME, &#x27;AssignTicket&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="51"
                                    class="link-to-prism">src/tickets/controllers/grpc/tickets-grpc.controller.ts:51</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>data</td>
                                            <td>
                                                        <code>tickets.AssignTicketRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createTicket"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>createTicket</b></span>
                        <a href="#createTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createTicket(data: tickets.CreateTicketRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(tickets.TICKETS_SERVICE_NAME, &#x27;CreateTicket&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="86"
                                    class="link-to-prism">src/tickets/controllers/grpc/tickets-grpc.controller.ts:86</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>data</td>
                                            <td>
                                                        <code>tickets.CreateTicketRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="escalateTicket"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>escalateTicket</b></span>
                        <a href="#escalateTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>escalateTicket(data: tickets.EscalateTicketRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(tickets.TICKETS_SERVICE_NAME, &#x27;EscalateTicket&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="254"
                                    class="link-to-prism">src/tickets/controllers/grpc/tickets-grpc.controller.ts:254</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>data</td>
                                            <td>
                                                        <code>tickets.EscalateTicketRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="formatTicketResponse"></a>
                    <span class="name">
                        <span ><b>formatTicketResponse</b></span>
                        <a href="#formatTicketResponse"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>formatTicketResponse(ticket: Ticket)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="400"
                                    class="link-to-prism">src/tickets/controllers/grpc/tickets-grpc.controller.ts:400</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>ticket</td>
                                            <td>
                                                        <code>Ticket</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/CommonTicketResponse.html" target="_self" >tickets.CommonTicketResponse</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTicket"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getTicket</b></span>
                        <a href="#getTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTicket(data: tickets.GetTicketRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(tickets.TICKETS_SERVICE_NAME, &#x27;GetTicket&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="169"
                                    class="link-to-prism">src/tickets/controllers/grpc/tickets-grpc.controller.ts:169</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>data</td>
                                            <td>
                                                        <code>tickets.GetTicketRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTicketData"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getTicketData</b></span>
                        <a href="#getTicketData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTicketData(request: tickets.GetTicketDataRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(tickets.TICKET_ANNOTATOR_SERVICE_NAME, &#x27;GetTicketData&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="333"
                                    class="link-to-prism">src/tickets/controllers/grpc/tickets-grpc.controller.ts:333</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                        <code>tickets.GetTicketDataRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;tickets.GetTicketDataResponse&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTicketFieldMetadata"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>getTicketFieldMetadata</b></span>
                        <a href="#getTicketFieldMetadata"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTicketFieldMetadata()</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(tickets.TICKET_ANNOTATOR_SERVICE_NAME, &#x27;GetTicketFieldMetadata&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="288"
                                    class="link-to-prism">src/tickets/controllers/grpc/tickets-grpc.controller.ts:288</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>    <code>tickets.GetTicketFieldMetadataResponse</code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTicketPriorityData"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getTicketPriorityData</b></span>
                        <a href="#getTicketPriorityData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTicketPriorityData(request: tickets.GetTicketPriorityDataRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(tickets.TICKET_ANNOTATOR_SERVICE_NAME, &#x27;GetTicketPriorityData&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="384"
                                    class="link-to-prism">src/tickets/controllers/grpc/tickets-grpc.controller.ts:384</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                        <code>tickets.GetTicketPriorityDataRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;tickets.GetTicketPriorityDataResponse&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTicketPriorityFieldMetadata"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>getTicketPriorityFieldMetadata</b></span>
                        <a href="#getTicketPriorityFieldMetadata"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTicketPriorityFieldMetadata()</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(tickets.TICKET_ANNOTATOR_SERVICE_NAME, &#x27;GetTicketPriorityFieldMetadata&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="324"
                                    class="link-to-prism">src/tickets/controllers/grpc/tickets-grpc.controller.ts:324</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>    <code>tickets.GetTicketPriorityFieldMetadataResponse</code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTicketStatusData"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getTicketStatusData</b></span>
                        <a href="#getTicketStatusData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTicketStatusData(request: tickets.GetTicketStatusDataRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(tickets.TICKET_ANNOTATOR_SERVICE_NAME, &#x27;GetTicketStatusData&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="350"
                                    class="link-to-prism">src/tickets/controllers/grpc/tickets-grpc.controller.ts:350</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                        <code>tickets.GetTicketStatusDataRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;tickets.GetTicketStatusDataResponse&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTicketStatusFieldMetadata"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>getTicketStatusFieldMetadata</b></span>
                        <a href="#getTicketStatusFieldMetadata"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTicketStatusFieldMetadata()</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(tickets.TICKET_ANNOTATOR_SERVICE_NAME, &#x27;GetTicketStatusFieldMetadata&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="300"
                                    class="link-to-prism">src/tickets/controllers/grpc/tickets-grpc.controller.ts:300</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>    <code>tickets.GetTicketStatusFieldMetadataResponse</code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTicketsWithCursor"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getTicketsWithCursor</b></span>
                        <a href="#getTicketsWithCursor"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTicketsWithCursor(data: tickets.GetTicketsWithCursorRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(tickets.TICKETS_SERVICE_NAME, &#x27;GetTicketsWithCursor&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="201"
                                    class="link-to-prism">src/tickets/controllers/grpc/tickets-grpc.controller.ts:201</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>data</td>
                                            <td>
                                                        <code>tickets.GetTicketsWithCursorRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTicketTypeData"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getTicketTypeData</b></span>
                        <a href="#getTicketTypeData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTicketTypeData(request: tickets.GetTicketTypeDataRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(tickets.TICKET_ANNOTATOR_SERVICE_NAME, &#x27;GetTicketTypeData&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="367"
                                    class="link-to-prism">src/tickets/controllers/grpc/tickets-grpc.controller.ts:367</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                        <code>tickets.GetTicketTypeDataRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;tickets.GetTicketTypeDataResponse&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTicketTypeFieldMetadata"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>getTicketTypeFieldMetadata</b></span>
                        <a href="#getTicketTypeFieldMetadata"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTicketTypeFieldMetadata()</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(tickets.TICKET_ANNOTATOR_SERVICE_NAME, &#x27;GetTicketTypeFieldMetadata&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="312"
                                    class="link-to-prism">src/tickets/controllers/grpc/tickets-grpc.controller.ts:312</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>    <code>tickets.GetTicketTypeFieldMetadataResponse</code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateTicket"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>updateTicket</b></span>
                        <a href="#updateTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateTicket(data: tickets.UpdateTicketRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(tickets.TICKETS_SERVICE_NAME, &#x27;UpdateTicket&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="128"
                                    class="link-to-prism">src/tickets/controllers/grpc/tickets-grpc.controller.ts:128</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>data</td>
                                            <td>
                                                        <code>tickets.UpdateTicketRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateAndFetchTeam"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>validateAndFetchTeam</b></span>
                        <a href="#validateAndFetchTeam"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateAndFetchTeam(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="37"
                                    class="link-to-prism">src/tickets/controllers/grpc/tickets-grpc.controller.ts:37</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Validates and fetches a team by its ID.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the team to fetch.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The team.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Metadata } from &quot;@grpc/grpc-js&quot;;
import {
  BadRequestException,
  Controller,
  NotFoundException,
  UseGuards,
} from &quot;@nestjs/common&quot;;
import { GrpcMethod, RpcException } from &quot;@nestjs/microservices&quot;;
import { handleRpcError } from &quot;@repo/nestjs-commons/errors&quot;;
import { GrpcAuthGuard } from &quot;@repo/nestjs-commons/guards&quot;;
import { RequestSource } from &quot;@repo/nestjs-commons/middlewares&quot;;
import { extractUserMetadata } from &quot;@repo/nestjs-commons/utils&quot;;
import { tickets } from &quot;@repo/shared-proto&quot;;
import { Ticket } from &quot;@repo/thena-platform-entities&quot;;
import { TeamsService } from &quot;../../../teams/services/teams.service&quot;;
import { CreateTicketBody, UpdateTicketBody } from &quot;../../../tickets/dto&quot;;
import { UsersService } from &quot;../../../users/services/users.service&quot;;
import { TicketAnnotatorService } from &quot;../../services/ticket-annotator.service&quot;;
import { TicketsService } from &quot;../../services/tickets.service&quot;;
import { createTicketValidator, updateTicketValidator } from &quot;./validators&quot;;

@Controller(&quot;v1/tickets&quot;)
@UseGuards(GrpcAuthGuard)
export class TicketsGrpcController {
  constructor(
    private readonly ticketsService: TicketsService,
    private readonly userService: UsersService,
    private readonly teamsService: TeamsService,
    private readonly ticketAnnotatorService: TicketAnnotatorService,
  ) {}

  /**
   * Validates and fetches a team by its ID.
   * @param teamId The ID of the team to fetch.
   * @returns The team.
   */
  private async validateAndFetchTeam(teamId: string, organizationId: string) {
    const team &#x3D; await this.teamsService.findOneByTeamId(
      teamId,
      organizationId,
    );

    if (!team) {
      throw new NotFoundException(&quot;Team not found!&quot;);
    }

    return team;
  }

  @GrpcMethod(tickets.TICKETS_SERVICE_NAME, &quot;AssignTicket&quot;)
  async assignTicket(data: tickets.AssignTicketRequest, metadata: Metadata) {
    try {
      const { ticketId, agentId } &#x3D; data || {};

      // Validate the request
      if (!ticketId || !agentId) {
        throw new RpcException(&quot;Invalid request!&quot;);
      }

      // Fetch the user
      const currentUser &#x3D; extractUserMetadata(metadata);

      // Assign the ticket
      const ticket &#x3D; await this.ticketsService.assignTicket(
        currentUser,
        ticketId,
        { assignedAgentId: agentId },
        { unassign: false },
      );

      const ticketResponse: tickets.CommonTicketResponse &#x3D;
        this.formatTicketResponse(ticket);

      const response: tickets.AssignTicketResponse &#x3D; {
        success: true,
        ticket: ticketResponse,
      };

      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(tickets.TICKETS_SERVICE_NAME, &quot;CreateTicket&quot;)
  async createTicket(data: tickets.CreateTicketRequest, metadata: Metadata) {
    try {
      const user &#x3D; extractUserMetadata(metadata);

      // Validate the request
      const parsedData &#x3D; createTicketValidator.safeParse(data);
      if (!parsedData.success) {
        throw new BadRequestException(&quot;Invalid request!&quot;);
      }

      // Fetch the team
      const team &#x3D; await this.validateAndFetchTeam(
        parsedData.data.teamId,
        user.orgId,
      );

      const payload &#x3D; {
        ...parsedData.data,
        dueDate: parsedData.data.dueDate
          ? new Date(parsedData.data.dueDate)
          : undefined,
      } as CreateTicketBody;

      // Create the ticket
      const ticket &#x3D; await this.ticketsService.createTicket(
        user,
        team,
        payload,
        RequestSource.WORKFLOW,
      );

      // Format the response
      const response &#x3D; this.formatTicketResponse(ticket);

      // Return the response
      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(tickets.TICKETS_SERVICE_NAME, &quot;UpdateTicket&quot;)
  async updateTicket(data: tickets.UpdateTicketRequest, metadata: Metadata) {
    try {
      // Extract the user
      const user &#x3D; extractUserMetadata(metadata);

      // If the ticket ID is not provided, throw an error
      if (!data.id) {
        throw new BadRequestException(&quot;Ticket ID is required&quot;);
      }

      // Validate the request
      const parsedData &#x3D; updateTicketValidator.safeParse(data);
      if (!parsedData.success) {
        throw new BadRequestException(&quot;Invalid request!&quot;);
      }

      const payload &#x3D; {
        ...parsedData.data,
        dueDate: parsedData.data.dueDate
          ? new Date(parsedData.data.dueDate)
          : undefined,
      } as UpdateTicketBody;

      // Update the ticket
      const updatedTicket &#x3D; await this.ticketsService.updateTicket(
        user,
        data.id,
        payload,
      );

      // Format the response
      const response: tickets.CommonTicketResponse &#x3D;
        this.formatTicketResponse(updatedTicket);

      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(tickets.TICKETS_SERVICE_NAME, &quot;GetTicket&quot;)
  async getTicket(data: tickets.GetTicketRequest, metadata: Metadata) {
    try {
      // Extract the user
      const user &#x3D; extractUserMetadata(metadata);

      // If the ticket ID is not provided, throw an error
      if (!data.id) {
        throw new BadRequestException(&quot;Ticket ID is required!&quot;);
      }

      // Fetch the ticket
      const ticket &#x3D; await this.ticketsService.getTicketById(
        data.id,
        user.orgId,
      );

      // If the ticket is not found, throw an error
      if (!ticket) {
        throw new NotFoundException(&quot;Ticket not found!&quot;);
      }

      // Format the response
      const response &#x3D; this.formatTicketResponse(ticket);

      // Return the response
      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(tickets.TICKETS_SERVICE_NAME, &quot;GetTicketsWithCursor&quot;)
  async getTicketsWithCursor(
    data: tickets.GetTicketsWithCursorRequest,
    metadata: Metadata,
  ) {
    try {
      // Extract the user
      const user &#x3D; extractUserMetadata(metadata);

      // Fetch the tickets
      const { data: tickets, cursor } &#x3D;
        await this.ticketsService.getTicketsWithCursor(user, {
          limit: data.limit,
          afterCursor: data.afterCursor,
        });

      // Format the response
      const response: tickets.GetTicketsWithCursorResponse &#x3D; {
        tickets: tickets.map((ticket) &#x3D;&gt; this.formatTicketResponse(ticket)),
        cursor: cursor.afterCursor,
      };

      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(tickets.TICKETS_SERVICE_NAME, &quot;ArchiveTicket&quot;)
  async archiveTicket(data: tickets.ArchiveTicketRequest, metadata: Metadata) {
    try {
      const user &#x3D; extractUserMetadata(metadata);

      // If the ticket ID is not provided, throw an error
      if (!data.id) {
        throw new BadRequestException(&quot;Ticket ID is required!&quot;);
      }

      // Archive the ticket
      const archivedTicket &#x3D; await this.ticketsService.archiveTicket(
        user,
        data.id,
      );

      // Format the response
      const response &#x3D; this.formatTicketResponse(archivedTicket);

      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(tickets.TICKETS_SERVICE_NAME, &quot;EscalateTicket&quot;)
  async escalateTicket(
    data: tickets.EscalateTicketRequest,
    metadata: Metadata,
  ) {
    try {
      const user &#x3D; extractUserMetadata(metadata);

      // If the ticket ID is not provided, throw an error
      if (!data.id) {
        throw new BadRequestException(&quot;Ticket ID is required!&quot;);
      }

      // Escalate the ticket
      const escalatedTicket &#x3D; await this.ticketsService.escalateTicket(
        user,
        data.id,
        {
          reason: data.reason,
          details: data.details,
          impact: data.impact,
        },
      );

      // Format the response
      const response &#x3D; this.formatTicketResponse(escalatedTicket);

      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  // Field metadata apis &#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;
  @GrpcMethod(tickets.TICKET_ANNOTATOR_SERVICE_NAME, &quot;GetTicketFieldMetadata&quot;)
  getTicketFieldMetadata(): tickets.GetTicketFieldMetadataResponse {
    try {
      return this.ticketAnnotatorService.getTicketFieldMetadata();
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    tickets.TICKET_ANNOTATOR_SERVICE_NAME,
    &quot;GetTicketStatusFieldMetadata&quot;,
  )
  getTicketStatusFieldMetadata(): tickets.GetTicketStatusFieldMetadataResponse {
    try {
      return this.ticketAnnotatorService.getTicketStatusFieldMetadata();
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    tickets.TICKET_ANNOTATOR_SERVICE_NAME,
    &quot;GetTicketTypeFieldMetadata&quot;,
  )
  getTicketTypeFieldMetadata(): tickets.GetTicketTypeFieldMetadataResponse {
    try {
      return this.ticketAnnotatorService.getTicketTypeFieldMetadata();
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    tickets.TICKET_ANNOTATOR_SERVICE_NAME,
    &quot;GetTicketPriorityFieldMetadata&quot;,
  )
  getTicketPriorityFieldMetadata(): tickets.GetTicketPriorityFieldMetadataResponse {
    try {
      return this.ticketAnnotatorService.getTicketPriorityFieldMetadata();
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(tickets.TICKET_ANNOTATOR_SERVICE_NAME, &quot;GetTicketData&quot;)
  async getTicketData(
    request: tickets.GetTicketDataRequest,
    metadata: Metadata,
  ): Promise&lt;tickets.GetTicketDataResponse&gt; {
    try {
      const { orgId } &#x3D; extractUserMetadata(metadata);
      return await this.ticketAnnotatorService.getTicketData(
        request.ticketId,
        request.relations,
        orgId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(tickets.TICKET_ANNOTATOR_SERVICE_NAME, &quot;GetTicketStatusData&quot;)
  async getTicketStatusData(
    request: tickets.GetTicketStatusDataRequest,
    metadata: Metadata,
  ): Promise&lt;tickets.GetTicketStatusDataResponse&gt; {
    try {
      const { orgId } &#x3D; extractUserMetadata(metadata);
      return await this.ticketAnnotatorService.getTicketStatusData(
        request.statusId,
        request.relations,
        orgId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(tickets.TICKET_ANNOTATOR_SERVICE_NAME, &quot;GetTicketTypeData&quot;)
  async getTicketTypeData(
    request: tickets.GetTicketTypeDataRequest,
    metadata: Metadata,
  ): Promise&lt;tickets.GetTicketTypeDataResponse&gt; {
    try {
      const { orgId } &#x3D; extractUserMetadata(metadata);
      return await this.ticketAnnotatorService.getTicketTypeData(
        request.typeId,
        request.relations,
        orgId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(tickets.TICKET_ANNOTATOR_SERVICE_NAME, &quot;GetTicketPriorityData&quot;)
  async getTicketPriorityData(
    request: tickets.GetTicketPriorityDataRequest,
    metadata: Metadata,
  ): Promise&lt;tickets.GetTicketPriorityDataResponse&gt; {
    try {
      const { orgId } &#x3D; extractUserMetadata(metadata);
      return await this.ticketAnnotatorService.getTicketPriorityData(
        request.priorityId,
        request.relations,
        orgId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  formatTicketResponse(ticket: Ticket) {
    const response: tickets.CommonTicketResponse &#x3D; {
      id: ticket.uid,
      title: ticket.title,
      ticketId: ticket.ticketId,
      teamId: ticket.team.uid,
      isPrivate: ticket.isPrivate,
      requestorEmail: ticket.requestorEmail,
      description: ticket.description,
      accountId: ticket.account?.uid,
      status: ticket.status.name,
      priority: ticket.priority.name,
      teamName: ticket.team.name,
      type: ticket.type?.name,
      typeId: ticket.type?.uid,
      assignedAgent: ticket.assignedAgent?.name,
      assignedAgentId: ticket.assignedAgent?.uid,
      submitterEmail: ticket.submitterEmail,
      deletedAt: ticket.deletedAt
        ? new Date(ticket.deletedAt).toISOString()
        : undefined,
      archivedAt: ticket.archivedAt
        ? new Date(ticket.archivedAt).toISOString()
        : undefined,
      createdAt: ticket.createdAt
        ? new Date(ticket.createdAt).toISOString()
        : undefined,
      updatedAt: ticket.updatedAt
        ? new Date(ticket.updatedAt).toISOString()
        : undefined,
    };

    return response;
  }
}
</code></pre>
    </div>
</div>
















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'controller';
            var COMPODOC_CURRENT_PAGE_URL = 'TicketsGrpcController.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
