<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content controller">
                   <div class="content-data">





<ol class="breadcrumb">
  <li class="breadcrumb-item">Controllers</li>
  <li class="breadcrumb-item" >OrganizationController</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/organization/controllers/organization.controller.ts</code>
        </p>

            <p class="comment">
                <h3>Prefix</h3>
            </p>
            <p class="comment">
                <code>v1/organizations</code>
            </p>






            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#create" >create</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#findAll" >findAll</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#findOne" >findOne</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#invite" >invite</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#join" >join</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#remove" >remove</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#update" >update</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="create"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>create</b></span>
                        <a href="#create"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>create(createOrganizationDto: <a href="../classes/CreateOrganizationDto.html" target="_self">CreateOrganizationDto</a>, request: <a href="../interfaces/FastifyRequest.html" target="_self">FastifyRequest</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Public()<br />@Post()<br />@ApiCreateEndpoint({summary: &#x27;Create an organization&#x27;, responseType: OrganizationResponseDto, operationId: &#x27;createOrganization&#x27;})<br />@Throttle({undefined: undefined})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="57"
                                    class="link-to-prism">src/organization/controllers/organization.controller.ts:57</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>createOrganizationDto</td>
                                            <td>
                                                            <code><a href="../classes/CreateOrganizationDto.html" target="_self" >CreateOrganizationDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../interfaces/FastifyRequest.html" target="_self" >FastifyRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/OrganizationResponseDto.html" target="_self" >Promise&lt;OrganizationResponseDto&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAll"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>findAll</b></span>
                        <a href="#findAll"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAll(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get()<br />@ApiGetEndpoint({summary: &#x27;Find all organizations&#x27;, responseType: OrganizationResponseDto})<br />@Throttle({undefined: undefined})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="143"
                                    class="link-to-prism">src/organization/controllers/organization.controller.ts:143</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/OrganizationResponseDto.html" target="_self" >Promise&lt;OrganizationResponseDto[]&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findOne"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>findOne</b></span>
                        <a href="#findOne"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findOne(id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get(&#x27;/:id&#x27;)<br />@ApiGetEndpoint({summary: &#x27;Find an organization by identifier&#x27;, responseType: OrganizationResponseDto})<br />@Throttle({undefined: undefined})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="156"
                                    class="link-to-prism">src/organization/controllers/organization.controller.ts:156</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/OrganizationResponseDto.html" target="_self" >Promise&lt;OrganizationResponseDto&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="invite"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>invite</b></span>
                        <a href="#invite"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>invite(inviteDto: <a href="../classes/InviteUserDto.html" target="_self">InviteUserDto</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Post(&#x27;/invite&#x27;)<br />@ApiCreateEndpoint({summary: &#x27;Create an invitation for a user to join an organization&#x27;})<br />@ThrottleTierFour()<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="125"
                                    class="link-to-prism">src/organization/controllers/organization.controller.ts:125</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>inviteDto</td>
                                            <td>
                                                            <code><a href="../classes/InviteUserDto.html" target="_self" >InviteUserDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="join"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>join</b></span>
                        <a href="#join"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>join(joinOrgDto: <a href="../classes/JoinOrganizationDto.html" target="_self">JoinOrganizationDto</a>, request: <a href="../interfaces/FastifyRequest.html" target="_self">FastifyRequest</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Public()<br />@Post(&#x27;/join&#x27;)<br />@ApiCreateEndpoint({summary: &#x27;Join an organization&#x27;})<br />@ApiBody({type: JoinOrganizationDto})<br />@ThrottleTierFour()<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="91"
                                    class="link-to-prism">src/organization/controllers/organization.controller.ts:91</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>joinOrgDto</td>
                                            <td>
                                                            <code><a href="../classes/JoinOrganizationDto.html" target="_self" >JoinOrganizationDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../interfaces/FastifyRequest.html" target="_self" >FastifyRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="remove"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>remove</b></span>
                        <a href="#remove"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>remove(id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Delete(&#x27;:id&#x27;)<br />@ApiDeleteEndpoint({summary: &#x27;Delete an organization&#x27;})<br />@Throttle({undefined: undefined})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="198"
                                    class="link-to-prism">src/organization/controllers/organization.controller.ts:198</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;void&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="update"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>update</b></span>
                        <a href="#update"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>update(id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, updateOrganizationDto: <a href="../classes/UpdateOrganizationDto.html" target="_self">UpdateOrganizationDto</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Patch(&#x27;:id&#x27;)<br />@ApiUpdateEndpoint({summary: &#x27;Update an organization&#x27;, responseType: OrganizationResponseDto})<br />@Throttle({undefined: undefined})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="179"
                                    class="link-to-prism">src/organization/controllers/organization.controller.ts:179</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>updateOrganizationDto</td>
                                            <td>
                                                            <code><a href="../classes/UpdateOrganizationDto.html" target="_self" >UpdateOrganizationDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/OrganizationResponseDto.html" target="_self" >Promise&lt;OrganizationResponseDto&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  InternalServerErrorException,
  NotFoundException,
  Param,
  Patch,
  Post,
  Req,
  UseInterceptors,
} from &quot;@nestjs/common&quot;;
import { ApiBody, ApiTags } from &quot;@nestjs/swagger&quot;;
import { Throttle } from &quot;@nestjs/throttler&quot;;
import {
  ApiCreateEndpoint,
  ApiDeleteEndpoint,
  ApiGetEndpoint,
  ApiUpdateEndpoint,
  ResponseTransformInterceptor,
} from &quot;@repo/nestjs-commons/decorators&quot;;
import { GENERIC_ERROR_MESSAGES } from &quot;@repo/thena-shared-libs&quot;;
import { FastifyRequest } from &quot;fastify&quot;;
import { Public } from &quot;../../auth/decorators/auth.decorator&quot;;
import { CurrentUser } from &quot;../../common/decorators&quot;;
import { ThrottleTierFour } from &quot;../../common/decorators/throttler.decorator&quot;;
import {
  THROTTLER_TIER,
  THROTTLER_TIER_2,
  THROTTLER_TIER_3,
} from &quot;../../config/throttler.config&quot;;
import {
  CreateOrganizationDto,
  InviteUserDto,
  JoinOrganizationDto,
  UpdateOrganizationDto,
} from &quot;../dto/organization.dto&quot;;
import { OrganizationService } from &quot;../services/organization.service&quot;;
import { OrganizationResponseDto } from &quot;../transformers&quot;;

@ApiTags(&quot;Organizations&quot;)
@Controller(&quot;v1/organizations&quot;)
@UseInterceptors(ResponseTransformInterceptor)
export class OrganizationController {
  constructor(private readonly organizationService: OrganizationService) {}

  @Public()
  @Post()
  @ApiCreateEndpoint({
    summary: &quot;Create an organization&quot;,
    responseType: OrganizationResponseDto,
    operationId: &quot;createOrganization&quot;,
  })
  @Throttle({ [THROTTLER_TIER_2]: THROTTLER_TIER.TIER_2 }) // This can even go lower since organizations cannot be created by an IP too often
  async create(
    @Body() createOrganizationDto: CreateOrganizationDto,
    @Req() request: FastifyRequest,
  ): Promise&lt;OrganizationResponseDto&gt; {
    try {
      // Extract the auth token from the request headers
      const authToken &#x3D; request.headers.authorization;

      // Create the organization
      const org &#x3D; await this.organizationService.create(
        createOrganizationDto,
        authToken,
      );

      // Return the organization
      return OrganizationResponseDto.fromEntity(org);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      throw new InternalServerErrorException(
        GENERIC_ERROR_MESSAGES.SOMETHING_WENT_WRONG,
      );
    }
  }

  @Public()
  @Post(&quot;/join&quot;)
  @ApiCreateEndpoint({
    summary: &quot;Join an organization&quot;,
  })
  @ApiBody({ type: JoinOrganizationDto })
  @ThrottleTierFour()
  async join(
    @Body() joinOrgDto: JoinOrganizationDto,
    @Req() request: FastifyRequest,
  ) {
    try {
      // Extract the auth token from the request headers
      const authToken &#x3D; request.headers.authorization;

      // Join the organization
      const { userPersona, organization } &#x3D;
        await this.organizationService.joinOrganization(joinOrgDto, authToken);

      // Return the user persona
      return {
        userId: userPersona.uid,
        organizationId: organization.id,
        organizationUid: organization.uid,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      throw new InternalServerErrorException(
        GENERIC_ERROR_MESSAGES.SOMETHING_WENT_WRONG,
      );
    }
  }

  @Post(&quot;/invite&quot;)
  @ApiCreateEndpoint({
    summary: &quot;Create an invitation for a user to join an organization&quot;,
  })
  @ThrottleTierFour()
  async invite(
    @Body() inviteDto: InviteUserDto,
    @CurrentUser() user: CurrentUser,
  ) {
    const newInvite &#x3D; await this.organizationService.createInvite(
      inviteDto,
      user,
    );

    return newInvite;
  }

  @Get()
  @ApiGetEndpoint({
    summary: &quot;Find all organizations&quot;,
    responseType: OrganizationResponseDto,
  })
  @Throttle({ [THROTTLER_TIER_3]: THROTTLER_TIER.TIER_3 })
  async findAll(
    @CurrentUser() user: CurrentUser,
  ): Promise&lt;OrganizationResponseDto[]&gt; {
    const orgs &#x3D; await this.organizationService.findAll(user);
    return orgs.map(OrganizationResponseDto.fromEntity);
  }

  @Get(&quot;/:id&quot;)
  @ApiGetEndpoint({
    summary: &quot;Find an organization by identifier&quot;,
    responseType: OrganizationResponseDto,
  })
  @Throttle({ [THROTTLER_TIER_3]: THROTTLER_TIER.TIER_3 })
  async findOne(
    @Param(&quot;id&quot;) id: string,
    @CurrentUser() user: CurrentUser,
  ): Promise&lt;OrganizationResponseDto&gt; {
    const org &#x3D; await this.organizationService.findOneByIdentifier(user.orgUid);
    if (!org) {
      throw new NotFoundException(&quot;Organization not found&quot;);
    }

    // Check if the user is authorized to access this organization
    if (id !&#x3D;&#x3D; org.uid) {
      throw new NotFoundException(&quot;Organization not found&quot;);
    }

    return OrganizationResponseDto.fromEntity(org);
  }

  @Patch(&quot;:id&quot;)
  @ApiUpdateEndpoint({
    summary: &quot;Update an organization&quot;,
    responseType: OrganizationResponseDto,
  })
  @Throttle({ [THROTTLER_TIER_2]: THROTTLER_TIER.TIER_2 })
  async update(
    @Param(&quot;id&quot;) id: string,
    @Body() updateOrganizationDto: UpdateOrganizationDto,
    @CurrentUser() user: CurrentUser,
  ): Promise&lt;OrganizationResponseDto&gt; {
    const org &#x3D; await this.organizationService.update(
      id,
      updateOrganizationDto,
      user,
    );

    return OrganizationResponseDto.fromEntity(org);
  }

  @Delete(&quot;:id&quot;)
  @ApiDeleteEndpoint({
    summary: &quot;Delete an organization&quot;,
  })
  @Throttle({ [THROTTLER_TIER_2]: THROTTLER_TIER.TIER_2 }) // Same with delete
  async remove(
    @Param(&quot;id&quot;) id: string,
    @CurrentUser() user: CurrentUser,
  ): Promise&lt;void&gt; {
    await this.organizationService.remove(id, user);
  }
}
</code></pre>
    </div>
</div>
















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'controller';
            var COMPODOC_CURRENT_PAGE_URL = 'OrganizationController.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
