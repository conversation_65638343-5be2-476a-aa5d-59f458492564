<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content controller">
                   <div class="content-data">





<ol class="breadcrumb">
  <li class="breadcrumb-item">Controllers</li>
  <li class="breadcrumb-item" >AccountTaskActionController</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/accounts/controllers/account-task.action.controller.ts</code>
        </p>

            <p class="comment">
                <h3>Prefix</h3>
            </p>
            <p class="comment">
                <code>v1/accounts/tasks</code>
            </p>






            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#createAccountTask" >createAccountTask</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#deleteAccountTask" >deleteAccountTask</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getAccountTasks" >getAccountTasks</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#removeTaskAttachment" >removeTaskAttachment</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#updateAccountTask" >updateAccountTask</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createAccountTask"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>createAccountTask</b></span>
                        <a href="#createAccountTask"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createAccountTask(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, createAccountTaskDto: <a href="../classes/CreateAccountTaskDto.html" target="_self">CreateAccountTaskDto</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Post()<br />@ApiResponseMessage(&#x27;Account task created successfully!&#x27;)<br />@ApiCreateEndpoint({summary: &#x27;Creates an account task&#x27;, responseType: AccountTaskResponseDto})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="93"
                                    class="link-to-prism">src/accounts/controllers/account-task.action.controller.ts:93</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>createAccountTaskDto</td>
                                            <td>
                                                            <code><a href="../classes/CreateAccountTaskDto.html" target="_self" >CreateAccountTaskDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/AccountTaskResponseDto.html" target="_self" >Promise&lt;AccountTaskResponseDto&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteAccountTask"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>deleteAccountTask</b></span>
                        <a href="#deleteAccountTask"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteAccountTask(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, taskId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Delete(&#x27;:taskId&#x27;)<br />@ApiResponseMessage(&#x27;Account task deleted successfully!&#x27;)<br />@ApiDeleteEndpoint({summary: &#x27;Deletes an account task&#x27;})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="177"
                                    class="link-to-prism">src/accounts/controllers/account-task.action.controller.ts:177</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>taskId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;void&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getAccountTasks"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getAccountTasks</b></span>
                        <a href="#getAccountTasks"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getAccountTasks(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, findAccountTaskDto: <a href="../classes/FindAccountTaskDto.html" target="_self">FindAccountTaskDto</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get()<br />@ApiResponseMessage(&#x27;Account tasks fetched successfully!&#x27;)<br />@ApiGetEndpoint({summary: &#x27;Fetches all account tasks by account ID or by task ID&#x27;, responseType: AccountTaskResponseDto})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="54"
                                    class="link-to-prism">src/accounts/controllers/account-task.action.controller.ts:54</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>findAccountTaskDto</td>
                                            <td>
                                                            <code><a href="../classes/FindAccountTaskDto.html" target="_self" >FindAccountTaskDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/AccountTaskResponseDto.html" target="_self" >Promise&lt;AccountTaskResponseDto[]&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="removeTaskAttachment"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>removeTaskAttachment</b></span>
                        <a href="#removeTaskAttachment"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>removeTaskAttachment(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, taskId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, attachmentId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Delete(&#x27;:taskId/attachments/:attachmentId&#x27;)<br />@ApiResponseMessage(&#x27;Attachment removed successfully!&#x27;)<br />@ApiDeleteEndpoint({summary: &#x27;Removes an attachment from an account task&#x27;})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="214"
                                    class="link-to-prism">src/accounts/controllers/account-task.action.controller.ts:214</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>taskId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>attachmentId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;void&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateAccountTask"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>updateAccountTask</b></span>
                        <a href="#updateAccountTask"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateAccountTask(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, taskId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, updateAccountTaskDto: <a href="../classes/UpdateAccountTaskDto.html" target="_self">UpdateAccountTaskDto</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Put(&#x27;:taskId&#x27;)<br />@ApiResponseMessage(&#x27;Account task updated successfully!&#x27;)<br />@ApiUpdateEndpoint({summary: &#x27;Updates an account task&#x27;, responseType: AccountTaskResponseDto})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="132"
                                    class="link-to-prism">src/accounts/controllers/account-task.action.controller.ts:132</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>taskId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>updateAccountTaskDto</td>
                                            <td>
                                                            <code><a href="../classes/UpdateAccountTaskDto.html" target="_self" >UpdateAccountTaskDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/AccountTaskResponseDto.html" target="_self" >Promise&lt;AccountTaskResponseDto&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  Inject,
  InternalServerErrorException,
  Param,
  Post,
  Put,
  Query,
  UseInterceptors,
} from &quot;@nestjs/common&quot;;
import { ApiTags } from &quot;@nestjs/swagger&quot;;
import {
  ApiCreateEndpoint,
  ApiDeleteEndpoint,
  ApiGetEndpoint,
  ApiResponseMessage,
  ApiUpdateEndpoint,
  ResponseTransformInterceptor,
} from &quot;@repo/nestjs-commons/decorators&quot;;
import { SentryService } from &quot;@repo/nestjs-commons/filters&quot;;
import { ILogger } from &quot;@repo/nestjs-commons/logger&quot;;
import { CurrentUser } from &quot;../../common/decorators/user.decorator&quot;;
import {
  CreateAccountTaskDto,
  FindAccountTaskDto,
  UpdateAccountTaskDto,
} from &quot;../dtos/account-task.dto&quot;;
import { AccountTaskResponseDto } from &quot;../dtos/response/account-task.dto&quot;;
import { AccountTaskActionService } from &quot;../services/account-task.action.service&quot;;

@ApiTags(&quot;Accounts&quot;)
@Controller(&quot;v1/accounts/tasks&quot;)
@UseInterceptors(ResponseTransformInterceptor)
export class AccountTaskActionController {
  private readonly logSpanId &#x3D; &quot;[AccountTaskActionController]&quot;;

  constructor(
    private readonly accountTaskActionService: AccountTaskActionService,
    @Inject(&quot;Sentry&quot;) private readonly sentryService: SentryService,
    @Inject(&quot;CustomLogger&quot;) private readonly logger: ILogger,
  ) {}

  @Get()
  @ApiResponseMessage(&quot;Account tasks fetched successfully!&quot;)
  @ApiGetEndpoint({
    summary: &quot;Fetches all account tasks by account ID or by task ID&quot;,
    responseType: AccountTaskResponseDto,
  })
  async getAccountTasks(
    @CurrentUser() user: CurrentUser,
    @Query() findAccountTaskDto: FindAccountTaskDto,
  ): Promise&lt;AccountTaskResponseDto[]&gt; {
    try {
      const tasks &#x3D; await this.accountTaskActionService.findAllAccountTasks(
        user,
        findAccountTaskDto,
      );

      return tasks.map(AccountTaskResponseDto.fromEntity);
    } catch (error) {
      this.logger.error(
        &#x60;${this.logSpanId} Error encountered while fetching account tasks. &gt; Error message: ${error.message}&#x60;,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: &quot;ACCOUNTS_TASK_CONTROLLER&quot;,
        fn: &quot;getAccountTasks&quot;,
        organizationId: user.orgUid,
        query: findAccountTaskDto,
        user,
      });

      throw new InternalServerErrorException(&quot;Something went wrong!&quot;);
    }
  }

  @Post()
  @ApiResponseMessage(&quot;Account task created successfully!&quot;)
  @ApiCreateEndpoint({
    summary: &quot;Creates an account task&quot;,
    responseType: AccountTaskResponseDto,
  })
  async createAccountTask(
    @CurrentUser() user: CurrentUser,
    @Body() createAccountTaskDto: CreateAccountTaskDto,
  ): Promise&lt;AccountTaskResponseDto&gt; {
    try {
      const createdTask &#x3D; await this.accountTaskActionService.createAccountTask(
        user,
        createAccountTaskDto,
      );

      return AccountTaskResponseDto.fromEntity(createdTask);
    } catch (error) {
      this.logger.error(
        &#x60;${this.logSpanId} Error encountered while creating account task. &gt; Error message: ${error.message}&#x60;,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: &quot;ACCOUNTS_TASK_CONTROLLER&quot;,
        fn: &quot;createAccountTask&quot;,
        organizationId: user.orgUid,
        body: createAccountTaskDto,
        user,
      });

      throw new InternalServerErrorException(&quot;Something went wrong!&quot;);
    }
  }

  @Put(&quot;:taskId&quot;)
  @ApiResponseMessage(&quot;Account task updated successfully!&quot;)
  @ApiUpdateEndpoint({
    summary: &quot;Updates an account task&quot;,
    responseType: AccountTaskResponseDto,
  })
  async updateAccountTask(
    @CurrentUser() user: CurrentUser,
    @Param(&quot;taskId&quot;) taskId: string,
    @Body() updateAccountTaskDto: UpdateAccountTaskDto,
  ): Promise&lt;AccountTaskResponseDto&gt; {
    if (!taskId) {
      throw new BadRequestException(&quot;Task ID is required!&quot;);
    }

    try {
      const updatedTask &#x3D; await this.accountTaskActionService.updateAccountTask(
        user,
        taskId,
        updateAccountTaskDto,
      );

      return AccountTaskResponseDto.fromEntity(updatedTask);
    } catch (error) {
      this.logger.error(
        &#x60;${this.logSpanId} Error encountered while updating account task. &gt; Error message: ${error.message}&#x60;,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: &quot;ACCOUNTS_TASK_CONTROLLER&quot;,
        fn: &quot;updateAccountTask&quot;,
        organizationId: user.orgUid,
        params: { taskId },
        body: updateAccountTaskDto,
        user,
      });

      throw new InternalServerErrorException(&quot;Something went wrong!&quot;);
    }
  }

  @Delete(&quot;:taskId&quot;)
  @ApiResponseMessage(&quot;Account task deleted successfully!&quot;)
  @ApiDeleteEndpoint({
    summary: &quot;Deletes an account task&quot;,
  })
  async deleteAccountTask(
    @CurrentUser() user: CurrentUser,
    @Param(&quot;taskId&quot;) taskId: string,
  ): Promise&lt;void&gt; {
    if (!taskId) {
      throw new BadRequestException(&quot;Task ID is required!&quot;);
    }

    try {
      await this.accountTaskActionService.deleteAccountTask(user, taskId);
    } catch (error) {
      this.logger.error(
        &#x60;${this.logSpanId} Error encountered while deleting account task. &gt; Error message: ${error.message}&#x60;,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: &quot;ACCOUNTS_TASK_CONTROLLER&quot;,
        fn: &quot;deleteAccountTask&quot;,
        organizationId: user.orgUid,
        params: { taskId },
        user,
      });

      throw new InternalServerErrorException(&quot;Something went wrong!&quot;);
    }
  }

  @Delete(&quot;:taskId/attachments/:attachmentId&quot;)
  @ApiResponseMessage(&quot;Attachment removed successfully!&quot;)
  @ApiDeleteEndpoint({
    summary: &quot;Removes an attachment from an account task&quot;,
  })
  async removeTaskAttachment(
    @CurrentUser() user: CurrentUser,
    @Param(&quot;taskId&quot;) taskId: string,
    @Param(&quot;attachmentId&quot;) attachmentId: string,
  ): Promise&lt;void&gt; {
    if (!taskId) {
      throw new BadRequestException(&quot;Task ID is required!&quot;);
    }

    if (!attachmentId) {
      throw new BadRequestException(&quot;Attachment ID is required!&quot;);
    }

    try {
      await this.accountTaskActionService.removeTaskAttachment(
        user,
        taskId,
        attachmentId,
      );
    } catch (error) {
      this.logger.error(
        &#x60;${this.logSpanId} Error encountered while removing task attachment. &gt; Error message: ${error.message}&#x60;,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: &quot;ACCOUNTS_TASK_CONTROLLER&quot;,
        fn: &quot;removeTaskAttachment&quot;,
        organizationId: user.orgUid,
        params: { taskId, attachmentId },
        user,
      });

      throw new InternalServerErrorException(&quot;Something went wrong!&quot;);
    }
  }
}
</code></pre>
    </div>
</div>
















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'controller';
            var COMPODOC_CURRENT_PAGE_URL = 'AccountTaskActionController.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
