<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content controller">
                   <div class="content-data">





<ol class="breadcrumb">
  <li class="breadcrumb-item">Controllers</li>
  <li class="breadcrumb-item" >CustomerContactsGrpcController</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/accounts/controllers/grpc/customer-contacts.grpc.controller.ts</code>
        </p>







            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#bulkCreateCustomerContacts" >bulkCreateCustomerContacts</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#createCustomerContact" >createCustomerContact</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#deleteCustomerContact" >deleteCustomerContact</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getCustomerContacts" >getCustomerContacts</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#updateCustomerContact" >updateCustomerContact</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="bulkCreateCustomerContacts"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>bulkCreateCustomerContacts</b></span>
                        <a href="#bulkCreateCustomerContacts"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>bulkCreateCustomerContacts(request: <a href="../interfaces/Customer.html" target="_self">accounts.BulkCreateCustomerContactsRequest</a>, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(accounts.CUSTOMER_CONTACTS_SERVICE_NAME, &#x27;BulkCreateCustomerContacts&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="137"
                                    class="link-to-prism">src/accounts/controllers/grpc/customer-contacts.grpc.controller.ts:137</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../interfaces/Customer.html" target="_self" >accounts.BulkCreateCustomerContactsRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../interfaces/Customer.html" target="_self" >Promise&lt;accounts.BulkCreateCustomerContactsResponse&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createCustomerContact"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>createCustomerContact</b></span>
                        <a href="#createCustomerContact"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createCustomerContact(request: <a href="../interfaces/Customer.html" target="_self">accounts.CreateCustomerContactRequest</a>, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(accounts.CUSTOMER_CONTACTS_SERVICE_NAME, &#x27;CreateCustomerContact&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="57"
                                    class="link-to-prism">src/accounts/controllers/grpc/customer-contacts.grpc.controller.ts:57</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../interfaces/Customer.html" target="_self" >accounts.CreateCustomerContactRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../interfaces/Customer.html" target="_self" >Promise&lt;accounts.CustomerContact&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteCustomerContact"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>deleteCustomerContact</b></span>
                        <a href="#deleteCustomerContact"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteCustomerContact(request: <a href="../interfaces/Customer.html" target="_self">accounts.DeleteCustomerContactRequest</a>, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(accounts.CUSTOMER_CONTACTS_SERVICE_NAME, &#x27;DeleteCustomerContact&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="118"
                                    class="link-to-prism">src/accounts/controllers/grpc/customer-contacts.grpc.controller.ts:118</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../interfaces/Customer.html" target="_self" >accounts.DeleteCustomerContactRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;void&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getCustomerContacts"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getCustomerContacts</b></span>
                        <a href="#getCustomerContacts"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getCustomerContacts(request: <a href="../interfaces/Customer.html" target="_self">accounts.GetCustomerContactsRequest</a>, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(accounts.CUSTOMER_CONTACTS_SERVICE_NAME, &#x27;GetCustomerContacts&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="25"
                                    class="link-to-prism">src/accounts/controllers/grpc/customer-contacts.grpc.controller.ts:25</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../interfaces/Customer.html" target="_self" >accounts.GetCustomerContactsRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../interfaces/Customer.html" target="_self" >Promise&lt;accounts.GetCustomerContactsResponse&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateCustomerContact"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>updateCustomerContact</b></span>
                        <a href="#updateCustomerContact"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateCustomerContact(request: <a href="../interfaces/Customer.html" target="_self">accounts.UpdateCustomerContactRequest</a>, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(accounts.CUSTOMER_CONTACTS_SERVICE_NAME, &#x27;UpdateCustomerContact&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="87"
                                    class="link-to-prism">src/accounts/controllers/grpc/customer-contacts.grpc.controller.ts:87</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../interfaces/Customer.html" target="_self" >accounts.UpdateCustomerContactRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../interfaces/Customer.html" target="_self" >Promise&lt;accounts.CustomerContact&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Metadata } from &quot;@grpc/grpc-js&quot;;
import { Controller, UseGuards } from &quot;@nestjs/common&quot;;
import { GrpcMethod } from &quot;@nestjs/microservices&quot;;
import { handleRpcError } from &quot;@repo/nestjs-commons/errors&quot;;
import { GrpcAuthGuard } from &quot;@repo/nestjs-commons/guards&quot;;
import { extractUserMetadata } from &quot;@repo/nestjs-commons/utils&quot;;
import { accounts } from &quot;@repo/shared-proto&quot;;
import {
  BulkCreateCustomerContactsDto,
  CreateCustomerContactDto,
  FindAllCustomerContactsDto,
  UpdateCustomerContactDto,
} from &quot;../../dtos/customer-contact.dto&quot;;
import { CustomerContactResponseDto } from &quot;../../dtos/response/customer-contact.dto&quot;;
import { CustomerContactActionService } from &quot;../../services/customer-contact.action.service&quot;;

@Controller()
@UseGuards(GrpcAuthGuard)
export class CustomerContactsGrpcController {
  constructor(
    private readonly customerContactActionService: CustomerContactActionService,
  ) {}

  @GrpcMethod(accounts.CUSTOMER_CONTACTS_SERVICE_NAME, &quot;GetCustomerContacts&quot;)
  async getCustomerContacts(
    request: accounts.GetCustomerContactsRequest,
    metadata: Metadata,
  ): Promise&lt;accounts.GetCustomerContactsResponse&gt; {
    try {
      const user &#x3D; extractUserMetadata(metadata);

      const query: FindAllCustomerContactsDto &#x3D; {
        accountId: request.accountId,
        contactType: request.contactType,
        page: request.page,
        limit: request.limit,
      };

      const contacts &#x3D;
        await this.customerContactActionService.findCustomerContacts(
          user,
          query,
        );
      const contactDtos &#x3D; contacts.map((contact) &#x3D;&gt;
        CustomerContactResponseDto.fromEntity(contact),
      ) as accounts.CustomerContact[];

      return {
        data: contactDtos,
      };
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(accounts.CUSTOMER_CONTACTS_SERVICE_NAME, &quot;CreateCustomerContact&quot;)
  async createCustomerContact(
    request: accounts.CreateCustomerContactRequest,
    metadata: Metadata,
  ): Promise&lt;accounts.CustomerContact&gt; {
    try {
      const user &#x3D; extractUserMetadata(metadata);

      const createDto: CreateCustomerContactDto &#x3D; {
        firstName: request.firstName,
        lastName: request.lastName,
        email: request.email,
        phoneNumber: request.phoneNumber,
        contactType: request.contactType,
        accountIds: request.accountIds,
      };

      const contact &#x3D;
        await this.customerContactActionService.createCustomerContact(
          user,
          createDto,
        );
      return CustomerContactResponseDto.fromEntity(
        contact,
      ) as accounts.CustomerContact;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(accounts.CUSTOMER_CONTACTS_SERVICE_NAME, &quot;UpdateCustomerContact&quot;)
  async updateCustomerContact(
    request: accounts.UpdateCustomerContactRequest,
    metadata: Metadata,
  ): Promise&lt;accounts.CustomerContact&gt; {
    try {
      const user &#x3D; extractUserMetadata(metadata);

      const updateDto: UpdateCustomerContactDto &#x3D; {
        firstName: request.firstName,
        lastName: request.lastName,
        email: request.email,
        phoneNumber: request.phoneNumber,
        contactType: request.contactType,
        accountIds: request.accountIds,
      };

      const contact &#x3D;
        await this.customerContactActionService.updateCustomerContact(
          user,
          request.contactId,
          updateDto,
        );
      return CustomerContactResponseDto.fromEntity(
        contact,
      ) as accounts.CustomerContact;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(accounts.CUSTOMER_CONTACTS_SERVICE_NAME, &quot;DeleteCustomerContact&quot;)
  async deleteCustomerContact(
    request: accounts.DeleteCustomerContactRequest,
    metadata: Metadata,
  ): Promise&lt;void&gt; {
    try {
      const user &#x3D; extractUserMetadata(metadata);
      await this.customerContactActionService.deleteCustomerContact(
        user,
        request.contactId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    accounts.CUSTOMER_CONTACTS_SERVICE_NAME,
    &quot;BulkCreateCustomerContacts&quot;,
  )
  async bulkCreateCustomerContacts(
    request: accounts.BulkCreateCustomerContactsRequest,
    metadata: Metadata,
  ): Promise&lt;accounts.BulkCreateCustomerContactsResponse&gt; {
    try {
      const user &#x3D; extractUserMetadata(metadata);

      const bulkCreateDto: BulkCreateCustomerContactsDto &#x3D; {
        contacts: request.contacts.map((contact) &#x3D;&gt; ({
          firstName: contact.firstName,
          lastName: contact.lastName,
          email: contact.email,
          phoneNumber: contact.phoneNumber,
        })),
        accountIds: request.accountIds,
        contactType: request.contactType,
      };

      const result &#x3D;
        await this.customerContactActionService.bulkCreateCustomerContacts(
          user,
          bulkCreateDto,
        );
      return {
        total: result.total,
        created: result.created,
        skipped: result.skipped,
      };
    } catch (error) {
      handleRpcError(error);
    }
  }
}
</code></pre>
    </div>
</div>
















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'controller';
            var COMPODOC_CURRENT_PAGE_URL = 'CustomerContactsGrpcController.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
