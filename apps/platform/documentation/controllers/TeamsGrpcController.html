<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content controller">
                   <div class="content-data">





<ol class="breadcrumb">
  <li class="breadcrumb-item">Controllers</li>
  <li class="breadcrumb-item" >TeamsGrpcController</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/teams/controllers/teams-grpc.controller.ts</code>
        </p>

            <p class="comment">
                <h3>Prefix</h3>
            </p>
            <p class="comment">
                <code>v1/teams</code>
            </p>






            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#addTeamMember" >addTeamMember</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#createRoutingRule" >createRoutingRule</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#createTeam" >createTeam</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#deleteTeam" >deleteTeam</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getAllPublicTeams" >getAllPublicTeams</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getAllTeamMembers" >getAllTeamMembers</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getAllTeams" >getAllTeams</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getBusinessHoursConfigData" >getBusinessHoursConfigData</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#getBusinessHoursConfigFieldMetadata" >getBusinessHoursConfigFieldMetadata</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getTeamById" >getTeamById</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getTeamCapacityData" >getTeamCapacityData</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#getTeamCapacityFieldMetadata" >getTeamCapacityFieldMetadata</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getTeamConfigurationData" >getTeamConfigurationData</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#getTeamConfigurationFieldMetadata" >getTeamConfigurationFieldMetadata</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getTeamConfigurations" >getTeamConfigurations</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getTeamData" >getTeamData</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#getTeamFieldMetadata" >getTeamFieldMetadata</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getTeamMemberData" >getTeamMemberData</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                <a href="#getTeamMemberFieldMetadata" >getTeamMemberFieldMetadata</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#removeTeamMember" >removeTeamMember</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#updateRoutingRule" >updateRoutingRule</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#updateTeam" >updateTeam</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#updateTeamConfigurations" >updateTeamConfigurations</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="addTeamMember"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>addTeamMember</b></span>
                        <a href="#addTeamMember"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>addTeamMember(data: teams.AddTeamMemberRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(teams.TEAMS_SERVICE_NAME, &#x27;AddTeamMember&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="222"
                                    class="link-to-prism">src/teams/controllers/teams-grpc.controller.ts:222</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>data</td>
                                            <td>
                                                        <code>teams.AddTeamMemberRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createRoutingRule"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>createRoutingRule</b></span>
                        <a href="#createRoutingRule"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createRoutingRule(data: teams.CreateRoutingRuleRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(teams.TEAMS_SERVICE_NAME, &#x27;CreateRoutingRule&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="452"
                                    class="link-to-prism">src/teams/controllers/teams-grpc.controller.ts:452</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>data</td>
                                            <td>
                                                        <code>teams.CreateRoutingRuleRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createTeam"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>createTeam</b></span>
                        <a href="#createTeam"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createTeam(data: teams.CreateTeamRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(teams.TEAMS_SERVICE_NAME, &#x27;CreateTeam&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="35"
                                    class="link-to-prism">src/teams/controllers/teams-grpc.controller.ts:35</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>data</td>
                                            <td>
                                                        <code>teams.CreateTeamRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteTeam"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>deleteTeam</b></span>
                        <a href="#deleteTeam"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteTeam(data: teams.DeleteTeamRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(teams.TEAMS_SERVICE_NAME, &#x27;DeleteTeam&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="111"
                                    class="link-to-prism">src/teams/controllers/teams-grpc.controller.ts:111</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>data</td>
                                            <td>
                                                        <code>teams.DeleteTeamRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getAllPublicTeams"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getAllPublicTeams</b></span>
                        <a href="#getAllPublicTeams"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getAllPublicTeams(_data: <a href="../s/Public.html" target="_self">teams.GetAllPublicTeamsRequest</a>, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(teams.TEAMS_SERVICE_NAME, &#x27;GetAllPublicTeams&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="191"
                                    class="link-to-prism">src/teams/controllers/teams-grpc.controller.ts:191</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>_data</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#Public" target="_self" >teams.GetAllPublicTeamsRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getAllTeamMembers"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getAllTeamMembers</b></span>
                        <a href="#getAllTeamMembers"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getAllTeamMembers(data: teams.GetAllTeamMembersRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(teams.TEAMS_SERVICE_NAME, &#x27;GetAllTeamMembers&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="304"
                                    class="link-to-prism">src/teams/controllers/teams-grpc.controller.ts:304</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>data</td>
                                            <td>
                                                        <code>teams.GetAllTeamMembersRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getAllTeams"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getAllTeams</b></span>
                        <a href="#getAllTeams"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getAllTeams(_data: teams.GetAllTeamsRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(teams.TEAMS_SERVICE_NAME, &#x27;GetAllTeams&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="163"
                                    class="link-to-prism">src/teams/controllers/teams-grpc.controller.ts:163</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>_data</td>
                                            <td>
                                                        <code>teams.GetAllTeamsRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getBusinessHoursConfigData"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getBusinessHoursConfigData</b></span>
                        <a href="#getBusinessHoursConfigData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getBusinessHoursConfigData(data: teams.GetBusinessHoursConfigDataRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(teams.TEAM_ANNOTATOR_SERVICE_NAME, &#x27;GetBusinessHoursConfigData&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="691"
                                    class="link-to-prism">src/teams/controllers/teams-grpc.controller.ts:691</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>data</td>
                                            <td>
                                                        <code>teams.GetBusinessHoursConfigDataRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;teams.GetBusinessHoursConfigDataResponse&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getBusinessHoursConfigFieldMetadata"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>getBusinessHoursConfigFieldMetadata</b></span>
                        <a href="#getBusinessHoursConfigFieldMetadata"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getBusinessHoursConfigFieldMetadata()</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(teams.TEAM_ANNOTATOR_SERVICE_NAME, &#x27;GetBusinessHoursConfigFieldMetadata&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="604"
                                    class="link-to-prism">src/teams/controllers/teams-grpc.controller.ts:604</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>    <code>teams.GetBusinessHoursConfigFieldMetadataResponse</code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTeamById"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getTeamById</b></span>
                        <a href="#getTeamById"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTeamById(data: teams.GetTeamByIdRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(teams.TEAMS_SERVICE_NAME, &#x27;GetTeamById&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="132"
                                    class="link-to-prism">src/teams/controllers/teams-grpc.controller.ts:132</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>data</td>
                                            <td>
                                                        <code>teams.GetTeamByIdRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTeamCapacityData"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getTeamCapacityData</b></span>
                        <a href="#getTeamCapacityData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTeamCapacityData(data: teams.GetTeamCapacityDataRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(teams.TEAM_ANNOTATOR_SERVICE_NAME, &#x27;GetTeamCapacityData&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="671"
                                    class="link-to-prism">src/teams/controllers/teams-grpc.controller.ts:671</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>data</td>
                                            <td>
                                                        <code>teams.GetTeamCapacityDataRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;teams.GetTeamCapacityDataResponse&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTeamCapacityFieldMetadata"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>getTeamCapacityFieldMetadata</b></span>
                        <a href="#getTeamCapacityFieldMetadata"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTeamCapacityFieldMetadata()</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(teams.TEAM_ANNOTATOR_SERVICE_NAME, &#x27;GetTeamCapacityFieldMetadata&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="592"
                                    class="link-to-prism">src/teams/controllers/teams-grpc.controller.ts:592</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>    <code>teams.GetTeamCapacityFieldMetadataResponse</code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTeamConfigurationData"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getTeamConfigurationData</b></span>
                        <a href="#getTeamConfigurationData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTeamConfigurationData(data: teams.GetTeamConfigurationDataRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(teams.TEAM_ANNOTATOR_SERVICE_NAME, &#x27;GetTeamConfigurationData&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="652"
                                    class="link-to-prism">src/teams/controllers/teams-grpc.controller.ts:652</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>data</td>
                                            <td>
                                                        <code>teams.GetTeamConfigurationDataRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;teams.GetTeamConfigurationDataResponse&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTeamConfigurationFieldMetadata"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>getTeamConfigurationFieldMetadata</b></span>
                        <a href="#getTeamConfigurationFieldMetadata"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTeamConfigurationFieldMetadata()</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(teams.TEAM_ANNOTATOR_SERVICE_NAME, &#x27;GetTeamConfigurationFieldMetadata&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="583"
                                    class="link-to-prism">src/teams/controllers/teams-grpc.controller.ts:583</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>    <code>teams.GetTeamConfigurationFieldMetadataResponse</code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTeamConfigurations"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getTeamConfigurations</b></span>
                        <a href="#getTeamConfigurations"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTeamConfigurations(data: teams.GetTeamConfigurationsRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(teams.TEAMS_SERVICE_NAME, &#x27;GetTeamConfigurations&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="347"
                                    class="link-to-prism">src/teams/controllers/teams-grpc.controller.ts:347</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>data</td>
                                            <td>
                                                        <code>teams.GetTeamConfigurationsRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTeamData"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getTeamData</b></span>
                        <a href="#getTeamData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTeamData(data: teams.GetTeamDataRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(teams.TEAM_ANNOTATOR_SERVICE_NAME, &#x27;GetTeamData&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="613"
                                    class="link-to-prism">src/teams/controllers/teams-grpc.controller.ts:613</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>data</td>
                                            <td>
                                                        <code>teams.GetTeamDataRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;teams.GetTeamDataResponse&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTeamFieldMetadata"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>getTeamFieldMetadata</b></span>
                        <a href="#getTeamFieldMetadata"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTeamFieldMetadata()</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(teams.TEAM_ANNOTATOR_SERVICE_NAME, &#x27;GetTeamFieldMetadata&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="562"
                                    class="link-to-prism">src/teams/controllers/teams-grpc.controller.ts:562</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>    <code>teams.GetTeamFieldMetadataResponse</code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTeamMemberData"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getTeamMemberData</b></span>
                        <a href="#getTeamMemberData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTeamMemberData(data: teams.GetTeamMemberDataRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(teams.TEAM_ANNOTATOR_SERVICE_NAME, &#x27;GetTeamMemberData&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="632"
                                    class="link-to-prism">src/teams/controllers/teams-grpc.controller.ts:632</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>data</td>
                                            <td>
                                                        <code>teams.GetTeamMemberDataRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;teams.GetTeamMemberDataResponse&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTeamMemberFieldMetadata"></a>
                    <span class="name">
                            <span class="modifier"></span>
                        <span ><b>getTeamMemberFieldMetadata</b></span>
                        <a href="#getTeamMemberFieldMetadata"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTeamMemberFieldMetadata()</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(teams.TEAM_ANNOTATOR_SERVICE_NAME, &#x27;GetTeamMemberFieldMetadata&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="571"
                                    class="link-to-prism">src/teams/controllers/teams-grpc.controller.ts:571</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>    <code>teams.GetTeamMemberFieldMetadataResponse</code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="removeTeamMember"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>removeTeamMember</b></span>
                        <a href="#removeTeamMember"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>removeTeamMember(data: teams.RemoveTeamMemberRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(teams.TEAMS_SERVICE_NAME, &#x27;RemoveTeamMember&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="276"
                                    class="link-to-prism">src/teams/controllers/teams-grpc.controller.ts:276</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>data</td>
                                            <td>
                                                        <code>teams.RemoveTeamMemberRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateRoutingRule"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>updateRoutingRule</b></span>
                        <a href="#updateRoutingRule"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateRoutingRule(data: teams.UpdateRoutingRuleRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(teams.TEAMS_SERVICE_NAME, &#x27;UpdateRoutingRule&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="507"
                                    class="link-to-prism">src/teams/controllers/teams-grpc.controller.ts:507</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>data</td>
                                            <td>
                                                        <code>teams.UpdateRoutingRuleRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateTeam"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>updateTeam</b></span>
                        <a href="#updateTeam"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateTeam(data: teams.UpdateTeamRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(teams.TEAMS_SERVICE_NAME, &#x27;UpdateTeam&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="73"
                                    class="link-to-prism">src/teams/controllers/teams-grpc.controller.ts:73</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>data</td>
                                            <td>
                                                        <code>teams.UpdateTeamRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateTeamConfigurations"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>updateTeamConfigurations</b></span>
                        <a href="#updateTeamConfigurations"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateTeamConfigurations(data: teams.UpdateTeamConfigurationsRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(teams.TEAMS_SERVICE_NAME, &#x27;UpdateTeamConfigurations&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="389"
                                    class="link-to-prism">src/teams/controllers/teams-grpc.controller.ts:389</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>data</td>
                                            <td>
                                                        <code>teams.UpdateTeamConfigurationsRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/TeamConfigurationsResponse.html" target="_self" >Promise&lt;teams.TeamConfigurationsResponse&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Metadata } from &quot;@grpc/grpc-js&quot;;
import { BadRequestException, Controller, UseGuards } from &quot;@nestjs/common&quot;;
import { GrpcMethod } from &quot;@nestjs/microservices&quot;;
import { handleRpcError } from &quot;@repo/nestjs-commons/errors&quot;;
import { GrpcAuthGuard } from &quot;@repo/nestjs-commons/guards&quot;;
import { extractUserMetadata } from &quot;@repo/nestjs-commons/utils&quot;;
import { teams } from &quot;@repo/shared-proto&quot;;
import { UpdateTimezoneWorkingHoursDto } from &quot;../../common/dto&quot;;
import { AddTeamMemberDto } from &quot;../dto&quot;;
import {
  CreateRoutingRuleGroupDto,
  UpdateRoutingRuleGroupDto,
} from &quot;../dto/team-routing.dto&quot;;
import { CreateTeamDto, UpdateTeamDto } from &quot;../dto/teams.dto&quot;;
import { TeamAnnotatorService } from &quot;../services/team-annotator.service&quot;;
import { TeamsService } from &quot;../services/teams.service&quot;;
import {
  addTeamMemberSchema,
  CreateRoutingRuleSchema,
  createTeamSchema,
  UpdateRoutingRuleSchema,
  updateTeamSchema,
  updateTimezoneWorkingHoursSchema,
} from &quot;../validators&quot;;

@Controller(&quot;v1/teams&quot;)
@UseGuards(GrpcAuthGuard)
export class TeamsGrpcController {
  constructor(
    private readonly teamsService: TeamsService,
    private readonly teamAnnotatorService: TeamAnnotatorService,
  ) {}

  @GrpcMethod(teams.TEAMS_SERVICE_NAME, &quot;CreateTeam&quot;)
  async createTeam(data: teams.CreateTeamRequest, metadata: Metadata) {
    try {
      // Extract the user metadata from the metadata object
      const user &#x3D; extractUserMetadata(metadata);

      // Validate the request
      const parsedData &#x3D; createTeamSchema.safeParse(data);
      if (!parsedData.success) {
        throw new BadRequestException(&quot;Invalid request!&quot;);
      }

      // Create the team payload
      const payload &#x3D; {
        ...parsedData.data,
        name: parsedData.data.name,
      } as CreateTeamDto;

      // Create the team
      const team &#x3D; await this.teamsService.createTeam(payload, user);

      // Format the response
      const response: teams.CommonTeamResponse &#x3D; {
        id: team.uid,
        name: team.name,
        description: team.description,
        identifier: team.identifier,
        parentTeamId: team.parentTeam?.uid,
        isPrivate: team.isPrivate,
      };

      // Return the response
      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAMS_SERVICE_NAME, &quot;UpdateTeam&quot;)
  async updateTeam(data: teams.UpdateTeamRequest, metadata: Metadata) {
    try {
      // Extract the user metadata from the metadata object
      const user &#x3D; extractUserMetadata(metadata);

      // Validate the request
      const parsedData &#x3D; updateTeamSchema.safeParse(data);
      if (!parsedData.success) {
        throw new BadRequestException(&quot;Invalid request!&quot;);
      }

      // Create the update team payload
      const payload &#x3D; {
        ...parsedData.data,
        name: parsedData.data.name,
      } as UpdateTeamDto;

      // Update the team
      const team &#x3D; await this.teamsService.updateTeam(data.id, payload, user);

      // Format the response
      const response: teams.CommonTeamResponse &#x3D; {
        id: team.uid,
        name: team.name,
        description: team.description,
        identifier: team.identifier,
        parentTeamId: team.parentTeam?.uid,
        isPrivate: team.isPrivate,
      };

      // Return the response
      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAMS_SERVICE_NAME, &quot;DeleteTeam&quot;)
  async deleteTeam(data: teams.DeleteTeamRequest, metadata: Metadata) {
    try {
      // Extract the user metadata from the metadata object
      const user &#x3D; extractUserMetadata(metadata);

      // Check if the team ID is provided
      if (!data.id) {
        throw new BadRequestException(&quot;Team ID is required!&quot;);
      }

      // Delete the team
      await this.teamsService.deleteOneTeam(data.id, user);

      // Return the response
      return {};
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAMS_SERVICE_NAME, &quot;GetTeamById&quot;)
  async getTeamById(data: teams.GetTeamByIdRequest, metadata: Metadata) {
    try {
      // Extract the user metadata from the metadata object
      const user &#x3D; extractUserMetadata(metadata);

      // Check if the team ID is provided
      if (!data.id) {
        throw new BadRequestException(&quot;Team ID is required!&quot;);
      }

      // Get the team by its ID
      const team &#x3D; await this.teamsService.findOneTeamById(data.id, user);

      // Format the response
      const response: teams.CommonTeamResponse &#x3D; {
        id: team.uid,
        name: team.name,
        description: team.description,
        identifier: team.identifier,
        parentTeamId: team.parentTeam?.uid,
        isPrivate: team.isPrivate,
      };

      // Return the response
      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAMS_SERVICE_NAME, &quot;GetAllTeams&quot;)
  async getAllTeams(_data: teams.GetAllTeamsRequest, metadata: Metadata) {
    try {
      // Extract the user metadata from the metadata object
      const user &#x3D; extractUserMetadata(metadata);

      // Get all teams
      const teams &#x3D; await this.teamsService.findAllTeams(user);

      // Format the response
      const response: teams.GetAllTeamsResponse &#x3D; {
        teams: teams.map((team) &#x3D;&gt; ({
          id: team.uid,
          name: team.name,
          description: team.description,
          identifier: team.identifier,
          parentTeamId: team.parentTeam?.uid,
          isPrivate: team.isPrivate,
        })),
      };

      // Return the response
      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAMS_SERVICE_NAME, &quot;GetAllPublicTeams&quot;)
  async getAllPublicTeams(
    _data: teams.GetAllPublicTeamsRequest,
    metadata: Metadata,
  ) {
    try {
      // Extract the user metadata from the metadata object
      const user &#x3D; extractUserMetadata(metadata);

      // Get all public teams
      const teams &#x3D; await this.teamsService.getAllPublicTeams(user);

      // Format the response
      const response: teams.GetAllPublicTeamsResponse &#x3D; {
        teams: teams.map((team) &#x3D;&gt; ({
          id: team.uid,
          name: team.name,
          description: team.description,
          identifier: team.identifier,
          parentTeamId: team.parentTeam?.uid,
          isPrivate: team.isPrivate,
        })),
      };

      // Return the response
      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAMS_SERVICE_NAME, &quot;AddTeamMember&quot;)
  async addTeamMember(data: teams.AddTeamMemberRequest, metadata: Metadata) {
    try {
      // Extract the user metadata from the metadata object
      const user &#x3D; extractUserMetadata(metadata);

      // Validate the request
      const parsedData &#x3D; addTeamMemberSchema.safeParse(data);
      if (!parsedData.success) {
        throw new BadRequestException(&quot;Invalid request!&quot;);
      }

      // Check if the email or user ID is provided
      if (!parsedData.data.email &amp;&amp; !parsedData.data.userId) {
        throw new BadRequestException(
          &quot;Either email or user ID is required to add a member to a team!&quot;,
        );
      }

      // Create the payload
      const payload &#x3D; {
        email: parsedData.data.email,
        userId: parsedData.data.userId,
        isAdmin: parsedData.data.isAdmin,
      } as AddTeamMemberDto;

      // Add the team member
      const teamMember &#x3D; await this.teamsService.addMemberToTeam(
        parsedData.data.teamId,
        payload,
        user,
      );

      // Format the response
      const response: teams.TeamMemberResponse &#x3D; {
        id: teamMember.user.uid,
        name: teamMember.user.name,
        email: teamMember.user.email,
        invitedBy: teamMember.invitedBy?.name,
        teamId: teamMember.team.uid,
        teamName: teamMember.team.name,
        isActive: teamMember.isActive,
        role: teamMember.role,
        isOwner: teamMember.team.teamOwnerId &#x3D;&#x3D;&#x3D; teamMember.user.id,
        joinedAt: teamMember.joinedAt.toString(),
      };

      // Return the response
      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAMS_SERVICE_NAME, &quot;RemoveTeamMember&quot;)
  async removeTeamMember(
    data: teams.RemoveTeamMemberRequest,
    metadata: Metadata,
  ) {
    try {
      // Extract the user metadata from the metadata object
      const user &#x3D; extractUserMetadata(metadata);

      // Check if the team ID and member ID are provided
      if (!data.teamId || !data.memberId) {
        throw new BadRequestException(&quot;Team ID and member ID are required!&quot;);
      }

      // Remove the team member
      await this.teamsService.removeMemberFromTeam(
        data.teamId,
        data.memberId,
        user,
      );

      // Return the response
      return {};
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAMS_SERVICE_NAME, &quot;GetAllTeamMembers&quot;)
  async getAllTeamMembers(
    data: teams.GetAllTeamMembersRequest,
    metadata: Metadata,
  ) {
    try {
      // Extract the user metadata from the metadata object
      const user &#x3D; extractUserMetadata(metadata);

      // Check if the team ID is provided
      if (!data.teamId) {
        throw new BadRequestException(&quot;Team ID is required!&quot;);
      }

      // Get all team members
      const teamMembers &#x3D; await this.teamsService.findTeamMembers(
        data.teamId,
        user,
      );

      // Format the response
      const response: teams.GetAllTeamMembersResponse &#x3D; {
        members: teamMembers.map((member) &#x3D;&gt; ({
          id: member.user.uid,
          name: member.user.name,
          email: member.user.email,
          invitedBy: member.invitedBy?.name,
          teamId: member.team.uid,
          teamName: member.team.name,
          isActive: member.isActive,
          role: member.role,
          isOwner: member.team.teamOwnerId &#x3D;&#x3D;&#x3D; member.user.id,
          joinedAt: member.joinedAt.toString(),
        })),
      };

      // Return the response
      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAMS_SERVICE_NAME, &quot;GetTeamConfigurations&quot;)
  async getTeamConfigurations(
    data: teams.GetTeamConfigurationsRequest,
    metadata: Metadata,
  ) {
    try {
      // Extract the user metadata from the metadata object
      const user &#x3D; extractUserMetadata(metadata);

      // Check if the team ID is provided
      if (!data.teamId) {
        throw new BadRequestException(&quot;Team ID is required!&quot;);
      }

      // Get the team configurations
      const teamConfigurations &#x3D; await this.teamsService.getTeamConfigurations(
        data.teamId,
        user,
      );

      // Format the response
      const response: teams.TeamConfigurationsResponse &#x3D; {
        teamId: teamConfigurations.teamId,
        timezone: teamConfigurations.teamConfig.timezone,
        holidays: teamConfigurations.teamConfig.holidays,
        routingRespectsTimezone:
          teamConfigurations.teamConfig.routingRespectsTimezone,
        routingRespectsUserTimezone:
          teamConfigurations.teamConfig.routingRespectsUserTimezone,
        routingRespectsUserAvailability:
          teamConfigurations.teamConfig.routingRespectsUserAvailability,
        userRoutingStrategy: teamConfigurations.teamConfig.userRoutingStrategy,
        dailyConfig: teamConfigurations.businessHoursConfig,
      };

      // Return the response
      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAMS_SERVICE_NAME, &quot;UpdateTeamConfigurations&quot;)
  async updateTeamConfigurations(
    data: teams.UpdateTeamConfigurationsRequest,
    metadata: Metadata,
  ): Promise&lt;teams.TeamConfigurationsResponse&gt; {
    try {
      // Extract the user metadata from the metadata object
      const user &#x3D; extractUserMetadata(metadata);

      // Check if the team ID is provided
      if (!data.teamId) {
        throw new BadRequestException(&quot;Team ID is required!&quot;);
      }

      // Validate the request
      const parsedData &#x3D; updateTimezoneWorkingHoursSchema.safeParse(data);
      if (!parsedData.success) {
        throw new BadRequestException(&quot;Invalid request!&quot;);
      }

      // Create the payload
      const payload &#x3D; {
        timezone: parsedData.data.timezone,
        routingRespectsTimezone: parsedData.data.routingRespectsTimezone,
        routingRespectsUserTimezone:
          parsedData.data.routingRespectsUserTimezone,
        routingRespectsUserAvailability:
          parsedData.data.routingRespectsUserAvailability,
        userRoutingStrategy: parsedData.data.userRoutingStrategy,
        businessHours: parsedData.data.dailyConfig,
      } as UpdateTimezoneWorkingHoursDto;

      // Update the team configurations
      const updatedTeamConfigurations &#x3D;
        await this.teamsService.updateTeamConfigurations(
          data.teamId,
          payload,
          user,
        );

      // Format the response
      const response: teams.TeamConfigurationsResponse &#x3D; {
        teamId: updatedTeamConfigurations.teamId,
        timezone: updatedTeamConfigurations.teamConfig.timezone,
        holidays: updatedTeamConfigurations.teamConfig.holidays,
        routingRespectsTimezone:
          updatedTeamConfigurations.teamConfig.routingRespectsTimezone,
        routingRespectsUserTimezone:
          updatedTeamConfigurations.teamConfig.routingRespectsUserTimezone,
        routingRespectsUserAvailability:
          updatedTeamConfigurations.teamConfig.routingRespectsUserAvailability,
        userRoutingStrategy:
          updatedTeamConfigurations.teamConfig.userRoutingStrategy,
        dailyConfig: updatedTeamConfigurations.businessHoursConfig,
      };

      // Return the response
      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAMS_SERVICE_NAME, &quot;CreateRoutingRule&quot;)
  async createRoutingRule(
    data: teams.CreateRoutingRuleRequest,
    metadata: Metadata,
  ) {
    try {
      // Extract the user metadata from the metadata object
      const user &#x3D; extractUserMetadata(metadata);

      // Validate the request
      const parsedData &#x3D; CreateRoutingRuleSchema.safeParse(data);
      if (!parsedData.success) {
        throw new BadRequestException(&quot;Invalid request!&quot;);
      }

      // Create the payload
      const payload &#x3D; {
        name: parsedData.data.name,
        description: parsedData.data.description,
        teamId: parsedData.data.teamId,
        evaluationOrder: parsedData.data.evaluationOrder,
        resultTeamId: parsedData.data.resultTeamId,
        andRules: parsedData.data.andRules,
        orRules: parsedData.data.orRules,
      } as CreateRoutingRuleGroupDto;

      // Create the routing rule
      const routingRule &#x3D; await this.teamsService.createRoutingRule(
        parsedData.data.teamId,
        user,
        payload,
      );

      // Format the response
      const response: teams.CommonRoutingRuleResponse &#x3D; {
        id: routingRule.uid,
        name: routingRule.name,
        description: routingRule.description,
        teamId: routingRule.team.uid,
        evaluationOrder: routingRule.priority,
        resultTeamId: routingRule.resultTeamId,
        andRules: routingRule.andRules,
        orRules: routingRule.orRules,
        createdBy: routingRule.createdBy.uid,
        createdAt: routingRule.createdAt.toString(),
        updatedAt: routingRule.updatedAt.toString(),
      };

      // Return the response
      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAMS_SERVICE_NAME, &quot;UpdateRoutingRule&quot;)
  async updateRoutingRule(
    data: teams.UpdateRoutingRuleRequest,
    metadata: Metadata,
  ) {
    try {
      // Extract the user metadata from the metadata object
      const user &#x3D; extractUserMetadata(metadata);

      // Validate the request
      const parsedData &#x3D; UpdateRoutingRuleSchema.safeParse(data);
      if (!parsedData.success) {
        throw new BadRequestException(&quot;Invalid request!&quot;);
      }

      // Create the payload
      const payload &#x3D; {
        name: parsedData.data.name,
        description: parsedData.data.description,
        evaluationOrder: parsedData.data.evaluationOrder,
        resultTeamId: parsedData.data.resultTeamId,
        andRules: parsedData.data.andRules,
        orRules: parsedData.data.orRules,
      } as UpdateRoutingRuleGroupDto;

      // Update the routing rule
      const routingRule &#x3D; await this.teamsService.updateRoutingRule(
        parsedData.data.teamId,
        parsedData.data.id,
        user,
        payload,
      );

      // Format the response
      const response: teams.CommonRoutingRuleResponse &#x3D; {
        id: routingRule.uid,
        name: routingRule.name,
        description: routingRule.description,
        teamId: routingRule.team.uid,
        evaluationOrder: routingRule.priority,
        resultTeamId: routingRule.resultTeamId,
        andRules: routingRule.andRules,
        orRules: routingRule.orRules,
        createdBy: routingRule.createdBy.uid,
        createdAt: routingRule.createdAt.toString(),
        updatedAt: routingRule.updatedAt.toString(),
      };

      // Return the response
      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAM_ANNOTATOR_SERVICE_NAME, &quot;GetTeamFieldMetadata&quot;)
  getTeamFieldMetadata(): teams.GetTeamFieldMetadataResponse {
    try {
      return this.teamAnnotatorService.getTeamFieldMetadata();
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAM_ANNOTATOR_SERVICE_NAME, &quot;GetTeamMemberFieldMetadata&quot;)
  getTeamMemberFieldMetadata(): teams.GetTeamMemberFieldMetadataResponse {
    try {
      return this.teamAnnotatorService.getTeamMemberFieldMetadata();
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    teams.TEAM_ANNOTATOR_SERVICE_NAME,
    &quot;GetTeamConfigurationFieldMetadata&quot;,
  )
  getTeamConfigurationFieldMetadata(): teams.GetTeamConfigurationFieldMetadataResponse {
    try {
      return this.teamAnnotatorService.getTeamConfigurationFieldMetadata();
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAM_ANNOTATOR_SERVICE_NAME, &quot;GetTeamCapacityFieldMetadata&quot;)
  getTeamCapacityFieldMetadata(): teams.GetTeamCapacityFieldMetadataResponse {
    try {
      return this.teamAnnotatorService.getTeamCapacityFieldMetadata();
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    teams.TEAM_ANNOTATOR_SERVICE_NAME,
    &quot;GetBusinessHoursConfigFieldMetadata&quot;,
  )
  getBusinessHoursConfigFieldMetadata(): teams.GetBusinessHoursConfigFieldMetadataResponse {
    try {
      return this.teamAnnotatorService.getBusinessHoursConfigFieldMetadata();
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAM_ANNOTATOR_SERVICE_NAME, &quot;GetTeamData&quot;)
  async getTeamData(
    data: teams.GetTeamDataRequest,
    metadata: Metadata,
  ): Promise&lt;teams.GetTeamDataResponse&gt; {
    try {
      // Extract the user
      const user &#x3D; extractUserMetadata(metadata);

      return await this.teamAnnotatorService.getTeamData(
        data.teamId,
        data.relations,
        user.orgId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAM_ANNOTATOR_SERVICE_NAME, &quot;GetTeamMemberData&quot;)
  async getTeamMemberData(
    data: teams.GetTeamMemberDataRequest,
    metadata: Metadata,
  ): Promise&lt;teams.GetTeamMemberDataResponse&gt; {
    try {
      // Extract the user
      const user &#x3D; extractUserMetadata(metadata);

      return await this.teamAnnotatorService.getTeamMemberData(
        data.teamId,
        data.memberId,
        data.relations,
        user.orgId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAM_ANNOTATOR_SERVICE_NAME, &quot;GetTeamConfigurationData&quot;)
  async getTeamConfigurationData(
    data: teams.GetTeamConfigurationDataRequest,
    metadata: Metadata,
  ): Promise&lt;teams.GetTeamConfigurationDataResponse&gt; {
    try {
      // Extract the user
      const user &#x3D; extractUserMetadata(metadata);

      return await this.teamAnnotatorService.getTeamConfigurationData(
        data.teamId,
        data.relations,
        user.orgId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAM_ANNOTATOR_SERVICE_NAME, &quot;GetTeamCapacityData&quot;)
  async getTeamCapacityData(
    data: teams.GetTeamCapacityDataRequest,
    metadata: Metadata,
  ): Promise&lt;teams.GetTeamCapacityDataResponse&gt; {
    try {
      // Extract the user
      const user &#x3D; extractUserMetadata(metadata);

      return await this.teamAnnotatorService.getTeamCapacityData(
        data.teamId,
        data.userId,
        data.relations,
        user.orgId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAM_ANNOTATOR_SERVICE_NAME, &quot;GetBusinessHoursConfigData&quot;)
  async getBusinessHoursConfigData(
    data: teams.GetBusinessHoursConfigDataRequest,
    metadata: Metadata,
  ): Promise&lt;teams.GetBusinessHoursConfigDataResponse&gt; {
    try {
      // Extract the user
      const user &#x3D; extractUserMetadata(metadata);

      return await this.teamAnnotatorService.getBusinessHoursConfigData(
        data.teamId,
        data.relations,
        user.orgId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }
}
</code></pre>
    </div>
</div>
















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'controller';
            var COMPODOC_CURRENT_PAGE_URL = 'TeamsGrpcController.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
