<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content controller">
                   <div class="content-data">





<ol class="breadcrumb">
  <li class="breadcrumb-item">Controllers</li>
  <li class="breadcrumb-item" >AccountAttributeValueActionController</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/accounts/controllers/account-attribute-value.action.controller.ts</code>
        </p>

            <p class="comment">
                <h3>Prefix</h3>
            </p>
            <p class="comment">
                <code>v1/accounts/attributes</code>
            </p>






            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#createAccountAttributeValue" >createAccountAttributeValue</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#deleteAccountAttributeValue" >deleteAccountAttributeValue</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#findAttributeValuesByAttribute" >findAttributeValuesByAttribute</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#updateAccountAttributeValue" >updateAccountAttributeValue</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createAccountAttributeValue"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>createAccountAttributeValue</b></span>
                        <a href="#createAccountAttributeValue"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createAccountAttributeValue(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, createAccountAttributeValueDto: <a href="../classes/CreateAccountAttributeValueDto.html" target="_self">CreateAccountAttributeValueDto</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Post()<br />@ApiResponseMessage(&#x27;Account attribute value created successfully!&#x27;)<br />@ApiCreateEndpoint({summary: &#x27;Creates an account attribute value&#x27;, responseType: AccountAttributeValueResponseDto})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="54"
                                    class="link-to-prism">src/accounts/controllers/account-attribute-value.action.controller.ts:54</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>createAccountAttributeValueDto</td>
                                            <td>
                                                            <code><a href="../classes/CreateAccountAttributeValueDto.html" target="_self" >CreateAccountAttributeValueDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/AccountAttributeValueResponseDto.html" target="_self" >Promise&lt;AccountAttributeValueResponseDto&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteAccountAttributeValue"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>deleteAccountAttributeValue</b></span>
                        <a href="#deleteAccountAttributeValue"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteAccountAttributeValue(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, forceDelete: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank">boolean</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Delete(&#x27;:id&#x27;)<br />@ApiResponseMessage(&#x27;Account attribute value deleted successfully!&#x27;)<br />@ApiDeleteEndpoint({summary: &#x27;Deletes an account attribute value&#x27;})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="172"
                                    class="link-to-prism">src/accounts/controllers/account-attribute-value.action.controller.ts:172</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>forceDelete</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;void&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAttributeValuesByAttribute"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>findAttributeValuesByAttribute</b></span>
                        <a href="#findAttributeValuesByAttribute"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAttributeValuesByAttribute(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, findAccountAttributeValueDto: <a href="../classes/FindAccountAttributeValueDto.html" target="_self">FindAccountAttributeValueDto</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get()<br />@ApiResponseMessage(&#x27;Account attribute values fetched successfully!&#x27;)<br />@ApiGetEndpoint({summary: &#x27;Gets all account attribute values&#x27;, responseType: AccountAttributeValueResponseDto})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="90"
                                    class="link-to-prism">src/accounts/controllers/account-attribute-value.action.controller.ts:90</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>findAccountAttributeValueDto</td>
                                            <td>
                                                            <code><a href="../classes/FindAccountAttributeValueDto.html" target="_self" >FindAccountAttributeValueDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/AccountAttributeValueResponseDto.html" target="_self" >Promise&lt;AccountAttributeValueResponseDto[]&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateAccountAttributeValue"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>updateAccountAttributeValue</b></span>
                        <a href="#updateAccountAttributeValue"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateAccountAttributeValue(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, updateAccountAttributeValueDto: <a href="../classes/UpdateAccountAttributeValueDto.html" target="_self">UpdateAccountAttributeValueDto</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Put(&#x27;:id&#x27;)<br />@ApiResponseMessage(&#x27;Account attribute value updated successfully!&#x27;)<br />@ApiUpdateEndpoint({summary: &#x27;Updates an account attribute value&#x27;, responseType: AccountAttributeValueResponseDto})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="127"
                                    class="link-to-prism">src/accounts/controllers/account-attribute-value.action.controller.ts:127</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>updateAccountAttributeValueDto</td>
                                            <td>
                                                            <code><a href="../classes/UpdateAccountAttributeValueDto.html" target="_self" >UpdateAccountAttributeValueDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/AccountAttributeValueResponseDto.html" target="_self" >Promise&lt;AccountAttributeValueResponseDto&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  Inject,
  InternalServerErrorException,
  Param,
  Post,
  Put,
  Query,
  UseInterceptors,
} from &quot;@nestjs/common&quot;;
import { ApiTags } from &quot;@nestjs/swagger&quot;;
import {
  ApiCreateEndpoint,
  ApiDeleteEndpoint,
  ApiGetEndpoint,
  ApiResponseMessage,
  ApiUpdateEndpoint,
  ResponseTransformInterceptor,
} from &quot;@repo/nestjs-commons/decorators&quot;;
import { SentryService } from &quot;@repo/nestjs-commons/filters&quot;;
import { ILogger } from &quot;@repo/nestjs-commons/logger&quot;;
import { CurrentUser } from &quot;../../common/decorators/user.decorator&quot;;
import {
  CreateAccountAttributeValueDto,
  FindAccountAttributeValueDto,
  UpdateAccountAttributeValueDto,
} from &quot;../dtos/account-attribute-value.dto&quot;;
import { AccountAttributeValueResponseDto } from &quot;../dtos/response/account-attribute-value.dto&quot;;
import { AccountAttributeValueActionService } from &quot;../services/account-attribute-value.action.service&quot;;

@ApiTags(&quot;Accounts&quot;)
@Controller(&quot;v1/accounts/attributes&quot;)
@UseInterceptors(ResponseTransformInterceptor)
export class AccountAttributeValueActionController {
  private readonly logSpanId &#x3D; &quot;[AccountAttributeValueActionController]&quot;;

  constructor(
    private readonly accountAttributeValueActionService: AccountAttributeValueActionService,
    @Inject(&quot;Sentry&quot;) private readonly sentryService: SentryService,
    @Inject(&quot;CustomLogger&quot;) private readonly logger: ILogger,
  ) {}

  @Post()
  @ApiResponseMessage(&quot;Account attribute value created successfully!&quot;)
  @ApiCreateEndpoint({
    summary: &quot;Creates an account attribute value&quot;,
    responseType: AccountAttributeValueResponseDto,
  })
  async createAccountAttributeValue(
    @CurrentUser() user: CurrentUser,
    @Body() createAccountAttributeValueDto: CreateAccountAttributeValueDto,
  ): Promise&lt;AccountAttributeValueResponseDto&gt; {
    try {
      const createdAttributeValue &#x3D;
        await this.accountAttributeValueActionService.createAccountAttributeValue(
          user,
          createAccountAttributeValueDto,
        );

      return AccountAttributeValueResponseDto.fromEntity(createdAttributeValue);
    } catch (error) {
      this.logger.error(
        &#x60;${this.logSpanId} Error encountered while creating account attribute value. &gt; Error message: ${error.message}&#x60;,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: &quot;ACCOUNTS_ATTRIBUTE_VALUE_CONTROLLER&quot;,
        fn: &quot;createAccountAttributeValue&quot;,
        organizationId: user.orgUid,
        body: createAccountAttributeValueDto,
        user,
      });

      throw new InternalServerErrorException(&quot;Something went wrong!&quot;);
    }
  }

  @Get()
  @ApiResponseMessage(&quot;Account attribute values fetched successfully!&quot;)
  @ApiGetEndpoint({
    summary: &quot;Gets all account attribute values&quot;,
    responseType: AccountAttributeValueResponseDto,
  })
  async findAttributeValuesByAttribute(
    @CurrentUser() user: CurrentUser,
    @Query()
    findAccountAttributeValueDto: FindAccountAttributeValueDto,
  ): Promise&lt;AccountAttributeValueResponseDto[]&gt; {
    try {
      const attributeValues &#x3D;
        await this.accountAttributeValueActionService.findAccountAttributeValues(
          user,
          findAccountAttributeValueDto.attribute,
        );

      return attributeValues.map(AccountAttributeValueResponseDto.fromEntity);
    } catch (error) {
      this.logger.error(
        &#x60;${this.logSpanId} Error encountered while fetching account attribute values. &gt; Error message: ${error.message}&#x60;,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: &quot;ACCOUNTS_ATTRIBUTE_VALUE_CONTROLLER&quot;,
        fn: &quot;findAttributeValuesByAttribute&quot;,
        organizationId: user.orgUid,
        body: findAccountAttributeValueDto,
        user,
      });

      throw new InternalServerErrorException(&quot;Something went wrong!&quot;);
    }
  }

  @Put(&quot;:id&quot;)
  @ApiResponseMessage(&quot;Account attribute value updated successfully!&quot;)
  @ApiUpdateEndpoint({
    summary: &quot;Updates an account attribute value&quot;,
    responseType: AccountAttributeValueResponseDto,
  })
  async updateAccountAttributeValue(
    @CurrentUser() user: CurrentUser,
    @Param(&quot;id&quot;) id: string,
    @Body() updateAccountAttributeValueDto: UpdateAccountAttributeValueDto,
  ): Promise&lt;AccountAttributeValueResponseDto&gt; {
    if (!id)
      throw new BadRequestException(&quot;Account attribute value ID is required!&quot;);

    try {
      const updatedAttributeValue &#x3D;
        await this.accountAttributeValueActionService.updateAccountAttributeValue(
          user,
          id,
          updateAccountAttributeValueDto,
        );

      return AccountAttributeValueResponseDto.fromEntity(updatedAttributeValue);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        &#x60;${this.logSpanId} Error encountered while updating account attribute value. &gt; Error message: ${error.message}&#x60;,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: &quot;ACCOUNTS_ATTRIBUTE_VALUE_CONTROLLER&quot;,
        fn: &quot;updateAccountAttributeValue&quot;,
        organizationId: user.orgUid,
        params: { id },
        body: updateAccountAttributeValueDto,
        user,
      });

      throw new InternalServerErrorException(&quot;Something went wrong!&quot;);
    }
  }

  @Delete(&quot;:id&quot;)
  @ApiResponseMessage(&quot;Account attribute value deleted successfully!&quot;)
  @ApiDeleteEndpoint({
    summary: &quot;Deletes an account attribute value&quot;,
  })
  async deleteAccountAttributeValue(
    @CurrentUser() user: CurrentUser,
    @Param(&quot;id&quot;) id: string,
    @Query(&quot;forceDelete&quot;) forceDelete: boolean,
  ): Promise&lt;void&gt; {
    try {
      await this.accountAttributeValueActionService.deleteAccountAttributeValue(
        user,
        id,
        forceDelete,
      );
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        &#x60;${this.logSpanId} Error encountered while deleting account attribute value. &gt; Error message: ${error.message}&#x60;,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: &quot;ACCOUNTS_ATTRIBUTE_VALUE_CONTROLLER&quot;,
        fn: &quot;deleteAccountAttributeValue&quot;,
        organizationId: user.orgUid,
        params: { id, forceDelete },
        user,
      });

      throw new InternalServerErrorException(&quot;Something went wrong!&quot;);
    }
  }
}
</code></pre>
    </div>
</div>
















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'controller';
            var COMPODOC_CURRENT_PAGE_URL = 'AccountAttributeValueActionController.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
