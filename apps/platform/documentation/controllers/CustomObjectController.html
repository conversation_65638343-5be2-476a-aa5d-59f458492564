<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content controller">
                   <div class="content-data">





<ol class="breadcrumb">
  <li class="breadcrumb-item">Controllers</li>
  <li class="breadcrumb-item" >CustomObjectController</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/custom-object/controllers/custom-object.controller.ts</code>
        </p>

            <p class="comment">
                <h3>Prefix</h3>
            </p>
            <p class="comment">
                <code>v1/custom-object</code>
            </p>






            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#checkIfUserBelongsToTeam" >checkIfUserBelongsToTeam</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#create" >create</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#findAll" >findAll</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#findByIds" >findByIds</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#search" >search</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#update" >update</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#validateAndFetchTeam" >validateAndFetchTeam</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="checkIfUserBelongsToTeam"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>checkIfUserBelongsToTeam</b></span>
                        <a href="#checkIfUserBelongsToTeam"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>checkIfUserBelongsToTeam(userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="286"
                                    class="link-to-prism">src/custom-object/controllers/custom-object.controller.ts:286</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>userId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="create"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>create</b></span>
                        <a href="#create"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>create(createCustomObjectDto: <a href="../classes/CreateCustomObjectDto.html" target="_self">CreateCustomObjectDto</a>, request: <a href="../interfaces/FastifyRequest.html" target="_self">FastifyRequest</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Post()<br />@ApiBody({type: CreateCustomObjectDto})<br />@ApiResponseMessage(&#x27;Custom object created successfully!&#x27;)<br />@ApiCreateEndpoint({summary: &#x27;Create a custom object&#x27;, responseType: CustomObjectResponseDto})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="55"
                                    class="link-to-prism">src/custom-object/controllers/custom-object.controller.ts:55</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>createCustomObjectDto</td>
                                            <td>
                                                            <code><a href="../classes/CreateCustomObjectDto.html" target="_self" >CreateCustomObjectDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../interfaces/FastifyRequest.html" target="_self" >FastifyRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAll"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>findAll</b></span>
                        <a href="#findAll"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAll(request: <a href="../interfaces/FastifyRequest.html" target="_self">FastifyRequest</a>, limit: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>, offset: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>, teamId?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, onlyTeamFields?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank">boolean</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get()<br />@ApiResponseMessage(&#x27;Custom objects fetched successfully!&#x27;)<br />@ApiGetEndpoint({summary: &#x27;Get all custom objects&#x27;, responseType: GetAllCustomObjectsResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="229"
                                    class="link-to-prism">src/custom-object/controllers/custom-object.controller.ts:229</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../interfaces/FastifyRequest.html" target="_self" >FastifyRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>limit</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>offset</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>onlyTeamFields</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findByIds"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>findByIds</b></span>
                        <a href="#findByIds"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findByIds(request: <a href="../interfaces/FastifyRequest.html" target="_self">FastifyRequest</a>, ids: string | string[])</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get(&#x27;/fetchByIds&#x27;)<br />@ApiResponseMessage(&#x27;Custom objects fetched successfully!&#x27;)<br />@ApiGetEndpoint({summary: &#x27;Get custom objects by IDs&#x27;, responseType: GetAllCustomObjectsResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="149"
                                    class="link-to-prism">src/custom-object/controllers/custom-object.controller.ts:149</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../interfaces/FastifyRequest.html" target="_self" >FastifyRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>ids</td>
                                            <td>
                                                        <code>string | string[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="search"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>search</b></span>
                        <a href="#search"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>search(request: <a href="../interfaces/FastifyRequest.html" target="_self">FastifyRequest</a>, term: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, teamId?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, onlyTeamFields?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank">boolean</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get(&#x27;/search&#x27;)<br />@ApiGetEndpoint({summary: &#x27;Search custom object using name&#x27;, responseType: GetAllCustomObjectsResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="188"
                                    class="link-to-prism">src/custom-object/controllers/custom-object.controller.ts:188</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../interfaces/FastifyRequest.html" target="_self" >FastifyRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>term</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>onlyTeamFields</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="update"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>update</b></span>
                        <a href="#update"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>update(updateCustomObjectDto: <a href="../classes/UpdateCustomObjectDto.html" target="_self">UpdateCustomObjectDto</a>, request: <a href="../interfaces/FastifyRequest.html" target="_self">FastifyRequest</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Patch()<br />@ApiResponseMessage(&#x27;Custom objects updated successfully!&#x27;)<br />@ApiUpdateEndpoint({summary: &#x27;Update custom objects&#x27;, responseType: GetAllCustomObjectsResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="107"
                                    class="link-to-prism">src/custom-object/controllers/custom-object.controller.ts:107</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>updateCustomObjectDto</td>
                                            <td>
                                                            <code><a href="../classes/UpdateCustomObjectDto.html" target="_self" >UpdateCustomObjectDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../interfaces/FastifyRequest.html" target="_self" >FastifyRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateAndFetchTeam"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>validateAndFetchTeam</b></span>
                        <a href="#validateAndFetchTeam"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateAndFetchTeam(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="273"
                                    class="link-to-prism">src/custom-object/controllers/custom-object.controller.ts:273</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Validates and fetches a team by its ID.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the team to fetch.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The team.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  BadRequestException,
  Body,
  Controller,
  DefaultValuePipe,
  Get,
  Inject,
  NotFoundException,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Req,
} from &quot;@nestjs/common&quot;;
import { ApiBody, ApiTags } from &quot;@nestjs/swagger&quot;;
import {
  ApiCreateEndpoint,
  ApiGetEndpoint,
  ApiResponseMessage,
  ApiUpdateEndpoint,
} from &quot;@repo/nestjs-commons/decorators&quot;;
import { ILogger } from &quot;@repo/nestjs-commons/logger&quot;;
import { FastifyRequest } from &quot;fastify&quot;;
import { SkipAllThrottler } from &quot;src/common/decorators/throttler.decorator&quot;;
import { TeamsService } from &quot;../../teams/services/teams.service&quot;;
import {
  CreateCustomObjectDto,
  CustomObjectResponseDto,
  GetAllCustomObjectsResponse,
  UpdateCustomObjectDto,
} from &quot;../dto/custom-object.dto&quot;;
import { CustomObjectService } from &quot;../services/custom-object.service&quot;;
import { CustomObjectValidatorService } from &quot;../validators/custom-object.validator&quot;;

@ApiTags(&quot;Custom Objects&quot;)
@Controller(&quot;v1/custom-object&quot;)
@SkipAllThrottler()
export class CustomObjectController {
  constructor(
    @Inject(&quot;CustomLogger&quot;)
    private readonly logger: ILogger,

    private readonly customObjectService: CustomObjectService,
    private readonly teamsService: TeamsService,
    private readonly customObjectValidatorService: CustomObjectValidatorService,
  ) {}

  @Post()
  @ApiBody({ type: CreateCustomObjectDto })
  @ApiResponseMessage(&quot;Custom object created successfully!&quot;)
  @ApiCreateEndpoint({
    summary: &quot;Create a custom object&quot;,
    responseType: CustomObjectResponseDto,
  })
  async create(
    @Body() createCustomObjectDto: CreateCustomObjectDto,
    @Req() request: FastifyRequest,
  ) {
    try {
      if (createCustomObjectDto.teamId) {
        const team &#x3D; await this.validateAndFetchTeam(
          createCustomObjectDto.teamId,
          request.user.orgId,
        );
        createCustomObjectDto.teamId &#x3D; team?.id;

        await this.checkIfUserBelongsToTeam(
          request.user.sub,
          team.id,
          request.user.orgId,
        );
      }

      await this.customObjectValidatorService.validateCreatePayload(
        request.user.orgId,
        createCustomObjectDto,
      );

      const customObject &#x3D; await this.customObjectService.create(
        request.user.orgId,
        request.user.sub,
        createCustomObjectDto,
      );

      const returnableCustomObject &#x3D; await this.customObjectService.findByIds(
        request.user.orgId,
        [customObject.uid],
      );

      return CustomObjectResponseDto.fromEntity(
        returnableCustomObject.items[0],
      );
    } catch (err) {
      this.logger.error(
        &#x60;Error while trying to create custom object: ${err.message}, stack: ${err.stack}&#x60;,
      );
      throw err;
    }
  }

  @Patch()
  @ApiResponseMessage(&quot;Custom objects updated successfully!&quot;)
  @ApiUpdateEndpoint({
    summary: &quot;Update custom objects&quot;,
    responseType: GetAllCustomObjectsResponse,
  })
  async update(
    @Body() updateCustomObjectDto: UpdateCustomObjectDto,
    @Req() request: FastifyRequest,
  ) {
    try {
      const teams &#x3D; await this.teamsService.getTeamsByUser(request.user);
      const teamIds &#x3D; teams.map((team) &#x3D;&gt; team.teamId);

      await this.customObjectValidatorService.validateUpdatePayload(
        request.user.orgId,
        updateCustomObjectDto.objects,
        teamIds,
      );
      await this.customObjectService.update(
        request.user.sub,
        request.user.orgId,
        updateCustomObjectDto,
      );

      const customObjects &#x3D; await this.customObjectService.findByIds(
        request.user.orgId,
        updateCustomObjectDto.objects.map((obj) &#x3D;&gt; obj.id),
      );

      const responseItems &#x3D; customObjects.items.map((obj) &#x3D;&gt;
        CustomObjectResponseDto.fromEntity(obj),
      );
      return { ...customObjects, items: responseItems };
    } catch (err) {
      this.logger.error(
        &#x60;Error while trying to update custom object: ${err.message}, stack: ${err.stack}&#x60;,
      );
      throw err;
    }
  }

  @Get(&quot;/fetchByIds&quot;)
  @ApiResponseMessage(&quot;Custom objects fetched successfully!&quot;)
  @ApiGetEndpoint({
    summary: &quot;Get custom objects by IDs&quot;,
    responseType: GetAllCustomObjectsResponse,
  })
  async findByIds(
    @Req() request: FastifyRequest,
    @Query(&quot;ids&quot;) ids: string | string[],
  ) {
    try {
      if (!ids) {
        throw new BadRequestException(&quot;No IDs provided&quot;);
      }

      const idArray &#x3D; typeof ids &#x3D;&#x3D;&#x3D; &quot;string&quot; ? [ids] : ids;

      if (idArray.length &#x3D;&#x3D;&#x3D; 0) {
        throw new BadRequestException(&quot;No IDs provided&quot;);
      }
      const teams &#x3D; await this.teamsService.getTeamsByUser(request.user);
      const teamIds &#x3D; teams.map((team) &#x3D;&gt; team.teamId);
      const customObjects &#x3D;
        await this.customObjectService.findByIdsWithTeamCheck(
          request.user.orgId,
          idArray,
          teamIds,
        );
      const responseItems &#x3D; customObjects.items.map((item) &#x3D;&gt;
        CustomObjectResponseDto.fromEntity(item),
      );
      return { ...customObjects, items: responseItems };
    } catch (err) {
      this.logger.error(
        &#x60;Error while trying to fetch custom objects by IDs: ${err.message}, stack: ${err.stack}&#x60;,
      );
      throw err;
    }
  }

  @Get(&quot;/search&quot;)
  @ApiGetEndpoint({
    summary: &quot;Search custom object using name&quot;,
    responseType: GetAllCustomObjectsResponse,
  })
  async search(
    @Req() request: FastifyRequest,
    @Query(&quot;term&quot;) term: string,
    @Query(&quot;teamId&quot;) teamId?: string,
    @Query(&quot;onlyTeamFields&quot;) onlyTeamFields?: boolean,
  ) {
    try {
      let team;
      if (teamId) {
        team &#x3D; await this.validateAndFetchTeam(teamId, request.user.orgId);
        await this.checkIfUserBelongsToTeam(
          request.user.sub,
          team?.id,
          request.user.orgId,
        );
      }
      const customObjects &#x3D; await this.customObjectService.search(
        request.user.orgId,
        term,
        team?.id,
        onlyTeamFields,
      );

      const responseItems &#x3D; customObjects.items.map((item) &#x3D;&gt;
        CustomObjectResponseDto.fromEntity(item),
      );
      return { ...customObjects, items: responseItems };
    } catch (err) {
      this.logger.error(
        &#x60;Error while trying to search custom objects: ${err.message}, stack: ${err.stack}&#x60;,
      );
      throw err;
    }
  }

  @Get()
  @ApiResponseMessage(&quot;Custom objects fetched successfully!&quot;)
  @ApiGetEndpoint({
    summary: &quot;Get all custom objects&quot;,
    responseType: GetAllCustomObjectsResponse,
  })
  async findAll(
    @Req() request: FastifyRequest,
    @Query(&quot;limit&quot;, new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query(&quot;offset&quot;, new DefaultValuePipe(0), ParseIntPipe) offset: number,
    @Query(&quot;teamId&quot;) teamId?: string,
    @Query(&quot;onlyTeamFields&quot;) onlyTeamFields?: boolean,
  ) {
    try {
      let team;
      if (teamId) {
        team &#x3D; await this.validateAndFetchTeam(teamId, request.user.orgId);
        await this.checkIfUserBelongsToTeam(
          request.user.sub,
          team?.id,
          request.user.orgId,
        );
      }

      const customObjects &#x3D;
        await this.customObjectService.fetchPaginatedResults(
          request.user.orgId,
          limit,
          offset,
          team?.id,
          onlyTeamFields,
        );

      customObjects.results.forEach((item) &#x3D;&gt; {
        CustomObjectResponseDto.fromEntity(item);
      });
      return customObjects;
    } catch (err) {
      this.logger.error(
        &#x60;Error while trying to fetch custom objects: ${err.message}, stack: ${err.stack}&#x60;,
      );
      throw err;
    }
  }

  /**
   * Validates and fetches a team by its ID.
   * @param teamId The ID of the team to fetch.
   * @returns The team.
   */
  private async validateAndFetchTeam(teamId: string, organizationId: string) {
    const team &#x3D; await this.teamsService.findOneByTeamId(
      teamId,
      organizationId,
    );

    if (!team) {
      throw new NotFoundException(&quot;Team not found!&quot;);
    }

    return team;
  }

  private async checkIfUserBelongsToTeam(
    userId: string,
    teamId: string,
    organizationId: string,
  ) {
    const teamMember &#x3D; await this.teamsService.userBelongsToTeam(
      userId,
      teamId,
      organizationId,
    );

    if (!teamMember) {
      throw new BadRequestException(&quot;User is not a member of this team!&quot;);
    }
  }
}
</code></pre>
    </div>
</div>
















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'controller';
            var COMPODOC_CURRENT_PAGE_URL = 'CustomObjectController.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
