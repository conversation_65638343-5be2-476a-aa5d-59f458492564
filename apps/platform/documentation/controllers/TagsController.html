<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content controller">
                   <div class="content-data">





<ol class="breadcrumb">
  <li class="breadcrumb-item">Controllers</li>
  <li class="breadcrumb-item" >TagsController</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/tags/controllers/tags.controller.ts</code>
        </p>

            <p class="comment">
                <h3>Prefix</h3>
            </p>
            <p class="comment">
                <code>v1/tags</code>
            </p>






            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#createTag" >createTag</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#deleteTag" >deleteTag</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getTag" >getTag</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getTags" >getTags</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#updateTag" >updateTag</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createTag"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>createTag</b></span>
                        <a href="#createTag"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createTag(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, createTagDto: <a href="../classes/CreateTagDto.html" target="_self">CreateTagDto</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Post(&#x27;/&#x27;)<br />@ApiBody({type: CreateTagDto})<br />@ApiResponseMessage(&#x27;Tag created successfully!&#x27;)<br />@ApiCreateEndpoint({summary: &#x27;Create a new tag&#x27;, responseType: CommonTagResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="43"
                                    class="link-to-prism">src/tags/controllers/tags.controller.ts:43</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>createTagDto</td>
                                            <td>
                                                            <code><a href="../classes/CreateTagDto.html" target="_self" >CreateTagDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteTag"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>deleteTag</b></span>
                        <a href="#deleteTag"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteTag(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, tagId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Delete(&#x27;:tagId&#x27;)<br />@ApiResponseMessage(&#x27;Tag deleted successfully!&#x27;)<br />@ApiDeleteEndpoint({summary: &#x27;Delete a tag&#x27;})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="110"
                                    class="link-to-prism">src/tags/controllers/tags.controller.ts:110</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>tagId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTag"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getTag</b></span>
                        <a href="#getTag"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTag(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, tagId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get(&#x27;/:tagId&#x27;)<br />@ApiResponseMessage(&#x27;Tag fetched successfully!&#x27;)<br />@ApiGetEndpoint({summary: &#x27;Get a specific tag&#x27;, responseType: CommonTagResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="72"
                                    class="link-to-prism">src/tags/controllers/tags.controller.ts:72</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>tagId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTags"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getTags</b></span>
                        <a href="#getTags"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTags(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, query: <a href="../classes/GetAllTagsQuery.html" target="_self">GetAllTagsQuery</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get(&#x27;/&#x27;)<br />@ApiResponseMessage(&#x27;Tags fetched successfully!&#x27;)<br />@ApiGetEndpoint({summary: &#x27;Get all tags&#x27;, responseType: GetAllTagsResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="57"
                                    class="link-to-prism">src/tags/controllers/tags.controller.ts:57</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>query</td>
                                            <td>
                                                            <code><a href="../classes/GetAllTagsQuery.html" target="_self" >GetAllTagsQuery</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateTag"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>updateTag</b></span>
                        <a href="#updateTag"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateTag(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, tagId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, updateTagDto: Partial<CreateTagDto>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Patch(&#x27;/:tagId&#x27;)<br />@ApiBody({type: CreateTagDto})<br />@ApiResponseMessage(&#x27;Tag updated successfully!&#x27;)<br />@ApiUpdateEndpoint({summary: &#x27;Update a tag&#x27;, responseType: CommonTagResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="88"
                                    class="link-to-prism">src/tags/controllers/tags.controller.ts:88</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>tagId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>updateTagDto</td>
                                            <td>
                                                        <code>Partial&lt;CreateTagDto&gt;</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseInterceptors,
} from &quot;@nestjs/common&quot;;
import { ApiBody, ApiTags } from &quot;@nestjs/swagger&quot;;
import {
  ApiCreateEndpoint,
  ApiDeleteEndpoint,
  ApiGetEndpoint,
  ApiResponseMessage,
  ApiUpdateEndpoint,
  ResponseTransformInterceptor,
} from &quot;@repo/nestjs-commons/decorators&quot;;
import { SkipAllThrottler } from &quot;src/common/decorators/throttler.decorator&quot;;
import { CurrentUser } from &quot;../../common/decorators&quot;;
import { CommonTagResponse, GetAllTagsResponse } from &quot;../dto/response&quot;;
import { CreateTagDto, GetAllTagsQuery } from &quot;../dto/tags.dto&quot;;
import { TagsService } from &quot;../services/tags.service&quot;;
import { TagsResponseDto } from &quot;../transformers/tags.transformer&quot;;

@ApiTags(&quot;Tags&quot;)
@Controller(&quot;v1/tags&quot;)
@SkipAllThrottler()
@UseInterceptors(ResponseTransformInterceptor)
export class TagsController {
  constructor(private readonly tagService: TagsService) {}

  @Post(&quot;/&quot;)
  @ApiBody({ type: CreateTagDto })
  @ApiResponseMessage(&quot;Tag created successfully!&quot;)
  @ApiCreateEndpoint({
    summary: &quot;Create a new tag&quot;,
    responseType: CommonTagResponse,
  })
  async createTag(
    @CurrentUser() user: CurrentUser,
    @Body() createTagDto: CreateTagDto,
  ) {
    const createdTag &#x3D; await this.tagService.createTag(createTagDto, user);
    return TagsResponseDto.fromEntity(createdTag);
  }

  @Get(&quot;/&quot;)
  @ApiResponseMessage(&quot;Tags fetched successfully!&quot;)
  @ApiGetEndpoint({
    summary: &quot;Get all tags&quot;,
    responseType: GetAllTagsResponse,
  })
  async getTags(
    @CurrentUser() user: CurrentUser,
    @Query() query: GetAllTagsQuery,
  ) {
    if (!query.type) throw new BadRequestException(&quot;Please provide a type!&quot;);
    const tags &#x3D; await this.tagService.findAllTags(user, query);
    return tags.map(TagsResponseDto.fromEntity);
  }

  @Get(&quot;/:tagId&quot;)
  @ApiResponseMessage(&quot;Tag fetched successfully!&quot;)
  @ApiGetEndpoint({
    summary: &quot;Get a specific tag&quot;,
    responseType: CommonTagResponse,
  })
  async getTag(
    @CurrentUser() user: CurrentUser,
    @Param(&quot;tagId&quot;) tagId: string,
  ) {
    if (!tagId) throw new BadRequestException(&quot;Please provide a tag ID!&quot;);
    const tag &#x3D; await this.tagService.findOneTag(tagId, user);
    return TagsResponseDto.fromEntity(tag);
  }

  @Patch(&quot;/:tagId&quot;)
  @ApiBody({ type: CreateTagDto })
  @ApiResponseMessage(&quot;Tag updated successfully!&quot;)
  @ApiUpdateEndpoint({
    summary: &quot;Update a tag&quot;,
    responseType: CommonTagResponse,
  })
  async updateTag(
    @CurrentUser() user: CurrentUser,
    @Param(&quot;tagId&quot;) tagId: string,
    @Body() updateTagDto: Partial&lt;CreateTagDto&gt;,
  ) {
    if (!tagId) throw new BadRequestException(&quot;Please provide a tag ID!&quot;);

    // Update the tag
    const updatedTag &#x3D; await this.tagService.updateTag(
      tagId,
      updateTagDto,
      user,
    );

    return TagsResponseDto.fromEntity(updatedTag);
  }

  @Delete(&quot;:tagId&quot;)
  @ApiResponseMessage(&quot;Tag deleted successfully!&quot;)
  @ApiDeleteEndpoint({
    summary: &quot;Delete a tag&quot;,
  })
  async deleteTag(
    @CurrentUser() user: CurrentUser,
    @Param(&quot;tagId&quot;) tagId: string,
  ) {
    if (!tagId) throw new BadRequestException(&quot;Please provide a tag ID!&quot;);
    await this.tagService.deleteTag(tagId, user);
  }
}
</code></pre>
    </div>
</div>
















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'controller';
            var COMPODOC_CURRENT_PAGE_URL = 'TagsController.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
