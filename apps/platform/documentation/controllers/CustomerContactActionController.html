<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content controller">
                   <div class="content-data">





<ol class="breadcrumb">
  <li class="breadcrumb-item">Controllers</li>
  <li class="breadcrumb-item" >CustomerContactActionController</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/accounts/controllers/customer-contact.action.controller.ts</code>
        </p>

            <p class="comment">
                <h3>Prefix</h3>
            </p>
            <p class="comment">
                <code>v1/accounts/contacts</code>
            </p>






            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#bulkCreateCustomerContacts" >bulkCreateCustomerContacts</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#createCustomerContact" >createCustomerContact</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#deleteCustomerContact" >deleteCustomerContact</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#findAllCustomerContacts" >findAllCustomerContacts</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#updateCustomerContact" >updateCustomerContact</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="bulkCreateCustomerContacts"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>bulkCreateCustomerContacts</b></span>
                        <a href="#bulkCreateCustomerContacts"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>bulkCreateCustomerContacts(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, bulkCreateDto: <a href="../classes/BulkCreateCustomerContactsDto.html" target="_self">BulkCreateCustomerContactsDto</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Post(&#x27;/bulk&#x27;)<br />@ApiResponseMessage(&#x27;Customer contacts created successfully!&#x27;)<br />@ApiCreateEndpoint({summary: &#x27;Bulk create customer contacts&#x27;, responseType: CustomerContactBulkResponseDto})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="98"
                                    class="link-to-prism">src/accounts/controllers/customer-contact.action.controller.ts:98</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>bulkCreateDto</td>
                                            <td>
                                                            <code><a href="../classes/BulkCreateCustomerContactsDto.html" target="_self" >BulkCreateCustomerContactsDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../interfaces/Customer.html" target="_self" >Promise&lt;CustomerContactBulkResponseDto&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createCustomerContact"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>createCustomerContact</b></span>
                        <a href="#createCustomerContact"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createCustomerContact(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, createDto: <a href="../classes/CreateCustomerContactDto.html" target="_self">CreateCustomerContactDto</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Post()<br />@ApiResponseMessage(&#x27;Customer contact created successfully!&#x27;)<br />@ApiCreateEndpoint({summary: &#x27;Create a customer contact&#x27;, responseType: CustomerContactResponseDto})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="58"
                                    class="link-to-prism">src/accounts/controllers/customer-contact.action.controller.ts:58</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>createDto</td>
                                            <td>
                                                            <code><a href="../classes/CreateCustomerContactDto.html" target="_self" >CreateCustomerContactDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../interfaces/Customer.html" target="_self" >Promise&lt;CustomerContactResponseDto&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteCustomerContact"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>deleteCustomerContact</b></span>
                        <a href="#deleteCustomerContact"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteCustomerContact(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, contactId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Delete(&#x27;:contactId&#x27;)<br />@ApiResponseMessage(&#x27;Customer contact deleted successfully!&#x27;)<br />@ApiDeleteEndpoint({summary: &#x27;Delete a customer contact&#x27;})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="222"
                                    class="link-to-prism">src/accounts/controllers/customer-contact.action.controller.ts:222</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>contactId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;void&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAllCustomerContacts"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>findAllCustomerContacts</b></span>
                        <a href="#findAllCustomerContacts"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAllCustomerContacts(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, query: <a href="../classes/FindAllCustomerContactsDto.html" target="_self">FindAllCustomerContactsDto</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get()<br />@ApiResponseMessage(&#x27;Customer contacts fetched successfully!&#x27;)<br />@ApiGetEndpoint({summary: &#x27;Get all customer contacts&#x27;, responseType: undefined})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="139"
                                    class="link-to-prism">src/accounts/controllers/customer-contact.action.controller.ts:139</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>query</td>
                                            <td>
                                                            <code><a href="../classes/FindAllCustomerContactsDto.html" target="_self" >FindAllCustomerContactsDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../interfaces/Customer.html" target="_self" >Promise&lt;CustomerContactResponseDto[]&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateCustomerContact"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>updateCustomerContact</b></span>
                        <a href="#updateCustomerContact"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateCustomerContact(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, contactId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, updateDto: <a href="../classes/UpdateCustomerContactDto.html" target="_self">UpdateCustomerContactDto</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Put(&#x27;:contactId&#x27;)<br />@ApiResponseMessage(&#x27;Customer contact updated successfully!&#x27;)<br />@ApiUpdateEndpoint({summary: &#x27;Update a customer contact&#x27;, responseType: CustomerContactResponseDto})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="179"
                                    class="link-to-prism">src/accounts/controllers/customer-contact.action.controller.ts:179</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>contactId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>updateDto</td>
                                            <td>
                                                            <code><a href="../classes/UpdateCustomerContactDto.html" target="_self" >UpdateCustomerContactDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../interfaces/Customer.html" target="_self" >Promise&lt;CustomerContactResponseDto&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  Inject,
  InternalServerErrorException,
  Param,
  Post,
  Put,
  Query,
  UseInterceptors,
} from &quot;@nestjs/common&quot;;
import { ApiTags } from &quot;@nestjs/swagger&quot;;
import {
  ApiCreateEndpoint,
  ApiDeleteEndpoint,
  ApiGetEndpoint,
  ApiResponseMessage,
  ApiUpdateEndpoint,
  ResponseTransformInterceptor,
} from &quot;@repo/nestjs-commons/decorators&quot;;
import { SentryService } from &quot;@repo/nestjs-commons/filters&quot;;
import { ILogger } from &quot;@repo/nestjs-commons/logger&quot;;
import { CurrentUser } from &quot;../../common/decorators/user.decorator&quot;;
import {
  BulkCreateCustomerContactsDto,
  CreateCustomerContactDto,
  FindAllCustomerContactsDto,
  UpdateCustomerContactDto,
} from &quot;../dtos/customer-contact.dto&quot;;
import {
  CustomerContactBulkResponseDto,
  CustomerContactResponseDto,
} from &quot;../dtos/response/customer-contact.dto&quot;;
import { CustomerContactActionService } from &quot;../services/customer-contact.action.service&quot;;

@ApiTags(&quot;Accounts&quot;)
@Controller(&quot;v1/accounts/contacts&quot;)
@UseInterceptors(ResponseTransformInterceptor)
export class CustomerContactActionController {
  private readonly logSpanId &#x3D; &quot;[CustomerContactActionController]&quot;;

  constructor(
    private readonly customerContactActionService: CustomerContactActionService,
    @Inject(&quot;Sentry&quot;) private readonly sentryService: SentryService,
    @Inject(&quot;CustomLogger&quot;) private readonly logger: ILogger,
  ) {}

  @Post()
  @ApiResponseMessage(&quot;Customer contact created successfully!&quot;)
  @ApiCreateEndpoint({
    summary: &quot;Create a customer contact&quot;,
    responseType: CustomerContactResponseDto,
  })
  async createCustomerContact(
    @CurrentUser() user: CurrentUser,
    @Body() createDto: CreateCustomerContactDto,
  ): Promise&lt;CustomerContactResponseDto&gt; {
    try {
      const contact &#x3D;
        await this.customerContactActionService.createCustomerContact(
          user,
          createDto,
        );

      return CustomerContactResponseDto.fromEntity(contact);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        &#x60;${this.logSpanId} Error encountered while creating customer contact. &gt; Error message: ${error.message}&#x60;,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: &quot;CUSTOMER_CONTACT_CONTROLLER&quot;,
        fn: &quot;createCustomerContact&quot;,
        organizationId: user.orgUid,
        body: createDto,
        user,
      });

      throw new InternalServerErrorException(&quot;Something went wrong!&quot;);
    }
  }

  @Post(&quot;/bulk&quot;)
  @ApiResponseMessage(&quot;Customer contacts created successfully!&quot;)
  @ApiCreateEndpoint({
    summary: &quot;Bulk create customer contacts&quot;,
    responseType: CustomerContactBulkResponseDto,
  })
  async bulkCreateCustomerContacts(
    @CurrentUser() user: CurrentUser,
    @Body()
    bulkCreateDto: BulkCreateCustomerContactsDto,
  ): Promise&lt;CustomerContactBulkResponseDto&gt; {
    try {
      const result &#x3D;
        await this.customerContactActionService.bulkCreateCustomerContacts(
          user,
          bulkCreateDto,
        );

      return result;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        &#x60;${this.logSpanId} Error encountered while bulk creating customer contacts. &gt; Error message: ${error.message}&#x60;,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: &quot;CUSTOMER_CONTACT_CONTROLLER&quot;,
        fn: &quot;bulkCreateCustomerContacts&quot;,
        organizationId: user.orgUid,
        body: bulkCreateDto,
        user,
      });

      throw new InternalServerErrorException(&quot;Something went wrong!&quot;);
    }
  }

  @Get()
  @ApiResponseMessage(&quot;Customer contacts fetched successfully!&quot;)
  @ApiGetEndpoint({
    summary: &quot;Get all customer contacts&quot;,
    responseType: [CustomerContactResponseDto],
  })
  async findAllCustomerContacts(
    @CurrentUser() user: CurrentUser,
    @Query() query: FindAllCustomerContactsDto,
  ): Promise&lt;CustomerContactResponseDto[]&gt; {
    try {
      const contacts &#x3D;
        await this.customerContactActionService.findCustomerContacts(
          user,
          query,
        );

      return contacts.map(CustomerContactResponseDto.fromEntity);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        &#x60;${this.logSpanId} Error encountered while fetching customer contacts. &gt; Error message: ${error.message}&#x60;,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: &quot;CUSTOMER_CONTACT_CONTROLLER&quot;,
        fn: &quot;findAllCustomerContacts&quot;,
        organizationId: user.orgUid,
        query,
        user,
      });

      throw new InternalServerErrorException(&quot;Something went wrong!&quot;);
    }
  }

  @Put(&quot;:contactId&quot;)
  @ApiResponseMessage(&quot;Customer contact updated successfully!&quot;)
  @ApiUpdateEndpoint({
    summary: &quot;Update a customer contact&quot;,
    responseType: CustomerContactResponseDto,
  })
  async updateCustomerContact(
    @CurrentUser() user: CurrentUser,
    @Param(&quot;contactId&quot;) contactId: string,
    @Body() updateDto: UpdateCustomerContactDto,
  ): Promise&lt;CustomerContactResponseDto&gt; {
    if (!contactId) throw new BadRequestException(&quot;Contact ID is required!&quot;);

    try {
      const contact &#x3D;
        await this.customerContactActionService.updateCustomerContact(
          user,
          contactId,
          updateDto,
        );

      return CustomerContactResponseDto.fromEntity(contact);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        &#x60;${this.logSpanId} Error encountered while updating customer contact. &gt; Error message: ${error.message}&#x60;,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: &quot;CUSTOMER_CONTACT_CONTROLLER&quot;,
        fn: &quot;updateCustomerContact&quot;,
        organizationId: user.orgUid,
        body: updateDto,
        user,
      });

      throw new InternalServerErrorException(&quot;Something went wrong!&quot;);
    }
  }

  @Delete(&quot;:contactId&quot;)
  @ApiResponseMessage(&quot;Customer contact deleted successfully!&quot;)
  @ApiDeleteEndpoint({
    summary: &quot;Delete a customer contact&quot;,
  })
  async deleteCustomerContact(
    @CurrentUser() user: CurrentUser,
    @Param(&quot;contactId&quot;) contactId: string,
  ): Promise&lt;void&gt; {
    if (!contactId) throw new BadRequestException(&quot;Contact ID is required!&quot;);

    try {
      await this.customerContactActionService.deleteCustomerContact(
        user,
        contactId,
      );
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        &#x60;${this.logSpanId} Error encountered while deleting customer contact. &gt; Error message: ${error.message}&#x60;,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: &quot;CUSTOMER_CONTACT_CONTROLLER&quot;,
        fn: &quot;deleteCustomerContact&quot;,
        organizationId: user.orgUid,
        params: { contactId },
        user,
      });

      throw new InternalServerErrorException(&quot;Something went wrong!&quot;);
    }
  }
}
</code></pre>
    </div>
</div>
















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'controller';
            var COMPODOC_CURRENT_PAGE_URL = 'CustomerContactActionController.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
