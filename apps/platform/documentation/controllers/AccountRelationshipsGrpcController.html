<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content controller">
                   <div class="content-data">





<ol class="breadcrumb">
  <li class="breadcrumb-item">Controllers</li>
  <li class="breadcrumb-item" >AccountRelationshipsGrpcController</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/accounts/controllers/grpc/account-relationships.grpc.controller.ts</code>
        </p>







            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#createAccountRelationship" >createAccountRelationship</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#createAccountRelationshipType" >createAccountRelationshipType</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#deleteAccountRelationship" >deleteAccountRelationship</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#deleteAccountRelationshipType" >deleteAccountRelationshipType</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getAccountRelationships" >getAccountRelationships</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getAccountRelationshipTypes" >getAccountRelationshipTypes</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#updateAccountRelationship" >updateAccountRelationship</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#updateAccountRelationshipType" >updateAccountRelationshipType</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createAccountRelationship"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>createAccountRelationship</b></span>
                        <a href="#createAccountRelationship"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createAccountRelationship(request: accounts.CreateAccountRelationshipRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME, &#x27;CreateAccountRelationship&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="171"
                                    class="link-to-prism">src/accounts/controllers/grpc/account-relationships.grpc.controller.ts:171</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                        <code>accounts.CreateAccountRelationshipRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;accounts.AccountRelationship&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createAccountRelationshipType"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>createAccountRelationshipType</b></span>
                        <a href="#createAccountRelationshipType"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createAccountRelationshipType(request: accounts.CreateAccountRelationshipTypeRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME, &#x27;CreateAccountRelationshipType&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="58"
                                    class="link-to-prism">src/accounts/controllers/grpc/account-relationships.grpc.controller.ts:58</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                        <code>accounts.CreateAccountRelationshipTypeRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;accounts.AccountRelationshipType&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteAccountRelationship"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>deleteAccountRelationship</b></span>
                        <a href="#deleteAccountRelationship"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteAccountRelationship(request: accounts.DeleteAccountRelationshipRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME, &#x27;DeleteAccountRelationship&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="230"
                                    class="link-to-prism">src/accounts/controllers/grpc/account-relationships.grpc.controller.ts:230</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                        <code>accounts.DeleteAccountRelationshipRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;void&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteAccountRelationshipType"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>deleteAccountRelationshipType</b></span>
                        <a href="#deleteAccountRelationshipType"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteAccountRelationshipType(request: accounts.DeleteAccountRelationshipTypeRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME, &#x27;DeleteAccountRelationshipType&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="117"
                                    class="link-to-prism">src/accounts/controllers/grpc/account-relationships.grpc.controller.ts:117</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                        <code>accounts.DeleteAccountRelationshipTypeRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;void&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getAccountRelationships"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getAccountRelationships</b></span>
                        <a href="#getAccountRelationships"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getAccountRelationships(request: <a href="../classes/GetAccountRelationships.html" target="_self">accounts.GetAccountRelationshipsRequest</a>, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME, &#x27;GetAccountRelationships&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="136"
                                    class="link-to-prism">src/accounts/controllers/grpc/account-relationships.grpc.controller.ts:136</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../classes/GetAccountRelationships.html" target="_self" >accounts.GetAccountRelationshipsRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/GetAccountRelationships.html" target="_self" >Promise&lt;accounts.GetAccountRelationshipsResponse&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getAccountRelationshipTypes"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getAccountRelationshipTypes</b></span>
                        <a href="#getAccountRelationshipTypes"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getAccountRelationshipTypes(metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME, &#x27;GetAccountRelationshipTypes&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="32"
                                    class="link-to-prism">src/accounts/controllers/grpc/account-relationships.grpc.controller.ts:32</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/GetAccountRelationshipTypes.html" target="_self" >Promise&lt;accounts.GetAccountRelationshipTypesResponse&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateAccountRelationship"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>updateAccountRelationship</b></span>
                        <a href="#updateAccountRelationship"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateAccountRelationship(request: accounts.UpdateAccountRelationshipRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME, &#x27;UpdateAccountRelationship&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="201"
                                    class="link-to-prism">src/accounts/controllers/grpc/account-relationships.grpc.controller.ts:201</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                        <code>accounts.UpdateAccountRelationshipRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;accounts.AccountRelationship&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateAccountRelationshipType"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>updateAccountRelationshipType</b></span>
                        <a href="#updateAccountRelationshipType"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateAccountRelationshipType(request: accounts.UpdateAccountRelationshipTypeRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME, &#x27;UpdateAccountRelationshipType&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="87"
                                    class="link-to-prism">src/accounts/controllers/grpc/account-relationships.grpc.controller.ts:87</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                        <code>accounts.UpdateAccountRelationshipTypeRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;accounts.AccountRelationshipType&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Metadata } from &quot;@grpc/grpc-js&quot;;
import { Controller, UseGuards } from &quot;@nestjs/common&quot;;
import { GrpcMethod } from &quot;@nestjs/microservices&quot;;
import { handleRpcError } from &quot;@repo/nestjs-commons/errors&quot;;
import { GrpcAuthGuard } from &quot;@repo/nestjs-commons/guards&quot;;
import { extractUserMetadata } from &quot;@repo/nestjs-commons/utils&quot;;
import { accounts } from &quot;@repo/shared-proto&quot;;
import { AccountRelationshipActionService } from &quot;src/accounts/services/account-relationship.action.service&quot;;
import {
  CreateAccountRelationshipDto,
  CreateAccountRelationshipTypeDto,
  FindAccountRelationshipDto,
  UpdateAccountRelationshipDto,
  UpdateAccountRelationshipTypeDto,
} from &quot;../../dtos/account-relationship.dto&quot;;
import {
  AccountRelationshipResponseDto,
  AccountRelationshipTypeResponseDto,
} from &quot;../../dtos/response/account-relationship.dto&quot;;

@Controller()
@UseGuards(GrpcAuthGuard)
export class AccountRelationshipsGrpcController {
  constructor(
    private readonly accountRelationshipActionService: AccountRelationshipActionService,
  ) {}

  @GrpcMethod(
    accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME,
    &quot;GetAccountRelationshipTypes&quot;,
  )
  async getAccountRelationshipTypes(
    metadata: Metadata,
  ): Promise&lt;accounts.GetAccountRelationshipTypesResponse&gt; {
    try {
      const user &#x3D; extractUserMetadata(metadata);

      const types &#x3D;
        await this.accountRelationshipActionService.findAllAccountRelationshipTypes(
          user.orgId,
        );
      const typeDtos &#x3D; types.map((type) &#x3D;&gt;
        AccountRelationshipTypeResponseDto.fromEntity(type),
      ) as accounts.AccountRelationshipType[];

      return {
        data: typeDtos,
      };
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME,
    &quot;CreateAccountRelationshipType&quot;,
  )
  async createAccountRelationshipType(
    request: accounts.CreateAccountRelationshipTypeRequest,
    metadata: Metadata,
  ): Promise&lt;accounts.AccountRelationshipType&gt; {
    try {
      const user &#x3D; extractUserMetadata(metadata);

      const createDto: CreateAccountRelationshipTypeDto &#x3D; {
        name: request.name,
        inverseRelationshipId: request.inverseRelationshipId,
      };

      const type &#x3D;
        await this.accountRelationshipActionService.createAccountRelationshipType(
          user,
          createDto,
        );
      return AccountRelationshipTypeResponseDto.fromEntity(
        type,
      ) as accounts.AccountRelationshipType;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME,
    &quot;UpdateAccountRelationshipType&quot;,
  )
  async updateAccountRelationshipType(
    request: accounts.UpdateAccountRelationshipTypeRequest,
    metadata: Metadata,
  ): Promise&lt;accounts.AccountRelationshipType&gt; {
    try {
      const user &#x3D; extractUserMetadata(metadata);

      const updateDto: UpdateAccountRelationshipTypeDto &#x3D; {
        name: request.name,
        inverseRelationshipId: request.inverseRelationshipId,
      };

      const type &#x3D;
        await this.accountRelationshipActionService.updateAccountRelationshipType(
          user,
          request.id,
          updateDto,
        );
      return AccountRelationshipTypeResponseDto.fromEntity(
        type,
      ) as accounts.AccountRelationshipType;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME,
    &quot;DeleteAccountRelationshipType&quot;,
  )
  async deleteAccountRelationshipType(
    request: accounts.DeleteAccountRelationshipTypeRequest,
    metadata: Metadata,
  ): Promise&lt;void&gt; {
    try {
      const user &#x3D; extractUserMetadata(metadata);
      await this.accountRelationshipActionService.deleteAccountRelationshipType(
        user,
        request.id,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME,
    &quot;GetAccountRelationships&quot;,
  )
  async getAccountRelationships(
    request: accounts.GetAccountRelationshipsRequest,
    metadata: Metadata,
  ): Promise&lt;accounts.GetAccountRelationshipsResponse&gt; {
    try {
      const user &#x3D; extractUserMetadata(metadata);

      const query: FindAccountRelationshipDto &#x3D; {
        accountId: request.accountId,
        relationshipTypeId: request.relationshipType,
        page: request.page,
        limit: request.limit,
      };

      const relationships &#x3D;
        await this.accountRelationshipActionService.findAllAccountRelationships(
          user,
          query,
        );
      const relationshipDtos &#x3D; relationships.map((relationship) &#x3D;&gt;
        AccountRelationshipResponseDto.fromEntity(relationship),
      ) as accounts.AccountRelationship[];

      return {
        data: relationshipDtos,
      };
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME,
    &quot;CreateAccountRelationship&quot;,
  )
  async createAccountRelationship(
    request: accounts.CreateAccountRelationshipRequest,
    metadata: Metadata,
  ): Promise&lt;accounts.AccountRelationship&gt; {
    try {
      const user &#x3D; extractUserMetadata(metadata);

      const createDto: CreateAccountRelationshipDto &#x3D; {
        accountId: request.accountId,
        relatedAccountId: request.relatedAccountId,
        relationshipType: request.relationshipType,
      };

      const relationship &#x3D;
        await this.accountRelationshipActionService.createAccountRelationship(
          user,
          createDto,
        );
      return AccountRelationshipResponseDto.fromEntity(
        relationship,
      ) as accounts.AccountRelationship;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME,
    &quot;UpdateAccountRelationship&quot;,
  )
  async updateAccountRelationship(
    request: accounts.UpdateAccountRelationshipRequest,
    metadata: Metadata,
  ): Promise&lt;accounts.AccountRelationship&gt; {
    try {
      const user &#x3D; extractUserMetadata(metadata);

      const updateDto: UpdateAccountRelationshipDto &#x3D; {
        relationshipType: request.relationshipType,
      };

      const relationship &#x3D;
        await this.accountRelationshipActionService.updateAccountRelationship(
          user,
          request.relationshipId,
          updateDto,
        );
      return AccountRelationshipResponseDto.fromEntity(
        relationship,
      ) as accounts.AccountRelationship;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME,
    &quot;DeleteAccountRelationship&quot;,
  )
  async deleteAccountRelationship(
    request: accounts.DeleteAccountRelationshipRequest,
    metadata: Metadata,
  ): Promise&lt;void&gt; {
    try {
      const user &#x3D; extractUserMetadata(metadata);
      await this.accountRelationshipActionService.deleteAccountRelationship(
        user,
        request.relationshipId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }
}
</code></pre>
    </div>
</div>
















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'controller';
            var COMPODOC_CURRENT_PAGE_URL = 'AccountRelationshipsGrpcController.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
