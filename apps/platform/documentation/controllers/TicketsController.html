<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content controller">
                   <div class="content-data">





<ol class="breadcrumb">
  <li class="breadcrumb-item">Controllers</li>
  <li class="breadcrumb-item" >TicketsController</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/tickets/controllers/tickets.controller.ts</code>
        </p>

            <p class="comment">
                <h3>Prefix</h3>
            </p>
            <p class="comment">
                <code>v1/tickets</code>
            </p>






            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#archiveTicket" >archiveTicket</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#assignTicket" >assignTicket</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                <a href="#attachFileToTicket" >attachFileToTicket</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#commentOnTicket" class="deprecated-name">commentOnTicket</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#createTicket" >createTicket</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#deleteTicket" >deleteTicket</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#escalateTicket" >escalateTicket</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                <a href="#getAttachment" >getAttachment</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getCommentsForATicket" class="deprecated-name">getCommentsForATicket</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getCommentsForATicketByUserType" class="deprecated-name">getCommentsForATicketByUserType</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getTicket" >getTicket</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                <a href="#getTicketFieldTypes" >getTicketFieldTypes</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getTicketRelated" >getTicketRelated</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getTickets" >getTickets</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getTimeLogsForTicket" >getTimeLogsForTicket</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#linkTickets" >linkTickets</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#logTimeForTicket" >logTimeForTicket</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#markDuplicate" >markDuplicate</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#markOrCreateSubTicket" >markOrCreateSubTicket</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#reassignTeamToTicket" >reassignTeamToTicket</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#updateTicket" >updateTicket</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#validateAndFetchTeam" >validateAndFetchTeam</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="archiveTicket"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>archiveTicket</b></span>
                        <a href="#archiveTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>archiveTicket(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Patch(&#x27;/:id/archive&#x27;)<br />@ApiResponseMessage(&#x27;Ticket archived successfully!&#x27;)<br />@ApiUpdateEndpoint({summary: &#x27;Archive a ticket&#x27;, responseType: CommonTicketResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="458"
                                    class="link-to-prism">src/tickets/controllers/tickets.controller.ts:458</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/TicketResponseDto.html" target="_self" >Promise&lt;TicketResponseDto&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="assignTicket"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>assignTicket</b></span>
                        <a href="#assignTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>assignTicket(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, query: <a href="../interfaces/AssignTicketQuery.html" target="_self">AssignTicketQuery</a>, body: <a href="../classes/AssignTicketBody.html" target="_self">AssignTicketBody</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Patch(&#x27;/:id/assign&#x27;)<br />@ApiResponseMessage(&#x27;Ticket assigned successfully!&#x27;)<br />@ApiUpdateEndpoint({summary: &#x27;Assign a ticket to an agent&#x27;, responseType: CommonTicketResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="245"
                                    class="link-to-prism">src/tickets/controllers/tickets.controller.ts:245</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>query</td>
                                            <td>
                                                            <code><a href="../interfaces/AssignTicketQuery.html" target="_self" >AssignTicketQuery</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>body</td>
                                            <td>
                                                            <code><a href="../classes/AssignTicketBody.html" target="_self" >AssignTicketBody</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/TicketResponseDto.html" target="_self" >Promise&lt;TicketResponseDto&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="attachFileToTicket"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                        <span ><b>attachFileToTicket</b></span>
                        <a href="#attachFileToTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>attachFileToTicket(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, request: <a href="../interfaces/FastifyRequest.html" target="_self">FastifyRequest</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Post(&#x27;/:id/attachments&#x27;)<br />@ApiResponseMessage(&#x27;Attachments attached successfully!&#x27;)<br />@ApiCreateEndpoint({summary: &#x27;Attach a file to a ticket&#x27;, responseType: CommonTicketResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="527"
                                    class="link-to-prism">src/tickets/controllers/tickets.controller.ts:527</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../interfaces/FastifyRequest.html" target="_self" >FastifyRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="commentOnTicket"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span class="deprecated-name"><b>commentOnTicket</b></span>
                        <a href="#commentOnTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4 deprecated">
                    
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>commentOnTicket(currentUser: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, body: <a href="../classes/CreateCommentDto.html" target="_self">CreateCommentDto</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Post(&#x27;/:id/comment&#x27;)<br />@ApiResponseMessage(&#x27;Comment created successfully!&#x27;)<br />@ApiCreateEndpoint({summary: &#x27;Comment on a ticket&#x27;, responseType: CommonCommentResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="270"
                                    class="link-to-prism">src/tickets/controllers/tickets.controller.ts:270</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>currentUser</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>body</td>
                                            <td>
                                                            <code><a href="../classes/CreateCommentDto.html" target="_self" >CreateCommentDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createTicket"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>createTicket</b></span>
                        <a href="#createTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createTicket(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, body: <a href="../classes/CreateTicketBody.html" target="_self">CreateTicketBody</a>, source: RequestSource)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Post()<br />@ApiResponseMessage(&#x27;Ticket created successfully!&#x27;)<br />@ApiCreateEndpoint({summary: &#x27;Create a ticket&#x27;, responseType: CommonTicketResponse, operationId: &#x27;create&#x27;})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="177"
                                    class="link-to-prism">src/tickets/controllers/tickets.controller.ts:177</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>body</td>
                                            <td>
                                                            <code><a href="../classes/CreateTicketBody.html" target="_self" >CreateTicketBody</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>source</td>
                                            <td>
                                                        <code>RequestSource</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteTicket"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>deleteTicket</b></span>
                        <a href="#deleteTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteTicket(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Delete(&#x27;/:id&#x27;)<br />@ApiResponseMessage(&#x27;Ticket deleted successfully!&#x27;)<br />@ApiDeleteEndpoint({summary: &#x27;Delete a ticket&#x27;, responseType: CommonTicketResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="514"
                                    class="link-to-prism">src/tickets/controllers/tickets.controller.ts:514</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="escalateTicket"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>escalateTicket</b></span>
                        <a href="#escalateTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>escalateTicket(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, body: <a href="../classes/EscalateTicketBody.html" target="_self">EscalateTicketBody</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Patch(&#x27;/:id/escalate&#x27;)<br />@ApiResponseMessage(&#x27;Ticket escalated successfully!&#x27;)<br />@ApiUpdateEndpoint({summary: &#x27;Escalate a ticket&#x27;, responseType: CommonTicketResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="224"
                                    class="link-to-prism">src/tickets/controllers/tickets.controller.ts:224</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>body</td>
                                            <td>
                                                            <code><a href="../classes/EscalateTicketBody.html" target="_self" >EscalateTicketBody</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getAttachment"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                        <span ><b>getAttachment</b></span>
                        <a href="#getAttachment"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getAttachment(request: <a href="../interfaces/FastifyRequest.html" target="_self">FastifyRequest</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@HttpCode(HttpStatus.OK)<br />@ApiResponseMessage(&#x27;Attachments fetched successfully!&#x27;)<br />@Get(&#x27;/:id/attachments&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="545"
                                    class="link-to-prism">src/tickets/controllers/tickets.controller.ts:545</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../interfaces/FastifyRequest.html" target="_self" >FastifyRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getCommentsForATicket"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span class="deprecated-name"><b>getCommentsForATicket</b></span>
                        <a href="#getCommentsForATicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4 deprecated">
                    
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getCommentsForATicket(currentUser: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, query: <a href="../classes/GetCommentQuery.html" target="_self">GetCommentQuery</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get(&#x27;/:id/comments&#x27;)<br />@ApiResponseMessage(&#x27;Comments fetched successfully!&#x27;)<br />@ApiGetEndpoint({summary: &#x27;Get comments for a ticket&#x27;, responseType: GetAllCommentsResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="311"
                                    class="link-to-prism">src/tickets/controllers/tickets.controller.ts:311</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>currentUser</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>query</td>
                                            <td>
                                                            <code><a href="../classes/GetCommentQuery.html" target="_self" >GetCommentQuery</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getCommentsForATicketByUserType"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span class="deprecated-name"><b>getCommentsForATicketByUserType</b></span>
                        <a href="#getCommentsForATicketByUserType"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4 deprecated">
                    
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getCommentsForATicketByUserType(currentUser: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, query: <a href="../classes/GetCommentByUserTypeQuery.html" target="_self">GetCommentByUserTypeQuery</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get(&#x27;/:id/comments/user-type&#x27;)<br />@ApiResponseMessage(&#x27;Comments fetched successfully!&#x27;)<br />@ApiGetEndpoint({summary: &#x27;Get comments for a ticket by user type&#x27;, responseType: GetAllCommentsResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="348"
                                    class="link-to-prism">src/tickets/controllers/tickets.controller.ts:348</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>currentUser</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>query</td>
                                            <td>
                                                            <code><a href="../classes/GetCommentByUserTypeQuery.html" target="_self" >GetCommentByUserTypeQuery</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTicket"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getTicket</b></span>
                        <a href="#getTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTicket(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get(&#x27;/:id&#x27;)<br />@ApiResponseMessage(&#x27;Ticket fetched successfully!&#x27;)<br />@ApiGetEndpoint({summary: &#x27;Get a ticket&#x27;, responseType: CommonTicketResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="139"
                                    class="link-to-prism">src/tickets/controllers/tickets.controller.ts:139</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTicketFieldTypes"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                        <span ><b>getTicketFieldTypes</b></span>
                        <a href="#getTicketFieldTypes"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTicketFieldTypes(slaEnabled: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get(&#x27;ticket-types&#x27;)<br />@ApiResponseMessage(&#x27;Ticket types fetched successfully!&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="551"
                                    class="link-to-prism">src/tickets/controllers/tickets.controller.ts:551</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>slaEnabled</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>literal type</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTicketRelated"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getTicketRelated</b></span>
                        <a href="#getTicketRelated"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTicketRelated(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, query: <a href="../classes/GetTicketRelatedQuery.html" target="_self">GetTicketRelatedQuery</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get(&#x27;/:id/related&#x27;)<br />@ApiResponseMessage(&#x27;Ticket types fetched successfully!&#x27;)<br />@ApiGetEndpoint({summary: &#x27;Get ticket related&#x27;, responseType: GetAllTicketsResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="154"
                                    class="link-to-prism">src/tickets/controllers/tickets.controller.ts:154</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>query</td>
                                            <td>
                                                            <code><a href="../classes/GetTicketRelatedQuery.html" target="_self" >GetTicketRelatedQuery</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTickets"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getTickets</b></span>
                        <a href="#getTickets"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTickets(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, query: <a href="../classes/GetTicketQuery.html" target="_self">GetTicketQuery</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get()<br />@ApiResponseMessage(&#x27;Tickets fetched successfully!&#x27;)<br />@ApiGetEndpoint({summary: &#x27;Get all tickets&#x27;, responseType: GetAllTicketsResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="111"
                                    class="link-to-prism">src/tickets/controllers/tickets.controller.ts:111</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>query</td>
                                            <td>
                                                            <code><a href="../classes/GetTicketQuery.html" target="_self" >GetTicketQuery</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTimeLogsForTicket"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getTimeLogsForTicket</b></span>
                        <a href="#getTimeLogsForTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTimeLogsForTicket(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get(&#x27;/:id/time-logs&#x27;)<br />@ApiResponseMessage(&#x27;Ticket time logs fetched successfully!&#x27;)<br />@ApiGetEndpoint({summary: &#x27;Get time logs for a ticket&#x27;, responseType: GetAllTicketTimeLogsResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="496"
                                    class="link-to-prism">src/tickets/controllers/tickets.controller.ts:496</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="linkTickets"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>linkTickets</b></span>
                        <a href="#linkTickets"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>linkTickets(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, body: <a href="../classes/LinkTicketsBody.html" target="_self">LinkTicketsBody</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Patch(&#x27;/link&#x27;)<br />@ApiResponseMessage(&#x27;Tickets linked successfully!&#x27;)<br />@ApiUpdateEndpoint({summary: &#x27;Link tickets&#x27;, responseType: CommonTicketResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="418"
                                    class="link-to-prism">src/tickets/controllers/tickets.controller.ts:418</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>body</td>
                                            <td>
                                                            <code><a href="../classes/LinkTicketsBody.html" target="_self" >LinkTicketsBody</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="logTimeForTicket"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>logTimeForTicket</b></span>
                        <a href="#logTimeForTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>logTimeForTicket(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, body: <a href="../classes/TicketTimeLogDto.html" target="_self">TicketTimeLogDto</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Patch(&#x27;/:id/log&#x27;)<br />@ApiResponseMessage(&#x27;Ticket time logged successfully!&#x27;)<br />@ApiUpdateEndpoint({summary: &#x27;Log time for a ticket&#x27;, responseType: CommonTicketTimeLogResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="472"
                                    class="link-to-prism">src/tickets/controllers/tickets.controller.ts:472</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>body</td>
                                            <td>
                                                            <code><a href="../classes/TicketTimeLogDto.html" target="_self" >TicketTimeLogDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="markDuplicate"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>markDuplicate</b></span>
                        <a href="#markDuplicate"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>markDuplicate(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, body: <a href="../classes/MarkDuplicateBody.html" target="_self">MarkDuplicateBody</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Patch(&#x27;/mark-duplicate&#x27;)<br />@ApiResponseMessage(&#x27;Ticket marked as duplicate successfully!&#x27;)<br />@ApiUpdateEndpoint({summary: &#x27;Mark a ticket as duplicate&#x27;, responseType: CommonTicketResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="400"
                                    class="link-to-prism">src/tickets/controllers/tickets.controller.ts:400</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>body</td>
                                            <td>
                                                            <code><a href="../classes/MarkDuplicateBody.html" target="_self" >MarkDuplicateBody</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="markOrCreateSubTicket"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>markOrCreateSubTicket</b></span>
                        <a href="#markOrCreateSubTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>markOrCreateSubTicket(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, body: <a href="../classes/MarkOrCreateSubTicketBody.html" target="_self">MarkOrCreateSubTicketBody</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Patch(&#x27;/sub-ticket&#x27;)<br />@ApiResponseMessage(&#x27;Ticket marked as sub-ticket successfully!&#x27;)<br />@ApiUpdateEndpoint({summary: &#x27;Mark a ticket as sub-ticket&#x27;, responseType: CommonTicketResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="382"
                                    class="link-to-prism">src/tickets/controllers/tickets.controller.ts:382</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>body</td>
                                            <td>
                                                            <code><a href="../classes/MarkOrCreateSubTicketBody.html" target="_self" >MarkOrCreateSubTicketBody</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="reassignTeamToTicket"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>reassignTeamToTicket</b></span>
                        <a href="#reassignTeamToTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>reassignTeamToTicket(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, body: <a href="../classes/AssignTeamToTicketBody.html" target="_self">AssignTeamToTicketBody</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Patch(&#x27;/:id/reassign-team&#x27;)<br />@ApiResponseMessage(&#x27;Ticket reassigned successfully!&#x27;)<br />@ApiUpdateEndpoint({summary: &#x27;Reassign a ticket to a team&#x27;, responseType: CommonTicketResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="433"
                                    class="link-to-prism">src/tickets/controllers/tickets.controller.ts:433</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>body</td>
                                            <td>
                                                            <code><a href="../classes/AssignTeamToTicketBody.html" target="_self" >AssignTeamToTicketBody</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/TicketResponseDto.html" target="_self" >Promise&lt;TicketResponseDto&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateTicket"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>updateTicket</b></span>
                        <a href="#updateTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateTicket(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, body: <a href="../classes/UpdateTicketBody.html" target="_self">UpdateTicketBody</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Patch(&#x27;/:id&#x27;)<br />@ApiResponseMessage(&#x27;Ticket updated successfully!&#x27;)<br />@ApiUpdateEndpoint({summary: &#x27;Update a ticket&#x27;, responseType: CommonTicketResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="200"
                                    class="link-to-prism">src/tickets/controllers/tickets.controller.ts:200</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>body</td>
                                            <td>
                                                            <code><a href="../classes/UpdateTicketBody.html" target="_self" >UpdateTicketBody</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/TicketResponseDto.html" target="_self" >Promise&lt;TicketResponseDto&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateAndFetchTeam"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>validateAndFetchTeam</b></span>
                        <a href="#validateAndFetchTeam"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateAndFetchTeam(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="92"
                                    class="link-to-prism">src/tickets/controllers/tickets.controller.ts:92</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Validates and fetches a team by its ID.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the team to fetch.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The team.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  forwardRef,
  Get,
  HttpCode,
  HttpStatus,
  Inject,
  InternalServerErrorException,
  NotFoundException,
  Param,
  Patch,
  Post,
  Query,
  Req,
  UseInterceptors,
} from &quot;@nestjs/common&quot;;
import { ApiTags } from &quot;@nestjs/swagger&quot;;
import {
  ApiCreateEndpoint,
  ApiDeleteEndpoint,
  ApiGetEndpoint,
  ApiResponseMessage,
  ApiUpdateEndpoint,
  RequestSourceType,
  ResponseTransformInterceptor,
} from &quot;@repo/nestjs-commons/decorators&quot;;
import { CUSTOM_LOGGER_TOKEN, ILogger } from &quot;@repo/nestjs-commons/logger&quot;;
import { RequestSource } from &quot;@repo/nestjs-commons/middlewares&quot;;
import { CommentEntityTypes, Team } from &quot;@repo/thena-platform-entities&quot;;
import type { FastifyRequest } from &quot;fastify&quot;;
import { CurrentUser } from &quot;../../common/decorators&quot;;
import { SkipAllThrottler } from &quot;../../common/decorators/throttler.decorator&quot;;
import { CreateCommentDto } from &quot;../../common/dto/comments.dto&quot;;
import {
  CommonCommentResponse,
  GetAllCommentsResponse,
} from &quot;../../communications/dto&quot;;
import {
  GetCommentByUserTypeQuery,
  GetCommentQuery,
} from &quot;../../communications/dto/comment.queries&quot;;
import { CommunicationsService } from &quot;../../communications/services/communications.service&quot;;
import { CommentResponseDto } from &quot;../../communications/transformers/comment-response.transformer&quot;;
import { TeamsService } from &quot;../../teams/services/teams.service&quot;;
import {
  AssignTeamToTicketBody,
  AssignTicketBody,
  AssignTicketQuery,
  CommonTicketResponse,
  CommonTicketTimeLogResponse,
  CreateTicketBody,
  EscalateTicketBody,
  GetAllTicketsResponse,
  GetAllTicketTimeLogsResponse,
  GetTicketQuery,
  GetTicketRelatedQuery,
  LinkTicketsBody,
  MarkDuplicateBody,
  MarkOrCreateSubTicketBody,
  TicketTimeLogDto,
  UpdateTicketBody,
} from &quot;../dto&quot;;
import { TicketAnnotatorService } from &quot;../services/ticket-annotator.service&quot;;
import { TicketsService } from &quot;../services/tickets.service&quot;;
import { TicketResponseDto } from &quot;../transformer/ticket-response.dto&quot;;
import { TicketTimeLogResponseDto } from &quot;../transformer/ticket-time-log.dto&quot;;

@ApiTags(&quot;Tickets&quot;)
@Controller(&quot;v1/tickets&quot;)
@SkipAllThrottler()
@UseInterceptors(ResponseTransformInterceptor)
export class TicketsController {
  private readonly logSpanId &#x3D; &quot;TicketsController&quot;;

  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly ticketsService: TicketsService,
    private readonly teamsService: TeamsService,
    @Inject(forwardRef(() &#x3D;&gt; CommunicationsService))
    private readonly communicationsService: CommunicationsService,
    private readonly ticketMetadataService: TicketAnnotatorService,
  ) {}

  /**
   * Validates and fetches a team by its ID.
   * @param teamId The ID of the team to fetch.
   * @returns The team.
   */
  private async validateAndFetchTeam(teamId: string, organizationId: string) {
    const team &#x3D; await this.teamsService.findOneByTeamId(
      teamId,
      organizationId,
    );

    if (!team) {
      throw new NotFoundException(&quot;Team not found!&quot;);
    }

    return team;
  }

  @Get()
  @ApiResponseMessage(&quot;Tickets fetched successfully!&quot;)
  @ApiGetEndpoint({
    summary: &quot;Get all tickets&quot;,
    responseType: GetAllTicketsResponse,
  })
  async getTickets(
    @CurrentUser() user: CurrentUser,
    @Query() query: GetTicketQuery,
  ) {
    // Validate the team ID
    let team: Team | null &#x3D; null;
    if (query.teamId) {
      team &#x3D; await this.validateAndFetchTeam(query.teamId, user.orgId);
    }

    // Fetch the tickets
    const { results: tickets } &#x3D; await this.ticketsService.getTickets(
      user,
      team,
      query,
    );

    // Format the results
    const formattedResults &#x3D; tickets.map(TicketResponseDto.fromEntity);
    return formattedResults;
  }

  @Get(&quot;/:id&quot;)
  @ApiResponseMessage(&quot;Ticket fetched successfully!&quot;)
  @ApiGetEndpoint({
    summary: &quot;Get a ticket&quot;,
    responseType: CommonTicketResponse,
  })
  async getTicket(@CurrentUser() user: CurrentUser, @Param(&quot;id&quot;) id: string) {
    if (!id) throw new BadRequestException(&quot;Ticket ID is required!&quot;);

    const ticket &#x3D; await this.ticketsService.getTicketById(id, user.orgId);
    if (!ticket) throw new NotFoundException(&quot;Ticket not found!&quot;);

    return TicketResponseDto.fromEntity(ticket);
  }

  @Get(&quot;/:id/related&quot;)
  @ApiResponseMessage(&quot;Ticket types fetched successfully!&quot;)
  @ApiGetEndpoint({
    summary: &quot;Get ticket related&quot;,
    responseType: GetAllTicketsResponse,
  })
  async getTicketRelated(
    @CurrentUser() user: CurrentUser,
    @Param(&quot;id&quot;) id: string,
    @Query() query: GetTicketRelatedQuery,
  ) {
    const { results: relatedTickets } &#x3D;
      await this.ticketsService.getTicketRelated(user, id, query);

    // Format the results
    const formattedResults &#x3D; relatedTickets.map((ticket) &#x3D;&gt;
      TicketResponseDto.fromEntity(ticket.targetTicket),
    );

    return formattedResults;
  }

  @Post()
  @ApiResponseMessage(&quot;Ticket created successfully!&quot;)
  @ApiCreateEndpoint({
    summary: &quot;Create a ticket&quot;,
    responseType: CommonTicketResponse,
    operationId: &quot;create&quot;,
  })
  async createTicket(
    @CurrentUser() user: CurrentUser,
    @Body() body: CreateTicketBody,
    @RequestSourceType() source: RequestSource,
  ) {
    console.log(&#x60;Request came from: ${source}&#x60;);
    const team &#x3D; await this.validateAndFetchTeam(body.teamId, user.orgId);
    const createdTicket &#x3D; await this.ticketsService.createTicket(
      user,
      team,
      body,
      source,
    );

    return TicketResponseDto.fromEntity(createdTicket);
  }

  @Patch(&quot;/:id&quot;)
  @ApiResponseMessage(&quot;Ticket updated successfully!&quot;)
  @ApiUpdateEndpoint({
    summary: &quot;Update a ticket&quot;,
    responseType: CommonTicketResponse,
  })
  async updateTicket(
    @CurrentUser() user: CurrentUser,
    @Param(&quot;id&quot;) id: string,
    @Body() body: UpdateTicketBody,
  ): Promise&lt;TicketResponseDto&gt; {
    // If the ticket ID is not provided, throw an error
    if (!id) throw new BadRequestException(&quot;Ticket ID is required!&quot;);

    // Update the ticket
    const updatedTicket &#x3D; await this.ticketsService.updateTicket(
      user,
      id,
      body,
    );

    return TicketResponseDto.fromEntity(updatedTicket);
  }

  @Patch(&quot;/:id/escalate&quot;)
  @ApiResponseMessage(&quot;Ticket escalated successfully!&quot;)
  @ApiUpdateEndpoint({
    summary: &quot;Escalate a ticket&quot;,
    responseType: CommonTicketResponse,
  })
  async escalateTicket(
    @CurrentUser() user: CurrentUser,
    @Param(&quot;id&quot;) id: string,
    @Body() body: EscalateTicketBody,
  ) {
    if (!id) throw new BadRequestException(&quot;Ticket ID is required!&quot;);

    const escalatedTicket &#x3D; await this.ticketsService.escalateTicket(
      user,
      id,
      body,
    );
    return TicketResponseDto.fromEntity(escalatedTicket);
  }

  @Patch(&quot;/:id/assign&quot;)
  @ApiResponseMessage(&quot;Ticket assigned successfully!&quot;)
  @ApiUpdateEndpoint({
    summary: &quot;Assign a ticket to an agent&quot;,
    responseType: CommonTicketResponse,
  })
  async assignTicket(
    @CurrentUser() user: CurrentUser,
    @Param(&quot;id&quot;) id: string,
    @Query() query: AssignTicketQuery,
    @Body() body: AssignTicketBody,
  ): Promise&lt;TicketResponseDto&gt; {
    const assignedTicket &#x3D; await this.ticketsService.assignTicket(
      user,
      id,
      body,
      query,
    );

    return TicketResponseDto.fromEntity(assignedTicket);
  }

  /**
   * @deprecated
   */
  @Post(&quot;/:id/comment&quot;)
  @ApiResponseMessage(&quot;Comment created successfully!&quot;)
  @ApiCreateEndpoint({
    summary: &quot;Comment on a ticket&quot;,
    responseType: CommonCommentResponse,
  })
  async commentOnTicket(
    @CurrentUser() currentUser: CurrentUser,
    @Param(&quot;id&quot;) id: string,
    @Body() body: CreateCommentDto,
  ) {
    // If the ticket ID is not provided, throw an error
    if (id &#x3D;&#x3D;&#x3D; undefined) {
      throw new BadRequestException(&quot;Ticket ID is required!&quot;);
    }

    // Fetch the ticket
    const ticket &#x3D; await this.ticketsService.getTicketById(
      id,
      currentUser.orgId,
    );

    // If the ticket is not found, throw an error
    if (!ticket) {
      throw new NotFoundException(&quot;Ticket not found!&quot;);
    }

    // Create the comment
    const comment &#x3D; await this.communicationsService.createCommentOnAnEntity(
      ticket,
      CommentEntityTypes.TICKET,
      body,
      currentUser,
    );

    return CommentResponseDto.fromEntity(comment);
  }

  /**
   * @deprecated
   */
  @Get(&quot;/:id/comments&quot;)
  @ApiResponseMessage(&quot;Comments fetched successfully!&quot;)
  @ApiGetEndpoint({
    summary: &quot;Get comments for a ticket&quot;,
    responseType: GetAllCommentsResponse,
  })
  async getCommentsForATicket(
    @CurrentUser() currentUser: CurrentUser,
    @Param(&quot;id&quot;) id: string,
    @Query() query: GetCommentQuery,
  ) {
    // Fetch the ticket
    const ticket &#x3D; await this.ticketsService.getTicketById(
      id,
      currentUser.orgId,
    );

    // If the ticket is not found, throw an error
    if (!ticket) {
      throw new NotFoundException(&quot;Ticket not found!&quot;);
    }

    // Get the comments
    const { results: comments } &#x3D;
      await this.communicationsService.getCommentsForAnEntity(
        ticket,
        CommentEntityTypes.TICKET,
        query,
        currentUser,
      );

    return comments.map(CommentResponseDto.fromEntity);
  }

  /**
   * @deprecated
   */
  @Get(&quot;/:id/comments/user-type&quot;)
  @ApiResponseMessage(&quot;Comments fetched successfully!&quot;)
  @ApiGetEndpoint({
    summary: &quot;Get comments for a ticket by user type&quot;,
    responseType: GetAllCommentsResponse,
  })
  async getCommentsForATicketByUserType(
    @CurrentUser() currentUser: CurrentUser,
    @Param(&quot;id&quot;) id: string,
    @Query() query: GetCommentByUserTypeQuery,
  ) {
    // Fetch the ticket
    const ticket &#x3D; await this.ticketsService.getTicketById(
      id,
      currentUser.orgId,
    );

    // If the ticket is not found, throw an error
    if (!ticket) {
      throw new NotFoundException(&quot;Ticket not found!&quot;);
    }

    // Get the comments
    const comment &#x3D;
      await this.communicationsService.getCommentForAnEntityByUserType(
        ticket,
        CommentEntityTypes.TICKET,
        query,
        currentUser,
      );

    return comment ? CommentResponseDto.fromEntity(comment) : null;
  }

  @Patch(&quot;/sub-ticket&quot;)
  @ApiResponseMessage(&quot;Ticket marked as sub-ticket successfully!&quot;)
  @ApiUpdateEndpoint({
    summary: &quot;Mark a ticket as sub-ticket&quot;,
    responseType: CommonTicketResponse,
  })
  async markOrCreateSubTicket(
    @CurrentUser() user: CurrentUser,
    @Body() body: MarkOrCreateSubTicketBody,
  ) {
    const markedSubTicket &#x3D; await this.ticketsService.markOrCreateSubTicket(
      user,
      body,
    );

    return TicketResponseDto.fromEntity(markedSubTicket);
  }

  @Patch(&quot;/mark-duplicate&quot;)
  @ApiResponseMessage(&quot;Ticket marked as duplicate successfully!&quot;)
  @ApiUpdateEndpoint({
    summary: &quot;Mark a ticket as duplicate&quot;,
    responseType: CommonTicketResponse,
  })
  async markDuplicate(
    @CurrentUser() user: CurrentUser,
    @Body() body: MarkDuplicateBody,
  ) {
    const markedDuplicateTicket &#x3D; await this.ticketsService.markDuplicateTicket(
      user,
      body,
    );

    return TicketResponseDto.fromEntity(markedDuplicateTicket);
  }

  @Patch(&quot;/link&quot;)
  @ApiResponseMessage(&quot;Tickets linked successfully!&quot;)
  @ApiUpdateEndpoint({
    summary: &quot;Link tickets&quot;,
    responseType: CommonTicketResponse,
  })
  async linkTickets(
    @CurrentUser() user: CurrentUser,
    @Body() body: LinkTicketsBody,
  ) {
    const linkedTicket &#x3D; await this.ticketsService.linkTickets(user, body);

    return TicketResponseDto.fromEntity(linkedTicket);
  }

  @Patch(&quot;/:id/reassign-team&quot;)
  @ApiResponseMessage(&quot;Ticket reassigned successfully!&quot;)
  @ApiUpdateEndpoint({
    summary: &quot;Reassign a ticket to a team&quot;,
    responseType: CommonTicketResponse,
  })
  async reassignTeamToTicket(
    @CurrentUser() user: CurrentUser,
    @Param(&quot;id&quot;) id: string,
    @Body() body: AssignTeamToTicketBody,
  ): Promise&lt;TicketResponseDto&gt; {
    const team &#x3D; await this.validateAndFetchTeam(body.teamId, user.orgId);
    if (!team) {
      throw new NotFoundException(&quot;Team not found!&quot;);
    }

    const reassignedTicket &#x3D; await this.ticketsService.reassignTeamToTicket(
      user,
      team,
      id,
    );

    return TicketResponseDto.fromEntity(reassignedTicket);
  }

  @Patch(&quot;/:id/archive&quot;)
  @ApiResponseMessage(&quot;Ticket archived successfully!&quot;)
  @ApiUpdateEndpoint({
    summary: &quot;Archive a ticket&quot;,
    responseType: CommonTicketResponse,
  })
  async archiveTicket(
    @CurrentUser() user: CurrentUser,
    @Param(&quot;id&quot;) id: string,
  ): Promise&lt;TicketResponseDto&gt; {
    const archivedTicket &#x3D; await this.ticketsService.archiveTicket(user, id);
    return TicketResponseDto.fromEntity(archivedTicket);
  }

  @Patch(&quot;/:id/log&quot;)
  @ApiResponseMessage(&quot;Ticket time logged successfully!&quot;)
  @ApiUpdateEndpoint({
    summary: &quot;Log time for a ticket&quot;,
    responseType: CommonTicketTimeLogResponse,
  })
  async logTimeForTicket(
    @CurrentUser() user: CurrentUser,
    @Param(&quot;id&quot;) id: string,
    @Body() body: TicketTimeLogDto,
  ) {
    // If the ticket ID is not provided, throw an error
    if (!id) throw new BadRequestException(&quot;Ticket ID is required!&quot;);

    // Log the time for the ticket
    const loggedTimeLog &#x3D; await this.ticketsService.logTimeForTicket(
      user,
      id,
      body,
    );

    return TicketTimeLogResponseDto.fromEntity(loggedTimeLog);
  }

  @Get(&quot;/:id/time-logs&quot;)
  @ApiResponseMessage(&quot;Ticket time logs fetched successfully!&quot;)
  @ApiGetEndpoint({
    summary: &quot;Get time logs for a ticket&quot;,
    responseType: GetAllTicketTimeLogsResponse,
  })
  async getTimeLogsForTicket(
    @CurrentUser() user: CurrentUser,
    @Param(&quot;id&quot;) id: string,
  ) {
    // If the ticket ID is not provided, throw an error
    if (!id) throw new BadRequestException(&quot;Ticket ID is required!&quot;);

    // Get the time logs for the ticket
    const timeLogs &#x3D; await this.ticketsService.getTimeLogsForTicket(user, id);
    return timeLogs.map(TicketTimeLogResponseDto.fromEntity);
  }

  @Delete(&quot;/:id&quot;)
  @ApiResponseMessage(&quot;Ticket deleted successfully!&quot;)
  @ApiDeleteEndpoint({
    summary: &quot;Delete a ticket&quot;,
    responseType: CommonTicketResponse,
  })
  async deleteTicket(
    @CurrentUser() user: CurrentUser,
    @Param(&quot;id&quot;) id: string,
  ) {
    await this.ticketsService.deleteTicket(user, id);
  }

  @Post(&quot;/:id/attachments&quot;)
  @ApiResponseMessage(&quot;Attachments attached successfully!&quot;)
  @ApiCreateEndpoint({
    summary: &quot;Attach a file to a ticket&quot;,
    responseType: CommonTicketResponse,
  })
  attachFileToTicket(
    @CurrentUser() user: CurrentUser,
    @Req() request: FastifyRequest,
    @Param(&quot;id&quot;) id: string,
  ) {
    try {
      return this.ticketsService.attachFileToTicket(request, id, user);
    } catch (error) {
      this.logger.error(
        &#x60;${this.logSpanId} Error encountered while attaching file to ticket: ${error?.message}&#x60;,
      );
      throw new InternalServerErrorException(&quot;Something went wrong!&quot;);
    }
  }

  @HttpCode(HttpStatus.OK)
  @ApiResponseMessage(&quot;Attachments fetched successfully!&quot;)
  @Get(&quot;/:id/attachments&quot;)
  getAttachment(@Req() request: FastifyRequest, @Param(&quot;id&quot;) id: string) {
    return this.ticketsService.getAttachments(request, id);
  }

  @Get(&quot;ticket-types&quot;)
  @ApiResponseMessage(&quot;Ticket types fetched successfully!&quot;)
  getTicketFieldTypes(@Query(&quot;slaEnabled&quot;) slaEnabled: string): {
    success: boolean;
    data: any;
  } {
    const isSlaEnabled &#x3D; slaEnabled &#x3D;&#x3D;&#x3D; &quot;true&quot;;
    const data &#x3D; this.ticketMetadataService.getEntityFieldTypes(isSlaEnabled);
    return {
      success: true,
      data,
    };
  }
}
</code></pre>
    </div>
</div>
















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'controller';
            var COMPODOC_CURRENT_PAGE_URL = 'TicketsController.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
