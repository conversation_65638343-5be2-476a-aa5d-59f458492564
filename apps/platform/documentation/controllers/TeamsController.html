<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content controller">
                   <div class="content-data">





<ol class="breadcrumb">
  <li class="breadcrumb-item">Controllers</li>
  <li class="breadcrumb-item" >TeamsController</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/teams/controllers/teams.controller.ts</code>
        </p>

            <p class="comment">
                <h3>Prefix</h3>
            </p>
            <p class="comment">
                <code>v1/teams</code>
            </p>






            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#addTeamMember" >addTeamMember</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#createRoutingRule" >createRoutingRule</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#createTeam" >createTeam</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#deleteRoutingRule" >deleteRoutingRule</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#deleteTeam" >deleteTeam</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getAllTeams" >getAllTeams</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getPublicTeams" >getPublicTeams</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getSubTeams" >getSubTeams</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getTeamById" >getTeamById</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getTeamConfigurations" >getTeamConfigurations</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getTeamMembers" >getTeamMembers</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getTeamRouting" >getTeamRouting</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#removeTeamMember" >removeTeamMember</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#updateRoutingRule" >updateRoutingRule</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#updateTeam" >updateTeam</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#updateTeamConfigurations" >updateTeamConfigurations</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="addTeamMember"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>addTeamMember</b></span>
                        <a href="#addTeamMember"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>addTeamMember(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, addTeamMemberDto: <a href="../classes/AddTeamMemberDto.html" target="_self">AddTeamMemberDto</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Post(&#x27;/:teamId/members&#x27;)<br />@ThrottleTierFour()<br />@ApiResponseMessage(&#x27;Team member added successfully!&#x27;)<br />@ApiCreateEndpoint({summary: &#x27;Add a team member&#x27;, responseType: TeamMemberResponseDto})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="407"
                                    class="link-to-prism">src/teams/controllers/teams.controller.ts:407</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>addTeamMemberDto</td>
                                            <td>
                                                            <code><a href="../classes/AddTeamMemberDto.html" target="_self" >AddTeamMemberDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createRoutingRule"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>createRoutingRule</b></span>
                        <a href="#createRoutingRule"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createRoutingRule(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, createRoutingRuleDto: <a href="../classes/CreateRoutingRuleGroupDto.html" target="_self">CreateRoutingRuleGroupDto</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Post(&#x27;/:teamId/routing&#x27;)<br />@ThrottleTierFour()<br />@ApiResponseMessage(&#x27;Team routing updated successfully!&#x27;)<br />@ApiCreateEndpoint({summary: &#x27;Create a team routing rule&#x27;, responseType: CommonTeamRoutingRuleResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="585"
                                    class="link-to-prism">src/teams/controllers/teams.controller.ts:585</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>createRoutingRuleDto</td>
                                            <td>
                                                            <code><a href="../classes/CreateRoutingRuleGroupDto.html" target="_self" >CreateRoutingRuleGroupDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createTeam"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>createTeam</b></span>
                        <a href="#createTeam"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createTeam(createTeamDto: <a href="../classes/CreateTeamDto.html" target="_self">CreateTeamDto</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Post()<br />@ThrottleTierFour()<br />@ApiResponseMessage(&#x27;Team created successfully!&#x27;)<br />@ApiCreateEndpoint({summary: &#x27;Create a team&#x27;, responseType: CommonTeamResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="116"
                                    class="link-to-prism">src/teams/controllers/teams.controller.ts:116</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>createTeamDto</td>
                                            <td>
                                                            <code><a href="../classes/CreateTeamDto.html" target="_self" >CreateTeamDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteRoutingRule"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>deleteRoutingRule</b></span>
                        <a href="#deleteRoutingRule"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteRoutingRule(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, ruleId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Delete(&#x27;/:teamId/routing/:ruleId&#x27;)<br />@ThrottleTierFour()<br />@ApiResponseMessage(&#x27;Team routing deleted successfully!&#x27;)<br />@ApiDeleteEndpoint({summary: &#x27;Delete a team routing rule&#x27;})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="731"
                                    class="link-to-prism">src/teams/controllers/teams.controller.ts:731</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>ruleId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteTeam"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>deleteTeam</b></span>
                        <a href="#deleteTeam"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteTeam(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Delete(&#x27;/:teamId&#x27;)<br />@ThrottleTierFour()<br />@ApiResponseMessage(&#x27;Team deleted successfully!&#x27;)<br />@ApiDeleteEndpoint({summary: &#x27;Delete a team&#x27;})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="324"
                                    class="link-to-prism">src/teams/controllers/teams.controller.ts:324</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getAllTeams"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getAllTeams</b></span>
                        <a href="#getAllTeams"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getAllTeams(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get()<br />@ThrottleTierFour()<br />@ApiResponseMessage(&#x27;Teams fetched successfully!&#x27;)<br />@ApiGetEndpoint({summary: &#x27;Get all teams that user is the part of!&#x27;, responseType: GetAllTeamsResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="80"
                                    class="link-to-prism">src/teams/controllers/teams.controller.ts:80</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getPublicTeams"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getPublicTeams</b></span>
                        <a href="#getPublicTeams"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getPublicTeams(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get(&#x27;/public&#x27;)<br />@ThrottleTierFour()<br />@ApiResponseMessage(&#x27;Public teams fetched successfully!&#x27;)<br />@ApiGetEndpoint({summary: &#x27;Get all public teams&#x27;, responseType: GetAllTeamsResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="159"
                                    class="link-to-prism">src/teams/controllers/teams.controller.ts:159</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getSubTeams"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getSubTeams</b></span>
                        <a href="#getSubTeams"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getSubTeams(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get(&#x27;/:teamId/sub-teams&#x27;)<br />@ThrottleTierFour()<br />@ApiResponseMessage(&#x27;Sub-teams fetched successfully!&#x27;)<br />@ApiGetEndpoint({summary: &#x27;Get sub-teams for a team&#x27;, responseType: GetAllTeamsResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="234"
                                    class="link-to-prism">src/teams/controllers/teams.controller.ts:234</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTeamById"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getTeamById</b></span>
                        <a href="#getTeamById"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTeamById(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get(&#x27;/:teamId&#x27;)<br />@ThrottleTierFour()<br />@ApiResponseMessage(&#x27;Team fetched successfully!&#x27;)<br />@ApiGetEndpoint({summary: &#x27;Get a team by ID&#x27;, responseType: CommonTeamResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="195"
                                    class="link-to-prism">src/teams/controllers/teams.controller.ts:195</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTeamConfigurations"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getTeamConfigurations</b></span>
                        <a href="#getTeamConfigurations"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTeamConfigurations(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get(&#x27;/:teamId/configurations&#x27;)<br />@ThrottleTierFour()<br />@ApiResponseMessage(&#x27;Team configurations fetched successfully!&#x27;)<br />@ApiGetEndpoint({summary: &#x27;Get team configurations&#x27;, responseType: TeamConfigurationsResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="495"
                                    class="link-to-prism">src/teams/controllers/teams.controller.ts:495</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTeamMembers"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getTeamMembers</b></span>
                        <a href="#getTeamMembers"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTeamMembers(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get(&#x27;/:teamId/members&#x27;)<br />@ThrottleTierFour()<br />@ApiResponseMessage(&#x27;Team members fetched successfully!&#x27;)<br />@ApiGetEndpoint({summary: &#x27;Get all team members&#x27;, responseType: GetAllTeamMembersResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="362"
                                    class="link-to-prism">src/teams/controllers/teams.controller.ts:362</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTeamRouting"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getTeamRouting</b></span>
                        <a href="#getTeamRouting"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTeamRouting(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get(&#x27;/:teamId/routing&#x27;)<br />@ThrottleTierFour()<br />@ApiResponseMessage(&#x27;Team routing fetched successfully!&#x27;)<br />@ApiGetEndpoint({summary: &#x27;Get team routing&#x27;, responseType: GetAllTeamRoutingRulesResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="686"
                                    class="link-to-prism">src/teams/controllers/teams.controller.ts:686</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="removeTeamMember"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>removeTeamMember</b></span>
                        <a href="#removeTeamMember"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>removeTeamMember(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, memberId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Delete(&#x27;/:teamId/members/:memberId&#x27;)<br />@ThrottleTierFour()<br />@ApiResponseMessage(&#x27;Team member removed successfully!&#x27;)<br />@ApiDeleteEndpoint({summary: &#x27;Remove a team member&#x27;})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="456"
                                    class="link-to-prism">src/teams/controllers/teams.controller.ts:456</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>memberId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateRoutingRule"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>updateRoutingRule</b></span>
                        <a href="#updateRoutingRule"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateRoutingRule(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, ruleId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, updateRoutingRuleDto: <a href="../classes/UpdateRoutingRuleGroupDto.html" target="_self">UpdateRoutingRuleGroupDto</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Patch(&#x27;/:teamId/routing/:ruleId&#x27;)<br />@ThrottleTierFour()<br />@ApiResponseMessage(&#x27;Team routing updated successfully!&#x27;)<br />@ApiUpdateEndpoint({summary: &#x27;Update team routing&#x27;, responseType: CommonTeamRoutingRuleResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="635"
                                    class="link-to-prism">src/teams/controllers/teams.controller.ts:635</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>ruleId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>updateRoutingRuleDto</td>
                                            <td>
                                                            <code><a href="../classes/UpdateRoutingRuleGroupDto.html" target="_self" >UpdateRoutingRuleGroupDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateTeam"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>updateTeam</b></span>
                        <a href="#updateTeam"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateTeam(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, updateTeamDto: <a href="../classes/UpdateTeamDto.html" target="_self">UpdateTeamDto</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Patch(&#x27;/:teamId&#x27;)<br />@ThrottleTierFour()<br />@ApiResponseMessage(&#x27;Team updated successfully!&#x27;)<br />@ApiUpdateEndpoint({summary: &#x27;Update a team&#x27;, responseType: CommonTeamResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="277"
                                    class="link-to-prism">src/teams/controllers/teams.controller.ts:277</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>updateTeamDto</td>
                                            <td>
                                                            <code><a href="../classes/UpdateTeamDto.html" target="_self" >UpdateTeamDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateTeamConfigurations"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>updateTeamConfigurations</b></span>
                        <a href="#updateTeamConfigurations"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateTeamConfigurations(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, updateTeamConfigurationsDto: <a href="../classes/UpdateTimezoneWorkingHoursDto.html" target="_self">UpdateTimezoneWorkingHoursDto</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Patch(&#x27;/:teamId/configurations&#x27;)<br />@ThrottleTierFour()<br />@ApiResponseMessage(&#x27;Team configurations updated successfully!&#x27;)<br />@ApiUpdateEndpoint({summary: &#x27;Update team configurations&#x27;, responseType: CommonTeamConfigurationsResponse})<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="540"
                                    class="link-to-prism">src/teams/controllers/teams.controller.ts:540</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>updateTeamConfigurationsDto</td>
                                            <td>
                                                            <code><a href="../classes/UpdateTimezoneWorkingHoursDto.html" target="_self" >UpdateTimezoneWorkingHoursDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  Inject,
  InternalServerErrorException,
  Param,
  Patch,
  Post,
  UseInterceptors,
} from &quot;@nestjs/common&quot;;
import { ApiTags } from &quot;@nestjs/swagger&quot;;
import {
  ApiCreateEndpoint,
  ApiDeleteEndpoint,
  ApiGetEndpoint,
  ApiResponseMessage,
  ApiUpdateEndpoint,
  ResponseTransformInterceptor,
} from &quot;@repo/nestjs-commons/decorators&quot;;
import {
  SENTRY_SERVICE_TOKEN,
  SentryService,
} from &quot;@repo/nestjs-commons/filters&quot;;
import { CUSTOM_LOGGER_TOKEN, ILogger } from &quot;@repo/nestjs-commons/logger&quot;;
import { GENERIC_ERROR_MESSAGES } from &quot;@repo/thena-shared-libs&quot;;
import { CurrentUser } from &quot;../../common/decorators&quot;;
import { ThrottleTierFour } from &quot;../../common/decorators/throttler.decorator&quot;;
import { UpdateTimezoneWorkingHoursDto } from &quot;../../common/dto&quot;;
import {
  AddTeamMemberDto,
  CommonTeamConfigurationsResponse,
  CommonTeamResponse,
  CommonTeamRoutingRuleResponse,
  CreateTeamDto,
  GetAllTeamMembersResponse,
  GetAllTeamRoutingRulesResponse,
  GetAllTeamsResponse,
  TeamConfigurationsResponse,
  UpdateTeamDto,
} from &quot;../dto&quot;;
import {
  CreateRoutingRuleGroupDto,
  UpdateRoutingRuleGroupDto,
} from &quot;../dto/team-routing.dto&quot;;
import { TeamsService } from &quot;../services/teams.service&quot;;
import {
  TeamConfigurationsResponseDto,
  TeamMemberResponseDto,
  TeamResponseDto,
  TeamRoutingRuleResponseDto,
} from &quot;../transformers&quot;;

const LOG_SPAN_ID &#x3D; &quot;TEAMS_CONTROLLER&quot;;

@ApiTags(&quot;Teams&quot;)
@Controller(&quot;v1/teams&quot;)
@UseInterceptors(ResponseTransformInterceptor)
export class TeamsController {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN)
    private readonly logger: ILogger,

    @Inject(SENTRY_SERVICE_TOKEN)
    private readonly sentryService: SentryService,

    private readonly teamsService: TeamsService,
  ) {}

  @Get()
  @ThrottleTierFour()
  @ApiResponseMessage(&quot;Teams fetched successfully!&quot;)
  @ApiGetEndpoint({
    summary: &quot;Get all teams that user is the part of!&quot;,
    responseType: GetAllTeamsResponse,
  })
  async getAllTeams(@CurrentUser() user: CurrentUser) {
    try {
      const teams &#x3D; await this.teamsService.findAllTeams(user);
      return teams.map(TeamResponseDto.fromEntity);
    } catch (error) {
      // If the error is an instance of HttpException, throw it
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        &#x60;${LOG_SPAN_ID} Error encountered while fetching all teams for user: ${user.uid}, error: ${error.message}&#x60;,
        error?.stack,
      );

      // Capture the error with Sentry
      this.sentryService.captureException(error, {
        tag: LOG_SPAN_ID,
        fn: &quot;getAllTeams&quot;,
        organizationId: user.orgUid,
        userId: user.uid,
      });

      throw new InternalServerErrorException(
        GENERIC_ERROR_MESSAGES.SOMETHING_WENT_WRONG,
      );
    }
  }

  @Post()
  @ThrottleTierFour()
  @ApiResponseMessage(&quot;Team created successfully!&quot;)
  @ApiCreateEndpoint({
    summary: &quot;Create a team&quot;,
    responseType: CommonTeamResponse,
  })
  async createTeam(
    @Body() createTeamDto: CreateTeamDto,
    @CurrentUser() user: CurrentUser,
  ) {
    try {
      const createdTeam &#x3D; await this.teamsService.createTeam(
        createTeamDto,
        user,
      );

      return TeamResponseDto.fromEntity(createdTeam);
    } catch (error) {
      // If the error is an instance of HttpException, throw it
      if (error instanceof HttpException) {
        throw error;
      }

      // Capture the error with Sentry
      this.sentryService.captureException(error, {
        tag: LOG_SPAN_ID,
        fn: &quot;createTeam&quot;,
        organizationId: user.orgUid,
        userId: user.uid,
      });

      this.logger.error(
        &#x60;${LOG_SPAN_ID} Error encountered while creating a team for user: ${user.uid}, error: ${error.message}&#x60;,
        error?.stack,
      );

      throw new InternalServerErrorException(
        GENERIC_ERROR_MESSAGES.SOMETHING_WENT_WRONG,
      );
    }
  }

  @Get(&quot;/public&quot;)
  @ThrottleTierFour()
  @ApiResponseMessage(&quot;Public teams fetched successfully!&quot;)
  @ApiGetEndpoint({
    summary: &quot;Get all public teams&quot;,
    responseType: GetAllTeamsResponse,
  })
  async getPublicTeams(@CurrentUser() user: CurrentUser) {
    try {
      const teams &#x3D; await this.teamsService.getAllPublicTeams(user);
      return teams.map(TeamResponseDto.fromEntity);
    } catch (error) {
      // If the error is an instance of HttpException, throw it
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        &#x60;${LOG_SPAN_ID} Error encountered while fetching all public teams for user: ${user.uid}, error: ${error.message}&#x60;,
        error?.stack,
      );

      // Capture the error with Sentry
      this.sentryService.captureException(error, {
        tag: LOG_SPAN_ID,
        fn: &quot;getPublicTeams&quot;,
        organizationId: user.orgUid,
        userId: user.uid,
      });

      throw new InternalServerErrorException(
        GENERIC_ERROR_MESSAGES.SOMETHING_WENT_WRONG,
      );
    }
  }

  @Get(&quot;/:teamId&quot;)
  @ThrottleTierFour()
  @ApiResponseMessage(&quot;Team fetched successfully!&quot;)
  @ApiGetEndpoint({
    summary: &quot;Get a team by ID&quot;,
    responseType: CommonTeamResponse,
  })
  async getTeamById(
    @Param(&quot;teamId&quot;) teamId: string,
    @CurrentUser() user: CurrentUser,
  ) {
    try {
      const foundTeam &#x3D; await this.teamsService.findOneTeamById(teamId, user);
      return TeamResponseDto.fromEntity(foundTeam);
    } catch (error) {
      // If the error is an instance of HttpException, throw it
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        &#x60;${LOG_SPAN_ID} Error encountered while fetching a team by ID for user: ${user.uid}, error: ${error.message}&#x60;,
        error?.stack,
      );

      // Capture the error with Sentry
      this.sentryService.captureException(error, {
        tag: LOG_SPAN_ID,
        fn: &quot;getTeamById&quot;,
        organizationId: user.orgUid,
        userId: user.uid,
      });

      throw new InternalServerErrorException(
        GENERIC_ERROR_MESSAGES.SOMETHING_WENT_WRONG,
      );
    }
  }

  @Get(&quot;/:teamId/sub-teams&quot;)
  @ThrottleTierFour()
  @ApiResponseMessage(&quot;Sub-teams fetched successfully!&quot;)
  @ApiGetEndpoint({
    summary: &quot;Get sub-teams for a team&quot;,
    responseType: GetAllTeamsResponse,
  })
  async getSubTeams(
    @Param(&quot;teamId&quot;) teamId: string,
    @CurrentUser() user: CurrentUser,
  ) {
    try {
      // Throw an error if the parent team id is not provided
      if (!teamId) throw new BadRequestException(&quot;Team id is required!&quot;);

      // Find sub-teams for this parent team
      const subTeams &#x3D; await this.teamsService.findAllSubTeams(user, teamId);
      return subTeams.map(TeamResponseDto.fromEntity);
    } catch (error) {
      // If the error is an instance of HttpException, throw it
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        &#x60;${LOG_SPAN_ID} Error encountered while fetching sub-teams for team: ${teamId} for user: ${user.uid}, error: ${error.message}&#x60;,
        error?.stack,
      );

      // Capture the error with Sentry
      this.sentryService.captureException(error, {
        tag: LOG_SPAN_ID,
        fn: &quot;getSubTeams&quot;,
        organizationId: user.orgUid,
        userId: user.uid,
      });

      throw new InternalServerErrorException(
        GENERIC_ERROR_MESSAGES.SOMETHING_WENT_WRONG,
      );
    }
  }

  @Patch(&quot;/:teamId&quot;)
  @ThrottleTierFour()
  @ApiResponseMessage(&quot;Team updated successfully!&quot;)
  @ApiUpdateEndpoint({
    summary: &quot;Update a team&quot;,
    responseType: CommonTeamResponse,
  })
  async updateTeam(
    @Param(&quot;teamId&quot;) teamId: string,
    @Body() updateTeamDto: UpdateTeamDto,
    @CurrentUser() user: CurrentUser,
  ) {
    try {
      // Check if the team exists
      if (!teamId) throw new BadRequestException(&quot;Team ID is required!&quot;);

      // Update the team
      const updatedTeam &#x3D; await this.teamsService.updateTeam(
        teamId,
        updateTeamDto,
        user,
      );

      // Return the updated team
      return TeamResponseDto.fromEntity(updatedTeam);
    } catch (error) {
      // If the error is an instance of HttpException, throw it
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        &#x60;${LOG_SPAN_ID} Error encountered while updating a team: ${teamId} for user: ${user.uid}, error: ${error.message}&#x60;,
        error?.stack,
      );

      // Capture the error with Sentry
      this.sentryService.captureException(error, {
        tag: LOG_SPAN_ID,
        fn: &quot;updateTeam&quot;,
        organizationId: user.orgUid,
        userId: user.uid,
      });

      throw new InternalServerErrorException(
        GENERIC_ERROR_MESSAGES.SOMETHING_WENT_WRONG,
      );
    }
  }

  @Delete(&quot;/:teamId&quot;)
  @ThrottleTierFour()
  @ApiResponseMessage(&quot;Team deleted successfully!&quot;)
  @ApiDeleteEndpoint({ summary: &quot;Delete a team&quot; })
  async deleteTeam(
    @Param(&quot;teamId&quot;) teamId: string,
    @CurrentUser() user: CurrentUser,
  ) {
    try {
      await this.teamsService.deleteOneTeam(teamId, user);
    } catch (error) {
      // If the error is an instance of HttpException, throw it
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        &#x60;${LOG_SPAN_ID} Error encountered while deleting a team: ${teamId} for user: ${user.uid}, error: ${error.message}&#x60;,
        error?.stack,
      );

      // Capture the error with Sentry
      this.sentryService.captureException(error, {
        tag: LOG_SPAN_ID,
        fn: &quot;deleteTeam&quot;,
        organizationId: user.orgUid,
        userId: user.uid,
      });

      throw new InternalServerErrorException(
        GENERIC_ERROR_MESSAGES.SOMETHING_WENT_WRONG,
      );
    }
  }

  @Get(&quot;/:teamId/members&quot;)
  @ThrottleTierFour()
  @ApiResponseMessage(&quot;Team members fetched successfully!&quot;)
  @ApiGetEndpoint({
    summary: &quot;Get all team members&quot;,
    responseType: GetAllTeamMembersResponse,
  })
  async getTeamMembers(
    @Param(&quot;teamId&quot;) teamId: string,
    @CurrentUser() user: CurrentUser,
  ) {
    try {
      // If no team id is provided, throw an error
      if (!teamId) throw new BadRequestException(&quot;Team ID is required!&quot;);

      // Get the team members
      const teamMembers &#x3D; await this.teamsService.findTeamMembers(teamId, user);

      // Return the team members
      return teamMembers.map(TeamMemberResponseDto.fromEntity);
    } catch (error) {
      // If the error is an instance of HttpException, throw it
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        &#x60;${LOG_SPAN_ID} Error encountered while fetching team members for team: ${teamId} for user: ${user.uid}, error: ${error.message}&#x60;,
        error?.stack,
      );

      // Capture the error with Sentry
      this.sentryService.captureException(error, {
        tag: LOG_SPAN_ID,
        fn: &quot;getTeamMembers&quot;,
        organizationId: user.orgUid,
        userId: user.uid,
      });

      throw new InternalServerErrorException(
        GENERIC_ERROR_MESSAGES.SOMETHING_WENT_WRONG,
      );
    }
  }

  @Post(&quot;/:teamId/members&quot;)
  @ThrottleTierFour()
  @ApiResponseMessage(&quot;Team member added successfully!&quot;)
  @ApiCreateEndpoint({
    summary: &quot;Add a team member&quot;,
    responseType: TeamMemberResponseDto,
  })
  async addTeamMember(
    @Param(&quot;teamId&quot;) teamId: string,
    @CurrentUser() user: CurrentUser,
    @Body() addTeamMemberDto: AddTeamMemberDto,
  ) {
    try {
      if (!addTeamMemberDto.email &amp;&amp; !addTeamMemberDto.userId) {
        throw new BadRequestException(
          &quot;Either email or user ID is required to add a member to a team!&quot;,
        );
      }

      // Add the team member
      const addedTeamMember &#x3D; await this.teamsService.addMemberToTeam(
        teamId,
        addTeamMemberDto,
        user,
      );

      return TeamMemberResponseDto.fromEntity(addedTeamMember);
    } catch (error) {
      // If the error is an instance of HttpException, throw it
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        &#x60;${LOG_SPAN_ID} Error encountered while adding a team member for team: ${teamId} for user: ${user.uid}, error: ${error.message}&#x60;,
        error?.stack,
      );

      // Capture the error with Sentry
      this.sentryService.captureException(error, {
        tag: LOG_SPAN_ID,
        fn: &quot;addTeamMember&quot;,
        organizationId: user.orgUid,
        userId: user.uid,
      });

      throw new InternalServerErrorException(
        GENERIC_ERROR_MESSAGES.SOMETHING_WENT_WRONG,
      );
    }
  }

  @Delete(&quot;/:teamId/members/:memberId&quot;)
  @ThrottleTierFour()
  @ApiResponseMessage(&quot;Team member removed successfully!&quot;)
  @ApiDeleteEndpoint({ summary: &quot;Remove a team member&quot; })
  async removeTeamMember(
    @Param(&quot;teamId&quot;) teamId: string,
    @Param(&quot;memberId&quot;) memberId: string,
    @CurrentUser() user: CurrentUser,
  ) {
    try {
      await this.teamsService.removeMemberFromTeam(teamId, memberId, user);
    } catch (error) {
      // If the error is an instance of HttpException, throw it
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        &#x60;${LOG_SPAN_ID} Error encountered while removing a team member for team: ${teamId} for user: ${user.uid}, error: ${error.message}&#x60;,
        error?.stack,
      );

      // Capture the error with Sentry
      this.sentryService.captureException(error, {
        tag: LOG_SPAN_ID,
        fn: &quot;removeTeamMember&quot;,
        organizationId: user.orgUid,
        userId: user.uid,
      });

      throw new InternalServerErrorException(
        GENERIC_ERROR_MESSAGES.SOMETHING_WENT_WRONG,
      );
    }
  }

  @Get(&quot;/:teamId/configurations&quot;)
  @ThrottleTierFour()
  @ApiResponseMessage(&quot;Team configurations fetched successfully!&quot;)
  @ApiGetEndpoint({
    summary: &quot;Get team configurations&quot;,
    responseType: TeamConfigurationsResponse,
  })
  async getTeamConfigurations(
    @Param(&quot;teamId&quot;) teamId: string,
    @CurrentUser() user: CurrentUser,
  ) {
    try {
      if (!teamId) throw new BadRequestException(&quot;Team ID is required!&quot;);

      const teamConfigurations &#x3D; await this.teamsService.getTeamConfigurations(
        teamId,
        user,
      );

      return TeamConfigurationsResponseDto.fromEntity(teamConfigurations);
    } catch (error) {
      // If the error is an instance of HttpException, throw it
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        &#x60;${LOG_SPAN_ID} Error encountered while fetching team configurations for team: ${teamId} for user: ${user.uid}, error: ${error.message}&#x60;,
        error?.stack,
      );

      // Capture the error with Sentry
      this.sentryService.captureException(error, {
        tag: LOG_SPAN_ID,
        fn: &quot;getTeamConfigurations&quot;,
        organizationId: user.orgUid,
        userId: user.uid,
      });

      throw new InternalServerErrorException(
        GENERIC_ERROR_MESSAGES.SOMETHING_WENT_WRONG,
      );
    }
  }

  @Patch(&quot;/:teamId/configurations&quot;)
  @ThrottleTierFour()
  @ApiResponseMessage(&quot;Team configurations updated successfully!&quot;)
  @ApiUpdateEndpoint({
    summary: &quot;Update team configurations&quot;,
    responseType: CommonTeamConfigurationsResponse,
  })
  async updateTeamConfigurations(
    @Param(&quot;teamId&quot;) teamId: string,
    @CurrentUser() user: CurrentUser,
    @Body() updateTeamConfigurationsDto: UpdateTimezoneWorkingHoursDto,
  ) {
    try {
      const updateResult &#x3D; await this.teamsService.updateTeamConfigurations(
        teamId,
        updateTeamConfigurationsDto,
        user,
      );

      return TeamConfigurationsResponseDto.fromEntity(updateResult);
    } catch (error) {
      // If the error is an instance of HttpException, throw it
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        &#x60;${LOG_SPAN_ID} Error encountered while updating team configurations for team: ${teamId} for user: ${user.uid}, error: ${error.message}&#x60;,
        error?.stack,
      );

      // Capture the error with Sentry
      this.sentryService.captureException(error, {
        tag: LOG_SPAN_ID,
        fn: &quot;updateTeamConfigurations&quot;,
        organizationId: user.orgUid,
        userId: user.uid,
      });

      throw new InternalServerErrorException(
        GENERIC_ERROR_MESSAGES.SOMETHING_WENT_WRONG,
      );
    }
  }

  @Post(&quot;/:teamId/routing&quot;)
  @ThrottleTierFour()
  @ApiResponseMessage(&quot;Team routing updated successfully!&quot;)
  @ApiCreateEndpoint({
    summary: &quot;Create a team routing rule&quot;,
    responseType: CommonTeamRoutingRuleResponse,
  })
  async createRoutingRule(
    @Param(&quot;teamId&quot;) teamId: string,
    @CurrentUser() user: CurrentUser,
    @Body() createRoutingRuleDto: CreateRoutingRuleGroupDto,
  ) {
    try {
      // If no team id is provided, throw an error
      if (!teamId) throw new BadRequestException(&quot;Team ID is required!&quot;);

      // Create the routing rule
      const createdRoutingRule &#x3D; await this.teamsService.createRoutingRule(
        teamId,
        user,
        createRoutingRuleDto,
      );

      // Return the created routing rule
      return TeamRoutingRuleResponseDto.fromEntity(createdRoutingRule);
    } catch (error) {
      // If the error is an instance of HttpException, throw it
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        &#x60;${LOG_SPAN_ID} Error encountered while creating a team routing rule for team: ${teamId} for user: ${user.uid}, error: ${error.message}&#x60;,
        error?.stack,
      );

      // Capture the error with Sentry
      this.sentryService.captureException(error, {
        tag: LOG_SPAN_ID,
        fn: &quot;createRoutingRule&quot;,
        organizationId: user.orgUid,
        userId: user.uid,
      });

      throw new InternalServerErrorException(
        GENERIC_ERROR_MESSAGES.SOMETHING_WENT_WRONG,
      );
    }
  }

  @Patch(&quot;/:teamId/routing/:ruleId&quot;)
  @ThrottleTierFour()
  @ApiResponseMessage(&quot;Team routing updated successfully!&quot;)
  @ApiUpdateEndpoint({
    summary: &quot;Update team routing&quot;,
    responseType: CommonTeamRoutingRuleResponse,
  })
  async updateRoutingRule(
    @Param(&quot;teamId&quot;) teamId: string,
    @Param(&quot;ruleId&quot;) ruleId: string,
    @CurrentUser() user: CurrentUser,
    @Body() updateRoutingRuleDto: UpdateRoutingRuleGroupDto,
  ) {
    try {
      // If no team id is provided, throw an error
      if (!teamId) throw new BadRequestException(&quot;Team ID is required!&quot;);

      // Update the routing rule
      const updatedRoutingRule &#x3D; await this.teamsService.updateRoutingRule(
        teamId,
        ruleId,
        user,
        updateRoutingRuleDto,
      );

      return TeamRoutingRuleResponseDto.fromEntity(updatedRoutingRule);
    } catch (error) {
      // If the error is an instance of HttpException, throw it
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        &#x60;${LOG_SPAN_ID} Error encountered while updating a team routing rule for team: ${teamId} for user: ${user.uid}, error: ${error.message}&#x60;,
        error?.stack,
      );

      // Capture the error with Sentry
      this.sentryService.captureException(error, {
        tag: LOG_SPAN_ID,
        fn: &quot;updateRoutingRule&quot;,
        organizationId: user.orgUid,
        userId: user.uid,
      });

      throw new InternalServerErrorException(
        GENERIC_ERROR_MESSAGES.SOMETHING_WENT_WRONG,
      );
    }
  }

  @Get(&quot;/:teamId/routing&quot;)
  @ThrottleTierFour()
  @ApiResponseMessage(&quot;Team routing fetched successfully!&quot;)
  @ApiGetEndpoint({
    summary: &quot;Get team routing&quot;,
    responseType: GetAllTeamRoutingRulesResponse,
  })
  async getTeamRouting(
    @Param(&quot;teamId&quot;) teamId: string,
    @CurrentUser() user: CurrentUser,
  ) {
    try {
      // If no team id is provided, throw an error
      if (!teamId) throw new BadRequestException(&quot;Team ID is required!&quot;);

      // Get the team routing rules
      const teamRoutingRules &#x3D; await this.teamsService.getTeamRoutingRules(
        teamId,
        user,
      );

      // Return the team routing rules
      return teamRoutingRules.map(TeamRoutingRuleResponseDto.fromEntity);
    } catch (error) {
      // If the error is an instance of HttpException, throw it
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        &#x60;${LOG_SPAN_ID} Error encountered while fetching team routing for team: ${teamId} for user: ${user.uid}, error: ${error.message}&#x60;,
        error?.stack,
      );

      // Capture the error with Sentry
      this.sentryService.captureException(error, {
        tag: LOG_SPAN_ID,
        fn: &quot;getTeamRouting&quot;,
        organizationId: user.orgUid,
        userId: user.uid,
      });

      throw new InternalServerErrorException(
        GENERIC_ERROR_MESSAGES.SOMETHING_WENT_WRONG,
      );
    }
  }

  @Delete(&quot;/:teamId/routing/:ruleId&quot;)
  @ThrottleTierFour()
  @ApiResponseMessage(&quot;Team routing deleted successfully!&quot;)
  @ApiDeleteEndpoint({ summary: &quot;Delete a team routing rule&quot; })
  async deleteRoutingRule(
    @Param(&quot;teamId&quot;) teamId: string,
    @Param(&quot;ruleId&quot;) ruleId: string,
    @CurrentUser() user: CurrentUser,
  ) {
    try {
      // If no rule id is provided, throw an error
      if (!ruleId) throw new BadRequestException(&quot;Rule ID is required!&quot;);

      // If no team id is provided, throw an error
      if (!teamId) throw new BadRequestException(&quot;Team ID is required!&quot;);

      // Delete the routing rule
      await this.teamsService.deleteRoutingRule(teamId, ruleId, user);
    } catch (error) {
      // If the error is an instance of HttpException, throw it
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        &#x60;${LOG_SPAN_ID} Error encountered while deleting a team routing rule for team: ${teamId} for user: ${user.uid}, error: ${error.message}&#x60;,
        error?.stack,
      );

      // Capture the error with Sentry
      this.sentryService.captureException(error, {
        tag: LOG_SPAN_ID,
        fn: &quot;deleteRoutingRule&quot;,
        organizationId: user.orgUid,
        userId: user.uid,
      });

      throw new InternalServerErrorException(
        GENERIC_ERROR_MESSAGES.SOMETHING_WENT_WRONG,
      );
    }
  }
}
</code></pre>
    </div>
</div>
















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'controller';
            var COMPODOC_CURRENT_PAGE_URL = 'TeamsController.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
