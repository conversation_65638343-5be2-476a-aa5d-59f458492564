<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content controller">
                   <div class="content-data">





<ol class="breadcrumb">
  <li class="breadcrumb-item">Controllers</li>
  <li class="breadcrumb-item" >AccountNotesGrpcController</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/accounts/controllers/grpc/account-notes.grpc.controller.ts</code>
        </p>







            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#createAccountNote" >createAccountNote</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#deleteAccountNote" >deleteAccountNote</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#getAccountNotes" >getAccountNotes</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#updateAccountNote" >updateAccountNote</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createAccountNote"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>createAccountNote</b></span>
                        <a href="#createAccountNote"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createAccountNote(request: accounts.CreateAccountNoteRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(accounts.ACCOUNT_NOTES_SERVICE_NAME, &#x27;CreateAccountNote&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="65"
                                    class="link-to-prism">src/accounts/controllers/grpc/account-notes.grpc.controller.ts:65</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                        <code>accounts.CreateAccountNoteRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;accounts.AccountNote&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteAccountNote"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>deleteAccountNote</b></span>
                        <a href="#deleteAccountNote"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteAccountNote(request: accounts.DeleteAccountNoteRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(accounts.ACCOUNT_NOTES_SERVICE_NAME, &#x27;DeleteAccountNote&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="141"
                                    class="link-to-prism">src/accounts/controllers/grpc/account-notes.grpc.controller.ts:141</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                        <code>accounts.DeleteAccountNoteRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;void&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getAccountNotes"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>getAccountNotes</b></span>
                        <a href="#getAccountNotes"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getAccountNotes(request: <a href="../classes/GetAccountNotes.html" target="_self">accounts.GetAccountNotesRequest</a>, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(accounts.ACCOUNT_NOTES_SERVICE_NAME, &#x27;GetAccountNotes&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="25"
                                    class="link-to-prism">src/accounts/controllers/grpc/account-notes.grpc.controller.ts:25</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../classes/GetAccountNotes.html" target="_self" >accounts.GetAccountNotesRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/GetAccountNotes.html" target="_self" >Promise&lt;accounts.GetAccountNotesResponse&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateAccountNote"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>updateAccountNote</b></span>
                        <a href="#updateAccountNote"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateAccountNote(request: accounts.UpdateAccountNoteRequest, metadata: Metadata)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@GrpcMethod(accounts.ACCOUNT_NOTES_SERVICE_NAME, &#x27;UpdateAccountNote&#x27;)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="103"
                                    class="link-to-prism">src/accounts/controllers/grpc/account-notes.grpc.controller.ts:103</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                        <code>accounts.UpdateAccountNoteRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>metadata</td>
                                            <td>
                                                        <code>Metadata</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;accounts.AccountNote&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Metadata } from &quot;@grpc/grpc-js&quot;;
import { Controller, UseGuards } from &quot;@nestjs/common&quot;;
import { GrpcMethod } from &quot;@nestjs/microservices&quot;;
import { handleRpcError } from &quot;@repo/nestjs-commons/errors&quot;;
import { GrpcAuthGuard } from &quot;@repo/nestjs-commons/guards&quot;;
import { extractUserMetadata } from &quot;@repo/nestjs-commons/utils&quot;;
import { accounts } from &quot;@repo/shared-proto&quot;;
import { AccountNoteVisibility } from &quot;@repo/thena-platform-entities&quot;;
import { AccountNoteActionService } from &quot;src/accounts/services/account-note.action.service&quot;;
import {
  CreateAccountNoteDto,
  FindAccountNoteDto,
  UpdateAccountNoteDto,
} from &quot;../../dtos/account-note.dto&quot;;
import { AccountNoteResponseDto } from &quot;../../dtos/response/account-note.dto&quot;;

@Controller()
@UseGuards(GrpcAuthGuard)
export class AccountNotesGrpcController {
  constructor(
    private readonly accountNoteActionService: AccountNoteActionService,
  ) {}

  @GrpcMethod(accounts.ACCOUNT_NOTES_SERVICE_NAME, &quot;GetAccountNotes&quot;)
  async getAccountNotes(
    request: accounts.GetAccountNotesRequest,
    metadata: Metadata,
  ): Promise&lt;accounts.GetAccountNotesResponse&gt; {
    try {
      const user &#x3D; extractUserMetadata(metadata);

      const query: FindAccountNoteDto &#x3D; {
        accountId: request.accountId,
        type: request.type,
        page: request.page,
        limit: request.limit,
      };

      const notes &#x3D; await this.accountNoteActionService.findAllAccountNotes(
        user,
        query,
      );

      const notesResponse &#x3D; notes.map((note) &#x3D;&gt;
        AccountNoteResponseDto.fromEntity(note),
      );

      const response: accounts.GetAccountNotesResponse &#x3D; {
        data: notesResponse.map((note) &#x3D;&gt; ({
          ...note,
          visibility:
            note.visibility &#x3D;&#x3D;&#x3D; AccountNoteVisibility.ORGANIZATION
              ? accounts.AccountNoteVisibility.PUBLIC
              : accounts.AccountNoteVisibility.PRIVATE,
        })),
      };

      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(accounts.ACCOUNT_NOTES_SERVICE_NAME, &quot;CreateAccountNote&quot;)
  async createAccountNote(
    request: accounts.CreateAccountNoteRequest,
    metadata: Metadata,
  ): Promise&lt;accounts.AccountNote&gt; {
    try {
      const user &#x3D; extractUserMetadata(metadata);

      const createDto: CreateAccountNoteDto &#x3D; {
        accountId: request.accountId,
        content: request.content,
        type: request.type,
        visibility:
          request.visibility &#x3D;&#x3D;&#x3D; accounts.AccountNoteVisibility.PUBLIC
            ? AccountNoteVisibility.ORGANIZATION
            : AccountNoteVisibility.PRIVATE,
        attachmentUrls: request.attachmentUrls,
      };

      const note &#x3D; await this.accountNoteActionService.createAccountNote(
        user,
        createDto,
      );

      const noteResponse &#x3D; AccountNoteResponseDto.fromEntity(note);
      const response: accounts.AccountNote &#x3D; {
        ...noteResponse,
        visibility:
          noteResponse.visibility &#x3D;&#x3D;&#x3D; AccountNoteVisibility.ORGANIZATION
            ? accounts.AccountNoteVisibility.PUBLIC
            : accounts.AccountNoteVisibility.PRIVATE,
      };
      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(accounts.ACCOUNT_NOTES_SERVICE_NAME, &quot;UpdateAccountNote&quot;)
  async updateAccountNote(
    request: accounts.UpdateAccountNoteRequest,
    metadata: Metadata,
  ): Promise&lt;accounts.AccountNote&gt; {
    try {
      const user &#x3D; extractUserMetadata(metadata);

      const updateDto: UpdateAccountNoteDto &#x3D; {
        content: request.content,
        type: request.type,
        visibility:
          request.visibility &#x3D;&#x3D;&#x3D; accounts.AccountNoteVisibility.PUBLIC
            ? AccountNoteVisibility.ORGANIZATION
            : AccountNoteVisibility.PRIVATE,
        attachmentUrls: request.attachmentUrls,
      };

      const note &#x3D; await this.accountNoteActionService.updateAccountNote(
        user,
        request.noteId,
        updateDto,
      );

      const noteResponse &#x3D; AccountNoteResponseDto.fromEntity(note);
      const response: accounts.AccountNote &#x3D; {
        ...noteResponse,
        visibility:
          noteResponse.visibility &#x3D;&#x3D;&#x3D; AccountNoteVisibility.ORGANIZATION
            ? accounts.AccountNoteVisibility.PUBLIC
            : accounts.AccountNoteVisibility.PRIVATE,
      };
      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(accounts.ACCOUNT_NOTES_SERVICE_NAME, &quot;DeleteAccountNote&quot;)
  async deleteAccountNote(
    request: accounts.DeleteAccountNoteRequest,
    metadata: Metadata,
  ): Promise&lt;void&gt; {
    try {
      const user &#x3D; extractUserMetadata(metadata);
      await this.accountNoteActionService.deleteAccountNote(
        user,
        request.noteId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }
}
</code></pre>
    </div>
</div>
















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'controller';
            var COMPODOC_CURRENT_PAGE_URL = 'AccountNotesGrpcController.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
