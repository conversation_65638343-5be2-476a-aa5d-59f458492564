/*!
 * https://github.com/paulmillr/es6-shim
 * @license es6-shim Copyright 2013-2016 by <PERSON> (http://paulmillr.com)
 *   and contributors,  MIT License
 * es6-shim: v0.35.4
 * see https://github.com/paulmillr/es6-shim/blob/0.35.3/LICENSE
 * Details and documentation:
 * https://github.com/paulmillr/es6-shim/
 */
(function(e,t){if(typeof define==="function"&&define.amd){define(t)}else if(typeof exports==="object"){module.exports=t()}else{e.returnExports=t()}})(this,function(){"use strict";var e=Function.call.bind(Function.apply);var t=Function.call.bind(Function.call);var r=Array.isArray;var n=Object.keys;var o=function notThunker(t){return function notThunk(){return!e(t,this,arguments)}};var i=function(e){try{e();return false}catch(t){return true}};var a=function valueOrFalseIfThrows(e){try{return e()}catch(t){return false}};var u=o(i);var f=function(){return!i(function(){return Object.defineProperty({},"x",{get:function(){}})})};var s=!!Object.defineProperty&&f();var c=function foo(){}.name==="foo";var l=Function.call.bind(Array.prototype.forEach);var p=Function.call.bind(Array.prototype.reduce);var v=Function.call.bind(Array.prototype.filter);var y=Function.call.bind(Array.prototype.some);var h=function(e,t,r,n){if(!n&&t in e){return}if(s){Object.defineProperty(e,t,{configurable:true,enumerable:false,writable:true,value:r})}else{e[t]=r}};var b=function(e,t,r){l(n(t),function(n){var o=t[n];h(e,n,o,!!r)})};var g=Function.call.bind(Object.prototype.toString);var d=typeof/abc/==="function"?function IsCallableSlow(e){return typeof e==="function"&&g(e)==="[object Function]"}:function IsCallableFast(e){return typeof e==="function"};var m={getter:function(e,t,r){if(!s){throw new TypeError("getters require true ES5 support")}Object.defineProperty(e,t,{configurable:true,enumerable:false,get:r})},proxy:function(e,t,r){if(!s){throw new TypeError("getters require true ES5 support")}var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,{configurable:n.configurable,enumerable:n.enumerable,get:function getKey(){return e[t]},set:function setKey(r){e[t]=r}})},redefine:function(e,t,r){if(s){var n=Object.getOwnPropertyDescriptor(e,t);n.value=r;Object.defineProperty(e,t,n)}else{e[t]=r}},defineByDescriptor:function(e,t,r){if(s){Object.defineProperty(e,t,r)}else if("value"in r){e[t]=r.value}},preserveToString:function(e,t){if(t&&d(t.toString)){h(e,"toString",t.toString.bind(t),true)}}};var O=Object.create||function(e,t){var r=function Prototype(){};r.prototype=e;var o=new r;if(typeof t!=="undefined"){n(t).forEach(function(e){m.defineByDescriptor(o,e,t[e])})}return o};var w=function(e,t){if(!Object.setPrototypeOf){return false}return a(function(){var r=function Subclass(t){var r=new e(t);Object.setPrototypeOf(r,Subclass.prototype);return r};Object.setPrototypeOf(r,e);r.prototype=O(e.prototype,{constructor:{value:r}});return t(r)})};var j=function(){if(typeof self!=="undefined"){return self}if(typeof window!=="undefined"){return window}if(typeof global!=="undefined"){return global}throw new Error("unable to locate global object")};var S=j();var T=S.isFinite;var I=Function.call.bind(String.prototype.indexOf);var E=Function.apply.bind(Array.prototype.indexOf);var P=Function.call.bind(Array.prototype.concat);var C=Function.call.bind(String.prototype.slice);var M=Function.call.bind(Array.prototype.push);var x=Function.apply.bind(Array.prototype.push);var N=Function.call.bind(Array.prototype.join);var A=Function.call.bind(Array.prototype.shift);var _=Math.max;var R=Math.min;var k=Math.floor;var L=Math.abs;var F=Math.exp;var D=Math.log;var z=Math.sqrt;var q=Function.call.bind(Object.prototype.hasOwnProperty);var W;var G=function(){};var H=S.Map;var V=H&&H.prototype["delete"];var B=H&&H.prototype.get;var U=H&&H.prototype.has;var $=H&&H.prototype.set;var J=S.Symbol||{};var X=J.species||"@@species";var K=Number.isNaN||function isNaN(e){return e!==e};var Z=Number.isFinite||function isFinite(e){return typeof e==="number"&&T(e)};var Y=d(Math.sign)?Math.sign:function sign(e){var t=Number(e);if(t===0){return t}if(K(t)){return t}return t<0?-1:1};var Q=function log1p(e){var t=Number(e);if(t<-1||K(t)){return NaN}if(t===0||t===Infinity){return t}if(t===-1){return-Infinity}return 1+t-1===0?t:t*(D(1+t)/(1+t-1))};var ee=function isArguments(e){return g(e)==="[object Arguments]"};var te=function isArguments(e){return e!==null&&typeof e==="object"&&typeof e.length==="number"&&e.length>=0&&g(e)!=="[object Array]"&&g(e.callee)==="[object Function]"};var re=ee(arguments)?ee:te;var ne={primitive:function(e){return e===null||typeof e!=="function"&&typeof e!=="object"},string:function(e){return g(e)==="[object String]"},regex:function(e){return g(e)==="[object RegExp]"},symbol:function(e){return typeof S.Symbol==="function"&&typeof e==="symbol"}};var oe=function overrideNative(e,t,r){var n=e[t];h(e,t,r,true);m.preserveToString(e[t],n)};var ie=typeof J==="function"&&typeof J["for"]==="function"&&ne.symbol(J());var ae=ne.symbol(J.iterator)?J.iterator:"_es6-shim iterator_";if(S.Set&&typeof(new S.Set)["@@iterator"]==="function"){ae="@@iterator"}if(!S.Reflect){h(S,"Reflect",{},true)}var ue=S.Reflect;var fe=String;var se=typeof document==="undefined"||!document?null:document.all;var ce=se==null?function isNullOrUndefined(e){return e==null}:function isNullOrUndefinedAndNotDocumentAll(e){return e==null&&e!==se};var le={Call:function Call(t,r){var n=arguments.length>2?arguments[2]:[];if(!le.IsCallable(t)){throw new TypeError(t+" is not a function")}return e(t,r,n)},RequireObjectCoercible:function(e,t){if(ce(e)){throw new TypeError(t||"Cannot call method on "+e)}return e},TypeIsObject:function(e){if(e===void 0||e===null||e===true||e===false){return false}return typeof e==="function"||typeof e==="object"||e===se},ToObject:function(e,t){return Object(le.RequireObjectCoercible(e,t))},IsCallable:d,IsConstructor:function(e){return le.IsCallable(e)},ToInt32:function(e){return le.ToNumber(e)>>0},ToUint32:function(e){return le.ToNumber(e)>>>0},ToNumber:function(e){if(ie&&g(e)==="[object Symbol]"){throw new TypeError("Cannot convert a Symbol value to a number")}return+e},ToInteger:function(e){var t=le.ToNumber(e);if(K(t)){return 0}if(t===0||!Z(t)){return t}return(t>0?1:-1)*k(L(t))},ToLength:function(e){var t=le.ToInteger(e);if(t<=0){return 0}if(t>Number.MAX_SAFE_INTEGER){return Number.MAX_SAFE_INTEGER}return t},SameValue:function(e,t){if(e===t){if(e===0){return 1/e===1/t}return true}return K(e)&&K(t)},SameValueZero:function(e,t){return e===t||K(e)&&K(t)},GetIterator:function(e){if(re(e)){return new W(e,"value")}var t=le.GetMethod(e,ae);if(!le.IsCallable(t)){throw new TypeError("value is not an iterable")}var r=le.Call(t,e);if(!le.TypeIsObject(r)){throw new TypeError("bad iterator")}return r},GetMethod:function(e,t){var r=le.ToObject(e)[t];if(ce(r)){return void 0}if(!le.IsCallable(r)){throw new TypeError("Method not callable: "+t)}return r},IteratorComplete:function(e){return!!e.done},IteratorClose:function(e,t){var r=le.GetMethod(e,"return");if(r===void 0){return}var n,o;try{n=le.Call(r,e)}catch(i){o=i}if(t){return}if(o){throw o}if(!le.TypeIsObject(n)){throw new TypeError("Iterator's return method returned a non-object.")}},IteratorNext:function(e){var t=arguments.length>1?e.next(arguments[1]):e.next();if(!le.TypeIsObject(t)){throw new TypeError("bad iterator")}return t},IteratorStep:function(e){var t=le.IteratorNext(e);var r=le.IteratorComplete(t);return r?false:t},Construct:function(e,t,r,n){var o=typeof r==="undefined"?e:r;if(!n&&ue.construct){return ue.construct(e,t,o)}var i=o.prototype;if(!le.TypeIsObject(i)){i=Object.prototype}var a=O(i);var u=le.Call(e,a,t);return le.TypeIsObject(u)?u:a},SpeciesConstructor:function(e,t){var r=e.constructor;if(r===void 0){return t}if(!le.TypeIsObject(r)){throw new TypeError("Bad constructor")}var n=r[X];if(ce(n)){return t}if(!le.IsConstructor(n)){throw new TypeError("Bad @@species")}return n},CreateHTML:function(e,t,r,n){var o=le.ToString(e);var i="<"+t;if(r!==""){var a=le.ToString(n);var u=a.replace(/"/g,"&quot;");i+=" "+r+'="'+u+'"'}var f=i+">";var s=f+o;return s+"</"+t+">"},IsRegExp:function IsRegExp(e){if(!le.TypeIsObject(e)){return false}var t=e[J.match];if(typeof t!=="undefined"){return!!t}return ne.regex(e)},ToString:function ToString(e){if(ie&&g(e)==="[object Symbol]"){throw new TypeError("Cannot convert a Symbol value to a number")}return fe(e)}};if(s&&ie){var pe=function defineWellKnownSymbol(e){if(ne.symbol(J[e])){return J[e]}var t=J["for"]("Symbol."+e);Object.defineProperty(J,e,{configurable:false,enumerable:false,writable:false,value:t});return t};if(!ne.symbol(J.search)){var ve=pe("search");var ye=String.prototype.search;h(RegExp.prototype,ve,function search(e){return le.Call(ye,e,[this])});var he=function search(e){var t=le.RequireObjectCoercible(this);if(!ce(e)){var r=le.GetMethod(e,ve);if(typeof r!=="undefined"){return le.Call(r,e,[t])}}return le.Call(ye,t,[le.ToString(e)])};oe(String.prototype,"search",he)}if(!ne.symbol(J.replace)){var be=pe("replace");var ge=String.prototype.replace;h(RegExp.prototype,be,function replace(e,t){return le.Call(ge,e,[this,t])});var de=function replace(e,t){var r=le.RequireObjectCoercible(this);if(!ce(e)){var n=le.GetMethod(e,be);if(typeof n!=="undefined"){return le.Call(n,e,[r,t])}}return le.Call(ge,r,[le.ToString(e),t])};oe(String.prototype,"replace",de)}if(!ne.symbol(J.split)){var me=pe("split");var Oe=String.prototype.split;h(RegExp.prototype,me,function split(e,t){return le.Call(Oe,e,[this,t])});var we=function split(e,t){var r=le.RequireObjectCoercible(this);if(!ce(e)){var n=le.GetMethod(e,me);if(typeof n!=="undefined"){return le.Call(n,e,[r,t])}}return le.Call(Oe,r,[le.ToString(e),t])};oe(String.prototype,"split",we)}var je=ne.symbol(J.match);var Se=je&&function(){var e={};e[J.match]=function(){return 42};return"a".match(e)!==42}();if(!je||Se){var Te=pe("match");var Ie=String.prototype.match;h(RegExp.prototype,Te,function match(e){return le.Call(Ie,e,[this])});var Ee=function match(e){var t=le.RequireObjectCoercible(this);if(!ce(e)){var r=le.GetMethod(e,Te);if(typeof r!=="undefined"){return le.Call(r,e,[t])}}return le.Call(Ie,t,[le.ToString(e)])};oe(String.prototype,"match",Ee)}}var Pe=function wrapConstructor(e,t,r){m.preserveToString(t,e);if(Object.setPrototypeOf){Object.setPrototypeOf(e,t)}if(s){l(Object.getOwnPropertyNames(e),function(n){if(n in G||r[n]){return}m.proxy(e,n,t)})}else{l(Object.keys(e),function(n){if(n in G||r[n]){return}t[n]=e[n]})}t.prototype=e.prototype;m.redefine(e.prototype,"constructor",t)};var Ce=function(){return this};var Me=function(e){if(s&&!q(e,X)){m.getter(e,X,Ce)}};var xe=function(e,t){var r=t||function iterator(){return this};h(e,ae,r);if(!e[ae]&&ne.symbol(ae)){e[ae]=r}};var Ne=function createDataProperty(e,t,r){if(s){Object.defineProperty(e,t,{configurable:true,enumerable:true,writable:true,value:r})}else{e[t]=r}};var Ae=function createDataPropertyOrThrow(e,t,r){Ne(e,t,r);if(!le.SameValue(e[t],r)){throw new TypeError("property is nonconfigurable")}};var _e=function(e,t,r,n){if(!le.TypeIsObject(e)){throw new TypeError("Constructor requires `new`: "+t.name)}var o=t.prototype;if(!le.TypeIsObject(o)){o=r}var i=O(o);for(var a in n){if(q(n,a)){var u=n[a];h(i,a,u,true)}}return i};if(String.fromCodePoint&&String.fromCodePoint.length!==1){var Re=String.fromCodePoint;oe(String,"fromCodePoint",function fromCodePoint(e){return le.Call(Re,this,arguments)})}var ke={fromCodePoint:function fromCodePoint(e){var t=[];var r;for(var n=0,o=arguments.length;n<o;n++){r=Number(arguments[n]);if(!le.SameValue(r,le.ToInteger(r))||r<0||r>1114111){throw new RangeError("Invalid code point "+r)}if(r<65536){M(t,String.fromCharCode(r))}else{r-=65536;M(t,String.fromCharCode((r>>10)+55296));M(t,String.fromCharCode(r%1024+56320))}}return N(t,"")},raw:function raw(e){var t=arguments.length-1;var r=le.ToObject(e,"bad template");var raw=le.ToObject(r.raw,"bad raw value");var n=raw.length;var o=le.ToLength(n);if(o<=0){return""}var i=[];var a=0;var u,f,s,c;while(a<o){u=le.ToString(a);s=le.ToString(raw[u]);M(i,s);if(a+1>=o){break}f=a+1<arguments.length?arguments[a+1]:"";c=le.ToString(f);M(i,c);a+=1}return N(i,"")}};if(String.raw&&String.raw({raw:{0:"x",1:"y",length:2}})!=="xy"){oe(String,"raw",ke.raw)}b(String,ke);var Le=function repeat(e,t){if(t<1){return""}if(t%2){return repeat(e,t-1)+e}var r=repeat(e,t/2);return r+r};var Fe=Infinity;var De={repeat:function repeat(e){var t=le.ToString(le.RequireObjectCoercible(this));var r=le.ToInteger(e);if(r<0||r>=Fe){throw new RangeError("repeat count must be less than infinity and not overflow maximum string size")}return Le(t,r)},startsWith:function startsWith(e){var t=le.ToString(le.RequireObjectCoercible(this));if(le.IsRegExp(e)){throw new TypeError('Cannot call method "startsWith" with a regex')}var r=le.ToString(e);var n;if(arguments.length>1){n=arguments[1]}var o=_(le.ToInteger(n),0);return C(t,o,o+r.length)===r},endsWith:function endsWith(e){var t=le.ToString(le.RequireObjectCoercible(this));if(le.IsRegExp(e)){throw new TypeError('Cannot call method "endsWith" with a regex')}var r=le.ToString(e);var n=t.length;var o;if(arguments.length>1){o=arguments[1]}var i=typeof o==="undefined"?n:le.ToInteger(o);var a=R(_(i,0),n);return C(t,a-r.length,a)===r},includes:function includes(e){if(le.IsRegExp(e)){throw new TypeError('"includes" does not accept a RegExp')}var t=le.ToString(e);var r;if(arguments.length>1){r=arguments[1]}return I(this,t,r)!==-1},codePointAt:function codePointAt(e){var t=le.ToString(le.RequireObjectCoercible(this));var r=le.ToInteger(e);var n=t.length;if(r>=0&&r<n){var o=t.charCodeAt(r);var i=r+1===n;if(o<55296||o>56319||i){return o}var a=t.charCodeAt(r+1);if(a<56320||a>57343){return o}return(o-55296)*1024+(a-56320)+65536}}};if(String.prototype.includes&&"a".includes("a",Infinity)!==false){oe(String.prototype,"includes",De.includes)}if(String.prototype.startsWith&&String.prototype.endsWith){var ze=i(function(){return"/a/".startsWith(/a/)});var qe=a(function(){return"abc".startsWith("a",Infinity)===false});if(!ze||!qe){oe(String.prototype,"startsWith",De.startsWith);oe(String.prototype,"endsWith",De.endsWith)}}if(ie){var We=a(function(){var e=/a/;e[J.match]=false;return"/a/".startsWith(e)});if(!We){oe(String.prototype,"startsWith",De.startsWith)}var Ge=a(function(){var e=/a/;e[J.match]=false;return"/a/".endsWith(e)});if(!Ge){oe(String.prototype,"endsWith",De.endsWith)}var He=a(function(){var e=/a/;e[J.match]=false;return"/a/".includes(e)});if(!He){oe(String.prototype,"includes",De.includes)}}b(String.prototype,De);var Ve=["\t\n\x0B\f\r \xa0\u1680\u180e\u2000\u2001\u2002\u2003","\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028","\u2029\ufeff"].join("");var Be=new RegExp("(^["+Ve+"]+)|(["+Ve+"]+$)","g");var Ue=function trim(){return le.ToString(le.RequireObjectCoercible(this)).replace(Be,"")};var $e=["\x85","\u200b","\ufffe"].join("");var Je=new RegExp("["+$e+"]","g");var Xe=/^[-+]0x[0-9a-f]+$/i;var Ke=$e.trim().length!==$e.length;h(String.prototype,"trim",Ue,Ke);var Ze=function(e){return{value:e,done:arguments.length===0}};var Ye=function(e){le.RequireObjectCoercible(e);h(this,"_s",le.ToString(e));h(this,"_i",0)};Ye.prototype.next=function(){var e=this._s;var t=this._i;if(typeof e==="undefined"||t>=e.length){this._s=void 0;return Ze()}var r=e.charCodeAt(t);var n,o;if(r<55296||r>56319||t+1===e.length){o=1}else{n=e.charCodeAt(t+1);o=n<56320||n>57343?1:2}this._i=t+o;return Ze(e.substr(t,o))};xe(Ye.prototype);xe(String.prototype,function(){return new Ye(this)});var Qe={from:function from(e){var r=this;var n;if(arguments.length>1){n=arguments[1]}var o,i;if(typeof n==="undefined"){o=false}else{if(!le.IsCallable(n)){throw new TypeError("Array.from: when provided, the second argument must be a function")}if(arguments.length>2){i=arguments[2]}o=true}var a=typeof(re(e)||le.GetMethod(e,ae))!=="undefined";var u,f,s;if(a){f=le.IsConstructor(r)?Object(new r):[];var c=le.GetIterator(e);var l,p;s=0;while(true){l=le.IteratorStep(c);if(l===false){break}p=l.value;try{if(o){p=typeof i==="undefined"?n(p,s):t(n,i,p,s)}f[s]=p}catch(v){le.IteratorClose(c,true);throw v}s+=1}u=s}else{var y=le.ToObject(e);u=le.ToLength(y.length);f=le.IsConstructor(r)?Object(new r(u)):new Array(u);var h;for(s=0;s<u;++s){h=y[s];if(o){h=typeof i==="undefined"?n(h,s):t(n,i,h,s)}Ae(f,s,h)}}f.length=u;return f},of:function of(){var e=arguments.length;var t=this;var n=r(t)||!le.IsCallable(t)?new Array(e):le.Construct(t,[e]);for(var o=0;o<e;++o){Ae(n,o,arguments[o])}n.length=e;return n}};b(Array,Qe);Me(Array);W=function(e,t){h(this,"i",0);h(this,"array",e);h(this,"kind",t)};b(W.prototype,{next:function(){var e=this.i;var t=this.array;if(!(this instanceof W)){throw new TypeError("Not an ArrayIterator")}if(typeof t!=="undefined"){var r=le.ToLength(t.length);if(e<r){var n=this.kind;var o;if(n==="key"){o=e}else if(n==="value"){o=t[e]}else if(n==="entry"){o=[e,t[e]]}this.i=e+1;return Ze(o)}}this.array=void 0;return Ze()}});xe(W.prototype);var et=Array.of===Qe.of||function(){var e=function Foo(e){this.length=e};e.prototype=[];var t=Array.of.apply(e,[1,2]);return t instanceof e&&t.length===2}();if(!et){oe(Array,"of",Qe.of)}var tt={copyWithin:function copyWithin(e,t){var r=le.ToObject(this);var n=le.ToLength(r.length);var o=le.ToInteger(e);var i=le.ToInteger(t);var a=o<0?_(n+o,0):R(o,n);var u=i<0?_(n+i,0):R(i,n);var f;if(arguments.length>2){f=arguments[2]}var s=typeof f==="undefined"?n:le.ToInteger(f);var c=s<0?_(n+s,0):R(s,n);var l=R(c-u,n-a);var p=1;if(u<a&&a<u+l){p=-1;u+=l-1;a+=l-1}while(l>0){if(u in r){r[a]=r[u]}else{delete r[a]}u+=p;a+=p;l-=1}return r},fill:function fill(e){var t;if(arguments.length>1){t=arguments[1]}var r;if(arguments.length>2){r=arguments[2]}var n=le.ToObject(this);var o=le.ToLength(n.length);t=le.ToInteger(typeof t==="undefined"?0:t);r=le.ToInteger(typeof r==="undefined"?o:r);var i=t<0?_(o+t,0):R(t,o);var a=r<0?o+r:r;for(var u=i;u<o&&u<a;++u){n[u]=e}return n},find:function find(e){var r=le.ToObject(this);var n=le.ToLength(r.length);if(!le.IsCallable(e)){throw new TypeError("Array#find: predicate must be a function")}var o=arguments.length>1?arguments[1]:null;for(var i=0,a;i<n;i++){a=r[i];if(o){if(t(e,o,a,i,r)){return a}}else if(e(a,i,r)){return a}}},findIndex:function findIndex(e){var r=le.ToObject(this);var n=le.ToLength(r.length);if(!le.IsCallable(e)){throw new TypeError("Array#findIndex: predicate must be a function")}var o=arguments.length>1?arguments[1]:null;for(var i=0;i<n;i++){if(o){if(t(e,o,r[i],i,r)){return i}}else if(e(r[i],i,r)){return i}}return-1},keys:function keys(){return new W(this,"key")},values:function values(){return new W(this,"value")},entries:function entries(){return new W(this,"entry")}};if(Array.prototype.keys&&!le.IsCallable([1].keys().next)){delete Array.prototype.keys}if(Array.prototype.entries&&!le.IsCallable([1].entries().next)){delete Array.prototype.entries}if(Array.prototype.keys&&Array.prototype.entries&&!Array.prototype.values&&Array.prototype[ae]){b(Array.prototype,{values:Array.prototype[ae]});if(ne.symbol(J.unscopables)){Array.prototype[J.unscopables].values=true}}if(c&&Array.prototype.values&&Array.prototype.values.name!=="values"){var rt=Array.prototype.values;oe(Array.prototype,"values",function values(){return le.Call(rt,this,arguments)});h(Array.prototype,ae,Array.prototype.values,true)}b(Array.prototype,tt);if(1/[true].indexOf(true,-0)<0){h(Array.prototype,"indexOf",function indexOf(e){var t=E(this,arguments);if(t===0&&1/t<0){return 0}return t},true)}xe(Array.prototype,function(){return this.values()});if(Object.getPrototypeOf){var nt=Object.getPrototypeOf([].values());if(nt){xe(nt)}}var ot=function(){return a(function(){return Array.from({length:-1}).length===0})}();var it=function(){var e=Array.from([0].entries());return e.length===1&&r(e[0])&&e[0][0]===0&&e[0][1]===0}();if(!ot||!it){oe(Array,"from",Qe.from)}var at=function(){return a(function(){return Array.from([0],void 0)})}();if(!at){var ut=Array.from;oe(Array,"from",function from(e){if(arguments.length>1&&typeof arguments[1]!=="undefined"){return le.Call(ut,this,arguments)}return t(ut,this,e)})}var ft=-(Math.pow(2,32)-1);var st=function(e,r){var n={length:ft};n[r?(n.length>>>0)-1:0]=true;return a(function(){t(e,n,function(){throw new RangeError("should not reach here")},[]);return true})};if(!st(Array.prototype.forEach)){var ct=Array.prototype.forEach;oe(Array.prototype,"forEach",function forEach(e){return le.Call(ct,this.length>=0?this:[],arguments)})}if(!st(Array.prototype.map)){var lt=Array.prototype.map;oe(Array.prototype,"map",function map(e){return le.Call(lt,this.length>=0?this:[],arguments)})}if(!st(Array.prototype.filter)){var pt=Array.prototype.filter;oe(Array.prototype,"filter",function filter(e){return le.Call(pt,this.length>=0?this:[],arguments)})}if(!st(Array.prototype.some)){var vt=Array.prototype.some;oe(Array.prototype,"some",function some(e){return le.Call(vt,this.length>=0?this:[],arguments)})}if(!st(Array.prototype.every)){var yt=Array.prototype.every;oe(Array.prototype,"every",function every(e){return le.Call(yt,this.length>=0?this:[],arguments)})}if(!st(Array.prototype.reduce)){var ht=Array.prototype.reduce;oe(Array.prototype,"reduce",function reduce(e){return le.Call(ht,this.length>=0?this:[],arguments)})}if(!st(Array.prototype.reduceRight,true)){var bt=Array.prototype.reduceRight;oe(Array.prototype,"reduceRight",function reduceRight(e){return le.Call(bt,this.length>=0?this:[],arguments)})}var gt=Number("0o10")!==8;var dt=Number("0b10")!==2;var mt=y($e,function(e){return Number(e+0+e)===0});if(gt||dt||mt){var Ot=Number;var wt=/^0b[01]+$/i;var jt=/^0o[0-7]+$/i;var St=wt.test.bind(wt);var Tt=jt.test.bind(jt);var It=function(e,t){var r;if(typeof e.valueOf==="function"){r=e.valueOf();if(ne.primitive(r)){return r}}if(typeof e.toString==="function"){r=e.toString();if(ne.primitive(r)){return r}}throw new TypeError("No default value")};var Et=Je.test.bind(Je);var Pt=Xe.test.bind(Xe);var Ct=function(){var e=function Number(t){var r;if(arguments.length>0){r=ne.primitive(t)?t:It(t,"number")}else{r=0}if(typeof r==="string"){r=le.Call(Ue,r);if(St(r)){r=parseInt(C(r,2),2)}else if(Tt(r)){r=parseInt(C(r,2),8)}else if(Et(r)||Pt(r)){r=NaN}}var n=this;var o=a(function(){Ot.prototype.valueOf.call(n);return true});if(n instanceof e&&!o){return new Ot(r)}return Ot(r)};return e}();Pe(Ot,Ct,{});b(Ct,{NaN:Ot.NaN,MAX_VALUE:Ot.MAX_VALUE,MIN_VALUE:Ot.MIN_VALUE,NEGATIVE_INFINITY:Ot.NEGATIVE_INFINITY,POSITIVE_INFINITY:Ot.POSITIVE_INFINITY});Number=Ct;m.redefine(S,"Number",Ct)}var Mt=Math.pow(2,53)-1;b(Number,{MAX_SAFE_INTEGER:Mt,MIN_SAFE_INTEGER:-Mt,EPSILON:2.220446049250313e-16,parseInt:S.parseInt,parseFloat:S.parseFloat,isFinite:Z,isInteger:function isInteger(e){return Z(e)&&le.ToInteger(e)===e},isSafeInteger:function isSafeInteger(e){return Number.isInteger(e)&&L(e)<=Number.MAX_SAFE_INTEGER},isNaN:K});h(Number,"parseInt",S.parseInt,Number.parseInt!==S.parseInt);if([,1].find(function(){return true})===1){oe(Array.prototype,"find",tt.find)}if([,1].findIndex(function(){return true})!==0){oe(Array.prototype,"findIndex",tt.findIndex)}var xt=Function.bind.call(Function.bind,Object.prototype.propertyIsEnumerable);var Nt=function ensureEnumerable(e,t){if(s&&xt(e,t)){Object.defineProperty(e,t,{enumerable:false})}};var At=function sliceArgs(){var e=Number(this);var t=arguments.length;var r=t-e;var n=new Array(r<0?0:r);for(var o=e;o<t;++o){n[o-e]=arguments[o]}return n};var _t=function assignTo(e){return function assignToSource(t,r){t[r]=e[r];return t}};var Rt=function(e,t){var r=n(Object(t));var o;if(le.IsCallable(Object.getOwnPropertySymbols)){o=v(Object.getOwnPropertySymbols(Object(t)),xt(t))}return p(P(r,o||[]),_t(t),e)};var kt={assign:function(e,t){var r=le.ToObject(e,"Cannot convert undefined or null to object");return p(le.Call(At,1,arguments),Rt,r)},is:function is(e,t){return le.SameValue(e,t)}};var Lt=Object.assign&&Object.preventExtensions&&function(){var e=Object.preventExtensions({1:2});try{Object.assign(e,"xy")}catch(t){return e[1]==="y"}}();if(Lt){oe(Object,"assign",kt.assign)}b(Object,kt);if(s){var Ft={setPrototypeOf:function(e){var r;var n=function(e,t){if(!le.TypeIsObject(e)){throw new TypeError("cannot set prototype on a non-object")}if(!(t===null||le.TypeIsObject(t))){throw new TypeError("can only set prototype to an object or null"+t)}};var o=function(e,o){n(e,o);t(r,e,o);return e};try{r=e.getOwnPropertyDescriptor(e.prototype,"__proto__").set;t(r,{},null)}catch(i){if(e.prototype!=={}.__proto__){return}r=function(e){this.__proto__=e};o.polyfill=o(o({},null),e.prototype)instanceof e}return o}(Object)};b(Object,Ft)}if(Object.setPrototypeOf&&Object.getPrototypeOf&&Object.getPrototypeOf(Object.setPrototypeOf({},null))!==null&&Object.getPrototypeOf(Object.create(null))===null){(function(){var e=Object.create(null);var t=Object.getPrototypeOf;var r=Object.setPrototypeOf;Object.getPrototypeOf=function(r){var n=t(r);return n===e?null:n};Object.setPrototypeOf=function(t,n){var o=n===null?e:n;return r(t,o)};Object.setPrototypeOf.polyfill=false})()}var Dt=!i(function(){return Object.keys("foo")});if(!Dt){var zt=Object.keys;oe(Object,"keys",function keys(e){return zt(le.ToObject(e))});n=Object.keys}var qt=i(function(){return Object.keys(/a/g)});if(qt){var Wt=Object.keys;oe(Object,"keys",function keys(e){if(ne.regex(e)){var t=[];for(var r in e){if(q(e,r)){M(t,r)}}return t}return Wt(e)});n=Object.keys}if(Object.getOwnPropertyNames){var Gt=!i(function(){return Object.getOwnPropertyNames("foo")});if(!Gt){var Ht=typeof window==="object"?Object.getOwnPropertyNames(window):[];var Vt=Object.getOwnPropertyNames;oe(Object,"getOwnPropertyNames",function getOwnPropertyNames(e){var t=le.ToObject(e);if(g(t)==="[object Window]"){try{return Vt(t)}catch(r){return P([],Ht)}}return Vt(t)})}}if(Object.getOwnPropertyDescriptor){var Bt=!i(function(){return Object.getOwnPropertyDescriptor("foo","bar")});if(!Bt){var Ut=Object.getOwnPropertyDescriptor;oe(Object,"getOwnPropertyDescriptor",function getOwnPropertyDescriptor(e,t){return Ut(le.ToObject(e),t)})}}if(Object.seal){var $t=!i(function(){return Object.seal("foo")});if(!$t){var Jt=Object.seal;oe(Object,"seal",function seal(e){if(!le.TypeIsObject(e)){return e}return Jt(e)})}}if(Object.isSealed){var Xt=!i(function(){return Object.isSealed("foo")});if(!Xt){var Kt=Object.isSealed;oe(Object,"isSealed",function isSealed(e){if(!le.TypeIsObject(e)){return true}return Kt(e)})}}if(Object.freeze){var Zt=!i(function(){return Object.freeze("foo")});if(!Zt){var Yt=Object.freeze;oe(Object,"freeze",function freeze(e){if(!le.TypeIsObject(e)){return e}return Yt(e)})}}if(Object.isFrozen){var Qt=!i(function(){return Object.isFrozen("foo")});if(!Qt){var er=Object.isFrozen;oe(Object,"isFrozen",function isFrozen(e){if(!le.TypeIsObject(e)){return true}return er(e)})}}if(Object.preventExtensions){var tr=!i(function(){return Object.preventExtensions("foo")});if(!tr){var rr=Object.preventExtensions;oe(Object,"preventExtensions",function preventExtensions(e){if(!le.TypeIsObject(e)){return e}return rr(e)})}}if(Object.isExtensible){var nr=!i(function(){return Object.isExtensible("foo")});if(!nr){var or=Object.isExtensible;oe(Object,"isExtensible",function isExtensible(e){if(!le.TypeIsObject(e)){return false}return or(e)})}}if(Object.getPrototypeOf){var ir=!i(function(){return Object.getPrototypeOf("foo")});if(!ir){var ar=Object.getPrototypeOf;oe(Object,"getPrototypeOf",function getPrototypeOf(e){return ar(le.ToObject(e))})}}var ur=s&&function(){var e=Object.getOwnPropertyDescriptor(RegExp.prototype,"flags");return e&&le.IsCallable(e.get)}();if(s&&!ur){var fr=function flags(){if(!le.TypeIsObject(this)){throw new TypeError("Method called on incompatible type: must be an object.")}var e="";if(this.global){e+="g"}if(this.ignoreCase){e+="i"}if(this.multiline){e+="m"}if(this.unicode){e+="u"}if(this.sticky){e+="y"}return e};m.getter(RegExp.prototype,"flags",fr)}var sr=s&&a(function(){return String(new RegExp(/a/g,"i"))==="/a/i"});var cr=ie&&s&&function(){var e=/./;e[J.match]=false;return RegExp(e)===e}();var lr=a(function(){return RegExp.prototype.toString.call({source:"abc"})==="/abc/"});var pr=lr&&a(function(){return RegExp.prototype.toString.call({source:"a",flags:"b"})==="/a/b"});if(!lr||!pr){var vr=RegExp.prototype.toString;h(RegExp.prototype,"toString",function toString(){var e=le.RequireObjectCoercible(this);if(ne.regex(e)){return t(vr,e)}var r=fe(e.source);var n=fe(e.flags);return"/"+r+"/"+n},true);m.preserveToString(RegExp.prototype.toString,vr);RegExp.prototype.toString.prototype=void 0}if(s&&(!sr||cr)){var yr=Object.getOwnPropertyDescriptor(RegExp.prototype,"flags").get;var hr=Object.getOwnPropertyDescriptor(RegExp.prototype,"source")||{};var br=function(){return this.source};var gr=le.IsCallable(hr.get)?hr.get:br;var dr=RegExp;var mr=function(){return function RegExp(e,t){var r=le.IsRegExp(e);var n=this instanceof RegExp;if(!n&&r&&typeof t==="undefined"&&e.constructor===RegExp){return e}var o=e;var i=t;if(ne.regex(e)){o=le.Call(gr,e);i=typeof t==="undefined"?le.Call(yr,e):t;return new RegExp(o,i)}else if(r){o=e.source;i=typeof t==="undefined"?e.flags:t}return new dr(e,t)}}();Pe(dr,mr,{$input:true});RegExp=mr;m.redefine(S,"RegExp",mr)}if(s){var Or={input:"$_",lastMatch:"$&",lastParen:"$+",leftContext:"$`",rightContext:"$'"};l(n(Or),function(e){if(e in RegExp&&!(Or[e]in RegExp)){m.getter(RegExp,Or[e],function get(){return RegExp[e]})}})}Me(RegExp);var wr=1/Number.EPSILON;var jr=function roundTiesToEven(e){return e+wr-wr};var Sr=Math.pow(2,-23);var Tr=Math.pow(2,127)*(2-Sr);var Ir=Math.pow(2,-126);var Er=Math.E;var Pr=Math.LOG2E;var Cr=Math.LOG10E;var Mr=Number.prototype.clz;delete Number.prototype.clz;var xr={acosh:function acosh(e){var t=Number(e);if(K(t)||e<1){return NaN}if(t===1){return 0}if(t===Infinity){return t}var r=1/(t*t);if(t<2){return Q(t-1+z(1-r)*t)}var n=t/2;return Q(n+z(1-r)*n-1)+1/Pr},asinh:function asinh(e){var t=Number(e);if(t===0||!T(t)){return t}var r=L(t);var n=r*r;var o=Y(t);if(r<1){return o*Q(r+n/(z(n+1)+1))}return o*(Q(r/2+z(1+1/n)*r/2-1)+1/Pr)},atanh:function atanh(e){var t=Number(e);if(t===0){return t}if(t===-1){return-Infinity}if(t===1){return Infinity}if(K(t)||t<-1||t>1){return NaN}var r=L(t);return Y(t)*Q(2*r/(1-r))/2},cbrt:function cbrt(e){var t=Number(e);if(t===0){return t}var r=t<0;var n;if(r){t=-t}if(t===Infinity){n=Infinity}else{n=F(D(t)/3);n=(t/(n*n)+2*n)/3}return r?-n:n},clz32:function clz32(e){var t=Number(e);var r=le.ToUint32(t);if(r===0){return 32}return Mr?le.Call(Mr,r):31-k(D(r+.5)*Pr)},cosh:function cosh(e){var t=Number(e);if(t===0){return 1}if(K(t)){return NaN}if(!T(t)){return Infinity}var r=F(L(t)-1);return(r+1/(r*Er*Er))*(Er/2)},expm1:function expm1(e){var t=Number(e);if(t===-Infinity){return-1}if(!T(t)||t===0){return t}if(L(t)>.5){return F(t)-1}var r=t;var n=0;var o=1;while(n+r!==n){n+=r;o+=1;r*=t/o}return n},hypot:function hypot(e,t){var r=0;var n=0;for(var o=0;o<arguments.length;++o){var i=L(Number(arguments[o]));if(n<i){r*=n/i*(n/i);r+=1;n=i}else{r+=i>0?i/n*(i/n):i}}return n===Infinity?Infinity:n*z(r)},log2:function log2(e){return D(e)*Pr},log10:function log10(e){return D(e)*Cr},log1p:Q,sign:Y,sinh:function sinh(e){var t=Number(e);if(!T(t)||t===0){return t}var r=L(t);if(r<1){var n=Math.expm1(r);return Y(t)*n*(1+1/(n+1))/2}var o=F(r-1);return Y(t)*(o-1/(o*Er*Er))*(Er/2)},tanh:function tanh(e){var t=Number(e);if(K(t)||t===0){return t}if(t>=20){return 1}if(t<=-20){return-1}return(Math.expm1(t)-Math.expm1(-t))/(F(t)+F(-t))},trunc:function trunc(e){var t=Number(e);return t<0?-k(-t):k(t)},imul:function imul(e,t){var r=le.ToUint32(e);var n=le.ToUint32(t);var o=r>>>16&65535;var i=r&65535;var a=n>>>16&65535;var u=n&65535;return i*u+(o*u+i*a<<16>>>0)|0},fround:function fround(e){var t=Number(e);if(t===0||t===Infinity||t===-Infinity||K(t)){return t}var r=Y(t);var n=L(t);if(n<Ir){return r*jr(n/Ir/Sr)*Ir*Sr}var o=(1+Sr/Number.EPSILON)*n;var i=o-(o-n);if(i>Tr||K(i)){return r*Infinity}return r*i}};var Nr=function withinULPDistance(e,t,r){return L(1-e/t)/Number.EPSILON<(r||8)};b(Math,xr);h(Math,"sinh",xr.sinh,Math.sinh(710)===Infinity);h(Math,"cosh",xr.cosh,Math.cosh(710)===Infinity);h(Math,"log1p",xr.log1p,Math.log1p(-1e-17)!==-1e-17);h(Math,"asinh",xr.asinh,Math.asinh(-1e7)!==-Math.asinh(1e7));
h(Math,"asinh",xr.asinh,Math.asinh(1e300)===Infinity);h(Math,"atanh",xr.atanh,Math.atanh(1e-300)===0);h(Math,"tanh",xr.tanh,Math.tanh(-2e-17)!==-2e-17);h(Math,"acosh",xr.acosh,Math.acosh(Number.MAX_VALUE)===Infinity);h(Math,"acosh",xr.acosh,!Nr(Math.acosh(1+Number.EPSILON),Math.sqrt(2*Number.EPSILON)));h(Math,"cbrt",xr.cbrt,!Nr(Math.cbrt(1e-300),1e-100));h(Math,"sinh",xr.sinh,Math.sinh(-2e-17)!==-2e-17);var Ar=Math.expm1(10);h(Math,"expm1",xr.expm1,Ar>22025.465794806718||Ar<22025.465794806718);h(Math,"hypot",xr.hypot,Math.hypot(Infinity,NaN)!==Infinity);var _r=Math.round;var Rr=Math.round(.5-Number.EPSILON/4)===0&&Math.round(-.5+Number.EPSILON/3.99)===1;var kr=wr+1;var Lr=2*wr-1;var Fr=[kr,Lr].every(function(e){return Math.round(e)===e});h(Math,"round",function round(e){var t=k(e);var r=t===-1?-0:t+1;return e-t<.5?t:r},!Rr||!Fr);m.preserveToString(Math.round,_r);var Dr=Math.imul;if(Math.imul(4294967295,5)!==-5){Math.imul=xr.imul;m.preserveToString(Math.imul,Dr)}if(Math.imul.length!==2){oe(Math,"imul",function imul(e,t){return le.Call(Dr,Math,arguments)})}var zr=function(){var e=S.setTimeout;if(typeof e!=="function"&&typeof e!=="object"){return}le.IsPromise=function(e){if(!le.TypeIsObject(e)){return false}if(typeof e._promise==="undefined"){return false}return true};var r=function(e){if(!le.IsConstructor(e)){throw new TypeError("Bad promise constructor")}var t=this;var r=function(e,r){if(t.resolve!==void 0||t.reject!==void 0){throw new TypeError("Bad Promise implementation!")}t.resolve=e;t.reject=r};t.resolve=void 0;t.reject=void 0;t.promise=new e(r);if(!(le.IsCallable(t.resolve)&&le.IsCallable(t.reject))){throw new TypeError("Bad promise constructor")}};var n;if(typeof window!=="undefined"&&le.IsCallable(window.postMessage)){n=function(){var e=[];var t="zero-timeout-message";var r=function(r){M(e,r);window.postMessage(t,"*")};var n=function(r){if(r.source===window&&r.data===t){r.stopPropagation();if(e.length===0){return}var n=A(e);n()}};window.addEventListener("message",n,true);return r}}var o=function(){var e=S.Promise;var t=e&&e.resolve&&e.resolve();return t&&function(e){return t.then(e)}};var i=le.IsCallable(S.setImmediate)?S.setImmediate:typeof process==="object"&&process.nextTick?process.nextTick:o()||(le.IsCallable(n)?n():function(t){e(t,0)});var a=function(e){return e};var u=function(e){throw e};var f=0;var s=1;var c=2;var l=0;var p=1;var v=2;var y={};var h=function(e,t,r){i(function(){g(e,t,r)})};var g=function(e,t,r){var n,o;if(t===y){return e(r)}try{n=e(r);o=t.resolve}catch(i){n=i;o=t.reject}o(n)};var d=function(e,t){var r=e._promise;var n=r.reactionLength;if(n>0){h(r.fulfillReactionHandler0,r.reactionCapability0,t);r.fulfillReactionHandler0=void 0;r.rejectReactions0=void 0;r.reactionCapability0=void 0;if(n>1){for(var o=1,i=0;o<n;o++,i+=3){h(r[i+l],r[i+v],t);e[i+l]=void 0;e[i+p]=void 0;e[i+v]=void 0}}}r.result=t;r.state=s;r.reactionLength=0};var m=function(e,t){var r=e._promise;var n=r.reactionLength;if(n>0){h(r.rejectReactionHandler0,r.reactionCapability0,t);r.fulfillReactionHandler0=void 0;r.rejectReactions0=void 0;r.reactionCapability0=void 0;if(n>1){for(var o=1,i=0;o<n;o++,i+=3){h(r[i+p],r[i+v],t);e[i+l]=void 0;e[i+p]=void 0;e[i+v]=void 0}}}r.result=t;r.state=c;r.reactionLength=0};var O=function(e){var t=false;var r=function(r){var n;if(t){return}t=true;if(r===e){return m(e,new TypeError("Self resolution"))}if(!le.TypeIsObject(r)){return d(e,r)}try{n=r.then}catch(o){return m(e,o)}if(!le.IsCallable(n)){return d(e,r)}i(function(){j(e,r,n)})};var n=function(r){if(t){return}t=true;return m(e,r)};return{resolve:r,reject:n}};var w=function(e,r,n,o){if(e===I){t(e,r,n,o,y)}else{t(e,r,n,o)}};var j=function(e,t,r){var n=O(e);var o=n.resolve;var i=n.reject;try{w(r,t,o,i)}catch(a){i(a)}};var T,I;var E=function(){var e=function Promise(t){if(!(this instanceof e)){throw new TypeError('Constructor Promise requires "new"')}if(this&&this._promise){throw new TypeError("Bad construction")}if(!le.IsCallable(t)){throw new TypeError("not a valid resolver")}var r=_e(this,e,T,{_promise:{result:void 0,state:f,reactionLength:0,fulfillReactionHandler0:void 0,rejectReactionHandler0:void 0,reactionCapability0:void 0}});var n=O(r);var o=n.reject;try{t(n.resolve,o)}catch(i){o(i)}return r};return e}();T=E.prototype;var P=function(e,t,r,n){var o=false;return function(i){if(o){return}o=true;t[e]=i;if(--n.count===0){var a=r.resolve;a(t)}}};var C=function(e,t,r){var n=e.iterator;var o=[];var i={count:1};var a,u;var f=0;while(true){try{a=le.IteratorStep(n);if(a===false){e.done=true;break}u=a.value}catch(s){e.done=true;throw s}o[f]=void 0;var c=t.resolve(u);var l=P(f,o,r,i);i.count+=1;w(c.then,c,l,r.reject);f+=1}if(--i.count===0){var p=r.resolve;p(o)}return r.promise};var x=function(e,t,r){var n=e.iterator;var o,i,a;while(true){try{o=le.IteratorStep(n);if(o===false){e.done=true;break}i=o.value}catch(u){e.done=true;throw u}a=t.resolve(i);w(a.then,a,r.resolve,r.reject)}return r.promise};b(E,{all:function all(e){var t=this;if(!le.TypeIsObject(t)){throw new TypeError("Promise is not object")}var n=new r(t);var o,i;try{o=le.GetIterator(e);i={iterator:o,done:false};return C(i,t,n)}catch(a){var u=a;if(i&&!i.done){try{le.IteratorClose(o,true)}catch(f){u=f}}var s=n.reject;s(u);return n.promise}},race:function race(e){var t=this;if(!le.TypeIsObject(t)){throw new TypeError("Promise is not object")}var n=new r(t);var o,i;try{o=le.GetIterator(e);i={iterator:o,done:false};return x(i,t,n)}catch(a){var u=a;if(i&&!i.done){try{le.IteratorClose(o,true)}catch(f){u=f}}var s=n.reject;s(u);return n.promise}},reject:function reject(e){var t=this;if(!le.TypeIsObject(t)){throw new TypeError("Bad promise constructor")}var n=new r(t);var o=n.reject;o(e);return n.promise},resolve:function resolve(e){var t=this;if(!le.TypeIsObject(t)){throw new TypeError("Bad promise constructor")}if(le.IsPromise(e)){var n=e.constructor;if(n===t){return e}}var o=new r(t);var i=o.resolve;i(e);return o.promise}});b(T,{"catch":function(e){return this.then(null,e)},then:function then(e,t){var n=this;if(!le.IsPromise(n)){throw new TypeError("not a promise")}var o=le.SpeciesConstructor(n,E);var i;var b=arguments.length>2&&arguments[2]===y;if(b&&o===E){i=y}else{i=new r(o)}var g=le.IsCallable(e)?e:a;var d=le.IsCallable(t)?t:u;var m=n._promise;var O;if(m.state===f){if(m.reactionLength===0){m.fulfillReactionHandler0=g;m.rejectReactionHandler0=d;m.reactionCapability0=i}else{var w=3*(m.reactionLength-1);m[w+l]=g;m[w+p]=d;m[w+v]=i}m.reactionLength+=1}else if(m.state===s){O=m.result;h(g,i,O)}else if(m.state===c){O=m.result;h(d,i,O)}else{throw new TypeError("unexpected Promise state")}return i.promise}});y=new r(E);I=T.then;return E}();if(S.Promise){delete S.Promise.accept;delete S.Promise.defer;delete S.Promise.prototype.chain}if(typeof zr==="function"){b(S,{Promise:zr});var qr=w(S.Promise,function(e){return e.resolve(42).then(function(){})instanceof e});var Wr=!i(function(){return S.Promise.reject(42).then(null,5).then(null,G)});var Gr=i(function(){return S.Promise.call(3,G)});var Hr=function(e){var t=e.resolve(5);t.constructor={};var r=e.resolve(t);try{r.then(null,G).then(null,G)}catch(n){return true}return t===r}(S.Promise);var Vr=s&&function(){var e=0;var t=Object.defineProperty({},"then",{get:function(){e+=1}});Promise.resolve(t);return e===1}();var Br=function BadResolverPromise(e){var t=new Promise(e);e(3,function(){});this.then=t.then;this.constructor=BadResolverPromise};Br.prototype=Promise.prototype;Br.all=Promise.all;var Ur=a(function(){return!!Br.all([1,2])});if(!qr||!Wr||!Gr||Hr||!Vr||Ur){Promise=zr;oe(S,"Promise",zr)}if(Promise.all.length!==1){var $r=Promise.all;oe(Promise,"all",function all(e){return le.Call($r,this,arguments)})}if(Promise.race.length!==1){var Jr=Promise.race;oe(Promise,"race",function race(e){return le.Call(Jr,this,arguments)})}if(Promise.resolve.length!==1){var Xr=Promise.resolve;oe(Promise,"resolve",function resolve(e){return le.Call(Xr,this,arguments)})}if(Promise.reject.length!==1){var Kr=Promise.reject;oe(Promise,"reject",function reject(e){return le.Call(Kr,this,arguments)})}Nt(Promise,"all");Nt(Promise,"race");Nt(Promise,"resolve");Nt(Promise,"reject");Me(Promise)}var Zr=function(e){var t=n(p(e,function(e,t){e[t]=true;return e},{}));return e.join(":")===t.join(":")};var Yr=Zr(["z","a","bb"]);var Qr=Zr(["z",1,"a","3",2]);if(s){var en=function fastkey(e,t){if(!t&&!Yr){return null}if(ce(e)){return"^"+le.ToString(e)}else if(typeof e==="string"){return"$"+e}else if(typeof e==="number"){if(!Qr){return"n"+e}return e}else if(typeof e==="boolean"){return"b"+e}return null};var tn=function emptyObject(){return Object.create?Object.create(null):{}};var rn=function addIterableToMap(e,n,o){if(r(o)||ne.string(o)){l(o,function(e){if(!le.TypeIsObject(e)){throw new TypeError("Iterator value "+e+" is not an entry object")}n.set(e[0],e[1])})}else if(o instanceof e){t(e.prototype.forEach,o,function(e,t){n.set(t,e)})}else{var i,a;if(!ce(o)){a=n.set;if(!le.IsCallable(a)){throw new TypeError("bad map")}i=le.GetIterator(o)}if(typeof i!=="undefined"){while(true){var u=le.IteratorStep(i);if(u===false){break}var f=u.value;try{if(!le.TypeIsObject(f)){throw new TypeError("Iterator value "+f+" is not an entry object")}t(a,n,f[0],f[1])}catch(s){le.IteratorClose(i,true);throw s}}}}};var nn=function addIterableToSet(e,n,o){if(r(o)||ne.string(o)){l(o,function(e){n.add(e)})}else if(o instanceof e){t(e.prototype.forEach,o,function(e){n.add(e)})}else{var i,a;if(!ce(o)){a=n.add;if(!le.IsCallable(a)){throw new TypeError("bad set")}i=le.GetIterator(o)}if(typeof i!=="undefined"){while(true){var u=le.IteratorStep(i);if(u===false){break}var f=u.value;try{t(a,n,f)}catch(s){le.IteratorClose(i,true);throw s}}}}};var on={Map:function(){var e={};var r=function MapEntry(e,t){this.key=e;this.value=t;this.next=null;this.prev=null};r.prototype.isRemoved=function isRemoved(){return this.key===e};var n=function isMap(e){return!!e._es6map};var o=function requireMapSlot(e,t){if(!le.TypeIsObject(e)||!n(e)){throw new TypeError("Method Map.prototype."+t+" called on incompatible receiver "+le.ToString(e))}};var i=function MapIterator(e,t){o(e,"[[MapIterator]]");h(this,"head",e._head);h(this,"i",this.head);h(this,"kind",t)};i.prototype={isMapIterator:true,next:function next(){if(!this.isMapIterator){throw new TypeError("Not a MapIterator")}var e=this.i;var t=this.kind;var r=this.head;if(typeof this.i==="undefined"){return Ze()}while(e.isRemoved()&&e!==r){e=e.prev}var n;while(e.next!==r){e=e.next;if(!e.isRemoved()){if(t==="key"){n=e.key}else if(t==="value"){n=e.value}else{n=[e.key,e.value]}this.i=e;return Ze(n)}}this.i=void 0;return Ze()}};xe(i.prototype);var a;var u=function Map(){if(!(this instanceof Map)){throw new TypeError('Constructor Map requires "new"')}if(this&&this._es6map){throw new TypeError("Bad construction")}var e=_e(this,Map,a,{_es6map:true,_head:null,_map:H?new H:null,_size:0,_storage:tn()});var t=new r(null,null);t.next=t.prev=t;e._head=t;if(arguments.length>0){rn(Map,e,arguments[0])}return e};a=u.prototype;m.getter(a,"size",function(){if(typeof this._size==="undefined"){throw new TypeError("size method called on incompatible Map")}return this._size});b(a,{get:function get(e){o(this,"get");var t;var r=en(e,true);if(r!==null){t=this._storage[r];if(t){return t.value}return}if(this._map){t=B.call(this._map,e);if(t){return t.value}return}var n=this._head;var i=n;while((i=i.next)!==n){if(le.SameValueZero(i.key,e)){return i.value}}},has:function has(e){o(this,"has");var t=en(e,true);if(t!==null){return typeof this._storage[t]!=="undefined"}if(this._map){return U.call(this._map,e)}var r=this._head;var n=r;while((n=n.next)!==r){if(le.SameValueZero(n.key,e)){return true}}return false},set:function set(e,t){o(this,"set");var n=this._head;var i=n;var a;var u=en(e,true);if(u!==null){if(typeof this._storage[u]!=="undefined"){this._storage[u].value=t;return this}a=this._storage[u]=new r(e,t);i=n.prev}else if(this._map){if(U.call(this._map,e)){B.call(this._map,e).value=t}else{a=new r(e,t);$.call(this._map,e,a);i=n.prev}}while((i=i.next)!==n){if(le.SameValueZero(i.key,e)){i.value=t;return this}}a=a||new r(e,t);if(le.SameValue(-0,e)){a.key=+0}a.next=this._head;a.prev=this._head.prev;a.prev.next=a;a.next.prev=a;this._size+=1;return this},"delete":function(t){o(this,"delete");var r=this._head;var n=r;var i=en(t,true);if(i!==null){if(typeof this._storage[i]==="undefined"){return false}n=this._storage[i].prev;delete this._storage[i]}else if(this._map){if(!U.call(this._map,t)){return false}n=B.call(this._map,t).prev;V.call(this._map,t)}while((n=n.next)!==r){if(le.SameValueZero(n.key,t)){n.key=e;n.value=e;n.prev.next=n.next;n.next.prev=n.prev;this._size-=1;return true}}return false},clear:function clear(){o(this,"clear");this._map=H?new H:null;this._size=0;this._storage=tn();var t=this._head;var r=t;var n=r.next;while((r=n)!==t){r.key=e;r.value=e;n=r.next;r.next=r.prev=t}t.next=t.prev=t},keys:function keys(){o(this,"keys");return new i(this,"key")},values:function values(){o(this,"values");return new i(this,"value")},entries:function entries(){o(this,"entries");return new i(this,"key+value")},forEach:function forEach(e){o(this,"forEach");var r=arguments.length>1?arguments[1]:null;var n=this.entries();for(var i=n.next();!i.done;i=n.next()){if(r){t(e,r,i.value[1],i.value[0],this)}else{e(i.value[1],i.value[0],this)}}}});xe(a,a.entries);return u}(),Set:function(){var e=function isSet(e){return e._es6set&&typeof e._storage!=="undefined"};var r=function requireSetSlot(t,r){if(!le.TypeIsObject(t)||!e(t)){throw new TypeError("Set.prototype."+r+" called on incompatible receiver "+le.ToString(t))}};var o;var i=function Set(){if(!(this instanceof Set)){throw new TypeError('Constructor Set requires "new"')}if(this&&this._es6set){throw new TypeError("Bad construction")}var e=_e(this,Set,o,{_es6set:true,"[[SetData]]":null,_storage:tn()});if(!e._es6set){throw new TypeError("bad set")}if(arguments.length>0){nn(Set,e,arguments[0])}return e};o=i.prototype;var a=function(e){var t=e;if(t==="^null"){return null}else if(t==="^undefined"){return void 0}var r=t.charAt(0);if(r==="$"){return C(t,1)}else if(r==="n"){return+C(t,1)}else if(r==="b"){return t==="btrue"}return+t};var u=function ensureMap(e){if(!e["[[SetData]]"]){var t=new on.Map;e["[[SetData]]"]=t;l(n(e._storage),function(e){var r=a(e);t.set(r,r)});e["[[SetData]]"]=t}e._storage=null};m.getter(i.prototype,"size",function(){r(this,"size");if(this._storage){return n(this._storage).length}u(this);return this["[[SetData]]"].size});b(i.prototype,{has:function has(e){r(this,"has");var t;if(this._storage&&(t=en(e))!==null){return!!this._storage[t]}u(this);return this["[[SetData]]"].has(e)},add:function add(e){r(this,"add");var t;if(this._storage&&(t=en(e))!==null){this._storage[t]=true;return this}u(this);this["[[SetData]]"].set(e,e);return this},"delete":function(e){r(this,"delete");var t;if(this._storage&&(t=en(e))!==null){var n=q(this._storage,t);return delete this._storage[t]&&n}u(this);return this["[[SetData]]"]["delete"](e)},clear:function clear(){r(this,"clear");if(this._storage){this._storage=tn()}if(this["[[SetData]]"]){this["[[SetData]]"].clear()}},values:function values(){r(this,"values");u(this);return new f(this["[[SetData]]"].values())},entries:function entries(){r(this,"entries");u(this);return new f(this["[[SetData]]"].entries())},forEach:function forEach(e){r(this,"forEach");var n=arguments.length>1?arguments[1]:null;var o=this;u(o);this["[[SetData]]"].forEach(function(r,i){if(n){t(e,n,i,i,o)}else{e(i,i,o)}})}});h(i.prototype,"keys",i.prototype.values,true);xe(i.prototype,i.prototype.values);var f=function SetIterator(e){h(this,"it",e)};f.prototype={isSetIterator:true,next:function next(){if(!this.isSetIterator){throw new TypeError("Not a SetIterator")}return this.it.next()}};xe(f.prototype);return i}()};var an=S.Set&&!Set.prototype["delete"]&&Set.prototype.remove&&Set.prototype.items&&Set.prototype.map&&Array.isArray((new Set).keys);if(an){S.Set=on.Set}if(S.Map||S.Set){var un=a(function(){return new Map([[1,2]]).get(1)===2});if(!un){S.Map=function Map(){if(!(this instanceof Map)){throw new TypeError('Constructor Map requires "new"')}var e=new H;if(arguments.length>0){rn(Map,e,arguments[0])}delete e.constructor;Object.setPrototypeOf(e,S.Map.prototype);return e};S.Map.prototype=O(H.prototype);h(S.Map.prototype,"constructor",S.Map,true);m.preserveToString(S.Map,H)}var fn=new Map;var sn=function(){var e=new Map([[1,0],[2,0],[3,0],[4,0]]);e.set(-0,e);return e.get(0)===e&&e.get(-0)===e&&e.has(0)&&e.has(-0)}();var cn=fn.set(1,2)===fn;if(!sn||!cn){oe(Map.prototype,"set",function set(e,r){t($,this,e===0?0:e,r);return this})}if(!sn){b(Map.prototype,{get:function get(e){return t(B,this,e===0?0:e)},has:function has(e){return t(U,this,e===0?0:e)}},true);m.preserveToString(Map.prototype.get,B);m.preserveToString(Map.prototype.has,U)}var ln=new Set;var pn=Set.prototype["delete"]&&Set.prototype.add&&Set.prototype.has&&function(e){e["delete"](0);e.add(-0);return!e.has(0)}(ln);var vn=ln.add(1)===ln;if(!pn||!vn){var yn=Set.prototype.add;Set.prototype.add=function add(e){t(yn,this,e===0?0:e);return this};m.preserveToString(Set.prototype.add,yn)}if(!pn){var hn=Set.prototype.has;Set.prototype.has=function has(e){return t(hn,this,e===0?0:e)};m.preserveToString(Set.prototype.has,hn);var bn=Set.prototype["delete"];Set.prototype["delete"]=function SetDelete(e){return t(bn,this,e===0?0:e)};m.preserveToString(Set.prototype["delete"],bn)}var gn=w(S.Map,function(e){var t=new e([]);t.set(42,42);return t instanceof e});var dn=Object.setPrototypeOf&&!gn;var mn=function(){try{return!(S.Map()instanceof S.Map)}catch(e){return e instanceof TypeError}}();if(S.Map.length!==0||dn||!mn){S.Map=function Map(){if(!(this instanceof Map)){throw new TypeError('Constructor Map requires "new"')}var e=new H;if(arguments.length>0){rn(Map,e,arguments[0])}delete e.constructor;Object.setPrototypeOf(e,Map.prototype);return e};S.Map.prototype=H.prototype;h(S.Map.prototype,"constructor",S.Map,true);m.preserveToString(S.Map,H)}var On=w(S.Set,function(e){var t=new e([]);t.add(42,42);return t instanceof e});var wn=Object.setPrototypeOf&&!On;var jn=function(){try{return!(S.Set()instanceof S.Set)}catch(e){return e instanceof TypeError}}();if(S.Set.length!==0||wn||!jn){var Sn=S.Set;S.Set=function Set(){if(!(this instanceof Set)){throw new TypeError('Constructor Set requires "new"')}var e=new Sn;if(arguments.length>0){nn(Set,e,arguments[0])}delete e.constructor;Object.setPrototypeOf(e,Set.prototype);return e};S.Set.prototype=Sn.prototype;h(S.Set.prototype,"constructor",S.Set,true);m.preserveToString(S.Set,Sn)}var Tn=new S.Map;var In=!a(function(){return Tn.keys().next().done});if(typeof S.Map.prototype.clear!=="function"||(new S.Set).size!==0||Tn.size!==0||typeof S.Map.prototype.keys!=="function"||typeof S.Set.prototype.keys!=="function"||typeof S.Map.prototype.forEach!=="function"||typeof S.Set.prototype.forEach!=="function"||u(S.Map)||u(S.Set)||typeof Tn.keys().next!=="function"||In||!gn){b(S,{Map:on.Map,Set:on.Set},true)}if(S.Set.prototype.keys!==S.Set.prototype.values){h(S.Set.prototype,"keys",S.Set.prototype.values,true)}xe(Object.getPrototypeOf((new S.Map).keys()));xe(Object.getPrototypeOf((new S.Set).keys()));if(c&&S.Set.prototype.has.name!=="has"){var En=S.Set.prototype.has;oe(S.Set.prototype,"has",function has(e){return t(En,this,e)})}}b(S,on);Me(S.Map);Me(S.Set)}var Pn=function throwUnlessTargetIsObject(e){if(!le.TypeIsObject(e)){throw new TypeError("target must be an object")}};var Cn={apply:function apply(){return le.Call(le.Call,null,arguments)},construct:function construct(e,t){if(!le.IsConstructor(e)){throw new TypeError("First argument must be a constructor.")}var r=arguments.length>2?arguments[2]:e;if(!le.IsConstructor(r)){throw new TypeError("new.target must be a constructor.")}return le.Construct(e,t,r,"internal")},deleteProperty:function deleteProperty(e,t){Pn(e);if(s){var r=Object.getOwnPropertyDescriptor(e,t);if(r&&!r.configurable){return false}}return delete e[t]},has:function has(e,t){Pn(e);return t in e}};if(Object.getOwnPropertyNames){Object.assign(Cn,{ownKeys:function ownKeys(e){Pn(e);var t=Object.getOwnPropertyNames(e);if(le.IsCallable(Object.getOwnPropertySymbols)){x(t,Object.getOwnPropertySymbols(e))}return t}})}var Mn=function ConvertExceptionToBoolean(e){return!i(e)};if(Object.preventExtensions){Object.assign(Cn,{isExtensible:function isExtensible(e){Pn(e);return Object.isExtensible(e)},preventExtensions:function preventExtensions(e){Pn(e);return Mn(function(){return Object.preventExtensions(e)})}})}if(s){var xn=function get(e,t,r){var n=Object.getOwnPropertyDescriptor(e,t);if(!n){var o=Object.getPrototypeOf(e);if(o===null){return void 0}return xn(o,t,r)}if("value"in n){return n.value}if(n.get){return le.Call(n.get,r)}return void 0};var Nn=function set(e,r,n,o){var i=Object.getOwnPropertyDescriptor(e,r);if(!i){var a=Object.getPrototypeOf(e);if(a!==null){return Nn(a,r,n,o)}i={value:void 0,writable:true,enumerable:true,configurable:true}}if("value"in i){if(!i.writable){return false}if(!le.TypeIsObject(o)){return false}var u=Object.getOwnPropertyDescriptor(o,r);if(u){return ue.defineProperty(o,r,{value:n})}return ue.defineProperty(o,r,{value:n,writable:true,enumerable:true,configurable:true})}if(i.set){t(i.set,o,n);return true}return false};Object.assign(Cn,{defineProperty:function defineProperty(e,t,r){Pn(e);return Mn(function(){return Object.defineProperty(e,t,r)})},getOwnPropertyDescriptor:function getOwnPropertyDescriptor(e,t){Pn(e);return Object.getOwnPropertyDescriptor(e,t)},get:function get(e,t){Pn(e);var r=arguments.length>2?arguments[2]:e;return xn(e,t,r)},set:function set(e,t,r){Pn(e);var n=arguments.length>3?arguments[3]:e;return Nn(e,t,r,n)}})}if(Object.getPrototypeOf){var An=Object.getPrototypeOf;Cn.getPrototypeOf=function getPrototypeOf(e){Pn(e);return An(e)}}if(Object.setPrototypeOf&&Cn.getPrototypeOf){var _n=function(e,t){var r=t;while(r){if(e===r){return true}r=Cn.getPrototypeOf(r)}return false};Object.assign(Cn,{setPrototypeOf:function setPrototypeOf(e,t){Pn(e);if(t!==null&&!le.TypeIsObject(t)){throw new TypeError("proto must be an object or null")}if(t===ue.getPrototypeOf(e)){return true}if(ue.isExtensible&&!ue.isExtensible(e)){return false}if(_n(e,t)){return false}Object.setPrototypeOf(e,t);return true}})}var Rn=function(e,t){if(!le.IsCallable(S.Reflect[e])){h(S.Reflect,e,t)}else{var r=a(function(){S.Reflect[e](1);S.Reflect[e](NaN);S.Reflect[e](true);return true});if(r){oe(S.Reflect,e,t)}}};Object.keys(Cn).forEach(function(e){Rn(e,Cn[e])});var kn=S.Reflect.getPrototypeOf;if(c&&kn&&kn.name!=="getPrototypeOf"){oe(S.Reflect,"getPrototypeOf",function getPrototypeOf(e){return t(kn,S.Reflect,e)})}if(S.Reflect.setPrototypeOf){if(a(function(){S.Reflect.setPrototypeOf(1,{});return true})){oe(S.Reflect,"setPrototypeOf",Cn.setPrototypeOf)}}if(S.Reflect.defineProperty){if(!a(function(){var e=!S.Reflect.defineProperty(1,"test",{value:1});var t=typeof Object.preventExtensions!=="function"||!S.Reflect.defineProperty(Object.preventExtensions({}),"test",{});return e&&t})){oe(S.Reflect,"defineProperty",Cn.defineProperty)}}if(S.Reflect.construct){if(!a(function(){var e=function F(){};return S.Reflect.construct(function(){},[],e)instanceof e})){oe(S.Reflect,"construct",Cn.construct)}}if(String(new Date(NaN))!=="Invalid Date"){var Ln=Date.prototype.toString;var Fn=function toString(){var e=+this;if(e!==e){return"Invalid Date"}return le.Call(Ln,this)};oe(Date.prototype,"toString",Fn)}var Dn={anchor:function anchor(e){return le.CreateHTML(this,"a","name",e)},big:function big(){return le.CreateHTML(this,"big","","")},blink:function blink(){return le.CreateHTML(this,"blink","","")},bold:function bold(){return le.CreateHTML(this,"b","","")},fixed:function fixed(){return le.CreateHTML(this,"tt","","")},fontcolor:function fontcolor(e){return le.CreateHTML(this,"font","color",e)},fontsize:function fontsize(e){return le.CreateHTML(this,"font","size",e)},italics:function italics(){return le.CreateHTML(this,"i","","")},link:function link(e){return le.CreateHTML(this,"a","href",e)},small:function small(){return le.CreateHTML(this,"small","","")},strike:function strike(){return le.CreateHTML(this,"strike","","")},sub:function sub(){return le.CreateHTML(this,"sub","","")},sup:function sub(){return le.CreateHTML(this,"sup","","")}};l(Object.keys(Dn),function(e){var r=String.prototype[e];var n=false;if(le.IsCallable(r)){var o=t(r,"",' " ');var i=P([],o.match(/"/g)).length;n=o!==o.toLowerCase()||i>2}else{n=true}if(n){oe(String.prototype,e,Dn[e])}});var zn=function(){if(!ie){return false}var e=typeof JSON==="object"&&typeof JSON.stringify==="function"?JSON.stringify:null;if(!e){return false}if(typeof e(J())!=="undefined"){return true}if(e([J()])!=="[null]"){return true}var t={a:J()};t[J()]=true;if(e(t)!=="{}"){return true}return false}();var qn=a(function(){if(!ie){return true}return JSON.stringify(Object(J()))==="{}"&&JSON.stringify([Object(J())])==="[{}]"});if(zn||!qn){var Wn=JSON.stringify;oe(JSON,"stringify",function stringify(e){if(typeof e==="symbol"){return}var n;if(arguments.length>1){n=arguments[1]}var o=[e];if(!r(n)){var i=le.IsCallable(n)?n:null;var a=function(e,r){var n=i?t(i,this,e,r):r;if(typeof n!=="symbol"){if(ne.symbol(n)){return _t({})(n)}return n}};o.push(a)}else{o.push(n)}if(arguments.length>2){o.push(arguments[2])}return Wn.apply(this,o)})}return S});
//# sourceMappingURL=es6-shim.map
