<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="./images/favicon.ico">
	   <link rel="stylesheet" href="./styles/style.css">
        <link rel="stylesheet" href="./styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="./" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content coverage">
                   <div class="content-data">



















<ol class="breadcrumb">
    <li class="breadcrumb-item">Documentation coverage</li>
</ol>

<div>
    <img src="./images/coverage-badge-documentation.svg">
</div>

<table class="table table-bordered coverage" id="coverage-table">
    <thead class="coverage-header">
        <tr>
            <th>File</th>
            <th>Type</th>
            <th>Identifier</th>
            <th style="text-align:right" class="statements" data-sort-default>Statements</th>
        </tr>
    </thead>
    <tbody>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#ContextUserType">__mocks__/aws/sns.mock.ts</a>
            </td>
            <td>variable</td>
            <td>ContextUserType</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#SNSModule">__mocks__/aws/sns.mock.ts</a>
            </td>
            <td>variable</td>
            <td>SNSModule</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#SNSPublisherService">__mocks__/aws/sns.mock.ts</a>
            </td>
            <td>variable</td>
            <td>SNSPublisherService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#SNSService">__mocks__/aws/sns.mock.ts</a>
            </td>
            <td>variable</td>
            <td>SNSService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#TicketEvents">__mocks__/aws/sns.mock.ts</a>
            </td>
            <td>variable</td>
            <td>TicketEvents</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#SQSConsumerService">__mocks__/aws/sqs.mock.ts</a>
            </td>
            <td>variable</td>
            <td>SQSConsumerService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#SQSModule">__mocks__/aws/sqs.mock.ts</a>
            </td>
            <td>variable</td>
            <td>SQSModule</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#SQSProducerService">__mocks__/aws/sqs.mock.ts</a>
            </td>
            <td>variable</td>
            <td>SQSProducerService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#Client">__mocks__/typesense/typesense.mock.ts</a>
            </td>
            <td>variable</td>
            <td>Client</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#TypesenseModule">__mocks__/typesense/typesense.mock.ts</a>
            </td>
            <td>variable</td>
            <td>TypesenseModule</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#TypesenseService">__mocks__/typesense/typesense.mock.ts</a>
            </td>
            <td>variable</td>
            <td>TypesenseService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/AccountActivityActionController.html">src/accounts/controllers/account-activity.action.controller.ts</a>
            </td>
            <td>controller</td>
            <td>AccountActivityActionController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/AccountAttributeValueActionController.html">src/accounts/controllers/account-attribute-value.action.controller.ts</a>
            </td>
            <td>controller</td>
            <td>AccountAttributeValueActionController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/AccountNoteActionController.html">src/accounts/controllers/account-note.action.controller.ts</a>
            </td>
            <td>controller</td>
            <td>AccountNoteActionController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/AccountRelationshipActionController.html">src/accounts/controllers/account-relationship.action.controller.ts</a>
            </td>
            <td>controller</td>
            <td>AccountRelationshipActionController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/9)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/AccountTaskActionController.html">src/accounts/controllers/account-task.action.controller.ts</a>
            </td>
            <td>controller</td>
            <td>AccountTaskActionController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/AccountsController.html">src/accounts/controllers/accounts.controller.ts</a>
            </td>
            <td>controller</td>
            <td>AccountsController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/CustomerContactActionController.html">src/accounts/controllers/customer-contact.action.controller.ts</a>
            </td>
            <td>controller</td>
            <td>CustomerContactActionController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/CustomerContactsIngestController.html">src/accounts/controllers/customer-contacts-ingest.controller.ts</a>
            </td>
            <td>controller</td>
            <td>CustomerContactsIngestController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/AccountActivitiesGrpcController.html">src/accounts/controllers/grpc/account-activities.grpc.controller.ts</a>
            </td>
            <td>controller</td>
            <td>AccountActivitiesGrpcController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/AccountAnnotatorGrpcController.html">src/accounts/controllers/grpc/account-annotator.grpc.controller.ts</a>
            </td>
            <td>controller</td>
            <td>AccountAnnotatorGrpcController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/AccountAttributeValuesGrpcController.html">src/accounts/controllers/grpc/account-attribute-values.grpc.controller.ts</a>
            </td>
            <td>controller</td>
            <td>AccountAttributeValuesGrpcController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/AccountNotesGrpcController.html">src/accounts/controllers/grpc/account-notes.grpc.controller.ts</a>
            </td>
            <td>controller</td>
            <td>AccountNotesGrpcController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/AccountRelationshipsGrpcController.html">src/accounts/controllers/grpc/account-relationships.grpc.controller.ts</a>
            </td>
            <td>controller</td>
            <td>AccountRelationshipsGrpcController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/9)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/AccountTasksGrpcController.html">src/accounts/controllers/grpc/account-tasks.grpc.controller.ts</a>
            </td>
            <td>controller</td>
            <td>AccountTasksGrpcController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/AccountsGrpcController.html">src/accounts/controllers/grpc/accounts.grpc.controller.ts</a>
            </td>
            <td>controller</td>
            <td>AccountsGrpcController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/CustomerContactsGrpcController.html">src/accounts/controllers/grpc/customer-contacts.grpc.controller.ts</a>
            </td>
            <td>controller</td>
            <td>CustomerContactsGrpcController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateAccountActivityDto.html">src/accounts/dtos/account-activity.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateAccountActivityDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/9)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/FindAccountActivityDto.html">src/accounts/dtos/account-activity.dto.ts</a>
            </td>
            <td>class</td>
            <td>FindAccountActivityDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateAccountActivityDto.html">src/accounts/dtos/account-activity.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateAccountActivityDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateAccountAttributeValueDto.html">src/accounts/dtos/account-attribute-value.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateAccountAttributeValueDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/FindAccountAttributeValueDto.html">src/accounts/dtos/account-attribute-value.dto.ts</a>
            </td>
            <td>class</td>
            <td>FindAccountAttributeValueDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateAccountAttributeValueDto.html">src/accounts/dtos/account-attribute-value.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateAccountAttributeValueDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateAccountNoteDto.html">src/accounts/dtos/account-note.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateAccountNoteDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/FindAccountNoteDto.html">src/accounts/dtos/account-note.dto.ts</a>
            </td>
            <td>class</td>
            <td>FindAccountNoteDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateAccountNoteDto.html">src/accounts/dtos/account-note.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateAccountNoteDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateAccountRelationshipDto.html">src/accounts/dtos/account-relationship.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateAccountRelationshipDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateAccountRelationshipTypeDto.html">src/accounts/dtos/account-relationship.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateAccountRelationshipTypeDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/FindAccountRelationshipDto.html">src/accounts/dtos/account-relationship.dto.ts</a>
            </td>
            <td>class</td>
            <td>FindAccountRelationshipDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateAccountRelationshipDto.html">src/accounts/dtos/account-relationship.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateAccountRelationshipDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateAccountRelationshipTypeDto.html">src/accounts/dtos/account-relationship.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateAccountRelationshipTypeDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateAccountTaskDto.html">src/accounts/dtos/account-task.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateAccountTaskDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/10)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/FindAccountTaskDto.html">src/accounts/dtos/account-task.dto.ts</a>
            </td>
            <td>class</td>
            <td>FindAccountTaskDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/9)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateAccountTaskDto.html">src/accounts/dtos/account-task.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateAccountTaskDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/9)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CommonAccountDto.html">src/accounts/dtos/account.dto.ts</a>
            </td>
            <td>class</td>
            <td>CommonAccountDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/17)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateAccountDto.html">src/accounts/dtos/account.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateAccountDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/19)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/FindAllAccountsDto.html">src/accounts/dtos/account.dto.ts</a>
            </td>
            <td>class</td>
            <td>FindAllAccountsDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/9)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateAccountDto.html">src/accounts/dtos/account.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateAccountDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/19)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/BulkCreateCustomerContactDetailsDto.html">src/accounts/dtos/customer-contact.dto.ts</a>
            </td>
            <td>class</td>
            <td>BulkCreateCustomerContactDetailsDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/BulkCreateCustomerContactsDto.html">src/accounts/dtos/customer-contact.dto.ts</a>
            </td>
            <td>class</td>
            <td>BulkCreateCustomerContactsDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateCustomerContactDto.html">src/accounts/dtos/customer-contact.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateCustomerContactDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/FindAllCustomerContactsDto.html">src/accounts/dtos/customer-contact.dto.ts</a>
            </td>
            <td>class</td>
            <td>FindAllCustomerContactsDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateCustomerContactDto.html">src/accounts/dtos/customer-contact.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateCustomerContactDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AccountActivityResponseDto.html">src/accounts/dtos/response/account-activity.dto.ts</a>
            </td>
            <td>class</td>
            <td>AccountActivityResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/19)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AccountAttributeValueResponseDto.html">src/accounts/dtos/response/account-attribute-value.dto.ts</a>
            </td>
            <td>class</td>
            <td>AccountAttributeValueResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AccountNoteResponseDto.html">src/accounts/dtos/response/account-note.dto.ts</a>
            </td>
            <td>class</td>
            <td>AccountNoteResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/15)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AccountRelationshipResponseDto.html">src/accounts/dtos/response/account-relationship.dto.ts</a>
            </td>
            <td>class</td>
            <td>AccountRelationshipResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/10)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AccountRelationshipTypeResponseDto.html">src/accounts/dtos/response/account-relationship.dto.ts</a>
            </td>
            <td>class</td>
            <td>AccountRelationshipTypeResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AccountTaskResponseDto.html">src/accounts/dtos/response/account-task.dto.ts</a>
            </td>
            <td>class</td>
            <td>AccountTaskResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/22)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AccountResponseDto.html">src/accounts/dtos/response/account.dto.ts</a>
            </td>
            <td>class</td>
            <td>AccountResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/28)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CustomerContactBulkResponseDto.html">src/accounts/dtos/response/customer-contact.dto.ts</a>
            </td>
            <td>class</td>
            <td>CustomerContactBulkResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CustomerContactResponseDto.html">src/accounts/dtos/response/customer-contact.dto.ts</a>
            </td>
            <td>class</td>
            <td>CustomerContactResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/12)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/AccountsEventsFactory.html">src/accounts/events/accounts-events.factory.ts</a>
            </td>
            <td>injectable</td>
            <td>AccountsEventsFactory</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AccountsSNSEventsFactory.html">src/accounts/events/accounts-sns-events.factory.ts</a>
            </td>
            <td>class</td>
            <td>AccountsSNSEventsFactory</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/20)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AccountAttributeValueDeletedEvent.html">src/accounts/events/accounts.events.ts</a>
            </td>
            <td>class</td>
            <td>AccountAttributeValueDeletedEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/BaseAccountServiceEvent.html">src/accounts/events/accounts.events.ts</a>
            </td>
            <td>class</td>
            <td>BaseAccountServiceEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LinkContactsToAccountByEmailDomain.html">src/accounts/events/accounts.events.ts</a>
            </td>
            <td>class</td>
            <td>LinkContactsToAccountByEmailDomain</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/AccountActivityPayload.html">src/accounts/interfaces/sns-events.interface.ts</a>
            </td>
            <td>interface</td>
            <td>AccountActivityPayload</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/AccountNotePayload.html">src/accounts/interfaces/sns-events.interface.ts</a>
            </td>
            <td>interface</td>
            <td>AccountNotePayload</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/AccountPayload.html">src/accounts/interfaces/sns-events.interface.ts</a>
            </td>
            <td>interface</td>
            <td>AccountPayload</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/AccountRelationshipPayload.html">src/accounts/interfaces/sns-events.interface.ts</a>
            </td>
            <td>interface</td>
            <td>AccountRelationshipPayload</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/AccountTaskData.html">src/accounts/interfaces/sns-events.interface.ts</a>
            </td>
            <td>interface</td>
            <td>AccountTaskData</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/AccountTaskPayload.html">src/accounts/interfaces/sns-events.interface.ts</a>
            </td>
            <td>interface</td>
            <td>AccountTaskPayload</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/Actor.html">src/accounts/interfaces/sns-events.interface.ts</a>
            </td>
            <td>interface</td>
            <td>Actor</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/CustomerContactPayload.html">src/accounts/interfaces/sns-events.interface.ts</a>
            </td>
            <td>interface</td>
            <td>CustomerContactPayload</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/SNSEvent.html">src/accounts/interfaces/sns-events.interface.ts</a>
            </td>
            <td>interface</td>
            <td>SNSEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/AccountsListeners.html">src/accounts/listeners/accounts.listeners.ts</a>
            </td>
            <td>injectable</td>
            <td>AccountsListeners</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/AccountsSNSPublisher.html">src/accounts/processors/sns-publisher.processor.ts</a>
            </td>
            <td>injectable</td>
            <td>AccountsSNSPublisher</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="good">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/AccountActivityActionService.html">src/accounts/services/account-activity.action.service.ts</a>
            </td>
            <td>injectable</td>
            <td>AccountActivityActionService</td>
            <td align="right" data-sort="71">
                <span class="coverage-percent">71 %</span>
                <span class="coverage-count">(10/14)</span>
            </td>
        </tr>
        <tr class="good">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/AccountAnnotatorService.html">src/accounts/services/account-annotator.service.ts</a>
            </td>
            <td>injectable</td>
            <td>AccountAnnotatorService</td>
            <td align="right" data-sort="66">
                <span class="coverage-percent">66 %</span>
                <span class="coverage-count">(4/6)</span>
            </td>
        </tr>
        <tr class="good">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/AccountAttributeValueActionService.html">src/accounts/services/account-attribute-value.action.service.ts</a>
            </td>
            <td>injectable</td>
            <td>AccountAttributeValueActionService</td>
            <td align="right" data-sort="75">
                <span class="coverage-percent">75 %</span>
                <span class="coverage-count">(6/8)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/AccountCommonService.html">src/accounts/services/account-commons.service.ts</a>
            </td>
            <td>injectable</td>
            <td>AccountCommonService</td>
            <td align="right" data-sort="85">
                <span class="coverage-percent">85 %</span>
                <span class="coverage-count">(6/7)</span>
            </td>
        </tr>
        <tr class="good">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/AccountNoteActionService.html">src/accounts/services/account-note.action.service.ts</a>
            </td>
            <td>injectable</td>
            <td>AccountNoteActionService</td>
            <td align="right" data-sort="72">
                <span class="coverage-percent">72 %</span>
                <span class="coverage-count">(8/11)</span>
            </td>
        </tr>
        <tr class="good">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/AccountRelationshipActionService.html">src/accounts/services/account-relationship.action.service.ts</a>
            </td>
            <td>injectable</td>
            <td>AccountRelationshipActionService</td>
            <td align="right" data-sort="57">
                <span class="coverage-percent">57 %</span>
                <span class="coverage-count">(8/14)</span>
            </td>
        </tr>
        <tr class="good">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/AccountTaskActionService.html">src/accounts/services/account-task.action.service.ts</a>
            </td>
            <td>injectable</td>
            <td>AccountTaskActionService</td>
            <td align="right" data-sort="72">
                <span class="coverage-percent">72 %</span>
                <span class="coverage-count">(8/11)</span>
            </td>
        </tr>
        <tr class="good">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/AccountsService.html">src/accounts/services/accounts.service.ts</a>
            </td>
            <td>injectable</td>
            <td>AccountsService</td>
            <td align="right" data-sort="73">
                <span class="coverage-percent">73 %</span>
                <span class="coverage-count">(11/15)</span>
            </td>
        </tr>
        <tr class="good">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/CustomerContactActionService.html">src/accounts/services/customer-contact.action.service.ts</a>
            </td>
            <td>injectable</td>
            <td>CustomerContactActionService</td>
            <td align="right" data-sort="61">
                <span class="coverage-percent">61 %</span>
                <span class="coverage-count">(8/13)</span>
            </td>
        </tr>
        <tr class="medium">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/CustomerContactsIngestService.html">src/accounts/services/customer-contacts-ingest.service.ts</a>
            </td>
            <td>injectable</td>
            <td>CustomerContactsIngestService</td>
            <td align="right" data-sort="33">
                <span class="coverage-percent">33 %</span>
                <span class="coverage-count">(1/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/ActivitiesService.html">src/activities/services/activities.service.ts</a>
            </td>
            <td>injectable</td>
            <td>ActivitiesService</td>
            <td align="right" data-sort="25">
                <span class="coverage-percent">25 %</span>
                <span class="coverage-count">(1/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/AuthController.html">src/auth/auth.controller.ts</a>
            </td>
            <td>controller</td>
            <td>AuthController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="medium">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/AuthService.html">src/auth/auth.service.ts</a>
            </td>
            <td>injectable</td>
            <td>AuthService</td>
            <td align="right" data-sort="40">
                <span class="coverage-percent">40 %</span>
                <span class="coverage-count">(2/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#jwtConstants">src/auth/constants.ts</a>
            </td>
            <td>variable</td>
            <td>jwtConstants</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#IS_PUBLIC_KEY">src/auth/decorators/auth.decorator.ts</a>
            </td>
            <td>variable</td>
            <td>IS_PUBLIC_KEY</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#Public">src/auth/decorators/auth.decorator.ts</a>
            </td>
            <td>variable</td>
            <td>Public</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/SignInDto.html">src/auth/dto/auth.dto.ts</a>
            </td>
            <td>class</td>
            <td>SignInDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/SignUpDto.html">src/auth/dto/auth.dto.ts</a>
            </td>
            <td>class</td>
            <td>SignUpDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/SignUpAuthResponse.html">src/auth/dto/response/auth-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>SignUpAuthResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AuthUserResponseDto.html">src/auth/transformers/auth.transformer.ts</a>
            </td>
            <td>class</td>
            <td>AuthUserResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/13)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/BullBoardPluginOptions.html">src/bull/bull-board.plugin.ts</a>
            </td>
            <td>interface</td>
            <td>BullBoardPluginOptions</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/BullBoardService.html">src/bull/bull-board.service.ts</a>
            </td>
            <td>injectable</td>
            <td>BullBoardService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#CACHE_TTL">src/common/constants/cache.constants.ts</a>
            </td>
            <td>variable</td>
            <td>CACHE_TTL</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#POSTGRES_ERROR_CODES">src/common/constants/postgres-errors.constants.ts</a>
            </td>
            <td>variable</td>
            <td>POSTGRES_ERROR_CODES</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#POSTGRES_CONFIG">src/common/constants/test.constants.ts</a>
            </td>
            <td>variable</td>
            <td>POSTGRES_CONFIG</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/functions.html#SkipAllThrottler">src/common/decorators/throttler.decorator.ts</a>
            </td>
            <td>function</td>
            <td>SkipAllThrottler</td>
            <td align="right" data-sort="100">
                <span class="coverage-percent">100 %</span>
                <span class="coverage-count">(1/1)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/functions.html#ThrottleSpecialTier">src/common/decorators/throttler.decorator.ts</a>
            </td>
            <td>function</td>
            <td>ThrottleSpecialTier</td>
            <td align="right" data-sort="100">
                <span class="coverage-percent">100 %</span>
                <span class="coverage-count">(1/1)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/functions.html#ThrottleTierFour">src/common/decorators/throttler.decorator.ts</a>
            </td>
            <td>function</td>
            <td>ThrottleTierFour</td>
            <td align="right" data-sort="100">
                <span class="coverage-percent">100 %</span>
                <span class="coverage-count">(1/1)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/functions.html#ThrottleTierOne">src/common/decorators/throttler.decorator.ts</a>
            </td>
            <td>function</td>
            <td>ThrottleTierOne</td>
            <td align="right" data-sort="100">
                <span class="coverage-percent">100 %</span>
                <span class="coverage-count">(1/1)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/functions.html#ThrottleTierThree">src/common/decorators/throttler.decorator.ts</a>
            </td>
            <td>function</td>
            <td>ThrottleTierThree</td>
            <td align="right" data-sort="100">
                <span class="coverage-percent">100 %</span>
                <span class="coverage-count">(1/1)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/functions.html#ThrottleTierTwo">src/common/decorators/throttler.decorator.ts</a>
            </td>
            <td>function</td>
            <td>ThrottleTierTwo</td>
            <td align="right" data-sort="100">
                <span class="coverage-percent">100 %</span>
                <span class="coverage-count">(1/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#ALL_SKIP">src/common/decorators/throttler.decorator.ts</a>
            </td>
            <td>variable</td>
            <td>ALL_SKIP</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#CurrentUser">src/common/decorators/user.decorator.ts</a>
            </td>
            <td>variable</td>
            <td>CurrentUser</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/BusinessDayDto.html">src/common/dto/business-hours.dto.ts</a>
            </td>
            <td>class</td>
            <td>BusinessDayDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/BusinessHoursConfigDto.html">src/common/dto/business-hours.dto.ts</a>
            </td>
            <td>class</td>
            <td>BusinessHoursConfigDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/BusinessSlotDto.html">src/common/dto/business-hours.dto.ts</a>
            </td>
            <td>class</td>
            <td>BusinessSlotDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateTimezoneWorkingHoursDto.html">src/common/dto/business-hours.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateTimezoneWorkingHoursDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/10)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateCommentDto.html">src/common/dto/comments.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateCommentDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/WorkflowsGrpcClient.html">src/common/grpc/workflows.client.grpc.ts</a>
            </td>
            <td>injectable</td>
            <td>WorkflowsGrpcClient</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/BusinessHoursValidatorService.html">src/common/services/business-hours-validation.service.ts</a>
            </td>
            <td>injectable</td>
            <td>BusinessHoursValidatorService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/10)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/FieldMetadataService.html">src/common/services/field-metadata.service.ts</a>
            </td>
            <td>injectable</td>
            <td>FieldMetadataService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/ValidationService.html">src/common/services/validation.service.ts</a>
            </td>
            <td>injectable</td>
            <td>ValidationService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IdValidation.html">src/common/services/validation.service.ts</a>
            </td>
            <td>interface</td>
            <td>IdValidation</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/functions.html#getMaxLimit">src/common/utils/api-helpers.utils.ts</a>
            </td>
            <td>function</td>
            <td>getMaxLimit</td>
            <td align="right" data-sort="100">
                <span class="coverage-percent">100 %</span>
                <span class="coverage-count">(1/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#extractEmailDetails">src/common/utils/extract-email-details.ts</a>
            </td>
            <td>variable</td>
            <td>extractEmailDetails</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#extractNameFromEmail">src/common/utils/extract-email-details.ts</a>
            </td>
            <td>variable</td>
            <td>extractNameFromEmail</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/IdGeneratorUtils.html">src/common/utils/id-generator.utils.ts</a>
            </td>
            <td>class</td>
            <td>IdGeneratorUtils</td>
            <td align="right" data-sort="25">
                <span class="coverage-percent">25 %</span>
                <span class="coverage-count">(1/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/functions.html#generateIdentifier">src/common/utils/identifier-generator.utils.ts</a>
            </td>
            <td>function</td>
            <td>generateIdentifier</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ResponseMessage.html">src/common/utils/response-dto.utils.ts</a>
            </td>
            <td>class</td>
            <td>ResponseMessage</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/functions.html#constructDailyConfigFromCommonSlots">src/common/utils/time-slots.utils.ts</a>
            </td>
            <td>function</td>
            <td>constructDailyConfigFromCommonSlots</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/functions.html#IsDateDDMM">src/common/validators/date.validators.ts</a>
            </td>
            <td>function</td>
            <td>IsDateDDMM</td>
            <td align="right" data-sort="100">
                <span class="coverage-percent">100 %</span>
                <span class="coverage-count">(1/1)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/functions.html#IsDateDDMMOrDDMMYYYY">src/common/validators/date.validators.ts</a>
            </td>
            <td>function</td>
            <td>IsDateDDMMOrDDMMYYYY</td>
            <td align="right" data-sort="100">
                <span class="coverage-percent">100 %</span>
                <span class="coverage-count">(1/1)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/functions.html#IsDateDDMMYYYY">src/common/validators/date.validators.ts</a>
            </td>
            <td>function</td>
            <td>IsDateDDMMYYYY</td>
            <td align="right" data-sort="100">
                <span class="coverage-percent">100 %</span>
                <span class="coverage-count">(1/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#IS_DATE_DDMM">src/common/validators/date.validators.ts</a>
            </td>
            <td>variable</td>
            <td>IS_DATE_DDMM</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#IS_DATE_DDMM_OR_DDMMYYYY">src/common/validators/date.validators.ts</a>
            </td>
            <td>variable</td>
            <td>IS_DATE_DDMM_OR_DDMMYYYY</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#IS_DATE_DDMMYYYY">src/common/validators/date.validators.ts</a>
            </td>
            <td>variable</td>
            <td>IS_DATE_DDMMYYYY</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/HasAllBusinessDaysConstraint.html">src/common/validators/time.validators.ts</a>
            </td>
            <td>class</td>
            <td>HasAllBusinessDaysConstraint</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/IsTimeFormatConstraint.html">src/common/validators/time.validators.ts</a>
            </td>
            <td>class</td>
            <td>IsTimeFormatConstraint</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/IsValidTimeSlotConstraint.html">src/common/validators/time.validators.ts</a>
            </td>
            <td>class</td>
            <td>IsValidTimeSlotConstraint</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ValidateActiveDayConstraint.html">src/common/validators/time.validators.ts</a>
            </td>
            <td>class</td>
            <td>ValidateActiveDayConstraint</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/functions.html#IsAfterDate">src/common/validators/time.validators.ts</a>
            </td>
            <td>function</td>
            <td>IsAfterDate</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/functions.html#IsTimezoneOrEmpty">src/common/validators/time.validators.ts</a>
            </td>
            <td>function</td>
            <td>IsTimezoneOrEmpty</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#IS_TIMEZONE_OR_EMPTY">src/common/validators/time.validators.ts</a>
            </td>
            <td>variable</td>
            <td>IS_TIMEZONE_OR_EMPTY</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/BulkCreateCustomerContacts.html">src/common/workflows/activities/accounts.activities.ts</a>
            </td>
            <td>class</td>
            <td>BulkCreateCustomerContacts</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateAccount.html">src/common/workflows/activities/accounts.activities.ts</a>
            </td>
            <td>class</td>
            <td>CreateAccount</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateAccountActivity.html">src/common/workflows/activities/accounts.activities.ts</a>
            </td>
            <td>class</td>
            <td>CreateAccountActivity</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateAccountAttributeValue.html">src/common/workflows/activities/accounts.activities.ts</a>
            </td>
            <td>class</td>
            <td>CreateAccountAttributeValue</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateAccountNote.html">src/common/workflows/activities/accounts.activities.ts</a>
            </td>
            <td>class</td>
            <td>CreateAccountNote</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateAccountRelationship.html">src/common/workflows/activities/accounts.activities.ts</a>
            </td>
            <td>class</td>
            <td>CreateAccountRelationship</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateAccountRelationshipType.html">src/common/workflows/activities/accounts.activities.ts</a>
            </td>
            <td>class</td>
            <td>CreateAccountRelationshipType</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateAccountTask.html">src/common/workflows/activities/accounts.activities.ts</a>
            </td>
            <td>class</td>
            <td>CreateAccountTask</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateCustomerContact.html">src/common/workflows/activities/accounts.activities.ts</a>
            </td>
            <td>class</td>
            <td>CreateCustomerContact</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DeleteAccount.html">src/common/workflows/activities/accounts.activities.ts</a>
            </td>
            <td>class</td>
            <td>DeleteAccount</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DeleteAccountActivity.html">src/common/workflows/activities/accounts.activities.ts</a>
            </td>
            <td>class</td>
            <td>DeleteAccountActivity</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DeleteAccountAttributeValue.html">src/common/workflows/activities/accounts.activities.ts</a>
            </td>
            <td>class</td>
            <td>DeleteAccountAttributeValue</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DeleteAccountNote.html">src/common/workflows/activities/accounts.activities.ts</a>
            </td>
            <td>class</td>
            <td>DeleteAccountNote</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DeleteAccountRelationship.html">src/common/workflows/activities/accounts.activities.ts</a>
            </td>
            <td>class</td>
            <td>DeleteAccountRelationship</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DeleteAccountRelationshipType.html">src/common/workflows/activities/accounts.activities.ts</a>
            </td>
            <td>class</td>
            <td>DeleteAccountRelationshipType</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DeleteAccountTask.html">src/common/workflows/activities/accounts.activities.ts</a>
            </td>
            <td>class</td>
            <td>DeleteAccountTask</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DeleteCustomerContact.html">src/common/workflows/activities/accounts.activities.ts</a>
            </td>
            <td>class</td>
            <td>DeleteCustomerContact</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetAccountActivities.html">src/common/workflows/activities/accounts.activities.ts</a>
            </td>
            <td>class</td>
            <td>GetAccountActivities</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetAccountAttributeValues.html">src/common/workflows/activities/accounts.activities.ts</a>
            </td>
            <td>class</td>
            <td>GetAccountAttributeValues</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetAccountDetails.html">src/common/workflows/activities/accounts.activities.ts</a>
            </td>
            <td>class</td>
            <td>GetAccountDetails</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetAccountNotes.html">src/common/workflows/activities/accounts.activities.ts</a>
            </td>
            <td>class</td>
            <td>GetAccountNotes</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetAccountRelationships.html">src/common/workflows/activities/accounts.activities.ts</a>
            </td>
            <td>class</td>
            <td>GetAccountRelationships</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetAccountRelationshipTypes.html">src/common/workflows/activities/accounts.activities.ts</a>
            </td>
            <td>class</td>
            <td>GetAccountRelationshipTypes</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetAccounts.html">src/common/workflows/activities/accounts.activities.ts</a>
            </td>
            <td>class</td>
            <td>GetAccounts</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetAccountTasks.html">src/common/workflows/activities/accounts.activities.ts</a>
            </td>
            <td>class</td>
            <td>GetAccountTasks</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetCustomerContacts.html">src/common/workflows/activities/accounts.activities.ts</a>
            </td>
            <td>class</td>
            <td>GetCustomerContacts</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateAccount.html">src/common/workflows/activities/accounts.activities.ts</a>
            </td>
            <td>class</td>
            <td>UpdateAccount</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateAccountActivity.html">src/common/workflows/activities/accounts.activities.ts</a>
            </td>
            <td>class</td>
            <td>UpdateAccountActivity</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateAccountAttributeValue.html">src/common/workflows/activities/accounts.activities.ts</a>
            </td>
            <td>class</td>
            <td>UpdateAccountAttributeValue</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateAccountNote.html">src/common/workflows/activities/accounts.activities.ts</a>
            </td>
            <td>class</td>
            <td>UpdateAccountNote</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateAccountRelationship.html">src/common/workflows/activities/accounts.activities.ts</a>
            </td>
            <td>class</td>
            <td>UpdateAccountRelationship</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateAccountRelationshipType.html">src/common/workflows/activities/accounts.activities.ts</a>
            </td>
            <td>class</td>
            <td>UpdateAccountRelationshipType</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateAccountTask.html">src/common/workflows/activities/accounts.activities.ts</a>
            </td>
            <td>class</td>
            <td>UpdateAccountTask</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateCustomerContact.html">src/common/workflows/activities/accounts.activities.ts</a>
            </td>
            <td>class</td>
            <td>UpdateCustomerContact</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AddReactionActivity.html">src/common/workflows/activities/communications.activities.ts</a>
            </td>
            <td>class</td>
            <td>AddReactionActivity</td>
            <td align="right" data-sort="12">
                <span class="coverage-percent">12 %</span>
                <span class="coverage-count">(1/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateCommentActivity.html">src/common/workflows/activities/communications.activities.ts</a>
            </td>
            <td>class</td>
            <td>CreateCommentActivity</td>
            <td align="right" data-sort="12">
                <span class="coverage-percent">12 %</span>
                <span class="coverage-count">(1/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DeleteCommentActivity.html">src/common/workflows/activities/communications.activities.ts</a>
            </td>
            <td>class</td>
            <td>DeleteCommentActivity</td>
            <td align="right" data-sort="12">
                <span class="coverage-percent">12 %</span>
                <span class="coverage-count">(1/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetCommentActivity.html">src/common/workflows/activities/communications.activities.ts</a>
            </td>
            <td>class</td>
            <td>GetCommentActivity</td>
            <td align="right" data-sort="12">
                <span class="coverage-percent">12 %</span>
                <span class="coverage-count">(1/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetCommentsActivity.html">src/common/workflows/activities/communications.activities.ts</a>
            </td>
            <td>class</td>
            <td>GetCommentsActivity</td>
            <td align="right" data-sort="12">
                <span class="coverage-percent">12 %</span>
                <span class="coverage-count">(1/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetCommentThreadsActivity.html">src/common/workflows/activities/communications.activities.ts</a>
            </td>
            <td>class</td>
            <td>GetCommentThreadsActivity</td>
            <td align="right" data-sort="12">
                <span class="coverage-percent">12 %</span>
                <span class="coverage-count">(1/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/RemoveReactionActivity.html">src/common/workflows/activities/communications.activities.ts</a>
            </td>
            <td>class</td>
            <td>RemoveReactionActivity</td>
            <td align="right" data-sort="12">
                <span class="coverage-percent">12 %</span>
                <span class="coverage-count">(1/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateCommentActivity.html">src/common/workflows/activities/communications.activities.ts</a>
            </td>
            <td>class</td>
            <td>UpdateCommentActivity</td>
            <td align="right" data-sort="12">
                <span class="coverage-percent">12 %</span>
                <span class="coverage-count">(1/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ArchiveTicketActivity.html">src/common/workflows/activities/tickets.activities.ts</a>
            </td>
            <td>class</td>
            <td>ArchiveTicketActivity</td>
            <td align="right" data-sort="12">
                <span class="coverage-percent">12 %</span>
                <span class="coverage-count">(1/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AssignTicketActivity.html">src/common/workflows/activities/tickets.activities.ts</a>
            </td>
            <td>class</td>
            <td>AssignTicketActivity</td>
            <td align="right" data-sort="10">
                <span class="coverage-percent">10 %</span>
                <span class="coverage-count">(1/10)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CompensateAssignTicketActivity.html">src/common/workflows/activities/tickets.activities.ts</a>
            </td>
            <td>class</td>
            <td>CompensateAssignTicketActivity</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/9)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateTicketActivity.html">src/common/workflows/activities/tickets.activities.ts</a>
            </td>
            <td>class</td>
            <td>CreateTicketActivity</td>
            <td align="right" data-sort="12">
                <span class="coverage-percent">12 %</span>
                <span class="coverage-count">(1/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/EscalateTicketActivity.html">src/common/workflows/activities/tickets.activities.ts</a>
            </td>
            <td>class</td>
            <td>EscalateTicketActivity</td>
            <td align="right" data-sort="12">
                <span class="coverage-percent">12 %</span>
                <span class="coverage-count">(1/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetTicketActivity.html">src/common/workflows/activities/tickets.activities.ts</a>
            </td>
            <td>class</td>
            <td>GetTicketActivity</td>
            <td align="right" data-sort="12">
                <span class="coverage-percent">12 %</span>
                <span class="coverage-count">(1/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetTicketsWithCursorActivity.html">src/common/workflows/activities/tickets.activities.ts</a>
            </td>
            <td>class</td>
            <td>GetTicketsWithCursorActivity</td>
            <td align="right" data-sort="12">
                <span class="coverage-percent">12 %</span>
                <span class="coverage-count">(1/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateTicketActivity.html">src/common/workflows/activities/tickets.activities.ts</a>
            </td>
            <td>class</td>
            <td>UpdateTicketActivity</td>
            <td align="right" data-sort="12">
                <span class="coverage-percent">12 %</span>
                <span class="coverage-count">(1/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#ACCOUNT_ACTIVITY_PAYLOAD_SCHEMA">src/common/workflows/constants/accounts-response.schema.ts</a>
            </td>
            <td>variable</td>
            <td>ACCOUNT_ACTIVITY_PAYLOAD_SCHEMA</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#ACCOUNT_ATTRIBUTE_VALUE_PAYLOAD_SCHEMA">src/common/workflows/constants/accounts-response.schema.ts</a>
            </td>
            <td>variable</td>
            <td>ACCOUNT_ATTRIBUTE_VALUE_PAYLOAD_SCHEMA</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#ACCOUNT_NOTE_PAYLOAD_SCHEMA">src/common/workflows/constants/accounts-response.schema.ts</a>
            </td>
            <td>variable</td>
            <td>ACCOUNT_NOTE_PAYLOAD_SCHEMA</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#ACCOUNT_PAYLOAD_SCHEMA">src/common/workflows/constants/accounts-response.schema.ts</a>
            </td>
            <td>variable</td>
            <td>ACCOUNT_PAYLOAD_SCHEMA</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#ACCOUNT_RELATIONSHIP_PAYLOAD_SCHEMA">src/common/workflows/constants/accounts-response.schema.ts</a>
            </td>
            <td>variable</td>
            <td>ACCOUNT_RELATIONSHIP_PAYLOAD_SCHEMA</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#ACCOUNT_RELATIONSHIP_TYPE_PAYLOAD_SCHEMA">src/common/workflows/constants/accounts-response.schema.ts</a>
            </td>
            <td>variable</td>
            <td>ACCOUNT_RELATIONSHIP_TYPE_PAYLOAD_SCHEMA</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#ACCOUNT_TASK_PAYLOAD_SCHEMA">src/common/workflows/constants/accounts-response.schema.ts</a>
            </td>
            <td>variable</td>
            <td>ACCOUNT_TASK_PAYLOAD_SCHEMA</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#CUSTOMER_CONTACT_PAYLOAD_SCHEMA">src/common/workflows/constants/accounts-response.schema.ts</a>
            </td>
            <td>variable</td>
            <td>CUSTOMER_CONTACT_PAYLOAD_SCHEMA</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#COMMON_MESSAGE_PROPERTIES">src/common/workflows/constants/common.schema.ts</a>
            </td>
            <td>variable</td>
            <td>COMMON_MESSAGE_PROPERTIES</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#COMMON_USER_RESPONSE_SCHEMA">src/common/workflows/constants/common.schema.ts</a>
            </td>
            <td>variable</td>
            <td>COMMON_USER_RESPONSE_SCHEMA</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#EXTERNAL_CUSTOM_FIELDS_RESPONSE_SCHEMA">src/common/workflows/constants/common.schema.ts</a>
            </td>
            <td>variable</td>
            <td>EXTERNAL_CUSTOM_FIELDS_RESPONSE_SCHEMA</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#EXTERNAL_STORAGE_RESPONSE_SCHEMA">src/common/workflows/constants/common.schema.ts</a>
            </td>
            <td>variable</td>
            <td>EXTERNAL_STORAGE_RESPONSE_SCHEMA</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#MESSAGE_ATTRIBUTES_SCHEMA">src/common/workflows/constants/common.schema.ts</a>
            </td>
            <td>variable</td>
            <td>MESSAGE_ATTRIBUTES_SCHEMA</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#COMMENT_RESPONSE_SCHEMA">src/common/workflows/constants/communications-response.schema.ts</a>
            </td>
            <td>variable</td>
            <td>COMMENT_RESPONSE_SCHEMA</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#ORGANIZATION_RESPONSE_SCHEMA">src/common/workflows/constants/organization-response.schema.ts</a>
            </td>
            <td>variable</td>
            <td>ORGANIZATION_RESPONSE_SCHEMA</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#COMMON_TICKET_RESPONSE_SCHEMA">src/common/workflows/constants/tickets-response.schema.ts</a>
            </td>
            <td>variable</td>
            <td>COMMON_TICKET_RESPONSE_SCHEMA</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#COMMON_TICKETS_EVENT_SCHEMA">src/common/workflows/constants/tickets-response.schema.ts</a>
            </td>
            <td>variable</td>
            <td>COMMON_TICKETS_EVENT_SCHEMA</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AccountActivityCreatedEvent.html">src/common/workflows/events/accounts.events.ts</a>
            </td>
            <td>class</td>
            <td>AccountActivityCreatedEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AccountActivityDeletedEvent.html">src/common/workflows/events/accounts.events.ts</a>
            </td>
            <td>class</td>
            <td>AccountActivityDeletedEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AccountActivityUpdatedEvent.html">src/common/workflows/events/accounts.events.ts</a>
            </td>
            <td>class</td>
            <td>AccountActivityUpdatedEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AccountCreatedEvent.html">src/common/workflows/events/accounts.events.ts</a>
            </td>
            <td>class</td>
            <td>AccountCreatedEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AccountDeletedEvent.html">src/common/workflows/events/accounts.events.ts</a>
            </td>
            <td>class</td>
            <td>AccountDeletedEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AccountNoteCreatedEvent.html">src/common/workflows/events/accounts.events.ts</a>
            </td>
            <td>class</td>
            <td>AccountNoteCreatedEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AccountNoteDeletedEvent.html">src/common/workflows/events/accounts.events.ts</a>
            </td>
            <td>class</td>
            <td>AccountNoteDeletedEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AccountNoteUpdatedEvent.html">src/common/workflows/events/accounts.events.ts</a>
            </td>
            <td>class</td>
            <td>AccountNoteUpdatedEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AccountRelationshipCreatedEvent.html">src/common/workflows/events/accounts.events.ts</a>
            </td>
            <td>class</td>
            <td>AccountRelationshipCreatedEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AccountRelationshipDeletedEvent.html">src/common/workflows/events/accounts.events.ts</a>
            </td>
            <td>class</td>
            <td>AccountRelationshipDeletedEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AccountRelationshipUpdatedEvent.html">src/common/workflows/events/accounts.events.ts</a>
            </td>
            <td>class</td>
            <td>AccountRelationshipUpdatedEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AccountTaskCreatedEvent.html">src/common/workflows/events/accounts.events.ts</a>
            </td>
            <td>class</td>
            <td>AccountTaskCreatedEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AccountTaskDeletedEvent.html">src/common/workflows/events/accounts.events.ts</a>
            </td>
            <td>class</td>
            <td>AccountTaskDeletedEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AccountTaskUpdatedEvent.html">src/common/workflows/events/accounts.events.ts</a>
            </td>
            <td>class</td>
            <td>AccountTaskUpdatedEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AccountUpdatedEvent.html">src/common/workflows/events/accounts.events.ts</a>
            </td>
            <td>class</td>
            <td>AccountUpdatedEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CustomerContactCreatedEvent.html">src/common/workflows/events/accounts.events.ts</a>
            </td>
            <td>class</td>
            <td>CustomerContactCreatedEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CustomerContactDeletedEvent.html">src/common/workflows/events/accounts.events.ts</a>
            </td>
            <td>class</td>
            <td>CustomerContactDeletedEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CustomerContactUpdatedEvent.html">src/common/workflows/events/accounts.events.ts</a>
            </td>
            <td>class</td>
            <td>CustomerContactUpdatedEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/OrganizationCreatedEvent.html">src/common/workflows/events/organization.events.ts</a>
            </td>
            <td>class</td>
            <td>OrganizationCreatedEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/OrganizationDeletedEvent.html">src/common/workflows/events/organization.events.ts</a>
            </td>
            <td>class</td>
            <td>OrganizationDeletedEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/OrganizationMemberJoinedEvent.html">src/common/workflows/events/organization.events.ts</a>
            </td>
            <td>class</td>
            <td>OrganizationMemberJoinedEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/OrganizationUpdatedEvent.html">src/common/workflows/events/organization.events.ts</a>
            </td>
            <td>class</td>
            <td>OrganizationUpdatedEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/TicketArchivedEvent.html">src/common/workflows/events/tickets.events.ts</a>
            </td>
            <td>class</td>
            <td>TicketArchivedEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/TicketCreatedEvent.html">src/common/workflows/events/tickets.events.ts</a>
            </td>
            <td>class</td>
            <td>TicketCreatedEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/TicketDeletedEvent.html">src/common/workflows/events/tickets.events.ts</a>
            </td>
            <td>class</td>
            <td>TicketDeletedEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/TicketEscalatedEvent.html">src/common/workflows/events/tickets.events.ts</a>
            </td>
            <td>class</td>
            <td>TicketEscalatedEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/TicketUpdatedEvent.html">src/common/workflows/events/tickets.events.ts</a>
            </td>
            <td>class</td>
            <td>TicketUpdatedEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/WorkflowsRegistrySyncService.html">src/common/workflows/registry-sync.service.ts</a>
            </td>
            <td>injectable</td>
            <td>WorkflowsRegistrySyncService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#COMMENT_SNS_PUBLISHER">src/communications/constants/comments.constants.ts</a>
            </td>
            <td>variable</td>
            <td>COMMENT_SNS_PUBLISHER</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#EAGERLY_LOAD_COMMENTS_RELATIONS">src/communications/constants/comments.constants.ts</a>
            </td>
            <td>variable</td>
            <td>EAGERLY_LOAD_COMMENTS_RELATIONS</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/CommentsActionController.html">src/communications/controllers/comments.action.controller.ts</a>
            </td>
            <td>controller</td>
            <td>CommentsActionController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/CommunicationsController.html">src/communications/controllers/communications.controller.ts</a>
            </td>
            <td>controller</td>
            <td>CommunicationsController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/CommentsGrpcController.html">src/communications/controllers/grpc/comments-grpc.controller.ts</a>
            </td>
            <td>controller</td>
            <td>CommentsGrpcController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/12)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/ReactionsGrpcController.html">src/communications/controllers/grpc/reactions-grpc.controller.ts</a>
            </td>
            <td>controller</td>
            <td>ReactionsGrpcController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/ReactionsActionController.html">src/communications/controllers/reactions.action.controller.ts</a>
            </td>
            <td>controller</td>
            <td>ReactionsActionController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetCommentByUserTypeQuery.html">src/communications/dto/comment.queries.ts</a>
            </td>
            <td>class</td>
            <td>GetCommentByUserTypeQuery</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetCommentQuery.html">src/communications/dto/comment.queries.ts</a>
            </td>
            <td>class</td>
            <td>GetCommentQuery</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetCommentsForAnEntityQuery.html">src/communications/dto/comment.queries.ts</a>
            </td>
            <td>class</td>
            <td>GetCommentsForAnEntityQuery</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetCommentThreadsQuery.html">src/communications/dto/comment.queries.ts</a>
            </td>
            <td>class</td>
            <td>GetCommentThreadsQuery</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/CreateComment.html">src/communications/dto/comment.queries.ts</a>
            </td>
            <td>interface</td>
            <td>CreateComment</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/16)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateCommentOnAnEntityDto.html">src/communications/dto/comments.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateCommentOnAnEntityDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/10)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateCommentDto.html">src/communications/dto/comments.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateCommentDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AddReactionDto.html">src/communications/dto/reactions.dto.ts</a>
            </td>
            <td>class</td>
            <td>AddReactionDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/RemoveReactionDto.html">src/communications/dto/reactions.dto.ts</a>
            </td>
            <td>class</td>
            <td>RemoveReactionDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CommonCommentResponse.html">src/communications/dto/response/comment-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>CommonCommentResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetAllCommentsResponse.html">src/communications/dto/response/comment-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>GetAllCommentsResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CommonReactionResponse.html">src/communications/dto/response/reaction-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>CommonReactionResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/Actor.html">src/communications/interfaces/sns-comment-created-payload.interface.ts</a>
            </td>
            <td>interface</td>
            <td>Actor</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/Author.html">src/communications/interfaces/sns-comment-created-payload.interface.ts</a>
            </td>
            <td>interface</td>
            <td>Author</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/CommentAttachmentPayload.html">src/communications/interfaces/sns-comment-created-payload.interface.ts</a>
            </td>
            <td>interface</td>
            <td>CommentAttachmentPayload</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/CommentPayload.html">src/communications/interfaces/sns-comment-created-payload.interface.ts</a>
            </td>
            <td>interface</td>
            <td>CommentPayload</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/13)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/CommentTicketPayload.html">src/communications/interfaces/sns-comment-created-payload.interface.ts</a>
            </td>
            <td>interface</td>
            <td>CommentTicketPayload</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/Payload.html">src/communications/interfaces/sns-comment-created-payload.interface.ts</a>
            </td>
            <td>interface</td>
            <td>Payload</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/SnsCommentCreatedPayload.html">src/communications/interfaces/sns-comment-created-payload.interface.ts</a>
            </td>
            <td>interface</td>
            <td>SnsCommentCreatedPayload</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="medium">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/CommentSnsConsumer.html">src/communications/processors/comment.sns-publish.processor.ts</a>
            </td>
            <td>injectable</td>
            <td>CommentSnsConsumer</td>
            <td align="right" data-sort="33">
                <span class="coverage-percent">33 %</span>
                <span class="coverage-count">(2/6)</span>
            </td>
        </tr>
        <tr class="good">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/CommentsActionService.html">src/communications/services/comments.action.service.ts</a>
            </td>
            <td>injectable</td>
            <td>CommentsActionService</td>
            <td align="right" data-sort="72">
                <span class="coverage-percent">72 %</span>
                <span class="coverage-count">(8/11)</span>
            </td>
        </tr>
        <tr class="good">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/CommunicationsService.html">src/communications/services/communications.service.ts</a>
            </td>
            <td>injectable</td>
            <td>CommunicationsService</td>
            <td align="right" data-sort="62">
                <span class="coverage-percent">62 %</span>
                <span class="coverage-count">(5/8)</span>
            </td>
        </tr>
        <tr class="good">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/ReactionsActionService.html">src/communications/services/reactions.action.service.ts</a>
            </td>
            <td>injectable</td>
            <td>ReactionsActionService</td>
            <td align="right" data-sort="66">
                <span class="coverage-percent">66 %</span>
                <span class="coverage-count">(4/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CommentResponseDto.html">src/communications/transformers/comment-response.transformer.ts</a>
            </td>
            <td>class</td>
            <td>CommentResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/20)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/functions.html#getCommentEventType">src/communications/utils/index.ts</a>
            </td>
            <td>function</td>
            <td>getCommentEventType</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/ConfigService.html">src/config/config.service.ts</a>
            </td>
            <td>injectable</td>
            <td>ConfigService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#entities">src/config/db.config.ts</a>
            </td>
            <td>variable</td>
            <td>entities</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#getPlatformDBConfig">src/config/db.config.ts</a>
            </td>
            <td>variable</td>
            <td>getPlatformDBConfig</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#BLOCK_DURATION_IN_MS">src/config/throttler.config.ts</a>
            </td>
            <td>variable</td>
            <td>BLOCK_DURATION_IN_MS</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#THROTTLER_CONFIG">src/config/throttler.config.ts</a>
            </td>
            <td>variable</td>
            <td>THROTTLER_CONFIG</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#THROTTLER_SPECIAL_TIER">src/config/throttler.config.ts</a>
            </td>
            <td>variable</td>
            <td>THROTTLER_SPECIAL_TIER</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#THROTTLER_TIER">src/config/throttler.config.ts</a>
            </td>
            <td>variable</td>
            <td>THROTTLER_TIER</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#THROTTLER_TIER_1">src/config/throttler.config.ts</a>
            </td>
            <td>variable</td>
            <td>THROTTLER_TIER_1</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#THROTTLER_TIER_2">src/config/throttler.config.ts</a>
            </td>
            <td>variable</td>
            <td>THROTTLER_TIER_2</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#THROTTLER_TIER_3">src/config/throttler.config.ts</a>
            </td>
            <td>variable</td>
            <td>THROTTLER_TIER_3</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#THROTTLER_TIER_4">src/config/throttler.config.ts</a>
            </td>
            <td>variable</td>
            <td>THROTTLER_TIER_4</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#TTL_IN_MS">src/config/throttler.config.ts</a>
            </td>
            <td>variable</td>
            <td>TTL_IN_MS</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#getTypesenseConfig">src/config/typesense.config.ts</a>
            </td>
            <td>variable</td>
            <td>getTypesenseConfig</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#EMBEDDING_CONSTANTS">src/config/typesense/constants/embedding.constants.ts</a>
            </td>
            <td>variable</td>
            <td>EMBEDDING_CONSTANTS</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#ticketEmbeddingsSchema">src/config/typesense/constants/embedding.constants.ts</a>
            </td>
            <td>variable</td>
            <td>ticketEmbeddingsSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#TYPESENSE_CONSTANTS">src/config/typesense/constants/typesense.constants.ts</a>
            </td>
            <td>variable</td>
            <td>TYPESENSE_CONSTANTS</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/TenantConfig.html">src/config/typesense/interfaces/tenant-config.interface.ts</a>
            </td>
            <td>interface</td>
            <td>TenantConfig</td>
            <td align="right" data-sort="100">
                <span class="coverage-percent">100 %</span>
                <span class="coverage-count">(5/5)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/TypesenseConfig.html">src/config/typesense/interfaces/typesense-config.interface.ts</a>
            </td>
            <td>interface</td>
            <td>TypesenseConfig</td>
            <td align="right" data-sort="100">
                <span class="coverage-percent">100 %</span>
                <span class="coverage-count">(3/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#ERROR_CODES">src/constants/error-codes.constants.ts</a>
            </td>
            <td>variable</td>
            <td>ERROR_CODES</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#QueueNames">src/constants/queue.constants.ts</a>
            </td>
            <td>variable</td>
            <td>QueueNames</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#CustomFieldValidatorConstants">src/custom-field/constants/constants.ts</a>
            </td>
            <td>variable</td>
            <td>CustomFieldValidatorConstants</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/CustomFieldController.html">src/custom-field/controllers/custom-field.controller.ts</a>
            </td>
            <td>controller</td>
            <td>CustomFieldController</td>
            <td align="right" data-sort="10">
                <span class="coverage-percent">10 %</span>
                <span class="coverage-count">(1/10)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/ValidationPipe.html">src/custom-field/custom-validator.ts</a>
            </td>
            <td>injectable</td>
            <td>ValidationPipe</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CommonCustomFieldValuesDto.html">src/custom-field/dto/custom-field-values.dto.ts</a>
            </td>
            <td>class</td>
            <td>CommonCustomFieldValuesDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateCustomFieldValuesDto.html">src/custom-field/dto/custom-field-values.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateCustomFieldValuesDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ExternalCustomFieldValuesDto.html">src/custom-field/dto/custom-field-values.dto.ts</a>
            </td>
            <td>class</td>
            <td>ExternalCustomFieldValuesDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateCustomFieldValuesDto.html">src/custom-field/dto/custom-field-values.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateCustomFieldValuesDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ValueData.html">src/custom-field/dto/custom-field-values.dto.ts</a>
            </td>
            <td>class</td>
            <td>ValueData</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/BatchCustomFieldResponseDto.html">src/custom-field/dto/custom-field.dto.ts</a>
            </td>
            <td>class</td>
            <td>BatchCustomFieldResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CommonCustomFieldDto.html">src/custom-field/dto/custom-field.dto.ts</a>
            </td>
            <td>class</td>
            <td>CommonCustomFieldDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateCustomFieldDto.html">src/custom-field/dto/custom-field.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateCustomFieldDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/17)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CustomFieldData.html">src/custom-field/dto/custom-field.dto.ts</a>
            </td>
            <td>class</td>
            <td>CustomFieldData</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/22)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CustomFieldOptionDto.html">src/custom-field/dto/custom-field.dto.ts</a>
            </td>
            <td>class</td>
            <td>CustomFieldOptionDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CustomFieldResponseDto.html">src/custom-field/dto/custom-field.dto.ts</a>
            </td>
            <td>class</td>
            <td>CustomFieldResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CustomFieldTypesData.html">src/custom-field/dto/custom-field.dto.ts</a>
            </td>
            <td>class</td>
            <td>CustomFieldTypesData</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/13)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CustomFieldUpdateData.html">src/custom-field/dto/custom-field.dto.ts</a>
            </td>
            <td>class</td>
            <td>CustomFieldUpdateData</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DeleteCustomFieldDto.html">src/custom-field/dto/custom-field.dto.ts</a>
            </td>
            <td>class</td>
            <td>DeleteCustomFieldDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DeleteFieldItemDto.html">src/custom-field/dto/custom-field.dto.ts</a>
            </td>
            <td>class</td>
            <td>DeleteFieldItemDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetAllCustomFieldsResponse.html">src/custom-field/dto/custom-field.dto.ts</a>
            </td>
            <td>class</td>
            <td>GetAllCustomFieldsResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetAllCustomFieldTypesResponse.html">src/custom-field/dto/custom-field.dto.ts</a>
            </td>
            <td>class</td>
            <td>GetAllCustomFieldTypesResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateCustomFieldDto.html">src/custom-field/dto/custom-field.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateCustomFieldDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateFieldsDto.html">src/custom-field/dto/custom-field.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateFieldsDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/12)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/CustomFieldValuesService.html">src/custom-field/services/custom-field-values.service.ts</a>
            </td>
            <td>injectable</td>
            <td>CustomFieldValuesService</td>
            <td align="right" data-sort="77">
                <span class="coverage-percent">77 %</span>
                <span class="coverage-count">(7/9)</span>
            </td>
        </tr>
        <tr class="good">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/CustomFieldService.html">src/custom-field/services/custom-field.service.ts</a>
            </td>
            <td>injectable</td>
            <td>CustomFieldService</td>
            <td align="right" data-sort="63">
                <span class="coverage-percent">63 %</span>
                <span class="coverage-count">(7/11)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/ThenaRestrictedFieldService.html">src/custom-field/services/thena-restricted-field.service.ts</a>
            </td>
            <td>injectable</td>
            <td>ThenaRestrictedFieldService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DateValidation.html">src/custom-field/utils/helper.ts</a>
            </td>
            <td>class</td>
            <td>DateValidation</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DecimalValidation.html">src/custom-field/utils/helper.ts</a>
            </td>
            <td>class</td>
            <td>DecimalValidation</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/EmailValidation.html">src/custom-field/utils/helper.ts</a>
            </td>
            <td>class</td>
            <td>EmailValidation</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/IpAddressValidation.html">src/custom-field/utils/helper.ts</a>
            </td>
            <td>class</td>
            <td>IpAddressValidation</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/NumberValidation.html">src/custom-field/utils/helper.ts</a>
            </td>
            <td>class</td>
            <td>NumberValidation</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UrlValidation.html">src/custom-field/utils/helper.ts</a>
            </td>
            <td>class</td>
            <td>UrlValidation</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#isValidDate">src/custom-field/utils/helper.ts</a>
            </td>
            <td>variable</td>
            <td>isValidDate</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#isValidDateTime">src/custom-field/utils/helper.ts</a>
            </td>
            <td>variable</td>
            <td>isValidDateTime</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#isValidEmail">src/custom-field/utils/helper.ts</a>
            </td>
            <td>variable</td>
            <td>isValidEmail</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#isValidNumber">src/custom-field/utils/helper.ts</a>
            </td>
            <td>variable</td>
            <td>isValidNumber</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#isValidPassword">src/custom-field/utils/helper.ts</a>
            </td>
            <td>variable</td>
            <td>isValidPassword</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#isValidPhoneNumber">src/custom-field/utils/helper.ts</a>
            </td>
            <td>variable</td>
            <td>isValidPhoneNumber</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#isValidRegex">src/custom-field/utils/helper.ts</a>
            </td>
            <td>variable</td>
            <td>isValidRegex</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#isValidString">src/custom-field/utils/helper.ts</a>
            </td>
            <td>variable</td>
            <td>isValidString</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#isValidUrl">src/custom-field/utils/helper.ts</a>
            </td>
            <td>variable</td>
            <td>isValidUrl</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/CustomFieldvalidatorService.html">src/custom-field/validators/custom-field.validator.ts</a>
            </td>
            <td>injectable</td>
            <td>CustomFieldvalidatorService</td>
            <td align="right" data-sort="14">
                <span class="coverage-percent">14 %</span>
                <span class="coverage-count">(1/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#GetObjectRelations">src/custom-object/constants/index.ts</a>
            </td>
            <td>variable</td>
            <td>GetObjectRelations</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/CustomObjectController.html">src/custom-object/controllers/custom-object.controller.ts</a>
            </td>
            <td>controller</td>
            <td>CustomObjectController</td>
            <td align="right" data-sort="12">
                <span class="coverage-percent">12 %</span>
                <span class="coverage-count">(1/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateCustomObjectDto.html">src/custom-object/dto/custom-object.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateCustomObjectDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CustomObjectResponseDto.html">src/custom-object/dto/custom-object.dto.ts</a>
            </td>
            <td>class</td>
            <td>CustomObjectResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CustomObjectUpdateData.html">src/custom-object/dto/custom-object.dto.ts</a>
            </td>
            <td>class</td>
            <td>CustomObjectUpdateData</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetAllCustomObjectsResponse.html">src/custom-object/dto/custom-object.dto.ts</a>
            </td>
            <td>class</td>
            <td>GetAllCustomObjectsResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateCustomObjectDto.html">src/custom-object/dto/custom-object.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateCustomObjectDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateCustomObjectMetaDTO.html">src/custom-object/dto/custom-object.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateCustomObjectMetaDTO</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="medium">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/CustomObjectService.html">src/custom-object/services/custom-object.service.ts</a>
            </td>
            <td>injectable</td>
            <td>CustomObjectService</td>
            <td align="right" data-sort="37">
                <span class="coverage-percent">37 %</span>
                <span class="coverage-count">(3/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/CustomObjectValidatorService.html">src/custom-object/validators/custom-object.validator.ts</a>
            </td>
            <td>injectable</td>
            <td>CustomObjectValidatorService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DatabaseExceptionFilter.html">src/filters/database-exception.filter.ts</a>
            </td>
            <td>class</td>
            <td>DatabaseExceptionFilter</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/typealiases.html#ErrorConfig">src/filters/database-exception.filter.ts</a>
            </td>
            <td>type alias</td>
            <td>ErrorConfig</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/typealiases.html#ErrorResponse">src/filters/database-exception.filter.ts</a>
            </td>
            <td>type alias</td>
            <td>ErrorResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/typealiases.html#PostgresErrorCode">src/filters/database-exception.filter.ts</a>
            </td>
            <td>type alias</td>
            <td>PostgresErrorCode</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/HttpExceptionFilter.html">src/filters/http-exception.filter.ts</a>
            </td>
            <td>class</td>
            <td>HttpExceptionFilter</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#THENA_RESTRICTED_FIELDS">src/forms/constants/index.ts</a>
            </td>
            <td>variable</td>
            <td>THENA_RESTRICTED_FIELDS</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#UpdatableThenaRestrictedFieldsProperties">src/forms/constants/index.ts</a>
            </td>
            <td>variable</td>
            <td>UpdatableThenaRestrictedFieldsProperties</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/FormsController.html">src/forms/controllers/form.controller.ts</a>
            </td>
            <td>controller</td>
            <td>FormsController</td>
            <td align="right" data-sort="11">
                <span class="coverage-percent">11 %</span>
                <span class="coverage-count">(1/9)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/Condition.html">src/forms/dto/form.dto.ts</a>
            </td>
            <td>class</td>
            <td>Condition</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateFormDto.html">src/forms/dto/form.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateFormDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DeleteFormItemDto.html">src/forms/dto/form.dto.ts</a>
            </td>
            <td>class</td>
            <td>DeleteFormItemDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DeleteFormsDto.html">src/forms/dto/form.dto.ts</a>
            </td>
            <td>class</td>
            <td>DeleteFormsDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/FormData.html">src/forms/dto/form.dto.ts</a>
            </td>
            <td>class</td>
            <td>FormData</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/10)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/FormFieldDto.html">src/forms/dto/form.dto.ts</a>
            </td>
            <td>class</td>
            <td>FormFieldDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/FormResponse.html">src/forms/dto/form.dto.ts</a>
            </td>
            <td>class</td>
            <td>FormResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/13)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/FormResponseDto.html">src/forms/dto/form.dto.ts</a>
            </td>
            <td>class</td>
            <td>FormResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetAllFormsResponse.html">src/forms/dto/form.dto.ts</a>
            </td>
            <td>class</td>
            <td>GetAllFormsResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/TargetField.html">src/forms/dto/form.dto.ts</a>
            </td>
            <td>class</td>
            <td>TargetField</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/TargetFieldOption.html">src/forms/dto/form.dto.ts</a>
            </td>
            <td>class</td>
            <td>TargetFieldOption</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateFormDto.html">src/forms/dto/form.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateFormDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateFormItemDto.html">src/forms/dto/form.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateFormItemDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/functions.html#IsValidTriggerFieldValue">src/forms/dto/form.dto.ts</a>
            </td>
            <td>function</td>
            <td>IsValidTriggerFieldValue</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/FormSetupService.html">src/forms/form-setup.ts</a>
            </td>
            <td>injectable</td>
            <td>FormSetupService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#createdCustomFields">src/forms/form-setup.ts</a>
            </td>
            <td>variable</td>
            <td>createdCustomFields</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#multiChoiceField">src/forms/form-setup.ts</a>
            </td>
            <td>variable</td>
            <td>multiChoiceField</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#multiChoiceFieldBody">src/forms/form-setup.ts</a>
            </td>
            <td>variable</td>
            <td>multiChoiceFieldBody</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#phoneNoField">src/forms/form-setup.ts</a>
            </td>
            <td>variable</td>
            <td>phoneNoField</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#phoneNoFieldBody">src/forms/form-setup.ts</a>
            </td>
            <td>variable</td>
            <td>phoneNoFieldBody</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#simpleTextField">src/forms/form-setup.ts</a>
            </td>
            <td>variable</td>
            <td>simpleTextField</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#simpleTextFieldBody">src/forms/form-setup.ts</a>
            </td>
            <td>variable</td>
            <td>simpleTextFieldBody</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#singleChoiceField">src/forms/form-setup.ts</a>
            </td>
            <td>variable</td>
            <td>singleChoiceField</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#singleChoiceFieldBody">src/forms/form-setup.ts</a>
            </td>
            <td>variable</td>
            <td>singleChoiceFieldBody</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/FormsListeners.html">src/forms/listeners/forms.listeners.ts</a>
            </td>
            <td>class</td>
            <td>FormsListeners</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="good">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/FormService.html">src/forms/services/form.service.ts</a>
            </td>
            <td>injectable</td>
            <td>FormService</td>
            <td align="right" data-sort="60">
                <span class="coverage-percent">60 %</span>
                <span class="coverage-count">(6/10)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/FormsValidatorService.html">src/forms/validators/form.validator.ts</a>
            </td>
            <td>injectable</td>
            <td>FormsValidatorService</td>
            <td align="right" data-sort="7">
                <span class="coverage-percent">7 %</span>
                <span class="coverage-count">(1/14)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#grpcServerConfig">src/grpc.server.config.ts</a>
            </td>
            <td>variable</td>
            <td>grpcServerConfig</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/HealthController.html">src/health/health.controller.ts</a>
            </td>
            <td>controller</td>
            <td>HealthController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/HealthService.html">src/health/health.service.ts</a>
            </td>
            <td>injectable</td>
            <td>HealthService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/functions.html#bootstrap">src/main.ts</a>
            </td>
            <td>function</td>
            <td>bootstrap</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/OrganizationGrpcController.html">src/organization/controllers/organization-grpc.controller.ts</a>
            </td>
            <td>controller</td>
            <td>OrganizationGrpcController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/OrganizationController.html">src/organization/controllers/organization.controller.ts</a>
            </td>
            <td>controller</td>
            <td>OrganizationController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CommonOrganizationDto.html">src/organization/dto/organization.dto.ts</a>
            </td>
            <td>class</td>
            <td>CommonOrganizationDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateOrganizationDto.html">src/organization/dto/organization.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateOrganizationDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/InviteUserDto.html">src/organization/dto/organization.dto.ts</a>
            </td>
            <td>class</td>
            <td>InviteUserDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/JoinOrganizationDto.html">src/organization/dto/organization.dto.ts</a>
            </td>
            <td>class</td>
            <td>JoinOrganizationDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateOrganizationDto.html">src/organization/dto/organization.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateOrganizationDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CommonOrganizationResponse.html">src/organization/dto/response/organization-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>CommonOrganizationResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetAllOrganizationsResponse.html">src/organization/dto/response/organization-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>GetAllOrganizationsResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/OrganizationEventsFactory.html">src/organization/events/organization-events.factory.ts</a>
            </td>
            <td>injectable</td>
            <td>OrganizationEventsFactory</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/OrganizationSNSEventsFactory.html">src/organization/events/organization-sns-events.factory.ts</a>
            </td>
            <td>injectable</td>
            <td>OrganizationSNSEventsFactory</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/OrganizationCreatedEvent.html">src/organization/events/organization.events.ts</a>
            </td>
            <td>class</td>
            <td>OrganizationCreatedEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#EmittableOrganizationEvents">src/organization/events/organization.events.ts</a>
            </td>
            <td>variable</td>
            <td>EmittableOrganizationEvents</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/Actor.html">src/organization/interfaces/sns-events.interface.ts</a>
            </td>
            <td>interface</td>
            <td>Actor</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/OrganizationPayload.html">src/organization/interfaces/sns-events.interface.ts</a>
            </td>
            <td>interface</td>
            <td>OrganizationPayload</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/SNSEvent.html">src/organization/interfaces/sns-events.interface.ts</a>
            </td>
            <td>interface</td>
            <td>SNSEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/UserJoinedOrganizationPayload.html">src/organization/interfaces/sns-events.interface.ts</a>
            </td>
            <td>interface</td>
            <td>UserJoinedOrganizationPayload</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/OrganizationSNSPublisher.html">src/organization/processors/sns-publisher.processor.ts</a>
            </td>
            <td>injectable</td>
            <td>OrganizationSNSPublisher</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="good">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/CreateOrgAndOrgAdminSaga.html">src/organization/sagas/create-org-and-admin.saga.ts</a>
            </td>
            <td>injectable</td>
            <td>CreateOrgAndOrgAdminSaga</td>
            <td align="right" data-sort="66">
                <span class="coverage-percent">66 %</span>
                <span class="coverage-count">(2/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/CreateOrgAndOrgAdminSagaInput.html">src/organization/sagas/create-org-and-admin.saga.ts</a>
            </td>
            <td>interface</td>
            <td>CreateOrgAndOrgAdminSagaInput</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#noop">src/organization/sagas/create-org-and-admin.saga.ts</a>
            </td>
            <td>variable</td>
            <td>noop</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/typealiases.html#CreateOrgAndOrgAdminSagaSkipSteps">src/organization/sagas/create-org-and-admin.saga.ts</a>
            </td>
            <td>type alias</td>
            <td>CreateOrgAndOrgAdminSagaSkipSteps</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/OrganizationService.html">src/organization/services/organization.service.ts</a>
            </td>
            <td>injectable</td>
            <td>OrganizationService</td>
            <td align="right" data-sort="83">
                <span class="coverage-percent">83 %</span>
                <span class="coverage-count">(10/12)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/OrganizationResponseDto.html">src/organization/transformers/organization.transformer.ts</a>
            </td>
            <td>class</td>
            <td>OrganizationResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/10)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#createOrganizationSchema">src/organization/validators/index.ts</a>
            </td>
            <td>variable</td>
            <td>createOrganizationSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#updateOrganizationSchema">src/organization/validators/index.ts</a>
            </td>
            <td>variable</td>
            <td>updateOrganizationSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#priorities">src/shared/constants/tickets.constants.ts</a>
            </td>
            <td>variable</td>
            <td>priorities</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#statuses">src/shared/constants/tickets.constants.ts</a>
            </td>
            <td>variable</td>
            <td>statuses</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#types">src/shared/constants/tickets.constants.ts</a>
            </td>
            <td>variable</td>
            <td>types</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="good">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/SharedService.html">src/shared/shared.service.ts</a>
            </td>
            <td>injectable</td>
            <td>SharedService</td>
            <td align="right" data-sort="58">
                <span class="coverage-percent">58 %</span>
                <span class="coverage-count">(7/12)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/StorageController.html">src/storage/controller/storage.controller.ts</a>
            </td>
            <td>controller</td>
            <td>StorageController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateStorageDto.html">src/storage/dto/storage.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateStorageDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/11)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ExternalStorageResponseDto.html">src/storage/dto/storage.dto.ts</a>
            </td>
            <td>class</td>
            <td>ExternalStorageResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/StorageResponseDto.html">src/storage/dto/storage.dto.ts</a>
            </td>
            <td>class</td>
            <td>StorageResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateStorageDto.html">src/storage/dto/storage.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateStorageDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/StorageData.html">src/storage/dto/storage.dto.ts</a>
            </td>
            <td>interface</td>
            <td>StorageData</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/GetFolderContentsOptions.html">src/storage/interfaces/storage.interface.ts</a>
            </td>
            <td>interface</td>
            <td>GetFolderContentsOptions</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IStorageProvider.html">src/storage/interfaces/storage.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IStorageProvider</td>
            <td align="right" data-sort="81">
                <span class="coverage-percent">81 %</span>
                <span class="coverage-count">(9/11)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/SignedFolderContent.html">src/storage/interfaces/storage.interface.ts</a>
            </td>
            <td>interface</td>
            <td>SignedFolderContent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/LocalStorageProvider.html">src/storage/providers/local-storage.provider.ts</a>
            </td>
            <td>injectable</td>
            <td>LocalStorageProvider</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/11)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/S3StorageProvider.html">src/storage/providers/s3-storage.provider.ts</a>
            </td>
            <td>injectable</td>
            <td>S3StorageProvider</td>
            <td align="right" data-sort="5">
                <span class="coverage-percent">5 %</span>
                <span class="coverage-count">(1/20)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/StorageService.html">src/storage/services/storage-service.ts</a>
            </td>
            <td>injectable</td>
            <td>StorageService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/12)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/FastifyFileRequest.html">src/storage/types/storage.types.ts</a>
            </td>
            <td>interface</td>
            <td>FastifyFileRequest</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/S3Config.html">src/storage/types/storage.types.ts</a>
            </td>
            <td>interface</td>
            <td>S3Config</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/StorageConfig.html">src/storage/types/storage.types.ts</a>
            </td>
            <td>interface</td>
            <td>StorageConfig</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/StorageOptions.html">src/storage/types/storage.types.ts</a>
            </td>
            <td>interface</td>
            <td>StorageOptions</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/StorageResponse.html">src/storage/types/storage.types.ts</a>
            </td>
            <td>interface</td>
            <td>StorageResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/typealiases.html#StorageType">src/storage/types/storage.types.ts</a>
            </td>
            <td>type alias</td>
            <td>StorageType</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/SwaggerController.html">src/swagger/swagger.controller.ts</a>
            </td>
            <td>controller</td>
            <td>SwaggerController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/SwaggerService.html">src/swagger/swagger.service.ts</a>
            </td>
            <td>injectable</td>
            <td>SwaggerService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/TagsGrpcController.html">src/tags/controllers/grpc/tags-grpc.controller.ts</a>
            </td>
            <td>controller</td>
            <td>TagsGrpcController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/TagsController.html">src/tags/controllers/tags.controller.ts</a>
            </td>
            <td>controller</td>
            <td>TagsController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/TeamsTagsController.html">src/tags/controllers/team-tags.controller.ts</a>
            </td>
            <td>controller</td>
            <td>TeamsTagsController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/TicketTagsController.html">src/tags/controllers/ticket-tags.controller.ts</a>
            </td>
            <td>controller</td>
            <td>TicketTagsController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CommonTagResponse.html">src/tags/dto/response/tags-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>CommonTagResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetAllTagsResponse.html">src/tags/dto/response/tags-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>GetAllTagsResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateTagDto.html">src/tags/dto/tag.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateTagDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/TagIdDto.html">src/tags/dto/tag.dto.ts</a>
            </td>
            <td>class</td>
            <td>TagIdDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateTagDto.html">src/tags/dto/tag.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateTagDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateTagDto.html">src/tags/dto/tags.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateTagDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetAllTagsQuery.html">src/tags/dto/tags.dto.ts</a>
            </td>
            <td>class</td>
            <td>GetAllTagsQuery</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AddTagsDto.html">src/tags/dto/ticket-tag.dto.ts</a>
            </td>
            <td>class</td>
            <td>AddTagsDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateTagsResponse.html">src/tags/interfaces/tag-response.interface.ts</a>
            </td>
            <td>class</td>
            <td>CreateTagsResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateTicketTagsResponse.html">src/tags/interfaces/tag-response.interface.ts</a>
            </td>
            <td>class</td>
            <td>CreateTicketTagsResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DeleteTagResponse.html">src/tags/interfaces/tag-response.interface.ts</a>
            </td>
            <td>class</td>
            <td>DeleteTagResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DeleteTicketTagResponse.html">src/tags/interfaces/tag-response.interface.ts</a>
            </td>
            <td>class</td>
            <td>DeleteTicketTagResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetTagsResponse.html">src/tags/interfaces/tag-response.interface.ts</a>
            </td>
            <td>class</td>
            <td>GetTagsResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetTicketTagsResponse.html">src/tags/interfaces/tag-response.interface.ts</a>
            </td>
            <td>class</td>
            <td>GetTicketTagsResponse</td>
            <td align="right" data-sort="16">
                <span class="coverage-percent">16 %</span>
                <span class="coverage-count">(1/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/TagListDto.html">src/tags/interfaces/tag-response.interface.ts</a>
            </td>
            <td>class</td>
            <td>TagListDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateTagsResponse.html">src/tags/interfaces/tag-response.interface.ts</a>
            </td>
            <td>class</td>
            <td>UpdateTagsResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="medium">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/TagAnnotatorService.html">src/tags/services/tag-annotator.service.ts</a>
            </td>
            <td>injectable</td>
            <td>TagAnnotatorService</td>
            <td align="right" data-sort="50">
                <span class="coverage-percent">50 %</span>
                <span class="coverage-count">(2/4)</span>
            </td>
        </tr>
        <tr class="good">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/TagsService.html">src/tags/services/tags.service.ts</a>
            </td>
            <td>injectable</td>
            <td>TagsService</td>
            <td align="right" data-sort="75">
                <span class="coverage-percent">75 %</span>
                <span class="coverage-count">(6/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DuplicateTagException.html">src/tags/services/team-tags.service.ts</a>
            </td>
            <td>class</td>
            <td>DuplicateTagException</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/TeamInactiveException.html">src/tags/services/team-tags.service.ts</a>
            </td>
            <td>class</td>
            <td>TeamInactiveException</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="good">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/TeamTagsService.html">src/tags/services/team-tags.service.ts</a>
            </td>
            <td>injectable</td>
            <td>TeamTagsService</td>
            <td align="right" data-sort="66">
                <span class="coverage-percent">66 %</span>
                <span class="coverage-count">(4/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/TagsNotFoundException.html">src/tags/services/ticket-tags.service.ts</a>
            </td>
            <td>class</td>
            <td>TagsNotFoundException</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/TeamNotAssignedException.html">src/tags/services/ticket-tags.service.ts</a>
            </td>
            <td>class</td>
            <td>TeamNotAssignedException</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/TicketTagService.html">src/tags/services/ticket-tags.service.ts</a>
            </td>
            <td>injectable</td>
            <td>TicketTagService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/TagsResponseDto.html">src/tags/transformers/tags.transformer.ts</a>
            </td>
            <td>class</td>
            <td>TagsResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#EAGERLY_LOADED_RELATIONS_FOR_TEAM_MEMBERS">src/teams/constants/team-members.constants.ts</a>
            </td>
            <td>variable</td>
            <td>EAGERLY_LOADED_RELATIONS_FOR_TEAM_MEMBERS</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#EAGERLY_LOADED_RELATIONS">src/teams/constants/teams.constants.ts</a>
            </td>
            <td>variable</td>
            <td>EAGERLY_LOADED_RELATIONS</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#TEAM_ROUTING_RULES_RELATIONS">src/teams/constants/teams.constants.ts</a>
            </td>
            <td>variable</td>
            <td>TEAM_ROUTING_RULES_RELATIONS</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/TeamsGrpcController.html">src/teams/controllers/teams-grpc.controller.ts</a>
            </td>
            <td>controller</td>
            <td>TeamsGrpcController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/24)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/TeamsController.html">src/teams/controllers/teams.controller.ts</a>
            </td>
            <td>controller</td>
            <td>TeamsController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/17)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#LOG_SPAN_ID">src/teams/controllers/teams.controller.ts</a>
            </td>
            <td>variable</td>
            <td>LOG_SPAN_ID</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CommonTeamConfigurationsResponse.html">src/teams/dto/response/team-configuration-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>CommonTeamConfigurationsResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/TeamConfigurationsResponse.html">src/teams/dto/response/team-configuration-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>TeamConfigurationsResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CommonTeamMemberResponse.html">src/teams/dto/response/team-member-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>CommonTeamMemberResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetAllTeamMembersResponse.html">src/teams/dto/response/team-member-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>GetAllTeamMembersResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CommonTeamResponse.html">src/teams/dto/response/team-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>CommonTeamResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetAllTeamsResponse.html">src/teams/dto/response/team-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>GetAllTeamsResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CommonTeamRoutingRuleResponse.html">src/teams/dto/response/team-routing-rule-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>CommonTeamRoutingRuleResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetAllTeamRoutingRulesResponse.html">src/teams/dto/response/team-routing-rule-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>GetAllTeamRoutingRulesResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AddTeamMemberDto.html">src/teams/dto/team-member.dto.ts</a>
            </td>
            <td>class</td>
            <td>AddTeamMemberDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CommonRoutingRuleGroupDto.html">src/teams/dto/team-routing.dto.ts</a>
            </td>
            <td>class</td>
            <td>CommonRoutingRuleGroupDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateRoutingRuleGroupDto.html">src/teams/dto/team-routing.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateRoutingRuleGroupDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/RuleDto.html">src/teams/dto/team-routing.dto.ts</a>
            </td>
            <td>class</td>
            <td>RuleDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateRoutingRuleGroupDto.html">src/teams/dto/team-routing.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateRoutingRuleGroupDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateTeamDto.html">src/teams/dto/teams.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateTeamDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateTeamDto.html">src/teams/dto/teams.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateTeamDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/TeamAnnotatorService.html">src/teams/services/team-annotator.service.ts</a>
            </td>
            <td>injectable</td>
            <td>TeamAnnotatorService</td>
            <td align="right" data-sort="83">
                <span class="coverage-percent">83 %</span>
                <span class="coverage-count">(10/12)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/TeamsService.html">src/teams/services/teams.service.ts</a>
            </td>
            <td>injectable</td>
            <td>TeamsService</td>
            <td align="right" data-sort="91">
                <span class="coverage-percent">91 %</span>
                <span class="coverage-count">(31/34)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/CombinedTeamConfig.html">src/teams/services/teams.service.ts</a>
            </td>
            <td>interface</td>
            <td>CombinedTeamConfig</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#TEAM_ROLE_MEMBER">src/teams/teams.constants.ts</a>
            </td>
            <td>variable</td>
            <td>TEAM_ROLE_MEMBER</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#TEAM_ROLE_OWNER">src/teams/teams.constants.ts</a>
            </td>
            <td>variable</td>
            <td>TEAM_ROLE_OWNER</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/BusinessDays.html">src/teams/transformers/team-configuration.transformer.ts</a>
            </td>
            <td>class</td>
            <td>BusinessDays</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/TeamConfigurationsResponseDto.html">src/teams/transformers/team-configuration.transformer.ts</a>
            </td>
            <td>class</td>
            <td>TeamConfigurationsResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/12)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/TeamMemberResponseDto.html">src/teams/transformers/team-members.transformer.ts</a>
            </td>
            <td>class</td>
            <td>TeamMemberResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/13)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/TeamRoutingRuleResponseDto.html">src/teams/transformers/team-routing-rule.transformer.ts</a>
            </td>
            <td>class</td>
            <td>TeamRoutingRuleResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/15)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/TeamResponseDto.html">src/teams/transformers/teams.transformer.ts</a>
            </td>
            <td>class</td>
            <td>TeamResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/18)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#addTeamMemberSchema">src/teams/validators/index.ts</a>
            </td>
            <td>variable</td>
            <td>addTeamMemberSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#businessDaySchema">src/teams/validators/index.ts</a>
            </td>
            <td>variable</td>
            <td>businessDaySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#businessHoursConfigSchema">src/teams/validators/index.ts</a>
            </td>
            <td>variable</td>
            <td>businessHoursConfigSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#businessSlotSchema">src/teams/validators/index.ts</a>
            </td>
            <td>variable</td>
            <td>businessSlotSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#CreateRoutingRuleSchema">src/teams/validators/index.ts</a>
            </td>
            <td>variable</td>
            <td>CreateRoutingRuleSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#createTeamSchema">src/teams/validators/index.ts</a>
            </td>
            <td>variable</td>
            <td>createTeamSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#isValidTimeFormat">src/teams/validators/index.ts</a>
            </td>
            <td>variable</td>
            <td>isValidTimeFormat</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#RuleOperatorEnum">src/teams/validators/index.ts</a>
            </td>
            <td>variable</td>
            <td>RuleOperatorEnum</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#RuleSchema">src/teams/validators/index.ts</a>
            </td>
            <td>variable</td>
            <td>RuleSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#UpdateRoutingRuleSchema">src/teams/validators/index.ts</a>
            </td>
            <td>variable</td>
            <td>UpdateRoutingRuleSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#updateTeamSchema">src/teams/validators/index.ts</a>
            </td>
            <td>variable</td>
            <td>updateTeamSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#updateTimezoneWorkingHoursSchema">src/teams/validators/index.ts</a>
            </td>
            <td>variable</td>
            <td>updateTimezoneWorkingHoursSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#GetTicketsRelations">src/tickets/constants/tickets.constants.ts</a>
            </td>
            <td>variable</td>
            <td>GetTicketsRelations</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#TicketSelect">src/tickets/constants/tickets.constants.ts</a>
            </td>
            <td>variable</td>
            <td>TicketSelect</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/FastifyFileInterceptor.html">src/tickets/controllers/fastify-file.interceptor.ts</a>
            </td>
            <td>injectable</td>
            <td>FastifyFileInterceptor</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/FastifyRequest.html">src/tickets/controllers/fastify-file.interceptor.ts</a>
            </td>
            <td>interface</td>
            <td>FastifyRequest</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/TicketsGrpcController.html">src/tickets/controllers/grpc/tickets-grpc.controller.ts</a>
            </td>
            <td>controller</td>
            <td>TicketsGrpcController</td>
            <td align="right" data-sort="5">
                <span class="coverage-percent">5 %</span>
                <span class="coverage-count">(1/18)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#createTicketValidator">src/tickets/controllers/grpc/validators/index.ts</a>
            </td>
            <td>variable</td>
            <td>createTicketValidator</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#updateTicketValidator">src/tickets/controllers/grpc/validators/index.ts</a>
            </td>
            <td>variable</td>
            <td>updateTicketValidator</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/TicketBulkActionController.html">src/tickets/controllers/ticket-bulk.action.controller.ts</a>
            </td>
            <td>controller</td>
            <td>TicketBulkActionController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/TicketDraftController.html">src/tickets/controllers/ticket-draft.action.controller.ts</a>
            </td>
            <td>controller</td>
            <td>TicketDraftController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/TicketPriorityActionController.html">src/tickets/controllers/ticket-priority.action.controller.ts</a>
            </td>
            <td>controller</td>
            <td>TicketPriorityActionController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/TicketSearchController.html">src/tickets/controllers/ticket-search.action.controller.ts</a>
            </td>
            <td>controller</td>
            <td>TicketSearchController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/TicketStatusActionController.html">src/tickets/controllers/ticket-status.action.controller.ts</a>
            </td>
            <td>controller</td>
            <td>TicketStatusActionController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/TicketTypeActionController.html">src/tickets/controllers/ticket-type.action.controller.ts</a>
            </td>
            <td>controller</td>
            <td>TicketTypeActionController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/TicketsController.html">src/tickets/controllers/tickets.controller.ts</a>
            </td>
            <td>controller</td>
            <td>TicketsController</td>
            <td align="right" data-sort="4">
                <span class="coverage-percent">4 %</span>
                <span class="coverage-count">(1/23)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetDraftTicketQuery.html">src/tickets/dto/queries.dto.ts</a>
            </td>
            <td>class</td>
            <td>GetDraftTicketQuery</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetTicketQuery.html">src/tickets/dto/queries.dto.ts</a>
            </td>
            <td>class</td>
            <td>GetTicketQuery</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetTicketRelatedQuery.html">src/tickets/dto/queries.dto.ts</a>
            </td>
            <td>class</td>
            <td>GetTicketRelatedQuery</td>
            <td align="right" data-sort="83">
                <span class="coverage-percent">83 %</span>
                <span class="coverage-count">(5/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/AssignTicketQuery.html">src/tickets/dto/queries.dto.ts</a>
            </td>
            <td>interface</td>
            <td>AssignTicketQuery</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/SearchTicketsQuery.html">src/tickets/dto/queries.dto.ts</a>
            </td>
            <td>interface</td>
            <td>SearchTicketsQuery</td>
            <td align="right" data-sort="87">
                <span class="coverage-percent">87 %</span>
                <span class="coverage-count">(7/8)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/SearchTicketsResponse.html">src/tickets/dto/queries.dto.ts</a>
            </td>
            <td>interface</td>
            <td>SearchTicketsResponse</td>
            <td align="right" data-sort="100">
                <span class="coverage-percent">100 %</span>
                <span class="coverage-count">(6/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CommonTicketBulkResponse.html">src/tickets/dto/response/ticket-bulk-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>CommonTicketBulkResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetAllTicketsBulkResponse.html">src/tickets/dto/response/ticket-bulk-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>GetAllTicketsBulkResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/SkippedTicketBulkResponseDto.html">src/tickets/dto/response/ticket-bulk-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>SkippedTicketBulkResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/TicketBulkResponseDto.html">src/tickets/dto/response/ticket-bulk-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>TicketBulkResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateTicketsBulkResponse.html">src/tickets/dto/response/ticket-bulk-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateTicketsBulkResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateTicketsBulkResponseDto.html">src/tickets/dto/response/ticket-bulk-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateTicketsBulkResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CommonTicketPriorityResponse.html">src/tickets/dto/response/ticket-priority.dto.ts</a>
            </td>
            <td>class</td>
            <td>CommonTicketPriorityResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetAllTicketPrioritiesResponse.html">src/tickets/dto/response/ticket-priority.dto.ts</a>
            </td>
            <td>class</td>
            <td>GetAllTicketPrioritiesResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CommonTicketResponse.html">src/tickets/dto/response/ticket-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>CommonTicketResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetAllTicketsResponse.html">src/tickets/dto/response/ticket-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>GetAllTicketsResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CommonTicketStatusResponse.html">src/tickets/dto/response/ticket-status.dto.ts</a>
            </td>
            <td>class</td>
            <td>CommonTicketStatusResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetAllTicketStatusesResponse.html">src/tickets/dto/response/ticket-status.dto.ts</a>
            </td>
            <td>class</td>
            <td>GetAllTicketStatusesResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CommonTicketTimeLogResponse.html">src/tickets/dto/response/ticket-time-log-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>CommonTicketTimeLogResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetAllTicketTimeLogsResponse.html">src/tickets/dto/response/ticket-time-log-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>GetAllTicketTimeLogsResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CommonTicketTypeResponse.html">src/tickets/dto/response/ticket-type-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>CommonTicketTypeResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetAllTicketTypesResponse.html">src/tickets/dto/response/ticket-type-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>GetAllTicketTypesResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ArchiveTicketsBulkDto.html">src/tickets/dto/ticket-bulk.dto.ts</a>
            </td>
            <td>class</td>
            <td>ArchiveTicketsBulkDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateBulkTicketsOptions.html">src/tickets/dto/ticket-bulk.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateBulkTicketsOptions</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="good">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateTicketsBulkDto.html">src/tickets/dto/ticket-bulk.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateTicketsBulkDto</td>
            <td align="right" data-sort="66">
                <span class="coverage-percent">66 %</span>
                <span class="coverage-count">(2/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DeleteTicketsBulkDto.html">src/tickets/dto/ticket-bulk.dto.ts</a>
            </td>
            <td>class</td>
            <td>DeleteTicketsBulkDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateTicketsBulkDto.html">src/tickets/dto/ticket-bulk.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateTicketsBulkDto</td>
            <td align="right" data-sort="85">
                <span class="coverage-percent">85 %</span>
                <span class="coverage-count">(6/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CommonTicketPriorityDto.html">src/tickets/dto/ticket-priority.dto.ts</a>
            </td>
            <td>class</td>
            <td>CommonTicketPriorityDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateTicketPriorityDto.html">src/tickets/dto/ticket-priority.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateTicketPriorityDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateTicketPriorityDto.html">src/tickets/dto/ticket-priority.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateTicketPriorityDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CommonTicketStatusDto.html">src/tickets/dto/ticket-status.dto.ts</a>
            </td>
            <td>class</td>
            <td>CommonTicketStatusDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateTicketStatusDto.html">src/tickets/dto/ticket-status.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateTicketStatusDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateTicketStatusDto.html">src/tickets/dto/ticket-status.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateTicketStatusDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/TicketTimeLogDto.html">src/tickets/dto/ticket-time-log.dto.ts</a>
            </td>
            <td>class</td>
            <td>TicketTimeLogDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CommonTicketTypeDto.html">src/tickets/dto/ticket-type.dto.ts</a>
            </td>
            <td>class</td>
            <td>CommonTicketTypeDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateTicketTypeDto.html">src/tickets/dto/ticket-type.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateTicketTypeDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateTicketTypeDto.html">src/tickets/dto/ticket-type.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateTicketTypeDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AssignTeamToTicketBody.html">src/tickets/dto/ticket.dto.ts</a>
            </td>
            <td>class</td>
            <td>AssignTeamToTicketBody</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AssignTicketBody.html">src/tickets/dto/ticket.dto.ts</a>
            </td>
            <td>class</td>
            <td>AssignTicketBody</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CommonTicketFields.html">src/tickets/dto/ticket.dto.ts</a>
            </td>
            <td>class</td>
            <td>CommonTicketFields</td>
            <td align="right" data-sort="85">
                <span class="coverage-percent">85 %</span>
                <span class="coverage-count">(12/14)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateTicketBody.html">src/tickets/dto/ticket.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateTicketBody</td>
            <td align="right" data-sort="89">
                <span class="coverage-percent">89 %</span>
                <span class="coverage-count">(17/19)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/EscalateTicketBody.html">src/tickets/dto/ticket.dto.ts</a>
            </td>
            <td>class</td>
            <td>EscalateTicketBody</td>
            <td align="right" data-sort="25">
                <span class="coverage-percent">25 %</span>
                <span class="coverage-count">(1/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LinkTicketsBody.html">src/tickets/dto/ticket.dto.ts</a>
            </td>
            <td>class</td>
            <td>LinkTicketsBody</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/MarkDuplicateBody.html">src/tickets/dto/ticket.dto.ts</a>
            </td>
            <td>class</td>
            <td>MarkDuplicateBody</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/MarkOrCreateSubTicketBody.html">src/tickets/dto/ticket.dto.ts</a>
            </td>
            <td>class</td>
            <td>MarkOrCreateSubTicketBody</td>
            <td align="right" data-sort="82">
                <span class="coverage-percent">82 %</span>
                <span class="coverage-count">(14/17)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateTicketBody.html">src/tickets/dto/ticket.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateTicketBody</td>
            <td align="right" data-sort="93">
                <span class="coverage-percent">93 %</span>
                <span class="coverage-count">(15/16)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/TicketsEventsFactory.html">src/tickets/events/tickets-events.factory.ts</a>
            </td>
            <td>injectable</td>
            <td>TicketsEventsFactory</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/TicketAssignedEvent.html">src/tickets/events/tickets.events.ts</a>
            </td>
            <td>class</td>
            <td>TicketAssignedEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/TicketUnassignedEvent.html">src/tickets/events/tickets.events.ts</a>
            </td>
            <td>class</td>
            <td>TicketUnassignedEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#EmittableTicketEvents">src/tickets/events/tickets.events.ts</a>
            </td>
            <td>variable</td>
            <td>EmittableTicketEvents</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CommonDraftFields.html">src/tickets/interfaces/draft.ticket.interface.ts</a>
            </td>
            <td>class</td>
            <td>CommonDraftFields</td>
            <td align="right" data-sort="100">
                <span class="coverage-percent">100 %</span>
                <span class="coverage-count">(11/11)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateDraftTicketDto.html">src/tickets/interfaces/draft.ticket.interface.ts</a>
            </td>
            <td>class</td>
            <td>CreateDraftTicketDto</td>
            <td align="right" data-sort="100">
                <span class="coverage-percent">100 %</span>
                <span class="coverage-count">(16/16)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DraftTicketResponseDto.html">src/tickets/interfaces/draft.ticket.interface.ts</a>
            </td>
            <td>class</td>
            <td>DraftTicketResponseDto</td>
            <td align="right" data-sort="9">
                <span class="coverage-percent">9 %</span>
                <span class="coverage-count">(1/11)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetDraftTicketsQueryDto.html">src/tickets/interfaces/draft.ticket.interface.ts</a>
            </td>
            <td>class</td>
            <td>GetDraftTicketsQueryDto</td>
            <td align="right" data-sort="16">
                <span class="coverage-percent">16 %</span>
                <span class="coverage-count">(1/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/PaginatedResponseDto.html">src/tickets/interfaces/draft.ticket.interface.ts</a>
            </td>
            <td>class</td>
            <td>PaginatedResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateDraftTicketDto.html">src/tickets/interfaces/draft.ticket.interface.ts</a>
            </td>
            <td>class</td>
            <td>UpdateDraftTicketDto</td>
            <td align="right" data-sort="100">
                <span class="coverage-percent">100 %</span>
                <span class="coverage-count">(14/14)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/SLAMessage.html">src/tickets/interfaces/sla.interface.ts</a>
            </td>
            <td>interface</td>
            <td>SLAMessage</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/10)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/SLAMetadata.html">src/tickets/interfaces/sla.interface.ts</a>
            </td>
            <td>interface</td>
            <td>SLAMetadata</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/SLAMetricData.html">src/tickets/interfaces/sla.interface.ts</a>
            </td>
            <td>interface</td>
            <td>SLAMetricData</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/TicketSLAMetadata.html">src/tickets/interfaces/sla.interface.ts</a>
            </td>
            <td>interface</td>
            <td>TicketSLAMetadata</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/Actor.html">src/tickets/interfaces/sns-ticket-created-payload.interface.ts</a>
            </td>
            <td>interface</td>
            <td>Actor</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/Customer.html">src/tickets/interfaces/sns-ticket-created-payload.interface.ts</a>
            </td>
            <td>interface</td>
            <td>Customer</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/Payload.html">src/tickets/interfaces/sns-ticket-created-payload.interface.ts</a>
            </td>
            <td>interface</td>
            <td>Payload</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/SnsTicketCreatedPayload.html">src/tickets/interfaces/sns-ticket-created-payload.interface.ts</a>
            </td>
            <td>interface</td>
            <td>SnsTicketCreatedPayload</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/TicketPayload.html">src/tickets/interfaces/sns-ticket-created-payload.interface.ts</a>
            </td>
            <td>interface</td>
            <td>TicketPayload</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/19)</span>
            </td>
        </tr>
        <tr class="medium">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/TicketSearchResponse.html">src/tickets/interfaces/ticket-search.interface.ts</a>
            </td>
            <td>class</td>
            <td>TicketSearchResponse</td>
            <td align="right" data-sort="33">
                <span class="coverage-percent">33 %</span>
                <span class="coverage-count">(1/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/UpdateResult.html">src/tickets/interfaces/ticket.interface.ts</a>
            </td>
            <td>interface</td>
            <td>UpdateResult</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/TicketsListeners.html">src/tickets/listeners/tickets.listeners.ts</a>
            </td>
            <td>injectable</td>
            <td>TicketsListeners</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/TicketSnsPublisherConsumer.html">src/tickets/processors/ticket.sns-publish.processor.ts</a>
            </td>
            <td>injectable</td>
            <td>TicketSnsPublisherConsumer</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="medium">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/RuleEvaluatorAbstract.html">src/tickets/routing/abstract/rule-evaluator.abstract.ts</a>
            </td>
            <td>class</td>
            <td>RuleEvaluatorAbstract</td>
            <td align="right" data-sort="40">
                <span class="coverage-percent">40 %</span>
                <span class="coverage-count">(4/10)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/typealiases.html#OperatorFunction">src/tickets/routing/abstract/rule-evaluator.abstract.ts</a>
            </td>
            <td>type alias</td>
            <td>OperatorFunction</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="medium">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/RequestRouterEngine.html">src/tickets/routing/interfaces/engine.interface.ts</a>
            </td>
            <td>interface</td>
            <td>RequestRouterEngine</td>
            <td align="right" data-sort="50">
                <span class="coverage-percent">50 %</span>
                <span class="coverage-count">(1/2)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/RuleEvaluator.html">src/tickets/routing/interfaces/rule-evaluator.interface.ts</a>
            </td>
            <td>interface</td>
            <td>RuleEvaluator</td>
            <td align="right" data-sort="100">
                <span class="coverage-percent">100 %</span>
                <span class="coverage-count">(3/3)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/ValidationError.html">src/tickets/routing/interfaces/rule-evaluator.interface.ts</a>
            </td>
            <td>interface</td>
            <td>ValidationError</td>
            <td align="right" data-sort="100">
                <span class="coverage-percent">100 %</span>
                <span class="coverage-count">(4/4)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/ValidationResult.html">src/tickets/routing/interfaces/rule-evaluator.interface.ts</a>
            </td>
            <td>interface</td>
            <td>ValidationResult</td>
            <td align="right" data-sort="100">
                <span class="coverage-percent">100 %</span>
                <span class="coverage-count">(3/3)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/UserRoutingStrategy.html">src/tickets/routing/interfaces/user-routing-strategy.interface.ts</a>
            </td>
            <td>interface</td>
            <td>UserRoutingStrategy</td>
            <td align="right" data-sort="80">
                <span class="coverage-percent">80 %</span>
                <span class="coverage-count">(4/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#REQUEST_ROUTER_ENGINE">src/tickets/routing/providers/request-router.provider.ts</a>
            </td>
            <td>variable</td>
            <td>REQUEST_ROUTER_ENGINE</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#RequestRouterProvider">src/tickets/routing/providers/request-router.provider.ts</a>
            </td>
            <td>variable</td>
            <td>RequestRouterProvider</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/ThenaAgentAllocator.html">src/tickets/routing/providers/thena-request-router/agent-allocator/allocator.ts</a>
            </td>
            <td>injectable</td>
            <td>ThenaAgentAllocator</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="medium">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/RoundRobinStrategy.html">src/tickets/routing/providers/thena-request-router/agent-allocator/strategies/round-robin.strategy.ts</a>
            </td>
            <td>injectable</td>
            <td>RoundRobinStrategy</td>
            <td align="right" data-sort="50">
                <span class="coverage-percent">50 %</span>
                <span class="coverage-count">(4/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/ThenaRequestRouterEngine.html">src/tickets/routing/providers/thena-request-router/engine/thena-request-router.engine.ts</a>
            </td>
            <td>injectable</td>
            <td>ThenaRequestRouterEngine</td>
            <td align="right" data-sort="14">
                <span class="coverage-percent">14 %</span>
                <span class="coverage-count">(1/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/TicketRoutingContext.html">src/tickets/routing/providers/thena-request-router/engine/thena-request-router.engine.ts</a>
            </td>
            <td>interface</td>
            <td>TicketRoutingContext</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/9)</span>
            </td>
        </tr>
        <tr class="good">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/ThenaRuleEvaluator.html">src/tickets/routing/providers/thena-request-router/rule-evaluator/evaluator.ts</a>
            </td>
            <td>injectable</td>
            <td>ThenaRuleEvaluator</td>
            <td align="right" data-sort="61">
                <span class="coverage-percent">61 %</span>
                <span class="coverage-count">(8/13)</span>
            </td>
        </tr>
        <tr class="good">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/TicketAnnotatorService.html">src/tickets/services/ticket-annotator.service.ts</a>
            </td>
            <td>injectable</td>
            <td>TicketAnnotatorService</td>
            <td align="right" data-sort="72">
                <span class="coverage-percent">72 %</span>
                <span class="coverage-count">(8/11)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/FieldTypeInfo.html">src/tickets/services/ticket-annotator.service.ts</a>
            </td>
            <td>interface</td>
            <td>FieldTypeInfo</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/9)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/TicketsBulkActionService.html">src/tickets/services/ticket-bulk.action.service.ts</a>
            </td>
            <td>injectable</td>
            <td>TicketsBulkActionService</td>
            <td align="right" data-sort="80">
                <span class="coverage-percent">80 %</span>
                <span class="coverage-count">(8/10)</span>
            </td>
        </tr>
        <tr class="good">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/TicketDraftService.html">src/tickets/services/ticket-draft.action.service.ts</a>
            </td>
            <td>injectable</td>
            <td>TicketDraftService</td>
            <td align="right" data-sort="75">
                <span class="coverage-percent">75 %</span>
                <span class="coverage-count">(6/8)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/TicketPriorityActionService.html">src/tickets/services/ticket-priority.action.service.ts</a>
            </td>
            <td>injectable</td>
            <td>TicketPriorityActionService</td>
            <td align="right" data-sort="80">
                <span class="coverage-percent">80 %</span>
                <span class="coverage-count">(12/15)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/TicketSearchService.html">src/tickets/services/ticket-search.action.service.ts</a>
            </td>
            <td>injectable</td>
            <td>TicketSearchService</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/TicketStatusActionService.html">src/tickets/services/ticket-status.action.service.ts</a>
            </td>
            <td>injectable</td>
            <td>TicketStatusActionService</td>
            <td align="right" data-sort="78">
                <span class="coverage-percent">78 %</span>
                <span class="coverage-count">(11/14)</span>
            </td>
        </tr>
        <tr class="good">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/TicketTypeActionService.html">src/tickets/services/ticket-type.action.service.ts</a>
            </td>
            <td>injectable</td>
            <td>TicketTypeActionService</td>
            <td align="right" data-sort="72">
                <span class="coverage-percent">72 %</span>
                <span class="coverage-count">(8/11)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/TicketValidationService.html">src/tickets/services/ticket-validation.service.ts</a>
            </td>
            <td>injectable</td>
            <td>TicketValidationService</td>
            <td align="right" data-sort="80">
                <span class="coverage-percent">80 %</span>
                <span class="coverage-count">(12/15)</span>
            </td>
        </tr>
        <tr class="good">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/TicketsService.html">src/tickets/services/tickets.service.ts</a>
            </td>
            <td>injectable</td>
            <td>TicketsService</td>
            <td align="right" data-sort="67">
                <span class="coverage-percent">67 %</span>
                <span class="coverage-count">(25/37)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/TicketPriorityResponseDto.html">src/tickets/transformer/ticket-priority.dto.ts</a>
            </td>
            <td>class</td>
            <td>TicketPriorityResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/11)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/TicketResponseDto.html">src/tickets/transformer/ticket-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>TicketResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/28)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/TicketStatusResponseDto.html">src/tickets/transformer/ticket-status.dto.ts</a>
            </td>
            <td>class</td>
            <td>TicketStatusResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/12)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/TicketTimeLogResponseDto.html">src/tickets/transformer/ticket-time-log.dto.ts</a>
            </td>
            <td>class</td>
            <td>TicketTimeLogResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/9)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/TicketTypeResponseDto.html">src/tickets/transformer/ticket-type.dto.ts</a>
            </td>
            <td>class</td>
            <td>TicketTypeResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/12)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/functions.html#updateSLAMetadata">src/tickets/utils/sla-metadata.utils.ts</a>
            </td>
            <td>function</td>
            <td>updateSLAMetadata</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#EAGER_TICKET_RELATIONS">src/tickets/utils/tickets.constants.ts</a>
            </td>
            <td>variable</td>
            <td>EAGER_TICKET_RELATIONS</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#TICKET_SNS_PUBLISHER">src/tickets/utils/tickets.constants.ts</a>
            </td>
            <td>variable</td>
            <td>TICKET_SNS_PUBLISHER</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#BOT_USER_EMAIL_DOMAIN">src/users/constants/users.constants.ts</a>
            </td>
            <td>variable</td>
            <td>BOT_USER_EMAIL_DOMAIN</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#SELECT_FROM_USERS">src/users/constants/users.dbconstants.ts</a>
            </td>
            <td>variable</td>
            <td>SELECT_FROM_USERS</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/UsersGrpcController.html">src/users/controllers/grpc/users-grpc.controller.ts</a>
            </td>
            <td>controller</td>
            <td>UsersGrpcController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/11)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/UsersSkillsActionController.html">src/users/controllers/users-skills.action.controller.ts</a>
            </td>
            <td>controller</td>
            <td>UsersSkillsActionController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/UsersTimeOffActionController.html">src/users/controllers/users-time-off.action.controllers.ts</a>
            </td>
            <td>controller</td>
            <td>UsersTimeOffActionController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/UsersController.html">src/users/controllers/users.controller.ts</a>
            </td>
            <td>controller</td>
            <td>UsersController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CommonTimeOffResponse.html">src/users/dto/response/time-off-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>CommonTimeOffResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetAllTimeOffsResponse.html">src/users/dto/response/time-off-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>GetAllTimeOffsResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CommonUserConfigurationsResponse.html">src/users/dto/response/user-configuration-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>CommonUserConfigurationsResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetAllUserConfigurationsResponse.html">src/users/dto/response/user-configuration-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>GetAllUserConfigurationsResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateUserAvailabilityResponse.html">src/users/dto/response/user-configuration-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateUserAvailabilityResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CommonUserResponse.html">src/users/dto/response/user-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>CommonUserResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetAllUsersResponse.html">src/users/dto/response/user-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>GetAllUsersResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetAllSkillsQuery.html">src/users/dto/skills.dto.ts</a>
            </td>
            <td>class</td>
            <td>GetAllSkillsQuery</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateTimeOffDto.html">src/users/dto/time-off.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateTimeOffDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetAllTimeOffsQuery.html">src/users/dto/time-off.dto.ts</a>
            </td>
            <td>class</td>
            <td>GetAllTimeOffsQuery</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateTimeOffDto.html">src/users/dto/time-off.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateTimeOffDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateOrganizationAdminDto.html">src/users/dto/users.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateOrganizationAdminDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateUserDto.html">src/users/dto/users.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateUserDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateUserAvailabilityDto.html">src/users/dto/users.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateUserAvailabilityDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateUserWorkloadDto.html">src/users/dto/users.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateUserWorkloadDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="good">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/UsersGrpcService.html">src/users/services/grpc/users-grpc.service.ts</a>
            </td>
            <td>injectable</td>
            <td>UsersGrpcService</td>
            <td align="right" data-sort="60">
                <span class="coverage-percent">60 %</span>
                <span class="coverage-count">(3/5)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/UserAnnotatorService.html">src/users/services/user-annotator.service.ts</a>
            </td>
            <td>injectable</td>
            <td>UserAnnotatorService</td>
            <td align="right" data-sort="80">
                <span class="coverage-percent">80 %</span>
                <span class="coverage-count">(8/10)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/UsersService.html">src/users/services/users.service.ts</a>
            </td>
            <td>injectable</td>
            <td>UsersService</td>
            <td align="right" data-sort="85">
                <span class="coverage-percent">85 %</span>
                <span class="coverage-count">(29/34)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/TimeOffResponseDto.html">src/users/transformers/time-off.transformer.ts</a>
            </td>
            <td>class</td>
            <td>TimeOffResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/9)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/BusinessDays.html">src/users/transformers/user-business-hours.transformer.ts</a>
            </td>
            <td>class</td>
            <td>BusinessDays</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateUserAvailabilityResponseDto.html">src/users/transformers/user-business-hours.transformer.ts</a>
            </td>
            <td>class</td>
            <td>UpdateUserAvailabilityResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UserConfigurationsResponseDto.html">src/users/transformers/user-business-hours.transformer.ts</a>
            </td>
            <td>class</td>
            <td>UserConfigurationsResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UserResponseDto.html">src/users/transformers/user.transformer.ts</a>
            </td>
            <td>class</td>
            <td>UserResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/13)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/AuthHeaders.html">src/users/utils/user-grpc-client.interface.ts</a>
            </td>
            <td>interface</td>
            <td>AuthHeaders</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AuthGrpcClient.html">src/users/utils/users-grpc-client.ts</a>
            </td>
            <td>class</td>
            <td>AuthGrpcClient</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/10)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#injectWithOrgId">src/utils/test-utils/index.ts</a>
            </td>
            <td>variable</td>
            <td>injectWithOrgId</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                        <a href="./miscellaneous/variables.html#EAGERLY_FETCH_RELATIONS">src/views/constants/views.constants.ts</a>
            </td>
            <td>variable</td>
            <td>EAGERLY_FETCH_RELATIONS</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/ViewsTypesController.html">src/views/controllers/view-types.controller.ts</a>
            </td>
            <td>controller</td>
            <td>ViewsTypesController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/ViewsController.html">src/views/controllers/views.controller.ts</a>
            </td>
            <td>controller</td>
            <td>ViewsController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetAllViewsTypesResponse.html">src/views/dto/view-types-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>GetAllViewsTypesResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CommonViewResponse.html">src/views/dto/views-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>CommonViewResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetAllViewsResponse.html">src/views/dto/views-response.dto.ts</a>
            </td>
            <td>class</td>
            <td>GetAllViewsResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateViewBody.html">src/views/dto/views.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateViewBody</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/GetViewQuery.html">src/views/dto/views.dto.ts</a>
            </td>
            <td>class</td>
            <td>GetViewQuery</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateViewBody.html">src/views/dto/views.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateViewBody</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/ViewsTypesService.html">src/views/services/views-types.service.ts</a>
            </td>
            <td>injectable</td>
            <td>ViewsTypesService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="good">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/ViewsService.html">src/views/services/views.service.ts</a>
            </td>
            <td>injectable</td>
            <td>ViewsService</td>
            <td align="right" data-sort="70">
                <span class="coverage-percent">70 %</span>
                <span class="coverage-count">(7/10)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ViewsTypesResponseDto.html">src/views/transformers/view-types-response.transformer.ts</a>
            </td>
            <td>class</td>
            <td>ViewsTypesResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ViewsResponseDto.html">src/views/transformers/views-response.transformer.ts</a>
            </td>
            <td>class</td>
            <td>ViewsResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/16)</span>
            </td>
        </tr>
    </tbody>
</table>

<script src="js/libs/tablesort.min.js"></script>
<script src="js/libs/tablesort.number.min.js"></script>
<script>
    new Tablesort(document.getElementById('coverage-table'));
</script>

                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 0;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'coverage';
            var COMPODOC_CURRENT_PAGE_URL = 'coverage.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="./js/libs/custom-elements.min.js"></script>
       <script src="./js/libs/lit-html.js"></script>

       <script src="./js/menu-wc.js" defer></script>
       <script nomodule src="./js/menu-wc_es5.js" defer></script>

       <script src="./js/libs/bootstrap-native.js"></script>

       <script src="./js/libs/es6-shim.min.js"></script>
       <script src="./js/libs/EventDispatcher.js"></script>
       <script src="./js/libs/promise.min.js"></script>

       <script src="./js/compodoc.js"></script>

       <script src="./js/tabs.js"></script>
       <script src="./js/menu.js"></script>
       <script src="./js/libs/clipboard.min.js"></script>
       <script src="./js/libs/prism.js"></script>
       <script src="./js/sourceCode.js"></script>
          <script src="./js/search/search.js"></script>
          <script src="./js/search/lunr.min.js"></script>
          <script src="./js/search/search-lunr.js"></script>
          <script src="./js/search/search_index.js"></script>
       <script src="./js/lazy-load-graphs.js"></script>


    </body>
</html>
