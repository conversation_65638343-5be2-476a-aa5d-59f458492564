<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="./images/favicon.ico">
	   <link rel="stylesheet" href="./styles/style.css">
        <link rel="stylesheet" href="./styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="./" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content modules">
                   <div class="content-data">



<ol class="breadcrumb">
    <li class="breadcrumb-item">Modules</li>
</ol>
<div class="container-fluid modules">
    <div class="row">
            <div class="col-md-6 col-lg-4">
                <div class="card card-module">
                    <div class="card-header">
                        <h4 class="card-title">AccountsModule</h4>
                    </div>
                    <div class="card-block">
                                <p>
                                    <object id="demo-svg" type="image/svg+xml" lazy="./modules/AccountsModule/dependencies.svg" style="width: 100%; height: 175px;">
                                        Your browser does not support SVG
                                    </object>
                                </p>
                        <footer class="text-center">
                            <a href="./modules/AccountsModule.html" class="btn btn-default">Browse</a>
                        </footer>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="card card-module">
                    <div class="card-header">
                        <h4 class="card-title">ActivitiesModule</h4>
                    </div>
                    <div class="card-block">
                                <p>
                                    <object id="demo-svg" type="image/svg+xml" lazy="./modules/ActivitiesModule/dependencies.svg" style="width: 100%; height: 175px;">
                                        Your browser does not support SVG
                                    </object>
                                </p>
                        <footer class="text-center">
                            <a href="./modules/ActivitiesModule.html" class="btn btn-default">Browse</a>
                        </footer>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="card card-module">
                    <div class="card-header">
                        <h4 class="card-title">AppModule</h4>
                    </div>
                    <div class="card-block">
                                <p>
                                    <object id="demo-svg" type="image/svg+xml" lazy="./modules/AppModule/dependencies.svg" style="width: 100%; height: 175px;">
                                        Your browser does not support SVG
                                    </object>
                                </p>
                        <footer class="text-center">
                            <a href="./modules/AppModule.html" class="btn btn-default">Browse</a>
                        </footer>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="card card-module">
                    <div class="card-header">
                        <h4 class="card-title">AuthModule</h4>
                    </div>
                    <div class="card-block">
                                <p>
                                    <object id="demo-svg" type="image/svg+xml" lazy="./modules/AuthModule/dependencies.svg" style="width: 100%; height: 175px;">
                                        Your browser does not support SVG
                                    </object>
                                </p>
                        <footer class="text-center">
                            <a href="./modules/AuthModule.html" class="btn btn-default">Browse</a>
                        </footer>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="card card-module">
                    <div class="card-header">
                        <h4 class="card-title">BullBoardModule</h4>
                    </div>
                    <div class="card-block">
                                <p>
                                    <object id="demo-svg" type="image/svg+xml" lazy="./modules/BullBoardModule/dependencies.svg" style="width: 100%; height: 175px;">
                                        Your browser does not support SVG
                                    </object>
                                </p>
                        <footer class="text-center">
                            <a href="./modules/BullBoardModule.html" class="btn btn-default">Browse</a>
                        </footer>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="card card-module">
                    <div class="card-header">
                        <h4 class="card-title">CommonModule</h4>
                    </div>
                    <div class="card-block">
                                <p>
                                    <object id="demo-svg" type="image/svg+xml" lazy="./modules/CommonModule/dependencies.svg" style="width: 100%; height: 175px;">
                                        Your browser does not support SVG
                                    </object>
                                </p>
                        <footer class="text-center">
                            <a href="./modules/CommonModule.html" class="btn btn-default">Browse</a>
                        </footer>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="card card-module">
                    <div class="card-header">
                        <h4 class="card-title">CommunicationsModule</h4>
                    </div>
                    <div class="card-block">
                                <p>
                                    <object id="demo-svg" type="image/svg+xml" lazy="./modules/CommunicationsModule/dependencies.svg" style="width: 100%; height: 175px;">
                                        Your browser does not support SVG
                                    </object>
                                </p>
                        <footer class="text-center">
                            <a href="./modules/CommunicationsModule.html" class="btn btn-default">Browse</a>
                        </footer>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="card card-module">
                    <div class="card-header">
                        <h4 class="card-title">ConfigModule</h4>
                    </div>
                    <div class="card-block">
                                <p>
                                    <object id="demo-svg" type="image/svg+xml" lazy="./modules/ConfigModule/dependencies.svg" style="width: 100%; height: 175px;">
                                        Your browser does not support SVG
                                    </object>
                                </p>
                        <footer class="text-center">
                            <a href="./modules/ConfigModule.html" class="btn btn-default">Browse</a>
                        </footer>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="card card-module">
                    <div class="card-header">
                        <h4 class="card-title">CustomFieldModule</h4>
                    </div>
                    <div class="card-block">
                                <p>
                                    <object id="demo-svg" type="image/svg+xml" lazy="./modules/CustomFieldModule/dependencies.svg" style="width: 100%; height: 175px;">
                                        Your browser does not support SVG
                                    </object>
                                </p>
                        <footer class="text-center">
                            <a href="./modules/CustomFieldModule.html" class="btn btn-default">Browse</a>
                        </footer>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="card card-module">
                    <div class="card-header">
                        <h4 class="card-title">CustomObjectModule</h4>
                    </div>
                    <div class="card-block">
                                <p>
                                    <object id="demo-svg" type="image/svg+xml" lazy="./modules/CustomObjectModule/dependencies.svg" style="width: 100%; height: 175px;">
                                        Your browser does not support SVG
                                    </object>
                                </p>
                        <footer class="text-center">
                            <a href="./modules/CustomObjectModule.html" class="btn btn-default">Browse</a>
                        </footer>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="card card-module">
                    <div class="card-header">
                        <h4 class="card-title">FormsModule</h4>
                    </div>
                    <div class="card-block">
                                <p>
                                    <object id="demo-svg" type="image/svg+xml" lazy="./modules/FormsModule/dependencies.svg" style="width: 100%; height: 175px;">
                                        Your browser does not support SVG
                                    </object>
                                </p>
                        <footer class="text-center">
                            <a href="./modules/FormsModule.html" class="btn btn-default">Browse</a>
                        </footer>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="card card-module">
                    <div class="card-header">
                        <h4 class="card-title">HealthModule</h4>
                    </div>
                    <div class="card-block">
                                <p>
                                    <object id="demo-svg" type="image/svg+xml" lazy="./modules/HealthModule/dependencies.svg" style="width: 100%; height: 175px;">
                                        Your browser does not support SVG
                                    </object>
                                </p>
                        <footer class="text-center">
                            <a href="./modules/HealthModule.html" class="btn btn-default">Browse</a>
                        </footer>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="card card-module">
                    <div class="card-header">
                        <h4 class="card-title">OrganizationModule</h4>
                    </div>
                    <div class="card-block">
                                <p>
                                    <object id="demo-svg" type="image/svg+xml" lazy="./modules/OrganizationModule/dependencies.svg" style="width: 100%; height: 175px;">
                                        Your browser does not support SVG
                                    </object>
                                </p>
                        <footer class="text-center">
                            <a href="./modules/OrganizationModule.html" class="btn btn-default">Browse</a>
                        </footer>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="card card-module">
                    <div class="card-header">
                        <h4 class="card-title">SharedModule</h4>
                    </div>
                    <div class="card-block">
                                <p>
                                    <object id="demo-svg" type="image/svg+xml" lazy="./modules/SharedModule/dependencies.svg" style="width: 100%; height: 175px;">
                                        Your browser does not support SVG
                                    </object>
                                </p>
                        <footer class="text-center">
                            <a href="./modules/SharedModule.html" class="btn btn-default">Browse</a>
                        </footer>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="card card-module">
                    <div class="card-header">
                        <h4 class="card-title">StorageModule</h4>
                    </div>
                    <div class="card-block">
                                <p>
                                    <object id="demo-svg" type="image/svg+xml" lazy="./modules/StorageModule/dependencies.svg" style="width: 100%; height: 175px;">
                                        Your browser does not support SVG
                                    </object>
                                </p>
                        <footer class="text-center">
                            <a href="./modules/StorageModule.html" class="btn btn-default">Browse</a>
                        </footer>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="card card-module">
                    <div class="card-header">
                        <h4 class="card-title">SwaggerModule</h4>
                    </div>
                    <div class="card-block">
                                <p>
                                    <object id="demo-svg" type="image/svg+xml" lazy="./modules/SwaggerModule/dependencies.svg" style="width: 100%; height: 175px;">
                                        Your browser does not support SVG
                                    </object>
                                </p>
                        <footer class="text-center">
                            <a href="./modules/SwaggerModule.html" class="btn btn-default">Browse</a>
                        </footer>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="card card-module">
                    <div class="card-header">
                        <h4 class="card-title">TagModule</h4>
                    </div>
                    <div class="card-block">
                                <p>
                                    <object id="demo-svg" type="image/svg+xml" lazy="./modules/TagModule/dependencies.svg" style="width: 100%; height: 175px;">
                                        Your browser does not support SVG
                                    </object>
                                </p>
                        <footer class="text-center">
                            <a href="./modules/TagModule.html" class="btn btn-default">Browse</a>
                        </footer>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="card card-module">
                    <div class="card-header">
                        <h4 class="card-title">TeamsModule</h4>
                    </div>
                    <div class="card-block">
                                <p>
                                    <object id="demo-svg" type="image/svg+xml" lazy="./modules/TeamsModule/dependencies.svg" style="width: 100%; height: 175px;">
                                        Your browser does not support SVG
                                    </object>
                                </p>
                        <footer class="text-center">
                            <a href="./modules/TeamsModule.html" class="btn btn-default">Browse</a>
                        </footer>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="card card-module">
                    <div class="card-header">
                        <h4 class="card-title">TicketsModule</h4>
                    </div>
                    <div class="card-block">
                                <p>
                                    <object id="demo-svg" type="image/svg+xml" lazy="./modules/TicketsModule/dependencies.svg" style="width: 100%; height: 175px;">
                                        Your browser does not support SVG
                                    </object>
                                </p>
                        <footer class="text-center">
                            <a href="./modules/TicketsModule.html" class="btn btn-default">Browse</a>
                        </footer>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="card card-module">
                    <div class="card-header">
                        <h4 class="card-title">UsersModule</h4>
                    </div>
                    <div class="card-block">
                                <p>
                                    <object id="demo-svg" type="image/svg+xml" lazy="./modules/UsersModule/dependencies.svg" style="width: 100%; height: 175px;">
                                        Your browser does not support SVG
                                    </object>
                                </p>
                        <footer class="text-center">
                            <a href="./modules/UsersModule.html" class="btn btn-default">Browse</a>
                        </footer>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="card card-module">
                    <div class="card-header">
                        <h4 class="card-title">UtilsModule</h4>
                    </div>
                    <div class="card-block">
                                <p>
                                    <object id="demo-svg" type="image/svg+xml" lazy="./modules/UtilsModule/dependencies.svg" style="width: 100%; height: 175px;">
                                        Your browser does not support SVG
                                    </object>
                                </p>
                        <footer class="text-center">
                            <a href="./modules/UtilsModule.html" class="btn btn-default">Browse</a>
                        </footer>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="card card-module">
                    <div class="card-header">
                        <h4 class="card-title">ViewsModule</h4>
                    </div>
                    <div class="card-block">
                                <p>
                                    <object id="demo-svg" type="image/svg+xml" lazy="./modules/ViewsModule/dependencies.svg" style="width: 100%; height: 175px;">
                                        Your browser does not support SVG
                                    </object>
                                </p>
                        <footer class="text-center">
                            <a href="./modules/ViewsModule.html" class="btn btn-default">Browse</a>
                        </footer>
                    </div>
                </div>
            </div>
    </div>
</div>


















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 0;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'modules';
            var COMPODOC_CURRENT_PAGE_URL = 'modules.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="./js/libs/custom-elements.min.js"></script>
       <script src="./js/libs/lit-html.js"></script>

       <script src="./js/menu-wc.js" defer></script>
       <script nomodule src="./js/menu-wc_es5.js" defer></script>

       <script src="./js/libs/bootstrap-native.js"></script>

       <script src="./js/libs/es6-shim.min.js"></script>
       <script src="./js/libs/EventDispatcher.js"></script>
       <script src="./js/libs/promise.min.js"></script>

       <script src="./js/compodoc.js"></script>

       <script src="./js/tabs.js"></script>
       <script src="./js/menu.js"></script>
       <script src="./js/libs/clipboard.min.js"></script>
       <script src="./js/libs/prism.js"></script>
       <script src="./js/sourceCode.js"></script>
          <script src="./js/search/search.js"></script>
          <script src="./js/search/lunr.min.js"></script>
          <script src="./js/search/search-lunr.js"></script>
          <script src="./js/search/search_index.js"></script>
       <script src="./js/lazy-load-graphs.js"></script>


    </body>
</html>
