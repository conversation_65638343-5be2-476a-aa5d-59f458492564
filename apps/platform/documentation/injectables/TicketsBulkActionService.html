<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >TicketsBulkActionService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/tickets/services/ticket-bulk.action.service.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#archiveTicketsBulk" >archiveTicketsBulk</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createTicketsBulk" >createTicketsBulk</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#deleteTicketsBulk" >deleteTicketsBulk</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#extractLookupDetailsFromTicketsPayload" >extractLookupDetailsFromTicketsPayload</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#extractUserFromRequest" >extractUserFromRequest</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#fetchDataForTicketBulkCreation" >fetchDataForTicketBulkCreation</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#findAccountForTicket" >findAccountForTicket</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateTicketsBulk" >updateTicketsBulk</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(ticketRepository: TicketRepository, cachedTicketRepository: CachedTicketRepository, transactionService: TransactionService, usersService: <a href="../injectables/UsersService.html" target="_self">UsersService</a>, teamsService: <a href="../injectables/TeamsService.html" target="_self">TeamsService</a>, accountsService: <a href="../injectables/AccountsService.html" target="_self">AccountsService</a>, ticketStatusService: <a href="../injectables/TicketStatusActionService.html" target="_self">TicketStatusActionService</a>, ticketPriorityService: <a href="../injectables/TicketPriorityActionService.html" target="_self">TicketPriorityActionService</a>, ticketTypeService: <a href="../injectables/TicketTypeActionService.html" target="_self">TicketTypeActionService</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="38" class="link-to-prism">src/tickets/services/ticket-bulk.action.service.ts:38</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>ticketRepository</td>
                                                  
                                                        <td>
                                                                    <code>TicketRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>cachedTicketRepository</td>
                                                  
                                                        <td>
                                                                    <code>CachedTicketRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>transactionService</td>
                                                  
                                                        <td>
                                                                    <code>TransactionService</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>usersService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/UsersService.html" target="_self" >UsersService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>teamsService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/TeamsService.html" target="_self" >TeamsService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>accountsService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/AccountsService.html" target="_self" >AccountsService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>ticketStatusService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/TicketStatusActionService.html" target="_self" >TicketStatusActionService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>ticketPriorityService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/TicketPriorityActionService.html" target="_self" >TicketPriorityActionService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>ticketTypeService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/TicketTypeActionService.html" target="_self" >TicketTypeActionService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="archiveTicketsBulk"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>archiveTicketsBulk</b></span>
                        <a href="#archiveTicketsBulk"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>archiveTicketsBulk(request: <a href="../interfaces/FastifyRequest.html" target="_self">FastifyRequest</a>, body: <a href="../classes/ArchiveTicketsBulkDto.html" target="_self">ArchiveTicketsBulkDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="571"
                                    class="link-to-prism">src/tickets/services/ticket-bulk.action.service.ts:571</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Archives tickets in bulk.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../interfaces/FastifyRequest.html" target="_self" >FastifyRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The request object.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>body</td>
                                            <td>
                                                            <code><a href="../classes/ArchiveTicketsBulkDto.html" target="_self" >ArchiveTicketsBulkDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The archived tickets.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createTicketsBulk"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>createTicketsBulk</b></span>
                        <a href="#createTicketsBulk"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createTicketsBulk(request: <a href="../interfaces/FastifyRequest.html" target="_self">FastifyRequest</a>, body: <a href="../classes/CreateTicketsBulkDto.html" target="_self">CreateTicketsBulkDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="263"
                                    class="link-to-prism">src/tickets/services/ticket-bulk.action.service.ts:263</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Creates tickets in bulk.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../interfaces/FastifyRequest.html" target="_self" >FastifyRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The request object.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>body</td>
                                            <td>
                                                            <code><a href="../classes/CreateTicketsBulkDto.html" target="_self" >CreateTicketsBulkDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The body object.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The created tickets.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteTicketsBulk"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>deleteTicketsBulk</b></span>
                        <a href="#deleteTicketsBulk"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteTicketsBulk(request: <a href="../interfaces/FastifyRequest.html" target="_self">FastifyRequest</a>, body: <a href="../classes/DeleteTicketsBulkDto.html" target="_self">DeleteTicketsBulkDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="639"
                                    class="link-to-prism">src/tickets/services/ticket-bulk.action.service.ts:639</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Deletes tickets in bulk.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../interfaces/FastifyRequest.html" target="_self" >FastifyRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The request object.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>body</td>
                                            <td>
                                                            <code><a href="../classes/DeleteTicketsBulkDto.html" target="_self" >DeleteTicketsBulkDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The deleted tickets.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="extractLookupDetailsFromTicketsPayload"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>extractLookupDetailsFromTicketsPayload</b></span>
                        <a href="#extractLookupDetailsFromTicketsPayload"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>extractLookupDetailsFromTicketsPayload(tickets: <a href="../classes/CreateTicketBody.html" target="_self">CreateTicketBody[]</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="77"
                                    class="link-to-prism">src/tickets/services/ticket-bulk.action.service.ts:77</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Extracts the accounts, teams, and requestor emails mentioned from a list of tickets</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>tickets</td>
                                            <td>
                                                            <code><a href="../classes/CreateTicketBody.html" target="_self" >CreateTicketBody[]</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The list of tickets to extract the details from</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>{ accountsMentioned: any; teamsMentioned: any; requestorEmailsMentioned: any; statusesMentioned: any; prioritiesMentioned: any; typesMentioned: any; assignedAgentsMentioned: any; }</code>

                        </div>
                            <div class="io-description">
                                <p>An object containing the accounts, teams, requestor emails, statuses, and priorities</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="extractUserFromRequest"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>extractUserFromRequest</b></span>
                        <a href="#extractUserFromRequest"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>extractUserFromRequest(request: <a href="../interfaces/FastifyRequest.html" target="_self">FastifyRequest</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="58"
                                    class="link-to-prism">src/tickets/services/ticket-bulk.action.service.ts:58</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Extracts the user from the request.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../interfaces/FastifyRequest.html" target="_self" >FastifyRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;User&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The user.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="fetchDataForTicketBulkCreation"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>fetchDataForTicketBulkCreation</b></span>
                        <a href="#fetchDataForTicketBulkCreation"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>fetchDataForTicketBulkCreation(options: Record<string | Set<string>>, organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="143"
                                    class="link-to-prism">src/tickets/services/ticket-bulk.action.service.ts:143</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Fetches the data for ticket bulk creation.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>options</td>
                                            <td>
                                                        <code>Record&lt;string | Set&lt;string&gt;&gt;</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The options for the ticket bulk creation.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The data for the ticket bulk creation.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAccountForTicket"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>findAccountForTicket</b></span>
                        <a href="#findAccountForTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAccountForTicket(ticketDetails: <a href="../classes/CreateTicketBody.html" target="_self">CreateTicketBody</a>, accounts: Account[], accountsByPrimaryDomains: Account[], stopOnError: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank">boolean</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="229"
                                    class="link-to-prism">src/tickets/services/ticket-bulk.action.service.ts:229</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds the account for a ticket.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>ticketDetails</td>
                                            <td>
                                                            <code><a href="../classes/CreateTicketBody.html" target="_self" >CreateTicketBody</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The details of the ticket.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>accounts</td>
                                            <td>
                                                        <code>Account[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The accounts to search through.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>accountsByPrimaryDomains</td>
                                            <td>
                                                        <code>Account[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The accounts by their primary domains to search through.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>stopOnError</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>Whether to stop on error.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                <p>The account for the ticket.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateTicketsBulk"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>updateTicketsBulk</b></span>
                        <a href="#updateTicketsBulk"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateTicketsBulk(request: <a href="../interfaces/FastifyRequest.html" target="_self">FastifyRequest</a>, body: <a href="../classes/UpdateTicketsBulkDto.html" target="_self">UpdateTicketsBulkDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="385"
                                    class="link-to-prism">src/tickets/services/ticket-bulk.action.service.ts:385</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Updates tickets in bulk.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../interfaces/FastifyRequest.html" target="_self" >FastifyRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The request object.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>body</td>
                                            <td>
                                                            <code><a href="../classes/UpdateTicketsBulkDto.html" target="_self" >UpdateTicketsBulkDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The body object.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../interfaces/UpdateResult.html" target="_self" >Promise&lt;UpdateResult&gt;</a></code>

                        </div>
                            <div class="io-description">
                                <p>The updated tickets.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from &quot;@nestjs/common&quot;;
import {
  Account,
  CachedTicketRepository,
  Team,
  TicketPriority,
  TicketRepository,
  TicketStatus,
  TicketType,
  TransactionService,
  User,
} from &quot;@repo/thena-platform-entities&quot;;
import { FastifyRequest } from &quot;fastify&quot;;
import { In } from &quot;typeorm&quot;;
import { AccountsService } from &quot;../../accounts/services/accounts.service&quot;;
import { extractEmailDetails } from &quot;../../common/utils/extract-email-details&quot;;
import { TeamsService } from &quot;../../teams/services/teams.service&quot;;
import { UsersService } from &quot;../../users/services/users.service&quot;;
import {
  ArchiveTicketsBulkDto,
  CreateTicketsBulkDto,
  DeleteTicketsBulkDto,
  UpdateTicketsBulkDto,
} from &quot;../dto/ticket-bulk.dto&quot;;
import { CreateTicketBody } from &quot;../dto/ticket.dto&quot;;
import { UpdateResult } from &quot;../interfaces/ticket.interface&quot;;
import { TicketPriorityActionService } from &quot;./ticket-priority.action.service&quot;;
import { TicketStatusActionService } from &quot;./ticket-status.action.service&quot;;
import { TicketTypeActionService } from &quot;./ticket-type.action.service&quot;;

@Injectable()
export class TicketsBulkActionService {
  constructor(
    // Injected Repositories
    private readonly ticketRepository: TicketRepository,
    private readonly cachedTicketRepository: CachedTicketRepository,

    // Injected Services
    private readonly transactionService: TransactionService,
    private readonly usersService: UsersService,
    private readonly teamsService: TeamsService,
    private readonly accountsService: AccountsService,
    private readonly ticketStatusService: TicketStatusActionService,
    private readonly ticketPriorityService: TicketPriorityActionService,
    private readonly ticketTypeService: TicketTypeActionService,
  ) {}

  /**
   * Extracts the user from the request.
   * @returns The user.
   */
  private async extractUserFromRequest(request: FastifyRequest): Promise&lt;User&gt; {
    const userEmail &#x3D; request.user.email;
    if (!userEmail) {
      throw new UnauthorizedException(&quot;User is not authenticated!&quot;);
    }

    const user &#x3D; await this.usersService.findOneByEmail(userEmail);
    if (!user) {
      throw new UnauthorizedException(&quot;User is not authenticated!&quot;);
    }

    return user;
  }

  /**
   * Extracts the accounts, teams, and requestor emails mentioned from a list of tickets
   * @param tickets The list of tickets to extract the details from
   * @returns An object containing the accounts, teams, requestor emails, statuses, and priorities
   */
  private extractLookupDetailsFromTicketsPayload(tickets: CreateTicketBody[]) {
    const accountsMentioned &#x3D; new Set&lt;string&gt;();
    const teamsMentioned &#x3D; new Set&lt;string&gt;();
    const requestorEmailsMentioned &#x3D; new Set&lt;string&gt;();
    const statusesMentioned &#x3D; new Set&lt;string&gt;();
    const prioritiesMentioned &#x3D; new Set&lt;string&gt;();
    const typesMentioned &#x3D; new Set&lt;string&gt;();
    const assignedAgentsMentioned &#x3D; new Set&lt;string&gt;();

    // Iterate through the tickets and extract the accounts, teams, and requestor emails mentioned
    for (let i &#x3D; 0; i &lt; tickets.length; i++) {
      const ticket &#x3D; tickets[i];

      // If the ticket has an account, add it to the list
      if (ticket.accountId) {
        accountsMentioned.add(ticket.accountId);
      } else if (ticket.requestorEmail) {
        // If the ticket has a requestor email but does not have an account id
        const { domain } &#x3D; extractEmailDetails(ticket.requestorEmail);
        if (domain) {
          requestorEmailsMentioned.add(domain);
        }
      }

      if (ticket.assignedAgentId) {
        assignedAgentsMentioned.add(ticket.assignedAgentId);
      }

      // If the ticket has a type id add it to the list
      if (ticket.typeId) {
        typesMentioned.add(ticket.typeId);
      }

      // If the ticket has a status id add it to the list
      if (ticket.statusId) {
        statusesMentioned.add(ticket.statusId);
      }

      // If the ticket has a priority id add it to the list
      if (ticket.priorityId) {
        prioritiesMentioned.add(ticket.priorityId);
      }

      // If the ticket has a team id add it to the list
      if (ticket.teamId) {
        teamsMentioned.add(ticket.teamId);
      }
    }

    return {
      accountsMentioned,
      teamsMentioned,
      requestorEmailsMentioned,
      statusesMentioned,
      prioritiesMentioned,
      typesMentioned,
      assignedAgentsMentioned,
    };
  }

  /**
   * Fetches the data for ticket bulk creation.
   * @param options The options for the ticket bulk creation.
   * @param organizationId The ID of the organization.
   * @returns The data for the ticket bulk creation.
   */
  private async fetchDataForTicketBulkCreation(
    options: Record&lt;string, Set&lt;string&gt;&gt;,
    organizationId: string,
  ) {
    const {
      accountsMentioned,
      teamsMentioned,
      requestorEmailsMentioned,
      statusesMentioned,
      prioritiesMentioned,
      typesMentioned,
      assignedAgentsMentioned,
    } &#x3D; options;

    // Fetch the accounts, teams, accounts by their primary domains, statuses, and priorities
    const [
      accounts,
      teams,
      accountsByPrimaryDomains,
      statuses,
      priorities,
      types,
      assignedAgents,
    ] &#x3D; await Promise.all([
      // Find the accounts by their public ids
      this.accountsService.findAccountsByPublicIds(
        Array.from(accountsMentioned),
        organizationId,
      ),

      // Find the teams by their public ids
      this.teamsService.findTeamsByPublicIds(
        Array.from(teamsMentioned),
        organizationId,
      ),

      // Find accounts by their primary domains
      this.accountsService.findAccountsByPrimaryDomains(
        Array.from(requestorEmailsMentioned),
        organizationId,
      ),

      // Find the statuses by their public ids
      this.ticketStatusService.findTicketStatusesByPublicIds(
        Array.from(statusesMentioned),
        organizationId,
      ),

      // Find the priorities by their public ids
      this.ticketPriorityService.findTicketPrioritiesByPublicIds(
        Array.from(prioritiesMentioned),
        organizationId,
      ),

      // Find the types by their public ids
      this.ticketTypeService.findTicketTypesByPublicIds(
        Array.from(typesMentioned),
        organizationId,
      ),

      // Find the users by their public ids
      this.usersService.getUserByUserId(
        Array.from(assignedAgentsMentioned),
        organizationId,
      ),
    ]);

    return {
      accounts,
      teams,
      accountsByPrimaryDomains,
      statuses,
      priorities,
      types,
      assignedAgents,
    };
  }

  /**
   * Finds the account for a ticket.
   * @param ticketDetails The details of the ticket.
   * @param accounts The accounts to search through.
   * @param accountsByPrimaryDomains The accounts by their primary domains to search through.
   * @param stopOnError Whether to stop on error.
   * @returns The account for the ticket.
   */
  private findAccountForTicket(
    ticketDetails: CreateTicketBody,
    accounts: Account[],
    accountsByPrimaryDomains: Account[],
    stopOnError: boolean,
  ) {
    let account: Account | undefined;
    if (ticketDetails.accountId) {
      account &#x3D; accounts.find(
        (account) &#x3D;&gt; account.uid &#x3D;&#x3D;&#x3D; ticketDetails.accountId,
      );

      // If the account is not found and the option to stop on error is true, throw an error
      if (!account &amp;&amp; stopOnError) {
        throw new BadRequestException(&quot;Account not found!&quot;);
      }
    } else {
      const { domain } &#x3D; extractEmailDetails(ticketDetails.requestorEmail);
      if (domain) {
        account &#x3D; accountsByPrimaryDomains.find(
          (account) &#x3D;&gt; account.primaryDomain &#x3D;&#x3D;&#x3D; domain,
        );
      }
    }

    return account;
  }

  /**
   * Creates tickets in bulk.
   * @param request The request object.
   * @param body The body object.
   * @returns The created tickets.
   */
  async createTicketsBulk(request: FastifyRequest, body: CreateTicketsBulkDto) {
    const user &#x3D; await this.extractUserFromRequest(request);
    const organizationId &#x3D; user.organizationId;

    const { tickets, options } &#x3D; body;
    const stopOnError &#x3D; options?.stopOnError ?? false;

    const lookupDetails &#x3D; this.extractLookupDetailsFromTicketsPayload(tickets);

    // Find the accounts, teams, and accounts by their primary domains
    const {
      accounts,
      teams,
      accountsByPrimaryDomains,
      statuses,
      priorities,
      types,
    } &#x3D; await this.fetchDataForTicketBulkCreation(
      lookupDetails,
      organizationId,
    );

    const ticketsToCreate &#x3D; tickets
      .map((ticketDetails) &#x3D;&gt; {
        // Find the account
        const account &#x3D; this.findAccountForTicket(
          ticketDetails,
          accounts,
          accountsByPrimaryDomains,
          stopOnError,
        );

        // Find the team
        let team: Team | undefined;
        if (ticketDetails.teamId) {
          team &#x3D; teams.find((team) &#x3D;&gt; team.uid &#x3D;&#x3D;&#x3D; ticketDetails.teamId);

          // If the team is not found and the option to stop on error is true, throw an error
          if (!team &amp;&amp; stopOnError) {
            throw new BadRequestException(&quot;Team not found!&quot;);
          } else if (!team) {
            // If the team is not found, skip the ticket
            return null;
          }
        }

        // Find the status
        let status: TicketStatus | undefined;
        if (ticketDetails.statusId) {
          status &#x3D; statuses.find(
            (status) &#x3D;&gt;
              status.uid &#x3D;&#x3D;&#x3D; ticketDetails.statusId &amp;&amp;
              status.teamId &#x3D;&#x3D;&#x3D; team.id,
          );

          // If the status is not found and the option to stop on error is true, throw an error
          if (!status &amp;&amp; stopOnError) {
            throw new BadRequestException(&quot;Status not found!&quot;);
          } else if (!status) {
            // If the status is not found, skip the ticket
            return null;
          }
        }

        // Find the priority
        let priority: TicketPriority | undefined;
        if (ticketDetails.priorityId) {
          priority &#x3D; priorities.find(
            (priority) &#x3D;&gt;
              priority.uid &#x3D;&#x3D;&#x3D; ticketDetails.priorityId &amp;&amp;
              priority.teamId &#x3D;&#x3D;&#x3D; team.id,
          );

          // If the priority is not found and the option to stop on error is true, throw an error
          if (!priority &amp;&amp; stopOnError) {
            throw new BadRequestException(&quot;Priority not found!&quot;);
          } else if (!priority) {
            // If the priority is not found, skip the ticket
            return null;
          }
        }

        // Find the type
        let type: TicketType | undefined;
        if (ticketDetails.typeId) {
          type &#x3D; types.find((type) &#x3D;&gt; type.uid &#x3D;&#x3D;&#x3D; ticketDetails.typeId);

          // If the type is not found and the option to stop on error is true, throw an error
          if (!type &amp;&amp; stopOnError) {
            throw new BadRequestException(&quot;Type not found!&quot;);
          }
        }

        return {
          title: ticketDetails.title,
          description: ticketDetails?.description,
          accountId: account?.id,
          requestorEmail: ticketDetails.requestorEmail,
          teamId: team?.id,
          organizationId,
          isPrivate: ticketDetails.isPrivate,
          statusId: status?.id,
          priorityId: priority?.id,
        };
      })
      .filter(Boolean);

    const createdTickets &#x3D;
      this.cachedTicketRepository.createMany(ticketsToCreate);
    const savedTickets &#x3D; await this.cachedTicketRepository.saveMany(
      createdTickets,
    );

    return savedTickets;
  }

  /**
   * Updates tickets in bulk.
   * @param request The request object.
   * @param body The body object.
   * @returns The updated tickets.
   */
  async updateTicketsBulk(
    request: FastifyRequest,
    body: UpdateTicketsBulkDto,
  ): Promise&lt;UpdateResult&gt; {
    const user &#x3D; await this.extractUserFromRequest(request);
    const organizationId &#x3D; user.organizationId;

    const {
      statusId,
      priorityId,
      isPrivate,
      ticketIds,
      typeId,
      assignedAgentId,
    } &#x3D; body;
    if (ticketIds.length &#x3D;&#x3D;&#x3D; 0) {
      throw new BadRequestException(&quot;No tickets provided!&quot;);
    }

    // Find the tickets to update, status, priority, and type
    const [tickets, status, priority, type] &#x3D; await Promise.all([
      // Find the tickets to update
      this.ticketRepository.findAll({
        where: { uid: In(ticketIds), organizationId },
        relations: [&quot;team&quot;],
      }),

      // Find the status
      statusId
        ? this.ticketStatusService.findTicketStatusByPublicId(
            statusId,
            organizationId,
          )
        : null,

      // Find the priority
      priorityId
        ? this.ticketPriorityService.findTicketPriorityByPublicId(
            priorityId,
            organizationId,
          )
        : null,

      // Find the types
      typeId
        ? this.ticketTypeService.findTicketTypeByPublicId(
            typeId,
            organizationId,
          )
        : null,
    ]);

    // If no tickets are found, throw an error
    if (!tickets.length) {
      throw new NotFoundException(&quot;No tickets found!&quot;);
    }

    // If the status is provided but not found, throw an error
    if (statusId &amp;&amp; !status) {
      throw new BadRequestException(&quot;Status not found!&quot;);
    }

    // If the type is provided but not found, throw an error
    if (typeId &amp;&amp; !type) {
      throw new BadRequestException(&quot;Type not found!&quot;);
    }

    // If the priority is provided but not found, throw an error
    if (priorityId &amp;&amp; !priority) {
      throw new BadRequestException(&quot;Priority not found!&quot;);
    }

    // If the tickets are not in the same team, throw an error
    const teamIds &#x3D; tickets.map((ticket) &#x3D;&gt; ticket.teamId);
    const teamIdsSet &#x3D; new Set(teamIds);
    if (teamIdsSet.size &gt; 1) {
      throw new BadRequestException(&quot;Tickets are not in the same team!&quot;);
    }

    // If the assigned agent is provided, check if the user belongs to the team
    const team &#x3D; tickets[0].team;
    let assignedAgent: User | undefined;
    if (assignedAgentId) {
      const users &#x3D; await this.usersService.getUserByUserId(
        assignedAgentId,
        organizationId,
      );
      const user &#x3D; users?.[0];

      // If the user is not found, throw an error
      if (!user) {
        throw new BadRequestException(&quot;Assigned agent not found!&quot;);
      }

      // Check if the user belongs to the team
      const userBelongsToTeam &#x3D; await this.teamsService.userBelongsToTeam(
        user.id,
        team.id,
        organizationId,
      );

      // If the user does not belong to the team, throw an error
      if (!userBelongsToTeam) {
        throw new BadRequestException(&quot;User does not belong to the team!&quot;);
      }

      assignedAgent &#x3D; user;
    }

    const skippedTickets: Array&lt;{ id: string; reason: string }&gt; &#x3D; [];

    // Run the update in a transaction
    const updatedTickets &#x3D; await this.transactionService.runInTransaction(
      async (txnContext) &#x3D;&gt; {
        const ticketsToUpdate &#x3D; tickets
          .map((ticket) &#x3D;&gt; {
            // If the status is not in the same team as the ticket, skip the ticket
            if (statusId &amp;&amp; status.teamId !&#x3D;&#x3D; ticket.team.id) {
              skippedTickets.push({
                id: ticket.id,
                reason: &quot;Status is not in the same team as the ticket&quot;,
              });

              return null;
            }

            // If the priority is not in the same team as the ticket, skip the ticket
            if (priorityId &amp;&amp; priority.teamId !&#x3D;&#x3D; ticket.team.id) {
              skippedTickets.push({
                id: ticket.id,
                reason: &quot;Priority is not in the same team as the ticket&quot;,
              });

              return null;
            }

            // If the type is not in the same team as the ticket, skip the ticket
            if (typeId &amp;&amp; type.teamId !&#x3D;&#x3D; ticket.team.id) {
              skippedTickets.push({
                id: ticket.id,
                reason: &quot;Type is not in the same team as the ticket&quot;,
              });

              return null;
            }

            // Build the ticket to update
            const build &#x3D; {
              ...ticket,
              statusId: statusId ? status?.id : ticket.statusId,
              priorityId: priorityId ? priority?.id : ticket.priorityId,
              typeId: typeId ? type?.id : ticket.typeId,
              isPrivate,
            };

            // If the assigned agent is provided, set the assigned agent ID and assigned agent
            if (assignedAgent) {
              build.assignedAgentId &#x3D; assignedAgent.id;
              build.assignedAgent &#x3D; assignedAgent;
            }

            return build;
          })
          .filter(Boolean);

        // Save the tickets
        const savedTickets &#x3D; await this.ticketRepository.saveManyWithTxn(
          txnContext,
          ticketsToUpdate,
        );

        return savedTickets;
      },
    );

    return {
      updated: updatedTickets,
      skipped: skippedTickets,
    };
  }

  /**
   * Archives tickets in bulk.
   * @param request The request object.
   * @returns The archived tickets.
   */
  async archiveTicketsBulk(
    request: FastifyRequest,
    body: ArchiveTicketsBulkDto,
  ) {
    const user &#x3D; await this.extractUserFromRequest(request);
    const organizationId &#x3D; user.organizationId;

    const { ticketIds } &#x3D; body;

    // Find the tickets to archive
    const ticketsToArchive &#x3D; await this.ticketRepository.findAll({
      where: { uid: In(ticketIds), organizationId },
      relations: [&quot;team&quot;],
    });

    // If no tickets are found, throw an error
    if (!ticketsToArchive.length) {
      throw new NotFoundException(&quot;No tickets found!&quot;);
    }

    // If the tickets are not in the same team, throw an error
    const ticketTeams &#x3D; ticketsToArchive.map((ticket) &#x3D;&gt; ticket.team.id);
    const ticketTeamsSet &#x3D; new Set(ticketTeams);
    if (ticketTeamsSet.size &gt; 1) {
      throw new BadRequestException(&quot;Tickets are not in the same team!&quot;);
    }

    // Check if the user belongs to the team
    const userBelongsToTeam &#x3D; await this.teamsService.userBelongsToTeam(
      user.id,
      ticketsToArchive[0].teamId,
      user.organizationId,
    );

    // If the user does not belong to the team, throw an error
    if (!userBelongsToTeam) {
      throw new ForbiddenException(
        &quot;You are not authorized to archive these tickets!&quot;,
      );
    }

    // Archive the tickets
    const archivedTickets &#x3D; await this.transactionService.runInTransaction(
      (txnContext) &#x3D;&gt; {
        // Archive the tickets
        for (const ticket of ticketsToArchive) {
          if (ticket.archivedAt) {
            continue;
          }

          ticket.archivedAt &#x3D; new Date();
        }

        return this.ticketRepository.saveManyWithTxn(
          txnContext,
          ticketsToArchive,
        );
      },
    );

    return archivedTickets;
  }

  /**
   * Deletes tickets in bulk.
   * @param request The request object.
   * @returns The deleted tickets.
   */
  async deleteTicketsBulk(request: FastifyRequest, body: DeleteTicketsBulkDto) {
    const user &#x3D; await this.extractUserFromRequest(request);
    const organizationId &#x3D; user.organizationId;

    const { ticketIds } &#x3D; body;

    // Find the tickets to delete
    const ticketsToDelete &#x3D; await this.ticketRepository.findAll({
      where: { uid: In(ticketIds), organizationId },
      relations: [&quot;team&quot;],
    });

    // If no tickets are found, throw an error
    if (!ticketsToDelete.length) {
      throw new NotFoundException(&quot;No tickets found!&quot;);
    }

    // If the tickets are not in the same team, throw an error
    const ticketTeams &#x3D; ticketsToDelete.map((ticket) &#x3D;&gt; ticket.team.id);
    const ticketTeamsSet &#x3D; new Set(ticketTeams);
    if (ticketTeamsSet.size &gt; 1) {
      throw new BadRequestException(&quot;Tickets are not in the same team!&quot;);
    }

    // Check if the user belongs to the team
    const userBelongsToTeam &#x3D; await this.teamsService.userBelongsToTeam(
      user.id,
      ticketsToDelete[0].teamId,
      user.organizationId,
    );

    // If the user does not belong to the team, throw an error
    if (!userBelongsToTeam) {
      throw new ForbiddenException(
        &quot;You are not authorized to delete these tickets!&quot;,
      );
    }

    // Delete the tickets
    for (const ticket of ticketsToDelete) {
      if (ticket.deletedAt) {
        continue;
      }

      ticket.deletedAt &#x3D; new Date();
    }

    const deletedTickets &#x3D; await this.cachedTicketRepository.saveMany(
      ticketsToDelete,
    );

    return deletedTickets;
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'TicketsBulkActionService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
