<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >AccountCommonService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/accounts/services/account-commons.service.ts</code>
        </p>


            <p class="comment">
                <h3>Description</h3>
            </p>
            <p class="comment">
                <p>This service is created to avoid circular dependencies.
This service should not be injecting any other account services. It can directly inject repositories.
Every account service should be able to inject this service.
This service should not be used for any business logic. It should only be used for validation and getting entities.
And to be used if and only if there is a risk of circular dependency.</p>

            </p>



            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findDefaultAttributeValue" >findDefaultAttributeValue</a>
                            </li>
                            <li>
                                <a href="#getAttributeValue" >getAttributeValue</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#publishEventToSNSQueue" >publishEventToSNSQueue</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#validateAndGetAccount" >validateAndGetAccount</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#validateAndGetActivity" >validateAndGetActivity</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(sentryService: SentryService, logger: ILogger, cachedAccountRepository: CachedAccountRepository, cachedAccountAttributeValueRepository: CachedAccountAttributeValueRepository, cachedAccountActivityRepository: CachedAccountActivityRepository, configService: <a href="../injectables/ConfigService.html" target="_self">ConfigService</a>, accountsSNSPublisherQueue: Queue)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="31" class="link-to-prism">src/accounts/services/account-commons.service.ts:31</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>sentryService</td>
                                                  
                                                        <td>
                                                                    <code>SentryService</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>logger</td>
                                                  
                                                        <td>
                                                                    <code>ILogger</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>cachedAccountRepository</td>
                                                  
                                                        <td>
                                                                    <code>CachedAccountRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>cachedAccountAttributeValueRepository</td>
                                                  
                                                        <td>
                                                                    <code>CachedAccountAttributeValueRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>cachedAccountActivityRepository</td>
                                                  
                                                        <td>
                                                                    <code>CachedAccountActivityRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>configService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/ConfigService.html" target="_self" >ConfigService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>accountsSNSPublisherQueue</td>
                                                  
                                                        <td>
                                                                    <code>Queue</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findDefaultAttributeValue"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findDefaultAttributeValue</b></span>
                        <a href="#findDefaultAttributeValue"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findDefaultAttributeValue(organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, attribute: AccountAttributeType)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="112"
                                    class="link-to-prism">src/accounts/services/account-commons.service.ts:112</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds the default attribute value for an organization and attribute.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>attribute</td>
                                            <td>
                                                        <code>AccountAttributeType</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The attribute type.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;AccountAttributeValue&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The default attribute value.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getAttributeValue"></a>
                    <span class="name">
                        <span ><b>getAttributeValue</b></span>
                        <a href="#getAttributeValue"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getAttributeValue(attributeValue: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="85"
                                    class="link-to-prism">src/accounts/services/account-commons.service.ts:85</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Gets an account attribute value.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>attributeValue</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The UID / value of the attribute value</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;AccountAttributeValue&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The account attribute value</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="publishEventToSNSQueue"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>publishEventToSNSQueue</b></span>
                        <a href="#publishEventToSNSQueue"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>publishEventToSNSQueue(event: T, eventData: T, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="155"
                                    class="link-to-prism">src/accounts/services/account-commons.service.ts:155</a></div>
                        </td>
                    </tr>

                    <tr>
                        <td class="col-md-4">
                            <b>Type parameters :</b>
                            <ul class="type-parameters">
                                    <li>Q</li>
                                    <li>T</li>
                            </ul>
                        </td>
                    </tr>

            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Publishes an account event to SNS.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>event</td>
                                            <td>
                                                        <code>T</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The event to publish.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>eventData</td>
                                            <td>
                                                        <code>T</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The data to publish.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The user associated with the event.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateAndGetAccount"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>validateAndGetAccount</b></span>
                        <a href="#validateAndGetAccount"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateAndGetAccount(accountUID: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="59"
                                    class="link-to-prism">src/accounts/services/account-commons.service.ts:59</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Validates and gets an account.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>accountUID</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The UID of the account</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;Account&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The account</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateAndGetActivity"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>validateAndGetActivity</b></span>
                        <a href="#validateAndGetActivity"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateAndGetActivity(activityUID: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="132"
                                    class="link-to-prism">src/accounts/services/account-commons.service.ts:132</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Validates and gets an account activity.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>activityUID</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The UID of the activity</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;AccountActivity&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The account activity</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { InjectQueue } from &quot;@nestjs/bullmq&quot;;
import { Inject, Injectable, NotFoundException } from &quot;@nestjs/common&quot;;
import { SentryService } from &quot;@repo/nestjs-commons/filters&quot;;
import { ILogger } from &quot;@repo/nestjs-commons/logger&quot;;
import {
  Account,
  AccountActivity,
  AccountAttributeType,
  AccountAttributeValue,
  CachedAccountActivityRepository,
  CachedAccountAttributeValueRepository,
  CachedAccountRepository,
} from &quot;@repo/thena-platform-entities&quot;;
import { Queue } from &quot;bullmq&quot;;
import { CurrentUser } from &quot;../../common/decorators&quot;;
import { ConfigService } from &quot;../../config/config.service&quot;;
import { QueueNames } from &quot;../../constants/queue.constants&quot;;
import { SNSEvent } from &quot;../interfaces/sns-events.interface&quot;;

/**
 * This service is created to avoid circular dependencies.
 * This service should not be injecting any other account services. It can directly inject repositories.
 * Every account service should be able to inject this service.
 * This service should not be used for any business logic. It should only be used for validation and getting entities.
 * And to be used if and only if there is a risk of circular dependency.
 *
 * @export
 * @class AccountCommonService
 */
@Injectable()
export class AccountCommonService {
  constructor(
    @Inject(&quot;Sentry&quot;) private readonly sentryService: SentryService,
    @Inject(&quot;CustomLogger&quot;) private readonly logger: ILogger,

    // Injected repositories
    private readonly cachedAccountRepository: CachedAccountRepository,

    private readonly cachedAccountAttributeValueRepository: CachedAccountAttributeValueRepository,

    private readonly cachedAccountActivityRepository: CachedAccountActivityRepository,

    // Config service
    private readonly configService: ConfigService,

    // SNS Publisher Queue
    @InjectQueue(QueueNames.ACCOUNTS_SNS_PUBLISHER)
    private readonly accountsSNSPublisherQueue: Queue,
  ) {}

  /**
   * Validates and gets an account.
   *
   * @param accountUID The UID of the account
   * @param orgId The ID of the organization
   * @returns The account
   * @throws NotFoundException if the account is not found
   */
  async validateAndGetAccount(
    accountUID: string,
    orgId: string,
  ): Promise&lt;Account&gt; {
    const account &#x3D; await this.cachedAccountRepository.findByCondition({
      where: { uid: accountUID, organizationId: orgId, isActive: true },
      relations: [
        &quot;accountOwner&quot;,
        &quot;statusAttribute&quot;,
        &quot;classificationAttribute&quot;,
        &quot;healthAttribute&quot;,
      ],
    });
    if (!account) {
      throw new NotFoundException(&quot;Account not found&quot;);
    }
    return account;
  }

  /**
   * Gets an account attribute value.
   *
   * @param attributeValue The UID / value of the attribute value
   * @param organizationId The ID of the organization
   * @returns The account attribute value
   */
  getAttributeValue(
    attributeValue: string,
    organizationId: string,
  ): Promise&lt;AccountAttributeValue&gt; {
    return this.cachedAccountAttributeValueRepository.findByCondition({
      where: [
        {
          uid: attributeValue,
          organizationId,
          isActive: true,
        },
        {
          value: attributeValue,
          organizationId,
          isActive: true,
        },
      ],
    });
  }

  /**
   * Finds the default attribute value for an organization and attribute.
   *
   * @param organizationId The ID of the organization.
   * @param attribute The attribute type.
   * @returns The default attribute value.
   */
  async findDefaultAttributeValue(
    organizationId: string,
    attribute: AccountAttributeType,
  ): Promise&lt;AccountAttributeValue&gt; {
    const defaultAttributeValue &#x3D;
      await this.cachedAccountAttributeValueRepository.findByCondition({
        where: { organizationId, isDefault: true, attribute },
      });

    return defaultAttributeValue;
  }

  /**
   * Validates and gets an account activity.
   *
   * @param activityUID The UID of the activity
   * @param organizationId The ID of the organization
   * @returns The account activity
   * @throws NotFoundException if the activity is not found
   */
  async validateAndGetActivity(
    activityUID: string,
    organizationId: string,
  ): Promise&lt;AccountActivity&gt; {
    const activity &#x3D; await this.cachedAccountActivityRepository.findByCondition(
      {
        where: { uid: activityUID, isActive: true },
        relations: [&quot;account&quot;],
      },
    );

    if (!activity || activity.account.organizationId !&#x3D;&#x3D; organizationId) {
      throw new NotFoundException(&quot;Activity not found&quot;);
    }
    return activity;
  }

  /**
   * Publishes an account event to SNS.
   * @param event The event to publish.
   * @param eventData The data to publish.
   * @param user The user associated with the event.
   */
  async publishEventToSNSQueue&lt;Q, T extends SNSEvent&lt;Q&gt;&gt;(
    event: T[&quot;eventType&quot;],
    eventData: T,
    user: CurrentUser,
  ) {
    try {
      await this.accountsSNSPublisherQueue.add(
        QueueNames.ACCOUNTS_SNS_PUBLISHER,
        {
          event,
          eventData,
          user,
        },
        {
          attempts: 3,
          backoff: {
            type: &quot;exponential&quot;,
            delay: 1000, // 1 second
          },
        },
      );
    } catch (error) {
      this.logger.error(
        &#x60;Error encountered while publishing account event - ${event} for organization ${eventData.orgId} to SNS: ${error?.message}&#x60;,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: &quot;ACCOUNTS_EVENTS&quot;,
        fn: &quot;publishEventToSNSQueue&quot;,
        organizationId: eventData.orgId,
        user,
        event,
        eventData,
      });

      throw error;
    }
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'AccountCommonService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
