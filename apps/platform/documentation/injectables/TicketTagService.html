<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >TicketTagService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/tags/services/ticket-tags.service.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#addTagsToTicket" >addTagsToTicket</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findTicketTags" >findTicketTags</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#removeTagFromTicket" >removeTagFromTicket</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(tagRepository: TagRepository, ticketRepository: TicketRepository, validationService: <a href="../injectables/ValidationService.html" target="_self">ValidationService</a>, logger: ILogger)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="35" class="link-to-prism">src/tags/services/ticket-tags.service.ts:35</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>tagRepository</td>
                                                  
                                                        <td>
                                                                    <code>TagRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>ticketRepository</td>
                                                  
                                                        <td>
                                                                    <code>TicketRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>validationService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/ValidationService.html" target="_self" >ValidationService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>logger</td>
                                                  
                                                        <td>
                                                                    <code>ILogger</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="addTagsToTicket"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>addTagsToTicket</b></span>
                        <a href="#addTagsToTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>addTagsToTicket(ticketUuid: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, tagUuids: string[])</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="84"
                                    class="link-to-prism">src/tags/services/ticket-tags.service.ts:84</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>ticketUuid</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>tagUuids</td>
                                            <td>
                                                        <code>string[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/CreateTicketTagsResponse.html" target="_self" >Promise&lt;CreateTicketTagsResponse&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findTicketTags"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findTicketTags</b></span>
                        <a href="#findTicketTags"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findTicketTags(ticketUuid: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="44"
                                    class="link-to-prism">src/tags/services/ticket-tags.service.ts:44</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>ticketUuid</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/GetTicketTagsResponse.html" target="_self" >Promise&lt;GetTicketTagsResponse&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="removeTagFromTicket"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>removeTagFromTicket</b></span>
                        <a href="#removeTagFromTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>removeTagFromTicket(ticketUuid: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, tagUuid: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="183"
                                    class="link-to-prism">src/tags/services/ticket-tags.service.ts:183</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>ticketUuid</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>tagUuid</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/DeleteTicketTagResponse.html" target="_self" >Promise&lt;DeleteTicketTagResponse&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  BadRequestException,
  Inject,
  Injectable,
  NotFoundException,
} from &quot;@nestjs/common&quot;;
import type { ILogger } from &quot;@repo/nestjs-commons/logger&quot;;
import {
  Tag,
  TagRepository,
  TicketRepository,
} from &quot;@repo/thena-platform-entities&quot;;
import { In } from &quot;typeorm&quot;;
import { ValidationService } from &quot;../../common/services/validation.service&quot;;
import {
  CreateTicketTagsResponse,
  DeleteTicketTagResponse,
  GetTicketTagsResponse,
} from &quot;../interfaces/tag-response.interface&quot;;

// Custom exceptions
export class TeamNotAssignedException extends BadRequestException {
  constructor(ticketId: string) {
    super(&#x60;Ticket ${ticketId} has no assigned team&#x60;);
  }
}

export class TagsNotFoundException extends NotFoundException {
  constructor() {
    super(&quot;Some tags were not found or do not belong to the ticket&#x27;s team&quot;);
  }
}

@Injectable()
export class TicketTagService {
  constructor(
    private tagRepository: TagRepository,
    private ticketRepository: TicketRepository,
    @Inject(&quot;ValidationService&quot;)
    private validationService: ValidationService,
    @Inject(&quot;CustomLogger&quot;) private readonly logger: ILogger,
  ) {}

  async findTicketTags(ticketUuid: string): Promise&lt;GetTicketTagsResponse&gt; {
    try {
      // Input validation
      this.validationService.validateUlids([
        { id: ticketUuid, fieldName: &quot;Ticket ID&quot; },
      ]);

      const tickets &#x3D; await this.ticketRepository.findWithRelations({
        where: { uid: ticketUuid },
        relations: {
          tags: true, // Explicitly load tags relation
        },
      });

      if (!tickets || tickets.length &#x3D;&#x3D;&#x3D; 0) {
        throw new NotFoundException(&#x60;Ticket with ID &quot;${ticketUuid}&quot; not found&#x60;);
      }

      const ticket &#x3D; tickets[0];

      // Return only active tags
      const activeTags &#x3D; ticket.tags.filter((tag) &#x3D;&gt; tag.isActive);
      return {
        ticketId: ticketUuid,
        status: true,
        timestamp: new Date(),
        data: {
          count: activeTags.length,
          items: activeTags,
        },
        message:
          activeTags.length &gt; 0
            ? &#x60;Found ${activeTags.length} tags for ticket&#x60;
            : &quot;No active tags found for ticket&quot;,
      };
    } catch (error) {
      return error;
    }
  }

  async addTagsToTicket(
    ticketUuid: string,
    tagUuids: string[],
  ): Promise&lt;CreateTicketTagsResponse&gt; {
    try {
      //   Input validation
      this.validationService.validateUlids([
        { id: ticketUuid, fieldName: &quot;Ticket ID&quot; },
      ]);

      //   Check if all tagIds are valid ulid values
      const invalidTagIds &#x3D; tagUuids.filter((id) &#x3D;&gt; !id.trim());
      if (invalidTagIds.length &gt; 0) {
        throw new BadRequestException(
          &#x60;Invalid tag IDs: ${invalidTagIds.join(&quot;, &quot;)}&#x60;,
        );
      }

      if (!Array.isArray(tagUuids) || tagUuids.length &#x3D;&#x3D;&#x3D; 0) {
        throw new BadRequestException(
          &quot;Tag IDs array is required and cannot be empty&quot;,
        );
      }

      // Load ticket with both tags and team relations
      const tickets &#x3D; await this.ticketRepository.findWithRelations({
        where: { uid: ticketUuid },
        relations: [&quot;tags&quot;, &quot;team&quot;],
      });

      if (!tickets || tickets.length &#x3D;&#x3D;&#x3D; 0) {
        throw new NotFoundException(&#x60;Ticket with ID &quot;${ticketUuid}&quot; not found&#x60;);
      }

      const ticket &#x3D; tickets[0];

      // Check team assignment
      if (!ticket.team) {
        throw new TeamNotAssignedException(ticketUuid);
      }

      // Validate and fetch tags
      const tags &#x3D; await this.tagRepository.findAll({
        where: {
          uid: In(tagUuids),
          teamId: ticket.team.id, // Use the column directly instead of relation TODO: CHECK
          isActive: true,
        },
      });

      // Validate all tags in the array were found. If not, throw an error
      if (tags.length !&#x3D;&#x3D; tagUuids.length) {
        const foundTagIds &#x3D; new Set(tags.map((tag) &#x3D;&gt; tag.id));
        const missingTagIds &#x3D; tagUuids.filter((id) &#x3D;&gt; !foundTagIds.has(id));
        this.logger.error(
          &#x60;Tags not found: ${missingTagIds.join(
            &quot;, &quot;,
          )} for ticket ${ticketUuid}&#x60;,
        );

        throw new TagsNotFoundException();
      }

      // Add new tags while preventing duplicates
      const existingTagIds &#x3D; new Set(ticket.tags.map((tag) &#x3D;&gt; tag.uid));
      const addedTags: Tag[] &#x3D; [];

      // check if the tag is already in the ticket. If not, add it
      tags.forEach((tag) &#x3D;&gt; {
        if (!existingTagIds.has(tag.uid)) {
          ticket.tags.push(tag);
          addedTags.push(tag);
        }
      });

      // Save if there are changes
      if (addedTags.length &gt; 0) {
        await this.ticketRepository.save(ticket);
      }

      // Return response
      return {
        status: true,
        ticketId: ticket.uid,
        timestamp: new Date(),
        data: {
          count: addedTags.length,
          items: addedTags,
        },
        message:
          addedTags.length &gt; 0
            ? &#x60;Successfully added ${addedTags.length} tags to ticket&#x60;
            : &quot;No new tags were added (tags already exist)&quot;,
      };
    } catch (error) {
      return error;
    }
  }

  async removeTagFromTicket(
    ticketUuid: string,
    tagUuid: string,
  ): Promise&lt;DeleteTicketTagResponse&gt; {
    try {
      // Input validation
      this.validationService.validateUlids([
        { id: ticketUuid, fieldName: &quot;Ticket ID&quot; },
        { id: tagUuid, fieldName: &quot;Tag ID&quot; },
      ]);

      const tickets &#x3D; await this.ticketRepository.findWithRelations({
        where: { uid: ticketUuid },
        relations: [&quot;tags&quot;, &quot;team&quot;], // Include team relation
      });

      if (!tickets) {
        throw new NotFoundException(&#x60;Ticket with ID &quot;${ticketUuid}&quot; not found&#x60;);
      }
      const ticket &#x3D; tickets[0];

      // Check if tag exists in ticket
      const hadTag &#x3D; ticket.tags.some((tag) &#x3D;&gt; tag.uid &#x3D;&#x3D;&#x3D; tagUuid);
      if (!hadTag) {
        throw new NotFoundException(
          &#x60;Tag with ID &quot;${tagUuid}&quot; not found in ticket &quot;${ticketUuid}&quot;&#x60;,
        );
      }

      ticket.tags &#x3D; ticket.tags.filter((tag) &#x3D;&gt; tag.uid !&#x3D;&#x3D; tagUuid);
      await this.ticketRepository.save(ticket);
      return {
        status: true,
        timestamp: new Date(),
        ticketId: ticketUuid,
        removedTagId: tagUuid,
        message: &#x60;Successfully removed tag from ticket&#x60;,
      };
    } catch (error) {
      return error;
    }
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'TicketTagService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
