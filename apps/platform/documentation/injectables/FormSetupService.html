<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >FormSetupService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/forms/form-setup.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#createForm" >createForm</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#createRandomCustomFields" >createRandomCustomFields</a>
                            </li>
                            <li>
                                <a href="#onModuleInit" >onModuleInit</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#run" >run</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(formService: <a href="../injectables/FormService.html" target="_self">FormService</a>, customFieldService: <a href="../injectables/CustomFieldService.html" target="_self">CustomFieldService</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="132" class="link-to-prism">src/forms/form-setup.ts:132</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>formService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/FormService.html" target="_self" >FormService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>customFieldService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/CustomFieldService.html" target="_self" >CustomFieldService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createForm"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>createForm</b></span>
                        <a href="#createForm"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createForm(orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="190"
                                    class="link-to-prism">src/forms/form-setup.ts:190</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>userId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createRandomCustomFields"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>createRandomCustomFields</b></span>
                        <a href="#createRandomCustomFields"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createRandomCustomFields(orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="153"
                                    class="link-to-prism">src/forms/form-setup.ts:153</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>userId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="onModuleInit"></a>
                    <span class="name">
                        <span ><b>onModuleInit</b></span>
                        <a href="#onModuleInit"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>onModuleInit()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="137"
                                    class="link-to-prism">src/forms/form-setup.ts:137</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="run"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>run</b></span>
                        <a href="#run"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>run()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="141"
                                    class="link-to-prism">src/forms/form-setup.ts:141</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Injectable, OnModuleInit } from &quot;@nestjs/common&quot;;
import {
  CustomFieldSource,
  CustomFieldType,
  FormFieldTriggers,
  FormFieldType,
  TargetFieldConditionsType,
} from &quot;@repo/thena-platform-entities&quot;;
import { CreateCustomFieldDto } from &quot;src/custom-field/dto&quot;;
import { CustomFieldService } from &quot;../custom-field/services/custom-field.service&quot;; // Adjust the import path as necessary
import { CreateFormDto } from &quot;./dto/form.dto&quot;;
import { FormService } from &quot;./services/form.service&quot;; // Adjust the import path as necessary

const phoneNoFieldBody: CreateCustomFieldDto &#x3D; {
  name: &quot;Phone Number field&quot;,
  fieldType: CustomFieldType.PHONE_NUMBER,
  source: CustomFieldSource.TICKET,
  placeholderText: &quot;Enter your phone number&quot;,
  hintText: &quot;Enter your phone number&quot;,
  mandatoryOnCreation: false,
  mandatoryOnClose: false,
  visibleToCustomer: false,
  editableByCustomer: false,
  autoAddToAllForms: false,
  defaultValue: null,
};

const singleChoiceFieldBody: CreateCustomFieldDto &#x3D; {
  name: &quot;Single Choice field&quot;,
  fieldType: CustomFieldType.SINGLE_CHOICE,
  source: CustomFieldSource.TICKET,
  options: [
    {
      value: &quot;option-1&quot;,
      is_disabled: false,
      order: 1,
      id: &quot;1&quot;,
      platformField: true,
    },
    {
      value: &quot;option-2&quot;,
      is_disabled: false,
      order: 2,
      id: &quot;2&quot;,
      platformField: true,
    },
    {
      value: &quot;option-3&quot;,
      is_disabled: false,
      order: 3,
      id: &quot;3&quot;,
      platformField: true,
    },
  ],
  placeholderText: &quot;Select an option&quot;,
  hintText: &quot;Select an option&quot;,
  mandatoryOnCreation: false,
  mandatoryOnClose: false,
  visibleToCustomer: false,
  editableByCustomer: false,
  autoAddToAllForms: false,
  defaultValue: null,
};

const multiChoiceFieldBody: CreateCustomFieldDto &#x3D; {
  name: &quot;Multi Choice field&quot;,
  fieldType: CustomFieldType.MULTI_CHOICE,
  source: CustomFieldSource.TICKET,
  options: [
    {
      value: &quot;option-1&quot;,
      is_disabled: false,
      order: 1,
      id: &quot;1&quot;,
      platformField: true,
    },
    {
      value: &quot;option-2&quot;,
      is_disabled: false,
      order: 2,
      id: &quot;2&quot;,
      platformField: true,
    },
    {
      value: &quot;option-3&quot;,
      is_disabled: false,
      order: 3,
      id: &quot;3&quot;,
      platformField: true,
    },
    {
      value: &quot;option-4&quot;,
      is_disabled: false,
      order: 4,
      id: &quot;4&quot;,
      platformField: true,
    },
  ],
  placeholderText: &quot;Select multiple options&quot;,
  hintText: &quot;Select multiple options&quot;,
  mandatoryOnCreation: false,
  mandatoryOnClose: false,
  visibleToCustomer: false,
  editableByCustomer: false,
  autoAddToAllForms: false,
  defaultValue: null,
};

const createdCustomFields &#x3D; [];

const simpleTextFieldBody: CreateCustomFieldDto &#x3D; {
  name: &quot;Simple Text field&quot;,
  fieldType: CustomFieldType.SINGLE_LINE,
  source: CustomFieldSource.TICKET,
  placeholderText: &quot;Enter your text&quot;,
  hintText: &quot;Enter your text&quot;,
  mandatoryOnCreation: false,
  mandatoryOnClose: false,
  visibleToCustomer: false,
  editableByCustomer: false,
  autoAddToAllForms: false,
  defaultValue: null,
};

let phoneNoField;
let singleChoiceField;
let multiChoiceField;
let simpleTextField;

@Injectable()
export class FormSetupService implements OnModuleInit {
  constructor(
    private readonly formService: FormService,
    private readonly customFieldService: CustomFieldService,
  ) {}
  onModuleInit() {
    return;
  }

  async run() {
    await this.createRandomCustomFields(
      global?.testOrganization?.id,
      global?.testUser?.uid,
    );
    const form &#x3D; await this.createForm(
      global?.testOrganization?.id,
      global?.testUser?.id,
    );
    return form;
  }

  private async createRandomCustomFields(orgId: string, userId: string) {
    if (!orgId || !userId) {
      return;
    }
    const customFields: CreateCustomFieldDto[] &#x3D; [
      phoneNoFieldBody,
      singleChoiceFieldBody,
      multiChoiceFieldBody,
      simpleTextFieldBody,
    ];

    for (const field of customFields) {
      const createdField &#x3D; await this.customFieldService.create(
        orgId,
        userId,
        field,
      );

      switch (field.name) {
        case &quot;Phone Number field&quot;:
          phoneNoField &#x3D; createdField;
          break;
        case &quot;Single Choice field&quot;:
          singleChoiceField &#x3D; createdField;
          break;
        case &quot;Multi Choice field&quot;:
          multiChoiceField &#x3D; createdField;
          break;
        case &quot;Simple Text field&quot;:
          simpleTextField &#x3D; createdField;
          break;
      }

      createdCustomFields.push(createdField);
    }
  }

  private async createForm(orgId: string, userId: string) {
    if (!orgId || !userId) {
      return;
    }
    const form: CreateFormDto &#x3D; {
      name: &quot;Default Form&quot;,
      fields: [],
      conditions: [],
    };

    const teamFormMeta: CreateFormDto &#x3D; {
      name: &quot;Team Form&quot;,
      teamId: global.testTeam2.id,
    };

    for (const field of createdCustomFields) {
      form.fields.push({
        field: field.uid,
        defaultValue: null,
        fieldType: FormFieldType.CUSTOM,
        mandatoryOnCreation: field.mandatoryOnCreation,
        mandatoryOnClose: field.mandatoryOnClose,
        visibleToCustomer: field.visibleToCustomer,
        editableByCustomer: field.editableByCustomer,
      });
    }

    form.conditions.push({
      triggerFieldId: phoneNoField.uid,
      triggerFieldValue: [&quot;1234567890&quot;],
      conditionType: TargetFieldConditionsType.EQUALS,
      targetFields: [
        {
          id: singleChoiceField.uid,
          type: FormFieldTriggers.FILL_VALUE,
          value: [&quot;option-1&quot;],
        },
      ],
    });

    form.conditions.push({
      triggerFieldId: simpleTextField.uid,
      triggerFieldValue: [&quot;abc&quot;],
      conditionType: TargetFieldConditionsType.EQUALS,
      targetFields: [
        {
          id: multiChoiceField.uid,
          type: FormFieldTriggers.MARK_MANDATORY,
        },
      ],
    });

    const createdForm &#x3D; await this.formService.create(userId, orgId, form);
    const teamForm &#x3D; await this.formService.create(userId, orgId, teamFormMeta);
    return {
      createdForm,
      teamForm,
      fieldNameToIdMap: {
        &quot;Phone Number field&quot;: phoneNoField.uid,
        &quot;Single Choice field&quot;: singleChoiceField.uid,
        &quot;Multi Choice field&quot;: multiChoiceField.uid,
        &quot;Simple Text field&quot;: simpleTextField.uid,
      },
    };
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'FormSetupService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
