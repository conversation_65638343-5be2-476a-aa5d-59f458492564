<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >TicketStatusActionService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/tickets/services/ticket-status.action.service.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createTicketStatus" >createTicketStatus</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#deleteTicketStatus" >deleteTicketStatus</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#extractUserFromRequest" >extractUserFromRequest</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findAllTicketStatuses" >findAllTicketStatuses</a>
                            </li>
                            <li>
                                <a href="#findClosestMatchLevenshtein" >findClosestMatchLevenshtein</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#findDuplicateDefaultStatus" >findDuplicateDefaultStatus</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findTicketStatusById" >findTicketStatusById</a>
                            </li>
                            <li>
                                <a href="#findTicketStatusByPublicId" >findTicketStatusByPublicId</a>
                            </li>
                            <li>
                                <a href="#findTicketStatusesByPublicIds" >findTicketStatusesByPublicIds</a>
                            </li>
                            <li>
                                <a href="#internalGetDefaultTicketStatus" >internalGetDefaultTicketStatus</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateTicketStatus" >updateTicketStatus</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#validateAndFetchTeam" >validateAndFetchTeam</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(logger: ILogger, cachedTicketStatusRepository: CachedTicketStatusRepository, ticketStatusRepository: TicketStatusRepository, ticketRepository: TicketRepository, usersService: <a href="../injectables/UsersService.html" target="_self">UsersService</a>, teamsService: <a href="../injectables/TeamsService.html" target="_self">TeamsService</a>, transactionService: TransactionService)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="27" class="link-to-prism">src/tickets/services/ticket-status.action.service.ts:27</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>logger</td>
                                                  
                                                        <td>
                                                                    <code>ILogger</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>cachedTicketStatusRepository</td>
                                                  
                                                        <td>
                                                                    <code>CachedTicketStatusRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>ticketStatusRepository</td>
                                                  
                                                        <td>
                                                                    <code>TicketStatusRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>ticketRepository</td>
                                                  
                                                        <td>
                                                                    <code>TicketRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>usersService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/UsersService.html" target="_self" >UsersService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>teamsService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/TeamsService.html" target="_self" >TeamsService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>transactionService</td>
                                                  
                                                        <td>
                                                                    <code>TransactionService</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createTicketStatus"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>createTicketStatus</b></span>
                        <a href="#createTicketStatus"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createTicketStatus(createTicketStatusDto: <a href="../classes/CreateTicketStatusDto.html" target="_self">CreateTicketStatusDto</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="231"
                                    class="link-to-prism">src/tickets/services/ticket-status.action.service.ts:231</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Creates a new ticket status.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>createTicketStatusDto</td>
                                            <td>
                                                            <code><a href="../classes/CreateTicketStatusDto.html" target="_self" >CreateTicketStatusDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ticket status data to create.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The created ticket status.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteTicketStatus"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>deleteTicketStatus</b></span>
                        <a href="#deleteTicketStatus"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteTicketStatus(ticketStatusId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, fallbackStatusId?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="490"
                                    class="link-to-prism">src/tickets/services/ticket-status.action.service.ts:490</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Deletes a ticket status.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>ticketStatusId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the ticket status to delete.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>fallbackStatusId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                                    <p>The ID of the fallback status to map the tickets to.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                <p>The deleted ticket status.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="extractUserFromRequest"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>extractUserFromRequest</b></span>
                        <a href="#extractUserFromRequest"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>extractUserFromRequest(request: <a href="../interfaces/FastifyRequest.html" target="_self">FastifyRequest</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="50"
                                    class="link-to-prism">src/tickets/services/ticket-status.action.service.ts:50</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Extracts the user from the request.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../interfaces/FastifyRequest.html" target="_self" >FastifyRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;User&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The user.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAllTicketStatuses"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findAllTicketStatuses</b></span>
                        <a href="#findAllTicketStatuses"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAllTicketStatuses(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, teamId?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="177"
                                    class="link-to-prism">src/tickets/services/ticket-status.action.service.ts:177</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds all ticket statuses for the organization.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>All ticket statuses for the organization.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findClosestMatchLevenshtein"></a>
                    <span class="name">
                        <span ><b>findClosestMatchLevenshtein</b></span>
                        <a href="#findClosestMatchLevenshtein"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findClosestMatchLevenshtein(name: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, teamId?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="112"
                                    class="link-to-prism">src/tickets/services/ticket-status.action.service.ts:112</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds the closest match for a ticket status name using levenshtein distance.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>name</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The name of the ticket status to match against.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                                    <p>The ID of the team.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                <p>The closest match for the ticket status name.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findDuplicateDefaultStatus"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>findDuplicateDefaultStatus</b></span>
                        <a href="#findDuplicateDefaultStatus"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findDuplicateDefaultStatus(orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, parentStatusId?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="89"
                                    class="link-to-prism">src/tickets/services/ticket-status.action.service.ts:89</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds a duplicate default status.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the team.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>parentStatusId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                <p>The duplicate default status.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findTicketStatusById"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findTicketStatusById</b></span>
                        <a href="#findTicketStatusById"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findTicketStatusById(ticketStatusId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="209"
                                    class="link-to-prism">src/tickets/services/ticket-status.action.service.ts:209</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds a ticket status by its ID.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>ticketStatusId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the ticket status to find.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The user object.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The ticket status.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findTicketStatusByPublicId"></a>
                    <span class="name">
                        <span ><b>findTicketStatusByPublicId</b></span>
                        <a href="#findTicketStatusByPublicId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findTicketStatusByPublicId(statusId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, teamId?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="158"
                                    class="link-to-prism">src/tickets/services/ticket-status.action.service.ts:158</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Gets a ticket status by its public ID.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>statusId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The public ID of the ticket status.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                                    <p>The ID of the team.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                <p>The internal ticket status.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findTicketStatusesByPublicIds"></a>
                    <span class="name">
                        <span ><b>findTicketStatusesByPublicIds</b></span>
                        <a href="#findTicketStatusesByPublicIds"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findTicketStatusesByPublicIds(statusIds: string[], organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="120"
                                    class="link-to-prism">src/tickets/services/ticket-status.action.service.ts:120</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>statusIds</td>
                                            <td>
                                                        <code>string[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="internalGetDefaultTicketStatus"></a>
                    <span class="name">
                        <span ><b>internalGetDefaultTicketStatus</b></span>
                        <a href="#internalGetDefaultTicketStatus"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>internalGetDefaultTicketStatus(orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, parentStatusId?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="134"
                                    class="link-to-prism">src/tickets/services/ticket-status.action.service.ts:134</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Gets a default ticket status.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the team.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>parentStatusId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                                    <p>The ID of the parent status.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                <p>The internal default ticket status.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateTicketStatus"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>updateTicketStatus</b></span>
                        <a href="#updateTicketStatus"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateTicketStatus(ticketStatusId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, updateTicketStatusDto: <a href="../classes/UpdateTicketStatusDto.html" target="_self">UpdateTicketStatusDto</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="385"
                                    class="link-to-prism">src/tickets/services/ticket-status.action.service.ts:385</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Updates a ticket status.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>ticketStatusId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the ticket status to update.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>updateTicketStatusDto</td>
                                            <td>
                                                            <code><a href="../classes/UpdateTicketStatusDto.html" target="_self" >UpdateTicketStatusDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ticket status data to update.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The updated ticket status.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateAndFetchTeam"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>validateAndFetchTeam</b></span>
                        <a href="#validateAndFetchTeam"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateAndFetchTeam(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="70"
                                    class="link-to-prism">src/tickets/services/ticket-status.action.service.ts:70</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Validates and fetches a team by its ID.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the team to fetch.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The team.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  BadRequestException,
  Inject,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from &quot;@nestjs/common&quot;;
import { CUSTOM_LOGGER_TOKEN, ILogger } from &quot;@repo/nestjs-commons/logger&quot;;
import {
  CachedTicketStatusRepository,
  TicketRepository,
  TicketStatus,
  TicketStatusRepository,
  TransactionService,
  User,
} from &quot;@repo/thena-platform-entities&quot;;
import { FastifyRequest } from &quot;fastify&quot;;
import { FindManyOptions, In, IsNull, Not } from &quot;typeorm&quot;;
import { CurrentUser } from &quot;../../common/decorators&quot;;
import { TeamsService } from &quot;../../teams/services/teams.service&quot;;
import { UsersService } from &quot;../../users/services/users.service&quot;;
import {
  CreateTicketStatusDto,
  UpdateTicketStatusDto,
} from &quot;../dto/ticket-status.dto&quot;;
@Injectable()
export class TicketStatusActionService {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN)
    private readonly logger: ILogger,

    // Injected cached repositories
    private readonly cachedTicketStatusRepository: CachedTicketStatusRepository,

    // Injected repositories
    private readonly ticketStatusRepository: TicketStatusRepository,
    private readonly ticketRepository: TicketRepository,

    // Injected services
    private readonly usersService: UsersService,
    private readonly teamsService: TeamsService,
    private readonly transactionService: TransactionService,
  ) {}

  /**
   * @private
   * Extracts the user from the request.
   * @returns The user.
   */
  private async extractUserFromRequest(request: FastifyRequest): Promise&lt;User&gt; {
    const userEmail &#x3D; request.user.email;
    if (!userEmail) {
      throw new UnauthorizedException(&quot;User is not authenticated!&quot;);
    }

    const user &#x3D; await this.usersService.findOneByEmail(userEmail);
    if (!user) {
      throw new UnauthorizedException(&quot;User is not authenticated!&quot;);
    }

    return user;
  }

  /**
   * @private
   * Validates and fetches a team by its ID.
   * @param teamId The ID of the team to fetch.
   * @returns The team.
   */
  private async validateAndFetchTeam(teamId: string, organizationId: string) {
    const team &#x3D; await this.teamsService.findOneByTeamId(
      teamId,
      organizationId,
    );
    if (!team) {
      throw new NotFoundException(&quot;Team not found!&quot;);
    }

    return team;
  }

  /**
   * @private
   * Finds a duplicate default status.
   * @param orgId The ID of the organization.
   * @param teamId The ID of the team.
   * @returns The duplicate default status.
   */
  private findDuplicateDefaultStatus(
    orgId: string,
    teamId: string,
    parentStatusId?: string,
  ) {
    return this.ticketStatusRepository.findByCondition({
      where: {
        isDefault: true,
        organizationId: orgId,
        teamId: teamId,
        parentStatusId: parentStatusId ? parentStatusId : IsNull(),
        deletedAt: IsNull(),
      },
    });
  }

  /**
   * Finds the closest match for a ticket status name using levenshtein distance.
   * @param name The name of the ticket status to match against.
   * @param orgId The ID of the organization.
   * @param teamId The ID of the team.
   * @returns The closest match for the ticket status name.
   */
  findClosestMatchLevenshtein(name: string, orgId: string, teamId?: string) {
    return this.ticketStatusRepository.findClosestMatchLevenshtein(
      name,
      orgId,
      teamId,
    );
  }

  findTicketStatusesByPublicIds(statusIds: string[], organizationId: string) {
    return this.ticketStatusRepository.findAll({
      where: { uid: In(statusIds), organizationId },
    });
  }

  /**
   * @internal
   * Gets a default ticket status.
   * @param orgId The ID of the organization.
   * @param teamId The ID of the team.
   * @param parentStatusId The ID of the parent status.
   * @returns The internal default ticket status.
   */
  internalGetDefaultTicketStatus(
    orgId: string,
    teamId: string,
    parentStatusId?: string,
  ) {
    // If a parent status ID is provided, find the default status for the parent status
    return this.ticketStatusRepository.findByCondition({
      where: {
        isDefault: true,
        organizationId: orgId,
        teamId,
        parentStatusId: parentStatusId ? parentStatusId : IsNull(),
      },
    });
  }

  /**
   * @internal
   * Gets a ticket status by its public ID.
   * @param statusId The public ID of the ticket status.
   * @param orgId The ID of the organization.
   * @param teamId The ID of the team.
   * @returns The internal ticket status.
   */
  findTicketStatusByPublicId(statusId: string, orgId: string, teamId?: string) {
    const query: FindManyOptions&lt;TicketStatus&gt; &#x3D; {
      where: {
        uid: statusId,
        organizationId: orgId,
      },
    };

    if (teamId) {
      query.where &#x3D; { ...query.where, teamId };
    }

    return this.ticketStatusRepository.findByCondition(query);
  }

  /**
   * Finds all ticket statuses for the organization.
   * @returns All ticket statuses for the organization.
   */
  async findAllTicketStatuses(user: CurrentUser, teamId?: string) {
    // Get the user from the request
    const whereClause: Record&lt;string, string&gt; &#x3D; {
      organizationId: user.orgId,
    };

    // If the team ID is provided, filter by team ID
    if (teamId?.trim()) {
      const team &#x3D; await this.validateAndFetchTeam(teamId, user.orgId);
      whereClause.teamId &#x3D; team.id;

      // If the team has a parent team then we&#x27;ll use the parent team&#x27;s statuses
      if (team.parentTeamId) {
        whereClause.teamId &#x3D; team.parentTeamId;
      }
    }

    // Find all ticket statuses for the organization
    const ticketStatuses &#x3D; await this.ticketStatusRepository.findAll({
      where: whereClause,
      relations: [&quot;team&quot;, &quot;organization&quot;, &quot;parentStatus&quot;],
    });

    return ticketStatuses;
  }

  /**
   * Finds a ticket status by its ID.
   * @param ticketStatusId The ID of the ticket status to find.
   * @param user The user object.
   * @returns The ticket status.
   */
  async findTicketStatusById(ticketStatusId: string, user: CurrentUser) {
    // Get the organization ID from the user object attached to the request
    const orgId &#x3D; user.orgId;

    const ticketStatus &#x3D;
      await this.cachedTicketStatusRepository.findByCondition({
        where: { uid: ticketStatusId, organizationId: orgId },
        relations: [&quot;team&quot;, &quot;organization&quot;, &quot;parentStatus&quot;],
      });

    if (!ticketStatus) {
      throw new NotFoundException(&quot;Ticket status not found!&quot;);
    }

    return ticketStatus;
  }

  /**
   * Creates a new ticket status.
   * @param createTicketStatusDto The ticket status data to create.
   * @returns The created ticket status.
   */
  async createTicketStatus(
    createTicketStatusDto: CreateTicketStatusDto,
    user: CurrentUser,
  ) {
    // Get team from the request
    const team &#x3D; await this.validateAndFetchTeam(
      createTicketStatusDto.teamId,
      user.orgId,
    );

    // If the team has a parent team then we&#x27;ll throw since child teams cannot have custom statuses
    if (team.parentTeamId) {
      throw new BadRequestException(
        &quot;You cannot create statuses for a sub-teams!&quot;,
      );
    }

    // Find the parent status
    const parentStatus &#x3D;
      await this.cachedTicketStatusRepository.findByCondition({
        where: {
          teamId: team.id,
          organizationId: user.orgId,
          uid: createTicketStatusDto.parentStatusId,
          parentStatusId: IsNull(),
        },
      });

    // If the parent status is not found, throw an error
    if (!parentStatus) {
      throw new NotFoundException(&quot;Parent status not found!&quot;);
    }

    // If the status is default, check if there is already a default status for the team
    let existingDefaultStatus: TicketStatus | null &#x3D; null;
    if (createTicketStatusDto.isDefault) {
      existingDefaultStatus &#x3D; await this.findDuplicateDefaultStatus(
        user.orgId,
        team.id,
        parentStatus.id,
      );
    }

    // Get the organization ID from the user object
    const orgId &#x3D; user.orgId;

    // Create ticket status and move tickets if applicable in a txn
    const ticketStatus &#x3D; await this.transactionService.runInTransaction(
      async (txnContext) &#x3D;&gt; {
        // Check if there are any sub-statuses for the parent status
        const subStatuses &#x3D; await this.ticketStatusRepository.findAll({
          where: {
            parentStatusId: parentStatus.id,
            organizationId: orgId,
            teamId: team.id,
          },
          take: 1,
        });

        // If there are no sub-statuses for the parent status, then this is the first sub-status
        const isFirstSubStatus &#x3D; subStatuses.length &#x3D;&#x3D;&#x3D; 0;

        // If the status is not the first sub-status and the status is default, then we need to update the default status
        if (!isFirstSubStatus &amp;&amp; createTicketStatusDto.isDefault) {
          // If the existing default status is not the same as the new status, then update the default status
          await this.ticketStatusRepository.updateWithTxn(
            txnContext,
            { uid: existingDefaultStatus.uid, organizationId: orgId },
            { isDefault: false },
          );
        }

        // Create the ticket status
        const newTicketStatus &#x3D; this.ticketStatusRepository.create({
          name: createTicketStatusDto.name,
          displayName:
            createTicketStatusDto.displayName ?? createTicketStatusDto.name,
          isDefault: createTicketStatusDto.isDefault || isFirstSubStatus,
          description: createTicketStatusDto.description,
          organizationId: orgId,
          parentStatusId: parentStatus.id,
          teamId: team.id,
        });

        // Save the new ticket status
        const savedTicketStatus &#x3D; await this.ticketStatusRepository.saveWithTxn(
          txnContext,
          newTicketStatus,
        );

        // Move tickets if requested
        const shouldMoveTickets &#x3D; createTicketStatusDto.moveParentTickets;
        if (shouldMoveTickets &amp;&amp; parentStatus.id) {
          const ticketsToMove &#x3D; await this.ticketRepository.findAll({
            where: {
              statusId: parentStatus.id,
              organizationId: orgId,
              teamId: team.id,
            },
            select: [&quot;id&quot;],
          });

          // Update the status of the tickets
          await this.ticketRepository.updateWithTxn(
            txnContext,
            {
              id: In(ticketsToMove.map((ticket) &#x3D;&gt; ticket.id)),
              organizationId: orgId,
              teamId: team.id,
            },
            { statusId: savedTicketStatus.id },
          );
        }

        // Invalidate the query for the existing default status
        if (existingDefaultStatus) {
          await Promise.all([
            // Invalidate the query for the existing default status
            this.cachedTicketStatusRepository.invalidateQuery({
              where: {
                uid: existingDefaultStatus.uid,
                organizationId: orgId,
              },
            }),

            // Invalidate the query with relations for the existing default status
            this.cachedTicketStatusRepository.invalidateQuery({
              where: { uid: existingDefaultStatus.uid, organizationId: orgId },
              relations: [&quot;parentStatus&quot;, &quot;team&quot;, &quot;organization&quot;],
            }),
          ]);
        }

        // Return the newly created status
        return savedTicketStatus;
      },
    );

    // Return the newly created status
    const returnableTicketStatus &#x3D;
      await this.cachedTicketStatusRepository.findByCondition({
        where: { teamId: team.id, organizationId: orgId, id: ticketStatus.id },
        relations: [&quot;parentStatus&quot;, &quot;team&quot;, &quot;organization&quot;],
      });

    return returnableTicketStatus;
  }

  /**
   * Updates a ticket status.
   * @param ticketStatusId The ID of the ticket status to update.
   * @param updateTicketStatusDto The ticket status data to update.
   * @returns The updated ticket status.
   */
  async updateTicketStatus(
    ticketStatusId: string,
    updateTicketStatusDto: UpdateTicketStatusDto,
    user: CurrentUser,
  ) {
    // Get the user email from the request
    const orgId &#x3D; user.orgId;

    // Find the ticket status by its ID
    const ticketStatus &#x3D;
      await this.cachedTicketStatusRepository.findByCondition({
        where: { uid: ticketStatusId, organizationId: orgId },
      });

    // If the ticket status is not found, throw an error
    if (!ticketStatus) {
      throw new NotFoundException(&quot;Ticket status not found!&quot;);
    }

    // If the ticket status is a parent status, throw an error
    if (!ticketStatus.parentStatusId) {
      throw new BadRequestException(
        &quot;Cannot update a parent status, if you wanna modify the name please create a sub-status.&quot;,
      );
    }

    // If the status is default, check if there is already a default status for the team
    let existingDefaultStatus: TicketStatus | null &#x3D; null;
    if (updateTicketStatusDto.isDefault) {
      existingDefaultStatus &#x3D; await this.findDuplicateDefaultStatus(
        orgId,
        ticketStatus.teamId,
        ticketStatus.parentStatusId,
      );
    }

    // Update the ticket status in a transaction
    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      // If the status is default, check if there is already a default status for the team
      if (updateTicketStatusDto.isDefault &amp;&amp; existingDefaultStatus) {
        await this.ticketStatusRepository.updateWithTxn(
          txnContext,
          { id: existingDefaultStatus.id },
          { isDefault: false },
        );
      }

      // Update the ticket status
      await this.ticketStatusRepository.updateWithTxn(
        txnContext,
        { id: ticketStatus.id },
        {
          name: updateTicketStatusDto.name,
          displayName: updateTicketStatusDto.displayName,
          description: updateTicketStatusDto.description,
          isDefault: updateTicketStatusDto.isDefault,
        },
      );

      // Invalidate the query
      const invalidateQueries &#x3D; [
        // Invalidate the updated ticket status
        this.cachedTicketStatusRepository.invalidateQuery({
          where: { uid: ticketStatus.uid, organizationId: orgId },
        }),

        // Invalidate the updated ticket status with relations
        this.cachedTicketStatusRepository.invalidateQuery({
          where: { uid: ticketStatus.uid, organizationId: orgId },
          relations: [&quot;team&quot;, &quot;organization&quot;, &quot;parentStatus&quot;],
        }),
      ];

      // If the existing default status is found, invalidate the existing default status
      if (existingDefaultStatus) {
        invalidateQueries.push(
          this.cachedTicketStatusRepository.invalidateQuery({
            where: {
              uid: existingDefaultStatus.uid,
              organizationId: orgId,
            },
          }),
        );
      }

      // Invalidate the queries
      await Promise.all(invalidateQueries);
    });

    // Return the updated ticket status
    const returnableTicketStatus &#x3D;
      await this.cachedTicketStatusRepository.findByCondition({
        where: { uid: ticketStatus.uid, organizationId: orgId },
        relations: [&quot;team&quot;, &quot;organization&quot;, &quot;parentStatus&quot;],
      });

    return returnableTicketStatus;
  }

  /**
   * Deletes a ticket status.
   * @param ticketStatusId The ID of the ticket status to delete.
   * @param fallbackStatusId The ID of the fallback status to map the tickets to.
   * @returns The deleted ticket status.
   */
  async deleteTicketStatus(
    ticketStatusId: string,
    user: CurrentUser,
    fallbackStatusId?: string,
  ) {
    // Get the user from the request
    const orgId &#x3D; user.orgId;

    let parentStatus: TicketStatus | null &#x3D; null;

    // Find the ticket status by its ID
    const ticketStatus &#x3D;
      await this.cachedTicketStatusRepository.findByCondition({
        where: { uid: ticketStatusId, organizationId: orgId },
        relations: [&quot;parentStatus&quot;],
      });

    // If the ticket status is not found, throw an error
    if (!ticketStatus) {
      throw new NotFoundException(&quot;Ticket status not found!&quot;);
    }

    // If the ticket status is not a parent status, throw an error
    if (!ticketStatus.parentStatusId) {
      throw new BadRequestException(&quot;Cannot delete a parent status!&quot;);
    }

    // Count the number of sub-statuses for the parent status
    const subStatusCount &#x3D; await this.ticketStatusRepository.count({
      where: {
        parentStatusId: ticketStatus.parentStatus.id,
        organizationId: orgId,
      },
    });

    // Checks if the ticket status is a parent status
    if (ticketStatus.parentStatus) {
      parentStatus &#x3D; ticketStatus.parentStatus;

      // If the fallback status ID is not provided and the ticket status is a default status and there are sub-statuses, throw an error
      if (!fallbackStatusId &amp;&amp; ticketStatus.isDefault &amp;&amp; subStatusCount &gt; 1) {
        throw new BadRequestException(
          &quot;Cannot delete a default status, please remap the tickets to a different status!&quot;,
        );
      }
    }

    // If the fallback status ID is provided, find the fallback status
    let fallbackStatus: TicketStatus | null &#x3D; null;
    if (fallbackStatusId) {
      fallbackStatus &#x3D; await this.cachedTicketStatusRepository.findByCondition({
        where: { uid: fallbackStatusId, organizationId: orgId },
      });

      // If the fallback status is not found, throw an error
      if (!fallbackStatus) {
        throw new NotFoundException(&quot;Fallback status not found!&quot;);
      }
    } else {
      fallbackStatus &#x3D; await this.cachedTicketStatusRepository.findByCondition({
        where: {
          parentStatusId: ticketStatus.parentStatusId,
          organizationId: orgId,
          teamId: ticketStatus.teamId,
          isDefault: true,
          deletedAt: IsNull(),
          id: Not(ticketStatus.id),
        },
      });
    }

    // Delete the ticket status and move tickets if applicable in a txn
    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      // Move tickets if applicable
      await this.ticketRepository.updateWithTxn(
        txnContext,
        { statusId: ticketStatus.id, organizationId: orgId },
        { statusId: fallbackStatus ? fallbackStatus.id : parentStatus.id },
      );

      // If the ticket status is a default status, then we need to update the default status
      // and set the fallback status to be the default status
      if (ticketStatus.isDefault &amp;&amp; fallbackStatus) {
        await this.ticketStatusRepository.updateWithTxn(
          txnContext,
          { id: fallbackStatus.id },
          { isDefault: true },
        );
      }

      // Delete the ticket status
      await this.ticketStatusRepository.softDeleteWithTxn(txnContext, {
        id: ticketStatus.id,
        uid: ticketStatus.uid,
        organizationId: orgId,
      });
    });
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'TicketStatusActionService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
