<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >TeamAnnotatorService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/teams/services/team-annotator.service.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getBusinessHoursConfigData" >getBusinessHoursConfigData</a>
                            </li>
                            <li>
                                <a href="#getBusinessHoursConfigFieldMetadata" >getBusinessHoursConfigFieldMetadata</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getTeamCapacityData" >getTeamCapacityData</a>
                            </li>
                            <li>
                                <a href="#getTeamCapacityFieldMetadata" >getTeamCapacityFieldMetadata</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getTeamConfigurationData" >getTeamConfigurationData</a>
                            </li>
                            <li>
                                <a href="#getTeamConfigurationFieldMetadata" >getTeamConfigurationFieldMetadata</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getTeamData" >getTeamData</a>
                            </li>
                            <li>
                                <a href="#getTeamFieldMetadata" >getTeamFieldMetadata</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getTeamMemberData" >getTeamMemberData</a>
                            </li>
                            <li>
                                <a href="#getTeamMemberFieldMetadata" >getTeamMemberFieldMetadata</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(fieldMetadataService: <a href="../injectables/FieldMetadataService.html" target="_self">FieldMetadataService</a>, teamRepository: TeamRepository, teamMemberRepository: TeamMemberRepository, teamConfigurationRepository: TeamConfigurationRepository, teamCapacityRepository: TeamCapacityRepository, businessHoursConfigRepository: BusinessHoursConfigRepository)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="18" class="link-to-prism">src/teams/services/team-annotator.service.ts:18</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>fieldMetadataService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/FieldMetadataService.html" target="_self" >FieldMetadataService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>teamRepository</td>
                                                  
                                                        <td>
                                                                    <code>TeamRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>teamMemberRepository</td>
                                                  
                                                        <td>
                                                                    <code>TeamMemberRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>teamConfigurationRepository</td>
                                                  
                                                        <td>
                                                                    <code>TeamConfigurationRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>teamCapacityRepository</td>
                                                  
                                                        <td>
                                                                    <code>TeamCapacityRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>businessHoursConfigRepository</td>
                                                  
                                                        <td>
                                                                    <code>BusinessHoursConfigRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getBusinessHoursConfigData"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>getBusinessHoursConfigData</b></span>
                        <a href="#getBusinessHoursConfigData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getBusinessHoursConfigData(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, relations: string[], organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="167"
                                    class="link-to-prism">src/teams/services/team-annotator.service.ts:167</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Get business hours config data with relations</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>relations</td>
                                            <td>
                                                        <code>string[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;teams.GetBusinessHoursConfigDataResponse&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getBusinessHoursConfigFieldMetadata"></a>
                    <span class="name">
                        <span ><b>getBusinessHoursConfigFieldMetadata</b></span>
                        <a href="#getBusinessHoursConfigFieldMetadata"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getBusinessHoursConfigFieldMetadata()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="59"
                                    class="link-to-prism">src/teams/services/team-annotator.service.ts:59</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Get field metadata for business hours config</p>
</div>

                        <div class="io-description">
                            <b>Returns : </b>    <code>teams.GetBusinessHoursConfigFieldMetadataResponse</code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTeamCapacityData"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>getTeamCapacityData</b></span>
                        <a href="#getTeamCapacityData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTeamCapacityData(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, relations: string[], organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="141"
                                    class="link-to-prism">src/teams/services/team-annotator.service.ts:141</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Get team capacity data with relations</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>userId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>relations</td>
                                            <td>
                                                        <code>string[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;teams.GetTeamCapacityDataResponse&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTeamCapacityFieldMetadata"></a>
                    <span class="name">
                        <span ><b>getTeamCapacityFieldMetadata</b></span>
                        <a href="#getTeamCapacityFieldMetadata"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getTeamCapacityFieldMetadata()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="52"
                                    class="link-to-prism">src/teams/services/team-annotator.service.ts:52</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Get field metadata for team capacity</p>
</div>

                        <div class="io-description">
                            <b>Returns : </b>    <code>teams.GetTeamCapacityFieldMetadataResponse</code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTeamConfigurationData"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>getTeamConfigurationData</b></span>
                        <a href="#getTeamConfigurationData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTeamConfigurationData(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, relations: string[], organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="115"
                                    class="link-to-prism">src/teams/services/team-annotator.service.ts:115</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Get team configuration data with relations</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>relations</td>
                                            <td>
                                                        <code>string[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;teams.GetTeamConfigurationDataResponse&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTeamConfigurationFieldMetadata"></a>
                    <span class="name">
                        <span ><b>getTeamConfigurationFieldMetadata</b></span>
                        <a href="#getTeamConfigurationFieldMetadata"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getTeamConfigurationFieldMetadata()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="45"
                                    class="link-to-prism">src/teams/services/team-annotator.service.ts:45</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Get field metadata for team configurations</p>
</div>

                        <div class="io-description">
                            <b>Returns : </b>    <code>teams.GetTeamConfigurationFieldMetadataResponse</code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTeamData"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>getTeamData</b></span>
                        <a href="#getTeamData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTeamData(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, relations: string[], organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="66"
                                    class="link-to-prism">src/teams/services/team-annotator.service.ts:66</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Get team data with relations</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>relations</td>
                                            <td>
                                                        <code>string[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;teams.GetTeamDataResponse&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTeamFieldMetadata"></a>
                    <span class="name">
                        <span ><b>getTeamFieldMetadata</b></span>
                        <a href="#getTeamFieldMetadata"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getTeamFieldMetadata()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="31"
                                    class="link-to-prism">src/teams/services/team-annotator.service.ts:31</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Get field metadata for teams</p>
</div>

                        <div class="io-description">
                            <b>Returns : </b>    <code>teams.GetTeamFieldMetadataResponse</code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTeamMemberData"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>getTeamMemberData</b></span>
                        <a href="#getTeamMemberData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTeamMemberData(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, memberId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, relations: string[], organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="88"
                                    class="link-to-prism">src/teams/services/team-annotator.service.ts:88</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Get team member data with relations</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>memberId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>relations</td>
                                            <td>
                                                        <code>string[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;teams.GetTeamMemberDataResponse&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTeamMemberFieldMetadata"></a>
                    <span class="name">
                        <span ><b>getTeamMemberFieldMetadata</b></span>
                        <a href="#getTeamMemberFieldMetadata"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getTeamMemberFieldMetadata()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="38"
                                    class="link-to-prism">src/teams/services/team-annotator.service.ts:38</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Get field metadata for team members</p>
</div>

                        <div class="io-description">
                            <b>Returns : </b>    <code>teams.GetTeamMemberFieldMetadataResponse</code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Injectable, NotFoundException } from &quot;@nestjs/common&quot;;
import { teams } from &quot;@repo/shared-proto&quot;;
import {
  BusinessHoursConfig,
  BusinessHoursConfigRepository,
  Team,
  TeamCapacity,
  TeamCapacityRepository,
  TeamConfiguration,
  TeamConfigurationRepository,
  TeamMember,
  TeamMemberRepository,
  TeamRepository,
} from &quot;@repo/thena-platform-entities&quot;;
import { FieldMetadataService } from &quot;../../common/services/field-metadata.service&quot;;

@Injectable()
export class TeamAnnotatorService {
  constructor(
    private readonly fieldMetadataService: FieldMetadataService,
    private readonly teamRepository: TeamRepository,
    private readonly teamMemberRepository: TeamMemberRepository,
    private readonly teamConfigurationRepository: TeamConfigurationRepository,
    private readonly teamCapacityRepository: TeamCapacityRepository,
    private readonly businessHoursConfigRepository: BusinessHoursConfigRepository,
  ) {}

  /**
   * Get field metadata for teams
   */
  getTeamFieldMetadata(): teams.GetTeamFieldMetadataResponse {
    return this.fieldMetadataService.getFieldMetadata(Team);
  }

  /**
   * Get field metadata for team members
   */
  getTeamMemberFieldMetadata(): teams.GetTeamMemberFieldMetadataResponse {
    return this.fieldMetadataService.getFieldMetadata(TeamMember);
  }

  /**
   * Get field metadata for team configurations
   */
  getTeamConfigurationFieldMetadata(): teams.GetTeamConfigurationFieldMetadataResponse {
    return this.fieldMetadataService.getFieldMetadata(TeamConfiguration);
  }

  /**
   * Get field metadata for team capacity
   */
  getTeamCapacityFieldMetadata(): teams.GetTeamCapacityFieldMetadataResponse {
    return this.fieldMetadataService.getFieldMetadata(TeamCapacity);
  }

  /**
   * Get field metadata for business hours config
   */
  getBusinessHoursConfigFieldMetadata(): teams.GetBusinessHoursConfigFieldMetadataResponse {
    return this.fieldMetadataService.getFieldMetadata(BusinessHoursConfig);
  }

  /**
   * Get team data with relations
   */
  async getTeamData(
    teamId: string,
    relations: string[],
    organizationId: string,
  ): Promise&lt;teams.GetTeamDataResponse&gt; {
    const team &#x3D; await this.teamRepository.findByCondition({
      where: { uid: teamId, organizationId },
      relations,
    });

    if (!team) {
      throw new NotFoundException(&quot;Team not found&quot;);
    }

    return {
      data: JSON.stringify(team),
    };
  }

  /**
   * Get team member data with relations
   */
  async getTeamMemberData(
    teamId: string,
    memberId: string,
    relations: string[],
    organizationId: string,
  ): Promise&lt;teams.GetTeamMemberDataResponse&gt; {
    const teamMember &#x3D; await this.teamMemberRepository.findByCondition({
      where: {
        team: { uid: teamId },
        user: { uid: memberId },
        organizationId,
      },
      relations: [...relations, &quot;team&quot;, &quot;user&quot;],
    });

    if (!teamMember) {
      throw new NotFoundException(&quot;Team member not found&quot;);
    }

    return {
      data: JSON.stringify(teamMember),
    };
  }

  /**
   * Get team configuration data with relations
   */
  async getTeamConfigurationData(
    teamId: string,
    relations: string[],
    organizationId: string,
  ): Promise&lt;teams.GetTeamConfigurationDataResponse&gt; {
    const teamConfiguration &#x3D;
      await this.teamConfigurationRepository.findByCondition({
        where: {
          team: { uid: teamId },
          organizationId,
        },
        relations: [...relations, &quot;team&quot;],
      });

    if (!teamConfiguration) {
      throw new NotFoundException(&quot;Team configuration not found&quot;);
    }

    return {
      data: JSON.stringify(teamConfiguration),
    };
  }

  /**
   * Get team capacity data with relations
   */
  async getTeamCapacityData(
    teamId: string,
    userId: string,
    relations: string[],
    organizationId: string,
  ): Promise&lt;teams.GetTeamCapacityDataResponse&gt; {
    const teamCapacity &#x3D; await this.teamCapacityRepository.findByCondition({
      where: {
        team: { uid: teamId, organizationId },
        user: { uid: userId },
      },
      relations: [...relations, &quot;team&quot;, &quot;user&quot;],
    });

    if (!teamCapacity) {
      throw new NotFoundException(&quot;Team capacity not found&quot;);
    }

    return {
      data: JSON.stringify(teamCapacity),
    };
  }

  /**
   * Get business hours config data with relations
   */
  async getBusinessHoursConfigData(
    teamId: string,
    relations: string[],
    organizationId: string,
  ): Promise&lt;teams.GetBusinessHoursConfigDataResponse&gt; {
    const businessHoursConfig &#x3D;
      await this.businessHoursConfigRepository.findByCondition({
        where: {
          team: { uid: teamId },
          organizationId,
        },
        relations: [...relations, &quot;team&quot;],
      });

    if (!businessHoursConfig) {
      throw new NotFoundException(&quot;Business hours config not found&quot;);
    }

    return {
      data: JSON.stringify(businessHoursConfig),
    };
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'TeamAnnotatorService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
