<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >CustomFieldService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/custom-field/services/custom-field.service.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#create" >create</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#delete" >delete</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#fetchPaginatedResults" >fetchPaginatedResults</a>
                            </li>
                            <li>
                                <a href="#findAllTypes" >findAllTypes</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findByIds" >findByIds</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findByIdsWithTeamCheck" >findByIdsWithTeamCheck</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getDefaultFields" >getDefaultFields</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#search" >search</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#update" >update</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(logger: ILogger, customFieldRepository: CustomFieldRepository, thenaRestrictedFieldRepository: ThenaRestrictedFieldRepository, sharedService: <a href="../injectables/SharedService.html" target="_self">SharedService</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="24" class="link-to-prism">src/custom-field/services/custom-field.service.ts:24</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>logger</td>
                                                  
                                                        <td>
                                                                    <code>ILogger</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>customFieldRepository</td>
                                                  
                                                        <td>
                                                                    <code>CustomFieldRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>thenaRestrictedFieldRepository</td>
                                                  
                                                        <td>
                                                                    <code>ThenaRestrictedFieldRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>sharedService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/SharedService.html" target="_self" >SharedService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="create"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>create</b></span>
                        <a href="#create"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>create(orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, createCustomFieldDto: <a href="../classes/CreateCustomFieldDto.html" target="_self">CreateCustomFieldDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="185"
                                    class="link-to-prism">src/custom-field/services/custom-field.service.ts:185</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Creates a new custom field.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>userId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>createCustomFieldDto</td>
                                            <td>
                                                            <code><a href="../classes/CreateCustomFieldDto.html" target="_self" >CreateCustomFieldDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The custom field data to create.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;CustomField&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The created custom field.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="delete"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>delete</b></span>
                        <a href="#delete"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>delete(request: <a href="../interfaces/FastifyRequest.html" target="_self">FastifyRequest</a>, orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, fields: <a href="../classes/DeleteCustomFieldDto.html" target="_self">DeleteCustomFieldDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="341"
                                    class="link-to-prism">src/custom-field/services/custom-field.service.ts:341</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Delete fields by custom ids.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../interfaces/FastifyRequest.html" target="_self" >FastifyRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>fields</td>
                                            <td>
                                                            <code><a href="../classes/DeleteCustomFieldDto.html" target="_self" >DeleteCustomFieldDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The custom field.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="fetchPaginatedResults"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>fetchPaginatedResults</b></span>
                        <a href="#fetchPaginatedResults"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>fetchPaginatedResults(request: <a href="../interfaces/FastifyRequest.html" target="_self">FastifyRequest</a>, limit: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>, offset: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>, teamId?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, onlyTeamFields?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank">boolean</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="37"
                                    class="link-to-prism">src/custom-field/services/custom-field.service.ts:37</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds all custom fields.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../interfaces/FastifyRequest.html" target="_self" >FastifyRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>limit</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>offset</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>onlyTeamFields</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>All custom fields.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAllTypes"></a>
                    <span class="name">
                        <span ><b>findAllTypes</b></span>
                        <a href="#findAllTypes"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findAllTypes()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="422"
                                    class="link-to-prism">src/custom-field/services/custom-field.service.ts:422</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Returns all available field types and their corresponding subtypes</p>
</div>

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                <p>A record of field types mapped to their possible subtypes</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findByIds"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findByIds</b></span>
                        <a href="#findByIds"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findByIds(orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, ids: string[])</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="78"
                                    class="link-to-prism">src/custom-field/services/custom-field.service.ts:78</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>ids</td>
                                            <td>
                                                        <code>string[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findByIdsWithTeamCheck"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findByIdsWithTeamCheck</b></span>
                        <a href="#findByIdsWithTeamCheck"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findByIdsWithTeamCheck(orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, ids: string[], teamIds?: string[])</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="98"
                                    class="link-to-prism">src/custom-field/services/custom-field.service.ts:98</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds custom fields by ids.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>ids</td>
                                            <td>
                                                        <code>string[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>Array of custom field IDs to fetch</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>teamIds</td>
                                            <td>
                                                        <code>string[]</code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>Custom fields matching the provided IDs</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getDefaultFields"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>getDefaultFields</b></span>
                        <a href="#getDefaultFields"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getDefaultFields(orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, teamId?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="426"
                                    class="link-to-prism">src/custom-field/services/custom-field.service.ts:426</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="search"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>search</b></span>
                        <a href="#search"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>search(request: <a href="../interfaces/FastifyRequest.html" target="_self">FastifyRequest</a>, term: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, teamId?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, onlyTeamFields?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank">boolean</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="145"
                                    class="link-to-prism">src/custom-field/services/custom-field.service.ts:145</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Search custom fields by name.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../interfaces/FastifyRequest.html" target="_self" >FastifyRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The FastifyRequest object containing user info</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>term</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>term to search</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>onlyTeamFields</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>Custom fields matching the provided term</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="update"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>update</b></span>
                        <a href="#update"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>update(request: <a href="../interfaces/FastifyRequest.html" target="_self">FastifyRequest</a>, organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, updates: <a href="../classes/UpdateCustomFieldDto.html" target="_self">UpdateCustomFieldDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="262"
                                    class="link-to-prism">src/custom-field/services/custom-field.service.ts:262</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Updates existing custom fields in batch.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../interfaces/FastifyRequest.html" target="_self" >FastifyRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>updates</td>
                                            <td>
                                                            <code><a href="../classes/UpdateCustomFieldDto.html" target="_self" >UpdateCustomFieldDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>Array of custom field update data.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                <p>The updated custom fields.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { BadRequestException, Inject, Injectable } from &quot;@nestjs/common&quot;;
import { ILogger } from &quot;@repo/nestjs-commons/logger&quot;;
import {
  CustomField,
  CustomFieldRepository,
  CustomFieldSource,
  CustomFieldTypeToNameMapping,
  CustomValidationError,
  FormFieldEventTypes,
  ThenaRestrictedFieldRepository,
} from &quot;@repo/thena-platform-entities&quot;;
import { FastifyRequest } from &quot;fastify&quot;;
import * as _ from &quot;lodash&quot;;
import { ILike, In, IsNull } from &quot;typeorm&quot;;
import { IdGeneratorUtils } from &quot;../../common/utils&quot;;
import { SharedService } from &quot;../../shared/shared.service&quot;;
import {
  CreateCustomFieldDto,
  DeleteCustomFieldDto,
  UpdateCustomFieldDto,
} from &quot;../dto/custom-field.dto&quot;;

@Injectable()
export class CustomFieldService {
  constructor(
    @Inject(&quot;CustomLogger&quot;)
    private readonly logger: ILogger,
    private customFieldRepository: CustomFieldRepository,
    private thenaRestrictedFieldRepository: ThenaRestrictedFieldRepository,
    private sharedService: SharedService,
  ) {}

  /**
   * Finds all custom fields.
   * @returns All custom fields.
   */
  async fetchPaginatedResults(
    request: FastifyRequest,
    limit: number,
    offset: number,
    teamId?: string,
    onlyTeamFields?: boolean,
  ) {
    const query &#x3D; [];
    if (teamId) {
      query.push({
        organizationId: request.user.orgId,
        isDeleted: false,
        teamId: teamId,
      });
    }

    if (!teamId || !onlyTeamFields) {
      query.push({
        organizationId: request.user.orgId,
        isDeleted: false,
        teamId: IsNull(),
      });
    }

    if (query.length &#x3D;&#x3D;&#x3D; 0) {
      throw new BadRequestException(&quot;No fields to fetch&quot;);
    }

    const customFields &#x3D; await this.customFieldRepository.fetchPaginatedResults(
      { limit, page: offset },
      {
        where: query,
      },
    );

    return {
      ...customFields,
      offset: (customFields.results || []).length &gt; 0 ? ++offset : undefined,
    };
  }

  async findByIds(orgId: string, ids: string[]) {
    const customFields &#x3D; await this.customFieldRepository.findAll({
      where: {
        organizationId: orgId,
        uid: In(ids),
        isDeleted: false,
      },
    });

    return {
      items: customFields,
    };
  }

  /**
   * Finds custom fields by ids.
   * @param request The FastifyRequest object containing user info
   * @param ids Array of custom field IDs to fetch
   * @returns Custom fields matching the provided IDs
   */
  async findByIdsWithTeamCheck(
    orgId: string,
    ids: string[],
    teamIds?: string[],
  ) {
    teamIds &#x3D; teamIds.filter(Boolean);
    const query &#x3D; [
      {
        organizationId: orgId,
        uid: In(ids),
        isDeleted: false,
        teamId: IsNull(),
      },
    ];

    if (teamIds?.length &gt; 0) {
      query.push({
        organizationId: orgId,
        uid: In(ids),
        isDeleted: false,
        teamId: In(teamIds),
      });
    }
    const customFields &#x3D; await this.customFieldRepository.findAll({
      where: query,
    });
    if (customFields.length !&#x3D;&#x3D; ids.length) {
      const missingFields &#x3D; ids.filter(
        (id) &#x3D;&gt; !customFields.some((field) &#x3D;&gt; field.uid &#x3D;&#x3D;&#x3D; id),
      );
      throw new BadRequestException(
        &#x60;Some fields not found, either they are deleted or not found in your teams, missing fields: ${missingFields.join(
          &quot;, &quot;,
        )}&#x60;,
      );
    }
    return {
      items: customFields,
    };
  }

  /**
   * Search custom fields by name.
   * @param request The FastifyRequest object containing user info
   * @param term term to search
   * @returns Custom fields matching the provided term
   */
  async search(
    request: FastifyRequest,
    term: string,
    teamId?: string,
    onlyTeamFields?: boolean,
  ) {
    if (_.isEmpty(term?.trim())) {
      throw new BadRequestException(&quot;No search term provided&quot;);
    }
    const query &#x3D; [];
    if (teamId) {
      query.push({
        name: ILike(&#x60;%${term.toLocaleLowerCase()}%&#x60;),
        organizationId: request.user.orgId,
        isDeleted: false,
        teamId: teamId,
      });
    }

    if (!teamId || !onlyTeamFields) {
      query.push({
        name: ILike(&#x60;%${term.toLocaleLowerCase()}%&#x60;),
        organizationId: request.user.orgId,
        isDeleted: false,
        teamId: IsNull(),
      });
    }
    const customFields &#x3D; await this.customFieldRepository.findAll({
      where: query,
    });
    return {
      items: customFields,
    };
  }

  /**
   * Creates a new custom field.
   * @param createCustomFieldDto The custom field data to create.
   * @returns The created custom field.
   */
  async create(
    orgId: string,
    userId: string,
    createCustomFieldDto: CreateCustomFieldDto,
  ): Promise&lt;CustomField&gt; {
    const uid &#x3D; IdGeneratorUtils.generate(&quot;CF&quot;);
    // eslint-disable-next-line max-len
    const customField &#x3D; this.customFieldRepository.create({
      organization: {
        id: orgId,
      },
      organizationId: orgId,
      name: createCustomFieldDto.name,
      source: createCustomFieldDto.source,
      fieldType: createCustomFieldDto.fieldType,
      uid,
      options: createCustomFieldDto.options,
      metadata: createCustomFieldDto.metadata,
      placeholderText: createCustomFieldDto.placeholderText,
      hintText: createCustomFieldDto.hintText,
      mandatoryOnClose: createCustomFieldDto.mandatoryOnClose,
      mandatoryOnCreation: createCustomFieldDto.mandatoryOnCreation,
      visibleToCustomer: createCustomFieldDto.visibleToCustomer,
      editableByCustomer: createCustomFieldDto.editableByCustomer,
      version: 1,
      autoAddToAllForms: createCustomFieldDto.autoAddToAllForms,
      defaultValue: createCustomFieldDto.defaultValue,
      team: createCustomFieldDto.teamId
        ? {
            id: createCustomFieldDto.teamId,
          }
        : undefined,
      teamId: createCustomFieldDto.teamId,
    });

    let res;
    try {
      res &#x3D; await this.customFieldRepository.save(customField);
    } catch (error) {
      if (error instanceof CustomValidationError) {
        throw new BadRequestException(error.message);
      }
      throw error;
    }

    try {
      const formFieldEvents &#x3D; [];
      formFieldEvents.push({
        organization: {
          id: orgId,
        },
        organizationId: orgId,
        entityId: res.uid,
        type: FormFieldEventTypes.FIELD_CREATED,
        data: res,
        user: {
          id: userId,
        },
        userId: userId,
        version: 1,
        createdAt: new Date(),
      });
      await this.sharedService.saveFormFieldEvents(formFieldEvents);
    } catch (error) {
      this.logger.error(
        &#x60;Error while trying to log custom field creation event: ${error.message}, stack: ${error.stack}&#x60;,
      );
      throw error;
    }
    return res;
  }

  /**
   * Updates existing custom fields in batch.
   * @param updates Array of custom field update data.
   * @returns The updated custom fields.
   */
  async update(
    request: FastifyRequest,
    organizationId: string,
    updates: UpdateCustomFieldDto,
  ) {
    if (!updates.fields || updates.fields.length &#x3D;&#x3D;&#x3D; 0) {
      throw new BadRequestException(&quot;No fields to update&quot;);
    }
    const fieldEvents &#x3D; [];

    const promises &#x3D; updates.fields.map(async (update) &#x3D;&gt; {
      const res &#x3D; await this.customFieldRepository.update(
        { organizationId, uid: update.fieldId, version: update.version },
        { ...update.updates, version: update.version + 1 },
      );
      if (res.affected &#x3D;&#x3D;&#x3D; 1) {
        try {
          const field &#x3D; await this.customFieldRepository.findByCondition({
            where: { uid: update.fieldId },
          });
          fieldEvents.push({
            organization: {
              id: organizationId,
            },
            organizationId: organizationId,
            entityId: update.fieldId,
            type: FormFieldEventTypes.FIELD_UPDATED,
            data: field,
            user: {
              id: request.user.sub,
            },
            userId: request.user.sub,
            version: update.version + 1,
            createdAt: new Date(),
          });
        } catch (err) {
          this.logger.error(
            &#x60;Error while trying to add form field event in batch: ${err.message}, stack: ${err.stack}&#x60;,
          );
        }
      } else {
        throw new BadRequestException(
          &#x60;Failed to update field ${update.fieldId} due to version mismatch or concurrent modification&#x60;,
        );
      }
      return res;
    });

    const results &#x3D; await Promise.allSettled(promises);

    if (fieldEvents.length &gt; 0) {
      try {
        await this.sharedService.saveFormFieldEvents(fieldEvents);
      } catch (err) {
        this.logger.error(
          &#x60;Error while trying to log custom field deletion event: ${err.message}, stack: ${err.stack}&#x60;,
        );
      }
    }

    // Collect IDs of forms that failed to update
    const failedUpdates &#x3D; results
      .map((result, index) &#x3D;&gt;
        result.status &#x3D;&#x3D;&#x3D; &quot;rejected&quot; ? updates.fields[index].fieldId : null,
      )
      .filter((id) &#x3D;&gt; id !&#x3D;&#x3D; null);

    if (failedUpdates.length &gt; 0) {
      throw new BadRequestException(
        &#x60;Failed to update the following IDs: ${failedUpdates.join(&quot;, &quot;)}&#x60;,
      );
    }
  }

  /**
   * Delete fields by custom ids.
   * @param deleteCustomFieldDto The custom field list to delete.
   * @returns The custom field.
   */
  async delete(
    request: FastifyRequest,
    orgId: string,
    fields: DeleteCustomFieldDto,
  ) {
    if (_.isEmpty(orgId) || _.isEmpty(fields)) {
      throw new Error(&#x60;Params are missing: org: ${orgId}&#x60;);
    }

    const fieldEvents &#x3D; [];

    const promises &#x3D; fields.fields.map(async (op) &#x3D;&gt; {
      const res &#x3D; await this.customFieldRepository.update(
        { organizationId: orgId, uid: op.fieldId, version: op.version },
        { isDeleted: true, version: op.version + 1 },
      );

      if (res.affected &#x3D;&#x3D;&#x3D; 1) {
        try {
          const field &#x3D; await this.customFieldRepository.findByCondition({
            where: { uid: op.fieldId },
          });
          fieldEvents.push({
            organization: {
              id: orgId,
            },
            organizationId: orgId,
            entityId: op.fieldId,
            type: FormFieldEventTypes.FIELD_DELETED,
            data: field,
            user: {
              id: request.user.sub,
            },
            userId: request.user.sub,
            version: op.version + 1,
            createdAt: new Date(),
          });
        } catch (err) {
          this.logger.error(
            &#x60;Error while trying to add form field event in batch: ${err.message}, stack: ${err.stack}&#x60;,
          );
        }
      } else {
        throw new BadRequestException(
          &#x60;Failed to delete field ${op.fieldId} due to version mismatch or concurrent modification&#x60;,
        );
      }
    });

    const results &#x3D; await Promise.allSettled(promises);

    try {
      await this.sharedService.saveFormFieldEvents(fieldEvents);
    } catch (err) {
      this.logger.error(
        &#x60;Error while trying to log custom field deletion event: ${err.message}, stack: ${err.stack}&#x60;,
      );
    }

    // Collect IDs of fields that failed to update
    const failedUpdates &#x3D; results
      .map((result, index) &#x3D;&gt;
        result.status &#x3D;&#x3D;&#x3D; &quot;rejected&quot; ? fields.fields[index].fieldId : null,
      )
      .filter((id) &#x3D;&gt; id !&#x3D;&#x3D; null);

    if (failedUpdates.length &gt; 0) {
      throw new BadRequestException(
        &#x60;Some fields could not be deleted due to version mismatch or concurrent modification, failed fields: ${failedUpdates.join(
          &quot;, &quot;,
        )}&#x60;,
      );
    }

    return fields.fields;
  }

  /**
   * Returns all available field types and their corresponding subtypes
   * @returns A record of field types mapped to their possible subtypes
   */
  findAllTypes() {
    return Promise.resolve(CustomFieldTypeToNameMapping);
  }

  async getDefaultFields(orgId: string, teamId?: string) {
    const query &#x3D; [
      {
        isDeleted: false,
        organizationId: orgId,
        source: CustomFieldSource.TICKET,
        autoAddToAllForms: true,
        teamId: IsNull(),
      },
    ];
    if (teamId) {
      query.push({
        isDeleted: false,
        organizationId: orgId,
        autoAddToAllForms: true,
        source: CustomFieldSource.TICKET,
        teamId: In([teamId]),
      });
    }
    const fields &#x3D; await this.customFieldRepository.findAll({
      where: query,
    });
    return fields;
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'CustomFieldService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
