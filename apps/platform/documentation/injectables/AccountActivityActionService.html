<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >AccountActivityActionService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/accounts/services/account-activity.action.service.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createAccountActivity" >createAccountActivity</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#deleteAccountActivity" >deleteAccountActivity</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findAccountActivities" >findAccountActivities</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findAccountActivity" >findAccountActivity</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#isActivityStatusAttributeInUse" >isActivityStatusAttributeInUse</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#isActivityTypeAttributeInUse" >isActivityTypeAttributeInUse</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#removeActivityAttachment" >removeActivityAttachment</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateAccountActivity" >updateAccountActivity</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateActivityStatusAttribute" >updateActivityStatusAttribute</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateActivityTypeAttribute" >updateActivityTypeAttribute</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#validateAndGetActivityStatus" >validateAndGetActivityStatus</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#validateAndGetActivityType" >validateAndGetActivityType</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(accountActivityRepository: AccountActivityRepository, cachedAccountActivityRepository: CachedAccountActivityRepository, accountCommonService: <a href="../injectables/AccountCommonService.html" target="_self">AccountCommonService</a>, transactionService: TransactionService, activitiesService: <a href="../injectables/ActivitiesService.html" target="_self">ActivitiesService</a>, usersService: <a href="../injectables/UsersService.html" target="_self">UsersService</a>, storageService: <a href="../injectables/StorageService.html" target="_self">StorageService</a>, accountsEventsFactory: <a href="../injectables/AccountsEventsFactory.html" target="_self">AccountsEventsFactory</a>, accountsSNSEventsFactory: <a href="../classes/AccountsSNSEventsFactory.html" target="_self">AccountsSNSEventsFactory</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="35" class="link-to-prism">src/accounts/services/account-activity.action.service.ts:35</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>accountActivityRepository</td>
                                                  
                                                        <td>
                                                                    <code>AccountActivityRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>cachedAccountActivityRepository</td>
                                                  
                                                        <td>
                                                                    <code>CachedAccountActivityRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>accountCommonService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/AccountCommonService.html" target="_self" >AccountCommonService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>transactionService</td>
                                                  
                                                        <td>
                                                                    <code>TransactionService</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>activitiesService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/ActivitiesService.html" target="_self" >ActivitiesService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>usersService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/UsersService.html" target="_self" >UsersService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>storageService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/StorageService.html" target="_self" >StorageService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>accountsEventsFactory</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/AccountsEventsFactory.html" target="_self" >AccountsEventsFactory</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>accountsSNSEventsFactory</td>
                                                  
                                                        <td>
                                                                        <code><a href="../classes/AccountsSNSEventsFactory.html" target="_self" >AccountsSNSEventsFactory</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createAccountActivity"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>createAccountActivity</b></span>
                        <a href="#createAccountActivity"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createAccountActivity(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, createAccountActivityDto: <a href="../classes/CreateAccountActivityDto.html" target="_self">CreateAccountActivityDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="295"
                                    class="link-to-prism">src/accounts/services/account-activity.action.service.ts:295</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Create a new account activity</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>Current user</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>createAccountActivityDto</td>
                                            <td>
                                                            <code><a href="../classes/CreateAccountActivityDto.html" target="_self" >CreateAccountActivityDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p><a href="../classes/CreateAccountActivityDto.html">CreateAccountActivityDto</a> The DTO containing the account activity details</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;AccountActivity&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The created account activity</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteAccountActivity"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>deleteAccountActivity</b></span>
                        <a href="#deleteAccountActivity"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteAccountActivity(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, activityId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="579"
                                    class="link-to-prism">src/accounts/services/account-activity.action.service.ts:579</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Delete an existing account activity</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>Current user</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>activityId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The Activity ID</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;void&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAccountActivities"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findAccountActivities</b></span>
                        <a href="#findAccountActivities"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAccountActivities(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, findAccountActivityDto: <a href="../classes/FindAccountActivityDto.html" target="_self">FindAccountActivityDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="67"
                                    class="link-to-prism">src/accounts/services/account-activity.action.service.ts:67</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Find all account activities</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>findAccountActivityDto</td>
                                            <td>
                                                            <code><a href="../classes/FindAccountActivityDto.html" target="_self" >FindAccountActivityDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p><a href="../classes/FindAccountActivityDto.html">FindAccountActivityDto</a> The DTO containing the search criteria</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;AccountActivity[]&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The list of account activities</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAccountActivity"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findAccountActivity</b></span>
                        <a href="#findAccountActivity"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAccountActivity(activityId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="131"
                                    class="link-to-prism">src/accounts/services/account-activity.action.service.ts:131</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Find an existing account activity</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>activityId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The Activity ID</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;AccountActivity&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The account activity</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isActivityStatusAttributeInUse"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>isActivityStatusAttributeInUse</b></span>
                        <a href="#isActivityStatusAttributeInUse"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>isActivityStatusAttributeInUse(activityStatusAttributeId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="215"
                                    class="link-to-prism">src/accounts/services/account-activity.action.service.ts:215</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Checks if an activity status attribute is in use.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>activityStatusAttributeId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the activity status attribute.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;boolean&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>Whether the activity status attribute is in use.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isActivityTypeAttributeInUse"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>isActivityTypeAttributeInUse</b></span>
                        <a href="#isActivityTypeAttributeInUse"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>isActivityTypeAttributeInUse(activityTypeAttributeId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="200"
                                    class="link-to-prism">src/accounts/services/account-activity.action.service.ts:200</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Checks if an activity type attribute is in use.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>activityTypeAttributeId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the activity type attribute.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;boolean&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>Whether the activity type attribute is in use.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="removeActivityAttachment"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>removeActivityAttachment</b></span>
                        <a href="#removeActivityAttachment"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>removeActivityAttachment(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, activityId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, attachmentId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="647"
                                    class="link-to-prism">src/accounts/services/account-activity.action.service.ts:647</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Remove an attachment from an activity</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>Current user</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>activityId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The Activity ID</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>attachmentId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The Attachment ID</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;void&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateAccountActivity"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>updateAccountActivity</b></span>
                        <a href="#updateAccountActivity"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateAccountActivity(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, activityId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, updateAccountActivityDto: <a href="../classes/UpdateAccountActivityDto.html" target="_self">UpdateAccountActivityDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="449"
                                    class="link-to-prism">src/accounts/services/account-activity.action.service.ts:449</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Update an existing account activity</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>Current user</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>activityId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The Activity ID</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>updateAccountActivityDto</td>
                                            <td>
                                                            <code><a href="../classes/UpdateAccountActivityDto.html" target="_self" >UpdateAccountActivityDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p><a href="../classes/UpdateAccountActivityDto.html">UpdateAccountActivityDto</a> The DTO containing the account activity details</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;AccountActivity&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The updated account activity</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateActivityStatusAttribute"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>updateActivityStatusAttribute</b></span>
                        <a href="#updateActivityStatusAttribute"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateActivityStatusAttribute(prevActivityStatusAttributeId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, newActivityStatusAttributeId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="256"
                                    class="link-to-prism">src/accounts/services/account-activity.action.service.ts:256</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>prevActivityStatusAttributeId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>newActivityStatusAttributeId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;void&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateActivityTypeAttribute"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>updateActivityTypeAttribute</b></span>
                        <a href="#updateActivityTypeAttribute"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateActivityTypeAttribute(prevActivityTypeAttributeId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, newActivityTypeAttributeId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="224"
                                    class="link-to-prism">src/accounts/services/account-activity.action.service.ts:224</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>prevActivityTypeAttributeId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>newActivityTypeAttributeId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;void&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateAndGetActivityStatus"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>validateAndGetActivityStatus</b></span>
                        <a href="#validateAndGetActivityStatus"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateAndGetActivityStatus(orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, activityStatusUID: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="177"
                                    class="link-to-prism">src/accounts/services/account-activity.action.service.ts:177</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Validate and get an activity status</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The Organization ID</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>activityStatusUID</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The Activity Status UID</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;AccountAttributeValue&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The activity status</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateAndGetActivityType"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>validateAndGetActivityType</b></span>
                        <a href="#validateAndGetActivityType"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateAndGetActivityType(orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, activityType: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="155"
                                    class="link-to-prism">src/accounts/services/account-activity.action.service.ts:155</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Validate and get an activity type</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The Organization ID</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>activityType</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The Activity Type UID / value</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;AccountAttributeValue&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The activity type</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from &quot;@nestjs/common&quot;;
import { AccountEvents } from &quot;@repo/thena-eventbridge&quot;;
import {
  AccountActivity,
  AccountActivityRepository,
  AccountAttributeType,
  AccountAttributeValue,
  AuditLogEntityType,
  AuditLogOp,
  AuditLogVisibility,
  CachedAccountActivityRepository,
  TransactionService,
  User,
} from &quot;@repo/thena-platform-entities&quot;;
import { cloneDeep } from &quot;lodash&quot;;
import { FindOptionsWhere } from &quot;typeorm&quot;;
import { ActivitiesService } from &quot;../../activities/services/activities.service&quot;;
import { CurrentUser } from &quot;../../common/decorators/user.decorator&quot;;
import { StorageService } from &quot;../../storage/services/storage-service&quot;;
import { UsersService } from &quot;../../users/services/users.service&quot;;
import {
  CreateAccountActivityDto,
  FindAccountActivityDto,
  UpdateAccountActivityDto,
} from &quot;../dtos/account-activity.dto&quot;;
import { AccountsEventsFactory } from &quot;../events/accounts-events.factory&quot;;
import { AccountsSNSEventsFactory } from &quot;../events/accounts-sns-events.factory&quot;;
import { AccountCommonService } from &quot;./account-commons.service&quot;;

@Injectable()
export class AccountActivityActionService {
  constructor(
    // Injected repositories
    private readonly accountActivityRepository: AccountActivityRepository,
    private readonly cachedAccountActivityRepository: CachedAccountActivityRepository,

    // Accounts services
    private readonly accountCommonService: AccountCommonService,

    // Transaction service
    private readonly transactionService: TransactionService,

    // Activities service
    private readonly activitiesService: ActivitiesService,

    // Users service
    private readonly usersService: UsersService,

    // Storage service
    private readonly storageService: StorageService,

    // Event emitter
    private readonly accountsEventsFactory: AccountsEventsFactory,
    private readonly accountsSNSEventsFactory: AccountsSNSEventsFactory,
  ) {}

  /**
   * Find all account activities
   *
   * @param findAccountActivityDto {@link FindAccountActivityDto} The DTO containing the search criteria
   * @returns The list of account activities
   */
  async findAccountActivities(
    user: CurrentUser,
    findAccountActivityDto: FindAccountActivityDto,
  ): Promise&lt;AccountActivity[]&gt; {
    const whereClause: FindOptionsWhere&lt;AccountActivity&gt; &#x3D; {
      account: {
        organizationId: user.orgId,
      },
      isActive: true,
    };

    if (findAccountActivityDto.accountId) {
      const account &#x3D; await this.accountCommonService.validateAndGetAccount(
        findAccountActivityDto.accountId,
        user.orgId,
      );

      whereClause.accountId &#x3D; account.id;
    }

    if (findAccountActivityDto.type) {
      const activityType &#x3D; await this.validateAndGetActivityType(
        user.orgId,
        findAccountActivityDto.type,
      );

      whereClause.typeAttribute &#x3D; activityType;
    }

    if (findAccountActivityDto.status) {
      const activityStatus &#x3D; await this.validateAndGetActivityStatus(
        user.orgId,
        findAccountActivityDto.status,
      );

      whereClause.statusAttribute &#x3D; activityStatus;
    }

    const { results } &#x3D;
      await this.accountActivityRepository.fetchPaginatedResults(
        {
          page: findAccountActivityDto.page ?? 0,
          limit: Math.min(findAccountActivityDto.limit ?? 10, 100),
        },
        {
          where: whereClause,
          relations: [
            &quot;account&quot;,
            &quot;creator&quot;,
            &quot;typeAttribute&quot;,
            &quot;statusAttribute&quot;,
            &quot;attachments&quot;,
          ],
        },
      );
    return results;
  }

  /**
   * Find an existing account activity
   *
   * @param activityId The Activity ID
   * @returns The account activity
   */
  async findAccountActivity(activityId: string): Promise&lt;AccountActivity&gt; {
    const activity &#x3D; await this.cachedAccountActivityRepository.findByCondition(
      {
        where: { uid: activityId, isActive: true },
        relations: [
          &quot;account&quot;,
          &quot;creator&quot;,
          &quot;typeAttribute&quot;,
          &quot;statusAttribute&quot;,
          &quot;attachments&quot;,
        ],
      },
    );

    return activity;
  }

  /**
   * Validate and get an activity type
   *
   * @param orgId The Organization ID
   * @param activityType The Activity Type UID / value
   * @returns The activity type
   */
  private async validateAndGetActivityType(
    orgId: string,
    activityType: string,
  ): Promise&lt;AccountAttributeValue&gt; {
    const activityTypeAttribute &#x3D;
      await this.accountCommonService.getAttributeValue(activityType, orgId);
    if (
      !activityTypeAttribute ||
      activityTypeAttribute.attribute !&#x3D;&#x3D; AccountAttributeType.ACTIVITY_TYPE
    ) {
      throw new NotFoundException(&quot;Activity type not found&quot;);
    }
    return activityTypeAttribute;
  }

  /**
   * Validate and get an activity status
   *
   * @param orgId The Organization ID
   * @param activityStatusUID The Activity Status UID
   * @returns The activity status
   */
  private async validateAndGetActivityStatus(
    orgId: string,
    activityStatusUID: string,
  ): Promise&lt;AccountAttributeValue&gt; {
    const activityStatus &#x3D; await this.accountCommonService.getAttributeValue(
      activityStatusUID,
      orgId,
    );
    if (
      !activityStatus ||
      activityStatus.attribute !&#x3D;&#x3D; AccountAttributeType.ACTIVITY_STATUS
    ) {
      throw new NotFoundException(&quot;Activity status not found&quot;);
    }
    return activityStatus;
  }

  /**
   * Checks if an activity type attribute is in use.
   *
   * @param activityTypeAttributeId The ID of the activity type attribute.
   * @returns Whether the activity type attribute is in use.
   */
  async isActivityTypeAttributeInUse(
    activityTypeAttributeId: string,
  ): Promise&lt;boolean&gt; {
    const count &#x3D; await this.accountActivityRepository.count({
      where: { type: activityTypeAttributeId, isActive: true },
    });
    return count &gt; 0;
  }

  /**
   * Checks if an activity status attribute is in use.
   *
   * @param activityStatusAttributeId The ID of the activity status attribute.
   * @returns Whether the activity status attribute is in use.
   */
  async isActivityStatusAttributeInUse(
    activityStatusAttributeId: string,
  ): Promise&lt;boolean&gt; {
    const count &#x3D; await this.accountActivityRepository.count({
      where: { status: activityStatusAttributeId, isActive: true },
    });
    return count &gt; 0;
  }

  async updateActivityTypeAttribute(
    prevActivityTypeAttributeId: string,
    newActivityTypeAttributeId: string,
  ): Promise&lt;void&gt; {
    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      // Find account activities using the previous activity status attribute
      const accountActivities &#x3D; await this.accountActivityRepository.findAll({
        where: { type: prevActivityTypeAttributeId },
      });

      if (accountActivities.length &#x3D;&#x3D;&#x3D; 0) {
        return;
      }

      // Update the activity status attribute for each account activity
      for (const accountActivity of accountActivities) {
        accountActivity.type &#x3D; newActivityTypeAttributeId;
      }

      // Save the account activities
      await this.accountActivityRepository.saveManyWithTxn(
        txnContext,
        accountActivities,
      );

      // Invalidate the cache for each account
      this.cachedAccountActivityRepository.invalidateAccountActivityCache({
        uids: accountActivities.map((a) &#x3D;&gt; a.uid),
      });
    });
  }

  async updateActivityStatusAttribute(
    prevActivityStatusAttributeId: string,
    newActivityStatusAttributeId: string,
  ): Promise&lt;void&gt; {
    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      // Find account activities using the previous activity status attribute
      const accountActivities &#x3D; await this.accountActivityRepository.findAll({
        where: { status: prevActivityStatusAttributeId },
      });

      if (accountActivities.length &#x3D;&#x3D;&#x3D; 0) {
        return;
      }

      // Update the activity status attribute for each account activity
      for (const accountActivity of accountActivities) {
        accountActivity.status &#x3D; newActivityStatusAttributeId;
      }

      // Save the account activities
      await this.accountActivityRepository.saveManyWithTxn(
        txnContext,
        accountActivities,
      );

      // Invalidate the cache for each account
      this.cachedAccountActivityRepository.invalidateAccountActivityCache({
        uids: accountActivities.map((a) &#x3D;&gt; a.uid),
      });
    });
  }

  /**
   * Create a new account activity
   *
   * @param user Current user
   * @param createAccountActivityDto {@link CreateAccountActivityDto} The DTO containing the account activity details
   * @returns The created account activity
   */
  async createAccountActivity(
    user: CurrentUser,
    createAccountActivityDto: CreateAccountActivityDto,
  ): Promise&lt;AccountActivity&gt; {
    // Find account
    const account &#x3D; await this.accountCommonService.validateAndGetAccount(
      createAccountActivityDto.accountId,
      user.orgId,
    );

    // get user for creator
    const creator &#x3D; await this.usersService.findOneByPublicId(user.uid);

    // verify if all participants exist
    let participants: User[] &#x3D; [];

    if (createAccountActivityDto.participants) {
      participants &#x3D; await this.usersService.findManyByPublicIds(
        createAccountActivityDto.participants,
      );
      if (
        participants.length !&#x3D;&#x3D; createAccountActivityDto.participants.length
      ) {
        throw new BadRequestException(
          &#x60;${
            createAccountActivityDto.participants.length - participants.length
          } participants not found!&#x60;,
        );
      }
    }

    let activityType: AccountAttributeValue;
    let activityStatus: AccountAttributeValue;

    if (!createAccountActivityDto.type) {
      // Use default activity type if not provided
      const defaultActivityType &#x3D;
        await this.accountCommonService.findDefaultAttributeValue(
          user.orgId,
          AccountAttributeType.ACTIVITY_TYPE,
        );

      activityType &#x3D; defaultActivityType;
    } else {
      // Use provided activity type
      activityType &#x3D; await this.validateAndGetActivityType(
        user.orgId,
        createAccountActivityDto.type,
      );
    }

    if (!createAccountActivityDto.status) {
      // Use default activity status if not provided
      const defaultActivityStatus &#x3D;
        await this.accountCommonService.findDefaultAttributeValue(
          user.orgId,
          AccountAttributeType.ACTIVITY_STATUS,
        );

      activityStatus &#x3D; defaultActivityStatus;
    } else {
      // Use provided activity status
      activityStatus &#x3D; await this.validateAndGetActivityStatus(
        user.orgId,
        createAccountActivityDto.status,
      );
    }

    const activity &#x3D; this.accountActivityRepository.create({
      accountId: account.id,
      typeAttribute: activityType,
      statusAttribute: activityStatus,
      duration: createAccountActivityDto.duration,
      location: createAccountActivityDto.location,
      activityTimestamp: createAccountActivityDto.activityTimestamp,
      creator,
      participants: participants.map((p) &#x3D;&gt; p.uid),
    });

    const activityId &#x3D; await this.transactionService.runInTransaction(
      async (txnContext) &#x3D;&gt; {
        // Save in db
        const savedActivity &#x3D; await this.accountActivityRepository.saveWithTxn(
          txnContext,
          activity,
        );

        // Save attachments
        if (createAccountActivityDto.attachmentUrls) {
          savedActivity.attachments &#x3D;
            await this.storageService.attachFilesToEntity(
              &quot;account-activity&quot;,
              savedActivity.uid,
              createAccountActivityDto.attachmentUrls,
              user.orgId,
              &#x60;${user.orgUid}/accounts/${account.uid}/activities/${savedActivity.uid}&#x60;,
            );

          await this.accountActivityRepository.saveWithTxn(
            txnContext,
            savedActivity,
          );
        }

        // Record audit log
        await this.activitiesService.recordAuditLog(
          {
            organization: { id: user.orgId },
            activityPerformedBy: { id: user.sub },
            entityId: savedActivity.id,
            entityType: AuditLogEntityType.ACCOUNT_ACTIVITY,
            op: AuditLogOp.CREATED,
            visibility: AuditLogVisibility.ORGANIZATION,
            activity: &#x60;Account activity ${savedActivity.id} for account ${account.id} was created!&#x60;,
            description: &#x60;Account activity ${savedActivity.id} for account ${account.id} was created by ${user.email}!&#x60;,
          },
          txnContext,
        );

        // Invalidate cache
        await this.cachedAccountActivityRepository.invalidateAccountActivityCache(
          {
            uids: [savedActivity.uid],
          },
        );

        // Publish account activity created event to SNS
        const accountActivityCreatedEvent &#x3D;
          this.accountsSNSEventsFactory.createAccountActivityCreatedSNSEvent(
            user,
            savedActivity,
          );

        this.accountCommonService.publishEventToSNSQueue(
          AccountEvents.ACCOUNT_ACTIVITY_CREATED,
          accountActivityCreatedEvent,
          user,
        );

        return savedActivity.uid;
      },
    );

    return this.findAccountActivity(activityId);
  }

  /**
   * Update an existing account activity
   *
   * @param user Current user
   * @param activityId The Activity ID
   * @param updateAccountActivityDto {@link UpdateAccountActivityDto} The DTO containing the account activity details
   * @returns The updated account activity
   */
  async updateAccountActivity(
    user: CurrentUser,
    activityId: string,
    updateAccountActivityDto: UpdateAccountActivityDto,
  ): Promise&lt;AccountActivity&gt; {
    const activity &#x3D; await this.findAccountActivity(activityId);
    if (!activity) {
      throw new NotFoundException(&quot;Activity not found!&quot;);
    }

    const prevActivity &#x3D; cloneDeep(activity);

    if (updateAccountActivityDto.type) {
      const activityType &#x3D; await this.validateAndGetActivityType(
        user.orgId,
        updateAccountActivityDto.type,
      );

      activity.typeAttribute &#x3D; activityType;
    }

    if (updateAccountActivityDto.status) {
      const activityStatus &#x3D; await this.validateAndGetActivityStatus(
        user.orgId,
        updateAccountActivityDto.status,
      );

      activity.statusAttribute &#x3D; activityStatus;
    }

    if (updateAccountActivityDto.participants) {
      const participants &#x3D; await this.usersService.findManyByPublicIds(
        updateAccountActivityDto.participants,
      );

      if (
        participants.length !&#x3D;&#x3D; updateAccountActivityDto.participants.length
      ) {
        throw new BadRequestException(
          &#x60;${
            updateAccountActivityDto.participants.length - participants.length
          } participants not found!&#x60;,
        );
      }

      activity.participants &#x3D; updateAccountActivityDto.participants;
    }

    if (updateAccountActivityDto.attachmentUrls) {
      const newAttachments &#x3D; await this.storageService.attachFilesToEntity(
        &quot;account-activity&quot;,
        activity.uid,
        updateAccountActivityDto.attachmentUrls,
        user.orgId,
        &#x60;${user.orgUid}/accounts/${activity.account.uid}/activities/${activity.uid}&#x60;,
      );

      activity.attachments &#x3D; [
        ...(activity.attachments ?? []),
        ...newAttachments,
      ];
    }

    activity.duration &#x3D; updateAccountActivityDto.duration ?? activity.duration;
    activity.location &#x3D; updateAccountActivityDto.location ?? activity.location;
    activity.activityTimestamp &#x3D; updateAccountActivityDto.activityTimestamp
      ? new Date(updateAccountActivityDto.activityTimestamp)
      : activity.activityTimestamp;

    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      // Save in db
      const savedActivity &#x3D; await this.accountActivityRepository.saveWithTxn(
        txnContext,
        activity,
      );

      // Record audit log
      await this.activitiesService.recordAuditLog(
        {
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityId: activity.id,
          entityUid: activity.uid,
          entityType: AuditLogEntityType.ACCOUNT_ACTIVITY,
          op: AuditLogOp.UPDATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: &#x60;Account activity ${activity.id} was updated!&#x60;,
          description: &#x60;Account activity ${activity.id} was updated by ${user.email}!&#x60;,
          metadata: {
            updatedFields: Object.keys(updateAccountActivityDto).map((key) &#x3D;&gt; ({
              field: key,
              previousValue: prevActivity[key],
              updatedToValue: savedActivity[key],
            })),
          },
        },
        txnContext,
      );

      // Invalidate cache
      await this.cachedAccountActivityRepository.invalidateAccountActivityCache(
        {
          uids: [activity.uid],
        },
      );

      // Publish account activity updated event to SNS
      const accountActivityUpdatedEvent &#x3D;
        this.accountsSNSEventsFactory.createAccountActivityUpdatedSNSEvent(
          user,
          prevActivity,
          activity,
        );

      this.accountCommonService.publishEventToSNSQueue(
        AccountEvents.ACCOUNT_ACTIVITY_UPDATED,
        accountActivityUpdatedEvent,
        user,
      );
    });

    return this.findAccountActivity(activityId);
  }

  /**
   * Delete an existing account activity
   *
   * @param user Current user
   * @param activityId The Activity ID
   */
  async deleteAccountActivity(
    user: CurrentUser,
    activityId: string,
  ): Promise&lt;void&gt; {
    const activity &#x3D; await this.findAccountActivity(activityId);
    if (!activity) {
      throw new NotFoundException(&quot;Activity not found!&quot;);
    }

    activity.isActive &#x3D; false;

    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      await this.accountActivityRepository.saveWithTxn(txnContext, activity);

      // Record account deleted activity
      await this.activitiesService.recordAuditLog(
        {
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityId: activity.id,
          entityUid: activity.uid,
          entityType: AuditLogEntityType.ACCOUNT_ACTIVITY,
          op: AuditLogOp.DELETED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: &#x60;Account activity ${activity.id} was deleted!&#x60;,
          description: &#x60;Account activity ${activity.id} was deleted by ${user.email}!&#x60;,
          metadata: {
            updatedFields: [
              {
                field: &quot;isActive&quot;,
                previousValue: &quot;true&quot;,
                updatedToValue: &quot;false&quot;,
              },
            ],
          },
        },
        txnContext,
      );

      // Invalidate cache
      await this.cachedAccountActivityRepository.invalidateAccountActivityCache(
        {
          uids: [activity.uid],
        },
      );

      // Publish account activity deleted event to SNS
      const accountActivityDeletedEvent &#x3D;
        this.accountsSNSEventsFactory.createAccountActivityDeletedSNSEvent(
          user,
          activity,
        );

      this.accountCommonService.publishEventToSNSQueue(
        AccountEvents.ACCOUNT_ACTIVITY_DELETED,
        accountActivityDeletedEvent,
        user,
      );
    });
  }

  /**
   * Remove an attachment from an activity
   *
   * @param user Current user
   * @param activityId The Activity ID
   * @param attachmentId The Attachment ID
   */
  async removeActivityAttachment(
    user: CurrentUser,
    activityId: string,
    attachmentId: string,
  ): Promise&lt;void&gt; {
    const activity &#x3D; await this.findAccountActivity(activityId);
    if (!activity) {
      throw new NotFoundException(&quot;Activity not found!&quot;);
    }
    const existingAttachmentIds &#x3D; activity.attachments?.map((a) &#x3D;&gt; a.uid);

    const attachment &#x3D; activity.attachments?.find(
      (a) &#x3D;&gt; a.uid &#x3D;&#x3D;&#x3D; attachmentId,
    );
    if (!attachment) {
      throw new NotFoundException(&quot;Attachment not found!&quot;);
    }

    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      activity.attachments &#x3D; activity.attachments?.filter(
        (a) &#x3D;&gt; a.uid !&#x3D;&#x3D; attachmentId,
      );

      // Save in db
      await this.accountActivityRepository.saveWithTxn(txnContext, activity);

      // Record audit log
      await this.activitiesService.recordAuditLog(
        {
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityId: activity.id,
          entityUid: activity.uid,
          entityType: AuditLogEntityType.ACCOUNT_ACTIVITY,
          op: AuditLogOp.UPDATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: &#x60;Attachment ${attachmentId} was removed from account activity ${activity.id}!&#x60;,
          description: &#x60;Attachment ${attachmentId} was removed from account activity ${activity.id} by ${user.email}!&#x60;,
          metadata: {
            updatedFields: [
              {
                field: &quot;attachments&quot;,
                previousValue: existingAttachmentIds?.join(&quot;,&quot;),
                updatedToValue: activity.attachments
                  ?.map((a) &#x3D;&gt; a.uid)
                  .join(&quot;,&quot;),
              },
            ],
          },
        },
        txnContext,
      );

      // Invalidate cache
      await this.cachedAccountActivityRepository.invalidateAccountActivityCache(
        {
          uids: [activity.uid],
        },
      );
    });
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'AccountActivityActionService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
