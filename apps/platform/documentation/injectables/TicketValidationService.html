<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >TicketValidationService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/tickets/services/ticket-validation.service.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Public</span>
                                <a href="#formatDraftResponses" >formatDraftResponses</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Public</span>
                                <a href="#createTicketFromDraft" >createTicketFromDraft</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                <a href="#draftResponseFormatter" >draftResponseFormatter</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                <a href="#formatPaginatedDraftResponse" >formatPaginatedDraftResponse</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                <a href="#isValidTicketDraft" >isValidTicketDraft</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#validateAndFetchAccount" >validateAndFetchAccount</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#validateAndFetchAgent" >validateAndFetchAgent</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#validateAndFetchPriority" >validateAndFetchPriority</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#validateAndFetchStatus" >validateAndFetchStatus</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#validateAndFetchType" >validateAndFetchType</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                <a href="#validateStatusTransition" >validateStatusTransition</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#validateTeam" >validateTeam</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#validateTicketFields" >validateTicketFields</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(accountsService: <a href="../injectables/AccountsService.html" target="_self">AccountsService</a>, usersService: <a href="../injectables/UsersService.html" target="_self">UsersService</a>, teamsService: <a href="../injectables/TeamsService.html" target="_self">TeamsService</a>, ticketStatusService: <a href="../injectables/TicketStatusActionService.html" target="_self">TicketStatusActionService</a>, ticketTypeService: <a href="../injectables/TicketTypeActionService.html" target="_self">TicketTypeActionService</a>, ticketPriorityService: <a href="../injectables/TicketPriorityActionService.html" target="_self">TicketPriorityActionService</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="32" class="link-to-prism">src/tickets/services/ticket-validation.service.ts:32</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>accountsService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/AccountsService.html" target="_self" >AccountsService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>usersService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/UsersService.html" target="_self" >UsersService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>teamsService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/TeamsService.html" target="_self" >TeamsService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>ticketStatusService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/TicketStatusActionService.html" target="_self" >TicketStatusActionService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>ticketTypeService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/TicketTypeActionService.html" target="_self" >TicketTypeActionService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>ticketPriorityService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/TicketPriorityActionService.html" target="_self" >TicketPriorityActionService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createTicketFromDraft"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                        <span ><b>createTicketFromDraft</b></span>
                        <a href="#createTicketFromDraft"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createTicketFromDraft(draft: Draft)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="75"
                                    class="link-to-prism">src/tickets/services/ticket-validation.service.ts:75</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Create ticket body from draft</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>draft</td>
                                            <td>
                                                        <code>Draft</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/CreateTicketBody.html" target="_self" >CreateTicketBody</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="draftResponseFormatter"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                        <span ><b>draftResponseFormatter</b></span>
                        <a href="#draftResponseFormatter"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>draftResponseFormatter(draft: Draft)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="316"
                                    class="link-to-prism">src/tickets/services/ticket-validation.service.ts:316</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Formats a draft entity into the expected response DTO structure</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>draft</td>
                                            <td>
                                                        <code>Draft</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/DraftTicketResponseDto.html" target="_self" >DraftTicketResponseDto</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="formatPaginatedDraftResponse"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                        <span ><b>formatPaginatedDraftResponse</b></span>
                        <a href="#formatPaginatedDraftResponse"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>formatPaginatedDraftResponse(data: literal type)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="354"
                                    class="link-to-prism">src/tickets/services/ticket-validation.service.ts:354</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Formats paginated draft response</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>data</td>
                                            <td>
                                                        <code>literal type</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>PaginatedResponseDto&lt;DraftTicketResponseDto&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isValidTicketDraft"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                        <span ><b>isValidTicketDraft</b></span>
                        <a href="#isValidTicketDraft"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>isValidTicketDraft(draft: Draft)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="56"
                                    class="link-to-prism">src/tickets/services/ticket-validation.service.ts:56</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Validate Draft tickets have fields present</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>draft</td>
                                            <td>
                                                        <code>Draft</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateAndFetchAccount"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>validateAndFetchAccount</b></span>
                        <a href="#validateAndFetchAccount"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateAndFetchAccount(accountId: string | undefined, requestorEmail: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="147"
                                    class="link-to-prism">src/tickets/services/ticket-validation.service.ts:147</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Validates and fetches account information</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>accountId</td>
                                            <td>
                                                        <code>string | undefined</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>requestorEmail</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;Account&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateAndFetchAgent"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>validateAndFetchAgent</b></span>
                        <a href="#validateAndFetchAgent"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateAndFetchAgent(agentId: string | undefined, teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="180"
                                    class="link-to-prism">src/tickets/services/ticket-validation.service.ts:180</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Validates and fetches agent information</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>agentId</td>
                                            <td>
                                                        <code>string | undefined</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;User | null&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateAndFetchPriority"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>validateAndFetchPriority</b></span>
                        <a href="#validateAndFetchPriority"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateAndFetchPriority(priorityId: string | undefined, teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="281"
                                    class="link-to-prism">src/tickets/services/ticket-validation.service.ts:281</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Validates and fetches priority information</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>priorityId</td>
                                            <td>
                                                        <code>string | undefined</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;TicketPriority&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateAndFetchStatus"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>validateAndFetchStatus</b></span>
                        <a href="#validateAndFetchStatus"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateAndFetchStatus(statusId: string | undefined, teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="212"
                                    class="link-to-prism">src/tickets/services/ticket-validation.service.ts:212</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Validates and fetches status information</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>statusId</td>
                                            <td>
                                                        <code>string | undefined</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;TicketStatus&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateAndFetchType"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>validateAndFetchType</b></span>
                        <a href="#validateAndFetchType"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateAndFetchType(typeId: string | undefined, teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="247"
                                    class="link-to-prism">src/tickets/services/ticket-validation.service.ts:247</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Validates and fetches type information</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>typeId</td>
                                            <td>
                                                        <code>string | undefined</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;TicketType&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateStatusTransition"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                        <span ><b>validateStatusTransition</b></span>
                        <a href="#validateStatusTransition"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateStatusTransition(currentStatus: DraftStatus, newStatus: DraftStatus)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="366"
                                    class="link-to-prism">src/tickets/services/ticket-validation.service.ts:366</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>currentStatus</td>
                                            <td>
                                                        <code>DraftStatus</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>newStatus</td>
                                            <td>
                                                        <code>DraftStatus</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateTeam"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>validateTeam</b></span>
                        <a href="#validateTeam"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateTeam(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="45"
                                    class="link-to-prism">src/tickets/services/ticket-validation.service.ts:45</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Validates the team</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateTicketFields"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>validateTicketFields</b></span>
                        <a href="#validateTicketFields"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateTicketFields(body: <a href="../classes/CreateDraftTicketDto.html" target="_self">CreateDraftTicketDto</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, team: Team)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="98"
                                    class="link-to-prism">src/tickets/services/ticket-validation.service.ts:98</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Validates all ticket fields and returns validated entities</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>body</td>
                                            <td>
                                                            <code><a href="../classes/CreateDraftTicketDto.html" target="_self" >CreateDraftTicketDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>team</td>
                                            <td>
                                                        <code>Team</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/CreateDraftTicketDto.html" target="_self" >Promise&lt;CreateDraftTicketDto&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>
            <section data-compodoc="block-properties">
    
    <h3 id="inputs">
        Properties
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="formatDraftResponses"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                        <span ><b>formatDraftResponses</b></span>
                        <a href="#formatDraftResponses"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>() &#x3D;&gt; {...}</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="347" class="link-to-prism">src/tickets/services/ticket-validation.service.ts:347</a></div>
                        </td>
                    </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-description"><p>Formats multiple drafts for response</p>
</div>
                </td>
            </tr>

        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from &quot;@nestjs/common&quot;;
import {
  Account,
  Draft,
  DraftStatus,
  Team,
  TicketPriority,
  TicketStatus,
  TicketType,
  User,
} from &quot;@repo/thena-platform-entities&quot;;
import { AccountsService } from &quot;../../accounts/services/accounts.service&quot;;
import { CurrentUser } from &quot;../../common/decorators/user.decorator&quot;;
import { extractEmailDetails } from &quot;../../common/utils/extract-email-details&quot;;
import { TeamsService } from &quot;../../teams/services/teams.service&quot;;
import { UsersService } from &quot;../../users/services/users.service&quot;;
import { CreateTicketBody } from &quot;../dto/ticket.dto&quot;;
import {
  CreateDraftTicketDto,
  DraftTicketResponseDto,
  PaginatedResponseDto,
} from &quot;../interfaces/draft.ticket.interface&quot;;
import { TicketStatusActionService } from &quot;../services/ticket-status.action.service&quot;;
import { TicketPriorityActionService } from &quot;./ticket-priority.action.service&quot;;
import { TicketTypeActionService } from &quot;./ticket-type.action.service&quot;;

@Injectable()
export class TicketValidationService {
  constructor(
    private readonly accountsService: AccountsService,
    private readonly usersService: UsersService,
    private readonly teamsService: TeamsService,
    private readonly ticketStatusService: TicketStatusActionService,
    private readonly ticketTypeService: TicketTypeActionService,
    private readonly ticketPriorityService: TicketPriorityActionService,
  ) {}

  /**
   * Validates the team
   */
  public async validateTeam(teamId: string, orgId: string) {
    const team &#x3D; await this.teamsService.findOneByTeamId(teamId, orgId);
    if (!team) {
      throw new NotFoundException(&#x60;Team with ID ${teamId} not found&#x60;);
    }
    return team;
  }

  /**
   * Validate Draft tickets have fields present
   */
  public isValidTicketDraft(draft: Draft): boolean {
    const requiredFields &#x3D; [
      &quot;title&quot;,
      &quot;description&quot;,
      &quot;requestorEmail&quot;,
      &quot;statusId&quot;,
      &quot;priorityId&quot;,
      &quot;typeId&quot;,
      &quot;teamId&quot;,
    ];

    return requiredFields.every(
      (field) &#x3D;&gt; draft.content &amp;&amp; draft.content[field] !&#x3D;&#x3D; undefined,
    );
  }

  /**
   * Create ticket body from draft
   */
  public createTicketFromDraft(draft: Draft): CreateTicketBody {
    const createTicketDto: CreateTicketBody &#x3D; {
      title: draft.content.title,
      description: draft.content.description,
      requestorEmail: draft.content.requestorEmail,
      submitterEmail: draft.content.submitterEmail,
      accountId: draft.content.accountId,
      teamId: draft.content.teamId,
      statusId: draft.content.statusId,
      priorityId: draft.content.priorityId,
      typeId: draft.content.typeId,
      assignedAgentId: draft.content.assignedAgentId,
      isPrivate: draft.content.isPrivate,
      attachmentUrls: draft.content.attachmentUrls,
      customFieldValues: draft.content.customFieldValues,
    };

    return createTicketDto;
  }

  /**
   * Validates all ticket fields and returns validated entities
   */
  public async validateTicketFields(
    body: CreateDraftTicketDto,
    user: CurrentUser,
    team: Team,
  ): Promise&lt;CreateDraftTicketDto&gt; {
    const { orgId } &#x3D; user;
    const { id: teamId } &#x3D; team;

    // Run all validations in parallel
    const [account, agent, status, type, priority] &#x3D; await Promise.all([
      this.validateAndFetchAccount(body.accountId, body.requestorEmail, orgId),
      this.validateAndFetchAgent(body.assignedAgentId, teamId, orgId),
      this.validateAndFetchStatus(body.statusId, teamId, orgId),
      this.validateAndFetchType(body.typeId, teamId, orgId),
      this.validateAndFetchPriority(body.priorityId, teamId, orgId),
    ]);

    // Construct complete ticket data with all fields
    return {
      // Original fields from body
      title: body.title,
      description: body.description,
      requestorEmail: body.requestorEmail,
      submitterEmail: body.submitterEmail,
      teamId: team.uid,
      isPrivate: body.isPrivate ?? false,
      attachmentUrls: body.attachmentUrls ?? [],
      customFieldValues: body.customFieldValues ?? [],
      draftScope: body.draftScope,

      // Account details
      accountId: account?.uid || body.accountId,

      // Agent details
      assignedAgentId: agent?.uid || body.assignedAgentId,

      // Status, Priority, Type with full objects and IDs
      statusId: body.statusId || status?.uid,
      priorityId: body.priorityId || priority?.uid,
      typeId: body.typeId || type?.uid,

      // Additional metadata
      metadata: body.metadata ?? {},
    };
  }

  /**
   * Validates and fetches account information
   */
  private async validateAndFetchAccount(
    accountId: string | undefined,
    requestorEmail: string, // This is the email on whose behalf the ticket is being created. the domain of the email is used to find the account
    orgId: string,
  ): Promise&lt;Account&gt; {
    if (!requestorEmail) {
      throw new BadRequestException(&quot;Requestor email is required&quot;);
    }

    let account: Account | null &#x3D; null;

    if (accountId) {
      account &#x3D; await this.accountsService.findOneByAccountId(accountId, orgId);
      if (!account) {
        throw new NotFoundException(&#x60;Account with ID ${accountId} not found&#x60;);
      }
    } else {
      const { domain } &#x3D; extractEmailDetails(requestorEmail);
      account &#x3D; await this.accountsService.findOneByPrimaryDomain(
        domain,
        orgId,
      );
      if (!account) {
        return null;
      }
    }

    return account;
  }

  /**
   * Validates and fetches agent information
   */
  private async validateAndFetchAgent(
    agentId: string | undefined,
    teamId: string,
    orgId: string,
  ): Promise&lt;User | null&gt; {
    if (!agentId) {
      return null;
    }

    const agent &#x3D; await this.usersService.findOneByPublicId(agentId);
    if (!agent) {
      throw new NotFoundException(&#x60;Agent with ID ${agentId} not found&#x60;);
    }

    const isTeamMember &#x3D; await this.teamsService.userBelongsToTeam(
      agent.id,
      teamId,
      orgId,
    );

    if (!isTeamMember) {
      throw new BadRequestException(
        &#x60;Agent ${agentId} is not a member of team ${teamId}&#x60;,
      );
    }

    return agent;
  }

  /**
   * Validates and fetches status information
   */
  private async validateAndFetchStatus(
    statusId: string | undefined,
    teamId: string,
    orgId: string,
  ): Promise&lt;TicketStatus&gt; {
    if (!statusId) {
      const defaultStatus &#x3D;
        await this.ticketStatusService.internalGetDefaultTicketStatus(
          orgId,
          teamId,
        );
      if (!defaultStatus) {
        throw new NotFoundException(
          &#x60;Default status not found for team ${teamId}&#x60;,
        );
      }
      return defaultStatus;
    }

    const status &#x3D; await this.ticketStatusService.findTicketStatusByPublicId(
      statusId,
      orgId,
      teamId,
    );

    if (!status) {
      throw new NotFoundException(&#x60;Status with ID ${statusId} not found&#x60;);
    }

    return status;
  }

  /**
   * Validates and fetches type information
   */
  private async validateAndFetchType(
    typeId: string | undefined,
    teamId: string,
    orgId: string,
  ): Promise&lt;TicketType&gt; {
    if (!typeId) {
      const defaultType &#x3D; await this.ticketTypeService.findTicketTypeByPublicId(
        typeId,
        orgId,
      );

      if (!defaultType) {
        throw new NotFoundException(
          &#x60;Default ticket type not found for team ${teamId}&#x60;,
        );
      }
      return defaultType;
    }

    const type &#x3D; await this.ticketTypeService.findTicketTypeByPublicId(
      typeId,
      orgId,
    );

    if (!type) {
      throw new NotFoundException(&#x60;Ticket type with ID ${typeId} not found&#x60;);
    }

    return type;
  }

  /**
   * Validates and fetches priority information
   */
  private async validateAndFetchPriority(
    priorityId: string | undefined,
    teamId: string,
    orgId: string,
  ): Promise&lt;TicketPriority&gt; {
    if (!priorityId) {
      const defaultPriority &#x3D;
        await this.ticketPriorityService.internalGetDefaultTicketPriority(
          orgId,
          teamId,
        );
      if (!defaultPriority) {
        throw new NotFoundException(
          &#x60;Default priority not found for team ${teamId}&#x60;,
        );
      }
      return defaultPriority;
    }

    const priority &#x3D;
      await this.ticketPriorityService.findTicketPriorityByPublicId(
        priorityId,
        orgId,
      );

    if (!priority) {
      throw new NotFoundException(&#x60;Priority with ID ${priorityId} not found&#x60;);
    }

    return priority;
  }

  /**
   * Formats a draft entity into the expected response DTO structure
   */
  public draftResponseFormatter(draft: Draft): DraftTicketResponseDto {
    return {
      uid: draft.uid,
      status: draft.status,
      draftScope: draft.draftScope,
      entityType: draft.entityType,
      content: {
        title: draft.content.title,
        description: draft.content.description,
        requestorEmail: draft.content.requestorEmail,
        accountId: draft.content.accountId,
        teamId: draft.content.teamId,
        statusId: draft.content.statusId,
        priorityId: draft.content.priorityId,
        typeId: draft.content.typeId,
        assignedAgentId: draft.content.assignedAgentId,
        isPrivate: draft.content.isPrivate,
        attachmentUrls: draft.content.attachmentUrls,
        customFieldValues: draft.content.customFieldValues,
      },
      metadata: draft.metadata,
      createdBy: draft.createdByUser?.uid || draft.createdBy,
      createdAt: draft.createdAt,
      updatedAt: draft.updatedAt,
      lastModifiedBy: draft.lastModifiedByUser?.uid || draft.lastModifiedBy,
    };
  }

  /**
   * Formats multiple drafts for response
   */
  public formatDraftResponses &#x3D; (drafts: Draft[]): DraftTicketResponseDto[] &#x3D;&gt; {
    return drafts.map(this.draftResponseFormatter);
  };

  /**
   * Formats paginated draft response
   */
  public formatPaginatedDraftResponse(data: {
    items: Draft[];
    meta: any;
  }): PaginatedResponseDto&lt;DraftTicketResponseDto&gt; {
    return new PaginatedResponseDto(
      this.formatDraftResponses(data.items),
      data.meta.totalItems,
      data.meta.currentPage,
      data.meta.itemsPerPage,
    );
  }

  public validateStatusTransition(
    currentStatus: DraftStatus,
    newStatus: DraftStatus,
  ): void {
    // Publishing should only happen through the publish endpoint
    if (newStatus &#x3D;&#x3D;&#x3D; DraftStatus.PUBLISHED) {
      throw new BadRequestException(
        &quot;Cannot set status to PUBLISHED directly. Use the publish endpoint instead.&quot;,
      );
    }

    const allowedTransitions &#x3D; {
      [DraftStatus.IN_PROGRESS]: [
        DraftStatus.READY_TO_PUBLISH,
        DraftStatus.DISCARDED,
      ],
      [DraftStatus.READY_TO_PUBLISH]: [
        DraftStatus.IN_PROGRESS,
        DraftStatus.PUBLISHED,
        DraftStatus.DISCARDED,
      ],
      [DraftStatus.PUBLISHED]: [], // No transitions allowed
      [DraftStatus.DISCARDED]: [], // No transitions allowed
    };

    if (!allowedTransitions[currentStatus]?.includes(newStatus)) {
      throw new BadRequestException(
        &#x60;Invalid status transition from ${currentStatus} to ${newStatus}&#x60;,
      );
    }
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'TicketValidationService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
