<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >TicketPriorityActionService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/tickets/services/ticket-priority.action.service.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createNewCustomPriority" >createNewCustomPriority</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#deleteCustomPriority" >deleteCustomPriority</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#extractUserFromRequest" >extractUserFromRequest</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findAllTicketPriorities" >findAllTicketPriorities</a>
                            </li>
                            <li>
                                <a href="#findClosestMatchLevenshtein" >findClosestMatchLevenshtein</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#findDuplicateDefaultPriority" >findDuplicateDefaultPriority</a>
                            </li>
                            <li>
                                <a href="#findTicketPrioritiesByPublicIds" >findTicketPrioritiesByPublicIds</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findTicketPrioritiesByTeamId" >findTicketPrioritiesByTeamId</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findTicketPriorityById" >findTicketPriorityById</a>
                            </li>
                            <li>
                                <a href="#findTicketPriorityByPublicId" >findTicketPriorityByPublicId</a>
                            </li>
                            <li>
                                <a href="#internalGetDefaultTicketPriority" >internalGetDefaultTicketPriority</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateCustomPriorityData" >updateCustomPriorityData</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#validateAndFetchTeam" >validateAndFetchTeam</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(cachedTicketPriorityRepository: CachedTicketPriorityRepository, ticketPriorityRepository: TicketPriorityRepository, ticketRepository: TicketRepository, usersService: <a href="../injectables/UsersService.html" target="_self">UsersService</a>, teamsService: <a href="../injectables/TeamsService.html" target="_self">TeamsService</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="25" class="link-to-prism">src/tickets/services/ticket-priority.action.service.ts:25</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>cachedTicketPriorityRepository</td>
                                                  
                                                        <td>
                                                                    <code>CachedTicketPriorityRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>ticketPriorityRepository</td>
                                                  
                                                        <td>
                                                                    <code>TicketPriorityRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>ticketRepository</td>
                                                  
                                                        <td>
                                                                    <code>TicketRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>usersService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/UsersService.html" target="_self" >UsersService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>teamsService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/TeamsService.html" target="_self" >TeamsService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createNewCustomPriority"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>createNewCustomPriority</b></span>
                        <a href="#createNewCustomPriority"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createNewCustomPriority(createTicketPriorityDto: <a href="../classes/CreateTicketPriorityDto.html" target="_self">CreateTicketPriorityDto</a>, request: <a href="../interfaces/FastifyRequest.html" target="_self">FastifyRequest</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="236"
                                    class="link-to-prism">src/tickets/services/ticket-priority.action.service.ts:236</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Creates a new ticket priority.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>createTicketPriorityDto</td>
                                            <td>
                                                            <code><a href="../classes/CreateTicketPriorityDto.html" target="_self" >CreateTicketPriorityDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ticket priority data to create.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../interfaces/FastifyRequest.html" target="_self" >FastifyRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The request object.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The created ticket priority.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteCustomPriority"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>deleteCustomPriority</b></span>
                        <a href="#deleteCustomPriority"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteCustomPriority(ticketPriorityId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, request: <a href="../interfaces/FastifyRequest.html" target="_self">FastifyRequest</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="342"
                                    class="link-to-prism">src/tickets/services/ticket-priority.action.service.ts:342</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Deletes a ticket priority.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>ticketPriorityId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the ticket priority to delete.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../interfaces/FastifyRequest.html" target="_self" >FastifyRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The request object.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;Partial&lt;TicketPriority&gt;&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The deleted ticket priority.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="extractUserFromRequest"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>extractUserFromRequest</b></span>
                        <a href="#extractUserFromRequest"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>extractUserFromRequest(request: <a href="../interfaces/FastifyRequest.html" target="_self">FastifyRequest</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="44"
                                    class="link-to-prism">src/tickets/services/ticket-priority.action.service.ts:44</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Extracts the user from the request.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../interfaces/FastifyRequest.html" target="_self" >FastifyRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The request object.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;User&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The user.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAllTicketPriorities"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findAllTicketPriorities</b></span>
                        <a href="#findAllTicketPriorities"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAllTicketPriorities(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, teamId?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="169"
                                    class="link-to-prism">src/tickets/services/ticket-priority.action.service.ts:169</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds all ticket priorities for the organization.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The user.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                                    <p>The ID of the team to filter by.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>All ticket priorities for the organization.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findClosestMatchLevenshtein"></a>
                    <span class="name">
                        <span ><b>findClosestMatchLevenshtein</b></span>
                        <a href="#findClosestMatchLevenshtein"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findClosestMatchLevenshtein(name: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, teamId?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="99"
                                    class="link-to-prism">src/tickets/services/ticket-priority.action.service.ts:99</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds the closest match for a ticket status name using levenshtein distance.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>name</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The name of the ticket status to match against.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                                    <p>The ID of the team.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                <p>The closest match for the ticket status name.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findDuplicateDefaultPriority"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>findDuplicateDefaultPriority</b></span>
                        <a href="#findDuplicateDefaultPriority"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findDuplicateDefaultPriority(orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="82"
                                    class="link-to-prism">src/tickets/services/ticket-priority.action.service.ts:82</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds a duplicate default priority.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the team.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The duplicate default priority.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findTicketPrioritiesByPublicIds"></a>
                    <span class="name">
                        <span ><b>findTicketPrioritiesByPublicIds</b></span>
                        <a href="#findTicketPrioritiesByPublicIds"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findTicketPrioritiesByPublicIds(priorityIds: string[], organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="113"
                                    class="link-to-prism">src/tickets/services/ticket-priority.action.service.ts:113</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds ticket priorities by their public IDs.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>priorityIds</td>
                                            <td>
                                                        <code>string[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The public IDs of the ticket priorities.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                <p>The ticket priorities.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findTicketPrioritiesByTeamId"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findTicketPrioritiesByTeamId</b></span>
                        <a href="#findTicketPrioritiesByTeamId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findTicketPrioritiesByTeamId(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="196"
                                    class="link-to-prism">src/tickets/services/ticket-priority.action.service.ts:196</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findTicketPriorityById"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findTicketPriorityById</b></span>
                        <a href="#findTicketPriorityById"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findTicketPriorityById(ticketPriorityId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, request: <a href="../interfaces/FastifyRequest.html" target="_self">FastifyRequest</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="210"
                                    class="link-to-prism">src/tickets/services/ticket-priority.action.service.ts:210</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds a ticket priority by its ID.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>ticketPriorityId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the ticket priority to find.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../interfaces/FastifyRequest.html" target="_self" >FastifyRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The ticket priority.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findTicketPriorityByPublicId"></a>
                    <span class="name">
                        <span ><b>findTicketPriorityByPublicId</b></span>
                        <a href="#findTicketPriorityByPublicId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findTicketPriorityByPublicId(priorityId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, teamId?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="147"
                                    class="link-to-prism">src/tickets/services/ticket-priority.action.service.ts:147</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Gets a ticket priority by its public ID.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>priorityId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The public ID of the ticket priority.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                                    <p>The ID of the team.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                <p>The internal ticket priority.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="internalGetDefaultTicketPriority"></a>
                    <span class="name">
                        <span ><b>internalGetDefaultTicketPriority</b></span>
                        <a href="#internalGetDefaultTicketPriority"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>internalGetDefaultTicketPriority(orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="129"
                                    class="link-to-prism">src/tickets/services/ticket-priority.action.service.ts:129</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Gets a default ticket priority.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the team.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                <p>The internal default ticket priority.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateCustomPriorityData"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>updateCustomPriorityData</b></span>
                        <a href="#updateCustomPriorityData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateCustomPriorityData(ticketPriorityId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, updateTicketPriorityDto: <a href="../classes/UpdateTicketPriorityDto.html" target="_self">UpdateTicketPriorityDto</a>, request: <a href="../interfaces/FastifyRequest.html" target="_self">FastifyRequest</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="296"
                                    class="link-to-prism">src/tickets/services/ticket-priority.action.service.ts:296</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Updates a ticket priority.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>ticketPriorityId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the ticket priority to update.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>updateTicketPriorityDto</td>
                                            <td>
                                                            <code><a href="../classes/UpdateTicketPriorityDto.html" target="_self" >UpdateTicketPriorityDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ticket priority data to update.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../interfaces/FastifyRequest.html" target="_self" >FastifyRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The request object.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The updated ticket priority.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateAndFetchTeam"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>validateAndFetchTeam</b></span>
                        <a href="#validateAndFetchTeam"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateAndFetchTeam(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="63"
                                    class="link-to-prism">src/tickets/services/ticket-priority.action.service.ts:63</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Validates and fetches a team by its ID.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the team to fetch.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The team.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from &quot;@nestjs/common&quot;;
import {
  CachedTicketPriorityRepository,
  TicketPriority,
  TicketPriorityRepository,
  TicketRepository,
  User,
} from &quot;@repo/thena-platform-entities&quot;;
import { FastifyRequest } from &quot;fastify&quot;;
import { CurrentUser } from &quot;src/common/decorators/user.decorator&quot;;
import { FindManyOptions, In } from &quot;typeorm&quot;;
import { TeamsService } from &quot;../../teams/services/teams.service&quot;;
import { UsersService } from &quot;../../users/services/users.service&quot;;
import {
  CreateTicketPriorityDto,
  UpdateTicketPriorityDto,
} from &quot;../dto/ticket-priority.dto&quot;;

@Injectable()
export class TicketPriorityActionService {
  constructor(
    // Injected cached repositories
    private cachedTicketPriorityRepository: CachedTicketPriorityRepository,

    // Injected repositories
    private ticketPriorityRepository: TicketPriorityRepository,
    private ticketRepository: TicketRepository,

    // Injected services
    private usersService: UsersService,
    private teamsService: TeamsService,
  ) {}

  /**
   * Extracts the user from the request.
   * @param request The request object.
   * @returns The user.
   */
  private async extractUserFromRequest(request: FastifyRequest): Promise&lt;User&gt; {
    const userEmail &#x3D; request.user.email;
    if (!userEmail) {
      throw new UnauthorizedException(&quot;User is not authenticated!&quot;);
    }

    const user &#x3D; await this.usersService.findOneByEmail(userEmail);
    if (!user) {
      throw new UnauthorizedException(&quot;User is not authenticated!&quot;);
    }

    return user;
  }

  /**
   * Validates and fetches a team by its ID.
   * @param teamId The ID of the team to fetch.
   * @returns The team.
   */
  private async validateAndFetchTeam(teamId: string, organizationId: string) {
    const team &#x3D; await this.teamsService.findOneByTeamId(
      teamId,
      organizationId,
    );

    if (!team) {
      throw new NotFoundException(&quot;Team not found!&quot;);
    }

    return team;
  }

  /**
   * Finds a duplicate default priority.
   * @param orgId The ID of the organization.
   * @param teamId The ID of the team.
   * @returns The duplicate default priority.
   */
  private async findDuplicateDefaultPriority(orgId: string, teamId: string) {
    return await this.ticketPriorityRepository.findByCondition({
      where: {
        isDefault: true,
        organizationId: orgId,
        teamId: teamId,
      },
    });
  }

  /**
   * Finds the closest match for a ticket status name using levenshtein distance.
   * @param name The name of the ticket status to match against.
   * @param orgId The ID of the organization.
   * @param teamId The ID of the team.
   * @returns The closest match for the ticket status name.
   */
  findClosestMatchLevenshtein(name: string, orgId: string, teamId?: string) {
    return this.ticketPriorityRepository.findClosestMatchLevenshtein(
      name,
      orgId,
      teamId,
    );
  }

  /**
   * Finds ticket priorities by their public IDs.
   * @param priorityIds The public IDs of the ticket priorities.
   * @param organizationId The ID of the organization.
   * @returns The ticket priorities.
   */
  findTicketPrioritiesByPublicIds(
    priorityIds: string[],
    organizationId: string,
  ) {
    return this.ticketPriorityRepository.findAll({
      where: { uid: In(priorityIds), organizationId },
    });
  }

  /**
   * @internal
   * Gets a default ticket priority.
   * @param orgId The ID of the organization.
   * @param teamId The ID of the team.
   * @returns The internal default ticket priority.
   */
  internalGetDefaultTicketPriority(orgId: string, teamId: string) {
    return this.ticketPriorityRepository.findByCondition({
      where: {
        isDefault: true,
        organizationId: orgId,
        teamId,
      },
    });
  }

  /**
   * @internal
   * Gets a ticket priority by its public ID.
   * @param priorityId The public ID of the ticket priority.
   * @param orgId The ID of the organization.
   * @param teamId The ID of the team.
   * @returns The internal ticket priority.
   */
  findTicketPriorityByPublicId(
    priorityId: string,
    orgId: string,
    teamId?: string,
  ) {
    const query: FindManyOptions&lt;TicketPriority&gt; &#x3D; {
      where: { uid: priorityId, organizationId: orgId },
    };

    if (teamId) {
      query.where &#x3D; { ...query.where, teamId };
    }

    return this.ticketPriorityRepository.findByCondition(query);
  }

  /**
   * Finds all ticket priorities for the organization.
   * @param user The user.
   * @param teamId The ID of the team to filter by.
   * @returns All ticket priorities for the organization.
   */
  async findAllTicketPriorities(user: CurrentUser, teamId?: string) {
    // Build the where clause
    const whereClause: Record&lt;string, string&gt; &#x3D; {
      organizationId: user.orgId,
    };

    // If the team ID is provided, filter by team ID
    if (teamId?.trim()) {
      // If the team ID is provided, filter by team ID
      const team &#x3D; await this.validateAndFetchTeam(teamId, user.orgId);
      whereClause.teamId &#x3D; team.id;

      // If the team has a parent team then we&#x27;ll use the parent team&#x27;s priorities
      if (team.parentTeamId) {
        whereClause.teamId &#x3D; team.parentTeamId;
      }
    }

    // Find all ticket priorities for the organization
    const ticketPriorities &#x3D; await this.ticketPriorityRepository.findAll({
      where: whereClause,
      relations: [&quot;team&quot;, &quot;organization&quot;],
    });

    return ticketPriorities;
  }

  async findTicketPrioritiesByTeamId(teamId: string, organizationId: string) {
    const ticketPriorities &#x3D; await this.ticketPriorityRepository.findAll({
      where: { teamId, organizationId },
      relations: [&quot;team&quot;, &quot;organization&quot;],
    });

    return ticketPriorities;
  }

  /**
   * Finds a ticket priority by its ID.
   * @param ticketPriorityId The ID of the ticket priority to find.
   * @returns The ticket priority.
   */
  async findTicketPriorityById(
    ticketPriorityId: string,
    request: FastifyRequest,
  ) {
    // Get the organization ID from the user object attached to the request
    const user &#x3D; await this.extractUserFromRequest(request);

    const ticketPriority &#x3D;
      await this.cachedTicketPriorityRepository.findByCondition({
        where: { uid: ticketPriorityId, organizationId: user.organizationId },
        relations: [&quot;team&quot;, &quot;organization&quot;],
      });

    if (!ticketPriority) {
      throw new NotFoundException(&quot;Ticket priority not found!&quot;);
    }

    return ticketPriority;
  }

  /**
   * Creates a new ticket priority.
   * @param createTicketPriorityDto The ticket priority data to create.
   * @param request The request object.
   * @returns The created ticket priority.
   */
  async createNewCustomPriority(
    createTicketPriorityDto: CreateTicketPriorityDto,
    request: FastifyRequest,
  ) {
    // Get the user from the request
    const user &#x3D; await this.extractUserFromRequest(request);

    // Get the organization ID from the user object
    const orgId &#x3D; user.organizationId;

    // Validate and fetch the team
    const team &#x3D; await this.validateAndFetchTeam(
      createTicketPriorityDto.teamId,
      orgId,
    );

    // If the team has a parent team then we&#x27;ll throw since child teams cannot have custom priorities
    if (team.parentTeamId) {
      throw new BadRequestException(
        &quot;You cannot create priorities for a sub-teams!&quot;,
      );
    }

    // If the priority is default, check if there is already a default priority for the team
    if (createTicketPriorityDto.isDefault) {
      const existingDefaultPriority &#x3D; await this.findDuplicateDefaultPriority(
        orgId,
        team.id,
      );
      if (existingDefaultPriority) {
        throw new BadRequestException(&quot;Default priority already exists!&quot;);
      }
    }

    // Create the ticket priority
    const newTicketPriority &#x3D; this.cachedTicketPriorityRepository.create({
      name: createTicketPriorityDto.name,
      displayName:
        createTicketPriorityDto.displayName ?? createTicketPriorityDto.name,
      isDefault: createTicketPriorityDto.isDefault,
      description: createTicketPriorityDto.description,
      organizationId: orgId,
      teamId: team.id,
    });

    // Save the new ticket priority
    const ticketPriority &#x3D; await this.cachedTicketPriorityRepository.save(
      newTicketPriority,
    );

    return ticketPriority;
  }

  /**
   * Updates a ticket priority.
   * @param ticketPriorityId The ID of the ticket priority to update.
   * @param updateTicketPriorityDto The ticket priority data to update.
   * @param request The request object.
   * @returns The updated ticket priority.
   */
  async updateCustomPriorityData(
    ticketPriorityId: string,
    updateTicketPriorityDto: UpdateTicketPriorityDto,
    request: FastifyRequest,
  ) {
    // Get the user email from the request
    const user &#x3D; await this.extractUserFromRequest(request);

    // Find the ticket priority by its ID
    const ticketPriority &#x3D;
      await this.cachedTicketPriorityRepository.findByCondition({
        where: { uid: ticketPriorityId, organizationId: user.organizationId },
      });

    if (!ticketPriority) {
      throw new NotFoundException(&quot;Ticket priority not found!&quot;);
    }

    // If the priority is default, check if there is already a default priority for the team
    if (updateTicketPriorityDto.isDefault) {
      const existingDefaultPriority &#x3D; await this.findDuplicateDefaultPriority(
        user.organizationId,
        ticketPriority.teamId,
      );

      if (existingDefaultPriority) {
        throw new BadRequestException(&quot;Default priority already exists!&quot;);
      }
    }

    // Update the ticket priority
    const updatedTicketPriority &#x3D;
      await this.cachedTicketPriorityRepository.save({
        ...ticketPriority,
        ...updateTicketPriorityDto,
      });

    return updatedTicketPriority;
  }

  /**
   * Deletes a ticket priority.
   * @param ticketPriorityId The ID of the ticket priority to delete.
   * @param request The request object.
   * @returns The deleted ticket priority.
   */
  async deleteCustomPriority(
    ticketPriorityId: string,
    request: FastifyRequest,
  ): Promise&lt;Partial&lt;TicketPriority&gt;&gt; {
    // Get the user from the request
    const user &#x3D; await this.extractUserFromRequest(request);

    // Find the ticket priority by its ID
    const ticketPriority &#x3D; await this.ticketPriorityRepository.findByCondition({
      where: { uid: ticketPriorityId, organizationId: user.organizationId },
    });

    // If the ticket priority is not found, throw an error
    if (!ticketPriority) {
      throw new NotFoundException(&quot;Ticket priority not found!&quot;);
    }

    // Find the number of tickets with the priority
    const ticketsWithPriorityCount &#x3D; await this.ticketRepository.count({
      where: {
        priorityId: ticketPriority.id,
        organizationId: user.organizationId,
      },
    });

    // If there are tickets with the priority, throw an error
    if (ticketsWithPriorityCount &gt; 0) {
      throw new BadRequestException(
        &quot;Cannot delete a priority that has tickets!&quot;,
      );
    }

    // Delete the ticket priority
    const removedTicketPriority &#x3D;
      await this.cachedTicketPriorityRepository.remove(ticketPriority);

    return removedTicketPriority;
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'TicketPriorityActionService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
