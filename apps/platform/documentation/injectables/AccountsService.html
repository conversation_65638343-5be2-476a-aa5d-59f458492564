<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >AccountsService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/accounts/services/accounts.service.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createAccount" >createAccount</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#deleteAccount" >deleteAccount</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findAccountDetails" >findAccountDetails</a>
                            </li>
                            <li>
                                <a href="#findAccountsByPrimaryDomains" >findAccountsByPrimaryDomains</a>
                            </li>
                            <li>
                                <a href="#findAccountsByPublicIds" >findAccountsByPublicIds</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findAllAccounts" >findAllAccounts</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#findIfDomainIsUsed" >findIfDomainIsUsed</a>
                            </li>
                            <li>
                                <a href="#findOneByAccountId" >findOneByAccountId</a>
                            </li>
                            <li>
                                <a href="#findOneByPrimaryDomain" >findOneByPrimaryDomain</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#isAccountAttributeValueInUse" >isAccountAttributeValueInUse</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateAccount" >updateAccount</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateAccountAttributeValue" >updateAccountAttributeValue</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#validateAndGetAttributeValue" >validateAndGetAttributeValue</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(accountRepository: AccountRepository, cachedAccountRepository: CachedAccountRepository, accountCommonService: <a href="../injectables/AccountCommonService.html" target="_self">AccountCommonService</a>, transactionService: TransactionService, activitiesService: <a href="../injectables/ActivitiesService.html" target="_self">ActivitiesService</a>, usersService: <a href="../injectables/UsersService.html" target="_self">UsersService</a>, customFieldValuesService: <a href="../injectables/CustomFieldValuesService.html" target="_self">CustomFieldValuesService</a>, accountsEventsFactory: <a href="../injectables/AccountsEventsFactory.html" target="_self">AccountsEventsFactory</a>, eventEmitter: EventEmitter2, accountsSNSEventsFactory: <a href="../classes/AccountsSNSEventsFactory.html" target="_self">AccountsSNSEventsFactory</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="38" class="link-to-prism">src/accounts/services/accounts.service.ts:38</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>accountRepository</td>
                                                  
                                                        <td>
                                                                    <code>AccountRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>cachedAccountRepository</td>
                                                  
                                                        <td>
                                                                    <code>CachedAccountRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>accountCommonService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/AccountCommonService.html" target="_self" >AccountCommonService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>transactionService</td>
                                                  
                                                        <td>
                                                                    <code>TransactionService</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>activitiesService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/ActivitiesService.html" target="_self" >ActivitiesService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>usersService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/UsersService.html" target="_self" >UsersService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>customFieldValuesService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/CustomFieldValuesService.html" target="_self" >CustomFieldValuesService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>accountsEventsFactory</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/AccountsEventsFactory.html" target="_self" >AccountsEventsFactory</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>eventEmitter</td>
                                                  
                                                        <td>
                                                                    <code>EventEmitter2</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>accountsSNSEventsFactory</td>
                                                  
                                                        <td>
                                                                        <code><a href="../classes/AccountsSNSEventsFactory.html" target="_self" >AccountsSNSEventsFactory</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createAccount"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>createAccount</b></span>
                        <a href="#createAccount"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createAccount(currentUser: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, createAccountDto: <a href="../classes/CreateAccountDto.html" target="_self">CreateAccountDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="378"
                                    class="link-to-prism">src/accounts/services/accounts.service.ts:378</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Creates a new account.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>currentUser</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>createAccountDto</td>
                                            <td>
                                                            <code><a href="../classes/CreateAccountDto.html" target="_self" >CreateAccountDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p><a href="../classes/CreateAccountDto.html">CreateAccountDto</a> The DTO containing the account details.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;Account&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The created account.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteAccount"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>deleteAccount</b></span>
                        <a href="#deleteAccount"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteAccount(uid: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, currentUser: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="792"
                                    class="link-to-prism">src/accounts/services/accounts.service.ts:792</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Deletes an existing account.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>uid</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The UID of the account to delete.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>currentUser</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;void&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAccountDetails"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findAccountDetails</b></span>
                        <a href="#findAccountDetails"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAccountDetails(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, accountId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="221"
                                    class="link-to-prism">src/accounts/services/accounts.service.ts:221</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds the details of an account.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>accountId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the account to find.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;Account&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The account details.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAccountsByPrimaryDomains"></a>
                    <span class="name">
                        <span ><b>findAccountsByPrimaryDomains</b></span>
                        <a href="#findAccountsByPrimaryDomains"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findAccountsByPrimaryDomains(primaryDomains: string[], organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="116"
                                    class="link-to-prism">src/accounts/services/accounts.service.ts:116</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds accounts by their primary domains and organization ID.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>primaryDomains</td>
                                            <td>
                                                        <code>string[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The primary domains of the accounts to find.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization to find the accounts in.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;Account[]&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The accounts.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAccountsByPublicIds"></a>
                    <span class="name">
                        <span ><b>findAccountsByPublicIds</b></span>
                        <a href="#findAccountsByPublicIds"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findAccountsByPublicIds(accountIds: string[], organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="101"
                                    class="link-to-prism">src/accounts/services/accounts.service.ts:101</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds accounts by their UIDs and organization ID.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>accountIds</td>
                                            <td>
                                                        <code>string[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The UIDs of the accounts to find.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization to find the accounts in.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;Account[]&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The accounts.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAllAccounts"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findAllAccounts</b></span>
                        <a href="#findAllAccounts"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAllAccounts(organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, query: <a href="../classes/FindAllAccountsDto.html" target="_self">FindAllAccountsDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="134"
                                    class="link-to-prism">src/accounts/services/accounts.service.ts:134</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds all accounts.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization to find the accounts in.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>query</td>
                                            <td>
                                                            <code><a href="../classes/FindAllAccountsDto.html" target="_self" >FindAllAccountsDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;Account[]&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The accounts.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findIfDomainIsUsed"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>findIfDomainIsUsed</b></span>
                        <a href="#findIfDomainIsUsed"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findIfDomainIsUsed(domain: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="319"
                                    class="link-to-prism">src/accounts/services/accounts.service.ts:319</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>domain</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;boolean&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findOneByAccountId"></a>
                    <span class="name">
                        <span ><b>findOneByAccountId</b></span>
                        <a href="#findOneByAccountId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findOneByAccountId(accountId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="86"
                                    class="link-to-prism">src/accounts/services/accounts.service.ts:86</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds an account by its UID and organization ID.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>accountId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The UID of the account to find.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization to find the account in.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;Account&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The account.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findOneByPrimaryDomain"></a>
                    <span class="name">
                        <span ><b>findOneByPrimaryDomain</b></span>
                        <a href="#findOneByPrimaryDomain"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findOneByPrimaryDomain(primaryDomain: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="71"
                                    class="link-to-prism">src/accounts/services/accounts.service.ts:71</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds an account by its primary domain and organization ID.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>primaryDomain</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The primary domain of the account to find.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization to find the account in.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;Account&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The account.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isAccountAttributeValueInUse"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>isAccountAttributeValueInUse</b></span>
                        <a href="#isAccountAttributeValueInUse"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>isAccountAttributeValueInUse(attributeValueId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, attributeType: AccountAttributeType)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="251"
                                    class="link-to-prism">src/accounts/services/accounts.service.ts:251</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Checks if an attribute value is in use.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>attributeValueId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the attribute value</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>attributeType</td>
                                            <td>
                                                        <code>AccountAttributeType</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The attribute type</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;boolean&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>Whether the attribute value is in use</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateAccount"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>updateAccount</b></span>
                        <a href="#updateAccount"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateAccount(uid: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, currentUser: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, updateAccountDto: <a href="../classes/UpdateAccountDto.html" target="_self">UpdateAccountDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="547"
                                    class="link-to-prism">src/accounts/services/accounts.service.ts:547</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Updates an existing account.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>uid</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the account to update.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>currentUser</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>updateAccountDto</td>
                                            <td>
                                                            <code><a href="../classes/UpdateAccountDto.html" target="_self" >UpdateAccountDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p><a href="../classes/UpdateAccountDto.html">UpdateAccountDto</a> The DTO containing the account details.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;Account&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The updated account.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateAccountAttributeValue"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>updateAccountAttributeValue</b></span>
                        <a href="#updateAccountAttributeValue"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateAccountAttributeValue(attributeValueId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, defaultAttributeValueId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, attributeType: AccountAttributeType)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="273"
                                    class="link-to-prism">src/accounts/services/accounts.service.ts:273</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>attributeValueId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>defaultAttributeValueId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>attributeType</td>
                                            <td>
                                                        <code>AccountAttributeType</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;void&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateAndGetAttributeValue"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>validateAndGetAttributeValue</b></span>
                        <a href="#validateAndGetAttributeValue"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateAndGetAttributeValue(attributeValue: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, attributeType: AccountAttributeType)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="348"
                                    class="link-to-prism">src/accounts/services/accounts.service.ts:348</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Validates and gets an account task attribute value. (One of the task type, task status, or task priority)</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>attributeValue</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The UID of the attribute value</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>attributeType</td>
                                            <td>
                                                        <code>AccountAttributeType</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The attribute type</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;AccountAttributeValue&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The account attribute value</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from &quot;@nestjs/common&quot;;
import { EventEmitter2 } from &quot;@nestjs/event-emitter&quot;;
import { AccountEvents } from &quot;@repo/thena-eventbridge&quot;;
import {
  Account,
  AccountAttributeType,
  AccountAttributeValue,
  AccountRepository,
  AuditLogEntityType,
  AuditLogOp,
  AuditLogVisibility,
  CachedAccountRepository,
  CustomFieldValues,
  TransactionService,
  User,
} from &quot;@repo/thena-platform-entities&quot;;
import { cloneDeep, isArray } from &quot;lodash&quot;;
import { FindOptionsWhere, In } from &quot;typeorm&quot;;
import { ActivitiesService } from &quot;../../activities/services/activities.service&quot;;
import { CurrentUser } from &quot;../../common/decorators/user.decorator&quot;;
import { CustomFieldValuesService } from &quot;../../custom-field/services/custom-field-values.service&quot;;
import { UsersService } from &quot;../../users/services/users.service&quot;;
import {
  CreateAccountDto,
  FindAllAccountsDto,
  UpdateAccountDto,
} from &quot;../dtos/account.dto&quot;;
import { AccountsEventsFactory } from &quot;../events/accounts-events.factory&quot;;
import { AccountsSNSEventsFactory } from &quot;../events/accounts-sns-events.factory&quot;;
import { EmittableAccountEvents } from &quot;../events/accounts.events&quot;;
import { AccountCommonService } from &quot;./account-commons.service&quot;;

@Injectable()
export class AccountsService {
  constructor(
    // Injected repositories
    private readonly accountRepository: AccountRepository,
    private readonly cachedAccountRepository: CachedAccountRepository,

    // Accounts services
    private readonly accountCommonService: AccountCommonService,

    // Transaction service
    private readonly transactionService: TransactionService,

    // Activities service
    private readonly activitiesService: ActivitiesService,

    // Users service
    private readonly usersService: UsersService,

    // Custom field values service
    private readonly customFieldValuesService: CustomFieldValuesService,

    // Event emitter
    private readonly accountsEventsFactory: AccountsEventsFactory,
    private readonly eventEmitter: EventEmitter2,
    private readonly accountsSNSEventsFactory: AccountsSNSEventsFactory,
  ) {}

  /**
   * Finds an account by its primary domain and organization ID.
   * @param primaryDomain The primary domain of the account to find.
   * @param organizationId The ID of the organization to find the account in.
   * @returns The account.
   */
  findOneByPrimaryDomain(
    primaryDomain: string,
    organizationId: string,
  ): Promise&lt;Account&gt; {
    return this.cachedAccountRepository.findByCondition({
      where: { primaryDomain, organizationId, isActive: true },
    });
  }

  /**
   * Finds an account by its UID and organization ID.
   * @param accountId The UID of the account to find.
   * @param organizationId The ID of the organization to find the account in.
   * @returns The account.
   */
  findOneByAccountId(
    accountId: string,
    organizationId: string,
  ): Promise&lt;Account&gt; {
    return this.cachedAccountRepository.findByCondition({
      where: { uid: accountId, organizationId, isActive: true },
    });
  }

  /**
   * Finds accounts by their UIDs and organization ID.
   * @param accountIds The UIDs of the accounts to find.
   * @param organizationId The ID of the organization to find the accounts in.
   * @returns The accounts.
   */
  findAccountsByPublicIds(
    accountIds: string[],
    organizationId: string,
  ): Promise&lt;Account[]&gt; {
    return this.accountRepository.findAll({
      where: { uid: In(accountIds), organizationId, isActive: true },
    });
  }

  /**
   * Finds accounts by their primary domains and organization ID.
   * @param primaryDomains The primary domains of the accounts to find.
   * @param organizationId The ID of the organization to find the accounts in.
   * @returns The accounts.
   */
  findAccountsByPrimaryDomains(
    primaryDomains: string[],
    organizationId: string,
  ): Promise&lt;Account[]&gt; {
    return this.accountRepository.findAll({
      where: {
        primaryDomain: In(primaryDomains),
        organizationId,
        isActive: true,
      },
    });
  }

  /**
   * Finds all accounts.
   * @param organizationId The ID of the organization to find the accounts in.
   * @returns The accounts.
   */
  async findAllAccounts(
    organizationId: string,
    query: FindAllAccountsDto,
  ): Promise&lt;Account[]&gt; {
    const whereClause: FindOptionsWhere&lt;Account&gt; &#x3D; {
      organizationId,
      isActive: true,
    };

    if (query.source) {
      whereClause.source &#x3D; query.source;
    }

    if (query.status) {
      const statusAttribute &#x3D; await this.validateAndGetAttributeValue(
        query.status,
        organizationId,
        AccountAttributeType.ACCOUNT_STATUS,
      );
      whereClause.status &#x3D; statusAttribute.id;
    }

    if (query.classification) {
      const classificationAttribute &#x3D; await this.validateAndGetAttributeValue(
        query.classification,
        organizationId,
        AccountAttributeType.ACCOUNT_CLASSIFICATION,
      );
      whereClause.classification &#x3D; classificationAttribute.id;
    }

    if (query.health) {
      const healthAttribute &#x3D; await this.validateAndGetAttributeValue(
        query.health,
        organizationId,
        AccountAttributeType.ACCOUNT_HEALTH,
      );
      whereClause.health &#x3D; healthAttribute.id;
    }

    if (query.industry) {
      const industryAttribute &#x3D; await this.validateAndGetAttributeValue(
        query.industry,
        organizationId,
        AccountAttributeType.ACCOUNT_INDUSTRY,
      );
      whereClause.industry &#x3D; industryAttribute.id;
    }

    if (query.accountOwnerId) {
      const accountOwner &#x3D; await this.usersService.findOneByPublicId(
        query.accountOwnerId,
      );

      if (!accountOwner) {
        throw new NotFoundException(&quot;Account owner not found&quot;);
      }

      whereClause.accountOwnerId &#x3D; accountOwner.id;
    }

    const { results: accounts } &#x3D;
      await this.accountRepository.fetchPaginatedResults(
        {
          page: query.page ?? 0,
          limit: Math.min(query.limit ?? 10, 100),
        },
        {
          where: whereClause,
          relations: [
            &quot;accountOwner&quot;,
            &quot;statusAttribute&quot;,
            &quot;classificationAttribute&quot;,
            &quot;healthAttribute&quot;,
            &quot;industryAttribute&quot;,
          ],
        },
      );
    return accounts;
  }

  /**
   * Finds the details of an account.
   * @param user The current user.
   * @param accountId The ID of the account to find.
   * @returns The account details.
   */
  async findAccountDetails(
    user: CurrentUser,
    accountId: string,
  ): Promise&lt;Account&gt; {
    const relations &#x3D; [
      &quot;accountOwner&quot;,
      &quot;statusAttribute&quot;,
      &quot;classificationAttribute&quot;,
      &quot;healthAttribute&quot;,
      &quot;industryAttribute&quot;,
      &quot;customFieldValues&quot;,
      &quot;customFieldValues.customField&quot;,
    ];
    const account &#x3D; await this.cachedAccountRepository.findByCondition({
      where: { uid: accountId, organizationId: user.orgId, isActive: true },
      relations,
    });
    if (!account) {
      throw new NotFoundException(&quot;Account not found!&quot;);
    }
    return account;
  }

  /**
   * Checks if an attribute value is in use.
   *
   * @param attributeValueId The ID of the attribute value
   * @param attributeType The attribute type
   * @returns Whether the attribute value is in use
   */
  async isAccountAttributeValueInUse(
    attributeValueId: string,
    attributeType: AccountAttributeType,
  ): Promise&lt;boolean&gt; {
    const attributeTypeColMapping &#x3D; {
      [AccountAttributeType.ACCOUNT_STATUS]: &quot;status&quot;,
      [AccountAttributeType.ACCOUNT_CLASSIFICATION]: &quot;classification&quot;,
      [AccountAttributeType.ACCOUNT_HEALTH]: &quot;health&quot;,
      [AccountAttributeType.ACCOUNT_INDUSTRY]: &quot;industry&quot;,
    };
    const colName &#x3D; attributeTypeColMapping[attributeType];

    if (!colName) {
      throw new BadRequestException(&quot;Invalid attribute type provided.&quot;);
    }

    const count &#x3D; await this.accountRepository.count({
      where: { [colName]: attributeValueId, isActive: true },
    });
    return count &gt; 0;
  }

  async updateAccountAttributeValue(
    attributeValueId: string,
    defaultAttributeValueId: string,
    attributeType: AccountAttributeType,
  ): Promise&lt;void&gt; {
    const attributeTypeColMapping &#x3D; {
      [AccountAttributeType.ACCOUNT_STATUS]: &quot;status&quot;,
      [AccountAttributeType.ACCOUNT_CLASSIFICATION]: &quot;classification&quot;,
      [AccountAttributeType.ACCOUNT_HEALTH]: &quot;health&quot;,
      [AccountAttributeType.ACCOUNT_INDUSTRY]: &quot;industry&quot;,
    };

    const colName &#x3D; attributeTypeColMapping[attributeType];
    if (!colName) {
      throw new BadRequestException(&quot;Invalid attribute type provided.&quot;);
    }

    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      // Find accounts using the previous account attribute
      const accounts &#x3D; await this.accountRepository.findAll({
        where: { [colName]: attributeValueId },
      });

      if (accounts.length &#x3D;&#x3D;&#x3D; 0) {
        return;
      }

      // Update the account attribute for each account
      for (const account of accounts) {
        account[colName] &#x3D; defaultAttributeValueId;
      }

      // Save the accounts
      await this.accountRepository.saveManyWithTxn(txnContext, accounts);

      // Invalidate the cache for each account
      accounts.forEach((account) &#x3D;&gt; {
        this.cachedAccountRepository.invalidateAccountCache({
          organizationId: account.organizationId,
          accountId: account.uid,
          primaryDomain: account.primaryDomain,
        });
      });
    });
  }

  private async findIfDomainIsUsed(
    domain: string,
    organizationId: string,
  ): Promise&lt;boolean&gt; {
    const count &#x3D; await this.accountRepository.count({
      where: [
        {
          organizationId,
          primaryDomain: domain,
          isActive: true,
        },
        {
          organizationId,
          secondaryDomain: domain,
          isActive: true,
        },
      ],
    });
    return count &gt; 0;
  }

  /**
   * Validates and gets an account task attribute value. (One of the task type, task status, or task priority)
   *
   * @param attributeValue The UID of the attribute value
   * @param organizationId The ID of the organization
   * @param attributeType The attribute type
   * @returns The account attribute value
   */
  private async validateAndGetAttributeValue(
    attributeValue: string,
    organizationId: string,
    attributeType: AccountAttributeType,
  ): Promise&lt;AccountAttributeValue&gt; {
    const attributeValueEntity &#x3D;
      await this.accountCommonService.getAttributeValue(
        attributeValue,
        organizationId,
      );

    if (
      !attributeValueEntity ||
      attributeValueEntity.attribute !&#x3D;&#x3D; attributeType
    ) {
      throw new BadRequestException(
        &#x60;Provided value for ${attributeType} is not valid!&#x60;,
      );
    }

    return attributeValueEntity;
  }

  /**
   * Creates a new account.
   *
   * @param currentUser The current user.
   * @param createAccountDto {@link CreateAccountDto} The DTO containing the account details.
   * @returns The created account.
   */
  async createAccount(
    currentUser: CurrentUser,
    createAccountDto: CreateAccountDto,
  ): Promise&lt;Account&gt; {
    const organizationId &#x3D; currentUser.orgId;

    const primaryDomainExists &#x3D; await this.findIfDomainIsUsed(
      createAccountDto.primaryDomain,
      organizationId,
    );
    if (primaryDomainExists) {
      throw new BadRequestException(
        &quot;Primary domain is already in use by another account!&quot;,
      );
    }

    let statusAttributeValue: AccountAttributeValue;
    if (createAccountDto.status) {
      statusAttributeValue &#x3D; await this.validateAndGetAttributeValue(
        createAccountDto.status,
        organizationId,
        AccountAttributeType.ACCOUNT_STATUS,
      );
    }

    let classificationAttributeValue: AccountAttributeValue;
    if (createAccountDto.classification) {
      classificationAttributeValue &#x3D; await this.validateAndGetAttributeValue(
        createAccountDto.classification,
        organizationId,
        AccountAttributeType.ACCOUNT_CLASSIFICATION,
      );
    }

    let healthAttributeValue: AccountAttributeValue;
    if (createAccountDto.health) {
      healthAttributeValue &#x3D; await this.validateAndGetAttributeValue(
        createAccountDto.health,
        organizationId,
        AccountAttributeType.ACCOUNT_HEALTH,
      );
    }

    let industryAttributeValue: AccountAttributeValue;
    if (createAccountDto.industry) {
      industryAttributeValue &#x3D; await this.validateAndGetAttributeValue(
        createAccountDto.industry,
        organizationId,
        AccountAttributeType.ACCOUNT_INDUSTRY,
      );
    }

    let accountOwner: User;
    if (createAccountDto.accountOwnerId) {
      accountOwner &#x3D; await this.usersService.findOneByPublicId(
        createAccountDto.accountOwnerId,
      );
      if (!accountOwner) {
        throw new BadRequestException(&quot;Account owner not found!&quot;);
      }
    }

    let customFieldValues: CustomFieldValues[];
    if (isArray(createAccountDto.customFieldValues)) {
      customFieldValues &#x3D;
        await this.customFieldValuesService.createCustomFieldValues(
          createAccountDto.customFieldValues,
          organizationId,
        );
    }

    if (createAccountDto.secondaryDomain) {
      const secondaryDomainExists &#x3D; await this.findIfDomainIsUsed(
        createAccountDto.secondaryDomain,
        organizationId,
      );
      if (secondaryDomainExists) {
        throw new BadRequestException(&quot;Secondary domain is already in use!&quot;);
      }
    }

    const account &#x3D; this.accountRepository.create({
      organizationId,
      accountOwnerId: accountOwner ? accountOwner.id : null,
      name: createAccountDto.name,
      description: createAccountDto.description ?? null,
      source: createAccountDto.source,
      primaryDomain: createAccountDto.primaryDomain,
      secondaryDomain: createAccountDto.secondaryDomain ?? null,
      logo: createAccountDto.logo ?? null,
      statusAttribute: statusAttributeValue ?? null,
      classificationAttribute: classificationAttributeValue ?? null,
      healthAttribute: healthAttributeValue ?? null,
      industryAttribute: industryAttributeValue ?? null,
      annualRevenue: createAccountDto.annualRevenue ?? null,
      employees: createAccountDto.employees ?? null,
      website: createAccountDto.website ?? null,
      billingAddress: createAccountDto.billingAddress ?? null,
      shippingAddress: createAccountDto.shippingAddress ?? null,
      customFieldValues,
      metadata: {},
    });

    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      // Save in db
      await this.accountRepository.saveWithTxn(txnContext, account);

      // Invalidate cache for the account with uid and primary domain
      await this.cachedAccountRepository.invalidateAccountCache({
        organizationId,
        accountId: account.uid,
        primaryDomain: account.primaryDomain,
      });

      // Record account created activity
      await this.activitiesService.recordAuditLog(
        {
          organization: { id: organizationId },
          activityPerformedBy: { id: currentUser.sub },
          entityId: account.id,
          entityUid: account.uid,
          entityType: AuditLogEntityType.ACCOUNT,
          op: AuditLogOp.CREATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: &#x60;Account ${account.id} was created!&#x60;,
          description: &#x60;Account ${account.id} with primary domain ${account.primaryDomain} was created by ${currentUser.email}!&#x60;,
        },
        txnContext,
      );

      // Publish to SNS
      const accountCreatedEvent &#x3D;
        this.accountsSNSEventsFactory.createAccountCreatedSNSEvent(
          currentUser,
          account,
        );

      this.accountCommonService.publishEventToSNSQueue(
        AccountEvents.ACCOUNT_CREATED,
        accountCreatedEvent,
        currentUser,
      );
    });

    if (createAccountDto.addExistingUsersToAccountContacts) {
      const triggerAccountContactsCreationEvent &#x3D;
        this.accountsEventsFactory.createTriggerAccountContactsCreationEvent(
          account.organizationId,
          account.uid,
          account.primaryDomain,
        );
      this.eventEmitter.emit(
        EmittableAccountEvents.LINK_CONTACTS_TO_ACCOUNT_BY_EMAIL_DOMAIN,
        triggerAccountContactsCreationEvent,
      );
    }

    // Return from cache
    return this.findAccountDetails(currentUser, account.uid);
  }

  /**
   * Updates an existing account.
   *
   * @param uid The ID of the account to update.
   * @param currentUser The current user.
   * @param updateAccountDto {@link UpdateAccountDto} The DTO containing the account details.
   * @returns The updated account.
   */
  async updateAccount(
    uid: string,
    currentUser: CurrentUser,
    updateAccountDto: UpdateAccountDto,
  ): Promise&lt;Account&gt; {
    const organizationId &#x3D; currentUser.orgId;

    const account &#x3D; await this.findAccountDetails(currentUser, uid);

    const existingAccount &#x3D; cloneDeep(account);

    if (!account) {
      throw new NotFoundException(&quot;Account not found!&quot;);
    }

    if (updateAccountDto.status) {
      const statusAttributeValue &#x3D; await this.validateAndGetAttributeValue(
        updateAccountDto.status,
        organizationId,
        AccountAttributeType.ACCOUNT_STATUS,
      );

      account.statusAttribute &#x3D; statusAttributeValue;
    }

    if (updateAccountDto.classification) {
      const classificationAttributeValue &#x3D;
        await this.validateAndGetAttributeValue(
          updateAccountDto.classification,
          organizationId,
          AccountAttributeType.ACCOUNT_CLASSIFICATION,
        );

      account.classificationAttribute &#x3D; classificationAttributeValue;
    }

    if (updateAccountDto.health) {
      const healthAttributeValue &#x3D; await this.validateAndGetAttributeValue(
        updateAccountDto.health,
        organizationId,
        AccountAttributeType.ACCOUNT_HEALTH,
      );

      account.healthAttribute &#x3D; healthAttributeValue;
    }

    if (updateAccountDto.industry) {
      const industryAttributeValue &#x3D; await this.validateAndGetAttributeValue(
        updateAccountDto.industry,
        organizationId,
        AccountAttributeType.ACCOUNT_INDUSTRY,
      );

      account.industryAttribute &#x3D; industryAttributeValue;
    }

    if (updateAccountDto.accountOwnerId) {
      const accountOwner &#x3D; await this.usersService.findOneByPublicId(
        updateAccountDto.accountOwnerId,
      );
      if (!accountOwner) {
        throw new BadRequestException(&quot;Account owner not found!&quot;);
      }

      account.accountOwner &#x3D; accountOwner;
    }

    if (updateAccountDto.primaryDomain) {
      const primaryDomainExists &#x3D; await this.findIfDomainIsUsed(
        updateAccountDto.primaryDomain,
        organizationId,
      );
      if (primaryDomainExists) {
        throw new BadRequestException(
          &quot;Primary domain is already in use by another account!&quot;,
        );
      }

      account.primaryDomain &#x3D; updateAccountDto.primaryDomain;
    }

    if (updateAccountDto.secondaryDomain) {
      const secondaryDomainExists &#x3D; await this.findIfDomainIsUsed(
        updateAccountDto.secondaryDomain,
        organizationId,
      );
      if (secondaryDomainExists) {
        throw new BadRequestException(
          &quot;Secondary domain is already in use by another account!&quot;,
        );
      }

      account.secondaryDomain &#x3D; updateAccountDto.secondaryDomain;
    }

    if (isArray(updateAccountDto.customFieldValues)) {
      const newCustomFieldValues: CustomFieldValues[] &#x3D;
        await this.customFieldValuesService.createCustomFieldValues(
          updateAccountDto.customFieldValues,
          organizationId,
        );

      const allowedPrevCustomFieldValues: CustomFieldValues[] &#x3D; [];
      const existingCustomFieldValues: CustomFieldValues[] &#x3D;
        account.customFieldValues ?? [];

      // For an account if C1 has A chosen and C2 has B chosen initially and update req comes with C1 and value C.
      // This means that for C1: A is removed and B is chosen and C2 remains unchanged.
      // In case of multiple choice, if C2 has D E F chosen initially and update req comes for C2 with value G.
      // This means D E F are removed and G is chosen.
      for (const customFieldValue of existingCustomFieldValues) {
        if (
          !newCustomFieldValues.find(
            (value) &#x3D;&gt; value.customField.id &#x3D;&#x3D;&#x3D; customFieldValue.customField.id,
          )
        ) {
          allowedPrevCustomFieldValues.push(customFieldValue);
        }
      }

      account.customFieldValues &#x3D; [
        ...allowedPrevCustomFieldValues,
        ...newCustomFieldValues,
      ];
    }

    account.name &#x3D; updateAccountDto.name ?? account.name;
    account.logo &#x3D; updateAccountDto.logo ?? account.logo;
    account.description &#x3D; updateAccountDto.description ?? account.description;
    account.annualRevenue &#x3D;
      updateAccountDto.annualRevenue ?? account.annualRevenue;
    account.employees &#x3D; updateAccountDto.employees ?? account.employees;
    account.website &#x3D; updateAccountDto.website ?? account.website;
    account.billingAddress &#x3D;
      updateAccountDto.billingAddress ?? account.billingAddress;
    account.shippingAddress &#x3D;
      updateAccountDto.shippingAddress ?? account.shippingAddress;

    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      // Save in db
      await this.accountRepository.saveWithTxn(txnContext, account);

      // Invalidate cache for the account with uid and primary domain
      await this.cachedAccountRepository.invalidateAccountCache({
        organizationId,
        accountId: account.uid,
        primaryDomain: account.primaryDomain,
      });

      // Record account updated activities
      await this.activitiesService.recordAuditLog(
        {
          organization: { id: organizationId },
          activityPerformedBy: { id: currentUser.sub },
          entityId: account.id,
          entityUid: account.uid,
          entityType: AuditLogEntityType.ACCOUNT,
          op: AuditLogOp.UPDATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: &#x60;Account ${account.id} was updated!&#x60;,
          description: &#x60;Account ${account.id} was updated by ${currentUser.email}!&#x60;,
          metadata: {
            updatedFields: [
              existingAccount.name !&#x3D;&#x3D; account.name &amp;&amp; {
                field: &quot;name&quot;,
                previousValue: existingAccount.name,
                updatedToValue: account.name,
              },
              existingAccount.logo !&#x3D;&#x3D; account.logo &amp;&amp; {
                field: &quot;logo&quot;,
                previousValue: existingAccount.logo,
                updatedToValue: account.logo,
              },
              existingAccount.accountOwnerId !&#x3D;&#x3D; account.accountOwnerId &amp;&amp; {
                field: &quot;accountOwnerId&quot;,
                previousValue: existingAccount.accountOwnerId,
                updatedToValue: account.accountOwnerId,
              },
              existingAccount.primaryDomain !&#x3D;&#x3D; account.primaryDomain &amp;&amp; {
                field: &quot;primaryDomain&quot;,
                previousValue: existingAccount.primaryDomain,
                updatedToValue: account.primaryDomain,
              },
              existingAccount.status !&#x3D;&#x3D; account.status &amp;&amp; {
                field: &quot;status&quot;,
                previousValue: existingAccount.status,
                updatedToValue: account.status,
              },
              existingAccount.classification !&#x3D;&#x3D; account.classification &amp;&amp; {
                field: &quot;classification&quot;,
                previousValue: existingAccount.classification,
                updatedToValue: account.classification,
              },
              existingAccount.health !&#x3D;&#x3D; account.health &amp;&amp; {
                field: &quot;health&quot;,
                previousValue: existingAccount.health,
                updatedToValue: account.health,
              },
            ].filter(Boolean),
          },
        },
        txnContext,
      );

      // Publish events to SNS
      const accountUpdatedEvent &#x3D;
        this.accountsSNSEventsFactory.createAccountUpdatedSNSEvent(
          currentUser,
          existingAccount,
          account,
        );

      this.accountCommonService.publishEventToSNSQueue(
        AccountEvents.ACCOUNT_UPDATED,
        accountUpdatedEvent,
        currentUser,
      );
    });

    if (
      existingAccount.primaryDomain !&#x3D;&#x3D; account.primaryDomain &amp;&amp;
      updateAccountDto.addExistingUsersToAccountContacts
    ) {
      const triggerAccountContactsCreationEvent &#x3D;
        this.accountsEventsFactory.createTriggerAccountContactsCreationEvent(
          account.organizationId,
          account.uid,
          account.primaryDomain,
        );
      this.eventEmitter.emit(
        EmittableAccountEvents.LINK_CONTACTS_TO_ACCOUNT_BY_EMAIL_DOMAIN,
        triggerAccountContactsCreationEvent,
      );
    }

    // Return from cache
    return this.findAccountDetails(currentUser, account.uid);
  }

  /**
   * Deletes an existing account.
   *
   * @param uid The UID of the account to delete.
   * @param currentUser The current user.
   */
  async deleteAccount(uid: string, currentUser: CurrentUser): Promise&lt;void&gt; {
    const organizationId &#x3D; currentUser.orgId;

    const account &#x3D; await this.findOneByAccountId(uid, organizationId);
    if (!account) {
      throw new NotFoundException(&quot;Account not found!&quot;);
    }

    account.isActive &#x3D; false;
    account.deletedAt &#x3D; new Date();

    return await this.transactionService.runInTransaction(
      async (txnContext) &#x3D;&gt; {
        // Save in db
        await this.accountRepository.saveWithTxn(txnContext, account);

        // Invalidate cache for the account with uid and primary domain
        await this.cachedAccountRepository.invalidateAccountCache({
          organizationId,
          accountId: account.uid,
          primaryDomain: account.primaryDomain,
        });

        // Record account deleted activity
        await this.activitiesService.recordAuditLog(
          {
            organization: { id: organizationId },
            activityPerformedBy: { id: currentUser.sub },
            entityId: account.id,
            entityUid: account.uid,
            entityType: AuditLogEntityType.ACCOUNT,
            op: AuditLogOp.DELETED,
            visibility: AuditLogVisibility.ORGANIZATION,
            activity: &#x60;Account ${account.id} was deleted!&#x60;,
            description: &#x60;Account ${account.id} was deleted by ${currentUser.email}!&#x60;,
            metadata: {
              updatedFields: [
                {
                  field: &quot;isActive&quot;,
                  previousValue: &quot;true&quot;,
                  updatedToValue: &quot;false&quot;,
                },
                {
                  field: &quot;deletedAt&quot;,
                  previousValue: null,
                  updatedToValue: account.deletedAt.toISOString(),
                },
              ],
            },
          },
          txnContext,
        );

        // Publish to SNS
        const accountDeletedEvent &#x3D;
          this.accountsSNSEventsFactory.createAccountDeletedSNSEvent(
            currentUser,
            account,
          );

        this.accountCommonService.publishEventToSNSQueue(
          AccountEvents.ACCOUNT_DELETED,
          accountDeletedEvent,
          currentUser,
        );

        return;
      },
    );
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'AccountsService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
