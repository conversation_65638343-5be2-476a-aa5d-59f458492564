<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >AccountRelationshipActionService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/accounts/services/account-relationship.action.service.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createAccountRelationship" >createAccountRelationship</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createAccountRelationshipType" >createAccountRelationshipType</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#deleteAccountRelationship" >deleteAccountRelationship</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#deleteAccountRelationshipType" >deleteAccountRelationshipType</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#findAccountRelationship" >findAccountRelationship</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#findAccountRelationshipByUID" >findAccountRelationshipByUID</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#findAccountRelationshipTypeByUID" >findAccountRelationshipTypeByUID</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findAllAccountRelationships" >findAllAccountRelationships</a>
                            </li>
                            <li>
                                <a href="#findAllAccountRelationshipTypes" >findAllAccountRelationshipTypes</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#hasCyclicRelationship" >hasCyclicRelationship</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateAccountRelationship" >updateAccountRelationship</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateAccountRelationshipType" >updateAccountRelationshipType</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(accountRelationshipTypeRepository: AccountRelationshipTypeRepository, cachedAccountRelationshipTypeRepository: CachedAccountRelationshipTypeRepository, accountRelationshipRepository: AccountRelationshipRepository, cachedAccountRelationshipRepository: CachedAccountRelationshipRepository, accountCommonService: <a href="../injectables/AccountCommonService.html" target="_self">AccountCommonService</a>, transactionService: TransactionService, activitiesService: <a href="../injectables/ActivitiesService.html" target="_self">ActivitiesService</a>, accountsSNSEventsFactory: <a href="../classes/AccountsSNSEventsFactory.html" target="_self">AccountsSNSEventsFactory</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="35" class="link-to-prism">src/accounts/services/account-relationship.action.service.ts:35</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>accountRelationshipTypeRepository</td>
                                                  
                                                        <td>
                                                                    <code>AccountRelationshipTypeRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>cachedAccountRelationshipTypeRepository</td>
                                                  
                                                        <td>
                                                                    <code>CachedAccountRelationshipTypeRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>accountRelationshipRepository</td>
                                                  
                                                        <td>
                                                                    <code>AccountRelationshipRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>cachedAccountRelationshipRepository</td>
                                                  
                                                        <td>
                                                                    <code>CachedAccountRelationshipRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>accountCommonService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/AccountCommonService.html" target="_self" >AccountCommonService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>transactionService</td>
                                                  
                                                        <td>
                                                                    <code>TransactionService</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>activitiesService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/ActivitiesService.html" target="_self" >ActivitiesService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>accountsSNSEventsFactory</td>
                                                  
                                                        <td>
                                                                        <code><a href="../classes/AccountsSNSEventsFactory.html" target="_self" >AccountsSNSEventsFactory</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createAccountRelationship"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>createAccountRelationship</b></span>
                        <a href="#createAccountRelationship"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createAccountRelationship(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, createDto: <a href="../classes/CreateAccountRelationshipDto.html" target="_self">CreateAccountRelationshipDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="692"
                                    class="link-to-prism">src/accounts/services/account-relationship.action.service.ts:692</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Create a new account relationship</p>
<ul>
<li>If cyclic relationship detected, throws BadRequestException</li>
<li>If inverse relationship exists for the relationship type, creates inverse relationship</li>
</ul>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>The current user</li>
</ul>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>createDto</td>
                                            <td>
                                                            <code><a href="../classes/CreateAccountRelationshipDto.html" target="_self" >CreateAccountRelationshipDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>The create DTO</li>
</ul>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;AccountRelationship&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The created account relationship</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createAccountRelationshipType"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>createAccountRelationshipType</b></span>
                        <a href="#createAccountRelationshipType"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createAccountRelationshipType(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, createDto: <a href="../classes/CreateAccountRelationshipTypeDto.html" target="_self">CreateAccountRelationshipTypeDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="150"
                                    class="link-to-prism">src/accounts/services/account-relationship.action.service.ts:150</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Creates a new account relationship type</p>
<ul>
<li>If inverse relationship specified, links them</li>
<li>If inverse relationship for specified relationship type already exists, throws BadRequestException</li>
</ul>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>The current user</li>
</ul>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>createDto</td>
                                            <td>
                                                            <code><a href="../classes/CreateAccountRelationshipTypeDto.html" target="_self" >CreateAccountRelationshipTypeDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>The create DTO</li>
</ul>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;AccountRelationshipType&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The created account relationship type</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteAccountRelationship"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>deleteAccountRelationship</b></span>
                        <a href="#deleteAccountRelationship"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteAccountRelationship(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, relationshipUID: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="1133"
                                    class="link-to-prism">src/accounts/services/account-relationship.action.service.ts:1133</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Deletes an existing account relationship</p>
<ul>
<li>If inverse relationship exists, deactivates the inverse relationship</li>
</ul>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>The current user</li>
</ul>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>relationshipUID</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>The UID of the relationship</li>
</ul>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;void&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteAccountRelationshipType"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>deleteAccountRelationshipType</b></span>
                        <a href="#deleteAccountRelationshipType"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteAccountRelationshipType(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, relationshipTypeUID: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="476"
                                    class="link-to-prism">src/accounts/services/account-relationship.action.service.ts:476</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Deletes an existing account relationship type</p>
<ul>
<li>If relationship type has active relationships, throws BadRequestException</li>
<li>Marks the relationship type as inactive</li>
<li>Removes inverse relationship for the current relationship type if exists</li>
<li>Removes inverse relationship for the inverse relationship type if exists</li>
</ul>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>The current user</li>
</ul>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>relationshipTypeUID</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>The UID of the relationship type</li>
</ul>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;void&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAccountRelationship"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>findAccountRelationship</b></span>
                        <a href="#findAccountRelationship"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAccountRelationship(accountId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, relatedAccountId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="663"
                                    class="link-to-prism">src/accounts/services/account-relationship.action.service.ts:663</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>accountId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>relatedAccountId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;AccountRelationship&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAccountRelationshipByUID"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>findAccountRelationshipByUID</b></span>
                        <a href="#findAccountRelationshipByUID"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAccountRelationshipByUID(uid: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="673"
                                    class="link-to-prism">src/accounts/services/account-relationship.action.service.ts:673</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>uid</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;AccountRelationship&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAccountRelationshipTypeByUID"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>findAccountRelationshipTypeByUID</b></span>
                        <a href="#findAccountRelationshipTypeByUID"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAccountRelationshipTypeByUID(uid: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="126"
                                    class="link-to-prism">src/accounts/services/account-relationship.action.service.ts:126</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>uid</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;AccountRelationshipType&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAllAccountRelationships"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findAllAccountRelationships</b></span>
                        <a href="#findAllAccountRelationships"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAllAccountRelationships(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, findAllRelationshipsDto: <a href="../classes/FindAccountRelationshipDto.html" target="_self">FindAccountRelationshipDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="615"
                                    class="link-to-prism">src/accounts/services/account-relationship.action.service.ts:615</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Find all active account relationships for an account</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>The current user</li>
</ul>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>findAllRelationshipsDto</td>
                                            <td>
                                                            <code><a href="../classes/FindAccountRelationshipDto.html" target="_self" >FindAccountRelationshipDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;AccountRelationship[]&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>A promise that resolves to an array of account relationships</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAllAccountRelationshipTypes"></a>
                    <span class="name">
                        <span ><b>findAllAccountRelationshipTypes</b></span>
                        <a href="#findAllAccountRelationshipTypes"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findAllAccountRelationshipTypes(organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="114"
                                    class="link-to-prism">src/accounts/services/account-relationship.action.service.ts:114</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds all active account relationship types for an organization</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>The ID of the organization</li>
</ul>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;AccountRelationshipType[]&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>A promise that resolves to an array of account relationship types</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="hasCyclicRelationship"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>hasCyclicRelationship</b></span>
                        <a href="#hasCyclicRelationship"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>hasCyclicRelationship(accountId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, relatedAccountId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, relationshipTypeId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="56"
                                    class="link-to-prism">src/accounts/services/account-relationship.action.service.ts:56</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>accountId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>relatedAccountId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>relationshipTypeId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;boolean&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateAccountRelationship"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>updateAccountRelationship</b></span>
                        <a href="#updateAccountRelationship"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateAccountRelationship(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, relationshipUID: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, updateDto: <a href="../classes/UpdateAccountRelationshipDto.html" target="_self">UpdateAccountRelationshipDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="863"
                                    class="link-to-prism">src/accounts/services/account-relationship.action.service.ts:863</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Updates an existing account relationship (updates only relationship type between two accounts)</p>
<ul>
<li>If existing relationship type and new relationship type both have inverse relationship, updates inverse relationship to new relationship type&#39;s inverse relationship</li>
<li>If existing relationship type does not have inverse relationship, creates inverse relationship with new relationship type&#39;s inverse relationship</li>
<li>If new relationship type does not have inverse relationship, deactivates the existing inverse relationship</li>
</ul>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>The current user</li>
</ul>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>relationshipUID</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>The UID of the relationship=</li>
</ul>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>updateDto</td>
                                            <td>
                                                            <code><a href="../classes/UpdateAccountRelationshipDto.html" target="_self" >UpdateAccountRelationshipDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>The update DTO</li>
</ul>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;AccountRelationship&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The updated account relationship</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateAccountRelationshipType"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>updateAccountRelationshipType</b></span>
                        <a href="#updateAccountRelationshipType"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateAccountRelationshipType(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, relationshipTypeUID: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, updateDto: <a href="../classes/UpdateAccountRelationshipTypeDto.html" target="_self">UpdateAccountRelationshipTypeDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="287"
                                    class="link-to-prism">src/accounts/services/account-relationship.action.service.ts:287</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Updates an existing account relationship type</p>
<ul>
<li>If inverse relationship is specified as null<ul>
<li>Removes existing inverse relationship</li>
<li>Marks inverse relationship as null for the current inverse relationship type</li>
</ul>
</li>
<li>If inverse relationship is specified<ul>
<li>Attaches a new inverse relationship to the current relationship type</li>
<li>Marks current relationship type as inverse relationship for new inverse relationship type</li>
<li>Marks inverse relationship as null for the current inverse relationship type</li>
</ul>
</li>
</ul>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>The current user</li>
</ul>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>relationshipTypeUID</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>The UID of the relationship type</li>
</ul>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>updateDto</td>
                                            <td>
                                                            <code><a href="../classes/UpdateAccountRelationshipTypeDto.html" target="_self" >UpdateAccountRelationshipTypeDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>The update DTO</li>
</ul>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;AccountRelationshipType&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The updated account relationship type</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from &quot;@nestjs/common&quot;;
import { AccountEvents } from &quot;@repo/thena-eventbridge&quot;;
import {
  AccountRelationship,
  AccountRelationshipRepository,
  AccountRelationshipType,
  AccountRelationshipTypeRepository,
  AuditLogEntityType,
  AuditLogOp,
  AuditLogVisibility,
  CachedAccountRelationshipRepository,
  CachedAccountRelationshipTypeRepository,
  TransactionService,
  UserType,
} from &quot;@repo/thena-platform-entities&quot;;
import { cloneDeep } from &quot;lodash&quot;;
import { DeepPartial, FindOptionsWhere } from &quot;typeorm&quot;;
import { ActivitiesService } from &quot;../../activities/services/activities.service&quot;;
import { CurrentUser } from &quot;../../common/decorators&quot;;
import {
  CreateAccountRelationshipDto,
  CreateAccountRelationshipTypeDto,
  FindAccountRelationshipDto,
  UpdateAccountRelationshipDto,
  UpdateAccountRelationshipTypeDto,
} from &quot;../dtos/account-relationship.dto&quot;;
import { AccountsSNSEventsFactory } from &quot;../events/accounts-sns-events.factory&quot;;
import { AccountCommonService } from &quot;./account-commons.service&quot;;

@Injectable()
export class AccountRelationshipActionService {
  constructor(
    // Injected repositories
    private readonly accountRelationshipTypeRepository: AccountRelationshipTypeRepository,
    private readonly cachedAccountRelationshipTypeRepository: CachedAccountRelationshipTypeRepository,
    private readonly accountRelationshipRepository: AccountRelationshipRepository,
    private readonly cachedAccountRelationshipRepository: CachedAccountRelationshipRepository,

    // Accounts services
    private readonly accountCommonService: AccountCommonService,

    // Transaction service
    private readonly transactionService: TransactionService,

    // Activities service
    private readonly activitiesService: ActivitiesService,

    // Event emitter
    private readonly accountsSNSEventsFactory: AccountsSNSEventsFactory,
  ) {}

  private async hasCyclicRelationship(
    accountId: string,
    relatedAccountId: string,
    relationshipTypeId: string,
  ): Promise&lt;boolean&gt; {
    // Set to keep track of visited accounts
    const visited &#x3D; new Set&lt;string&gt;();
    // Queue to process accounts in BFS manner
    const queue: Array&lt;{ id: string; depth: number }&gt; &#x3D; [];
    const MAX_DEPTH &#x3D; 10; // Prevent infinite loops in deep relationships

    // Start with the account we&#x27;re trying to relate to (relatedAccountId)
    queue.push({ id: relatedAccountId, depth: 0 });

    while (queue.length &gt; 0) {
      const { id: currentAccountId, depth } &#x3D; queue.shift()!;

      // Skip if we&#x27;ve reached max depth or already visited this account
      if (depth &gt;&#x3D; MAX_DEPTH || visited.has(currentAccountId)) {
        continue;
      }

      visited.add(currentAccountId);

      // Get all relationships where current account is the primary account
      const relationships &#x3D; await this.accountRelationshipRepository.findAll({
        where: {
          accountId: currentAccountId,
          relationship: relationshipTypeId, // Only check relationships of same type
          isActive: true,
        },
      });

      for (const relationship of relationships) {
        // If we find the original account in the chain, we have a cycle
        if (relationship.relatedAccountId &#x3D;&#x3D;&#x3D; accountId) {
          return true;
        }

        // Add related account to queue for processing
        if (!visited.has(relationship.relatedAccountId)) {
          queue.push({
            id: relationship.relatedAccountId,
            depth: depth + 1,
          });
        }
      }
    }

    return false;
  }

  /**
   * Finds all active account relationship types for an organization
   *
   * @param organizationId - The ID of the organization
   * @returns A promise that resolves to an array of account relationship types
   */
  findAllAccountRelationshipTypes(
    organizationId: string,
  ): Promise&lt;AccountRelationshipType[]&gt; {
    return this.cachedAccountRelationshipTypeRepository.findAll({
      where: {
        organizationId,
        isActive: true,
      },
      relations: [&quot;inverseRelationship&quot;],
    });
  }

  private findAccountRelationshipTypeByUID(
    uid: string,
    organizationId: string,
  ): Promise&lt;AccountRelationshipType&gt; {
    return this.cachedAccountRelationshipTypeRepository.findByCondition({
      where: {
        uid,
        organizationId,
        isActive: true,
      },
      relations: [&quot;inverseRelationship&quot;],
    });
  }

  /**
   * Creates a new account relationship type
   *
   * - If inverse relationship specified, links them
   * - If inverse relationship for specified relationship type already exists, throws BadRequestException
   *
   * @param user - The current user
   * @param createDto - The create DTO
   * @returns The created account relationship type
   */
  async createAccountRelationshipType(
    user: CurrentUser,
    createDto: CreateAccountRelationshipTypeDto,
  ): Promise&lt;AccountRelationshipType&gt; {
    const organizationId &#x3D; user.orgId;

    const savedType &#x3D; await this.transactionService.runInTransaction(
      async (txnContext) &#x3D;&gt; {
        const uidsToInvalidate: string[] &#x3D; [];

        // Create the relationship type
        const relationshipType &#x3D; this.accountRelationshipTypeRepository.create({
          organizationId,
          name: createDto.name,
          isActive: true,
        });

        const savedRelationshipType &#x3D;
          await this.accountRelationshipTypeRepository.saveWithTxn(
            txnContext,
            relationshipType,
          );

        uidsToInvalidate.push(savedRelationshipType.uid);

        // If inverse relationship specified, link them
        if (createDto.inverseRelationshipId) {
          const inverseType &#x3D;
            await this.accountRelationshipTypeRepository.findByCondition({
              where: {
                uid: createDto.inverseRelationshipId,
                organizationId,
                isActive: true,
              },
            });

          if (!inverseType) {
            throw new NotFoundException(&quot;Inverse relationship type not found&quot;);
          }

          // If inverse relationship for specified inverse relationship type already exists, throw BadRequestException
          if (inverseType.inverseRelationshipId) {
            throw new BadRequestException(
              &#x60;Relationship type ${inverseType.name} already has an inverse relationship&#x60;,
            );
          }

          // Update both types with inverse relationship
          savedRelationshipType.inverseRelationship &#x3D; inverseType;
          inverseType.inverseRelationship &#x3D; savedRelationshipType;

          uidsToInvalidate.push(inverseType.uid);

          await this.accountRelationshipTypeRepository.saveWithTxn(
            txnContext,
            inverseType,
          );
          await this.accountRelationshipTypeRepository.saveWithTxn(
            txnContext,
            savedRelationshipType,
          );

          // Record audit log
          await this.activitiesService.recordAuditLog(
            {
              organization: { id: organizationId },
              activityPerformedBy:
                user.userType &#x3D;&#x3D;&#x3D; UserType.BOT_USER ? null : { id: user.sub },
              isAutomated: user.userType &#x3D;&#x3D;&#x3D; UserType.BOT_USER,
              entityId: inverseType.id,
              entityUid: inverseType.uid,
              entityType: AuditLogEntityType.ACCOUNT_RELATIONSHIP_TYPE,
              op: AuditLogOp.UPDATED,
              visibility: AuditLogVisibility.ORGANIZATION,
              activity: &#x60;Inverse relationship type for ${inverseType.name} was added!&#x60;,
              description: &#x60;Inverse relationship type for ${inverseType.name} was added by ${user.email}!&#x60;,
              metadata: {
                updatedFields: [
                  {
                    field: &quot;inverseRelationshipId&quot;,
                    previousValue: null,
                    updatedToValue: savedRelationshipType.uid,
                  },
                ],
              },
            },
            txnContext,
          );
        }

        // Invalidate cache
        await this.cachedAccountRelationshipTypeRepository.invalidateAccountRelationshipTypeCache(
          {
            organizationId,
            uids: uidsToInvalidate,
          },
        );

        await this.activitiesService.recordAuditLog(
          {
            organization: { id: organizationId },
            activityPerformedBy: { id: user.sub },
            entityId: savedRelationshipType.id,
            entityUid: savedRelationshipType.uid,
            entityType: AuditLogEntityType.ACCOUNT_RELATIONSHIP_TYPE,
            op: AuditLogOp.CREATED,
            visibility: AuditLogVisibility.ORGANIZATION,
            activity: &#x60;Account relationship type ${savedRelationshipType.name} was created!&#x60;,
            description: &#x60;Account relationship type ${savedRelationshipType.name} was created by ${user.email}!&#x60;,
          },
          txnContext,
        );

        return savedRelationshipType;
      },
    );

    // Return from cache
    return this.findAccountRelationshipTypeByUID(savedType.uid, organizationId);
  }

  /**
   * Updates an existing account relationship type
   *
   * - If inverse relationship is specified as null
   * 	 - Removes existing inverse relationship
   *   - Marks inverse relationship as null for the current inverse relationship type
   * - If inverse relationship is specified
   *   - Attaches a new inverse relationship to the current relationship type
   *   - Marks current relationship type as inverse relationship for new inverse relationship type
   *   - Marks inverse relationship as null for the current inverse relationship type
   *
   * @param user - The current user
   * @param relationshipTypeUID - The UID of the relationship type
   * @param updateDto - The update DTO
   * @returns The updated account relationship type
   */
  async updateAccountRelationshipType(
    user: CurrentUser,
    relationshipTypeUID: string,
    updateDto: UpdateAccountRelationshipTypeDto,
  ): Promise&lt;AccountRelationshipType&gt; {
    const organizationId &#x3D; user.orgId;

    // Find existing relationship type from cache
    const existingType &#x3D;
      await this.cachedAccountRelationshipTypeRepository.findByCondition({
        where: { uid: relationshipTypeUID, organizationId, isActive: true },
      });

    if (!existingType) {
      throw new NotFoundException(&quot;Relationship type not found&quot;);
    }

    const savedType &#x3D; await this.transactionService.runInTransaction(
      async (txnContext) &#x3D;&gt; {
        const uidsToInvalidate: string[] &#x3D; [];

        uidsToInvalidate.push(existingType.uid);

        const updates: DeepPartial&lt;AccountRelationshipType&gt; &#x3D; {
          ...existingType,
          name: updateDto.name ?? existingType.name,
        };

        // Handle inverse relationship changes
        if (updateDto.inverseRelationshipId !&#x3D;&#x3D; undefined) {
          if (updateDto.inverseRelationshipId &#x3D;&#x3D;&#x3D; null) {
            // Remove existing inverse relationship
            updates.inverseRelationship &#x3D; null;
          } else {
            // Attach a new inverse relationship
            const newInverseType &#x3D;
              await this.accountRelationshipTypeRepository.findByCondition({
                where: {
                  uid: updateDto.inverseRelationshipId,
                  organizationId,
                  isActive: true,
                },
              });

            if (!newInverseType) {
              throw new NotFoundException(
                &quot;Inverse relationship type not found&quot;,
              );
            }

            // If inverse relationship for specified inverse relationship type already exists, throw BadRequestException
            if (newInverseType.inverseRelationshipId) {
              throw new BadRequestException(
                &#x60;Relationship type ${newInverseType.name} already has an inverse relationship&#x60;,
              );
            }

            updates.inverseRelationship &#x3D; newInverseType;
            newInverseType.inverseRelationship &#x3D; existingType;

            // Save new inverse relationship type
            await this.accountRelationshipTypeRepository.saveWithTxn(
              txnContext,
              newInverseType,
            );

            uidsToInvalidate.push(newInverseType.uid);

            // Record audit log
            await this.activitiesService.recordAuditLog(
              {
                organization: { id: organizationId },
                activityPerformedBy: { id: user.sub },
                entityId: newInverseType.id,
                entityUid: newInverseType.uid,
                entityType: AuditLogEntityType.ACCOUNT_RELATIONSHIP_TYPE,
                op: AuditLogOp.UPDATED,
                visibility: AuditLogVisibility.ORGANIZATION,
                activity: &#x60;Inverse relationship type for ${newInverseType.name} was added!&#x60;,
                description: &#x60;Inverse relationship type for ${newInverseType.name} was added by ${user.email}!&#x60;,
                metadata: {
                  updatedFields: [
                    {
                      field: &quot;inverseRelationshipId&quot;,
                      previousValue: null,
                      updatedToValue: existingType.id,
                    },
                  ],
                },
              },
              txnContext,
            );
          }

          // Remove inverse relationship from the current inverse relationship type
          const prevInverseType &#x3D;
            await this.accountRelationshipTypeRepository.findByCondition({
              where: {
                id: existingType.inverseRelationshipId,
                organizationId,
                isActive: true,
              },
            });

          prevInverseType.inverseRelationship &#x3D; null;

          // Save current inverse relationship type
          await this.accountRelationshipTypeRepository.saveWithTxn(
            txnContext,
            prevInverseType,
          );

          uidsToInvalidate.push(prevInverseType.uid);

          // Record audit log
          await this.activitiesService.recordAuditLog(
            {
              organization: { id: organizationId },
              activityPerformedBy: { id: user.sub },
              entityId: prevInverseType.id,
              entityUid: prevInverseType.uid,
              entityType: AuditLogEntityType.ACCOUNT_RELATIONSHIP_TYPE,
              op: AuditLogOp.UPDATED,
              visibility: AuditLogVisibility.ORGANIZATION,
              activity: &#x60;Inverse relationship type for ${prevInverseType.name} was added!&#x60;,
              description: &#x60;Inverse relationship type for ${prevInverseType.name} was added by ${user.email}!&#x60;,
              metadata: {
                updatedFields: [
                  {
                    field: &quot;inverseRelationshipId&quot;,
                    previousValue: existingType.id,
                    updatedToValue: null,
                  },
                ],
              },
            },
            txnContext,
          );
        }

        // Save updated relationship type
        const updatedType &#x3D;
          await this.accountRelationshipTypeRepository.saveWithTxn(
            txnContext,
            updates,
          );

        await this.activitiesService.recordAuditLog(
          {
            organization: { id: organizationId },
            activityPerformedBy: { id: user.sub },
            entityId: updatedType.id,
            entityUid: updatedType.uid,
            entityType: AuditLogEntityType.ACCOUNT_RELATIONSHIP_TYPE,
            op: AuditLogOp.UPDATED,
            visibility: AuditLogVisibility.ORGANIZATION,
            activity: &#x60;Account relationship type ${updatedType.name} was updated!&#x60;,
            description: &#x60;Account relationship type ${updatedType.name} was updated by ${user.email}!&#x60;,
          },
          txnContext,
        );

        // Invalidate cache
        await this.cachedAccountRelationshipTypeRepository.invalidateAccountRelationshipTypeCache(
          {
            organizationId,
            uids: uidsToInvalidate,
          },
        );

        return updatedType;
      },
    );

    // Return from cache
    return this.findAccountRelationshipTypeByUID(savedType.uid, organizationId);
  }

  /**
   * Deletes an existing account relationship type
   *
   * - If relationship type has active relationships, throws BadRequestException
   * - Marks the relationship type as inactive
   * - Removes inverse relationship for the current relationship type if exists
   * - Removes inverse relationship for the inverse relationship type if exists
   *
   * @param user - The current user
   * @param relationshipTypeUID - The UID of the relationship type
   */
  async deleteAccountRelationshipType(
    user: CurrentUser,
    relationshipTypeUID: string,
  ): Promise&lt;void&gt; {
    const organizationId &#x3D; user.orgId;

    // Find existing relationship type from cache
    const existingType &#x3D;
      await this.cachedAccountRelationshipTypeRepository.findByCondition({
        where: { uid: relationshipTypeUID, organizationId, isActive: true },
      });

    if (!existingType) {
      throw new NotFoundException(&quot;Relationship type not found&quot;);
    }

    // If relationship type has active relationships, throw BadRequestException
    const relationships &#x3D; await this.accountRelationshipRepository.findAll({
      where: { relationship: existingType.id, isActive: true },
    });

    if (relationships.length &gt; 0) {
      throw new BadRequestException(
        &quot;Cannot delete relationship type with active relationships&quot;,
      );
    }

    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      const uidsToInvalidate: string[] &#x3D; [];

      const inverseRelationshipId &#x3D; existingType.inverseRelationshipId;

      // Remove inverse relationship for the inverse relationship type if exists
      if (inverseRelationshipId) {
        const inverseType &#x3D;
          await this.accountRelationshipTypeRepository.findByCondition({
            where: { id: existingType.inverseRelationshipId },
          });

        if (inverseType) {
          // If relationship type has active relationships, throw BadRequestException
          const relationships &#x3D;
            await this.accountRelationshipRepository.findAll({
              where: { relationship: inverseType.id, isActive: true },
            });

          if (relationships.length &gt; 0) {
            throw new BadRequestException(
              &quot;Cannot delete relationship type as inverse relationship type has active relationships&quot;,
            );
          }

          inverseType.inverseRelationship &#x3D; null;

          // Save in db
          await this.accountRelationshipTypeRepository.saveWithTxn(
            txnContext,
            inverseType,
          );

          uidsToInvalidate.push(inverseType.uid);

          // Record audit log
          await this.activitiesService.recordAuditLog(
            {
              organization: { id: organizationId },
              activityPerformedBy: { id: user.sub },
              entityId: inverseType.id,
              entityUid: inverseType.uid,
              entityType: AuditLogEntityType.ACCOUNT_RELATIONSHIP_TYPE,
              op: AuditLogOp.UPDATED,
              visibility: AuditLogVisibility.ORGANIZATION,
              activity: &#x60;Inverse relationship type for ${inverseType.name} was removed!&#x60;,
              description: &#x60;Inverse relationship type for ${inverseType.name} was removed by ${user.email}!&#x60;,
            },
            txnContext,
          );
        }
      }

      // Mark the relationship type as inactive
      existingType.inverseRelationship &#x3D; null;
      existingType.isActive &#x3D; false;
      existingType.deletedAt &#x3D; new Date();

      uidsToInvalidate.push(existingType.uid);

      // Save in db
      await this.accountRelationshipTypeRepository.saveWithTxn(
        txnContext,
        existingType,
      );

      // Invalidate cache
      await this.cachedAccountRelationshipTypeRepository.invalidateAccountRelationshipTypeCache(
        {
          organizationId,
          uids: uidsToInvalidate,
        },
      );

      // Record audit log
      await this.activitiesService.recordAuditLog(
        {
          organization: { id: organizationId },
          activityPerformedBy: { id: user.sub },
          entityId: existingType.id,
          entityUid: existingType.uid,
          entityType: AuditLogEntityType.ACCOUNT_RELATIONSHIP_TYPE,
          op: AuditLogOp.DELETED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: &#x60;Account relationship type ${existingType.name} was deleted!&#x60;,
          description: &#x60;Account relationship type ${existingType.name} was deleted by ${user.email}!&#x60;,
          metadata: {
            updatedFields: [
              {
                field: &quot;isActive&quot;,
                previousValue: &quot;true&quot;,
                updatedToValue: &quot;false&quot;,
              },
              {
                field: &quot;inverseRelationshipId&quot;,
                previousValue: inverseRelationshipId,
                updatedToValue: null,
              },
            ],
          },
        },
        txnContext,
      );
    });
  }

  /**
   * Find all active account relationships for an account
   * @param user - The current user
   * @param accountId - The UID of the account
   * @returns A promise that resolves to an array of account relationships
   */
  async findAllAccountRelationships(
    user: CurrentUser,
    findAllRelationshipsDto: FindAccountRelationshipDto,
  ): Promise&lt;AccountRelationship[]&gt; {
    const whereClause: FindOptionsWhere&lt;AccountRelationship&gt; &#x3D; {
      account: {
        organizationId: user.orgId,
      },
      isActive: true,
    };

    if (findAllRelationshipsDto.accountId) {
      const account &#x3D; await this.accountCommonService.validateAndGetAccount(
        findAllRelationshipsDto.accountId,
        user.orgId,
      );

      whereClause.accountId &#x3D; account.id;
    }

    if (findAllRelationshipsDto.relationshipTypeId) {
      const relationshipType &#x3D; await this.findAccountRelationshipTypeByUID(
        findAllRelationshipsDto.relationshipTypeId,
        user.orgId,
      );

      if (!relationshipType) {
        throw new NotFoundException(&quot;Relationship type not found&quot;);
      }

      whereClause.relationship &#x3D; relationshipType.id;
    }

    const { results: relationships } &#x3D;
      await this.accountRelationshipRepository.fetchPaginatedResults(
        {
          page: findAllRelationshipsDto.page ?? 0,
          limit: Math.min(findAllRelationshipsDto.limit ?? 10, 100),
        },
        {
          where: whereClause,
          relations: [&quot;account&quot;, &quot;relatedAccount&quot;, &quot;relationshipType&quot;],
        },
      );

    return relationships;
  }

  private findAccountRelationship(
    accountId: string,
    relatedAccountId: string,
  ): Promise&lt;AccountRelationship&gt; {
    return this.cachedAccountRelationshipRepository.findByCondition({
      where: { accountId, relatedAccountId, isActive: true },
      relations: [&quot;account&quot;, &quot;relatedAccount&quot;, &quot;relationshipType&quot;],
    });
  }

  private findAccountRelationshipByUID(
    uid: string,
  ): Promise&lt;AccountRelationship&gt; {
    return this.cachedAccountRelationshipRepository.findByCondition({
      where: { uid, isActive: true },
      relations: [&quot;account&quot;, &quot;relatedAccount&quot;, &quot;relationshipType&quot;],
    });
  }

  /**
   * Create a new account relationship
   *
   * - If cyclic relationship detected, throws BadRequestException
   * - If inverse relationship exists for the relationship type, creates inverse relationship
   *
   * @param user - The current user
   * @param createDto - The create DTO
   * @returns The created account relationship
   */
  async createAccountRelationship(
    user: CurrentUser,
    createDto: CreateAccountRelationshipDto,
  ): Promise&lt;AccountRelationship&gt; {
    const organizationId &#x3D; user.orgId;

    const account &#x3D; await this.accountCommonService.validateAndGetAccount(
      createDto.accountId,
      organizationId,
    );

    const relatedAccount &#x3D;
      await this.accountCommonService.validateAndGetAccount(
        createDto.relatedAccountId,
        organizationId,
      );

    const relationshipType &#x3D;
      await this.accountRelationshipTypeRepository.findByCondition({
        where: {
          uid: createDto.relationshipType,
          organizationId,
          isActive: true,
        },
      });

    if (!relationshipType) {
      throw new NotFoundException(&quot;Relationship type not found&quot;);
    }

    const existingRelationship &#x3D; await this.findAccountRelationship(
      account.id,
      relatedAccount.id,
    );

    if (existingRelationship) {
      throw new BadRequestException(&quot;Relationship already exists&quot;);
    }

    if (relationshipType.inverseRelationshipId) {
      const hasCyclicRelationship &#x3D; await this.hasCyclicRelationship(
        account.id,
        relatedAccount.id,
        relationshipType.id,
      );

      if (hasCyclicRelationship) {
        throw new BadRequestException(&quot;Cyclic relationship detected&quot;);
      }
    }

    const savedRelationship &#x3D; await this.transactionService.runInTransaction(
      async (txnContext) &#x3D;&gt; {
        const relationship &#x3D; this.accountRelationshipRepository.create({
          accountId: account.id,
          relatedAccountId: relatedAccount.id,
          relationship: relationshipType.id,
          isActive: true,
        });

        const savedRelationship &#x3D;
          await this.accountRelationshipRepository.saveWithTxn(
            txnContext,
            relationship,
          );

        // Invalidate cache
        await this.cachedAccountRelationshipRepository.invalidateAccountRelationshipCache(
          {
            accountId: account.id,
            relatedAccountId: relatedAccount.id,
            uid: savedRelationship.uid,
          },
        );

        // Record audit log
        await this.activitiesService.recordAuditLog({
          organization: { id: organizationId },
          activityPerformedBy: { id: user.sub },
          entityId: savedRelationship.id,
          entityUid: savedRelationship.uid,
          entityType: AuditLogEntityType.ACCOUNT_RELATIONSHIP,
          op: AuditLogOp.CREATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: &#x60;Account relationship ${relationshipType.id} between ${account.id} and ${relatedAccount.id} was created!&#x60;,
          description: &#x60;Account relationship ${relationshipType.id} between ${account.id} and ${relatedAccount.id} was created by ${user.email}!&#x60;,
        });

        if (relationshipType.inverseRelationshipId) {
          const inverseRelationship &#x3D; this.accountRelationshipRepository.create(
            {
              accountId: relatedAccount.id,
              relatedAccountId: account.id,
              relationship: relationshipType.inverseRelationshipId,
              isActive: true,
            },
          );

          const savedInverseRelationship &#x3D;
            await this.accountRelationshipRepository.saveWithTxn(
              txnContext,
              inverseRelationship,
            );

          await this.cachedAccountRelationshipRepository.invalidateAccountRelationshipCache(
            {
              accountId: relatedAccount.id,
              relatedAccountId: account.id,
              uid: savedInverseRelationship.uid,
            },
          );

          // Record audit log
          await this.activitiesService.recordAuditLog({
            organization: { id: organizationId },
            activityPerformedBy: { id: user.sub },
            entityId: savedInverseRelationship.id,
            entityUid: savedInverseRelationship.uid,
            entityType: AuditLogEntityType.ACCOUNT_RELATIONSHIP,
            op: AuditLogOp.CREATED,
            visibility: AuditLogVisibility.ORGANIZATION,
            activity: &#x60;Account relationship ${relationshipType.inverseRelationshipId} between ${relatedAccount.id} and ${account.id} was created!&#x60;,
            description: &#x60;Account relationship ${relationshipType.inverseRelationshipId} between ${relatedAccount.id} and ${account.id} was created by ${user.email}!&#x60;,
          });

          const accountInverseRelationshipCreatedEvent &#x3D;
            this.accountsSNSEventsFactory.createAccountRelationshipCreatedSNSEvent(
              user,
              savedInverseRelationship,
            );

          this.accountCommonService.publishEventToSNSQueue(
            AccountEvents.ACCOUNT_RELATIONSHIP_CREATED,
            accountInverseRelationshipCreatedEvent,
            user,
          );
        }

        // Note: To be done at the end as we can&#x27;t roll back event published to SNS if something fails while creating inverse relationship
        // Publish account relationship created event to SNS
        const accountRelationshipCreatedEvent &#x3D;
          this.accountsSNSEventsFactory.createAccountRelationshipCreatedSNSEvent(
            user,
            savedRelationship,
          );

        this.accountCommonService.publishEventToSNSQueue(
          AccountEvents.ACCOUNT_RELATIONSHIP_CREATED,
          accountRelationshipCreatedEvent,
          user,
        );

        return savedRelationship;
      },
    );

    return this.findAccountRelationshipByUID(savedRelationship.uid);
  }

  /**
   * Updates an existing account relationship (updates only relationship type between two accounts)
   *
   * - If existing relationship type and new relationship type both have inverse relationship, updates inverse relationship to new relationship type&#x27;s inverse relationship
   * - If existing relationship type does not have inverse relationship, creates inverse relationship with new relationship type&#x27;s inverse relationship
   * - If new relationship type does not have inverse relationship, deactivates the existing inverse relationship
   *
   * @param user - The current user
   * @param relationshipUID - The UID of the relationship&#x3D;
   * @param updateDto - The update DTO
   * @returns The updated account relationship
   */
  async updateAccountRelationship(
    user: CurrentUser,
    relationshipUID: string,
    updateDto: UpdateAccountRelationshipDto,
  ): Promise&lt;AccountRelationship&gt; {
    const currentRelationship &#x3D; await this.findAccountRelationshipByUID(
      relationshipUID,
    );

    const prevRelationship &#x3D; cloneDeep(currentRelationship);

    if (!currentRelationship) {
      throw new NotFoundException(&quot;Relationship not found&quot;);
    }

    const prevRelationshipType &#x3D; currentRelationship.relationshipType;

    const newRelationshipType &#x3D; await this.findAccountRelationshipTypeByUID(
      updateDto.relationshipType,
      user.orgId,
    );

    if (!newRelationshipType) {
      throw new NotFoundException(&quot;Relationship type not found&quot;);
    }

    const savedRelation &#x3D; await this.transactionService.runInTransaction(
      async (txnContext) &#x3D;&gt; {
        const relationship &#x3D;
          await this.accountRelationshipRepository.saveWithTxn(txnContext, {
            ...currentRelationship,
            relationshipType: newRelationshipType,
          });

        if (
          prevRelationshipType.inverseRelationshipId &amp;&amp;
          newRelationshipType.inverseRelationshipId
        ) {
          // Update inverse relationship with new relationship type&#x27;s inverse relationship
          const existingInverseRelationship &#x3D;
            await this.findAccountRelationship(
              currentRelationship.relatedAccountId,
              currentRelationship.accountId,
            );

          const prevInverseRelationship &#x3D; cloneDeep(
            existingInverseRelationship,
          );

          existingInverseRelationship.relationshipType &#x3D;
            newRelationshipType.inverseRelationship;

          const inverseRelationship &#x3D;
            await this.accountRelationshipRepository.saveWithTxn(
              txnContext,
              existingInverseRelationship,
            );

          // Invalidate cache
          await this.cachedAccountRelationshipRepository.invalidateAccountRelationshipCache(
            {
              accountId: inverseRelationship.accountId,
              relatedAccountId: inverseRelationship.relatedAccountId,
              uid: inverseRelationship.uid,
            },
          );

          // Record audit log
          await this.activitiesService.recordAuditLog({
            organization: { id: user.orgId },
            activityPerformedBy: { id: user.sub },
            entityId: inverseRelationship.id,
            entityUid: inverseRelationship.uid,
            entityType: AuditLogEntityType.ACCOUNT_RELATIONSHIP,
            op: AuditLogOp.UPDATED,
            visibility: AuditLogVisibility.ORGANIZATION,
            activity: &#x60;Account relationship between ${currentRelationship.relatedAccountId} and ${currentRelationship.accountId} was updated to ${newRelationshipType.inverseRelationshipId}!&#x60;,
            description: &#x60;Account relationship between ${currentRelationship.relatedAccountId} and ${currentRelationship.accountId} was updated to ${newRelationshipType.inverseRelationshipId} by ${user.email}!&#x60;,
            metadata: {
              updatedFields: [
                {
                  field: &quot;relationship&quot;,
                  previousValue: prevRelationshipType.inverseRelationshipId,
                  updatedToValue: newRelationshipType.inverseRelationshipId,
                },
              ],
            },
          });

          // Publish account relationship type updated event to SNS
          const accountRelationshipTypeUpdatedEvent &#x3D;
            this.accountsSNSEventsFactory.createAccountRelationshipUpdatedSNSEvent(
              user,
              prevInverseRelationship,
              inverseRelationship,
            );

          this.accountCommonService.publishEventToSNSQueue(
            AccountEvents.ACCOUNT_RELATIONSHIP_UPDATED,
            accountRelationshipTypeUpdatedEvent,
            user,
          );
        } else if (prevRelationshipType.inverseRelationshipId) {
          // Mark inverse relationship inactive
          const existingInverseRelationship &#x3D;
            await this.findAccountRelationship(
              currentRelationship.relatedAccountId,
              currentRelationship.accountId,
            );

          existingInverseRelationship.isActive &#x3D; false;
          existingInverseRelationship.deletedAt &#x3D; new Date();

          const inverseRelationship &#x3D;
            await this.accountRelationshipRepository.saveWithTxn(
              txnContext,
              existingInverseRelationship,
            );

          // Invalidate cache
          await this.cachedAccountRelationshipRepository.invalidateAccountRelationshipCache(
            {
              accountId: inverseRelationship.accountId,
              relatedAccountId: inverseRelationship.relatedAccountId,
              uid: inverseRelationship.uid,
            },
          );

          // Record audit log
          await this.activitiesService.recordAuditLog({
            organization: { id: user.orgId },
            activityPerformedBy: { id: user.sub },
            entityId: inverseRelationship.id,
            entityUid: inverseRelationship.uid,
            entityType: AuditLogEntityType.ACCOUNT_RELATIONSHIP,
            op: AuditLogOp.DELETED,
            visibility: AuditLogVisibility.ORGANIZATION,
            activity: &#x60;Account relationship between ${currentRelationship.relatedAccountId} and ${currentRelationship.accountId} was removed!&#x60;,
            description: &#x60;Account relationship between ${currentRelationship.relatedAccountId} and ${currentRelationship.accountId} was removed by ${user.email}!&#x60;,
            metadata: {
              updatedFields: [
                {
                  field: &quot;isActive&quot;,
                  previousValue: &quot;true&quot;,
                  updatedToValue: &quot;false&quot;,
                },
                {
                  field: &quot;deletedAt&quot;,
                  previousValue: null,
                  updatedToValue: new Date().toISOString(),
                },
              ],
            },
          });

          // Publish account relationship deleted event to SNS
          const accountInverseRelationshipDeletedEvent &#x3D;
            this.accountsSNSEventsFactory.createAccountRelationshipDeletedSNSEvent(
              user,
              inverseRelationship,
            );

          this.accountCommonService.publishEventToSNSQueue(
            AccountEvents.ACCOUNT_RELATIONSHIP_DELETED,
            accountInverseRelationshipDeletedEvent,
            user,
          );
        } else if (newRelationshipType.inverseRelationshipId) {
          const inverseRelationship &#x3D; this.accountRelationshipRepository.create(
            {
              accountId: currentRelationship.relatedAccountId,
              relatedAccountId: currentRelationship.accountId,
              relationship: newRelationshipType.inverseRelationshipId,
              isActive: true,
            },
          );

          const savedInverseRelationship &#x3D;
            await this.accountRelationshipRepository.saveWithTxn(
              txnContext,
              inverseRelationship,
            );

          // Record audit log
          await this.activitiesService.recordAuditLog({
            organization: { id: user.orgId },
            activityPerformedBy: { id: user.sub },
            entityId: savedInverseRelationship.id,
            entityUid: savedInverseRelationship.uid,
            entityType: AuditLogEntityType.ACCOUNT_RELATIONSHIP,
            op: AuditLogOp.CREATED,
            visibility: AuditLogVisibility.ORGANIZATION,
            activity: &#x60;Account relationship ${newRelationshipType.inverseRelationshipId} between ${currentRelationship.relatedAccountId} and ${currentRelationship.accountId} was created!&#x60;,
            description: &#x60;Account relationship ${newRelationshipType.inverseRelationshipId} between ${currentRelationship.relatedAccountId} and ${currentRelationship.accountId} was created by ${user.email}!&#x60;,
          });

          // Publish account relationship created event to SNS
          const accountInverseRelationshipCreatedEvent &#x3D;
            this.accountsSNSEventsFactory.createAccountRelationshipCreatedSNSEvent(
              user,
              savedInverseRelationship,
            );

          this.accountCommonService.publishEventToSNSQueue(
            AccountEvents.ACCOUNT_RELATIONSHIP_CREATED,
            accountInverseRelationshipCreatedEvent,
            user,
          );
        }

        // Invalidate cache
        await this.cachedAccountRelationshipRepository.invalidateAccountRelationshipCache(
          {
            accountId: relationship.accountId,
            relatedAccountId: relationship.relatedAccountId,
            uid: relationship.uid,
          },
        );

        // Record audit log
        await this.activitiesService.recordAuditLog({
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityId: relationship.id,
          entityUid: relationship.uid,
          entityType: AuditLogEntityType.ACCOUNT_RELATIONSHIP,
          op: AuditLogOp.UPDATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: &#x60;Account relationship between ${relationship.accountId} and ${relationship.relatedAccountId} was updated to ${newRelationshipType.id}!&#x60;,
          description: &#x60;Account relationship between ${relationship.accountId} and ${relationship.relatedAccountId} was updated to ${newRelationshipType.id} by ${user.email}!&#x60;,
          metadata: {
            updatedFields: [
              {
                field: &quot;relationship&quot;,
                previousValue: prevRelationshipType.id,
                updatedToValue: newRelationshipType.id,
              },
            ],
          },
        });

        // Publish account relationship updated event to SNS
        const accountRelationshipTypeUpdatedEvent &#x3D;
          this.accountsSNSEventsFactory.createAccountRelationshipUpdatedSNSEvent(
            user,
            prevRelationship,
            relationship,
          );

        this.accountCommonService.publishEventToSNSQueue(
          AccountEvents.ACCOUNT_RELATIONSHIP_UPDATED,
          accountRelationshipTypeUpdatedEvent,
          user,
        );

        return relationship;
      },
    );

    return this.findAccountRelationshipByUID(savedRelation.uid);
  }

  /**
   * Deletes an existing account relationship
   *
   * - If inverse relationship exists, deactivates the inverse relationship
   *
   * @param user - The current user
   * @param relationshipUID - The UID of the relationship
   */
  async deleteAccountRelationship(
    user: CurrentUser,
    relationshipUID: string,
  ): Promise&lt;void&gt; {
    const currentRelationship &#x3D; await this.findAccountRelationshipByUID(
      relationshipUID,
    );

    if (!currentRelationship) {
      throw new NotFoundException(&quot;Relationship not found&quot;);
    }

    const existingRelationshipType &#x3D; currentRelationship.relationshipType;

    return await this.transactionService.runInTransaction(
      async (txnContext) &#x3D;&gt; {
        if (existingRelationshipType.inverseRelationshipId) {
          const inverseRelationship &#x3D; this.accountRelationshipRepository.create(
            {
              accountId: currentRelationship.relatedAccountId,
              relatedAccountId: currentRelationship.accountId,
              relationship: existingRelationshipType.inverseRelationshipId,
              isActive: false,
              deletedAt: new Date(),
            },
          );

          const savedInverseRelationship &#x3D;
            await this.accountRelationshipRepository.saveWithTxn(
              txnContext,
              inverseRelationship,
            );

          // Invalidate cache
          await this.cachedAccountRelationshipRepository.invalidateAccountRelationshipCache(
            {
              accountId: savedInverseRelationship.accountId,
              relatedAccountId: savedInverseRelationship.relatedAccountId,
              uid: savedInverseRelationship.uid,
            },
          );

          // Record audit log
          await this.activitiesService.recordAuditLog({
            organization: { id: user.orgId },
            activityPerformedBy: { id: user.sub },
            entityId: savedInverseRelationship.id,
            entityUid: savedInverseRelationship.uid,
            entityType: AuditLogEntityType.ACCOUNT_RELATIONSHIP,
            op: AuditLogOp.UPDATED,
            visibility: AuditLogVisibility.ORGANIZATION,
            activity: &#x60;Account relationship between ${savedInverseRelationship.accountId} and ${savedInverseRelationship.relatedAccountId} was removed!&#x60;,
            description: &#x60;Account relationship between ${savedInverseRelationship.accountId} and ${savedInverseRelationship.relatedAccountId} was removed by ${user.email}!&#x60;,
            metadata: {
              updatedFields: [
                {
                  field: &quot;isActive&quot;,
                  previousValue: &quot;true&quot;,
                  updatedToValue: &quot;false&quot;,
                },
                {
                  field: &quot;deletedAt&quot;,
                  previousValue: null,
                  updatedToValue: new Date().toISOString(),
                },
              ],
            },
          });

          // Publish account relationship deleted event to SNS
          const accountInverseRelationshipDeletedEvent &#x3D;
            this.accountsSNSEventsFactory.createAccountRelationshipDeletedSNSEvent(
              user,
              savedInverseRelationship,
            );

          this.accountCommonService.publishEventToSNSQueue(
            AccountEvents.ACCOUNT_RELATIONSHIP_DELETED,
            accountInverseRelationshipDeletedEvent,
            user,
          );
        }

        currentRelationship.isActive &#x3D; false;
        currentRelationship.deletedAt &#x3D; new Date();

        // Invalidate cache
        await this.cachedAccountRelationshipRepository.invalidateAccountRelationshipCache(
          {
            accountId: currentRelationship.accountId,
            relatedAccountId: currentRelationship.relatedAccountId,
            uid: currentRelationship.uid,
          },
        );

        // Record audit log
        await this.activitiesService.recordAuditLog({
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityId: currentRelationship.id,
          entityUid: currentRelationship.uid,
          entityType: AuditLogEntityType.ACCOUNT_RELATIONSHIP,
          op: AuditLogOp.UPDATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: &#x60;Account relationship between ${currentRelationship.accountId} and ${currentRelationship.relatedAccountId} was removed!&#x60;,
          description: &#x60;Account relationship between ${currentRelationship.accountId} and ${currentRelationship.relatedAccountId} was removed by ${user.email}!&#x60;,
          metadata: {
            updatedFields: [
              {
                field: &quot;isActive&quot;,
                previousValue: &quot;true&quot;,
                updatedToValue: &quot;false&quot;,
              },
              {
                field: &quot;deletedAt&quot;,
                previousValue: null,
                updatedToValue: new Date().toISOString(),
              },
            ],
          },
        });

        // Publish account relationship deleted event to SNS
        const accountRelationshipDeletedEvent &#x3D;
          this.accountsSNSEventsFactory.createAccountRelationshipDeletedSNSEvent(
            user,
            currentRelationship,
          );

        this.accountCommonService.publishEventToSNSQueue(
          AccountEvents.ACCOUNT_RELATIONSHIP_DELETED,
          accountRelationshipDeletedEvent,
          user,
        );

        return;
      },
    );
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'AccountRelationshipActionService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
