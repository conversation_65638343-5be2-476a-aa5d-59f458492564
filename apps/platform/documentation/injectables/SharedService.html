<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >SharedService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/shared/shared.service.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#addTeamMemberAsBot" >addTeamMemberAsBot</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#belongsToTeams" >belongsToTeams</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findAllTags" >findAllTags</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findAllTeams" >findAllTeams</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findFormsByFieldIds" >findFormsByFieldIds</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findOneTag" >findOneTag</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findOrganization" >findOrganization</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#removeTeamMembers" >removeTeamMembers</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#saveFormFieldEvents" >saveFormFieldEvents</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#seedDefaultStatusPriorityAndType" >seedDefaultStatusPriorityAndType</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(teamRepository: TeamRepository, teamMemberRepository: TeamMemberRepository, tagRepository: TagRepository, ticketStatusRepository: TicketStatusRepository, ticketPriorityRepository: TicketPriorityRepository, ticketTypeRepository: TicketTypeRepository, transactionService: TransactionService, organizationRepository: OrganizationRepository, formRepository: FormRepository, formFieldEventRepository: FormFieldEventRepository)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="30" class="link-to-prism">src/shared/shared.service.ts:30</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>teamRepository</td>
                                                  
                                                        <td>
                                                                    <code>TeamRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>teamMemberRepository</td>
                                                  
                                                        <td>
                                                                    <code>TeamMemberRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>tagRepository</td>
                                                  
                                                        <td>
                                                                    <code>TagRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>ticketStatusRepository</td>
                                                  
                                                        <td>
                                                                    <code>TicketStatusRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>ticketPriorityRepository</td>
                                                  
                                                        <td>
                                                                    <code>TicketPriorityRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>ticketTypeRepository</td>
                                                  
                                                        <td>
                                                                    <code>TicketTypeRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>transactionService</td>
                                                  
                                                        <td>
                                                                    <code>TransactionService</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>organizationRepository</td>
                                                  
                                                        <td>
                                                                    <code>OrganizationRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>formRepository</td>
                                                  
                                                        <td>
                                                                    <code>FormRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>formFieldEventRepository</td>
                                                  
                                                        <td>
                                                                    <code>FormFieldEventRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="addTeamMemberAsBot"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>addTeamMemberAsBot</b></span>
                        <a href="#addTeamMemberAsBot"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>addTeamMemberAsBot(teamId: string | Array<string>, userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, txnContext?: TransactionContext)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="173"
                                    class="link-to-prism">src/shared/shared.service.ts:173</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Adds a team member as a bot.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                        <code>string | Array&lt;string&gt;</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the team.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>userId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the user.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>txnContext</td>
                                            <td>
                                                        <code>TransactionContext</code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                                    <p>The transaction context.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The created team member.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="belongsToTeams"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>belongsToTeams</b></span>
                        <a href="#belongsToTeams"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>belongsToTeams(userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="157"
                                    class="link-to-prism">src/shared/shared.service.ts:157</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Checks if a user is part of any teams.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>userId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the user.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>True if the user is part of any teams, false otherwise.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAllTags"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findAllTags</b></span>
                        <a href="#findAllTags"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAllTags(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, query: <a href="../classes/GetAllTagsQuery.html" target="_self">GetAllTagsQuery</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="96"
                                    class="link-to-prism">src/shared/shared.service.ts:96</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds all tags for an organization.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>query</td>
                                            <td>
                                                            <code><a href="../classes/GetAllTagsQuery.html" target="_self" >GetAllTagsQuery</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;Tag[]&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>Array of tags.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAllTeams"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findAllTeams</b></span>
                        <a href="#findAllTeams"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAllTeams(teamIds: Array<string>, orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="121"
                                    class="link-to-prism">src/shared/shared.service.ts:121</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds all teams for an organization.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamIds</td>
                                            <td>
                                                        <code>Array&lt;string&gt;</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The IDs of the teams to find.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;Array&lt;Team&gt;&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>Array of teams.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findFormsByFieldIds"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findFormsByFieldIds</b></span>
                        <a href="#findFormsByFieldIds"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findFormsByFieldIds(fieldIds: Array<string>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="320"
                                    class="link-to-prism">src/shared/shared.service.ts:320</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds all forms that use any of the specified field IDs.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>fieldIds</td>
                                            <td>
                                                        <code>Array&lt;string&gt;</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The list of field IDs to search for.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;Array&lt;Form&gt;&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>Array of forms that use the specified field IDs.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findOneTag"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findOneTag</b></span>
                        <a href="#findOneTag"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findOneTag(tagId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="72"
                                    class="link-to-prism">src/shared/shared.service.ts:72</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds a tag by its ID.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>tagId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the tag to find.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;Tag&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The found tag.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findOrganization"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findOrganization</b></span>
                        <a href="#findOrganization"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findOrganization(orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="58"
                                    class="link-to-prism">src/shared/shared.service.ts:58</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="removeTeamMembers"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>removeTeamMembers</b></span>
                        <a href="#removeTeamMembers"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>removeTeamMembers(teamIds: Array<string>, userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, txnContext?: TransactionContext)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="216"
                                    class="link-to-prism">src/shared/shared.service.ts:216</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamIds</td>
                                            <td>
                                                        <code>Array&lt;string&gt;</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>userId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>txnContext</td>
                                            <td>
                                                        <code>TransactionContext</code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="saveFormFieldEvents"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>saveFormFieldEvents</b></span>
                        <a href="#saveFormFieldEvents"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>saveFormFieldEvents(formFieldEvents: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="339"
                                    class="link-to-prism">src/shared/shared.service.ts:339</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>formFieldEvents</td>
                                            <td>
                                                            <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="seedDefaultStatusPriorityAndType"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>seedDefaultStatusPriorityAndType</b></span>
                        <a href="#seedDefaultStatusPriorityAndType"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>seedDefaultStatusPriorityAndType(team: Team, existingTransaction: TransactionContext)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="251"
                                    class="link-to-prism">src/shared/shared.service.ts:251</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Seeds default statuses, priorities, and types for a team.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>team</td>
                                            <td>
                                                        <code>Team</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The team to seed.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>existingTransaction</td>
                                            <td>
                                                        <code>TransactionContext</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The existing transaction context.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Injectable, NotFoundException } from &quot;@nestjs/common&quot;;
import {
  Form,
  FormFieldEventRepository,
  FormRepository,
  Organization,
  OrganizationRepository,
  Tag,
  TagRepository,
  Team,
  TeamMember,
  TeamMemberRepository,
  TeamMemberRole,
  TeamRepository,
  TicketPriority,
  TicketPriorityRepository,
  TicketStatus,
  TicketStatusRepository,
  TicketType,
  TicketTypeRepository,
  TransactionContext,
  TransactionService,
} from &quot;@repo/thena-platform-entities&quot;;
import { DeepPartial, FindOptionsWhere, In } from &quot;typeorm&quot;;
import { CurrentUser } from &quot;../common/decorators&quot;;
import { GetAllTagsQuery } from &quot;../tags/dto&quot;;
import { priorities, statuses, types } from &quot;./constants&quot;;

@Injectable()
export class SharedService {
  constructor(
    // Team Repositories
    private readonly teamRepository: TeamRepository,

    // Team Member Repositories
    private readonly teamMemberRepository: TeamMemberRepository,

    // Tag Repositories
    private readonly tagRepository: TagRepository,

    // Ticket utility repositories
    private readonly ticketStatusRepository: TicketStatusRepository,
    private readonly ticketPriorityRepository: TicketPriorityRepository,
    private readonly ticketTypeRepository: TicketTypeRepository,

    // Injected services
    private readonly transactionService: TransactionService,

    // Organization Repository
    private readonly organizationRepository: OrganizationRepository,

    // Form Repository
    private readonly formRepository: FormRepository,
    private readonly formFieldEventRepository: FormFieldEventRepository,
  ) {}

  //TODO : REMOVE THIS LATER
  async findOrganization(orgId: string) {
    return await this.organizationRepository.findByCondition({
      where: {
        uid: orgId,
      },
    });
  }

  /**
   * Finds a tag by its ID.
   * @param tagId The ID of the tag to find.
   * @param user The current user.
   * @returns The found tag.
   */
  async findOneTag(tagId: string, user: CurrentUser): Promise&lt;Tag&gt; {
    // Find the tag by its ID
    const tag &#x3D; await this.tagRepository.findByCondition({
      where: {
        uid: tagId,
        organizationId: user.orgId,
        isActive: true,
      },
    });

    // If tag not found, throw an error
    if (!tag) {
      throw new NotFoundException(&quot;Tag not found&quot;);
    }

    return tag;
  }

  /**
   * Finds all tags for an organization.
   * @param user The current user.
   * @param type Optional tag type filter.
   * @returns Array of tags.
   */
  async findAllTags(user: CurrentUser, query: GetAllTagsQuery): Promise&lt;Tag[]&gt; {
    const whereClause: FindOptionsWhere&lt;Tag&gt; &#x3D; {
      organizationId: user.orgId,
      tagType: query.type,
      isActive: true,
    };

    const limit &#x3D; Math.min(query.limit ?? 10, 100);
    const { results: tags } &#x3D; await this.tagRepository.fetchPaginatedResults(
      { page: query.page ?? 0, limit },
      {
        where: whereClause,
        order: { createdAt: &quot;DESC&quot; },
      },
    );

    return tags;
  }

  /**
   * Finds all teams for an organization.
   * @param teamIds The IDs of the teams to find.
   * @param orgId The ID of the organization.
   * @returns Array of teams.
   */
  async findAllTeams(
    teamIds: Array&lt;string&gt;,
    orgId: string,
  ): Promise&lt;Array&lt;Team&gt;&gt; {
    // If no team IDs are provided, return an empty array
    if (!teamIds || teamIds.length &#x3D;&#x3D;&#x3D; 0) {
      return [];
    }

    let orgQuery: FindOptionsWhere&lt;Organization&gt; &#x3D; {
      id: orgId,
    };

    if (orgId.startsWith(&quot;E&quot;)) {
      orgQuery &#x3D; {
        uid: orgId,
      };
    }

    // Find the teams by their IDs
    const teams &#x3D; await this.teamRepository.findAll({
      where: {
        uid: In(teamIds),
        organization: orgQuery,
      },
    });

    return teams;
  }

  /**
   * Checks if a user is part of any teams.
   * @param userId The ID of the user.
   * @param orgId The ID of the organization.
   * @returns True if the user is part of any teams, false otherwise.
   */
  async belongsToTeams(userId: string, orgId: string) {
    const teamMembersCount &#x3D; await this.teamMemberRepository.count({
      where: { userId, organizationId: orgId },
    });

    return teamMembersCount;
  }

  /**
   * Adds a team member as a bot.
   * @param teamId The ID of the team.
   * @param userId The ID of the user.
   * @param orgId The ID of the organization.
   * @param txnContext The transaction context.
   * @returns The created team member.
   */
  async addTeamMemberAsBot(
    teamId: string | Array&lt;string&gt;,
    userId: string,
    orgId: string,
    txnContext?: TransactionContext,
  ) {
    const teamIds &#x3D; Array.isArray(teamId) ? teamId : [teamId];
    const writes: DeepPartial&lt;TeamMember&gt;[] &#x3D; teamIds.map((teamId) &#x3D;&gt; ({
      teamId,
      userId,
      organizationId: orgId,
      isActive: true,
      isBot: true,
      role: TeamMemberRole.BOT,
    }));

    let teamMembers: TeamMember[] &#x3D; [];
    try {
      // Run the transaction
      teamMembers &#x3D; await this.transactionService.runInTransaction(
        async (txnContext) &#x3D;&gt; {
          return await this.teamMemberRepository.saveManyWithTxn(
            txnContext,
            writes,
          );
        },
        txnContext,
      );
    } catch (error) {
      if (error.code &#x3D;&#x3D;&#x3D; &quot;23505&quot;) {
        teamMembers &#x3D; await this.teamMemberRepository.findAll({
          where: {
            teamId: In(teamIds),
            userId,
            organizationId: orgId,
          },
        });
      }
    }

    return teamMembers;
  }

  async removeTeamMembers(
    teamIds: Array&lt;string&gt;,
    userId: string,
    orgId: string,
    txnContext?: TransactionContext,
  ) {
    // Find all team members
    const teamMembers &#x3D; await this.teamMemberRepository.findAll({
      where: {
        teamId: In(teamIds),
        userId,
        organizationId: orgId,
      },
    });

    // If no team members are found, return
    if (!teamMembers || teamMembers.length &#x3D;&#x3D;&#x3D; 0) {
      return;
    }

    // Run the transaction
    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      // Delete the team members records
      await this.teamMemberRepository.removeManyWithTxn(
        txnContext,
        teamMembers,
      );
    }, txnContext);
  }

  /**
   * Seeds default statuses, priorities, and types for a team.
   * @param team The team to seed.
   * @param existingTransaction The existing transaction context.
   */
  async seedDefaultStatusPriorityAndType(
    team: Team,
    existingTransaction: TransactionContext,
  ) {
    // Seed statuses
    const statusesRecords: DeepPartial&lt;Array&lt;TicketStatus&gt;&gt; &#x3D; statuses.map(
      (status) &#x3D;&gt; {
        return {
          teamId: team.id,
          organizationId: team.organizationId,
          name: status.name,
          displayName: status.name,
          description: status.description,
          isDefault: status.default,
          order: status.order,
        };
      },
    );

    // Seed priorities
    const prioritiesRecords: DeepPartial&lt;Array&lt;TicketPriority&gt;&gt; &#x3D;
      priorities.map((priority) &#x3D;&gt; {
        return {
          teamId: team.id,
          organizationId: team.organizationId,
          name: priority.name,
          displayName: priority.name,
          description: priority.description,
          isDefault: priority.default,
        };
      });

    // Seed types
    const typesRecords: DeepPartial&lt;Array&lt;TicketType&gt;&gt; &#x3D; types.map((type) &#x3D;&gt; {
      return {
        teamId: team.id,
        organizationId: team.organizationId,
        name: type.name,
        description: type.description,
        isDefault: type.default,
        icon: type.icon,
        color: type.color,
      };
    });

    // Seed data on team creation inside a transaction
    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      // Seed statuses
      await this.ticketStatusRepository.saveManyWithTxn(
        txnContext,
        statusesRecords,
      );

      // Seed priorities
      await this.ticketPriorityRepository.saveManyWithTxn(
        txnContext,
        prioritiesRecords,
      );

      // Seed types
      await this.ticketTypeRepository.saveManyWithTxn(txnContext, typesRecords);
    }, existingTransaction);
  }

  /**
   * Finds all forms that use any of the specified field IDs.
   * @param fieldIds The list of field IDs to search for.
   * @returns Array of forms that use the specified field IDs.
   */
  async findFormsByFieldIds(fieldIds: Array&lt;string&gt;): Promise&lt;Array&lt;Form&gt;&gt; {
    if (!fieldIds || fieldIds.length &#x3D;&#x3D;&#x3D; 0) {
      return [];
    }

    const forms &#x3D; await this.formRepository.queryRaw(
      &#x60;SELECT *
		FROM forms
		WHERE EXISTS (
		  SELECT 1
		  FROM jsonb_array_elements(fields) AS field_data
		  WHERE field_data-&gt;&gt;&#x27;field&#x27; &#x3D; ANY($1)
		);&#x60;,
      [fieldIds],
    );

    return forms;
  }

  async saveFormFieldEvents(formFieldEvents: any) {
    const fieldEvent &#x3D;
      this.formFieldEventRepository.createMany(formFieldEvents);
    await this.formFieldEventRepository.saveMany(fieldEvent);
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'SharedService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
