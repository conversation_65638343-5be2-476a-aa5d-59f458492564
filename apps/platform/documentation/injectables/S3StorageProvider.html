<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >S3StorageProvider</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/storage/providers/s3-storage.provider.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#accessKeyId" >accessKeyId</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#bucketMappings" >bucketMappings</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Optional</span>
                                <a href="#defaultBucket" >defaultBucket</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#region" >region</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#s3" >s3</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#secretAccessKey" >secretAccessKey</a>
                            </li>
                            <li>
                                <a href="#type" >type</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#copyFile" >copyFile</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#delete" >delete</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#extractKeyFromUrl" >extractKeyFromUrl</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#findOrUpdateStorage" >findOrUpdateStorage</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getFolderContentsWithSignedUrls" >getFolderContentsWithSignedUrls</a>
                            </li>
                            <li>
                                <a href="#getUrl" >getUrl</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#list" >list</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#parseS3Path" >parseS3Path</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#read" >read</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#resolveBucket" >resolveBucket</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#write" >write</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(credentials: literal type, regionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, storageRepository: StorageRepository)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="29" class="link-to-prism">src/storage/providers/s3-storage.provider.ts:29</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>credentials</td>
                                                  
                                                        <td>
                                                                    <code>literal type</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>regionName</td>
                                                  
                                                        <td>
                                                                        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>storageRepository</td>
                                                  
                                                        <td>
                                                                    <code>StorageRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="copyFile"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>copyFile</b></span>
                        <a href="#copyFile"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>copyFile(sourceUrl: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, destinationPath: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, bucketName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, options?: <a href="../interfaces/StorageOptions.html" target="_self">StorageOptions</a>, organizationId?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="222"
                                    class="link-to-prism">src/storage/providers/s3-storage.provider.ts:222</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>sourceUrl</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>destinationPath</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>bucketName</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>options</td>
                                            <td>
                                                            <code><a href="../interfaces/StorageOptions.html" target="_self" >StorageOptions</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;Storage&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="delete"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>delete</b></span>
                        <a href="#delete"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>delete(path: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, options?: literal type)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="185"
                                    class="link-to-prism">src/storage/providers/s3-storage.provider.ts:185</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>path</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>options</td>
                                            <td>
                                                        <code>literal type</code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;void&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="extractKeyFromUrl"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>extractKeyFromUrl</b></span>
                        <a href="#extractKeyFromUrl"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>extractKeyFromUrl(url: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="270"
                                    class="link-to-prism">src/storage/providers/s3-storage.provider.ts:270</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>url</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findOrUpdateStorage"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>findOrUpdateStorage</b></span>
                        <a href="#findOrUpdateStorage"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findOrUpdateStorage(url: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, storageData: Partial<Storage>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="116"
                                    class="link-to-prism">src/storage/providers/s3-storage.provider.ts:116</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Helper method to find or create storage entity</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>url</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>storageData</td>
                                            <td>
                                                        <code>Partial&lt;Storage&gt;</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;Storage&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getFolderContentsWithSignedUrls"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>getFolderContentsWithSignedUrls</b></span>
                        <a href="#getFolderContentsWithSignedUrls"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getFolderContentsWithSignedUrls(options: <a href="../interfaces/GetFolderContentsOptions.html" target="_self">GetFolderContentsOptions</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="288"
                                    class="link-to-prism">src/storage/providers/s3-storage.provider.ts:288</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>options</td>
                                            <td>
                                                            <code><a href="../interfaces/GetFolderContentsOptions.html" target="_self" >GetFolderContentsOptions</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../interfaces/SignedFolderContent.html" target="_self" >Promise&lt;SignedFolderContent[]&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getUrl"></a>
                    <span class="name">
                        <span ><b>getUrl</b></span>
                        <a href="#getUrl"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getUrl(path: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, options?: literal type)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="333"
                                    class="link-to-prism">src/storage/providers/s3-storage.provider.ts:333</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>path</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>options</td>
                                            <td>
                                                        <code>literal type</code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;string&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="list"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>list</b></span>
                        <a href="#list"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>list(directory: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, options?: literal type)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="201"
                                    class="link-to-prism">src/storage/providers/s3-storage.provider.ts:201</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>directory</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>options</td>
                                            <td>
                                                        <code>literal type</code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;string[]&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="parseS3Path"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>parseS3Path</b></span>
                        <a href="#parseS3Path"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>parseS3Path(fullPath: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="74"
                                    class="link-to-prism">src/storage/providers/s3-storage.provider.ts:74</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>fullPath</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>literal type</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="read"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>read</b></span>
                        <a href="#read"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>read(path: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, options?: literal type)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="89"
                                    class="link-to-prism">src/storage/providers/s3-storage.provider.ts:89</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>path</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>options</td>
                                            <td>
                                                        <code>literal type</code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;any&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="resolveBucket"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>resolveBucket</b></span>
                        <a href="#resolveBucket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>resolveBucket(bucket?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="57"
                                    class="link-to-prism">src/storage/providers/s3-storage.provider.ts:57</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>bucket</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="write"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>write</b></span>
                        <a href="#write"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>write(path: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, content: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, options?: <a href="../interfaces/StorageOptions.html" target="_self">StorageOptions</a>, organizationId?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="136"
                                    class="link-to-prism">src/storage/providers/s3-storage.provider.ts:136</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>path</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>content</td>
                                            <td>
                                                            <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>options</td>
                                            <td>
                                                            <code><a href="../interfaces/StorageOptions.html" target="_self" >StorageOptions</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;Storage&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>
            <section data-compodoc="block-properties">
    
    <h3 id="inputs">
        Properties
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="accessKeyId"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>accessKeyId</b></span>
                        <a href="#accessKeyId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="28" class="link-to-prism">src/storage/providers/s3-storage.provider.ts:28</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="bucketMappings"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>bucketMappings</b></span>
                        <a href="#bucketMappings"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>Record&lt;string | string&gt;</code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="26" class="link-to-prism">src/storage/providers/s3-storage.provider.ts:26</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="defaultBucket"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Optional</span>
                        <span ><b>defaultBucket</b></span>
                        <a href="#defaultBucket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="25" class="link-to-prism">src/storage/providers/s3-storage.provider.ts:25</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="region"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>region</b></span>
                        <a href="#region"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="27" class="link-to-prism">src/storage/providers/s3-storage.provider.ts:27</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="s3"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>s3</b></span>
                        <a href="#s3"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="../miscellaneous/variables.html#Client" target="_self" >S3Client</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="24" class="link-to-prism">src/storage/providers/s3-storage.provider.ts:24</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="secretAccessKey"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>secretAccessKey</b></span>
                        <a href="#secretAccessKey"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="29" class="link-to-prism">src/storage/providers/s3-storage.provider.ts:29</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="type"></a>
                    <span class="name">
                        <span ><b>type</b></span>
                        <a href="#type"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="../miscellaneous/typealiases.html#StorageType" target="_self" >StorageType</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&quot;s3&quot;</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="23" class="link-to-prism">src/storage/providers/s3-storage.provider.ts:23</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  CopyObjectCommand,
  DeleteObjectCommand,
  GetObjectCommand,
  ListObjectsV2Command,
  ListObjectsV2CommandInput,
  PutObjectCommand,
  S3Client,
} from &quot;@aws-sdk/client-s3&quot;;
import { getSignedUrl } from &quot;@aws-sdk/s3-request-presigner&quot;;
import { Inject, Injectable } from &quot;@nestjs/common&quot;;
import { Storage, StorageRepository } from &quot;@repo/thena-platform-entities&quot;;
import * as yaml from &quot;js-yaml&quot;;
import {
  GetFolderContentsOptions,
  IStorageProvider,
  SignedFolderContent,
} from &quot;../interfaces/storage.interface&quot;;
import { StorageOptions, StorageType } from &quot;../types/storage.types&quot;;

@Injectable()
export class S3StorageProvider implements IStorageProvider {
  type: StorageType &#x3D; &quot;s3&quot;;
  private s3: S3Client;
  private defaultBucket?: string;
  private bucketMappings: Record&lt;string, string&gt;;
  private region: string;
  private accessKeyId: string;
  private secretAccessKey: string;
  // eslint-disable-next-line max-len
  constructor(
    // eslint-disable-next-line max-len
    private credentials: { accessKeyId: string; secretAccessKey: string },
    private regionName: string,
    @Inject(StorageRepository)
    private readonly storageRepository: StorageRepository,
  ) {
    this.accessKeyId &#x3D; credentials.accessKeyId;
    this.secretAccessKey &#x3D; credentials.secretAccessKey;
    this.region &#x3D; regionName;
    this.s3 &#x3D; new S3Client({
      region: this.region,
      // eslint-disable-next-line max-len
      credentials:
        this.accessKeyId &amp;&amp; this.secretAccessKey
          ? {
              accessKeyId: this.accessKeyId,
              secretAccessKey: this.secretAccessKey,
            }
          : undefined,
    });

    this.defaultBucket &#x3D; &quot;thena-new-backend-test&quot;;
    // this.bucketMappings &#x3D; configService.get(ConfigKeys.AWS_S3_BUCKET_MAPPINGS) || {};
  }

  private resolveBucket(bucket?: string): string {
    if (!bucket) {
      if (!this.defaultBucket) {
        throw new Error(&quot;No bucket specified and no default bucket configured&quot;);
      }
      return this.defaultBucket;
    }

    // Check if this is a named bucket from our mappings
    // if (this.bucketMappings[bucket]) {
    //   return this.bucketMappings[bucket];
    // }

    // If not in mappings, use the bucket name directly
    return bucket;
  }

  private parseS3Path(fullPath: string): { bucket?: string; key: string } {
    if (fullPath.startsWith(&quot;s3://&quot;)) {
      const withoutPrefix &#x3D; fullPath.substring(5);
      const firstSlash &#x3D; withoutPrefix.indexOf(&quot;/&quot;);
      if (firstSlash &#x3D;&#x3D;&#x3D; -1) {
        return { key: withoutPrefix };
      }
      return {
        bucket: withoutPrefix.substring(0, firstSlash),
        key: withoutPrefix.substring(firstSlash + 1),
      };
    }
    return { key: fullPath };
  }

  async read(path: string, options?: { bucket?: string }): Promise&lt;any&gt; {
    try {
      const { bucket: pathBucket, key } &#x3D; this.parseS3Path(path);
      const bucket &#x3D; this.resolveBucket(options?.bucket || pathBucket);

      const response &#x3D; await this.s3.send(
        new GetObjectCommand({
          Bucket: bucket,
          Key: key,
        }),
      );

      const content &#x3D; response.Body.toString();
      if (key.endsWith(&quot;.yaml&quot;) || key.endsWith(&quot;.yml&quot;)) {
        return yaml.load(content);
      } else if (key.endsWith(&quot;.json&quot;)) {
        return JSON.parse(content);
      }
      return content;
    } catch (error) {
      throw new Error(&#x60;S3: Error reading file: ${error.message}&#x60;);
    }
  }

  /**
   * Helper method to find or create storage entity
   */
  private async findOrUpdateStorage(
    url: string,
    storageData: Partial&lt;Storage&gt;,
  ): Promise&lt;Storage&gt; {
    // Try to find existing storage entity with the same URL
    const existingStorage &#x3D; await this.storageRepository.findByCondition({
      where: { url },
    });

    if (existingStorage) {
      // Update existing storage with new data
      Object.assign(existingStorage, storageData);
      return this.storageRepository.save(existingStorage);
    }

    // Create new storage if none exists
    const storage &#x3D; this.storageRepository.create(storageData);
    return this.storageRepository.save(storage);
  }

  async write(
    path: string,
    content: any,
    options?: StorageOptions,
    organizationId?: string,
  ): Promise&lt;Storage&gt; {
    try {
      const { bucket: pathBucket, key } &#x3D; this.parseS3Path(path);
      const bucket &#x3D; this.resolveBucket(options?.bucket || pathBucket);

      // Handle different content types
      let body: string | Buffer;
      if (Buffer.isBuffer(content)) {
        body &#x3D; content;
      } else if (typeof content &#x3D;&#x3D;&#x3D; &quot;string&quot;) {
        body &#x3D; content;
      } else {
        body &#x3D; JSON.stringify(content);
      }

      await this.s3.send(
        new PutObjectCommand({
          Bucket: bucket,
          Key: key,
          Body: body,
          ContentType: options?.contentType, // Add content type to S3 metadata
        }),
      );

      const url &#x3D; &#x60;https://${bucket}.s3.${this.region}.amazonaws.com/${key}&#x60;;
      const storageData &#x3D; {
        organizationId,
        url,
        path: key,
        contentType: options?.contentType || &quot;text/plain&quot;,
        metadata: options?.metadata || {},
        originalName: key,
        encoding: options?.encoding || &quot;utf-8&quot;,
        size: body.length,
        entityId: options?.entityId || &quot;&quot;,
        entityType: options?.entityType || &quot;&quot;,
      };

      return this.findOrUpdateStorage(url, storageData);
    } catch (error) {
      throw new Error(&#x60;S3: Error writing file: ${error.message}&#x60;);
    }
  }

  async delete(path: string, options?: { bucket?: string }): Promise&lt;void&gt; {
    try {
      const { bucket: pathBucket, key } &#x3D; this.parseS3Path(path);
      const bucket &#x3D; this.resolveBucket(options?.bucket || pathBucket);

      await this.s3.send(
        new DeleteObjectCommand({
          Bucket: bucket,
          Key: key,
        }),
      );
    } catch (error) {
      throw new Error(&#x60;S3: Error deleting file: ${error.message}&#x60;);
    }
  }

  async list(
    directory: string,
    options?: { bucket?: string },
  ): Promise&lt;string[]&gt; {
    try {
      const { bucket: pathBucket, key } &#x3D; this.parseS3Path(directory);
      const bucket &#x3D; this.resolveBucket(options?.bucket || pathBucket);

      const response &#x3D; await this.s3.send(
        new ListObjectsV2Command({
          Bucket: bucket,
          Prefix: key,
        }),
      );

      return response.Contents.map((item) &#x3D;&gt; item.Key);
    } catch (error) {
      throw new Error(&#x60;S3: Error listing files: ${error.message}&#x60;);
    }
  }

  async copyFile(
    sourceUrl: string,
    destinationPath: string,
    bucketName: string,
    options?: StorageOptions,
    organizationId?: string,
  ): Promise&lt;Storage&gt; {
    try {
      const sourceKey &#x3D; this.extractKeyFromUrl(sourceUrl);
      const destinationKey &#x3D; &#x60;${destinationPath}/${sourceKey.split(&quot;/&quot;).pop()}&#x60;;
      // Copy the object to the new location
      await this.s3.send(
        new CopyObjectCommand({
          Bucket: bucketName,
          CopySource: &#x60;${bucketName}/${sourceKey}&#x60;,
          Key: destinationKey,
        }),
      );

      // Get the object to determine its size and content type
      const response &#x3D; await this.s3.send(
        new GetObjectCommand({
          Bucket: bucketName,
          Key: destinationKey,
        }),
      );

      const url &#x3D; &#x60;https://${bucketName}.s3.${this.region}.amazonaws.com/${destinationKey}&#x60;;
      const storageData &#x3D; {
        organizationId,
        url,
        path: destinationKey,
        contentType:
          response.ContentType || options?.contentType || &quot;text/plain&quot;,
        metadata: options?.metadata || {},
        originalName: destinationKey,
        encoding: options?.encoding || &quot;utf-8&quot;,
        size: response.ContentLength || 0,
        entityId: options?.entityId || &quot;&quot;,
        entityType: options?.entityType || &quot;&quot;,
      };

      return this.findOrUpdateStorage(url, storageData);
    } catch (error) {
      throw new Error(&#x60;Failed to move file in S3: ${error.message}&#x60;);
    }
  }

  private extractKeyFromUrl(url: string): string {
    try {
      const urlObj &#x3D; new URL(url);
      if (
        !urlObj.hostname.includes(&quot;s3.&quot;) ||
        !urlObj.hostname.includes(&quot;amazonaws.com&quot;)
      ) {
        throw new Error(&quot;URL is not a valid S3 URL&quot;);
      }

      // Remove the leading &#x27;/&#x27; from pathname
      return urlObj.pathname.substring(1);
    } catch (error) {
      console.log(error);
      throw new Error(&quot;Invalid S3 URL provided&quot;);
    }
  }

  async getFolderContentsWithSignedUrls(
    options: GetFolderContentsOptions,
  ): Promise&lt;SignedFolderContent[]&gt; {
    try {
      // Construct the folder path
      const bucketToUse &#x3D; this.resolveBucket(options.bucket);

      const params: ListObjectsV2CommandInput &#x3D; {
        Bucket: bucketToUse,
        Prefix: options.folderPrefix,
      };
      // List all objects in the folder
      const data &#x3D; await this.s3.send(new ListObjectsV2Command(params));

      // Generate signed URLs for each file
      const filesWithUrls &#x3D; await Promise.all(
        data.Contents.map(async (file) &#x3D;&gt; {
          const signedUrl &#x3D; await getSignedUrl(
            this.s3,
            new GetObjectCommand({
              Bucket: this.defaultBucket,
              Key: file.Key,
            }),
            {
              expiresIn: 60 * 5, // 5 minutes
            },
          );

          return {
            filename: file.Key.split(&quot;/&quot;).pop() || file.Key,
            key: file.Key,
            size: file.Size,
            lastModified: file.LastModified,
            signedUrl,
          };
        }),
      );

      return filesWithUrls;
    } catch (error) {
      console.error(&quot;Error getting folder contents:&quot;, error);
      throw error;
    }
  }

  getUrl(path: string, options?: { bucket?: string }): Promise&lt;string&gt; {
    try {
      const { bucket: pathBucket, key } &#x3D; this.parseS3Path(path);
      const bucket &#x3D; this.resolveBucket(options?.bucket || pathBucket);

      return Promise.resolve(
        &#x60;https://${bucket}.s3.${this.region}.amazonaws.com/${encodeURIComponent(
          key,
        )}&#x60;,
      );
    } catch (error) {
      throw new Error(&#x60;S3: Error generating URL: ${error.message}&#x60;);
    }
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'S3StorageProvider.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
