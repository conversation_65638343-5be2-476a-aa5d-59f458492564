<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >TeamsService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/teams/services/teams.service.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#addMemberToTeam" >addMemberToTeam</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#canUserReadTeam" >canUserReadTeam</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#canUserUpdateTeam" >canUserUpdateTeam</a>
                            </li>
                            <li>
                                <a href="#checkExists" >checkExists</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createRoutingRule" >createRoutingRule</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createTeam" >createTeam</a>
                            </li>
                            <li>
                                <a href="#createTeamMember" >createTeamMember</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#deleteOneTeam" >deleteOneTeam</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#deleteRoutingRule" >deleteRoutingRule</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findActiveTeamMembers" >findActiveTeamMembers</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findAllSubTeams" >findAllSubTeams</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findAllTeams" >findAllTeams</a>
                            </li>
                            <li>
                                <a href="#findOneByTeamId" >findOneByTeamId</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findOneTeamById" >findOneTeamById</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#findTeamFromUser" >findTeamFromUser</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findTeamMembers" >findTeamMembers</a>
                            </li>
                            <li>
                                <a href="#findTeamsByPublicIds" >findTeamsByPublicIds</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#generateBusinessHoursUpdates" >generateBusinessHoursUpdates</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#generateTeamIdentifier" >generateTeamIdentifier</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getAllPublicTeams" >getAllPublicTeams</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getTeamCapacity" >getTeamCapacity</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getTeamConfigurations" >getTeamConfigurations</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getTeamRoutingRules" >getTeamRoutingRules</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getTeamsByUser" >getTeamsByUser</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getUserAndTeam" >getUserAndTeam</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#invalidateAndReCacheTeamRoutingRules" >invalidateAndReCacheTeamRoutingRules</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#removeMemberFromTeam" >removeMemberFromTeam</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#removeTeamMember" >removeTeamMember</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateRoutingRule" >updateRoutingRule</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateTeam" >updateTeam</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateTeamConfigurations" >updateTeamConfigurations</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#userBelongsToTeam" >userBelongsToTeam</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(logger: ILogger, teamRepository: TeamRepository, cachedTeamRepository: CachedTeamRepository, teamMemberRepository: TeamMemberRepository, teamConfigurationRepository: TeamConfigurationRepository, cachedTeamConfigurationRepository: CachedTeamConfigurationRepository, teamCapacityRepository: TeamCapacityRepository, cachedTeamCapacityRepository: CachedTeamCapacityRepository, teamRoutingRulesRepository: TeamRoutingRulesRepository, cachedTeamRoutingRulesRepository: CachedTeamRoutingRulesRepository, businessHoursConfigRepository: BusinessHoursConfigRepository, cachedBusinessHoursConfigRepository: CachedBusinessHoursConfigRepository, cacheProvider: RedisCacheProvider, usersService: <a href="../injectables/UsersService.html" target="_self">UsersService</a>, transactionService: TransactionService, businessHoursValidationService: <a href="../injectables/BusinessHoursValidatorService.html" target="_self">BusinessHoursValidatorService</a>, sharedService: <a href="../injectables/SharedService.html" target="_self">SharedService</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="80" class="link-to-prism">src/teams/services/teams.service.ts:80</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>logger</td>
                                                  
                                                        <td>
                                                                    <code>ILogger</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>teamRepository</td>
                                                  
                                                        <td>
                                                                    <code>TeamRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>cachedTeamRepository</td>
                                                  
                                                        <td>
                                                                    <code>CachedTeamRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>teamMemberRepository</td>
                                                  
                                                        <td>
                                                                    <code>TeamMemberRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>teamConfigurationRepository</td>
                                                  
                                                        <td>
                                                                    <code>TeamConfigurationRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>cachedTeamConfigurationRepository</td>
                                                  
                                                        <td>
                                                                    <code>CachedTeamConfigurationRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>teamCapacityRepository</td>
                                                  
                                                        <td>
                                                                    <code>TeamCapacityRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>cachedTeamCapacityRepository</td>
                                                  
                                                        <td>
                                                                    <code>CachedTeamCapacityRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>teamRoutingRulesRepository</td>
                                                  
                                                        <td>
                                                                    <code>TeamRoutingRulesRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>cachedTeamRoutingRulesRepository</td>
                                                  
                                                        <td>
                                                                    <code>CachedTeamRoutingRulesRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>businessHoursConfigRepository</td>
                                                  
                                                        <td>
                                                                    <code>BusinessHoursConfigRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>cachedBusinessHoursConfigRepository</td>
                                                  
                                                        <td>
                                                                    <code>CachedBusinessHoursConfigRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>cacheProvider</td>
                                                  
                                                        <td>
                                                                    <code>RedisCacheProvider</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>usersService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/UsersService.html" target="_self" >UsersService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>transactionService</td>
                                                  
                                                        <td>
                                                                    <code>TransactionService</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>businessHoursValidationService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/BusinessHoursValidatorService.html" target="_self" >BusinessHoursValidatorService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>sharedService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/SharedService.html" target="_self" >SharedService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="addMemberToTeam"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>addMemberToTeam</b></span>
                        <a href="#addMemberToTeam"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>addMemberToTeam(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, addTeamMemberDto: <a href="../classes/AddTeamMemberDto.html" target="_self">AddTeamMemberDto</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="780"
                                    class="link-to-prism">src/teams/services/teams.service.ts:780</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Adds a team member to a team.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The team ID to add the member to.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>addTeamMemberDto</td>
                                            <td>
                                                            <code><a href="../classes/AddTeamMemberDto.html" target="_self" >AddTeamMemberDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The team member data to add.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The added team member.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="canUserReadTeam"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>canUserReadTeam</b></span>
                        <a href="#canUserReadTeam"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>canUserReadTeam(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="156"
                                    class="link-to-prism">src/teams/services/teams.service.ts:156</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Checks if a user can read a team.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The team ID.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The user.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The team and whether the user belongs to the team.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="canUserUpdateTeam"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>canUserUpdateTeam</b></span>
                        <a href="#canUserUpdateTeam"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>canUserUpdateTeam(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, checkOwner: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank">boolean</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="188"
                                    class="link-to-prism">src/teams/services/teams.service.ts:188</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Checks if a user can update a team.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Default value</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>

                                            <td>
                                            </td>

                                            <td>
                                                    <p>The team ID.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>

                                            <td>
                                            </td>

                                            <td>
                                                    <p>The user.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>checkOwner</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>

                                            <td>
                                                    <code>false</code>
                                            </td>

                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The team and whether the user belongs to the team.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="checkExists"></a>
                    <span class="name">
                        <span ><b>checkExists</b></span>
                        <a href="#checkExists"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>checkExists(whereClause: FindOptionsWhere<Team>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="145"
                                    class="link-to-prism">src/teams/services/teams.service.ts:145</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Checks if a team exists.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>whereClause</td>
                                            <td>
                                                        <code>FindOptionsWhere&lt;Team&gt;</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The where clause to check.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                <p>Whether the team exists.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createRoutingRule"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>createRoutingRule</b></span>
                        <a href="#createRoutingRule"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createRoutingRule(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, createRoutingRuleDto: <a href="../classes/CreateRoutingRuleGroupDto.html" target="_self">CreateRoutingRuleGroupDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="1155"
                                    class="link-to-prism">src/teams/services/teams.service.ts:1155</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Creates a routing rule for a team.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The team ID to create the routing rule for.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The user making the request.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>createRoutingRuleDto</td>
                                            <td>
                                                            <code><a href="../classes/CreateRoutingRuleGroupDto.html" target="_self" >CreateRoutingRuleGroupDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The routing rule data to create.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The created routing rule.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createTeam"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>createTeam</b></span>
                        <a href="#createTeam"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createTeam(createTeamDto: <a href="../classes/CreateTeamDto.html" target="_self">CreateTeamDto</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="505"
                                    class="link-to-prism">src/teams/services/teams.service.ts:505</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Creates a new team.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>createTeamDto</td>
                                            <td>
                                                            <code><a href="../classes/CreateTeamDto.html" target="_self" >CreateTeamDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The team data to create.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The created team.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createTeamMember"></a>
                    <span class="name">
                        <span ><b>createTeamMember</b></span>
                        <a href="#createTeamMember"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>createTeamMember(teamMember: Partial<TeamMember>, context?: TransactionContext)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="265"
                                    class="link-to-prism">src/teams/services/teams.service.ts:265</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Creates a new team member.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamMember</td>
                                            <td>
                                                        <code>Partial&lt;TeamMember&gt;</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The team member data to create.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>context</td>
                                            <td>
                                                        <code>TransactionContext</code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;TeamMember&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The created team member.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteOneTeam"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>deleteOneTeam</b></span>
                        <a href="#deleteOneTeam"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteOneTeam(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="730"
                                    class="link-to-prism">src/teams/services/teams.service.ts:730</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Deletes a team.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The team ID to delete.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                <p>The deleted team.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteRoutingRule"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>deleteRoutingRule</b></span>
                        <a href="#deleteRoutingRule"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteRoutingRule(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, ruleId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="1416"
                                    class="link-to-prism">src/teams/services/teams.service.ts:1416</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Deletes a routing rule for a team.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The team ID to delete the routing rule for.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>ruleId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The rule ID to delete.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The user making the request.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findActiveTeamMembers"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findActiveTeamMembers</b></span>
                        <a href="#findActiveTeamMembers"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findActiveTeamMembers(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="388"
                                    class="link-to-prism">src/teams/services/teams.service.ts:388</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds all active team members for a team.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The team ID of the team to find the members of.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The user making the request.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The team members.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAllSubTeams"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findAllSubTeams</b></span>
                        <a href="#findAllSubTeams"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAllSubTeams(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, parentTeamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="437"
                                    class="link-to-prism">src/teams/services/teams.service.ts:437</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds all the sub teams that belong to a parent team</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>Currently logged in user</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>parentTeamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>UID of the parent team</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The teams</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAllTeams"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findAllTeams</b></span>
                        <a href="#findAllTeams"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAllTeams(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, parentTeamId?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="452"
                                    class="link-to-prism">src/teams/services/teams.service.ts:452</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds all teams for the user</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>parentTeamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The teams.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findOneByTeamId"></a>
                    <span class="name">
                        <span ><b>findOneByTeamId</b></span>
                        <a href="#findOneByTeamId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findOneByTeamId(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, parentTeamId?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, noRelations?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank">boolean</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="122"
                                    class="link-to-prism">src/teams/services/teams.service.ts:122</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds a team by its team ID.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The team ID of the team to find.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>parentTeamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>noRelations</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                <p>The team.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findOneTeamById"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findOneTeamById</b></span>
                        <a href="#findOneTeamById"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findOneTeamById(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="359"
                                    class="link-to-prism">src/teams/services/teams.service.ts:359</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds a team by its team ID.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The team ID of the team to find.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The team.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findTeamFromUser"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>findTeamFromUser</b></span>
                        <a href="#findTeamFromUser"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findTeamFromUser(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="297"
                                    class="link-to-prism">src/teams/services/teams.service.ts:297</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds a team by its team ID from the request.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The team ID of the team to find.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The team.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findTeamMembers"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findTeamMembers</b></span>
                        <a href="#findTeamMembers"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findTeamMembers(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="370"
                                    class="link-to-prism">src/teams/services/teams.service.ts:370</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds the members of a team.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The team ID of the team to find the members of.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The team members.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findTeamsByPublicIds"></a>
                    <span class="name">
                        <span ><b>findTeamsByPublicIds</b></span>
                        <a href="#findTeamsByPublicIds"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findTeamsByPublicIds(teamIds: string[], organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="284"
                                    class="link-to-prism">src/teams/services/teams.service.ts:284</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds teams by their public IDs.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamIds</td>
                                            <td>
                                                        <code>string[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The team IDs to find.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The organization ID.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                <p>The teams.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="generateBusinessHoursUpdates"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>generateBusinessHoursUpdates</b></span>
                        <a href="#generateBusinessHoursUpdates"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>generateBusinessHoursUpdates(businessHours: <a href="../classes/BusinessHoursConfigDto.html" target="_self">BusinessHoursConfigDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="1446"
                                    class="link-to-prism">src/teams/services/teams.service.ts:1446</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Generates the business hours updates for a team.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>businessHours</td>
                                            <td>
                                                            <code><a href="../classes/BusinessHoursConfigDto.html" target="_self" >BusinessHoursConfigDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The business hours configurations to update.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>{}</code>

                        </div>
                            <div class="io-description">
                                <p>The business hours updates.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="generateTeamIdentifier"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>generateTeamIdentifier</b></span>
                        <a href="#generateTeamIdentifier"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>generateTeamIdentifier()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="1502"
                                    class="link-to-prism">src/teams/services/teams.service.ts:1502</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Generates a team identifier.</p>
</div>

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </div>
                            <div class="io-description">
                                <p>The generated team identifier.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getAllPublicTeams"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>getAllPublicTeams</b></span>
                        <a href="#getAllPublicTeams"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getAllPublicTeams(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="491"
                                    class="link-to-prism">src/teams/services/teams.service.ts:491</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds all public teams for the organization.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The teams.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTeamCapacity"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>getTeamCapacity</b></span>
                        <a href="#getTeamCapacity"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTeamCapacity(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="415"
                                    class="link-to-prism">src/teams/services/teams.service.ts:415</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds the team capacity for a team.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The team ID of the team to find the capacity of.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The user making the request.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The team capacity.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTeamConfigurations"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>getTeamConfigurations</b></span>
                        <a href="#getTeamConfigurations"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTeamConfigurations(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="984"
                                    class="link-to-prism">src/teams/services/teams.service.ts:984</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Gets a team&#39;s configurations.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The team ID to get the configurations of.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The user making the request.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../interfaces/CombinedTeamConfig.html" target="_self" >Promise&lt;CombinedTeamConfig&gt;</a></code>

                        </div>
                            <div class="io-description">
                                <p>The team configurations.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTeamRoutingRules"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>getTeamRoutingRules</b></span>
                        <a href="#getTeamRoutingRules"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTeamRoutingRules(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="1373"
                                    class="link-to-prism">src/teams/services/teams.service.ts:1373</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Gets a team&#39;s routing rules.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The team ID to get the routing rules of.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The user making the request.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The team&#39;s routing rules.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTeamsByUser"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>getTeamsByUser</b></span>
                        <a href="#getTeamsByUser"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTeamsByUser(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, select?: FindOptionsSelect<TeamMember>, relations?: FindOptionsRelations<TeamMember>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="217"
                                    class="link-to-prism">src/teams/services/teams.service.ts:217</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>select</td>
                                            <td>
                                                        <code>FindOptionsSelect&lt;TeamMember&gt;</code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>relations</td>
                                            <td>
                                                        <code>FindOptionsRelations&lt;TeamMember&gt;</code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getUserAndTeam"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>getUserAndTeam</b></span>
                        <a href="#getUserAndTeam"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getUserAndTeam(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="239"
                                    class="link-to-prism">src/teams/services/teams.service.ts:239</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Gets a user and team by their IDs and organization ID. This also validates if the user belongs to the team.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The team ID.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>userId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The user ID.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The organization ID.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The user and team.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="invalidateAndReCacheTeamRoutingRules"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>invalidateAndReCacheTeamRoutingRules</b></span>
                        <a href="#invalidateAndReCacheTeamRoutingRules"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>invalidateAndReCacheTeamRoutingRules(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="1468"
                                    class="link-to-prism">src/teams/services/teams.service.ts:1468</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Invalidates and re-caches the team routing rules.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The team ID to invalidate and re-cache the routing rules for.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The organization ID to invalidate and re-cache the routing rules for.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="removeMemberFromTeam"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>removeMemberFromTeam</b></span>
                        <a href="#removeMemberFromTeam"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>removeMemberFromTeam(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, memberId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="918"
                                    class="link-to-prism">src/teams/services/teams.service.ts:918</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Removes a team member from a team.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The team ID to remove the member from.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>memberId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The member ID to remove from the team.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                <p>The removed team member.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="removeTeamMember"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>removeTeamMember</b></span>
                        <a href="#removeTeamMember"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>removeTeamMember(teamMemberId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="333"
                                    class="link-to-prism">src/teams/services/teams.service.ts:333</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Removes a team member from a team.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamMemberId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The team member ID to remove.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The removed team member.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateRoutingRule"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>updateRoutingRule</b></span>
                        <a href="#updateRoutingRule"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateRoutingRule(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, ruleId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, updateRoutingRuleDto: <a href="../classes/UpdateRoutingRuleGroupDto.html" target="_self">UpdateRoutingRuleGroupDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="1263"
                                    class="link-to-prism">src/teams/services/teams.service.ts:1263</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Updates a routing rule for a team.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The team ID to update the routing rule for.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>ruleId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The rule ID to update.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The user making the request.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>updateRoutingRuleDto</td>
                                            <td>
                                                            <code><a href="../classes/UpdateRoutingRuleGroupDto.html" target="_self" >UpdateRoutingRuleGroupDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The routing rule data to update.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The updated routing rule.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateTeam"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>updateTeam</b></span>
                        <a href="#updateTeam"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateTeam(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, updateTeamDto: <a href="../classes/UpdateTeamDto.html" target="_self">UpdateTeamDto</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="668"
                                    class="link-to-prism">src/teams/services/teams.service.ts:668</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Updates a team.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The team ID to update.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>updateTeamDto</td>
                                            <td>
                                                            <code><a href="../classes/UpdateTeamDto.html" target="_self" >UpdateTeamDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The team data to update.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The user making the request.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The updated team.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateTeamConfigurations"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>updateTeamConfigurations</b></span>
                        <a href="#updateTeamConfigurations"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateTeamConfigurations(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, updateTeamsDto: <a href="../classes/UpdateTimezoneWorkingHoursDto.html" target="_self">UpdateTimezoneWorkingHoursDto</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="1010"
                                    class="link-to-prism">src/teams/services/teams.service.ts:1010</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Updates a team&#39;s configurations and business hours.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The team ID to update the configurations of.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>updateTeamsDto</td>
                                            <td>
                                                            <code><a href="../classes/UpdateTimezoneWorkingHoursDto.html" target="_self" >UpdateTimezoneWorkingHoursDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The team configurations data to update.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The user making the request.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The updated team configurations.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="userBelongsToTeam"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>userBelongsToTeam</b></span>
                        <a href="#userBelongsToTeam"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>userBelongsToTeam(userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, options: literal type)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="311"
                                    class="link-to-prism">src/teams/services/teams.service.ts:311</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Checks if a user belongs to a team.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Default value</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>userId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>

                                            <td>
                                            </td>

                                            <td>
                                                    <p>The user ID.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>

                                            <td>
                                            </td>

                                            <td>
                                                    <p>The team ID.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>

                                            <td>
                                            </td>

                                            <td>
                                                    <p>The organization ID.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>options</td>
                                            <td>
                                                        <code>literal type</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>

                                            <td>
                                                    <code>{}</code>
                                            </td>

                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>Whether the user belongs to the team.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  Inject,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  UnauthorizedException,
} from &quot;@nestjs/common&quot;;
import { RedisCacheProvider } from &quot;@repo/nestjs-commons/cache&quot;;
import { ILogger } from &quot;@repo/nestjs-commons/logger&quot;;
import {
  BusinessHoursConfig,
  BusinessHoursConfigRepository,
  CachedBusinessHoursConfigRepository,
  CachedTeamCapacityRepository,
  CachedTeamConfigurationRepository,
  CachedTeamRepository,
  CachedTeamRoutingRulesRepository,
  Team,
  TeamCapacityRepository,
  TeamConfiguration,
  TeamConfigurationRepository,
  TeamMember,
  TeamMemberRepository,
  TeamMemberRole,
  TeamRepository,
  TeamRoutingRules,
  TeamRoutingRulesRepository,
  TransactionContext,
  TransactionService,
  User,
  UserStatus,
  UserType,
} from &quot;@repo/thena-platform-entities&quot;;
import {
  DeepPartial,
  FindOptionsRelations,
  FindOptionsSelect,
  FindOptionsWhere,
  In,
  IsNull,
  Not,
  QueryFailedError,
} from &quot;typeorm&quot;;
import { IdGeneratorUtils } from &quot;../../common&quot;;
import { CACHE_TTL } from &quot;../../common/constants/cache.constants&quot;;
import { POSTGRES_ERROR_CODES } from &quot;../../common/constants/postgres-errors.constants&quot;;
import { CurrentUser } from &quot;../../common/decorators&quot;;
import {
  BusinessDayDto,
  BusinessHoursConfigDto,
  UpdateTimezoneWorkingHoursDto,
} from &quot;../../common/dto&quot;;
import { BusinessHoursValidatorService } from &quot;../../common/services/business-hours-validation.service&quot;;
import { generateIdentifier } from &quot;../../common/utils/identifier-generator.utils&quot;;
import { constructDailyConfigFromCommonSlots } from &quot;../../common/utils/time-slots.utils&quot;;
import { SharedService } from &quot;../../shared/shared.service&quot;;
import { UsersService } from &quot;../../users/services/users.service&quot;;
import { EAGERLY_LOADED_RELATIONS_FOR_TEAM_MEMBERS } from &quot;../constants/team-members.constants&quot;;
import {
  EAGERLY_LOADED_RELATIONS,
  TEAM_ROUTING_RULES_RELATIONS,
} from &quot;../constants/teams.constants&quot;;
import { AddTeamMemberDto } from &quot;../dto/team-member.dto&quot;;
import {
  CreateRoutingRuleGroupDto,
  UpdateRoutingRuleGroupDto,
} from &quot;../dto/team-routing.dto&quot;;
import { CreateTeamDto, UpdateTeamDto } from &quot;../dto/teams.dto&quot;;

export interface CombinedTeamConfig {
  teamId: string;
  teamConfig: TeamConfiguration;
  businessHoursConfig: BusinessHoursConfig;
}

@Injectable()
export class TeamsService {
  constructor(
    @Inject(&quot;CustomLogger&quot;)
    private readonly logger: ILogger,

    // Teams repositories
    private readonly teamRepository: TeamRepository,
    private readonly cachedTeamRepository: CachedTeamRepository,

    // Team members repositories
    private readonly teamMemberRepository: TeamMemberRepository,

    // Team configuration repositories
    private readonly teamConfigurationRepository: TeamConfigurationRepository,
    private readonly cachedTeamConfigurationRepository: CachedTeamConfigurationRepository,

    // Team capacity repositories
    private readonly teamCapacityRepository: TeamCapacityRepository,
    private readonly cachedTeamCapacityRepository: CachedTeamCapacityRepository,

    // Team routing rules repositories
    private readonly teamRoutingRulesRepository: TeamRoutingRulesRepository,
    private readonly cachedTeamRoutingRulesRepository: CachedTeamRoutingRulesRepository,

    // Business hours configuration repositories
    private readonly businessHoursConfigRepository: BusinessHoursConfigRepository,
    private readonly cachedBusinessHoursConfigRepository: CachedBusinessHoursConfigRepository,

    // Injected services
    private readonly cacheProvider: RedisCacheProvider,
    private readonly usersService: UsersService,
    private readonly transactionService: TransactionService,
    private readonly businessHoursValidationService: BusinessHoursValidatorService,
    private readonly sharedService: SharedService,
  ) {}

  /**
   * @internal
   * Finds a team by its team ID.
   * @param teamId The team ID of the team to find.
   * @returns The team.
   */
  findOneByTeamId(
    teamId: string,
    organizationId: string,
    parentTeamId?: string,
    noRelations?: boolean,
  ) {
    const whereClause: FindOptionsWhere&lt;Team&gt; &#x3D; { uid: teamId, organizationId };
    if (parentTeamId) {
      whereClause.parentTeamId &#x3D; parentTeamId;
    }

    return this.cachedTeamRepository.findByCondition({
      where: whereClause,
      relations: noRelations ? [] : EAGERLY_LOADED_RELATIONS,
    });
  }

  /**
   * @internal
   * Checks if a team exists.
   * @param whereClause The where clause to check.
   * @returns Whether the team exists.
   */
  checkExists(whereClause: FindOptionsWhere&lt;Team&gt;) {
    return this.teamRepository.exists({ where: whereClause });
  }

  /**
   * @internal
   * Checks if a user can read a team.
   * @param teamId The team ID.
   * @param user The user.
   * @returns The team and whether the user belongs to the team.
   */
  async canUserReadTeam(teamId: string, user: CurrentUser) {
    // Find the team by its team ID and organization ID
    const team &#x3D; await this.findOneByTeamId(teamId, user.orgId);
    if (!team) {
      throw new NotFoundException(&quot;Team not found!&quot;);
    }

    // Check if the user belongs to the team
    const userBelongsToTeam &#x3D; await this.userBelongsToTeam(
      user.sub,
      team.id,
      user.orgId,
    );

    // If the team is private, perform additional checks
    if (team.isPrivate) {
      // If the user does not belong to the team, throw an error
      if (!userBelongsToTeam) {
        throw new NotFoundException(&quot;Team not found!&quot;);
      }
    }

    return { team, userInTeam: userBelongsToTeam };
  }

  /**
   * @internal
   * Checks if a user can update a team.
   * @param teamId The team ID.
   * @param user The user.
   * @returns The team and whether the user belongs to the team.
   */
  async canUserUpdateTeam(
    teamId: string,
    user: CurrentUser,
    checkOwner: boolean &#x3D; false,
  ) {
    const { team, userInTeam } &#x3D; await this.canUserReadTeam(teamId, user);

    // If the user does not belong to the team, throw an error
    if (!userInTeam) {
      throw new ForbiddenException(
        &quot;You need to be a member of this team to update the resource!&quot;,
      );
    }

    // If the user is checking if they are the team owner, perform the check
    if (checkOwner) {
      // If the user is not the team owner, throw an error
      const isOwner &#x3D; user.sub &#x3D;&#x3D;&#x3D; team.teamOwnerId;
      const isAdmin &#x3D; userInTeam.role &#x3D;&#x3D;&#x3D; TeamMemberRole.ADMIN;
      if (!isOwner &amp;&amp; !isAdmin) {
        throw new ForbiddenException(
          &quot;Only the team owner or admins can perform this action!&quot;,
        );
      }
    }

    return { team, userInTeam };
  }

  async getTeamsByUser(
    user: CurrentUser,
    select?: FindOptionsSelect&lt;TeamMember&gt;,
    relations?: FindOptionsRelations&lt;TeamMember&gt;,
  ) {
    const teams &#x3D; await this.teamMemberRepository.findAll({
      where: { userId: user.sub, organizationId: user.orgId },
      select,
      relations,
    });

    return teams;
  }

  /**
   * @internal
   * Gets a user and team by their IDs and organization ID. This also validates if the user belongs to the team.
   * @param teamId The team ID.
   * @param userId The user ID.
   * @param organizationId The organization ID.
   * @returns The user and team.
   */
  async getUserAndTeam(teamId: string, userId: string, organizationId: string) {
    // Look up the team if it exists
    const team &#x3D; await this.findOneByTeamId(teamId, organizationId);
    if (!team) {
      throw new NotFoundException(&quot;Team not found!&quot;);
    }

    // Look up the team member if it exists
    const teamMember &#x3D; await this.teamMemberRepository.findByCondition({
      where: { teamId: team.id, userId, organizationId },
    });

    // If the user is not found in the team, throw an error
    if (!teamMember) {
      throw new ForbiddenException(&quot;User is not a member of this team!&quot;);
    }

    return { team };
  }

  /**
   * @internal
   * Creates a new team member.
   * @param teamMember The team member data to create.
   * @returns The created team member.
   */
  createTeamMember(
    teamMember: Partial&lt;TeamMember&gt;,
    context?: TransactionContext,
  ): Promise&lt;TeamMember&gt; {
    const newTeamMember &#x3D; this.teamMemberRepository.create(teamMember);
    if (context) {
      return this.teamMemberRepository.saveWithTxn(context, newTeamMember);
    }

    return this.teamMemberRepository.save(newTeamMember);
  }

  /**
   * @internal
   * Finds teams by their public IDs.
   * @param teamIds The team IDs to find.
   * @param organizationId The organization ID.
   * @returns The teams.
   */
  findTeamsByPublicIds(teamIds: string[], organizationId: string) {
    return this.teamRepository.findAll({
      where: { uid: In(teamIds), organizationId },
    });
  }

  /**
   * @private
   * Finds a team by its team ID from the request.
   * @param teamId The team ID of the team to find.
   * @param request The request object.
   * @returns The team.
   */
  private async findTeamFromUser(teamId: string, user: CurrentUser) {
    // Find the team by its team ID and organization ID
    const team &#x3D; await this.findOneByTeamId(teamId, user.orgId);
    return team;
  }

  /**
   * @internal
   * Checks if a user belongs to a team.
   * @param userId The user ID.
   * @param teamId The team ID.
   * @param organizationId The organization ID.
   * @returns Whether the user belongs to the team.
   */
  async userBelongsToTeam(
    userId: string,
    teamId: string,
    organizationId: string,
    options: {
      relations?: FindOptionsRelations&lt;TeamMember&gt;;
    } &#x3D; {},
  ) {
    const teamMember &#x3D; await this.teamMemberRepository.findByCondition({
      where: { organizationId, userId, teamId },
      relations: options.relations,
    });

    return teamMember;
  }

  /**
   * @internal
   * Removes a team member from a team.
   * @param teamMemberId The team member ID to remove.
   * @returns The removed team member.
   */
  async removeTeamMember(teamMemberId: string, teamId: string) {
    // Find the team member to remove
    const teamMemberToRemove &#x3D; await this.teamMemberRepository.findByCondition({
      where: { userId: teamMemberId, teamId },
    });

    // If the team member is not found, throw an error
    if (!teamMemberToRemove) {
      throw new NotFoundException(
        &quot;Team member not found! Or not a member of this team.&quot;,
      );
    }

    // Remove the team member
    const removedTeamMember &#x3D; await this.teamMemberRepository.remove(
      teamMemberToRemove,
    );

    return removedTeamMember;
  }

  /**
   * Finds a team by its team ID.
   * @param teamId The team ID of the team to find.
   * @returns The team.
   */
  async findOneTeamById(teamId: string, user: CurrentUser) {
    const { team } &#x3D; await this.canUserReadTeam(teamId, user);
    return team;
  }

  /**
   * Finds the members of a team.
   * @param teamId The team ID of the team to find the members of.
   * @param request The request object.
   * @returns The team members.
   */
  async findTeamMembers(teamId: string, user: CurrentUser) {
    const { team } &#x3D; await this.canUserReadTeam(teamId, user);

    // Find all team members for the team
    const teamMembers &#x3D; await this.teamMemberRepository.findAll({
      where: { teamId: team.id },
      relations: EAGERLY_LOADED_RELATIONS_FOR_TEAM_MEMBERS,
    });

    return teamMembers;
  }

  /**
   * Finds all active team members for a team.
   * @param teamId The team ID of the team to find the members of.
   * @param user The user making the request.
   * @returns The team members.
   */
  async findActiveTeamMembers(teamId: string, user: CurrentUser) {
    const team &#x3D; await this.findTeamFromUser(teamId, user);
    if (!team) {
      throw new NotFoundException(&quot;Team not found!&quot;);
    }

    // Find all team members for the team
    const members &#x3D; await this.teamMemberRepository.findAll({
      where: {
        teamId: team.id,
        organizationId: user.orgId,
        user: { status: UserStatus.ACTIVE },
      },
      relations: {
        user: { businessHoursConfig: true, userTeamCapacity: true },
      },
    });

    return members;
  }

  /**
   * Finds the team capacity for a team.
   * @param teamId The team ID of the team to find the capacity of.
   * @param user The user making the request.
   * @returns The team capacity.
   */
  async getTeamCapacity(teamId: string, user: CurrentUser) {
    // Find the team by its team ID and organization ID
    const team &#x3D; await this.findOneByTeamId(teamId, user.orgId);
    if (!team) {
      throw new NotFoundException(&quot;Team not found!&quot;);
    }

    // Find the team capacity by its team ID and organization ID
    const teamCapacity &#x3D;
      await this.cachedTeamCapacityRepository.findByCondition({
        where: { team: { id: team.id }, organization: { id: user.orgId } },
      });

    return teamCapacity;
  }

  /**
   * Finds all the sub teams that belong to a parent team
   * @param user Currently logged in user
   * @param parentTeamId UID of the parent team
   * @return The teams
   */
  async findAllSubTeams(user: CurrentUser, parentTeamId: string) {
    // Find the parent team first
    const parentTeam &#x3D; await this.cachedTeamRepository.findByCondition({
      where: { uid: parentTeamId, organizationId: user.orgId },
    });

    // Find the sub-teams
    const subTeams &#x3D; await this.findAllTeams(user, parentTeam.id);
    return subTeams;
  }

  /**
   * Finds all teams for the user
   * @returns The teams.
   */
  async findAllTeams(user: CurrentUser, parentTeamId?: string) {
    // Construct the initial where clause
    let whereClause: FindOptionsWhere&lt;TeamMember&gt; &#x3D; {
      organizationId: user.orgId,
      userId: user.sub,
    };

    // If parent team id is provided add it as a filter
    if (parentTeamId) {
      whereClause &#x3D; {
        ...whereClause,
        team: { parentTeamId, deletedAt: IsNull() },
      };
    }

    // Find all teams for the organization
    const userInTeams &#x3D; await this.teamMemberRepository.findAll({
      where: whereClause,
      relations: {
        team: {
          parentTeam: true,
          teamOwner: true,
        },
      },
    });

    // Format the teams
    const teams &#x3D; userInTeams
      .map((userInTeam) &#x3D;&gt; userInTeam.team)
      .filter(Boolean);

    // Return the teams
    return teams;
  }

  /**
   * Finds all public teams for the organization.
   * @returns The teams.
   */
  async getAllPublicTeams(user: CurrentUser) {
    const teams &#x3D; await this.teamRepository.findAll({
      where: { organizationId: user.orgId, isPrivate: false },
      relations: { parentTeam: true },
    });

    return teams;
  }

  /**
   * Creates a new team.
   * @param createTeamDto The team data to create.
   * @returns The created team.
   */
  async createTeam(createTeamDto: CreateTeamDto, user: CurrentUser) {
    // If the user is not an organization admin, throw an error
    if (user.userType !&#x3D;&#x3D; UserType.ORG_ADMIN) {
      throw new ForbiddenException(
        &quot;Only organization admins can create teams!&quot;,
      );
    }

    try {
      const createdTeam &#x3D; await this.transactionService.runInTransaction(
        async (txnContext) &#x3D;&gt; {
          // Generate a unique identifier for the team
          const teamId &#x3D; this.generateTeamIdentifier();

          // Get the user email from the user object attached to the request
          const userEmail &#x3D; user.email;
          if (!userEmail) {
            throw new UnauthorizedException(&quot;User is not authenticated!&quot;);
          }

          // If the identifier is not provided, generate a unique identifier for the team
          if (!createTeamDto.identifier) {
            createTeamDto.identifier &#x3D; generateIdentifier(createTeamDto.name);
          }

          // If the parent team ID is provided, find the parent team
          let parentTeam: Team | null;
          if (createTeamDto.parentTeamId) {
            parentTeam &#x3D; await this.findOneByTeamId(
              createTeamDto.parentTeamId,
              user.orgId,
            );

            // If the parent team is a sub-team, throw an error
            if (parentTeam?.parentTeamId) {
              throw new BadRequestException(
                &quot;Cannot create a sub-team inside a sub-team!&quot;,
              );
            }

            // If the parent team is not found, throw an error
            if (!parentTeam) {
              throw new NotFoundException(&quot;Parent team not found!&quot;);
            }
          }

          // Create the team
          const newTeam &#x3D; await this.teamRepository.saveWithTxn(txnContext, {
            uid: teamId,
            identifier: createTeamDto.identifier,
            name: createTeamDto.name,
            description: createTeamDto.description,
            teamOwnerId: user.sub,
            organizationId: user.orgId,
            parentTeamId: parentTeam?.id,
            isPrivate: createTeamDto?.isPrivate,
          });

          // Create team&#x27;s business hours config
          const businessHoursConfig &#x3D;
            await this.businessHoursConfigRepository.saveWithTxn(txnContext, {
              teamId: newTeam.id,
              organizationId: user.orgId,
              monday: {
                isActive: true,
                slots: [{ start: &quot;00:00&quot;, end: &quot;23:59&quot; }],
              },
              tuesday: {
                isActive: true,
                slots: [{ start: &quot;00:00&quot;, end: &quot;23:59&quot; }],
              },
              wednesday: {
                isActive: true,
                slots: [{ start: &quot;00:00&quot;, end: &quot;23:59&quot; }],
              },
              thursday: {
                isActive: true,
                slots: [{ start: &quot;00:00&quot;, end: &quot;23:59&quot; }],
              },
              friday: {
                isActive: true,
                slots: [{ start: &quot;00:00&quot;, end: &quot;23:59&quot; }],
              },
              saturday: {
                isActive: true,
                slots: [{ start: &quot;00:00&quot;, end: &quot;23:59&quot; }],
              },
              sunday: {
                isActive: true,
                slots: [{ start: &quot;00:00&quot;, end: &quot;23:59&quot; }],
              },
            });

          // Create team configurations
          const teamConfigurations &#x3D;
            await this.teamConfigurationRepository.saveWithTxn(txnContext, {
              teamId: newTeam.id,
              organizationId: user.orgId,
              businessHoursConfigId: businessHoursConfig.id,
            });

          // Update the team with its configs
          await this.teamRepository.updateWithTxn(
            txnContext,
            { id: newTeam.id },
            { configurationId: teamConfigurations.id },
          );

          // Seed default status, priority and type if the team is not a sub-team
          if (!createTeamDto.parentTeamId) {
            await this.sharedService.seedDefaultStatusPriorityAndType(
              newTeam,
              txnContext,
            );
          }

          // Create the team member and make him the member
          await this.createTeamMember(
            {
              teamId: newTeam.id,
              userId: user.sub,
              isActive: true,
              organizationId: user.orgId,
              role: TeamMemberRole.ADMIN,
            },
            txnContext,
          );

          return newTeam;
        },
      );

      const returnableTeam &#x3D; await this.findOneTeamById(createdTeam.uid, user);
      return returnableTeam;
    } catch (error) {
      if (error instanceof QueryFailedError) {
        if (error.code &#x3D;&#x3D;&#x3D; POSTGRES_ERROR_CODES.DUPLICATE_KEY_VALUE) {
          // If the error is due to a duplicate team name, throw an error
          if ((error as any)?.constraint &#x3D;&#x3D;&#x3D; &quot;unique_team_name_ex_deleted&quot;) {
            throw new ConflictException(&quot;Team name already exists!&quot;);
          }

          // If the error is due to a duplicate team ID, throw an error
          if ((error as any)?.constraint &#x3D;&#x3D;&#x3D; &quot;unique_team_id&quot;) {
            throw new InternalServerErrorException(
              &quot;Something went wrong while creating the team! Please try again.&quot;,
            );
          }
        }
      }

      // Propagate the error
      throw error;
    }
  }

  /**
   * Updates a team.
   * @param teamId The team ID to update.
   * @param updateTeamDto The team data to update.
   * @param user The user making the request.
   * @returns The updated team.
   */
  async updateTeam(
    teamId: string,
    updateTeamDto: UpdateTeamDto,
    user: CurrentUser,
  ) {
    // Find the team by its team ID and organization ID
    const { team } &#x3D; await this.canUserUpdateTeam(teamId, user, true);

    // If the identifier is provided, check if it is unique
    if (updateTeamDto.identifier) {
      const identifierExists &#x3D; await this.teamRepository.exists({
        where: {
          identifier: updateTeamDto.identifier,
          organizationId: user.orgId,
          id: Not(team.id),
        },
      });

      // If the identifier already exists, throw an error
      if (identifierExists) {
        throw new ConflictException(
          &quot;Identifier already exists for another team!&quot;,
        );
      }
    }

    // Update the provided team with the new data
    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      // Update the team
      const updateTeamResult &#x3D; await this.teamRepository.updateWithTxn(
        txnContext,
        { id: team.id },
        {
          name: updateTeamDto.name,
          description: updateTeamDto.description,
          isPrivate: updateTeamDto.isPrivate,
          icon: updateTeamDto.icon,
          color: updateTeamDto.color,
          identifier: updateTeamDto.identifier,
        },
      );

      // Invalidate the query
      await this.cachedTeamRepository.invalidateQuery({
        where: { uid: teamId, organizationId: user.orgId },
        relations: EAGERLY_LOADED_RELATIONS,
      });

      return updateTeamResult;
    });

    // Fetch the returnable team
    const returnableTeam &#x3D; await this.findOneTeamById(teamId, user);
    return returnableTeam;
  }

  /**
   * Deletes a team.
   * @param teamId The team ID to delete.
   * @param request The request object.
   * @returns The deleted team.
   */
  async deleteOneTeam(teamId: string, user: CurrentUser) {
    // Find the team by its team ID and organization ID
    const { team } &#x3D; await this.canUserUpdateTeam(teamId, user, true);

    // Delete team and team configurations in a transaction
    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      const commonOptions &#x3D; {
        id: team.configurationId,
        teamId: team.id,
        organizationId: user.orgId,
      };

      // Delete the business hours config
      await this.cachedBusinessHoursConfigRepository.softDeleteWithTxn(
        txnContext,
        commonOptions,
      );

      // Delete the team configurations
      await this.cachedTeamConfigurationRepository.softDeleteWithTxn(
        txnContext,
        commonOptions,
      );

      // Delete the team members
      await this.teamMemberRepository.softRemoveByConditionTxn(txnContext, {
        where: { teamId: team.id, organizationId: user.orgId },
      });

      // Delete the team
      await this.cachedTeamRepository.softDeleteWithTxn(txnContext, {
        id: team.id,
        uid: team.uid,
        organizationId: user.orgId,
      });

      await this.cachedTeamRepository.invalidateTeamCacheWithKey({
        where: { uid: team.uid, organizationId: user.orgId },
        relations: EAGERLY_LOADED_RELATIONS,
      });
    });
  }

  /**
   * Adds a team member to a team.
   * @param teamId The team ID to add the member to.
   * @param addTeamMemberDto The team member data to add.
   * @param request The request object.
   * @returns The added team member.
   */
  async addMemberToTeam(
    teamId: string,
    addTeamMemberDto: AddTeamMemberDto,
    user: CurrentUser,
  ) {
    let userToAdd: User | null;

    // Find the user by their email address
    if (addTeamMemberDto.email) {
      userToAdd &#x3D; await this.usersService.findOneByEmail(
        addTeamMemberDto.email,
        user.orgId,
      );
    } else if (addTeamMemberDto.userId) {
      userToAdd &#x3D; await this.usersService.findOneByPublicId(
        addTeamMemberDto.userId,
        user.orgId,
      );
    }

    // If the user to add is not found, throw an error
    if (!userToAdd) {
      throw new NotFoundException(&quot;User not found!&quot;);
    }

    // Find the team by its team ID and organization ID
    const team &#x3D; await this.findOneByTeamId(teamId, user.orgId);
    if (!team) {
      throw new NotFoundException(&quot;Team not found!&quot;);
    }

    // Check if the user who is inviting and the user to add belong to the same organization
    const teamOrg &#x3D; team.organizationId;
    if (teamOrg !&#x3D;&#x3D; user.orgId &amp;&amp; teamOrg !&#x3D;&#x3D; userToAdd.organizationId) {
      throw new ForbiddenException(&quot;Invalid foreign organization invite!&quot;);
    }

    // Check if the user who is inviting belongs to the team
    const userBelongsToTeam &#x3D; await this.userBelongsToTeam(
      user.sub,
      team.id,
      user.orgId,
    );

    // Checks to perform if the team is private
    if (team.isPrivate) {
      // User is self-adding to a private team, throw a Not Found Exception
      // to prevent user from checking if the team exists
      if (userToAdd.id &#x3D;&#x3D;&#x3D; user.sub) {
        throw new NotFoundException(&quot;Team not found!&quot;);
      }

      // If the user is not the team owner, throw an error
      if (user.sub !&#x3D;&#x3D; team.teamOwnerId) {
        throw new ForbiddenException(
          &quot;Only the team owner can invite new members to a private team!&quot;,
        );
      }

      // If the user does not belong to the team, throw an error
      if (!userBelongsToTeam) {
        throw new NotFoundException(&quot;Team not found!&quot;);
      }
    }

    // If the user does not belong to the team, throw an error
    if (!userBelongsToTeam &amp;&amp; user.sub !&#x3D;&#x3D; userToAdd.id) {
      throw new ForbiddenException(
        &quot;You need to be a member of this team to invite new members!&quot;,
      );
    }

    // CHECK: If the user is not an admin and is trying to add an admin, throw an error
    // NOTE: Do not check if the user&#x27;s role is MEMBER because you might end up adding
    // more roles but the admin remains admin therefore the NOT check is better
    const isMember &#x3D;
      userBelongsToTeam &amp;&amp; userBelongsToTeam.role !&#x3D;&#x3D; TeamMemberRole.ADMIN;
    if (addTeamMemberDto.isAdmin &amp;&amp; isMember) {
      throw new ForbiddenException(
        &quot;Only admins can add new admins to the team!&quot;,
      );
    }

    try {
      // Create the team member
      const teamMember &#x3D; await this.createTeamMember({
        teamId: team.id,
        userId: userToAdd.id,
        invitedById: user.sub,
        organizationId: user.orgId,
      });

      // If the team has a parent team, add the user to the parent team
      if (team.parentTeamId) {
        await this.createTeamMember({
          teamId: team.parentTeamId,
          userId: userToAdd.id,
          invitedById: user.sub,
          organizationId: user.orgId,
        });
      }

      // Find the team member by its ID
      const returnableTeamMember &#x3D;
        await this.teamMemberRepository.findByCondition({
          where: {
            id: teamMember.id,
            teamId: team.id,
            userId: userToAdd.id,
            organizationId: user.orgId,
          },
          relations: EAGERLY_LOADED_RELATIONS_FOR_TEAM_MEMBERS,
        });

      return returnableTeamMember;
    } catch (error) {
      if (error instanceof QueryFailedError) {
        if (error.code &#x3D;&#x3D;&#x3D; POSTGRES_ERROR_CODES.DUPLICATE_KEY_VALUE) {
          // If the error is due to a duplicate team name, throw an error
          if ((error as any)?.constraint &#x3D;&#x3D;&#x3D; &quot;unique_team_member&quot;) {
            throw new ConflictException(
              &quot;This user is already a member of this team!&quot;,
            );
          }
        }
      }

      throw error;
    }
  }

  /**
   * Removes a team member from a team.
   * @param teamId The team ID to remove the member from.
   * @param memberId The member ID to remove from the team.
   * @param request The request object.
   * @returns The removed team member.
   */
  async removeMemberFromTeam(
    teamId: string,
    memberId: string,
    user: CurrentUser,
  ) {
    // Find the user to remove and the team
    const [userToRemove, team] &#x3D; await Promise.all([
      this.usersService.findOneByPublicId(memberId),
      this.findOneByTeamId(teamId, user.orgId),
    ]);

    // If the user to remove is not found, throw an error
    if (!userToRemove) {
      throw new NotFoundException(&quot;User not found!&quot;);
    }

    // If the team is not found, throw an error
    if (!team) {
      throw new NotFoundException(&quot;Team not found!&quot;);
    }

    // Check if the user belongs to the team
    const userBelongsToTeam &#x3D; await this.userBelongsToTeam(
      user.sub,
      team.id,
      user.orgId,
    );

    // Checks to perform if the team is private
    if (team.isPrivate) {
      // If the user does not belong to the team, throw an error
      if (!userBelongsToTeam) {
        throw new NotFoundException(&quot;Team not found!&quot;);
      }
    }

    // If the user is not the team owner, throw an error
    if (user.sub !&#x3D;&#x3D; team.teamOwnerId &amp;&amp; user.uid !&#x3D;&#x3D; memberId) {
      throw new ForbiddenException(
        &quot;Only the team owner can remove members from the team!&quot;,
      );
    }

    // If the user to remove is the team owner, throw an error
    if (team.teamOwner.uid &#x3D;&#x3D;&#x3D; memberId) {
      // If the user trying to delete the team owner is not the team owner
      if (user.sub !&#x3D;&#x3D; team.teamOwnerId) {
        throw new ForbiddenException(&quot;Cannot remove the team owner!&quot;);
      }

      // If the user is the team owner, throw an error
      throw new ForbiddenException(
        &quot;You are the owner of this team, please archive or delete the team instead.&quot;,
      );
    }

    // Remove the team member
    await this.removeTeamMember(userToRemove.id, team.id);
  }

  /**
   * Gets a team&#x27;s configurations.
   * @param teamId The team ID to get the configurations of.
   * @param user The user making the request.
   * @returns The team configurations.
   */
  async getTeamConfigurations(
    teamId: string,
    user: CurrentUser,
  ): Promise&lt;CombinedTeamConfig&gt; {
    const { team } &#x3D; await this.canUserUpdateTeam(teamId, user, true);

    const lookupClause &#x3D; { where: { teamId: team.id } };
    const [teamConfig, businessHoursConfig] &#x3D; await Promise.all([
      this.cachedTeamConfigurationRepository.findByCondition(lookupClause),
      this.cachedBusinessHoursConfigRepository.findByCondition(lookupClause),
    ]);

    return {
      teamId: team.uid,
      teamConfig,
      businessHoursConfig,
    };
  }

  /**
   * Updates a team&#x27;s configurations and business hours.
   * @param teamId The team ID to update the configurations of.
   * @param updateTeamsDto The team configurations data to update.
   * @param user The user making the request.
   * @returns The updated team configurations.
   */
  async updateTeamConfigurations(
    teamId: string,
    updateTeamsDto: UpdateTimezoneWorkingHoursDto,
    user: CurrentUser,
  ) {
    const {
      holidays,
      timezone,
      dailyConfig,
      commonDailyConfig,
      commonSlots,
      routingRespectsTimezone,
      routingRespectsUserTimezone,
      routingRespectsUserAvailability,
      userRoutingStrategy,
    } &#x3D; updateTeamsDto;

    // Check if the user can update the team
    const { team } &#x3D; await this.canUserUpdateTeam(teamId, user, true);

    const currentTeamConfig &#x3D;
      await this.cachedTeamConfigurationRepository.findByCondition({
        where: { teamId: team.id },
      });

    // If the common daily config is enabled, validate configurations
    if (commonDailyConfig) {
      // If the common slots are not provided, throw an error
      if (!commonSlots) {
        throw new BadRequestException(
          &quot;Common slots are required when common daily config is enabled!&quot;,
        );
      }

      // If the daily config is provided, throw an error
      if (dailyConfig) {
        throw new BadRequestException(
          &quot;Cannot update both common daily config and daily config!&quot;,
        );
      }
    }

    // If the business hours are being updated, validate them
    if (dailyConfig) {
      const tz &#x3D; timezone || currentTeamConfig.timezone || &quot;UTC&quot;;
      const validationResult &#x3D;
        this.businessHoursValidationService.validateBusinessHours(
          dailyConfig,
          tz,
        );

      // If the business hours are invalid, throw an error
      if (!validationResult.isValid) {
        throw new BadRequestException(validationResult.error);
      }
    }

    // Update the team configurations in a transaction
    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      const commonOptions &#x3D; {
        id: team.configurationId,
        teamId: team.id,
        organizationId: user.orgId,
      };

      const commonCacheOptions &#x3D; {
        teamId: team.id,
      };

      // Update the team&#x27;s timezone
      await this.teamConfigurationRepository.updateWithTxn(
        txnContext,
        commonOptions,
        {
          holidays,
          timezone,
          routingRespectsTimezone,
          routingRespectsUserTimezone,
          routingRespectsUserAvailability,
          userRoutingStrategy,
        },
      );

      // Update the business hours config
      if (dailyConfig) {
        await this.businessHoursConfigRepository.updateWithTxn(
          txnContext,
          { organizationId: user.orgId, teamId: team.id },
          { ...dailyConfig, commonDailyConfig: false },
        );

        // Invalidate the business hours cache
        await this.cachedBusinessHoursConfigRepository.invalidateBusinessHoursCache(
          commonCacheOptions,
        );
      }

      // If the common daily config is enabled, update the configurations
      if (commonDailyConfig &amp;&amp; commonSlots) {
        const dailyConfig &#x3D; constructDailyConfigFromCommonSlots(commonSlots);
        await this.businessHoursConfigRepository.updateWithTxn(
          txnContext,
          { organizationId: user.orgId, teamId: team.id },
          { ...dailyConfig, commonDailyConfig },
        );

        // Invalidate the business hours cache
        await this.cachedBusinessHoursConfigRepository.invalidateBusinessHoursCache(
          commonCacheOptions,
        );
      }

      // Invalidate the team configuration cache
      await this.cachedTeamConfigurationRepository.invalidateTeamConfigurationCache(
        commonCacheOptions,
      );
    });

    const lookupClause &#x3D; { where: { teamId: team.id } };
    const [teamConfig, businessHoursConfig] &#x3D; await Promise.all([
      this.cachedTeamConfigurationRepository.findByCondition(lookupClause),
      this.cachedBusinessHoursConfigRepository.findByCondition(lookupClause),
    ]);

    // If the business hours or team configurations are not found, throw an error
    if (!businessHoursConfig || !teamConfig) {
      throw new NotFoundException(
        &quot;Team configurations are in an inconsistent state!&quot;,
      );
    }

    return {
      teamId: team.uid,
      teamConfig,
      businessHoursConfig,
    };
  }

  /**
   * Creates a routing rule for a team.
   * @param teamId The team ID to create the routing rule for.
   * @param user The user making the request.
   * @param createRoutingRuleDto The routing rule data to create.
   * @returns The created routing rule.
   */
  async createRoutingRule(
    teamId: string,
    user: CurrentUser,
    createRoutingRuleDto: CreateRoutingRuleGroupDto,
  ) {
    // Check if the user have permission to create team routing rules
    const { team } &#x3D; await this.canUserUpdateTeam(teamId, user, true);

    // Does sub-teams exist
    const doesSubTeamsExist &#x3D; await this.teamRepository.exists({
      where: {
        parentTeamId: team.id,
        organizationId: user.orgId,
      },
    });

    // If sub-teams do not exist, throw an error since we cannot create routing
    // rules for a team that does not have sub-teams
    if (!doesSubTeamsExist) {
      throw new BadRequestException(
        &quot;Cannot create routing rules for a team that does not have sub-teams!&quot;,
      );
    }

    // Check if the result team exists
    const doesResultTeamExists &#x3D; await this.teamRepository.exists({
      where: {
        uid: createRoutingRuleDto.resultTeamId,
        organizationId: user.orgId,
        parentTeamId: team.id,
      },
    });

    // If the result team does not exist, throw an error
    if (!doesResultTeamExists) {
      throw new NotFoundException(&quot;Result team not found!&quot;);
    }

    // Check if the AND or OR rules are provided
    if (!createRoutingRuleDto.andRules &amp;&amp; !createRoutingRuleDto.orRules) {
      throw new BadRequestException(
        &quot;At least one set of rules are required to create a routing rule!&quot;,
      );
    }

    // Get the last rule
    const previousRules &#x3D; await this.teamRoutingRulesRepository.findAll({
      where: { team: { id: team.id }, organization: { id: user.orgId } },
      order: { priority: &quot;DESC&quot; },
      take: 1,
    });

    // Get the last rule and its priority
    let lastRule: TeamRoutingRules;
    let lastRulePriority &#x3D; 0;
    if (previousRules.length &gt; 0) {
      lastRule &#x3D; previousRules?.[0];
      lastRulePriority &#x3D; lastRule?.priority;
    }

    // Create the routing rules in txn
    const rule &#x3D; await this.transactionService.runInTransaction(
      async (txnContext) &#x3D;&gt; {
        const ruleData: DeepPartial&lt;TeamRoutingRules&gt; &#x3D; {
          name: createRoutingRuleDto.name,
          description: createRoutingRuleDto.description,
          team: { id: team.id },
          organization: { id: user.orgId },
          createdBy: { id: user.sub },
          andRules: createRoutingRuleDto.andRules,
          orRules: createRoutingRuleDto.orRules,
          priority:
            createRoutingRuleDto.evaluationOrder ?? lastRulePriority + 1,
          resultTeamId: createRoutingRuleDto.resultTeamId,
        };

        // Create the routing rule
        const routingRule &#x3D; await this.teamRoutingRulesRepository.saveWithTxn(
          txnContext,
          ruleData,
        );

        return routingRule;
      },
    );

    // Get the returnable rule
    const returnableRule &#x3D;
      await this.cachedTeamRoutingRulesRepository.findByCondition({
        where: { id: rule.id },
        relations: TEAM_ROUTING_RULES_RELATIONS,
      });

    // Invalidate and re-cache the team routing rules
    this.invalidateAndReCacheTeamRoutingRules(team.id, user.orgId);

    // Invalidate the team routing rules cache
    return returnableRule;
  }

  /**
   * Updates a routing rule for a team.
   * @param teamId The team ID to update the routing rule for.
   * @param ruleId The rule ID to update.
   * @param user The user making the request.
   * @param updateRoutingRuleDto The routing rule data to update.
   * @returns The updated routing rule.
   */
  async updateRoutingRule(
    teamId: string,
    ruleId: string,
    user: CurrentUser,
    updateRoutingRuleDto: UpdateRoutingRuleGroupDto,
  ) {
    // Check if the user can update the team
    const { team } &#x3D; await this.canUserUpdateTeam(teamId, user, true);

    // Check if the rule exists
    const rule &#x3D; await this.teamRoutingRulesRepository.exists({
      where: {
        uid: ruleId,
        team: { id: team.id },
        organization: { id: user.orgId },
      },
    });

    // If the rule does not exist, throw an error
    if (!rule) {
      throw new NotFoundException(&quot;Rule not found!&quot;);
    }

    // Check if the result team exists
    if (updateRoutingRuleDto.resultTeamId) {
      const doesResultTeamExists &#x3D; await this.teamRepository.exists({
        where: {
          uid: updateRoutingRuleDto.resultTeamId,
          organizationId: user.orgId,
          parentTeamId: team.id,
        },
      });

      // If the result team does not exist, throw an error
      if (!doesResultTeamExists) {
        throw new NotFoundException(&quot;Result team not found!&quot;);
      }
    }

    // Update the routing rule in a transaction
    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      const update: DeepPartial&lt;TeamRoutingRules&gt; &#x3D; {};

      // Update the name
      if (updateRoutingRuleDto.name) {
        update.name &#x3D; updateRoutingRuleDto.name;
      }

      // Update the description
      if (updateRoutingRuleDto.description) {
        update.description &#x3D; updateRoutingRuleDto.description;
      }

      // Update the result team ID
      if (updateRoutingRuleDto.resultTeamId) {
        update.resultTeamId &#x3D; updateRoutingRuleDto.resultTeamId;
      }

      // Update the evaluation order
      if (updateRoutingRuleDto.evaluationOrder) {
        update.priority &#x3D; updateRoutingRuleDto.evaluationOrder;
      }

      // Update the AND rules
      if (updateRoutingRuleDto.andRules) {
        update.andRules &#x3D; updateRoutingRuleDto.andRules;
      }

      // Update the OR rules
      if (updateRoutingRuleDto.orRules) {
        update.orRules &#x3D; updateRoutingRuleDto.orRules;
      }

      // Update the routing rule
      await this.teamRoutingRulesRepository.updateWithTxn(
        txnContext,
        {
          uid: ruleId,
          team: { id: team.id },
          organization: { id: user.orgId },
        },
        update,
      );

      return update;
    });

    // Get the returnable rule
    const returnableRule &#x3D;
      await this.teamRoutingRulesRepository.findByCondition({
        where: {
          uid: ruleId,
          organization: { id: user.orgId },
          team: { id: team.id },
        },
        relations: TEAM_ROUTING_RULES_RELATIONS,
      });

    // Invalidate and re-cache the team routing rules
    this.invalidateAndReCacheTeamRoutingRules(team.id, user.orgId);

    return returnableRule;
  }

  /**
   * Gets a team&#x27;s routing rules.
   * @param teamId The team ID to get the routing rules of.
   * @param user The user making the request.
   * @returns The team&#x27;s routing rules.
   */
  async getTeamRoutingRules(teamId: string, user: CurrentUser) {
    // Check if the user can update the team
    const { team } &#x3D; await this.canUserUpdateTeam(teamId, user);

    // Get the team routing rules from cache
    const cacheKey &#x3D; &#x60;team-routing-${team.id}-${user.orgId}&#x60;;
    const cachedRoutingRules &#x3D; await this.cacheProvider.get&lt;string&gt;(cacheKey);

    // If the routing rules are cached, return them
    if (cachedRoutingRules) {
      const rules &#x3D; JSON.parse(cachedRoutingRules) as TeamRoutingRules[];
      return rules;
    }

    // If the routing rules are not cached, get them from the database
    const routingRules &#x3D; await this.teamRoutingRulesRepository.findAll({
      where: { team: { id: team.id }, organization: { id: user.orgId } },
      relations: TEAM_ROUTING_RULES_RELATIONS,
    });

    try {
      // Cache the routing rules
      await this.cacheProvider.set(
        cacheKey,
        JSON.stringify(routingRules),
        CACHE_TTL.MONTH * 3,
      );
    } catch (error) {
      this.logger.error(
        &#x60;Failed to cache team routing rules for team ${team.id}, error: ${error.message}&#x60;,
      );
    }

    // Return the routing rules
    return routingRules;
  }

  /**
   * Deletes a routing rule for a team.
   * @param teamId The team ID to delete the routing rule for.
   * @param ruleId The rule ID to delete.
   * @param user The user making the request.
   */
  async deleteRoutingRule(teamId: string, ruleId: string, user: CurrentUser) {
    // Check if the user can update the team
    const { team } &#x3D; await this.canUserUpdateTeam(teamId, user, true);

    // Check if the rule exists
    const rule &#x3D; await this.teamRoutingRulesRepository.findByCondition({
      where: {
        uid: ruleId,
        team: { id: team.id },
        organization: { id: user.orgId },
      },
    });

    // If the rule does not exist, throw an error
    if (!rule) {
      throw new NotFoundException(&quot;Rule not found!&quot;);
    }

    // Delete the rule
    await this.teamRoutingRulesRepository.remove(rule);

    // Invalidate and re-cache the team routing rules
    await this.invalidateAndReCacheTeamRoutingRules(team.id, user.orgId);
  }

  /**
   * Generates the business hours updates for a team.
   * @param businessHours The business hours configurations to update.
   * @returns The business hours updates.
   */
  private generateBusinessHoursUpdates(businessHours: BusinessHoursConfigDto) {
    const updateClause &#x3D; {};

    for (const day in businessHours) {
      const dayProperty: BusinessDayDto &#x3D; businessHours[day];

      // If the day is active, update the business hours
      if (dayProperty.isActive) {
        updateClause[day] &#x3D; dayProperty.slots;
      } else {
        updateClause[day] &#x3D; null;
      }
    }

    return updateClause;
  }

  /**
   * Invalidates and re-caches the team routing rules.
   * @param teamId The team ID to invalidate and re-cache the routing rules for.
   * @param orgId The organization ID to invalidate and re-cache the routing rules for.
   */
  private async invalidateAndReCacheTeamRoutingRules(
    teamId: string,
    orgId: string,
  ) {
    try {
      // Generate the cache key
      const cacheKey &#x3D; &#x60;team-routing-${teamId}-${orgId}&#x60;;

      // Purge the cache
      await this.cacheProvider.del(cacheKey);

      // Get the routing rules
      const routingRules &#x3D; await this.teamRoutingRulesRepository.findAll({
        where: { team: { id: teamId }, organization: { id: orgId } },
        relations: TEAM_ROUTING_RULES_RELATIONS,
      });

      // Cache the routing rules
      await this.cacheProvider.set(
        cacheKey,
        JSON.stringify(routingRules),
        CACHE_TTL.WEEK * 2,
      );
    } catch (error) {
      this.logger.error(
        &#x60;Failed to purge and rec-ache team routing rules for team ${teamId}, error: ${error.message}&#x60;,
      );
    }
  }

  /**
   * Generates a team identifier.
   * @returns The generated team identifier.
   */
  private generateTeamIdentifier(): string {
    return IdGeneratorUtils.generate(&quot;T&quot;);
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'TeamsService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
