<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >OrganizationService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/organization/services/organization.service.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#create" >create</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createInvite" >createInvite</a>
                            </li>
                            <li>
                                <a href="#findAll" >findAll</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findOne" >findOne</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findOneByIdentifier" >findOneByIdentifier</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#generateOrganizationIdentifier" >generateOrganizationIdentifier</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#joinOrganization" >joinOrganization</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#publishEventToSNSQueue" >publishEventToSNSQueue</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#remove" >remove</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#update" >update</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(sentryService: SentryService, logger: ILogger, organizationRepository: OrganizationRepository, organizationDomainsRepository: OrganizationDomainsRepository, createOrgAndOrgAdminSaga: <a href="../injectables/CreateOrgAndOrgAdminSaga.html" target="_self">CreateOrgAndOrgAdminSaga</a>, usersService: <a href="../injectables/UsersService.html" target="_self">UsersService</a>, organizationInvitationsRepository: OrganizationInvitationsRepository, organizationSNSPublisherQueue: Queue, organizationSNSEventsFactory: <a href="../injectables/OrganizationSNSEventsFactory.html" target="_self">OrganizationSNSEventsFactory</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="39" class="link-to-prism">src/organization/services/organization.service.ts:39</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>sentryService</td>
                                                  
                                                        <td>
                                                                    <code>SentryService</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>logger</td>
                                                  
                                                        <td>
                                                                    <code>ILogger</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>organizationRepository</td>
                                                  
                                                        <td>
                                                                    <code>OrganizationRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>organizationDomainsRepository</td>
                                                  
                                                        <td>
                                                                    <code>OrganizationDomainsRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>createOrgAndOrgAdminSaga</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/CreateOrgAndOrgAdminSaga.html" target="_self" >CreateOrgAndOrgAdminSaga</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>usersService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/UsersService.html" target="_self" >UsersService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>organizationInvitationsRepository</td>
                                                  
                                                        <td>
                                                                    <code>OrganizationInvitationsRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>organizationSNSPublisherQueue</td>
                                                  
                                                        <td>
                                                                    <code>Queue</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>organizationSNSEventsFactory</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/OrganizationSNSEventsFactory.html" target="_self" >OrganizationSNSEventsFactory</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="create"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>create</b></span>
                        <a href="#create"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>create(createOrganizationDto: <a href="../classes/CreateOrganizationDto.html" target="_self">CreateOrganizationDto</a>, authToken?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="318"
                                    class="link-to-prism">src/organization/services/organization.service.ts:318</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Creates a new organization.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>createOrganizationDto</td>
                                            <td>
                                                            <code><a href="../classes/CreateOrganizationDto.html" target="_self" >CreateOrganizationDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The organization data to create.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>authToken</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;Organization&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The created organization.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createInvite"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>createInvite</b></span>
                        <a href="#createInvite"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createInvite(inviteDto: <a href="../classes/InviteUserDto.html" target="_self">InviteUserDto</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="295"
                                    class="link-to-prism">src/organization/services/organization.service.ts:295</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Creates an invitation for a user to join an organization.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>inviteDto</td>
                                            <td>
                                                            <code><a href="../classes/InviteUserDto.html" target="_self" >InviteUserDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The DTO containing the email of the user to invite.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The invitation.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAll"></a>
                    <span class="name">
                        <span ><b>findAll</b></span>
                        <a href="#findAll"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findAll(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="108"
                                    class="link-to-prism">src/organization/services/organization.service.ts:108</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds all organizations.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;Organization[]&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>All organizations.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findOne"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findOne</b></span>
                        <a href="#findOne"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findOne(id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="117"
                                    class="link-to-prism">src/organization/services/organization.service.ts:117</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds an organization by ID.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization to find.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;Organization&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The organization.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findOneByIdentifier"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findOneByIdentifier</b></span>
                        <a href="#findOneByIdentifier"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findOneByIdentifier(orgIdentifier: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="150"
                                    class="link-to-prism">src/organization/services/organization.service.ts:150</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds an organization by identifier.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>orgIdentifier</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;Organization&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The organization.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="generateOrganizationIdentifier"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>generateOrganizationIdentifier</b></span>
                        <a href="#generateOrganizationIdentifier"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>generateOrganizationIdentifier()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="559"
                                    class="link-to-prism">src/organization/services/organization.service.ts:559</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Generates an organization identifier.</p>
</div>

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </div>
                            <div class="io-description">
                                <p>The generated organization identifier.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="joinOrganization"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>joinOrganization</b></span>
                        <a href="#joinOrganization"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>joinOrganization(joinOrgDto: <a href="../classes/JoinOrganizationDto.html" target="_self">JoinOrganizationDto</a>, authToken: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="163"
                                    class="link-to-prism">src/organization/services/organization.service.ts:163</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Joins an organization.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>joinOrgDto</td>
                                            <td>
                                                            <code><a href="../classes/JoinOrganizationDto.html" target="_self" >JoinOrganizationDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The DTO containing the organization ID and the user ID.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>authToken</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The organization.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="publishEventToSNSQueue"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>publishEventToSNSQueue</b></span>
                        <a href="#publishEventToSNSQueue"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>publishEventToSNSQueue(event: T, eventData: T, user: literal type)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="66"
                                    class="link-to-prism">src/organization/services/organization.service.ts:66</a></div>
                        </td>
                    </tr>

                    <tr>
                        <td class="col-md-4">
                            <b>Type parameters :</b>
                            <ul class="type-parameters">
                                    <li>Q</li>
                                    <li>T</li>
                            </ul>
                        </td>
                    </tr>

            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Publishes an account event to SNS.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>event</td>
                                            <td>
                                                        <code>T</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The event to publish.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>eventData</td>
                                            <td>
                                                        <code>T</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The data to publish.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                        <code>literal type</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The user associated with the event.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="remove"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>remove</b></span>
                        <a href="#remove"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>remove(id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, currentUser: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="512"
                                    class="link-to-prism">src/organization/services/organization.service.ts:512</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Removes an organization by ID.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization to remove.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>currentUser</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;void&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="update"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>update</b></span>
                        <a href="#update"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>update(id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, updateOrganizationDto: <a href="../classes/UpdateOrganizationDto.html" target="_self">UpdateOrganizationDto</a>, currentUser: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="442"
                                    class="link-to-prism">src/organization/services/organization.service.ts:442</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Updates an existing organization.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization to update.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>updateOrganizationDto</td>
                                            <td>
                                                            <code><a href="../classes/UpdateOrganizationDto.html" target="_self" >UpdateOrganizationDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The organization data to update.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>currentUser</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;Organization&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The updated organization.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { InjectQueue } from &quot;@nestjs/bullmq&quot;;
import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException,
} from &quot;@nestjs/common&quot;;
import { SentryService } from &quot;@repo/nestjs-commons/filters&quot;;
import { ILogger } from &quot;@repo/nestjs-commons/logger&quot;;
import { OrganizationEvents } from &quot;@repo/thena-eventbridge&quot;;
import {
  Organization,
  OrganizationDomainsRepository,
  OrganizationInvitationsRepository,
  OrganizationRepository,
  UserType,
} from &quot;@repo/thena-platform-entities&quot;;
import { Queue } from &quot;bullmq&quot;;
import * as _ from &quot;lodash&quot;;
import tldts from &quot;tldts&quot;;
import { POSTGRES_ERROR_CODES } from &quot;../../common/constants/postgres-errors.constants&quot;;
import { CurrentUser } from &quot;../../common/decorators&quot;;
import { IdGeneratorUtils } from &quot;../../common/utils&quot;;
import { QueueNames } from &quot;../../constants/queue.constants&quot;;
import { UsersService } from &quot;../../users/services/users.service&quot;;
import {
  CreateOrganizationDto,
  InviteUserDto,
  JoinOrganizationDto,
  UpdateOrganizationDto,
} from &quot;../dto/organization.dto&quot;;
import { OrganizationSNSEventsFactory } from &quot;../events/organization-sns-events.factory&quot;;
import { SNSEvent } from &quot;../interfaces/sns-events.interface&quot;;
import { CreateOrgAndOrgAdminSaga } from &quot;../sagas&quot;;

@Injectable()
export class OrganizationService {
  constructor(
    // Sentry and logger
    @Inject(&quot;Sentry&quot;) private readonly sentryService: SentryService,
    @Inject(&quot;CustomLogger&quot;) private readonly logger: ILogger,

    // Repositories
    private readonly organizationRepository: OrganizationRepository,
    private readonly organizationDomainsRepository: OrganizationDomainsRepository,
    private readonly createOrgAndOrgAdminSaga: CreateOrgAndOrgAdminSaga,
    private readonly usersService: UsersService,
    private readonly organizationInvitationsRepository: OrganizationInvitationsRepository,

    // SNS Publisher Queue
    @InjectQueue(QueueNames.ORGANIZATION_SNS_PUBLISHER)
    private readonly organizationSNSPublisherQueue: Queue,

    // SNS Events Factory
    private readonly organizationSNSEventsFactory: OrganizationSNSEventsFactory,
  ) {}

  /**
   * Publishes an account event to SNS.
   * @param event The event to publish.
   * @param eventData The data to publish.
   * @param user The user associated with the event.
   */
  async publishEventToSNSQueue&lt;Q, T extends SNSEvent&lt;Q&gt;&gt;(
    event: T[&quot;eventType&quot;],
    eventData: T,
    user: { uid: string; email: string; userType: UserType; orgUid: string },
  ) {
    try {
      await this.organizationSNSPublisherQueue.add(
        QueueNames.ORGANIZATION_SNS_PUBLISHER,
        {
          event,
          eventData,
          user,
        },
        {
          attempts: 3,
          backoff: {
            type: &quot;exponential&quot;,
            delay: 1000, // 1 second
          },
        },
      );
    } catch (error) {
      this.logger.error(
        &#x60;Error encountered while publishing organization event - ${event} for organization ${eventData.orgId} to SNS: ${error?.message}&#x60;,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: &quot;ORGANIZATION_EVENTS&quot;,
        fn: &quot;publishEventToSNSQueue&quot;,
        organizationId: eventData.orgId,
        user,
        event,
        eventData,
      });
    }
  }

  /**
   * Finds all organizations.
   * @returns All organizations.
   */
  findAll(user: CurrentUser): Promise&lt;Organization[]&gt; {
    return this.usersService.findAllOrgsForUser(user);
  }

  /**
   * Finds an organization by ID.
   * @param id The ID of the organization to find.
   * @returns The organization.
   */
  async findOne(id: string): Promise&lt;Organization&gt; {
    try {
      const orgByIdentifier &#x3D; await this.findOneByIdentifier(id);

      // If the organization was found by identifier, return it
      if (!_.isEmpty(orgByIdentifier)) {
        return orgByIdentifier;
      }

      const organization &#x3D; await this.organizationRepository.findOneById(id);
      if (!organization) {
        throw new NotFoundException(
          &quot;Organization with provided id was not found!&quot;,
        );
      }

      return organization;
    } catch (error) {
      if (error?.code &#x3D;&#x3D;&#x3D; POSTGRES_ERROR_CODES.INVALID_TEXT_REPRESENTATION) {
        throw new NotFoundException(
          &quot;Organization with provided id was not found!&quot;,
        );
      }

      throw error;
    }
  }

  /**
   * Finds an organization by identifier.
   * @param identifier The identifier of the organization to find.
   * @returns The organization.
   */
  async findOneByIdentifier(orgIdentifier: string): Promise&lt;Organization&gt; {
    const organization &#x3D; await this.organizationRepository.findByCondition({
      where: { uid: orgIdentifier },
    });

    return organization;
  }

  /**
   * Joins an organization.
   * @param joinOrgDto The DTO containing the organization ID and the user ID.
   * @returns The organization.
   */
  async joinOrganization(joinOrgDto: JoinOrganizationDto, authToken: string) {
    // Find and check if the organization exists
    const organization &#x3D; await this.findOne(joinOrgDto.orgId);

    // Get the token from the auth token
    const [_type, token] &#x3D; authToken?.split(&quot; &quot;) || [];
    if (!token) {
      throw new BadRequestException(&quot;Invalid auth token provided!&quot;);
    }

    // Get the current user
    const currentUser &#x3D; await this.usersService.getSupabaseUser(token);

    // Check if the user is already a member of the organization
    const isMember &#x3D; await this.usersService.isUserPartOfOrganization(
      { email: currentUser.email },
      organization.id,
      { exists: true },
    );

    // If the user is already a member, throw an error
    if (isMember) {
      throw new ConflictException(
        &quot;You are already a member of this organization.&quot;,
      );
    }

    // Check if the invite is a link invite or is a domain organization join
    const isLinkInvite &#x3D; joinOrgDto.inviteeEmail &amp;&amp; joinOrgDto.invitingUserId;

    // Check if the invite is valid
    if (isLinkInvite) {
      // Get the inviting user
      const invitingUser &#x3D; await this.usersService.getUserByUserId(
        joinOrgDto.invitingUserId,
        organization.id,
      );

      // If the inviting user is not found, throw an error
      if (!invitingUser || invitingUser.length &#x3D;&#x3D;&#x3D; 0) {
        throw new ForbiddenException(&quot;Invalid invite!&quot;);
      }

      // Check if the invitation exists
      const invitation &#x3D;
        await this.organizationInvitationsRepository.findByCondition({
          where: {
            organization: { id: organization.id },
            inviteeEmail: joinOrgDto.inviteeEmail,
            inviter: { id: invitingUser[0].id },
          },
        });

      // If the invitation is not found, throw an error
      if (!invitation) {
        throw new ForbiddenException(&quot;Invalid invite!&quot;);
      }
    } else {
      const { domain } &#x3D; tldts.parse(currentUser.email);

      // Get all organizations with matching domains
      const orgsWithMatchingDomains &#x3D;
        await this.organizationDomainsRepository.findAll({
          where: { domain },
          relations: { organization: true },
          select: { organization: { id: true, uid: true } },
        });

      // Get the organizations with matching domains
      const joinableOrganizations &#x3D; orgsWithMatchingDomains.map(
        (org) &#x3D;&gt; org.organization,
      );

      // If there are no joinable organizations, throw an error
      if (joinableOrganizations.length &#x3D;&#x3D;&#x3D; 0) {
        throw new ForbiddenException(&quot;Forbidden&quot;);
      }

      // Check if the organization is in the joinable organizations
      const foundOrganization &#x3D; joinableOrganizations.find(
        (org) &#x3D;&gt; org.uid &#x3D;&#x3D;&#x3D; organization.uid,
      );

      // If the organization is not in the joinable organizations, throw an error
      if (!foundOrganization) {
        throw new ForbiddenException(&quot;Forbidden&quot;);
      }
    }

    // At this point we know the user can join the above organization
    const userPersona &#x3D; await this.usersService.createUserPersona({
      email: currentUser.email,
      name: currentUser.email.split(&quot;@&quot;)?.[0] || currentUser.email,
      organizationId: organization.id,
      authId: currentUser.id,
      userType: UserType.USER,
    });

    // Publish the user joined organization event
    const userJoinedOrganizationEvent &#x3D;
      this.organizationSNSEventsFactory.createUserJoinedOrganizationSNSEvent(
        {
          uid: currentUser.id,
          email: currentUser.email,
          userType: UserType.USER,
          orgUid: organization.uid,
        },
        organization,
        userPersona,
      );

    await this.publishEventToSNSQueue(
      OrganizationEvents.MEMBER_JOINED,
      userJoinedOrganizationEvent,
      {
        uid: currentUser.id,
        email: currentUser.email,
        userType: UserType.USER,
        orgUid: organization.uid,
      },
    );

    // Return the user persona
    return { userPersona, organization };
  }

  /**
   * Creates an invitation for a user to join an organization.
   * @param inviteDto The DTO containing the email of the user to invite.
   * @param user The current user.
   * @returns The invitation.
   */
  async createInvite(inviteDto: InviteUserDto, user: CurrentUser) {
    const { email } &#x3D; inviteDto;

    // Check if the user is an organization admin
    if (user.userType !&#x3D;&#x3D; UserType.ORG_ADMIN) {
      throw new ForbiddenException(&quot;You are not authorized to invite users!&quot;);
    }

    const invite &#x3D; await this.organizationInvitationsRepository.save({
      inviteeEmail: email,
      inviter: { id: user.sub },
      organization: { id: user.orgId },
      // TODO: expiresAt: addDays(new Date(), 7),
    });

    return invite;
  }

  /**
   * Creates a new organization.
   * @param createOrganizationDto The organization data to create.
   * @returns The created organization.
   */
  async create(
    createOrganizationDto: CreateOrganizationDto,
    authToken?: string,
  ): Promise&lt;Organization&gt; {
    let { email } &#x3D; createOrganizationDto;
    const { password, name, logoUrl &#x3D; &quot;&quot; } &#x3D; createOrganizationDto;

    // Generate a unique identifier for the organization
    const identifier &#x3D; this.generateOrganizationIdentifier();

    // Tracks if we should skip user creation
    let skipUserCreation &#x3D; false;
    let authId &#x3D; &quot;&quot;;

    // If the email and password are not provided, the user
    // was already created, we&#x27;ll go with create org flow with this
    // user and treating this user as the org admin
    if (!email &amp;&amp; !password &amp;&amp; authToken) {
      // Get the token from the auth token
      const [_type, token] &#x3D; authToken.split(&quot; &quot;) || [];
      if (!token) {
        throw new BadRequestException(&quot;Invalid auth token provided!&quot;);
      }

      // Get the supabase user
      const supabaseUser &#x3D; await this.usersService.getSupabaseUser(token);

      // If the supabase user is not found, throw an error
      if (!supabaseUser) {
        throw new BadRequestException(
          &quot;User attached in the auth token is not found in the database!&quot;,
        );
      }

      // Set the email and password to the supabase user details
      email &#x3D; supabaseUser.email.toLowerCase();
      authId &#x3D; supabaseUser.id;
      skipUserCreation &#x3D; true;
    } else if (!email &amp;&amp; !password) {
      // If the email and password are not provided with no auth token
      throw new BadRequestException(
        &quot;You need to provide user details to create an organization.&quot;,
      );
    }

    // Validate the email domain
    const { domain } &#x3D; tldts.parse(email);
    if (!domain) {
      throw new BadRequestException(&quot;Invalid email domain&quot;);
    }

    // Get all organizations for the user, just their names
    const userMemberOfOrgs &#x3D; await this.usersService.findAllOrgsForUserEmail(
      email,
      { name: true },
    );

    // Check if the organization name is already taken
    const conflictWithJoinedOrg &#x3D; userMemberOfOrgs.find(
      (org) &#x3D;&gt; org.name.toLowerCase() &#x3D;&#x3D;&#x3D; name.toLowerCase(),
    );

    // If the organization name is already taken, throw an error
    if (conflictWithJoinedOrg) {
      throw new ConflictException(
        &quot;Organization with this name already exists!&quot;,
      );
    }

    // Get all organizations with matching domains
    const orgsWithMatchingDomains &#x3D;
      await this.organizationDomainsRepository.findAll({
        where: { domain },
        relations: { organization: true },
        select: { organization: { id: true, uid: true, name: true } },
      });

    // Get the organizations with matching domains
    const foundOrgs &#x3D; orgsWithMatchingDomains.map((org) &#x3D;&gt; org.organization);
    const matchedOrg &#x3D; foundOrgs.find(
      (org) &#x3D;&gt; org.name.toLowerCase() &#x3D;&#x3D;&#x3D; name.toLowerCase(),
    );

    // If the organization name is already taken, throw an error
    if (matchedOrg) {
      throw new ConflictException(
        &quot;Organization with this name already exists!&quot;,
      );
    }

    try {
      // Execute the saga
      const orgInput &#x3D; {
        identifier,
        email: email.toLowerCase(),
        password,
        logoUrl,
        authId,
        organizationName: name,
      };

      // Execute the saga
      const { organization } &#x3D; await this.createOrgAndOrgAdminSaga.execute(
        orgInput,
        { createUser: skipUserCreation },
      );

      // Return the organization
      return organization;
    } catch (error) {
      if (error?.code &#x3D;&#x3D;&#x3D; 6) {
        throw new ConflictException(error?.details);
      }

      throw error;
    }
  }

  /**
   * Updates an existing organization.
   * @param id The ID of the organization to update.
   * @param updateOrganizationDto The organization data to update.
   * @returns The updated organization.
   */
  async update(
    id: string,
    updateOrganizationDto: UpdateOrganizationDto,
    currentUser: CurrentUser,
  ): Promise&lt;Organization&gt; {
    try {
      // Check if the organization exists
      const organization &#x3D; await this.organizationRepository.findByCondition({
        where: { uid: id },
      });

      const previousOrganization &#x3D; _.cloneDeep(organization);

      // If the organization was not found, throw an error
      if (_.isEmpty(organization)) {
        throw new NotFoundException(
          &quot;Organization with provided id was not found!&quot;,
        );
      }

      // Update the organization name if provided
      if (updateOrganizationDto.name) {
        organization.name &#x3D; updateOrganizationDto.name;
      }

      // Update the organization logo URL if provided
      if (updateOrganizationDto.logoUrl) {
        organization.logoUrl &#x3D; updateOrganizationDto.logoUrl;
      }

      // Update the organization isActive status if provided
      if (updateOrganizationDto.isActive !&#x3D;&#x3D; undefined) {
        organization.isActive &#x3D; updateOrganizationDto.isActive;
      }

      // Publish the organization updated event
      const organizationUpdatedEvent &#x3D;
        this.organizationSNSEventsFactory.createOrganizationUpdatedSNSEvent(
          currentUser,
          previousOrganization,
          organization,
        );

      await this.publishEventToSNSQueue(
        OrganizationEvents.UPDATED,
        organizationUpdatedEvent,
        {
          uid: currentUser.uid,
          email: currentUser.email,
          userType: currentUser.userType,
          orgUid: organization.uid,
        },
      );

      return this.organizationRepository.save(organization);
    } catch (error) {
      if (error?.code &#x3D;&#x3D;&#x3D; POSTGRES_ERROR_CODES.INVALID_TEXT_REPRESENTATION) {
        throw new NotFoundException(
          &quot;Organization with provided id was not found!&quot;,
        );
      }

      throw error;
    }
  }

  /**
   * Removes an organization by ID.
   * @param id The ID of the organization to remove.
   */
  async remove(id: string, currentUser: CurrentUser): Promise&lt;void&gt; {
    try {
      const organization &#x3D; await this.organizationRepository.findByCondition({
        where: { uid: id },
      });

      // If the organization was not found, throw an error
      if (_.isEmpty(organization)) {
        throw new NotFoundException(
          &quot;Organization with provided id was not found!&quot;,
        );
      }

      // Publish the organization updated event
      const organizationDeletedEvent &#x3D;
        this.organizationSNSEventsFactory.createOrganizationDeletedSNSEvent(
          currentUser,
          organization,
        );

      await this.publishEventToSNSQueue(
        OrganizationEvents.DELETED,
        organizationDeletedEvent,
        {
          uid: currentUser.uid,
          email: currentUser.email,
          userType: currentUser.userType,
          orgUid: organization.uid,
        },
      );

      await this.organizationRepository.remove(organization);
    } catch (error) {
      if (error?.code &#x3D;&#x3D;&#x3D; POSTGRES_ERROR_CODES.INVALID_TEXT_REPRESENTATION) {
        throw new NotFoundException(
          &quot;Organization with provided id was not found!&quot;,
        );
      }

      throw error;
    }
  }

  /**
   * Generates an organization identifier.
   * @returns The generated organization identifier.
   */
  private generateOrganizationIdentifier(): string {
    const identifier &#x3D; IdGeneratorUtils.generate(&quot;E&quot;);
    return identifier;
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'OrganizationService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
