<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >CommentSnsConsumer</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/communications/processors/comment.sns-publish.processor.ts</code>
        </p>



            <p class="comment">
                <h3>Extends</h3>
            </p>
            <p class="comment">
                        <code>WorkerHost</code>
            </p>


            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#constructForEntityType" >constructForEntityType</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#constructSnsPayload" >constructSnsPayload</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#process" >process</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#publishToSNS" >publishToSNS</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(snsPublisherService: <a href="../s/SNSPublisherService.html" target="_self">SNSPublisherService</a>, logger: ILogger, configService: <a href="../injectables/ConfigService.html" target="_self">ConfigService</a>, commentRepository: CommentRepository)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="23" class="link-to-prism">src/communications/processors/comment.sns-publish.processor.ts:23</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>snsPublisherService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../miscellaneous/variables.html#SNSPublisherService" target="_self" >SNSPublisherService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>logger</td>
                                                  
                                                        <td>
                                                                    <code>ILogger</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>configService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/ConfigService.html" target="_self" >ConfigService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>commentRepository</td>
                                                  
                                                        <td>
                                                                    <code>CommentRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="constructForEntityType"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>constructForEntityType</b></span>
                        <a href="#constructForEntityType"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>constructForEntityType(data: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, entityType: CommentEntityTypes)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="144"
                                    class="link-to-prism">src/communications/processors/comment.sns-publish.processor.ts:144</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Construct the SNS payload for the given entity type</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>data</td>
                                            <td>
                                                            <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The data object</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>entityType</td>
                                            <td>
                                                        <code>CommentEntityTypes</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The entity type</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The SNS payload</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="constructSnsPayload"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>constructSnsPayload</b></span>
                        <a href="#constructSnsPayload"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>constructSnsPayload(comment: Comment, eventType: <a href="../undefineds/CommentOp.html" target="_self">CommentOp</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, entityType: CommentEntityTypes)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="178"
                                    class="link-to-prism">src/communications/processors/comment.sns-publish.processor.ts:178</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>comment</td>
                                            <td>
                                                        <code>Comment</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>eventType</td>
                                            <td>
                                                            <code><a href="../miscellaneous/enumerations.html#CommentOp" target="_self" >CommentOp</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>entityType</td>
                                            <td>
                                                        <code>CommentEntityTypes</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>DeepPartial&lt;SnsCommentCreatedPayload&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="process"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>process</b></span>
                        <a href="#process"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>process(job: Job<literal type>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="57"
                                    class="link-to-prism">src/communications/processors/comment.sns-publish.processor.ts:57</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Process the SNS message for the comment created event</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>job</td>
                                            <td>
                                                        <code>Job&lt;literal type&gt;</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The job object</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="publishToSNS"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>publishToSNS</b></span>
                        <a href="#publishToSNS"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>publishToSNS(snsPayload: <a href="../interfaces/SnsCommentCreatedPayload.html" target="_self">SnsCommentCreatedPayload</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="37"
                                    class="link-to-prism">src/communications/processors/comment.sns-publish.processor.ts:37</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>snsPayload</td>
                                            <td>
                                                            <code><a href="../interfaces/SnsCommentCreatedPayload.html" target="_self" >SnsCommentCreatedPayload</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Processor, WorkerHost } from &quot;@nestjs/bullmq&quot;;
import { Inject, Injectable } from &quot;@nestjs/common&quot;;
import { CUSTOM_LOGGER_TOKEN, ILogger } from &quot;@repo/nestjs-commons/logger&quot;;
import { ContextUserType, SNSPublisherService } from &quot;@repo/thena-eventbridge&quot;;
import {
  Comment,
  CommentEntityTypes,
  CommentRepository,
} from &quot;@repo/thena-platform-entities&quot;;
import { Job } from &quot;bullmq&quot;;
import { DeepPartial } from &quot;typeorm&quot;;
import { v4 as uuidv4 } from &quot;uuid&quot;;
import { CurrentUser } from &quot;../../common/decorators&quot;;
import { ConfigKeys, ConfigService } from &quot;../../config/config.service&quot;;
import { QueueNames } from &quot;../../constants/queue.constants&quot;;
import { COMMENT_SNS_PUBLISHER } from &quot;../constants/comments.constants&quot;;
import { CommentEvents } from &quot;../events/comment-events&quot;;
import { SnsCommentCreatedPayload } from &quot;../interfaces/sns-comment-created-payload.interface&quot;;
import { CommentOp, getCommentEventType } from &quot;../utils&quot;;

@Injectable()
@Processor(QueueNames.COMMENT_SNS_PUBLISHER)
export class CommentSnsConsumer extends WorkerHost {
  constructor(
    @Inject(COMMENT_SNS_PUBLISHER)
    private snsPublisherService: SNSPublisherService,

    @Inject(CUSTOM_LOGGER_TOKEN)
    private readonly logger: ILogger,
    private readonly configService: ConfigService,

    private readonly commentRepository: CommentRepository,
  ) {
    super();
  }

  private async publishToSNS(snsPayload: SnsCommentCreatedPayload) {
    await this.snsPublisherService.publishSNSMessage({
      subject: snsPayload.eventType,
      message: JSON.stringify(snsPayload),
      topicArn: this.configService.get(ConfigKeys.AWS_SNS_TICKET_TOPIC_ARN),
      messageAttributes: {
        event_name: snsPayload.eventType,
        event_id: snsPayload.eventId, // Unique identifier for the event
        event_timestamp: snsPayload.timestamp,
        context_user_id: snsPayload.actor.id,
        context_user_type: snsPayload.actor.type as ContextUserType,
        context_organization_id: snsPayload.orgId,
      },
    });
  }

  /**
   * Process the SNS message for the comment created event
   * @param job The job object
   */
  async process(
    job: Job&lt;{
      comment: string;
      user: CurrentUser;
      eventType: CommentOp;
      entityType: CommentEntityTypes;
      previousComment?: Comment;
    }&gt;,
  ) {
    try {
      this.logger.log(
        &#x60;Processing SNS message for job ${job.id}, event type: ${job.data.eventType}&#x60;,
      );

      const { comment, user, eventType, entityType, previousComment } &#x3D;
        job.data;

      // For delete events, use the previousComment data
      const commentData &#x3D;
        eventType &#x3D;&#x3D;&#x3D; CommentOp.DELETED
          ? previousComment
          : await this.constructForEntityType(
              { comment, user, eventType },
              entityType,
            );

      // If no comment data found for non-delete events, throw error
      if (!commentData &amp;&amp; eventType !&#x3D;&#x3D; CommentOp.DELETED) {
        const error &#x3D; new Error(&#x60;Comment not found with id ${comment}&#x60;);
        error.name &#x3D; &quot;CommentNotFoundError&quot;;
        throw error;
      }

      // Construct the SNS payload
      const snsPayload &#x3D; this.constructSnsPayload(
        commentData,
        eventType,
        user,
        entityType,
      );

      // Add timeout to SNS publish operation
      await Promise.race([
        this.publishToSNS(snsPayload as SnsCommentCreatedPayload),
        new Promise((_, reject) &#x3D;&gt;
          setTimeout(() &#x3D;&gt; {
            const error &#x3D; new Error(&quot;SNS publish timeout&quot;);
            error.name &#x3D; &quot;SNSTimeoutError&quot;;
            reject(error);
          }, 5000),
        ),
      ]);

      return {
        success: true,
        processedAt: new Date(),
        commentId: comment,
      };
    } catch (error) {
      this.logger.error(
        &#x60;Error processing SNS message for job ${job.id}: ${error.message}&#x60;,
        error?.stack,
      );

      // Determine if we should retry based on error type
      if (job.attemptsMade &lt; job.opts.attempts - 1) {
        const retryError &#x3D; new Error(
          &#x60;SNS publishing failed (attempt ${job.attemptsMade + 1}): ${
            error.message
          }&#x60;,
        );
        retryError.name &#x3D; &quot;RetryableError&quot;;
        throw retryError;
      }

      // On final attempt, mark as permanent failure
      throw error;
    }
  }

  /**
   * Construct the SNS payload for the given entity type
   * @template T The type of the data object
   * @param data The data object
   * @param entityType The entity type
   * @returns The SNS payload
   */
  private async constructForEntityType(
    data: any,
    entityType: CommentEntityTypes,
  ) {
    switch (entityType) {
      case CommentEntityTypes.TICKET: {
        const { comment, user, eventType } &#x3D; data;

        // Get the comment data
        const commentData &#x3D; await this.commentRepository.findByCondition({
          where: { uid: comment, organizationId: user.orgId },
          relations: {
            team: true,
            author: true,
            ticket: true,
            attachments: true,
          },
        });

        // If the comment is not found and the event type is not a delete event, throw an error
        if (!commentData &amp;&amp; eventType !&#x3D;&#x3D; CommentEvents.COMMENT_DELETED) {
          const error &#x3D; new Error(&#x60;Comment not found with id ${comment}&#x60;);
          error.name &#x3D; &quot;CommentNotFoundError&quot;;
          throw error;
        }

        return commentData;
      }

      default:
        throw new Error(&#x60;Unsupported entity type: ${entityType}&#x60;);
    }
  }

  private constructSnsPayload(
    comment: Comment,
    eventType: CommentOp,
    user: CurrentUser,
    entityType: CommentEntityTypes,
  ) {
    const payload: DeepPartial&lt;SnsCommentCreatedPayload&gt; &#x3D; {
      eventId: uuidv4(),
      eventType: getCommentEventType(entityType, eventType),
      timestamp: new Date().getTime().toString(),
      orgId: user.orgUid,
      teamId: comment.team.uid,
      actor: {
        id: user.uid,
        type: user.userType,
        email: user.email,
      },
      payload: {
        comment: {
          id: comment.uid,
          content: comment.content,
          contentMarkdown: comment.contentMarkdown,
          contentHtml: comment.contentHtml,
          teamId: comment.team.uid,
          commentType: comment.commentType,
          author: {
            id: comment.author.uid,
            name: comment.author.name,
            email: comment.author.email,
          },
          attachments: comment.attachments.map((attachment) &#x3D;&gt; ({
            id: attachment.uid,
            url: attachment.url,
            size: attachment.size,
            createdAt: attachment.createdAt,
            updatedAt: attachment.updatedAt,
            deletedAt: attachment.deletedAt,
          })),
          commentVisibility: comment.commentVisibility,
          createdAt: comment.createdAt,
          updatedAt: comment.updatedAt,
          deletedAt: comment.deletedAt,
        },
      },
    };

    // Add the entity type to the payload
    if (entityType &#x3D;&#x3D;&#x3D; CommentEntityTypes.TICKET) {
      payload.payload.ticket &#x3D; {
        id: comment.ticket.uid,
        title: comment.ticket.title,
      };
    }

    return payload;
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'CommentSnsConsumer.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
