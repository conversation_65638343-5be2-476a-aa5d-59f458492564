<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >RoundRobinStrategy</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/tickets/routing/providers/thena-request-router/agent-allocator/strategies/round-robin.strategy.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#assignUser" >assignUser</a>
                            </li>
                            <li>
                                <a href="#checkUserAvailability" >checkUserAvailability</a>
                            </li>
                            <li>
                                <a href="#checkUserCapacity" >checkUserCapacity</a>
                            </li>
                            <li>
                                <a href="#filterUsersByCapacity" >filterUsersByCapacity</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#getAndIncrementIndex" >getAndIncrementIndex</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getAvailableUsers" >getAvailableUsers</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(cacheProvider: RedisCacheProvider, teamService: <a href="../injectables/TeamsService.html" target="_self">TeamsService</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="18" class="link-to-prism">src/tickets/routing/providers/thena-request-router/agent-allocator/strategies/round-robin.strategy.ts:18</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>cacheProvider</td>
                                                  
                                                        <td>
                                                                    <code>RedisCacheProvider</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>teamService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/TeamsService.html" target="_self" >TeamsService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="assignUser"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>assignUser</b></span>
                        <a href="#assignUser"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>assignUser(team: Team, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="24"
                                    class="link-to-prism">src/tickets/routing/providers/thena-request-router/agent-allocator/strategies/round-robin.strategy.ts:24</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>team</td>
                                            <td>
                                                        <code>Team</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;User | null&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="checkUserAvailability"></a>
                    <span class="name">
                        <span ><b>checkUserAvailability</b></span>
                        <a href="#checkUserAvailability"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>checkUserAvailability(user: User)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="112"
                                    class="link-to-prism">src/tickets/routing/providers/thena-request-router/agent-allocator/strategies/round-robin.strategy.ts:112</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Checks if the team is available.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                        <code>User</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The user to check the availability of.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                        </div>
                            <div class="io-description">
                                <p>True if the team is available, false otherwise.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="checkUserCapacity"></a>
                    <span class="name">
                        <span ><b>checkUserCapacity</b></span>
                        <a href="#checkUserCapacity"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>checkUserCapacity(user: User, capacity: DeepPartial<TeamCapacity>, teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="166"
                                    class="link-to-prism">src/tickets/routing/providers/thena-request-router/agent-allocator/strategies/round-robin.strategy.ts:166</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Checks if the user has capacity.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                        <code>User</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>capacity</td>
                                            <td>
                                                        <code>DeepPartial&lt;TeamCapacity&gt;</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="filterUsersByCapacity"></a>
                    <span class="name">
                        <span ><b>filterUsersByCapacity</b></span>
                        <a href="#filterUsersByCapacity"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>filterUsersByCapacity(users: Array<User>, capacity: TeamCapacity | null, team: Team)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="184"
                                    class="link-to-prism">src/tickets/routing/providers/thena-request-router/agent-allocator/strategies/round-robin.strategy.ts:184</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Filters users by capacity.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>users</td>
                                            <td>
                                                        <code>Array&lt;User&gt;</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>capacity</td>
                                            <td>
                                                        <code>TeamCapacity | null</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>team</td>
                                            <td>
                                                        <code>Team</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Array&lt;User&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getAndIncrementIndex"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>getAndIncrementIndex</b></span>
                        <a href="#getAndIncrementIndex"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getAndIncrementIndex(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, maxIndex: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="44"
                                    class="link-to-prism">src/tickets/routing/providers/thena-request-router/agent-allocator/strategies/round-robin.strategy.ts:44</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Gets the next index for a team.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>maxIndex</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;number&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getAvailableUsers"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>getAvailableUsers</b></span>
                        <a href="#getAvailableUsers"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getAvailableUsers(team: Team, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="79"
                                    class="link-to-prism">src/tickets/routing/providers/thena-request-router/agent-allocator/strategies/round-robin.strategy.ts:79</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>team</td>
                                            <td>
                                                        <code>Team</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;User[]&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Injectable } from &quot;@nestjs/common&quot;;
import { RedisCacheProvider } from &quot;@repo/nestjs-commons/cache&quot;;
import {
  BusinessHoursConfig,
  BusinessSlot,
  DayConfig,
  Team,
  TeamCapacity,
  User,
} from &quot;@repo/thena-platform-entities&quot;;
import { DateTime, DateTimeOptions } from &quot;luxon&quot;;
import { DeepPartial } from &quot;typeorm&quot;;
import { CurrentUser } from &quot;../../../../../../common/decorators&quot;;
import { TeamsService } from &quot;../../../../../../teams/services/teams.service&quot;;
import { UserRoutingStrategy } from &quot;../../../../interfaces&quot;;

@Injectable()
export class RoundRobinStrategy implements UserRoutingStrategy {
  constructor(
    private readonly cacheProvider: RedisCacheProvider,
    private readonly teamService: TeamsService,
  ) {}

  async assignUser(team: Team, user: CurrentUser): Promise&lt;User | null&gt; {
    // Get the available users
    const availableUsers &#x3D; await this.getAvailableUsers(team, user);
    if (availableUsers.length &#x3D;&#x3D;&#x3D; 0) {
      return null;
    }

    // Get the last assigned user index
    const nextIndex &#x3D; await this.getAndIncrementIndex(
      team.id,
      availableUsers.length,
    );

    // Return the next user
    return availableUsers[nextIndex];
  }

  /**
   * Gets the next index for a team.
   */
  private async getAndIncrementIndex(
    teamId: string,
    maxIndex: number,
  ): Promise&lt;number&gt; {
    // Get the key for the last assigned index
    const key &#x3D; &#x60;team:${teamId}:last_assigned_index&#x60;;

    // Lua script to get and increment the index, this is done so we can do it atomically
    // note the reason for NOT using &#x60;redis.incr&#x60; is that it doesn&#x27;t support the modulo operation
    // which makes this operation non-atomic and prone to race conditions
    const script &#x3D; &#x60;
      -- Get the last assigned index for this team
      local index &#x3D; redis.call(&#x27;GET&#x27;, KEYS[1]) or -1

      -- Increment the index and wrap it around if it exceeds the max index
      index &#x3D; (tonumber(index) + 1) % tonumber(ARGV[1])

      -- Save the new index
      redis.call(&#x27;SET&#x27;, KEYS[1], index)

      -- Return the new index
      return index
    &#x60;;

    // Get the next index
    const result &#x3D; await this.cacheProvider.eval&lt;number&gt;(
      script,
      1,
      key,
      maxIndex,
    );

    return result;
  }

  async getAvailableUsers(team: Team, user: CurrentUser): Promise&lt;User[]&gt; {
    // Get the team users
    const teamUsers &#x3D; await this.teamService.findActiveTeamMembers(
      team.uid,
      user,
    );

    // Generate the user maps from team members
    const users &#x3D; teamUsers.map((member) &#x3D;&gt; member.user);

    // Filter the users using their business hours
    const availableUsers &#x3D; users.filter((user) &#x3D;&gt;
      this.checkUserAvailability(user),
    );

    // Fetch team capacity configuration
    const teamCapacity &#x3D; await this.teamService.getTeamCapacity(team.uid, user);

    // Filter users by capacity
    const usersWithCapacity &#x3D; this.filterUsersByCapacity(
      availableUsers,
      teamCapacity,
      team,
    );

    return usersWithCapacity;
  }

  /**
   * Checks if the team is available.
   * @param user The user to check the availability of.
   * @returns True if the team is available, false otherwise.
   */
  checkUserAvailability(user: User): boolean {
    const { businessHoursConfig } &#x3D; user;
    if (!businessHoursConfig) return true;

    // Check if the business hours config is active
    const teamTimezone &#x3D; user.timezone || &quot;UTC&quot;;
    const now &#x3D; DateTime.now().setZone(teamTimezone);

    // Get current day of the week in lowercase
    const currentDay &#x3D; now.weekdayLong.toLowerCase();

    // Get business hours for the current day
    const businessHours &#x3D; (
      businessHoursConfig[currentDay as keyof BusinessHoursConfig] as
        | DayConfig
        | undefined
    ).slots as BusinessSlot[] | undefined;

    // If no business hours are set for the current day, the team is unavailable
    if (!businessHours || businessHours.length &#x3D;&#x3D;&#x3D; 0) return false;

    // Current time in HH:mm format
    const currentTime &#x3D; now.toFormat(&quot;HH:mm&quot;);

    // Check if the current time is within any of the business hours slots
    return businessHours.some((slot) &#x3D;&gt; {
      // Convert slot times to DateTime objects in team&#x27;s timezone for proper comparison
      const zone: DateTimeOptions &#x3D; { zone: teamTimezone };
      const slotStart &#x3D; DateTime.fromFormat(slot.start, &quot;HH:mm&quot;, zone);
      const slotEnd &#x3D; DateTime.fromFormat(slot.end, &quot;HH:mm&quot;, zone);

      // Handle slots that span midnight
      if (slotEnd &lt; slotStart) {
        // If current time is before midnight, compare with start time
        if (currentTime &gt;&#x3D; slot.start) {
          return true;
        }

        // If current time is after midnight, compare with end time
        if (currentTime &lt;&#x3D; slot.end) {
          return true;
        }

        return false;
      }

      // Normal slot comparison
      return currentTime &gt;&#x3D; slot.start &amp;&amp; currentTime &lt;&#x3D; slot.end;
    });
  }

  /**
   * Checks if the user has capacity.
   */
  checkUserCapacity(
    user: User,
    capacity: DeepPartial&lt;TeamCapacity&gt;,
    teamId: string,
  ) {
    const userCurrentTickets &#x3D; user?.metadata?.workload?.[teamId]?.count ?? 0;
    const maxTicketsPerUser &#x3D; capacity.maxTicketsPerUser;

    // If the max tickets per user is not set, the user has capacity
    if (maxTicketsPerUser &#x3D;&#x3D; null) return true;

    // Check if the user has capacity
    return userCurrentTickets &lt; capacity.maxTicketsPerUser;
  }

  /**
   * Filters users by capacity.
   */
  filterUsersByCapacity(
    users: Array&lt;User&gt;,
    capacity: TeamCapacity | null,
    team: Team,
  ): Array&lt;User&gt; {
    // Filter users by capacity
    const usersWithCapacity &#x3D; users.filter((user) &#x3D;&gt; {
      // Get the user&#x27;s personal capacity
      const usersPersonalCapacity &#x3D; user.userTeamCapacity?.[0] || {};

      // Merge the team capacity with the user&#x27;s personal capacity
      const finalCapacity: DeepPartial&lt;TeamCapacity&gt; &#x3D; {
        ...(capacity ?? {}),
        ...usersPersonalCapacity,
      };

      // Check if the user has capacity
      return this.checkUserCapacity(user, finalCapacity, team.uid);
    });

    // Return the users with capacity
    return usersWithCapacity;
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'RoundRobinStrategy.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
