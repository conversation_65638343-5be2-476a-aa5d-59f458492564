<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >AccountsListeners</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/accounts/listeners/accounts.listeners.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Readonly</span>
                                <a href="#logSpanId" >logSpanId</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#handleAccountAttributeValueDeletedEvent" >handleAccountAttributeValueDeletedEvent</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#handleLinkContactsToAccountByEmailDomainEvent" >handleLinkContactsToAccountByEmailDomainEvent</a>
                            </li>
                            <li>
                                    <span class="modifier"></span>
                                    <span class="modifier">Async</span>
                                <a href="#handleOrganizationCreatedEvent" >handleOrganizationCreatedEvent</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(logger: ILogger, sentryService: SentryService, accountsService: <a href="../injectables/AccountsService.html" target="_self">AccountsService</a>, accountCommonService: <a href="../injectables/AccountCommonService.html" target="_self">AccountCommonService</a>, accountAttributeValueActionService: <a href="../injectables/AccountAttributeValueActionService.html" target="_self">AccountAttributeValueActionService</a>, accountRelationshipActionService: <a href="../injectables/AccountRelationshipActionService.html" target="_self">AccountRelationshipActionService</a>, customerContactActionService: <a href="../injectables/CustomerContactActionService.html" target="_self">CustomerContactActionService</a>, accountActivityActionService: <a href="../injectables/AccountActivityActionService.html" target="_self">AccountActivityActionService</a>, accountNoteActionService: <a href="../injectables/AccountNoteActionService.html" target="_self">AccountNoteActionService</a>, accountTaskActionService: <a href="../injectables/AccountTaskActionService.html" target="_self">AccountTaskActionService</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="32" class="link-to-prism">src/accounts/listeners/accounts.listeners.ts:32</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>logger</td>
                                                  
                                                        <td>
                                                                    <code>ILogger</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>sentryService</td>
                                                  
                                                        <td>
                                                                    <code>SentryService</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>accountsService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/AccountsService.html" target="_self" >AccountsService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>accountCommonService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/AccountCommonService.html" target="_self" >AccountCommonService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>accountAttributeValueActionService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/AccountAttributeValueActionService.html" target="_self" >AccountAttributeValueActionService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>accountRelationshipActionService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/AccountRelationshipActionService.html" target="_self" >AccountRelationshipActionService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>customerContactActionService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/CustomerContactActionService.html" target="_self" >CustomerContactActionService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>accountActivityActionService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/AccountActivityActionService.html" target="_self" >AccountActivityActionService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>accountNoteActionService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/AccountNoteActionService.html" target="_self" >AccountNoteActionService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>accountTaskActionService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/AccountTaskActionService.html" target="_self" >AccountTaskActionService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="handleAccountAttributeValueDeletedEvent"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>handleAccountAttributeValueDeletedEvent</b></span>
                        <a href="#handleAccountAttributeValueDeletedEvent"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>handleAccountAttributeValueDeletedEvent(event: <a href="../classes/AccountAttributeValueDeletedEvent.html" target="_self">AccountAttributeValueDeletedEvent</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@OnEvent(EmittableAccountEvents.ACCOUNT_ATTRIBUTE_VALUE_DELETED)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="49"
                                    class="link-to-prism">src/accounts/listeners/accounts.listeners.ts:49</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>event</td>
                                            <td>
                                                            <code><a href="../classes/AccountAttributeValueDeletedEvent.html" target="_self" >AccountAttributeValueDeletedEvent</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="handleLinkContactsToAccountByEmailDomainEvent"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>handleLinkContactsToAccountByEmailDomainEvent</b></span>
                        <a href="#handleLinkContactsToAccountByEmailDomainEvent"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>handleLinkContactsToAccountByEmailDomainEvent(event: <a href="../classes/LinkContactsToAccountByEmailDomain.html" target="_self">LinkContactsToAccountByEmailDomain</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@OnEvent(EmittableAccountEvents.LINK_CONTACTS_TO_ACCOUNT_BY_EMAIL_DOMAIN)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="215"
                                    class="link-to-prism">src/accounts/listeners/accounts.listeners.ts:215</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>event</td>
                                            <td>
                                                            <code><a href="../classes/LinkContactsToAccountByEmailDomain.html" target="_self" >LinkContactsToAccountByEmailDomain</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="handleOrganizationCreatedEvent"></a>
                    <span class="name">
                            <span class="modifier"></span>
                            <span class="modifier">Async</span>
                        <span ><b>handleOrganizationCreatedEvent</b></span>
                        <a href="#handleOrganizationCreatedEvent"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>handleOrganizationCreatedEvent(event: <a href="../classes/OrganizationCreatedEvent.html" target="_self">OrganizationCreatedEvent</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@OnEvent(EmittableOrganizationEvents.ORGANIZATION_CREATED)<br /></code>
                </td>
            </tr>

                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="137"
                                    class="link-to-prism">src/accounts/listeners/accounts.listeners.ts:137</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>event</td>
                                            <td>
                                                            <code><a href="../classes/OrganizationCreatedEvent.html" target="_self" >OrganizationCreatedEvent</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>
            <section data-compodoc="block-properties">
    
    <h3 id="inputs">
        Properties
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="logSpanId"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Readonly</span>
                        <span ><b>logSpanId</b></span>
                        <a href="#logSpanId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&quot;[AccountsListeners]&quot;</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="32" class="link-to-prism">src/accounts/listeners/accounts.listeners.ts:32</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Inject, Injectable } from &quot;@nestjs/common&quot;;
import { OnEvent } from &quot;@nestjs/event-emitter&quot;;
import { SentryService } from &quot;@repo/nestjs-commons/filters/sentry-alerts.filter&quot;;
import { ILogger } from &quot;@repo/nestjs-commons/logger&quot;;
import {
  AccountAttributeType,
  DEFAULT_ACCOUNT_ATTRIBUTE_VALUE_SEEDS,
  DEFAULT_ACCOUNT_RELATIONSHIP_TYPES,
  UserType,
} from &quot;@repo/thena-platform-entities&quot;;
import { CurrentUser } from &quot;../../common/decorators/user.decorator&quot;;
import {
  EmittableOrganizationEvents,
  OrganizationCreatedEvent,
} from &quot;../../organization/events/organization.events&quot;;
import {
  AccountAttributeValueDeletedEvent,
  EmittableAccountEvents,
  LinkContactsToAccountByEmailDomain,
} from &quot;../events/accounts.events&quot;;
import { AccountActivityActionService } from &quot;../services/account-activity.action.service&quot;;
import { AccountAttributeValueActionService } from &quot;../services/account-attribute-value.action.service&quot;;
import { AccountCommonService } from &quot;../services/account-commons.service&quot;;
import { AccountNoteActionService } from &quot;../services/account-note.action.service&quot;;
import { AccountRelationshipActionService } from &quot;../services/account-relationship.action.service&quot;;
import { AccountTaskActionService } from &quot;../services/account-task.action.service&quot;;
import { AccountsService } from &quot;../services/accounts.service&quot;;
import { CustomerContactActionService } from &quot;../services/customer-contact.action.service&quot;;

@Injectable()
export class AccountsListeners {
  private readonly logSpanId &#x3D; &quot;[AccountsListeners]&quot;;

  constructor(
    @Inject(&quot;CustomLogger&quot;) private readonly logger: ILogger,
    @Inject(&quot;Sentry&quot;) private readonly sentryService: SentryService,

    private readonly accountsService: AccountsService,
    private readonly accountCommonService: AccountCommonService,
    private readonly accountAttributeValueActionService: AccountAttributeValueActionService,
    private readonly accountRelationshipActionService: AccountRelationshipActionService,
    private readonly customerContactActionService: CustomerContactActionService,
    private readonly accountActivityActionService: AccountActivityActionService,
    private readonly accountNoteActionService: AccountNoteActionService,
    private readonly accountTaskActionService: AccountTaskActionService,
  ) {}

  @OnEvent(EmittableAccountEvents.ACCOUNT_ATTRIBUTE_VALUE_DELETED)
  async handleAccountAttributeValueDeletedEvent(
    event: AccountAttributeValueDeletedEvent,
  ) {
    const { organizationId, attribute, attributeValueId } &#x3D; event;

    this.logger.log(
      &#x60;${this.logSpanId} Account attribute value deleted event received. OrgId: ${organizationId}, Attribute: ${attribute}, AttributeValueId: ${attributeValueId}&#x60;,
    );

    const defaultAttributeValue &#x3D;
      await this.accountCommonService.findDefaultAttributeValue(
        organizationId,
        attribute,
      );

    try {
      switch (attribute) {
        case AccountAttributeType.ACCOUNT_CLASSIFICATION:
        case AccountAttributeType.ACCOUNT_STATUS:
        case AccountAttributeType.ACCOUNT_HEALTH:
        case AccountAttributeType.ACCOUNT_INDUSTRY: {
          await this.accountsService.updateAccountAttributeValue(
            attributeValueId,
            defaultAttributeValue.id,
            attribute,
          );
          break;
        }
        case AccountAttributeType.CONTACT_TYPE: {
          await this.customerContactActionService.updateContactTypeAttribute(
            attributeValueId,
            defaultAttributeValue.id,
          );
          break;
        }
        case AccountAttributeType.ACTIVITY_STATUS: {
          await this.accountActivityActionService.updateActivityStatusAttribute(
            attributeValueId,
            defaultAttributeValue.id,
          );
          break;
        }
        case AccountAttributeType.ACTIVITY_TYPE: {
          await this.accountActivityActionService.updateActivityTypeAttribute(
            attributeValueId,
            defaultAttributeValue.id,
          );
          break;
        }
        case AccountAttributeType.NOTE_TYPE: {
          await this.accountNoteActionService.updateNoteTypeAttribute(
            attributeValueId,
            defaultAttributeValue.id,
          );
          break;
        }
        case AccountAttributeType.TASK_TYPE:
        case AccountAttributeType.TASK_STATUS:
        case AccountAttributeType.TASK_PRIORITY: {
          await this.accountTaskActionService.updateTaskAttributeValue(
            attributeValueId,
            defaultAttributeValue.id,
            attribute,
          );
          break;
        }
        default: {
          this.logger.error(
            &#x60;${this.logSpanId} Unsupported attribute type: ${attribute}&#x60;,
          );
          break;
        }
      }
    } catch (error) {
      this.logger.error(
        &#x60;${this.logSpanId} Failed to update account attribute value for organization ${organizationId}&#x60;,
        error,
      );
      this.sentryService.captureException(error, {
        tag: &quot;ACCOUNTS_LISTENERS&quot;,
        fn: &quot;handleAccountAttributeValueDeletedEvent&quot;,
        organizationId,
        body: event,
      });
    }
  }

  @OnEvent(EmittableOrganizationEvents.ORGANIZATION_CREATED)
  async handleOrganizationCreatedEvent(event: OrganizationCreatedEvent) {
    const { organizationId } &#x3D; event;

    this.logger.log(
      &#x60;${this.logSpanId} Organization created event received. OrgId: ${organizationId}&#x60;,
    );

    // Create initial AccountAttributeValue seeds for each attribute type
    for (const [attribute, values] of Object.entries(
      DEFAULT_ACCOUNT_ATTRIBUTE_VALUE_SEEDS,
    )) {
      for (const [index, value] of values.entries()) {
        try {
          await this.accountAttributeValueActionService.createAccountAttributeValue(
            {
              orgId: organizationId,
              userType: UserType.BOT_USER,
            } as CurrentUser,
            {
              attribute: attribute as AccountAttributeType,
              value,
              isDefault: index &#x3D;&#x3D;&#x3D; 0, // First value in the array is default
            },
          );
        } catch (error) {
          this.logger.error(
            &#x60;${this.logSpanId} Failed to create account attribute value for organization ${organizationId}&#x60;,
            error,
          );
          this.sentryService.captureException(error, {
            tag: &quot;ACCOUNTS_LISTENERS&quot;,
            fn: &quot;handleOrganizationCreatedEvent&quot;,
            organizationId,
            body: event,
          });
        }
      }
    }

    // Create initial AccountRelationshipType seeds
    const createdRelationshipTypes &#x3D; new Map&lt;string, string&gt;(); // Map to store name -&gt; uid mapping

    for (const relationshipType of DEFAULT_ACCOUNT_RELATIONSHIP_TYPES) {
      try {
        const createdType &#x3D;
          await this.accountRelationshipActionService.createAccountRelationshipType(
            {
              orgId: organizationId,
              userType: UserType.BOT_USER,
            } as CurrentUser,
            {
              name: relationshipType.name,
              inverseRelationshipId: relationshipType.inverseRelationship
                ? createdRelationshipTypes.get(
                    relationshipType.inverseRelationship,
                  )
                : null,
            },
          );

        // Store the created type&#x27;s uid for potential inverse relationships
        createdRelationshipTypes.set(relationshipType.name, createdType.uid);
      } catch (error) {
        this.logger.error(
          &#x60;${this.logSpanId} Failed to create account relationship type for organization ${organizationId}&#x60;,
          error,
        );
        this.sentryService.captureException(error, {
          tag: &quot;ACCOUNTS_LISTENERS&quot;,
          fn: &quot;handleOrganizationCreatedEvent&quot;,
          organizationId,
          body: event,
        });
      }
    }
  }

  @OnEvent(EmittableAccountEvents.LINK_CONTACTS_TO_ACCOUNT_BY_EMAIL_DOMAIN)
  async handleLinkContactsToAccountByEmailDomainEvent(
    event: LinkContactsToAccountByEmailDomain,
  ) {
    const { organizationId, accountId, domain } &#x3D; event;

    this.logger.log(
      &#x60;${this.logSpanId} Link contacts to account by email domain event received. OrgId: ${organizationId}, AccountId: ${accountId}, Domain: ${domain}&#x60;,
    );

    try {
      await this.customerContactActionService.addContactsMatchingDomainToAccount(
        organizationId,
        domain,
        accountId,
      );
    } catch (error) {
      this.logger.error(
        &#x60;${this.logSpanId} Failed to add contacts matching domain to account for organization ${organizationId}&#x60;,
        error,
      );
      this.sentryService.captureException(error, {
        tag: &quot;ACCOUNTS_LISTENERS&quot;,
        fn: &quot;handleLinkContactsToAccountByEmailDomainEvent&quot;,
        organizationId,
        body: event,
      });
    }
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'AccountsListeners.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
