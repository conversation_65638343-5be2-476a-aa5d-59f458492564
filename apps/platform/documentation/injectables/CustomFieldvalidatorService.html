<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >CustomFieldvalidatorService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/custom-field/validators/custom-field.validator.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#singleValue" >singleValue</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#validateBatchDeletePayload" >validateBatchDeletePayload</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#validateBatchUpdatePayload" >validateBatchUpdatePayload</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#validateCreatePayload" >validateCreatePayload</a>
                            </li>
                            <li>
                                <a href="#validateFieldValue" >validateFieldValue</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(customFieldRepository: CustomFieldRepository, thenaRestrictedFieldRepository: ThenaRestrictedFieldRepository, sharedService: <a href="../injectables/SharedService.html" target="_self">SharedService</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="31" class="link-to-prism">src/custom-field/validators/custom-field.validator.ts:31</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>customFieldRepository</td>
                                                  
                                                        <td>
                                                                    <code>CustomFieldRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>thenaRestrictedFieldRepository</td>
                                                  
                                                        <td>
                                                                    <code>ThenaRestrictedFieldRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>sharedService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/SharedService.html" target="_self" >SharedService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="singleValue"></a>
                    <span class="name">
                        <span ><b>singleValue</b></span>
                        <a href="#singleValue"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>singleValue(field: CustomField | ThenaRestrictedField, values: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="705"
                                    class="link-to-prism">src/custom-field/validators/custom-field.validator.ts:705</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>field</td>
                                            <td>
                                                        <code>CustomField | ThenaRestrictedField</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>values</td>
                                            <td>
                                                            <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateBatchDeletePayload"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>validateBatchDeletePayload</b></span>
                        <a href="#validateBatchDeletePayload"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateBatchDeletePayload(orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, fields: <a href="../classes/DeleteCustomFieldDto.html" target="_self">DeleteCustomFieldDto</a>, teamIds: string[])</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="152"
                                    class="link-to-prism">src/custom-field/validators/custom-field.validator.ts:152</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Validate batch delete custom field payload</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>fields</td>
                                            <td>
                                                            <code><a href="../classes/DeleteCustomFieldDto.html" target="_self" >DeleteCustomFieldDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>teamIds</td>
                                            <td>
                                                        <code>string[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>validated object</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateBatchUpdatePayload"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>validateBatchUpdatePayload</b></span>
                        <a href="#validateBatchUpdatePayload"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateBatchUpdatePayload(orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, updates: <a href="../classes/UpdateCustomFieldDto.html" target="_self">UpdateCustomFieldDto</a>, teamIds: string[])</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="267"
                                    class="link-to-prism">src/custom-field/validators/custom-field.validator.ts:267</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>updates</td>
                                            <td>
                                                            <code><a href="../classes/UpdateCustomFieldDto.html" target="_self" >UpdateCustomFieldDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>teamIds</td>
                                            <td>
                                                        <code>string[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateCreatePayload"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>validateCreatePayload</b></span>
                        <a href="#validateCreatePayload"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateCreatePayload(orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, createCustomFieldDto: <a href="../classes/CreateCustomFieldDto.html" target="_self">CreateCustomFieldDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="38"
                                    class="link-to-prism">src/custom-field/validators/custom-field.validator.ts:38</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>createCustomFieldDto</td>
                                            <td>
                                                            <code><a href="../classes/CreateCustomFieldDto.html" target="_self" >CreateCustomFieldDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateFieldValue"></a>
                    <span class="name">
                        <span ><b>validateFieldValue</b></span>
                        <a href="#validateFieldValue"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>validateFieldValue(field: CustomField | ThenaRestrictedField, values: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, isTicketClosing?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank">boolean</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="474"
                                    class="link-to-prism">src/custom-field/validators/custom-field.validator.ts:474</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>field</td>
                                            <td>
                                                        <code>CustomField | ThenaRestrictedField</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>values</td>
                                            <td>
                                                            <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>isTicketClosing</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { BadRequestException, Injectable } from &quot;@nestjs/common&quot;;
import {
  CustomField,
  CustomFieldRepository,
  CustomFieldType,
  ThenaRestrictedField,
  ThenaRestrictedFieldRepository,
} from &quot;@repo/thena-platform-entities&quot;;
import { validateSync } from &quot;class-validator&quot;;
import { ILike, In } from &quot;typeorm&quot;;
import { IdGeneratorUtils } from &quot;../../common/utils/id-generator.utils&quot;;
import { SharedService } from &quot;../../shared/shared.service&quot;;
import {
  CreateCustomFieldDto,
  DeleteCustomFieldDto,
  UpdateCustomFieldDto,
} from &quot;../dto&quot;;
import {
  DateValidation,
  DecimalValidation,
  EmailValidation,
  IpAddressValidation,
  isValidPassword,
  isValidPhoneNumber,
  isValidRegex,
  isValidString,
  UrlValidation,
} from &quot;../utils/helper&quot;;

@Injectable()
export class CustomFieldvalidatorService {
  constructor(
    private customFieldRepository: CustomFieldRepository,
    private thenaRestrictedFieldRepository: ThenaRestrictedFieldRepository,
    private sharedService: SharedService,
  ) {}

  async validateCreatePayload(
    orgId: string,
    createCustomFieldDto: CreateCustomFieldDto,
  ) {
    const teamId &#x3D; createCustomFieldDto.teamId;

    if (
      createCustomFieldDto.mandatoryOnCreation &#x3D;&#x3D;&#x3D; true &amp;&amp;
      createCustomFieldDto.visibleToCustomer &#x3D;&#x3D;&#x3D; false
    ) {
      throw new BadRequestException(
        &quot;Mandatory on creation cannot be true when visible to customer is false&quot;,
      );
    }

    if (
      createCustomFieldDto.visibleToCustomer &#x3D;&#x3D;&#x3D; false &amp;&amp;
      createCustomFieldDto.editableByCustomer &#x3D;&#x3D;&#x3D; true
    ) {
      throw new BadRequestException(
        &quot;Editable by customer cannot be true when visible to customer is false&quot;,
      );
    }

    if (teamId) {
      const existingFieldsInTeam &#x3D; await this.customFieldRepository.findAll({
        where: {
          organizationId: orgId,
          name: createCustomFieldDto.name,
          isDeleted: false,
          teamId: teamId,
        },
      });

      if (existingFieldsInTeam?.length &gt; 0) {
        throw new BadRequestException(
          &#x60;Custom field with the name &quot;${createCustomFieldDto.name}&quot; already exists in the team.&#x60;,
        );
      }
    }

    const [existingCustomFieldResult, thenaRestrictedFieldResult] &#x3D;
      await Promise.allSettled([
        this.customFieldRepository.findByCondition({
          where: {
            organizationId: orgId,
            name: ILike(createCustomFieldDto.name.toLocaleLowerCase()),
            source: createCustomFieldDto.source,
          },
        }),
        this.thenaRestrictedFieldRepository.findByCondition({
          where: {
            name: ILike(createCustomFieldDto.name.toLocaleLowerCase()),
          },
        }),
      ]);

    // Handle potential failures in the database queries
    if (existingCustomFieldResult.status &#x3D;&#x3D;&#x3D; &quot;rejected&quot;) {
      throw new BadRequestException(&quot;Error checking existing custom field&quot;);
    }
    if (thenaRestrictedFieldResult.status &#x3D;&#x3D;&#x3D; &quot;rejected&quot;) {
      // console.log(thenaRestrictedFieldResult.reason)
      throw new BadRequestException(
        &quot;Error checking thena restricted field names&quot;,
      );
    }

    const existingCustomField &#x3D; existingCustomFieldResult.value;
    const thenaRestrictedField &#x3D; thenaRestrictedFieldResult.value;

    if (existingCustomField) {
      throw new BadRequestException(
        &#x60;Custom field with name &#x27;${createCustomFieldDto.name}&#x27; already exists in your organization&#x60;,
      );
    }

    if (thenaRestrictedField) {
      throw new BadRequestException(
        &#x60;${createCustomFieldDto.name} is thena restricted field name, cannot be used to create custom field&#x60;,
      );
    }

    if (createCustomFieldDto.options) {
      createCustomFieldDto.options.forEach((option) &#x3D;&gt; {
        option.id &#x3D; IdGeneratorUtils.generate(&quot;OP&quot;);
      });
    }

    // Validate default value if provided
    if (createCustomFieldDto.defaultValue) {
      const tempField &#x3D; {
        fieldType: createCustomFieldDto.fieldType,
        mandatoryOnCreation: createCustomFieldDto.mandatoryOnCreation,
        name: createCustomFieldDto.name,
        options: createCustomFieldDto.options,
      } as CustomField;

      if (createCustomFieldDto.options) {
        for (const option of createCustomFieldDto.options) {
          if (option.value &#x3D;&#x3D;&#x3D; createCustomFieldDto.defaultValue) {
            createCustomFieldDto.defaultValue &#x3D; option.id;
          }
        }
      }

      this.validateFieldValue(tempField, [createCustomFieldDto.defaultValue]);
    }
  }

  /**
   * Validate batch delete custom field payload
   * @returns validated object
   */
  async validateBatchDeletePayload(
    orgId: string,
    fields: DeleteCustomFieldDto,
    teamIds: string[],
  ) {
    const fieldIds &#x3D; fields.fields.map((field) &#x3D;&gt; field.fieldId);
    const fieldIdsSet &#x3D; new Set(fieldIds);
    if (fieldIdsSet.size !&#x3D;&#x3D; fieldIds.length) {
      throw new BadRequestException(
        &#x60;Duplicate field IDs are not allowed. Please provide unique field IDs.&#x60;,
      );
    }

    const promises &#x3D; [];
    promises.push(
      this.customFieldRepository.findAll({
        where: {
          organizationId: orgId,
          uid: In(fieldIds),
          isDeleted: false,
        },
      }),
    );

    promises.push(
      this.thenaRestrictedFieldRepository.findAll({
        where: {
          uid: In(fieldIds),
          isDeleted: false,
        },
      }),
    );

    const response &#x3D; await Promise.all(promises);

    const existingFields &#x3D; response[0];
    const thenaRestrictedFields &#x3D; response[1];

    if (thenaRestrictedFields.length &gt; 0) {
      const restrictedFieldIds &#x3D; thenaRestrictedFields.map(
        (field) &#x3D;&gt; field.uid,
      );
      throw new BadRequestException(
        &#x60;Cannot delete thena restricted fields: ${restrictedFieldIds.join(
          &quot;, &quot;,
        )}&#x60;,
      );
    }

    if (existingFields.length !&#x3D;&#x3D; fieldIds.length) {
      const foundFieldIds &#x3D; new Set(existingFields.map((f) &#x3D;&gt; f.uid));
      const missingFields &#x3D; fieldIds.filter((id) &#x3D;&gt; !foundFieldIds.has(id));
      throw new BadRequestException(
        &#x60;Fields not found: ${missingFields.join(&quot;, &quot;)}&#x60;,
      );
    }

    for (const field of existingFields) {
      if (field.teamId &amp;&amp; !teamIds.includes(field.teamId)) {
        throw new BadRequestException(
          &#x60;You are trying to delete fields of a team that you are not a part of, fieldId: ${field.uid}&#x60;,
        );
      }
    }

    const modifieldFields &#x3D; [];
    const autoAddFields &#x3D; [];
    for (const field of fields.fields) {
      const existingField &#x3D; existingFields.find(
        (ef) &#x3D;&gt; ef.uid &#x3D;&#x3D;&#x3D; field.fieldId,
      );

      if (existingField.version !&#x3D;&#x3D; field.version) {
        modifieldFields.push(field.fieldId);
      }

      if (existingField.autoAddToAllForms) {
        autoAddFields.push(field.fieldId);
      }
    }

    if (modifieldFields.length &gt; 0) {
      throw new BadRequestException(
        &#x60;Some fields have been modified, Please try updating the latest version. Modified fields: ${modifieldFields.join(
          &quot;, &quot;,
        )}&#x60;,
      );
    }

    if (autoAddFields.length &gt; 0) {
      throw new BadRequestException(
        &#x60;Fields with IDs: ${autoAddFields.join(
          &quot;, &quot;,
        )} cannot be deleted as they are configured to be automatically added to all forms.&#x60;,
      );
    }

    const forms &#x3D; await this.sharedService.findFormsByFieldIds(
      fields.fields.map((field) &#x3D;&gt; field.fieldId),
    );
    if (forms.length) {
      throw new BadRequestException(
        &#x60;Cannot delete field ${fields.fields
          .map((field) &#x3D;&gt; field.fieldId)
          .join(&quot;, &quot;)} as it is used in the following forms: ${forms
          .map((form) &#x3D;&gt; form.name)
          .join(&quot;, &quot;)}&#x60;,
      );
    }

    return {
      ...fields,
    };
  }

  async validateBatchUpdatePayload(
    orgId: string,
    updates: UpdateCustomFieldDto,
    teamIds: string[],
  ) {
    if (!updates?.fields || updates.fields.length &#x3D;&#x3D;&#x3D; 0) {
      throw new BadRequestException(&quot;No fields to update&quot;);
    }

    const fieldIds &#x3D; updates.fields.map((field) &#x3D;&gt; field.fieldId);
    const promises &#x3D; [];
    promises.push(
      this.customFieldRepository.findAll({
        where: {
          organizationId: orgId,
          uid: In(fieldIds),
          isDeleted: false,
        },
      }),
    );

    promises.push(
      this.thenaRestrictedFieldRepository.findAll({
        where: {
          uid: In(fieldIds),
          isDeleted: false,
        },
      }),
    );

    // TODO: fetch all the forms in which current fieldIds are present

    const response &#x3D; await Promise.all(promises);

    const existingFields &#x3D; response[0];
    const thenaRestrictedFields &#x3D; response[1];

    if (thenaRestrictedFields.length &gt; 0) {
      const restrictedFieldIds &#x3D; thenaRestrictedFields.map(
        (field) &#x3D;&gt; field.uid,
      );
      throw new BadRequestException(
        &#x60;Cannot update thena restricted fields: ${restrictedFieldIds.join(
          &quot;, &quot;,
        )}&#x60;,
      );
    }

    if (existingFields.length !&#x3D;&#x3D; fieldIds.length) {
      const foundFieldIds &#x3D; new Set(existingFields.map((f) &#x3D;&gt; f.uid));
      const missingFields &#x3D; fieldIds.filter((id) &#x3D;&gt; !foundFieldIds.has(id));
      throw new BadRequestException(
        &#x60;Fields not found: ${missingFields.join(&quot;, &quot;)}&#x60;,
      );
    }

    for (const field of existingFields) {
      if (field.teamId &amp;&amp; !teamIds.includes(field.teamId)) {
        throw new BadRequestException(
          &#x60;You are trying to update fields of a team that you are not a part of, fieldId: ${field.uid}&#x60;,
        );
      }
    }

    const modifieldFields &#x3D; [];
    const autoAddFields &#x3D; [];
    for (const field of updates.fields) {
      const existingField &#x3D; existingFields.find(
        (ef) &#x3D;&gt; ef.uid &#x3D;&#x3D;&#x3D; field.fieldId,
      );

      if (existingField.version !&#x3D;&#x3D; field.version) {
        modifieldFields.push(field.fieldId);
      }

      if (existingField.autoAddToAllForms) {
        autoAddFields.push(field.fieldId);
      }

      if (field.updates.options) {
        const optionValueToIdMap &#x3D; existingField.options.reduce(
          (acc, option) &#x3D;&gt; {
            acc[option.value] &#x3D; option.id;
            return acc;
          },
          {},
        );

        for (const option of field.updates.options) {
          if (optionValueToIdMap[option.value]) {
            option.id &#x3D; optionValueToIdMap[option.value];
          } else if (!option.id) {
            option.id &#x3D; IdGeneratorUtils.generate(&quot;OP&quot;);
          }
        }
      }

      if (field.updates.defaultValue) {
        const tempField &#x3D; {
          fieldType: existingField.fieldType,
          mandatoryOnCreation: field.updates.mandatoryOnCreation,
          name: field.updates.name,
          uid: field.fieldId,
          options: existingField.options,
        } as CustomField;

        if (existingField.options) {
          for (const option of existingField.options) {
            if (option.value &#x3D;&#x3D;&#x3D; field.updates.defaultValue) {
              field.updates.defaultValue &#x3D; option.id;
            }
          }
        }

        this.validateFieldValue(tempField, [field.updates.defaultValue]);
      }
    }

    if (modifieldFields.length &gt; 0) {
      throw new BadRequestException(
        &#x60;Some fields have been modified, Please try updating the latest version. Modified fields: ${modifieldFields.join(
          &quot;, &quot;,
        )}&#x60;,
      );
    }

    const fieldsWithNameChanges &#x3D; updates.fields.filter(
      (field) &#x3D;&gt; field.updates.name,
    );

    if (fieldsWithNameChanges.length &gt; 0) {
      const [existingFieldsWithSameName, thenaRestrictedFieldsWithSameName] &#x3D;
        await Promise.all([
          this.customFieldRepository.findAll({
            where: {
              organizationId: orgId,
              name: ILike(
                In(fieldsWithNameChanges.map((field) &#x3D;&gt; field.updates.name)),
              ),
            },
          }),
          this.thenaRestrictedFieldRepository.findAll({
            where: {
              name: ILike(
                In(fieldsWithNameChanges.map((field) &#x3D;&gt; field.updates.name)),
              ),
            },
          }),
        ]);

      if (existingFieldsWithSameName.length &gt; 0) {
        const existingFieldIds &#x3D; existingFieldsWithSameName.map(
          (field) &#x3D;&gt; field.uid,
        );
        throw new BadRequestException(
          &#x60;Fields with IDs: ${existingFieldIds.join(
            &quot;, &quot;,
          )} already exist with the same name. Please choose a different name.&#x60;,
        );
      }

      if (thenaRestrictedFieldsWithSameName.length &gt; 0) {
        const restrictedFieldIds &#x3D; thenaRestrictedFieldsWithSameName.map(
          (field) &#x3D;&gt; field.uid,
        );
        throw new BadRequestException(
          &#x60;Field name is reserved for thena restricted fields: ${restrictedFieldIds.join(
            &quot;, &quot;,
          )}&#x60;,
        );
      }

      const names &#x3D; new Set(
        fieldsWithNameChanges.map((field) &#x3D;&gt; field.updates.name.toLowerCase()),
      );
      if (names.size !&#x3D;&#x3D; fieldsWithNameChanges.length) {
        throw new BadRequestException(
          &#x60;Duplicate field names are not allowed. Please choose a unique name for each field.&#x60;,
        );
      }
    }

    for (const field of updates.fields) {
      if (
        field.updates.mandatoryOnCreation &#x3D;&#x3D;&#x3D; true &amp;&amp;
        field.updates.visibleToCustomer &#x3D;&#x3D;&#x3D; false
      ) {
        throw new BadRequestException(
          &#x60;Field ${field.fieldId}: If mandatory on creation is true then visible to customer cannot be false.&#x60;,
        );
      }

      if (
        field.updates.visibleToCustomer &#x3D;&#x3D;&#x3D; false &amp;&amp;
        field.updates.editableByCustomer &#x3D;&#x3D;&#x3D; true
      ) {
        throw new BadRequestException(
          &#x60;Field ${field.fieldId}: If visible to customer is false then editable by customer cannot be true.&#x60;,
        );
      }
    }

    return {
      ...updates,
    };
  }

  validateFieldValue(
    field: CustomField | ThenaRestrictedField,
    values: any,
    isTicketClosing?: boolean,
  ) {
    if (
      isTicketClosing &amp;&amp;
      field.mandatoryOnClose &#x3D;&#x3D;&#x3D; true &amp;&amp;
      (values?.[0] &#x3D;&#x3D;&#x3D; undefined || values?.[0] &#x3D;&#x3D;&#x3D; null)
    ) {
      throw new BadRequestException(
        &#x60;Field ${field.name || field.uid} cannot be empty&#x60;,
      );
    }

    if (
      !isTicketClosing &amp;&amp;
      field.mandatoryOnCreation &#x3D;&#x3D;&#x3D; true &amp;&amp;
      (values?.[0] &#x3D;&#x3D;&#x3D; undefined || values?.[0] &#x3D;&#x3D;&#x3D; null)
    ) {
      throw new BadRequestException(
        &#x60;Field ${field.name || field.uid} cannot be empty&#x60;,
      );
    }
    if (values &#x3D;&#x3D;&#x3D; undefined) return;
    if (!Array.isArray(values)) {
      throw new BadRequestException(&quot;Unexpected value format&quot;);
    }

    switch (field.fieldType) {
      case CustomFieldType.SINGLE_LINE:
        for (const value of values || []) {
          if (!isValidString(value)) {
            throw new BadRequestException(
              &#x60;Field ${field.name || field.uid} contains invalid string&#x60;,
            );
          }
        }
        break;
      case CustomFieldType.RICH_TEXT:
      case CustomFieldType.MULTI_LINE:
        this.singleValue(field, values);
        for (const value of values || []) {
          if (!isValidString(value)) {
            throw new BadRequestException(
              &#x60;Field ${field.name || field.uid} contains invalid string&#x60;,
            );
          }
        }
        break;
      case CustomFieldType.EMAIL: {
        this.singleValue(field, values);
        for (const value of values || []) {
          const emailValidation &#x3D; new EmailValidation();
          emailValidation.email &#x3D; value;
          const errors &#x3D; validateSync(emailValidation);
          if (errors.length &gt; 0) {
            throw new BadRequestException(
              &#x60;Field ${field.name || field.uid} contains invalid email&#x60;,
            );
          }
        }
        break;
      }
      case CustomFieldType.SINGLE_CHOICE:
      case CustomFieldType.RADIO_BUTTON:
        this.singleValue(field, values);
        for (const value of values || []) {
          if (!field.options.some((option) &#x3D;&gt; option.id &#x3D;&#x3D;&#x3D; value)) {
            throw new BadRequestException(
              &#x60;Field ${
                field.name || field.uid
              } expects a valid option value, valid values: ${field.options
                .map((option) &#x3D;&gt; option.value)
                .join(&quot;, &quot;)}&#x60;,
            );
          }
        }
        break;
      case CustomFieldType.MULTI_CHOICE:
      case CustomFieldType.CHECKBOX:
        if (!Array.isArray(values)) {
          throw new BadRequestException(
            &#x60;Field ${field.name || field.uid} expects an array of values&#x60;,
          );
        }
        if (values.length !&#x3D;&#x3D; new Set(values).size) {
          throw new BadRequestException(
            &#x60;Field ${field.name || field.uid} contains duplicate values&#x60;,
          );
        }
        for (const option of values) {
          if (!field.options.some((o) &#x3D;&gt; o.id &#x3D;&#x3D;&#x3D; option)) {
            throw new BadRequestException(
              &#x60;Field ${
                field.name || field.uid
              } expects a valid option value, valid values: ${field.options
                .map((option) &#x3D;&gt; option.value)
                .join(&quot;, &quot;)}&#x60;,
            );
          }
        }
        break;
      case CustomFieldType.URL: {
        this.singleValue(field, values);
        for (const value of values || []) {
          const urlValidation &#x3D; new UrlValidation();
          urlValidation.url &#x3D; value;
          const errors &#x3D; validateSync(urlValidation);
          if (errors.length &gt; 0) {
            throw new BadRequestException(
              &#x60;Field ${field.name || field.uid} contains invalid URL&#x60;,
            );
          }
        }
        break;
      }
      case CustomFieldType.INTEGER: {
        this.singleValue(field, values);
        for (const value of values || []) {
          const parsedInt &#x3D; parseInt(value, 10);
          if (Number.isNaN(parsedInt) || parsedInt.toString() !&#x3D;&#x3D; value) {
            throw new BadRequestException(
              &#x60;Field ${field.name || field.uid} contains invalid integer&#x60;,
            );
          }
        }
        break;
      }
      case CustomFieldType.DATE:
      case CustomFieldType.DATE_TIME: {
        this.singleValue(field, values);
        for (const value of values || []) {
          const dateValidation &#x3D; new DateValidation();
          dateValidation.date &#x3D; value;
          const errors &#x3D; validateSync(dateValidation);
          if (errors.length &gt; 0) {
            throw new BadRequestException(
              &#x60;Field ${field.name || field.uid} must be a valid date&#x60;,
            );
          }
        }
        break;
      }
      case CustomFieldType.CURRENCY:
      case CustomFieldType.DECIMAL: {
        this.singleValue(field, values);
        for (const value of values || []) {
          const decimalValidation &#x3D; new DecimalValidation();
          decimalValidation.decimal &#x3D; value;
          const errors &#x3D; validateSync(decimalValidation);
          if (errors.length &gt; 0) {
            throw new BadRequestException(
              &#x60;Field ${field.name || field.uid} contains invalid format&#x60;,
            );
          }
        }
        break;
      }
      case CustomFieldType.PASSWORD:
        this.singleValue(field, values);
        for (const value of values || []) {
          if (!isValidPassword(value)) {
            throw new BadRequestException(
              &#x60;Field ${field.name || field.uid} contains invalid password&#x60;,
            );
          }
        }
        break;
      case CustomFieldType.PHONE_NUMBER:
        this.singleValue(field, values);
        for (const value of values || []) {
          if (!isValidPhoneNumber(value)) {
            throw new BadRequestException(
              &#x60;Field ${field.name || field.uid} contains invalid phone number&#x60;,
            );
          }
        }
        break;
      case CustomFieldType.REGEX:
        this.singleValue(field, values);
        for (const value of values || []) {
          if (!isValidRegex(value)) {
            throw new BadRequestException(
              &#x60;Field ${field.name || field.uid} contains invalid regex&#x60;,
            );
          }
        }
        break;
      case CustomFieldType.IP_ADDRESS: {
        this.singleValue(field, values);
        for (const value of values || []) {
          const ipAddressValidation &#x3D; new IpAddressValidation();
          ipAddressValidation.ipAddress &#x3D; value;
          const errors &#x3D; validateSync(ipAddressValidation);

          if (errors.length &gt; 0) {
            throw new BadRequestException(
              &#x60;Field ${field.name || field.uid} contains invalid IP address&#x60;,
            );
          }
        }
        break;
      }
      case CustomFieldType.TOGGLE:
      case CustomFieldType.BOOLEAN:
        this.singleValue(field, values);
        for (const value of values || []) {
          if (value !&#x3D;&#x3D; &quot;true&quot; &amp;&amp; value !&#x3D;&#x3D; &quot;false&quot;) {
            throw new BadRequestException(
              &#x60;Field ${field.name || field.uid} contains invalid boolean&#x60;,
            );
          }
        }
        break;
      case CustomFieldType.FILE_UPLOAD:
      case CustomFieldType.CALCULATED:
      case CustomFieldType.LOOKUP:
      case CustomFieldType.ADDRESS:
      case CustomFieldType.COORDINATES:
      case CustomFieldType.RATING:
        break;
      default:
        throw new BadRequestException(
          &#x60;Unknown field type: ${field.fieldType} for field ${
            field.name || field.uid
          }&#x60;,
        );
    }
  }

  singleValue(field: CustomField | ThenaRestrictedField, values: any) {
    if (Array.isArray(values) &amp;&amp; values.length &gt; 1) {
      throw new BadRequestException(
        &#x60;Field ${field.name || field.uid} cannot have multiple values&#x60;,
      );
    }
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'CustomFieldvalidatorService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
