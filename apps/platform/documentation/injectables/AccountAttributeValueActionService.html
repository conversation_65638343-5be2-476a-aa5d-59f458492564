<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >AccountAttributeValueActionService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/accounts/services/account-attribute-value.action.service.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createAccountAttributeValue" >createAccountAttributeValue</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#deleteAccountAttributeValue" >deleteAccountAttributeValue</a>
                            </li>
                            <li>
                                <a href="#findAccountAttributeValueByUID" >findAccountAttributeValueByUID</a>
                            </li>
                            <li>
                                <a href="#findAccountAttributeValues" >findAccountAttributeValues</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#isAttributeInUse" >isAttributeInUse</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateAccountAttributeValue" >updateAccountAttributeValue</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(accountAttributeValueRepository: AccountAttributeValueRepository, cachedAccountAttributeValueRepository: CachedAccountAttributeValueRepository, accountCommonService: <a href="../injectables/AccountCommonService.html" target="_self">AccountCommonService</a>, accountsService: <a href="../injectables/AccountsService.html" target="_self">AccountsService</a>, customerContactActionService: <a href="../injectables/CustomerContactActionService.html" target="_self">CustomerContactActionService</a>, accountActivityActionService: <a href="../injectables/AccountActivityActionService.html" target="_self">AccountActivityActionService</a>, accountNoteActionService: <a href="../injectables/AccountNoteActionService.html" target="_self">AccountNoteActionService</a>, accountTaskActionService: <a href="../injectables/AccountTaskActionService.html" target="_self">AccountTaskActionService</a>, transactionService: TransactionService, activitiesService: <a href="../injectables/ActivitiesService.html" target="_self">ActivitiesService</a>, accountsEventsFactory: <a href="../injectables/AccountsEventsFactory.html" target="_self">AccountsEventsFactory</a>, eventEmitter: EventEmitter2)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="35" class="link-to-prism">src/accounts/services/account-attribute-value.action.service.ts:35</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>accountAttributeValueRepository</td>
                                                  
                                                        <td>
                                                                    <code>AccountAttributeValueRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>cachedAccountAttributeValueRepository</td>
                                                  
                                                        <td>
                                                                    <code>CachedAccountAttributeValueRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>accountCommonService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/AccountCommonService.html" target="_self" >AccountCommonService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>accountsService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/AccountsService.html" target="_self" >AccountsService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>customerContactActionService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/CustomerContactActionService.html" target="_self" >CustomerContactActionService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>accountActivityActionService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/AccountActivityActionService.html" target="_self" >AccountActivityActionService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>accountNoteActionService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/AccountNoteActionService.html" target="_self" >AccountNoteActionService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>accountTaskActionService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/AccountTaskActionService.html" target="_self" >AccountTaskActionService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>transactionService</td>
                                                  
                                                        <td>
                                                                    <code>TransactionService</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>activitiesService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/ActivitiesService.html" target="_self" >ActivitiesService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>accountsEventsFactory</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/AccountsEventsFactory.html" target="_self" >AccountsEventsFactory</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>eventEmitter</td>
                                                  
                                                        <td>
                                                                    <code>EventEmitter2</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createAccountAttributeValue"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>createAccountAttributeValue</b></span>
                        <a href="#createAccountAttributeValue"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createAccountAttributeValue(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, createAccountAttributeValueDto: <a href="../classes/CreateAccountAttributeValueDto.html" target="_self">CreateAccountAttributeValueDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="117"
                                    class="link-to-prism">src/accounts/services/account-attribute-value.action.service.ts:117</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Creates a new account attribute value.</p>
<p>If the attribute value is set as default,</p>
<ul>
<li>we update the previous default attribute value to not be default.</li>
<li>we invalidate the cache for the previous default attribute value.</li>
</ul>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>createAccountAttributeValueDto</td>
                                            <td>
                                                            <code><a href="../classes/CreateAccountAttributeValueDto.html" target="_self" >CreateAccountAttributeValueDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The DTO containing the attribute value details.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;AccountAttributeValue&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The created account attribute value.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteAccountAttributeValue"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>deleteAccountAttributeValue</b></span>
                        <a href="#deleteAccountAttributeValue"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteAccountAttributeValue(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, attributeValueUID: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, forceDelete: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank">boolean</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="367"
                                    class="link-to-prism">src/accounts/services/account-attribute-value.action.service.ts:367</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Deletes an account attribute value.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Default value</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>

                                            <td>
                                            </td>

                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>attributeValueUID</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>

                                            <td>
                                            </td>

                                            <td>
                                                    <p>The UID of the account attribute value.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>forceDelete</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>

                                            <td>
                                                    <code>false</code>
                                            </td>

                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;AccountAttributeValue&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The deleted account attribute value.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAccountAttributeValueByUID"></a>
                    <span class="name">
                        <span ><b>findAccountAttributeValueByUID</b></span>
                        <a href="#findAccountAttributeValueByUID"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findAccountAttributeValueByUID(organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, attributeValueUID: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="222"
                                    class="link-to-prism">src/accounts/services/account-attribute-value.action.service.ts:222</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds an account attribute value by its UID.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>attributeValueUID</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The UID of the account attribute value.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;AccountAttributeValue&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The account attribute value.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAccountAttributeValues"></a>
                    <span class="name">
                        <span ><b>findAccountAttributeValues</b></span>
                        <a href="#findAccountAttributeValues"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findAccountAttributeValues(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, attribute: AccountAttributeType)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="204"
                                    class="link-to-prism">src/accounts/services/account-attribute-value.action.service.ts:204</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds all active account attribute values for an organization.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>attribute</td>
                                            <td>
                                                        <code>AccountAttributeType</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;AccountAttributeValue[]&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The active account attribute values.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isAttributeInUse"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>isAttributeInUse</b></span>
                        <a href="#isAttributeInUse"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>isAttributeInUse(attributeValue: AccountAttributeValue)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="66"
                                    class="link-to-prism">src/accounts/services/account-attribute-value.action.service.ts:66</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Checks if an attribute value is in use.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>attributeValue</td>
                                            <td>
                                                        <code>AccountAttributeValue</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The attribute value to check.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;boolean&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>Whether the attribute value is in use.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateAccountAttributeValue"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>updateAccountAttributeValue</b></span>
                        <a href="#updateAccountAttributeValue"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateAccountAttributeValue(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, accountAttributeValueUID: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, updateAccountAttributeValueDto: <a href="../classes/UpdateAccountAttributeValueDto.html" target="_self">UpdateAccountAttributeValueDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="243"
                                    class="link-to-prism">src/accounts/services/account-attribute-value.action.service.ts:243</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Updates an account attribute value.</p>
<p>If the attribute value is set as default,</p>
<ul>
<li>we update the previous default attribute value to not be default.</li>
<li>we invalidate the cache for the previous default attribute value.</li>
</ul>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>accountAttributeValueUID</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The UID of the account attribute value.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>updateAccountAttributeValueDto</td>
                                            <td>
                                                            <code><a href="../classes/UpdateAccountAttributeValueDto.html" target="_self" >UpdateAccountAttributeValueDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The DTO containing the updated attribute value details.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;AccountAttributeValue&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The updated account attribute value.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from &quot;@nestjs/common&quot;;
import { EventEmitter2 } from &quot;@nestjs/event-emitter&quot;;
import {
  AccountAttributeType,
  AccountAttributeValue,
  AccountAttributeValueRepository,
  AuditLogEntityType,
  AuditLogOp,
  AuditLogVisibility,
  CachedAccountAttributeValueRepository,
  TransactionService,
  UserType,
} from &quot;@repo/thena-platform-entities&quot;;
import { cloneDeep } from &quot;lodash&quot;;
import { ActivitiesService } from &quot;../../activities/services/activities.service&quot;;
import { CurrentUser } from &quot;../../common/decorators/user.decorator&quot;;
import {
  CreateAccountAttributeValueDto,
  UpdateAccountAttributeValueDto,
} from &quot;../dtos/account-attribute-value.dto&quot;;
import { AccountsEventsFactory } from &quot;../events/accounts-events.factory&quot;;
import { EmittableAccountEvents } from &quot;../events/accounts.events&quot;;
import { AccountActivityActionService } from &quot;./account-activity.action.service&quot;;
import { AccountCommonService } from &quot;./account-commons.service&quot;;
import { AccountNoteActionService } from &quot;./account-note.action.service&quot;;
import { AccountTaskActionService } from &quot;./account-task.action.service&quot;;
import { AccountsService } from &quot;./accounts.service&quot;;
import { CustomerContactActionService } from &quot;./customer-contact.action.service&quot;;

@Injectable()
export class AccountAttributeValueActionService {
  constructor(
    // Injected repositories
    private accountAttributeValueRepository: AccountAttributeValueRepository,
    private cachedAccountAttributeValueRepository: CachedAccountAttributeValueRepository,

    // Accounts services
    private readonly accountCommonService: AccountCommonService,
    private readonly accountsService: AccountsService,
    private readonly customerContactActionService: CustomerContactActionService,
    private readonly accountActivityActionService: AccountActivityActionService,
    private readonly accountNoteActionService: AccountNoteActionService,
    private readonly accountTaskActionService: AccountTaskActionService,

    // Transaction service
    private readonly transactionService: TransactionService,

    // Activities service
    private readonly activitiesService: ActivitiesService,

    // Event emitter
    private readonly accountsEventsFactory: AccountsEventsFactory,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * Checks if an attribute value is in use.
   *
   * @param attributeValue The attribute value to check.
   * @returns Whether the attribute value is in use.
   */
  private async isAttributeInUse(
    attributeValue: AccountAttributeValue,
  ): Promise&lt;boolean&gt; {
    switch (attributeValue.attribute) {
      case AccountAttributeType.ACCOUNT_STATUS:
      case AccountAttributeType.ACCOUNT_CLASSIFICATION:
      case AccountAttributeType.ACCOUNT_HEALTH:
      case AccountAttributeType.ACCOUNT_INDUSTRY:
        return await this.accountsService.isAccountAttributeValueInUse(
          attributeValue.id,
          attributeValue.attribute,
        );
      case AccountAttributeType.CONTACT_TYPE:
        return await this.customerContactActionService.isContactTypeAttributeInUse(
          attributeValue.id,
        );
      case AccountAttributeType.ACTIVITY_STATUS:
        return await this.accountActivityActionService.isActivityStatusAttributeInUse(
          attributeValue.id,
        );
      case AccountAttributeType.ACTIVITY_TYPE:
        return await this.accountActivityActionService.isActivityTypeAttributeInUse(
          attributeValue.id,
        );
      case AccountAttributeType.NOTE_TYPE:
        return await this.accountNoteActionService.isNoteTypeAttributeInUse(
          attributeValue.id,
        );
      case AccountAttributeType.TASK_TYPE:
      case AccountAttributeType.TASK_STATUS:
      case AccountAttributeType.TASK_PRIORITY:
        return await this.accountTaskActionService.isTaskAttributeValueInUse(
          attributeValue.id,
          attributeValue.attribute,
        );
      default:
        return false;
    }
  }

  /**
   * Creates a new account attribute value.
   *
   * If the attribute value is set as default,
   * - we update the previous default attribute value to not be default.
   * - we invalidate the cache for the previous default attribute value.
   *
   * @param organizationId The ID of the organization.
   * @param createAccountAttributeValueDto The DTO containing the attribute value details.
   * @returns The created account attribute value.
   */
  async createAccountAttributeValue(
    user: CurrentUser,
    createAccountAttributeValueDto: CreateAccountAttributeValueDto,
  ): Promise&lt;AccountAttributeValue&gt; {
    const organizationId &#x3D; user.orgId;

    const attributeValue &#x3D; this.accountAttributeValueRepository.create({
      organizationId,
      ...createAccountAttributeValueDto,
    });

    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      if (createAccountAttributeValueDto.isDefault) {
        // Find default attribute value and set it to not default
        const prevDefaultAttributeValue &#x3D;
          await this.accountCommonService.findDefaultAttributeValue(
            organizationId,
            createAccountAttributeValueDto.attribute,
          );

        if (prevDefaultAttributeValue) {
          prevDefaultAttributeValue.isDefault &#x3D; false;
          await this.accountAttributeValueRepository.saveWithTxn(
            txnContext,
            prevDefaultAttributeValue,
          );

          // Invalidate cache for the all attribute values and old default attribute value
          await this.cachedAccountAttributeValueRepository.invalidateAccountAttributeValueCache(
            {
              organizationId,
              attribute: createAccountAttributeValueDto.attribute,
              isDefault: true,
              attributeValueUID: prevDefaultAttributeValue.uid,
            },
          );
        }
      }

      // Invalidate cache for the all attribute values
      await this.cachedAccountAttributeValueRepository.invalidateAccountAttributeValueCache(
        {
          organizationId,
          attribute: createAccountAttributeValueDto.attribute,
        },
      );

      // Record audit log
      await this.activitiesService.recordAuditLog(
        {
          organization: { id: organizationId },
          activityPerformedBy:
            user.userType &#x3D;&#x3D;&#x3D; UserType.BOT_USER ? null : { id: user.sub },
          isAutomated: user.userType &#x3D;&#x3D;&#x3D; UserType.BOT_USER,
          entityId: attributeValue.id,
          entityUid: attributeValue.uid,
          entityType: AuditLogEntityType.ACCOUNT_ATTRIBUTE_VALUE,
          op: AuditLogOp.CREATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: &#x60;Account attribute value ${attributeValue.id} was created!&#x60;,
          description: &#x60;Account attribute value ${attributeValue.id} was created by ${user.email}!&#x60;,
        },
        txnContext,
      );

      // Save the attribute value
      await this.accountAttributeValueRepository.saveWithTxn(
        txnContext,
        attributeValue,
      );
    });

    return await this.cachedAccountAttributeValueRepository.findByCondition({
      where: {
        uid: attributeValue.uid,
        organizationId,
        isActive: true,
      },
    });
  }

  /**
   * Finds all active account attribute values for an organization.
   *
   * @param organizationId The ID of the organization.
   * @returns The active account attribute values.
   */
  findAccountAttributeValues(
    user: CurrentUser,
    attribute: AccountAttributeType,
  ): Promise&lt;AccountAttributeValue[]&gt; {
    const organizationId &#x3D; user.orgId;

    return this.cachedAccountAttributeValueRepository.findAll({
      where: { organizationId, isActive: true, attribute },
    });
  }

  /**
   * Finds an account attribute value by its UID.
   *
   * @param organizationId The ID of the organization.
   * @param attributeValueUID The UID of the account attribute value.
   * @returns The account attribute value.
   */
  findAccountAttributeValueByUID(
    organizationId: string,
    attributeValueUID: string,
  ): Promise&lt;AccountAttributeValue&gt; {
    return this.cachedAccountAttributeValueRepository.findByCondition({
      where: { uid: attributeValueUID, organizationId, isActive: true },
    });
  }

  /**
   * Updates an account attribute value.
   *
   * If the attribute value is set as default,
   * - we update the previous default attribute value to not be default.
   * - we invalidate the cache for the previous default attribute value.
   *
   * @param organizationId The ID of the organization.
   * @param accountAttributeValueUID The UID of the account attribute value.
   * @param updateAccountAttributeValueDto The DTO containing the updated attribute value details.
   * @returns The updated account attribute value.
   */
  async updateAccountAttributeValue(
    user: CurrentUser,
    accountAttributeValueUID: string,
    updateAccountAttributeValueDto: UpdateAccountAttributeValueDto,
  ): Promise&lt;AccountAttributeValue&gt; {
    const organizationId &#x3D; user.orgId;

    const attributeValue &#x3D;
      await this.cachedAccountAttributeValueRepository.findByCondition({
        where: {
          uid: accountAttributeValueUID,
          isActive: true,
          organizationId,
        },
      });

    if (!attributeValue) {
      throw new NotFoundException(&quot;Account attribute value not found&quot;);
    }

    const existingAttributeValue &#x3D; cloneDeep(attributeValue);

    attributeValue.value &#x3D;
      updateAccountAttributeValueDto.value ?? attributeValue.value;

    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      if (updateAccountAttributeValueDto.isDefault) {
        // Find default attribute value and set it to not default
        const prevDefaultAttributeValue &#x3D;
          await this.accountCommonService.findDefaultAttributeValue(
            organizationId,
            attributeValue.attribute,
          );

        if (prevDefaultAttributeValue) {
          prevDefaultAttributeValue.isDefault &#x3D; false;
          await this.accountAttributeValueRepository.saveWithTxn(
            txnContext,
            prevDefaultAttributeValue,
          );

          attributeValue.isDefault &#x3D; true;

          // Invalidate cache for the all attribute values and old default attribute value
          await this.cachedAccountAttributeValueRepository.invalidateAccountAttributeValueCache(
            {
              organizationId,
              attribute: attributeValue.attribute,
              attributeValueUID: prevDefaultAttributeValue.uid,
            },
          );
        }
      } else if (
        updateAccountAttributeValueDto.isDefault &#x3D;&#x3D;&#x3D; false &amp;&amp;
        attributeValue.isDefault
      ) {
        throw new BadRequestException(
          &quot;Cannot set attribute value as not default. Assign another default value first.&quot;,
        );
      }

      // Invalidate cache for the all attribute values and current attribute value
      await this.cachedAccountAttributeValueRepository.invalidateAccountAttributeValueCache(
        {
          organizationId,
          attribute: attributeValue.attribute,
          isDefault: true,
          attributeValueUID: attributeValue.uid,
        },
      );

      // Record audit log
      await this.activitiesService.recordAuditLog(
        {
          organization: { id: organizationId },
          activityPerformedBy: { id: user.sub },
          entityId: attributeValue.id,
          entityUid: attributeValue.uid,
          entityType: AuditLogEntityType.ACCOUNT_ATTRIBUTE_VALUE,
          op: AuditLogOp.UPDATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: &#x60;Account attribute value ${attributeValue.id} was updated!&#x60;,
          description: &#x60;Account attribute value ${attributeValue.id} was updated by ${user.email}!&#x60;,
          metadata: {
            updatedFields: [
              existingAttributeValue.value !&#x3D;&#x3D; attributeValue.value &amp;&amp; {
                field: &quot;value&quot;,
                previousValue: JSON.stringify(existingAttributeValue.value),
                updatedToValue: JSON.stringify(attributeValue.value),
              },
              existingAttributeValue.isDefault !&#x3D;&#x3D; attributeValue.isDefault &amp;&amp; {
                field: &quot;isDefault&quot;,
                previousValue: existingAttributeValue.isDefault.toString(),
                updatedToValue: attributeValue.isDefault.toString(),
              },
            ].filter(Boolean),
          },
        },
        txnContext,
      );

      // Save attribute value
      await this.accountAttributeValueRepository.saveWithTxn(
        txnContext,
        attributeValue,
      );
    });

    return await this.cachedAccountAttributeValueRepository.findByCondition({
      where: {
        uid: attributeValue.uid,
        organizationId,
        isActive: true,
      },
    });
  }

  /**
   * Deletes an account attribute value.
   *
   * @param organizationId The ID of the organization.
   * @param attributeValueUID The UID of the account attribute value.
   * @returns The deleted account attribute value.
   */
  async deleteAccountAttributeValue(
    user: CurrentUser,
    attributeValueUID: string,
    forceDelete: boolean &#x3D; false,
  ): Promise&lt;AccountAttributeValue&gt; {
    const organizationId &#x3D; user.orgId;

    const attributeValue &#x3D;
      await this.cachedAccountAttributeValueRepository.findByCondition({
        where: {
          uid: attributeValueUID,
          isActive: true,
          organizationId,
        },
      });

    if (!attributeValue) {
      throw new NotFoundException(&quot;Account attribute value not found&quot;);
    }

    if (attributeValue.isDefault) {
      throw new BadRequestException(
        &quot;Cannot delete default attribute value. Assign another default value first.&quot;,
      );
    }

    if (!forceDelete &amp;&amp; (await this.isAttributeInUse(attributeValue))) {
      throw new BadRequestException(
        &quot;Cannot delete attribute value. It is in use.&quot;,
      );
    }

    attributeValue.isActive &#x3D; false;
    attributeValue.deletedAt &#x3D; new Date();

    return this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      // Save attribute value
      const savedAttributeValue &#x3D;
        await this.accountAttributeValueRepository.saveWithTxn(
          txnContext,
          attributeValue,
        );

      // Record audit log
      await this.activitiesService.recordAuditLog(
        {
          organization: { id: organizationId },
          activityPerformedBy: { id: user.sub },
          entityId: attributeValue.id,
          entityUid: attributeValue.uid,
          entityType: AuditLogEntityType.ACCOUNT_ATTRIBUTE_VALUE,
          op: AuditLogOp.DELETED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: &#x60;Account attribute value ${attributeValue.id} was deleted!&#x60;,
          description: &#x60;Account attribute value ${attributeValue.id} was deleted by ${user.email}!&#x60;,
          metadata: {
            updatedFields: [
              {
                field: &quot;isActive&quot;,
                previousValue: &quot;true&quot;,
                updatedToValue: &quot;false&quot;,
              },
            ],
          },
        },
        txnContext,
      );

      // Invalidate cache for the all attribute values and the deleted attribute value
      await this.cachedAccountAttributeValueRepository.invalidateAccountAttributeValueCache(
        {
          organizationId,
          attribute: attributeValue.attribute,
          attributeValueUID: attributeValue.uid,
        },
      );

      if (forceDelete) {
        // Emit deleted event
        const accountAttributeValueDeletedEvent &#x3D;
          this.accountsEventsFactory.createAccountAttributeValueForceDeletedEvent(
            savedAttributeValue.organizationId,
            savedAttributeValue.attribute,
            savedAttributeValue.id,
          );

        this.eventEmitter.emit(
          EmittableAccountEvents.ACCOUNT_ATTRIBUTE_VALUE_DELETED,
          accountAttributeValueDeletedEvent,
        );
      }

      return savedAttributeValue;
    });
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'AccountAttributeValueActionService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
