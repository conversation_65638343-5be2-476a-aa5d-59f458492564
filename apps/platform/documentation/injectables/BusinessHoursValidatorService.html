<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >BusinessHoursValidatorService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/common/services/business-hours-validation.service.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#getAllSlotsInUTC" >getAllSlotsInUTC</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#isValidTimeFormat" >isValidTimeFormat</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#parseTimeInMinutes" >parseTimeInMinutes</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#parseTimeInTimezone" >parseTimeInTimezone</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#parseTimeToMinutes" >parseTimeToMinutes</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#validateBusinessDay" >validateBusinessDay</a>
                            </li>
                            <li>
                                <a href="#validateBusinessHours" >validateBusinessHours</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#validateDaySlots" >validateDaySlots</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#validateNoOverlapsInUTC" >validateNoOverlapsInUTC</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>


            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getAllSlotsInUTC"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>getAllSlotsInUTC</b></span>
                        <a href="#getAllSlotsInUTC"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getAllSlotsInUTC(businessHours: <a href="../classes/BusinessHoursConfigDto.html" target="_self">BusinessHoursConfigDto</a>, timezone: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="145"
                                    class="link-to-prism">src/common/services/business-hours-validation.service.ts:145</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>businessHours</td>
                                            <td>
                                                            <code><a href="../classes/BusinessHoursConfigDto.html" target="_self" >BusinessHoursConfigDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>timezone</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isValidTimeFormat"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>isValidTimeFormat</b></span>
                        <a href="#isValidTimeFormat"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>isValidTimeFormat(time: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="142"
                                    class="link-to-prism">src/common/services/business-hours-validation.service.ts:142</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>time</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="parseTimeInMinutes"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>parseTimeInMinutes</b></span>
                        <a href="#parseTimeInMinutes"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>parseTimeInMinutes(time: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="222"
                                    class="link-to-prism">src/common/services/business-hours-validation.service.ts:222</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>time</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="parseTimeInTimezone"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>parseTimeInTimezone</b></span>
                        <a href="#parseTimeInTimezone"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>parseTimeInTimezone(time: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, timezone: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, baseDate?: DateTime)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="227"
                                    class="link-to-prism">src/common/services/business-hours-validation.service.ts:227</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>time</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>timezone</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>baseDate</td>
                                            <td>
                                                        <code>DateTime</code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>DateTime</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="parseTimeToMinutes"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>parseTimeToMinutes</b></span>
                        <a href="#parseTimeToMinutes"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>parseTimeToMinutes(time: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="137"
                                    class="link-to-prism">src/common/services/business-hours-validation.service.ts:137</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>time</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateBusinessDay"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>validateBusinessDay</b></span>
                        <a href="#validateBusinessDay"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateBusinessDay(dayConfig: <a href="../classes/BusinessDayDto.html" target="_self">BusinessDayDto</a>, dayName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="40"
                                    class="link-to-prism">src/common/services/business-hours-validation.service.ts:40</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>dayConfig</td>
                                            <td>
                                                            <code><a href="../classes/BusinessDayDto.html" target="_self" >BusinessDayDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>dayName</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>literal type</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateBusinessHours"></a>
                    <span class="name">
                        <span ><b>validateBusinessHours</b></span>
                        <a href="#validateBusinessHours"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>validateBusinessHours(businessHours: <a href="../classes/BusinessHoursConfigDto.html" target="_self">BusinessHoursConfigDto</a>, timezone: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="11"
                                    class="link-to-prism">src/common/services/business-hours-validation.service.ts:11</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>businessHours</td>
                                            <td>
                                                            <code><a href="../classes/BusinessHoursConfigDto.html" target="_self" >BusinessHoursConfigDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>timezone</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>literal type</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateDaySlots"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>validateDaySlots</b></span>
                        <a href="#validateDaySlots"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateDaySlots(slots: <a href="../classes/BusinessSlotDto.html" target="_self">BusinessSlotDto[]</a>, dayName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="88"
                                    class="link-to-prism">src/common/services/business-hours-validation.service.ts:88</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>slots</td>
                                            <td>
                                                            <code><a href="../classes/BusinessSlotDto.html" target="_self" >BusinessSlotDto[]</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>dayName</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>literal type</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateNoOverlapsInUTC"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>validateNoOverlapsInUTC</b></span>
                        <a href="#validateNoOverlapsInUTC"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateNoOverlapsInUTC(slots: Array<literal type>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="200"
                                    class="link-to-prism">src/common/services/business-hours-validation.service.ts:200</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>slots</td>
                                            <td>
                                                        <code>Array&lt;literal type&gt;</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>literal type</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Injectable } from &quot;@nestjs/common&quot;;
import { DateTime } from &quot;luxon&quot;;
import {
  BusinessDayDto,
  BusinessHoursConfigDto,
  BusinessSlotDto,
} from &quot;../dto&quot;;

@Injectable()
export class BusinessHoursValidatorService {
  validateBusinessHours(
    businessHours: BusinessHoursConfigDto,
    timezone: string,
  ): { isValid: boolean; error?: string } {
    try {
      // Validate individual days first (no timezone needed)
      for (const [day, config] of Object.entries(businessHours)) {
        const dayValidation &#x3D; this.validateBusinessDay(config, day);
        if (!dayValidation.isValid) {
          return dayValidation;
        }
      }

      // Then validate all days together for timezone-based overlaps
      const allSlotsInUTC &#x3D; this.getAllSlotsInUTC(businessHours, timezone);
      const overlapValidation &#x3D; this.validateNoOverlapsInUTC(allSlotsInUTC);
      if (!overlapValidation.isValid) {
        return overlapValidation;
      }

      return { isValid: true };
    } catch (error) {
      return {
        isValid: false,
        error: &#x60;Invalid business hours configuration: ${error.message}&#x60;,
      };
    }
  }

  private validateBusinessDay(
    dayConfig: BusinessDayDto,
    dayName: string,
  ): { isValid: boolean; error?: string } {
    if (!dayConfig) {
      return { isValid: false, error: &#x60;Invalid configuration for ${dayName}&#x60; };
    }

    if (dayConfig.isActive) {
      if (
        !dayConfig.slots ||
        !Array.isArray(dayConfig.slots) ||
        dayConfig.slots.length &#x3D;&#x3D;&#x3D; 0
      ) {
        return {
          isValid: false,
          error: &#x60;Active day ${dayName} must have at least one time slot&#x60;,
        };
      }

      // Validate each slot&#x27;s time format
      for (const slot of dayConfig.slots) {
        if (
          !this.isValidTimeFormat(slot.start) ||
          !this.isValidTimeFormat(slot.end)
        ) {
          return {
            isValid: false,
            error: &#x60;Invalid time format for ${dayName}. Use HH:mm format&#x60;,
          };
        }
      }

      // Validate slots don&#x27;t overlap within the same day
      const slotsValidation &#x3D; this.validateDaySlots(dayConfig.slots, dayName);
      if (!slotsValidation.isValid) {
        return slotsValidation;
      }
    } else if (dayConfig.slots &amp;&amp; dayConfig.slots.length &gt; 0) {
      return {
        isValid: false,
        error: &#x60;Inactive day ${dayName} should not have time slots&#x60;,
      };
    }

    return { isValid: true };
  }

  private validateDaySlots(
    slots: BusinessSlotDto[],
    dayName: string,
  ): { isValid: boolean; error?: string } {
    // Sort slots by start time
    const sortedSlots &#x3D; [...slots].sort((a, b) &#x3D;&gt; {
      const startA &#x3D; this.parseTimeToMinutes(a.start);
      const startB &#x3D; this.parseTimeToMinutes(b.start);
      return startA - startB;
    });

    // Check each slot&#x27;s validity and overlaps
    for (let i &#x3D; 0; i &lt; sortedSlots.length; i++) {
      const currentSlot &#x3D; sortedSlots[i];
      const startMinutes &#x3D; this.parseTimeToMinutes(currentSlot.start);
      const endMinutes &#x3D; this.parseTimeToMinutes(currentSlot.end);

      // Check if slot spans more than 24 hours
      let duration &#x3D; endMinutes - startMinutes;
      if (endMinutes &lt;&#x3D; startMinutes) {
        // Crossing midnight
        duration &#x3D; 24 * 60 - startMinutes + endMinutes;
      }

      if (duration &lt;&#x3D; 0 || duration &gt; 24 * 60) {
        return {
          isValid: false,
          error: &#x60;Invalid duration for slot in ${dayName}: ${currentSlot.start}-${currentSlot.end}&#x60;,
        };
      }

      // Check overlap with next slot
      if (i &lt; sortedSlots.length - 1) {
        const nextSlot &#x3D; sortedSlots[i + 1];
        const nextStartMinutes &#x3D; this.parseTimeToMinutes(nextSlot.start);

        // For same-day validation, we just need to check if the end time is after the next start time
        if (endMinutes &gt; nextStartMinutes) {
          return {
            isValid: false,
            error: &#x60;Overlapping slots in ${dayName}: ${currentSlot.start}-${currentSlot.end} and ${nextSlot.start}-${nextSlot.end}&#x60;,
          };
        }
      }
    }

    return { isValid: true };
  }

  private parseTimeToMinutes(time: string): number {
    const [hours, minutes] &#x3D; time.split(&quot;:&quot;).map(Number);
    return hours * 60 + minutes;
  }

  private isValidTimeFormat(time: string): boolean {
    return /^([01]\d|2[0-3]):([0-5]\d)$/.test(time);
  }
  private getAllSlotsInUTC(
    businessHours: BusinessHoursConfigDto,
    timezone: string,
  ) {
    const slots: Array&lt;{
      day: string;
      utcStart: DateTime;
      utcEnd: DateTime;
    }&gt; &#x3D; [];

    const days &#x3D; [
      &quot;monday&quot;,
      &quot;tuesday&quot;,
      &quot;wednesday&quot;,
      &quot;thursday&quot;,
      &quot;friday&quot;,
      &quot;saturday&quot;,
      &quot;sunday&quot;,
    ];

    days.forEach((day, index) &#x3D;&gt; {
      const businessDay &#x3D; businessHours[day];
      if (businessDay?.isActive &amp;&amp; businessDay.slots) {
        const baseDate &#x3D; DateTime.now()
          .setZone(timezone)
          .startOf(&quot;week&quot;)
          .plus({ days: index });

        // Add each slot for the day
        businessDay.slots.forEach((slot) &#x3D;&gt; {
          const startDt &#x3D; this.parseTimeInTimezone(
            slot.start,
            timezone,
            baseDate,
          );
          const endDt &#x3D; this.parseTimeInTimezone(slot.end, timezone, baseDate);

          // Handle slots crossing midnight
          let adjustedEndDt &#x3D; endDt;
          if (endDt &lt; startDt) {
            adjustedEndDt &#x3D; endDt.plus({ days: 1 });
          }

          slots.push({
            day,
            utcStart: startDt.toUTC(),
            utcEnd: adjustedEndDt.toUTC(),
          });
        });
      }
    });

    return slots.sort((a, b) &#x3D;&gt; a.utcStart.toMillis() - b.utcStart.toMillis());
  }

  private validateNoOverlapsInUTC(
    slots: Array&lt;{
      day: string;
      utcStart: DateTime;
      utcEnd: DateTime;
    }&gt;,
  ): { isValid: boolean; error?: string } {
    for (let i &#x3D; 0; i &lt; slots.length - 1; i++) {
      const current &#x3D; slots[i];
      const next &#x3D; slots[i + 1];

      if (current.utcEnd &gt; next.utcStart) {
        return {
          isValid: false,
          error: &#x60;Business hours overlap detected between ${current.day} and ${next.day} in UTC&#x60;,
        };
      }
    }

    return { isValid: true };
  }

  private parseTimeInMinutes(time: string): number {
    const [hours, minutes] &#x3D; time.split(&quot;:&quot;).map(Number);
    return hours * 60 + minutes;
  }

  private parseTimeInTimezone(
    time: string,
    timezone: string,
    baseDate?: DateTime,
  ): DateTime {
    const [hours, minutes] &#x3D; time.split(&quot;:&quot;).map(Number);
    const base &#x3D; baseDate || DateTime.now().setZone(timezone);

    return base.set({
      hour: hours,
      minute: minutes,
      second: 0,
      millisecond: 0,
    });
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'BusinessHoursValidatorService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
