<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >ViewsService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/views/services/views.service.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Readonly</span>
                                <a href="#logSpanId" >logSpanId</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#createNewView" >createNewView</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#createViewWithViewType" >createViewWithViewType</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#deleteView" >deleteView</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#duplicateViewForUser" >duplicateViewForUser</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#getViewForUser" >getViewForUser</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#getViewsForUser" >getViewsForUser</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#updateView" >updateView</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(logger: ILogger, viewsRepository: ViewsRepository, viewsTypeRepository: ViewsTypeRepository, teamsService: <a href="../injectables/TeamsService.html" target="_self">TeamsService</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="22" class="link-to-prism">src/views/services/views.service.ts:22</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>logger</td>
                                                  
                                                        <td>
                                                                    <code>ILogger</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>viewsRepository</td>
                                                  
                                                        <td>
                                                                    <code>ViewsRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>viewsTypeRepository</td>
                                                  
                                                        <td>
                                                                    <code>ViewsTypeRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>teamsService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/TeamsService.html" target="_self" >TeamsService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createNewView"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>createNewView</b></span>
                        <a href="#createNewView"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createNewView(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, body: <a href="../classes/CreateViewBody.html" target="_self">CreateViewBody</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="82"
                                    class="link-to-prism">src/views/services/views.service.ts:82</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Creates a new view.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>body</td>
                                            <td>
                                                            <code><a href="../classes/CreateViewBody.html" target="_self" >CreateViewBody</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The body of the request.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The created view.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createViewWithViewType"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>createViewWithViewType</b></span>
                        <a href="#createViewWithViewType"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createViewWithViewType(data: Partial<Views>, readTheWrite: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank">boolean</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="38"
                                    class="link-to-prism">src/views/services/views.service.ts:38</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Creates a view with a view type.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Default value</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>data</td>
                                            <td>
                                                        <code>Partial&lt;Views&gt;</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>

                                            <td>
                                            </td>

                                            <td>
                                                    <p>The data to create the view with.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>readTheWrite</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>

                                            <td>
                                                    <code>false</code>
                                            </td>

                                            <td>
                                                    <p>Whether to fetch the view from the database after it is created.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The created view.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteView"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>deleteView</b></span>
                        <a href="#deleteView"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteView(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, viewId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="266"
                                    class="link-to-prism">src/views/services/views.service.ts:266</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Deletes a view for a user.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>viewId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the view to delete.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The deleted view.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="duplicateViewForUser"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>duplicateViewForUser</b></span>
                        <a href="#duplicateViewForUser"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>duplicateViewForUser(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, viewId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="293"
                                    class="link-to-prism">src/views/services/views.service.ts:293</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Duplicates a view for a user.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>viewId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the view to duplicate.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The duplicated view.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getViewForUser"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>getViewForUser</b></span>
                        <a href="#getViewForUser"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getViewForUser(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, viewId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="170"
                                    class="link-to-prism">src/views/services/views.service.ts:170</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Gets a view for a user.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>viewId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the view to get.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The view.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getViewsForUser"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>getViewsForUser</b></span>
                        <a href="#getViewsForUser"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getViewsForUser(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, query: <a href="../classes/GetViewQuery.html" target="_self">GetViewQuery</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="121"
                                    class="link-to-prism">src/views/services/views.service.ts:121</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Gets all views for a user.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>query</td>
                                            <td>
                                                            <code><a href="../classes/GetViewQuery.html" target="_self" >GetViewQuery</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The views.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateView"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>updateView</b></span>
                        <a href="#updateView"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateView(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, viewId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, body: <a href="../classes/UpdateViewBody.html" target="_self">UpdateViewBody</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="214"
                                    class="link-to-prism">src/views/services/views.service.ts:214</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Updates a view for a user.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>viewId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the view to update.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>body</td>
                                            <td>
                                                            <code><a href="../classes/UpdateViewBody.html" target="_self" >UpdateViewBody</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The body of the request.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The updated view.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>
            <section data-compodoc="block-properties">
    
    <h3 id="inputs">
        Properties
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="logSpanId"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Readonly</span>
                        <span ><b>logSpanId</b></span>
                        <a href="#logSpanId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&quot;ViewsService&quot;</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="22" class="link-to-prism">src/views/services/views.service.ts:22</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException,
} from &quot;@nestjs/common&quot;;
import { ILogger } from &quot;@repo/nestjs-commons/logger&quot;;
import {
  Views,
  ViewsRepository,
  ViewsTypeRepository,
} from &quot;@repo/thena-platform-entities&quot;;
import { IsNull } from &quot;typeorm&quot;;
import { CurrentUser } from &quot;../../common/decorators&quot;;
import { TeamsService } from &quot;../../teams/services/teams.service&quot;;
import { EAGERLY_FETCH_RELATIONS } from &quot;../constants/views.constants&quot;;
import { CreateViewBody, GetViewQuery, UpdateViewBody } from &quot;../dto/views.dto&quot;;

@Injectable()
export class ViewsService {
  private readonly logSpanId &#x3D; &quot;ViewsService&quot;;

  constructor(
    @Inject(&quot;CustomLogger&quot;) private readonly logger: ILogger,
    private readonly viewsRepository: ViewsRepository,
    private readonly viewsTypeRepository: ViewsTypeRepository,
    private readonly teamsService: TeamsService,
  ) {}

  /**
   * @internal
   * Creates a view with a view type.
   * @param data The data to create the view with.
   * @param readTheWrite Whether to fetch the view from the database after it is created.
   * @returns The created view.
   */
  private async createViewWithViewType(
    data: Partial&lt;Views&gt;,
    readTheWrite: boolean &#x3D; false,
  ) {
    // Find the view type
    const viewType &#x3D; await this.viewsTypeRepository.findByCondition({
      where: { uid: data.viewsTypeId },
    });

    // If the view type is not found, throw an error
    if (!viewType) {
      throw new NotFoundException(&quot;View type not found!&quot;);
    }

    // Create the view entity
    const view &#x3D; await this.viewsRepository.createView({
      name: data.name,
      description: data?.description,
      teamId: data.teamId,
      viewsTypeId: viewType.id,
      organizationId: data.organizationId,
      ownerId: data.ownerId,
      isShared: data.isShared,
      isPersonal: data.isPersonal,
      configuration: data.configuration,
    });

    // If the read the write flag is true, fetch the view from the database
    if (readTheWrite) {
      return this.viewsRepository.findByCondition({
        where: { id: view.id, organizationId: data.organizationId },
        relations: EAGERLY_FETCH_RELATIONS,
      });
    }

    return view;
  }

  /**
   * Creates a new view.
   * @param user The current user.
   * @param body The body of the request.
   * @returns The created view.
   */
  public async createNewView(user: CurrentUser, body: CreateViewBody) {
    const { name, description, teamId, viewsTypeId, isShared, configuration } &#x3D;
      body;

    // Validate if the user belongs to the team
    const { team } &#x3D; await this.teamsService.getUserAndTeam(
      teamId,
      user.sub,
      user.orgId,
    );

    this.logger.log(
      &#x60;${this.logSpanId} Creating a new view for team ${team.name} by user ${user.email}&#x60;,
    );

    // Create the view entity
    const newView &#x3D; await this.createViewWithViewType(
      {
        name,
        description,
        viewsTypeId,
        teamId: team.id,
        ownerId: user.sub,
        organizationId: user.orgId,
        isShared: isShared ?? false,
        isPersonal: !isShared,
        configuration,
      },
      true,
    );

    return newView;
  }

  /**
   * Gets all views for a user.
   * @param user The current user.
   * @returns The views.
   */
  public async getViewsForUser(user: CurrentUser, query: GetViewQuery) {
    const { team } &#x3D; await this.teamsService.getUserAndTeam(
      query.teamId,
      user.sub,
      user.orgId,
    );

    // Get all views for the user
    const __common &#x3D; {
      organizationId: user.orgId,
      teamId: team.id,
      deletedAt: IsNull(),
    };

    const paginationQuery &#x3D; {
      page: query.page ?? 0,
      limit: Math.min(query.limit ?? 10, 100),
    };

    // Fetch the views from the database
    const { results: views } &#x3D; await this.viewsRepository.fetchPaginatedResults(
      paginationQuery,
      {
        where: [
          // User&#x27;s personal views
          { ...__common, isPersonal: true, ownerId: user.sub },

          // Team views (shared) across a team id
          { ...__common, isShared: true, teamId: team.id },
        ],
        order: { createdAt: &quot;DESC&quot; },
        relations: EAGERLY_FETCH_RELATIONS,
      },
    );

    // If no views are found, throw an error
    if (views.length &#x3D;&#x3D;&#x3D; 0) {
      throw new NotFoundException(&quot;No views found!&quot;);
    }

    return views;
  }

  /**
   * Gets a view for a user.
   * @param user The current user.
   * @param viewId The ID of the view to get.
   * @returns The view.
   */
  public async getViewForUser(user: CurrentUser, viewId: string) {
    // Get the view from the database
    const view &#x3D; await this.viewsRepository.findByCondition({
      where: { uid: viewId, organizationId: user.orgId, deletedAt: IsNull() },
      relations: EAGERLY_FETCH_RELATIONS,
    });

    // If the view is not found, throw an error
    if (!view) {
      throw new NotFoundException(&quot;Requested view not found!&quot;);
    }

    // Validate if the user belongs to the team
    const doesUserBelongToTeam &#x3D; await this.teamsService.userBelongsToTeam(
      user.sub,
      view.teamId,
      user.orgId,
    );

    // If the user does not belong to the team, throw an error
    if (!doesUserBelongToTeam) {
      throw new ForbiddenException(&quot;User does not belong to this team!&quot;);
    }

    // If the view is not shared or if the user is not the owner, throw an error
    const isOwner &#x3D; view.ownerId &#x3D;&#x3D;&#x3D; user.sub;
    const isShared &#x3D; view.isShared;
    const canAccess &#x3D; isOwner || isShared; // We can use the &#x60;isShared&#x60; flag since user&#x27;s existence in the team is already validated

    // If the user cannot access the view, throw an error
    if (!canAccess) {
      throw new ForbiddenException(&quot;User does not have access to this view!&quot;);
    }

    return view;
  }

  /**
   * Updates a view for a user.
   * @param user The current user.
   * @param viewId The ID of the view to update.
   * @param body The body of the request.
   * @returns The updated view.
   */
  public async updateView(
    user: CurrentUser,
    viewId: string,
    body: UpdateViewBody,
  ) {
    // Get the view from the database
    const view &#x3D; await this.getViewForUser(user, viewId);
    const { name, description, configuration, isShared } &#x3D; body;

    // If no fields are provided to update, throw an error
    if (
      name &#x3D;&#x3D;&#x3D; undefined &amp;&amp;
      description &#x3D;&#x3D;&#x3D; undefined &amp;&amp;
      configuration &#x3D;&#x3D;&#x3D; undefined &amp;&amp;
      isShared &#x3D;&#x3D;&#x3D; undefined
    ) {
      throw new BadRequestException(
        &quot;At least one field is required to update a view!&quot;,
      );
    }

    // Update the name
    if (name) {
      view.name &#x3D; name;
    }

    // Update the description
    if (description) {
      view.description &#x3D; description;
    }

    // Update the configuration
    if (configuration) {
      view.configuration &#x3D; configuration;
    }

    // Update the shared flag
    if (isShared !&#x3D;&#x3D; undefined) {
      view.isShared &#x3D; isShared;
    }

    // Save the view to the database
    const updatedView &#x3D; await this.viewsRepository.save(view);
    return updatedView;
  }

  /**
   * Deletes a view for a user.
   * @param user The current user.
   * @param viewId The ID of the view to delete.
   * @returns The deleted view.
   */
  public async deleteView(user: CurrentUser, viewId: string) {
    // Get the view from the database
    const view &#x3D; await this.getViewForUser(user, viewId);

    // If the view is not owned by the user, throw an error
    if (view.ownerId !&#x3D;&#x3D; user.sub) {
      throw new ForbiddenException(
        &quot;This view does not belong to you! You can only delete your own views!&quot;,
      );
    }

    // Delete the view from the database
    await this.viewsRepository.softDelete({
      id: view.id,
      uid: view.uid,
      organizationId: user.orgId,
    });

    return view;
  }

  /**
   * Duplicates a view for a user.
   * @param user The current user.
   * @param viewId The ID of the view to duplicate.
   * @returns The duplicated view.
   */
  public async duplicateViewForUser(user: CurrentUser, viewId: string) {
    // Get the view from the database
    const view &#x3D; await this.getViewForUser(user, viewId);

    // Create a new view with the same configuration
    const duplicatedView &#x3D; await this.createViewWithViewType({
      name: &#x60;${view.name} -- Duplicated&#x60;,
      description: view.description,
      viewsTypeId: view.viewsType.uid,
      teamId: view.teamId,
      ownerId: user.sub,
      organizationId: user.orgId,
      isShared: false,
      isPersonal: true,
      configuration: view.configuration,
    });

    return duplicatedView;
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'ViewsService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
