<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >UserAnnotatorService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/users/services/user-annotator.service.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getTimeOffData" >getTimeOffData</a>
                            </li>
                            <li>
                                <a href="#getTimeOffFieldMetadata" >getTimeOffFieldMetadata</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getUserBusinessHoursConfigData" >getUserBusinessHoursConfigData</a>
                            </li>
                            <li>
                                <a href="#getUserBusinessHoursConfigFieldMetadata" >getUserBusinessHoursConfigFieldMetadata</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getUserData" >getUserData</a>
                            </li>
                            <li>
                                <a href="#getUserFieldMetadata" >getUserFieldMetadata</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getUserSkillsData" >getUserSkillsData</a>
                            </li>
                            <li>
                                <a href="#getUserSkillsFieldMetadata" >getUserSkillsFieldMetadata</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(fieldMetadataService: <a href="../injectables/FieldMetadataService.html" target="_self">FieldMetadataService</a>, userRepository: UserRepository, userSkillsRepository: UserSkillsRepository, businessHoursConfigRepository: BusinessHoursConfigRepository, timeOffRepository: TimeOffRepository)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="16" class="link-to-prism">src/users/services/user-annotator.service.ts:16</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>fieldMetadataService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/FieldMetadataService.html" target="_self" >FieldMetadataService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>userRepository</td>
                                                  
                                                        <td>
                                                                    <code>UserRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>userSkillsRepository</td>
                                                  
                                                        <td>
                                                                    <code>UserSkillsRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>businessHoursConfigRepository</td>
                                                  
                                                        <td>
                                                                    <code>BusinessHoursConfigRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>timeOffRepository</td>
                                                  
                                                        <td>
                                                                    <code>TimeOffRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTimeOffData"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>getTimeOffData</b></span>
                        <a href="#getTimeOffData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTimeOffData(userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, relations: string[], organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="128"
                                    class="link-to-prism">src/users/services/user-annotator.service.ts:128</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Get time off data with relations</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>userId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>relations</td>
                                            <td>
                                                        <code>string[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;users.GetTimeOffDataResponse&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTimeOffFieldMetadata"></a>
                    <span class="name">
                        <span ><b>getTimeOffFieldMetadata</b></span>
                        <a href="#getTimeOffFieldMetadata"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getTimeOffFieldMetadata()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="49"
                                    class="link-to-prism">src/users/services/user-annotator.service.ts:49</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Get field metadata for time off</p>
</div>

                        <div class="io-description">
                            <b>Returns : </b>    <code>users.GetTimeOffFieldMetadataResponse</code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getUserBusinessHoursConfigData"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>getUserBusinessHoursConfigData</b></span>
                        <a href="#getUserBusinessHoursConfigData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getUserBusinessHoursConfigData(userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, relations: string[], organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="102"
                                    class="link-to-prism">src/users/services/user-annotator.service.ts:102</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Get business hours config data with relations</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>userId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>relations</td>
                                            <td>
                                                        <code>string[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;users.GetUserBusinessHoursConfigDataResponse&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getUserBusinessHoursConfigFieldMetadata"></a>
                    <span class="name">
                        <span ><b>getUserBusinessHoursConfigFieldMetadata</b></span>
                        <a href="#getUserBusinessHoursConfigFieldMetadata"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getUserBusinessHoursConfigFieldMetadata()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="42"
                                    class="link-to-prism">src/users/services/user-annotator.service.ts:42</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Get field metadata for business hours config</p>
</div>

                        <div class="io-description">
                            <b>Returns : </b>    <code>users.GetUserBusinessHoursConfigFieldMetadataResponse</code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getUserData"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>getUserData</b></span>
                        <a href="#getUserData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getUserData(userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, relations: string[], organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="56"
                                    class="link-to-prism">src/users/services/user-annotator.service.ts:56</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Get user data with relations</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>userId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>relations</td>
                                            <td>
                                                        <code>string[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;users.GetUserDataResponse&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getUserFieldMetadata"></a>
                    <span class="name">
                        <span ><b>getUserFieldMetadata</b></span>
                        <a href="#getUserFieldMetadata"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getUserFieldMetadata()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="28"
                                    class="link-to-prism">src/users/services/user-annotator.service.ts:28</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Get field metadata for users</p>
</div>

                        <div class="io-description">
                            <b>Returns : </b>    <code>users.GetUserFieldMetadataResponse</code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getUserSkillsData"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>getUserSkillsData</b></span>
                        <a href="#getUserSkillsData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getUserSkillsData(userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, relations: string[], organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="78"
                                    class="link-to-prism">src/users/services/user-annotator.service.ts:78</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Get user skills data with relations</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>userId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>relations</td>
                                            <td>
                                                        <code>string[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;users.GetUserSkillsDataResponse&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getUserSkillsFieldMetadata"></a>
                    <span class="name">
                        <span ><b>getUserSkillsFieldMetadata</b></span>
                        <a href="#getUserSkillsFieldMetadata"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getUserSkillsFieldMetadata()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="35"
                                    class="link-to-prism">src/users/services/user-annotator.service.ts:35</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Get field metadata for user skills</p>
</div>

                        <div class="io-description">
                            <b>Returns : </b>    <code>users.GetUserSkillsFieldMetadataResponse</code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Injectable, NotFoundException } from &quot;@nestjs/common&quot;;
import { users } from &quot;@repo/shared-proto&quot;;
import {
  BusinessHoursConfig,
  BusinessHoursConfigRepository,
  TimeOff,
  TimeOffRepository,
  User,
  UserRepository,
  UserSkills,
  UserSkillsRepository,
} from &quot;@repo/thena-platform-entities&quot;;
import { FieldMetadataService } from &quot;../../common/services/field-metadata.service&quot;;

@Injectable()
export class UserAnnotatorService {
  constructor(
    private readonly fieldMetadataService: FieldMetadataService,
    private readonly userRepository: UserRepository,
    private readonly userSkillsRepository: UserSkillsRepository,
    private readonly businessHoursConfigRepository: BusinessHoursConfigRepository,
    private readonly timeOffRepository: TimeOffRepository,
  ) {}

  /**
   * Get field metadata for users
   */
  getUserFieldMetadata(): users.GetUserFieldMetadataResponse {
    return this.fieldMetadataService.getFieldMetadata(User);
  }

  /**
   * Get field metadata for user skills
   */
  getUserSkillsFieldMetadata(): users.GetUserSkillsFieldMetadataResponse {
    return this.fieldMetadataService.getFieldMetadata(UserSkills);
  }

  /**
   * Get field metadata for business hours config
   */
  getUserBusinessHoursConfigFieldMetadata(): users.GetUserBusinessHoursConfigFieldMetadataResponse {
    return this.fieldMetadataService.getFieldMetadata(BusinessHoursConfig);
  }

  /**
   * Get field metadata for time off
   */
  getTimeOffFieldMetadata(): users.GetTimeOffFieldMetadataResponse {
    return this.fieldMetadataService.getFieldMetadata(TimeOff);
  }

  /**
   * Get user data with relations
   */
  async getUserData(
    userId: string,
    relations: string[],
    organizationId: string,
  ): Promise&lt;users.GetUserDataResponse&gt; {
    const user &#x3D; await this.userRepository.findByCondition({
      where: { uid: userId, organizationId },
      relations,
    });

    if (!user) {
      throw new NotFoundException(&quot;User not found&quot;);
    }

    return {
      data: JSON.stringify(user),
    };
  }

  /**
   * Get user skills data with relations
   */
  async getUserSkillsData(
    userId: string,
    relations: string[],
    organizationId: string,
  ): Promise&lt;users.GetUserSkillsDataResponse&gt; {
    const userSkills &#x3D; await this.userSkillsRepository.findByCondition({
      where: {
        user: { uid: userId, organizationId },
      },
      relations: [...relations, &quot;user&quot;],
    });

    if (!userSkills) {
      throw new NotFoundException(&quot;User skills not found&quot;);
    }

    return {
      data: JSON.stringify(userSkills),
    };
  }

  /**
   * Get business hours config data with relations
   */
  async getUserBusinessHoursConfigData(
    userId: string,
    relations: string[],
    organizationId: string,
  ): Promise&lt;users.GetUserBusinessHoursConfigDataResponse&gt; {
    const businessHoursConfig &#x3D;
      await this.businessHoursConfigRepository.findByCondition({
        where: {
          user: { uid: userId },
          organizationId,
        },
        relations: [...relations, &quot;user&quot;],
      });

    if (!businessHoursConfig) {
      throw new NotFoundException(&quot;Business hours config not found&quot;);
    }

    return {
      data: JSON.stringify(businessHoursConfig),
    };
  }

  /**
   * Get time off data with relations
   */
  async getTimeOffData(
    userId: string,
    relations: string[],
    organizationId: string,
  ): Promise&lt;users.GetTimeOffDataResponse&gt; {
    const timeOff &#x3D; await this.timeOffRepository.findByCondition({
      where: {
        user: { uid: userId, organizationId },
      },
      relations: [...relations, &quot;user&quot;],
    });

    if (!timeOff) {
      throw new NotFoundException(&quot;Time off not found&quot;);
    }

    return {
      data: JSON.stringify(timeOff),
    };
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'UserAnnotatorService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
