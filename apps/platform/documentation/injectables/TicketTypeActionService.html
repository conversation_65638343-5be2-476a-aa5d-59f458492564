<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >TicketTypeActionService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/tickets/services/ticket-type.action.service.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createTicketType" >createTicketType</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#deleteTicketType" >deleteTicketType</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#extractUserFromRequest" >extractUserFromRequest</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findAllTicketTypes" >findAllTicketTypes</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findTicketTypeById" >findTicketTypeById</a>
                            </li>
                            <li>
                                <a href="#findTicketTypeByPublicId" >findTicketTypeByPublicId</a>
                            </li>
                            <li>
                                <a href="#findTicketTypesByPublicIds" >findTicketTypesByPublicIds</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateTicketType" >updateTicketType</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#validateAndFetchTeam" >validateAndFetchTeam</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(cachedTicketTypeRepository: CachedTicketTypeRepository, ticketTypeRepository: TicketTypeRepository, usersService: <a href="../injectables/UsersService.html" target="_self">UsersService</a>, teamsService: <a href="../injectables/TeamsService.html" target="_self">TeamsService</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="23" class="link-to-prism">src/tickets/services/ticket-type.action.service.ts:23</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>cachedTicketTypeRepository</td>
                                                  
                                                        <td>
                                                                    <code>CachedTicketTypeRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>ticketTypeRepository</td>
                                                  
                                                        <td>
                                                                    <code>TicketTypeRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>usersService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/UsersService.html" target="_self" >UsersService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>teamsService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/TeamsService.html" target="_self" >TeamsService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createTicketType"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>createTicketType</b></span>
                        <a href="#createTicketType"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createTicketType(createTicketTypeDto: <a href="../classes/CreateTicketTypeDto.html" target="_self">CreateTicketTypeDto</a>, request: <a href="../interfaces/FastifyRequest.html" target="_self">FastifyRequest</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="143"
                                    class="link-to-prism">src/tickets/services/ticket-type.action.service.ts:143</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Creates a new ticket status.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>createTicketTypeDto</td>
                                            <td>
                                                            <code><a href="../classes/CreateTicketTypeDto.html" target="_self" >CreateTicketTypeDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ticket type data to create.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../interfaces/FastifyRequest.html" target="_self" >FastifyRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The created ticket type.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteTicketType"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>deleteTicketType</b></span>
                        <a href="#deleteTicketType"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteTicketType(ticketTypeId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, request: <a href="../interfaces/FastifyRequest.html" target="_self">FastifyRequest</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="220"
                                    class="link-to-prism">src/tickets/services/ticket-type.action.service.ts:220</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Deletes a ticket type.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>ticketTypeId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the ticket type to delete.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../interfaces/FastifyRequest.html" target="_self" >FastifyRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                <p>The deleted ticket type.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="extractUserFromRequest"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>extractUserFromRequest</b></span>
                        <a href="#extractUserFromRequest"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>extractUserFromRequest(request: <a href="../interfaces/FastifyRequest.html" target="_self">FastifyRequest</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="56"
                                    class="link-to-prism">src/tickets/services/ticket-type.action.service.ts:56</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Extracts the user from the request.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../interfaces/FastifyRequest.html" target="_self" >FastifyRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;User&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The user.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAllTicketTypes"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findAllTicketTypes</b></span>
                        <a href="#findAllTicketTypes"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAllTicketTypes(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, teamId?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="91"
                                    class="link-to-prism">src/tickets/services/ticket-type.action.service.ts:91</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds all ticket types for the organization.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>All ticket types for the organization.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findTicketTypeById"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findTicketTypeById</b></span>
                        <a href="#findTicketTypeById"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findTicketTypeById(ticketTypeId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, request: <a href="../interfaces/FastifyRequest.html" target="_self">FastifyRequest</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="122"
                                    class="link-to-prism">src/tickets/services/ticket-type.action.service.ts:122</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds a ticket type by its ID.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>ticketTypeId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the ticket type to find.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../interfaces/FastifyRequest.html" target="_self" >FastifyRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The ticket type.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findTicketTypeByPublicId"></a>
                    <span class="name">
                        <span ><b>findTicketTypeByPublicId</b></span>
                        <a href="#findTicketTypeByPublicId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findTicketTypeByPublicId(publicId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="46"
                                    class="link-to-prism">src/tickets/services/ticket-type.action.service.ts:46</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>publicId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findTicketTypesByPublicIds"></a>
                    <span class="name">
                        <span ><b>findTicketTypesByPublicIds</b></span>
                        <a href="#findTicketTypesByPublicIds"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findTicketTypesByPublicIds(typeIds: Array<string>, organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="40"
                                    class="link-to-prism">src/tickets/services/ticket-type.action.service.ts:40</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds ticket types by their public IDs.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>typeIds</td>
                                            <td>
                                                        <code>Array&lt;string&gt;</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The public IDs of the ticket types to find.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization to find the ticket types in.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                <p>The ticket types.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateTicketType"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>updateTicketType</b></span>
                        <a href="#updateTicketType"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateTicketType(ticketTypeId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, updateTicketTypeDto: <a href="../classes/UpdateTicketTypeDto.html" target="_self">UpdateTicketTypeDto</a>, request: <a href="../interfaces/FastifyRequest.html" target="_self">FastifyRequest</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="189"
                                    class="link-to-prism">src/tickets/services/ticket-type.action.service.ts:189</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Updates a ticket type.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>ticketTypeId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the ticket type to update.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>updateTicketTypeDto</td>
                                            <td>
                                                            <code><a href="../classes/UpdateTicketTypeDto.html" target="_self" >UpdateTicketTypeDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ticket type data to update.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../interfaces/FastifyRequest.html" target="_self" >FastifyRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The updated ticket type.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateAndFetchTeam"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>validateAndFetchTeam</b></span>
                        <a href="#validateAndFetchTeam"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateAndFetchTeam(teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="75"
                                    class="link-to-prism">src/tickets/services/ticket-type.action.service.ts:75</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Validates and fetches a team by its ID.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the team to fetch.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The team.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from &quot;@nestjs/common&quot;;
import {
  CachedTicketTypeRepository,
  TicketTypeRepository,
  User,
} from &quot;@repo/thena-platform-entities&quot;;
import { FastifyRequest } from &quot;fastify&quot;;
import { In } from &quot;typeorm&quot;;
import { CurrentUser } from &quot;../../common/decorators&quot;;
import { TeamsService } from &quot;../../teams/services/teams.service&quot;;
import { UsersService } from &quot;../../users/services/users.service&quot;;
import {
  CreateTicketTypeDto,
  UpdateTicketTypeDto,
} from &quot;../dto/ticket-type.dto&quot;;

@Injectable()
export class TicketTypeActionService {
  constructor(
    // Injected cached repositories
    private cachedTicketTypeRepository: CachedTicketTypeRepository,

    // Injected repositories
    private ticketTypeRepository: TicketTypeRepository,
    private usersService: UsersService,
    private teamsService: TeamsService,
  ) {}

  /**
   * Finds ticket types by their public IDs.
   * @param typeIds The public IDs of the ticket types to find.
   * @param organizationId The ID of the organization to find the ticket types in.
   * @returns The ticket types.
   */
  findTicketTypesByPublicIds(typeIds: Array&lt;string&gt;, organizationId: string) {
    return this.ticketTypeRepository.findAll({
      where: { uid: In(typeIds), organizationId },
    });
  }

  findTicketTypeByPublicId(publicId: string, orgId: string) {
    return this.cachedTicketTypeRepository.findByCondition({
      where: { uid: publicId, organizationId: orgId },
    });
  }

  /**
   * Extracts the user from the request.
   * @returns The user.
   */
  private async extractUserFromRequest(request: FastifyRequest): Promise&lt;User&gt; {
    const userEmail &#x3D; request.user.email;
    if (!userEmail) {
      throw new UnauthorizedException(&quot;User is not authenticated!&quot;);
    }

    const user &#x3D; await this.usersService.findOneByEmail(userEmail);
    if (!user) {
      throw new UnauthorizedException(&quot;User is not authenticated!&quot;);
    }

    return user;
  }

  /**
   * Validates and fetches a team by its ID.
   * @param teamId The ID of the team to fetch.
   * @returns The team.
   */
  private async validateAndFetchTeam(teamId: string, organizationId: string) {
    const team &#x3D; await this.teamsService.findOneByTeamId(
      teamId,
      organizationId,
    );
    if (!team) {
      throw new NotFoundException(&quot;Team not found!&quot;);
    }

    return team;
  }

  /**
   * Finds all ticket types for the organization.
   * @returns All ticket types for the organization.
   */
  async findAllTicketTypes(user: CurrentUser, teamId?: string) {
    // Build the where clause
    const whereClause: Record&lt;string, string&gt; &#x3D; {
      organizationId: user.orgId,
    };

    // If the team ID is provided, filter by team ID
    if (teamId?.trim()) {
      const team &#x3D; await this.validateAndFetchTeam(teamId, user.orgId);
      whereClause.teamId &#x3D; team.id;

      // If the team has a parent team then we&#x27;ll use the parent team&#x27;s statuses
      if (team.parentTeamId) {
        whereClause.teamId &#x3D; team.parentTeamId;
      }
    }

    // Find all ticket statuses for the organization
    const ticketTypes &#x3D; await this.ticketTypeRepository.findAll({
      where: whereClause,
      relations: [&quot;organization&quot;, &quot;team&quot;],
    });

    return ticketTypes;
  }

  /**
   * Finds a ticket type by its ID.
   * @param ticketTypeId The ID of the ticket type to find.
   * @returns The ticket type.
   */
  async findTicketTypeById(ticketTypeId: string, request: FastifyRequest) {
    // Get the organization ID from the user object attached to the request
    const user &#x3D; await this.extractUserFromRequest(request);

    const ticketType &#x3D; await this.cachedTicketTypeRepository.findByCondition({
      where: { uid: ticketTypeId, organizationId: user.organizationId },
      relations: [&quot;organization&quot;, &quot;team&quot;],
    });

    if (!ticketType) {
      throw new NotFoundException(&quot;Ticket type not found!&quot;);
    }

    return ticketType;
  }

  /**
   * Creates a new ticket status.
   * @param createTicketTypeDto The ticket type data to create.
   * @returns The created ticket type.
   */
  async createTicketType(
    createTicketTypeDto: CreateTicketTypeDto,
    request: FastifyRequest,
  ) {
    // Get the user from the request
    const user &#x3D; await this.extractUserFromRequest(request);

    // Validate and fetch the team
    const team &#x3D; await this.validateAndFetchTeam(
      createTicketTypeDto.teamId,
      user.organizationId,
    );

    // If the team has a parent team then we&#x27;ll throw since child teams cannot have custom types
    if (team.parentTeamId) {
      throw new BadRequestException(&quot;You cannot create types for a sub-teams!&quot;);
    }

    // Get the organization ID from the user object
    const orgId &#x3D; user.organizationId;

    // Create the ticket type
    const newTicketType &#x3D; this.cachedTicketTypeRepository.create({
      name: createTicketTypeDto.name,
      color: createTicketTypeDto.color,
      icon: createTicketTypeDto.icon,
      autoAssign: createTicketTypeDto.autoAssign,
      isActive: createTicketTypeDto.isActive,
      organizationId: orgId,
      teamId: team.id,
    });

    // Save the new ticket type
    const ticketType &#x3D; await this.cachedTicketTypeRepository.save(
      newTicketType,
    );

    return ticketType;
  }

  /**
   * Updates a ticket type.
   * @param ticketTypeId The ID of the ticket type to update.
   * @param updateTicketTypeDto The ticket type data to update.
   * @returns The updated ticket type.
   */
  async updateTicketType(
    ticketTypeId: string,
    updateTicketTypeDto: UpdateTicketTypeDto,
    request: FastifyRequest,
  ) {
    // Get the user email from the request
    const user &#x3D; await this.extractUserFromRequest(request);

    // Find the ticket status by its ID
    const ticketType &#x3D; await this.cachedTicketTypeRepository.findByCondition({
      where: { uid: ticketTypeId, organizationId: user.organizationId },
    });

    if (!ticketType) {
      throw new NotFoundException(&quot;Ticket type not found!&quot;);
    }

    // Update the ticket type
    const updatedTicketType &#x3D; await this.cachedTicketTypeRepository.save({
      ...ticketType,
      ...updateTicketTypeDto,
    });

    return updatedTicketType;
  }

  /**
   * Deletes a ticket type.
   * @param ticketTypeId The ID of the ticket type to delete.
   * @returns The deleted ticket type.
   */
  async deleteTicketType(ticketTypeId: string, request: FastifyRequest) {
    // Get the user from the request
    const user &#x3D; await this.extractUserFromRequest(request);

    // Find the ticket status by its ID
    const ticketType &#x3D; await this.cachedTicketTypeRepository.findByCondition({
      where: { uid: ticketTypeId, organizationId: user.organizationId },
    });

    if (!ticketType) {
      throw new NotFoundException(&quot;Ticket type not found!&quot;);
    }

    // Delete the ticket type
    await this.cachedTicketTypeRepository.remove(ticketType);
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'TicketTypeActionService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
