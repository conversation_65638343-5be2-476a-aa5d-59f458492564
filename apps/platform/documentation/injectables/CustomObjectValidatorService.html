<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >CustomObjectValidatorService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/custom-object/validators/custom-object.validator.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#validateCreatePayload" >validateCreatePayload</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#validateUpdatePayload" >validateUpdatePayload</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(customObjectRepository: CustomObjectRepository)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="10" class="link-to-prism">src/custom-object/validators/custom-object.validator.ts:10</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>customObjectRepository</td>
                                                  
                                                        <td>
                                                                    <code>CustomObjectRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateCreatePayload"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>validateCreatePayload</b></span>
                        <a href="#validateCreatePayload"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateCreatePayload(orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, createCustomObjectDto: <a href="../classes/CreateCustomObjectDto.html" target="_self">CreateCustomObjectDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="13"
                                    class="link-to-prism">src/custom-object/validators/custom-object.validator.ts:13</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>createCustomObjectDto</td>
                                            <td>
                                                            <code><a href="../classes/CreateCustomObjectDto.html" target="_self" >CreateCustomObjectDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateUpdatePayload"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>validateUpdatePayload</b></span>
                        <a href="#validateUpdatePayload"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateUpdatePayload(orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, objects: <a href="../classes/UpdateCustomObjectMetaDTO.html" target="_self">UpdateCustomObjectMetaDTO[]</a>, teamIds: string[])</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="42"
                                    class="link-to-prism">src/custom-object/validators/custom-object.validator.ts:42</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>objects</td>
                                            <td>
                                                            <code><a href="../classes/UpdateCustomObjectMetaDTO.html" target="_self" >UpdateCustomObjectMetaDTO[]</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>teamIds</td>
                                            <td>
                                                        <code>string[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { BadRequestException, Injectable } from &quot;@nestjs/common&quot;;
import { CustomObjectRepository } from &quot;@repo/thena-platform-entities&quot;;
import { ILike, In } from &quot;typeorm&quot;;
import {
  CreateCustomObjectDto,
  UpdateCustomObjectMetaDTO,
} from &quot;../dto/custom-object.dto&quot;;

@Injectable()
export class CustomObjectValidatorService {
  constructor(private customObjectRepository: CustomObjectRepository) {}

  async validateCreatePayload(
    orgId: string,
    createCustomObjectDto: CreateCustomObjectDto,
  ) {
    const customObjects &#x3D; await this.customObjectRepository.findAll({
      where: {
        organizationId: orgId,
        name: ILike(createCustomObjectDto.name.toLocaleLowerCase()),
        isDeleted: false,
      },
    });

    if (createCustomObjectDto.teamId &amp;&amp; customObjects.length &gt; 0) {
      for (const customObject of customObjects) {
        if (customObject.teamId &#x3D;&#x3D;&#x3D; createCustomObjectDto.teamId) {
          throw new BadRequestException(
            &#x60;Custom object with the name &quot;${createCustomObjectDto.name}&quot; already exists in the team.&#x60;,
          );
        }
      }
    }

    if (customObjects.length &gt; 0) {
      throw new BadRequestException(
        &#x60;Custom object with the name &quot;${createCustomObjectDto.name}&quot; already exists in your organization.&#x60;,
      );
    }
  }

  async validateUpdatePayload(
    orgId: string,
    objects: UpdateCustomObjectMetaDTO[],
    teamIds: string[],
  ) {
    const names &#x3D; (objects || [])
      .map((object) &#x3D;&gt; object.updates?.name?.toLocaleLowerCase())
      .filter(Boolean);

    const namesSet &#x3D; new Set(names);
    if (namesSet.size !&#x3D;&#x3D; names.length) {
      throw new BadRequestException(
        &quot;Duplicate custom object names are not allowed&quot;,
      );
    }

    const objectIds &#x3D; (objects || []).map((object) &#x3D;&gt; object.id);
    const objectIdsSet &#x3D; new Set(objectIds);
    if (objectIdsSet.size !&#x3D;&#x3D; objectIds.length) {
      throw new BadRequestException(
        &quot;Duplicate custom object IDs found in the payload&quot;,
      );
    }

    const customObjects &#x3D; await this.customObjectRepository.findAll({
      where: {
        organizationId: orgId,
        uid: In(objectIds),
        isDeleted: false,
      },
    });

    const foundCustomObjectIds &#x3D; customObjects.map((obj) &#x3D;&gt; obj.uid);

    if (foundCustomObjectIds.length !&#x3D;&#x3D; objectIds.length) {
      const missingIds &#x3D; objectIds.filter(
        (id) &#x3D;&gt; !foundCustomObjectIds.includes(id),
      );

      throw new BadRequestException(
        &#x60;One or more custom objects not found in the database: ${missingIds.join(
          &quot;, &quot;,
        )}&#x60;,
      );
    }

    const modifiedObjects &#x3D; [];
    for (const customObject of customObjects) {
      const existingObject &#x3D; objects.find((obj) &#x3D;&gt; obj.id &#x3D;&#x3D;&#x3D; customObject.uid);
      if (customObject.teamId &amp;&amp; !teamIds.includes(customObject.teamId)) {
        throw new BadRequestException(
          &#x60;You are trying to update object of a team that you are not a part of, objectId: ${customObject.uid}&#x60;,
        );
      }

      if (existingObject.version !&#x3D;&#x3D; customObject.version) {
        modifiedObjects.push(customObject.uid);
      }
    }

    if (modifiedObjects.length &gt; 0) {
      throw new BadRequestException(
        &#x60;Some objects have been modified, Please try updating the latest version. Modified objects: ${modifiedObjects.join(
          &quot;, &quot;,
        )}&#x60;,
      );
    }

    if (names.length &gt; 0) {
      const objectsWithSameName &#x3D; await this.customObjectRepository.findAll({
        where: {
          organizationId: orgId,
          name: In(names),
          isDeleted: false,
        },
      });

      if (objectsWithSameName.length &gt; 0) {
        const alreadyExists &#x3D; objectsWithSameName.map((obj) &#x3D;&gt; obj.name);
        throw new BadRequestException(
          &#x60;Custom object with the name &quot;${alreadyExists.join(
            &quot;, &quot;,
          )}&quot; already exists in your organization.&#x60;,
        );
      }
    }
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'CustomObjectValidatorService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
