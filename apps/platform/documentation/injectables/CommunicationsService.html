<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >CommunicationsService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/communications/services/communications.service.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createCommentOnAnEntity" >createCommentOnAnEntity</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getCommentForAnEntityByUserType" >getCommentForAnEntityByUserType</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getCommentsForAnEntity" >getCommentsForAnEntity</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getEntity" >getEntity</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#getEntityProperties" >getEntityProperties</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#isValidParentComment" >isValidParentComment</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(ticketService: <a href="../injectables/TicketsService.html" target="_self">TicketsService</a>, accountActivityActionService: <a href="../injectables/AccountActivityActionService.html" target="_self">AccountActivityActionService</a>, accountNoteActionService: <a href="../injectables/AccountNoteActionService.html" target="_self">AccountNoteActionService</a>, accountTaskActionService: <a href="../injectables/AccountTaskActionService.html" target="_self">AccountTaskActionService</a>, snsPublishQueue: Queue, commentsActionService: <a href="../injectables/CommentsActionService.html" target="_self">CommentsActionService</a>, sqsProducerService: <a href="../s/SQSProducerService.html" target="_self">SQSProducerService</a>, snsPublisherService: <a href="../s/SNSPublisherService.html" target="_self">SNSPublisherService</a>, transactionService: TransactionService, activitiesService: <a href="../injectables/ActivitiesService.html" target="_self">ActivitiesService</a>, configService: <a href="../injectables/ConfigService.html" target="_self">ConfigService</a>, logger: ILogger, storageService: <a href="../injectables/StorageService.html" target="_self">StorageService</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="49" class="link-to-prism">src/communications/services/communications.service.ts:49</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>ticketService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/TicketsService.html" target="_self" >TicketsService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>accountActivityActionService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/AccountActivityActionService.html" target="_self" >AccountActivityActionService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>accountNoteActionService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/AccountNoteActionService.html" target="_self" >AccountNoteActionService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>accountTaskActionService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/AccountTaskActionService.html" target="_self" >AccountTaskActionService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>snsPublishQueue</td>
                                                  
                                                        <td>
                                                                    <code>Queue</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>commentsActionService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/CommentsActionService.html" target="_self" >CommentsActionService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>sqsProducerService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../miscellaneous/variables.html#SQSProducerService" target="_self" >SQSProducerService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>snsPublisherService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../miscellaneous/variables.html#SNSPublisherService" target="_self" >SNSPublisherService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>transactionService</td>
                                                  
                                                        <td>
                                                                    <code>TransactionService</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>activitiesService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/ActivitiesService.html" target="_self" >ActivitiesService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>configService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/ConfigService.html" target="_self" >ConfigService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>logger</td>
                                                  
                                                        <td>
                                                                    <code>ILogger</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>storageService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/StorageService.html" target="_self" >StorageService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createCommentOnAnEntity"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>createCommentOnAnEntity</b></span>
                        <a href="#createCommentOnAnEntity"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createCommentOnAnEntity(entity: T, entityType: CommentEntityTypes, createCommentDto: <a href="../classes/CreateCommentDto.html" target="_self">CreateCommentDto</a>, currentUser: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="170"
                                    class="link-to-prism">src/communications/services/communications.service.ts:170</a></div>
                        </td>
                    </tr>

                    <tr>
                        <td class="col-md-4">
                            <b>Type parameters :</b>
                            <ul class="type-parameters">
                                    <li>T</li>
                            </ul>
                        </td>
                    </tr>

            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Create a comment on an entity</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>entity</td>
                                            <td>
                                                        <code>T</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ticket or account note</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>entityType</td>
                                            <td>
                                                        <code>CommentEntityTypes</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>createCommentDto</td>
                                            <td>
                                                            <code><a href="../classes/CreateCommentDto.html" target="_self" >CreateCommentDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The comment data</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>currentUser</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The newly created comment</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getCommentForAnEntityByUserType"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>getCommentForAnEntityByUserType</b></span>
                        <a href="#getCommentForAnEntityByUserType"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getCommentForAnEntityByUserType(entity: T, entityType: CommentEntityTypes, getCommentsQuery: <a href="../classes/GetCommentByUserTypeQuery.html" target="_self">GetCommentByUserTypeQuery</a>, currentUser: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="389"
                                    class="link-to-prism">src/communications/services/communications.service.ts:389</a></div>
                        </td>
                    </tr>

                    <tr>
                        <td class="col-md-4">
                            <b>Type parameters :</b>
                            <ul class="type-parameters">
                                    <li>T</li>
                            </ul>
                        </td>
                    </tr>

            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Get comments for a ticket</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>entity</td>
                                            <td>
                                                        <code>T</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>entityType</td>
                                            <td>
                                                        <code>CommentEntityTypes</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>getCommentsQuery</td>
                                            <td>
                                                            <code><a href="../classes/GetCommentByUserTypeQuery.html" target="_self" >GetCommentByUserTypeQuery</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The query for getting comments</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>currentUser</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The comments and the total number of comments</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getCommentsForAnEntity"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>getCommentsForAnEntity</b></span>
                        <a href="#getCommentsForAnEntity"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getCommentsForAnEntity(entity: T, entityType: CommentEntityTypes, getCommentsQuery: <a href="../classes/GetCommentQuery.html" target="_self">GetCommentQuery</a>, currentUser: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="351"
                                    class="link-to-prism">src/communications/services/communications.service.ts:351</a></div>
                        </td>
                    </tr>

                    <tr>
                        <td class="col-md-4">
                            <b>Type parameters :</b>
                            <ul class="type-parameters">
                                    <li>T</li>
                            </ul>
                        </td>
                    </tr>

            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Get comments for an entity</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>entity</td>
                                            <td>
                                                        <code>T</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ticket or account note</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>entityType</td>
                                            <td>
                                                        <code>CommentEntityTypes</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>getCommentsQuery</td>
                                            <td>
                                                            <code><a href="../classes/GetCommentQuery.html" target="_self" >GetCommentQuery</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The query for getting comments</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>currentUser</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The comments and the total number of comments</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getEntity"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>getEntity</b></span>
                        <a href="#getEntity"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getEntity(currentUser: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, entityType: CommentEntityTypes, entityId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="74"
                                    class="link-to-prism">src/communications/services/communications.service.ts:74</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>currentUser</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>entityType</td>
                                            <td>
                                                        <code>CommentEntityTypes</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>entityId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getEntityProperties"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>getEntityProperties</b></span>
                        <a href="#getEntityProperties"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getEntityProperties(entity: T, entityType: CommentEntityTypes)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="109"
                                    class="link-to-prism">src/communications/services/communications.service.ts:109</a></div>
                        </td>
                    </tr>

                    <tr>
                        <td class="col-md-4">
                            <b>Type parameters :</b>
                            <ul class="type-parameters">
                                    <li>T</li>
                            </ul>
                        </td>
                    </tr>

            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Helper function to get the properties of an entity</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>entity</td>
                                            <td>
                                                        <code>T</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The entity</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>entityType</td>
                                            <td>
                                                        <code>CommentEntityTypes</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>{ ticketId: any; teamId: any; accountActivityId?: undefined; accountId?: undefined; accountNoteId?: undefined; accountTaskId?: undefined; } | { accountActivityId: any; accountId: any; ticketId?: undefined; teamId?: undefined; accountNoteId?: undefined; accountTaskId?: undefined; } | { ...; } | { ...; }</code>

                        </div>
                            <div class="io-description">
                                <p>The properties of the entity</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isValidParentComment"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>isValidParentComment</b></span>
                        <a href="#isValidParentComment"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>isValidParentComment(parentComment: Comment, entity: T, entityType: CommentEntityTypes)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="152"
                                    class="link-to-prism">src/communications/services/communications.service.ts:152</a></div>
                        </td>
                    </tr>

                    <tr>
                        <td class="col-md-4">
                            <b>Type parameters :</b>
                            <ul class="type-parameters">
                                    <li>T</li>
                            </ul>
                        </td>
                    </tr>

            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Helper function to check if a parent comment is valid</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>parentComment</td>
                                            <td>
                                                        <code>Comment</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The parent comment</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>entity</td>
                                            <td>
                                                        <code>T</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The entity</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>entityType</td>
                                            <td>
                                                        <code>CommentEntityTypes</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                        </div>
                            <div class="io-description">
                                <p>Whether the parent comment is valid</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { InjectQueue } from &quot;@nestjs/bullmq&quot;;
import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from &quot;@nestjs/common&quot;;
import {
  EnforcedMessageAttributes,
  SQSProducerService,
} from &quot;@repo/nestjs-commons/aws-utils/sqs&quot;;
import { ILogger } from &quot;@repo/nestjs-commons/logger&quot;;
import { SNSPublisherService } from &quot;@repo/thena-eventbridge&quot;;
import {
  AccountActivity,
  AccountNote,
  AccountTask,
  AuditLog,
  AuditLogEntityType,
  AuditLogOp,
  AuditLogVisibility,
  Comment,
  CommentEntityTypes,
  Ticket,
  TransactionService,
} from &quot;@repo/thena-platform-entities&quot;;
import { Queue } from &quot;bullmq&quot;;
import { DeepPartial } from &quot;typeorm&quot;;
import { v4 as uuidv4 } from &quot;uuid&quot;;
import { AccountActivityActionService } from &quot;../../accounts/services/account-activity.action.service&quot;;
import { AccountNoteActionService } from &quot;../../accounts/services/account-note.action.service&quot;;
import { AccountTaskActionService } from &quot;../../accounts/services/account-task.action.service&quot;;
import { ActivitiesService } from &quot;../../activities/services/activities.service&quot;;
import { CurrentUser } from &quot;../../common/decorators&quot;;
import { CreateCommentDto } from &quot;../../common/dto/comments.dto&quot;;
import { ConfigService } from &quot;../../config/config.service&quot;;
import { QueueNames } from &quot;../../constants/queue.constants&quot;;
import { StorageService } from &quot;../../storage/services/storage-service&quot;;
import { TicketsService } from &quot;../../tickets/services/tickets.service&quot;;
import {
  GetCommentByUserTypeQuery,
  GetCommentQuery,
} from &quot;../dto/comment.queries&quot;;
import { CommentOp } from &quot;../utils&quot;;
import { CommentsActionService } from &quot;./comments.action.service&quot;;

@Injectable()
export class CommunicationsService {
  constructor(
    @Inject(forwardRef(() &#x3D;&gt; TicketsService))
    private readonly ticketService: TicketsService,

    private readonly accountActivityActionService: AccountActivityActionService,
    private readonly accountNoteActionService: AccountNoteActionService,
    private readonly accountTaskActionService: AccountTaskActionService,

    @InjectQueue(QueueNames.COMMENT_SNS_PUBLISHER)
    private readonly snsPublishQueue: Queue,

    private readonly commentsActionService: CommentsActionService, // private readonly ticketsService: TicketsService,
    @Inject(&quot;COMMUNICATIONS_SQS_PRODUCER&quot;)
    private readonly sqsProducerService: SQSProducerService,
    @Inject(&quot;COMMUNICATIONS_SNS_PUBLISHER&quot;)
    private readonly snsPublisherService: SNSPublisherService,
    private readonly transactionService: TransactionService,
    private readonly activitiesService: ActivitiesService,
    private readonly configService: ConfigService,
    @Inject(&quot;CustomLogger&quot;)
    private readonly logger: ILogger,
    private readonly storageService: StorageService,
  ) {}

  async getEntity(
    currentUser: CurrentUser,
    entityType: CommentEntityTypes,
    entityId: string,
  ) {
    switch (entityType) {
      case CommentEntityTypes.TICKET: {
        return await this.ticketService.getTicketById(
          entityId,
          currentUser.orgId,
        );
      }
      case CommentEntityTypes.ACCOUNT_NOTE: {
        return await this.accountNoteActionService.findAccountNote(
          entityId,
          currentUser.sub,
        );
      }
      case CommentEntityTypes.ACCOUNT_TASK: {
        return await this.accountTaskActionService.findAccountTask(entityId);
      }
      case CommentEntityTypes.ACCOUNT_ACTIVITY: {
        return await this.accountActivityActionService.findAccountActivity(
          entityId,
        );
      }
    }
  }

  /**
   * Helper function to get the properties of an entity
   *
   * @param entity The entity
   * @returns The properties of the entity
   */
  private getEntityProperties&lt;T extends { id: string; uid: string }&gt;(
    entity: T,
    entityType: CommentEntityTypes,
  ) {
    switch (entityType) {
      case CommentEntityTypes.TICKET: {
        const ticket &#x3D; entity as unknown as Ticket;
        return {
          ticketId: ticket.id,
          teamId: ticket.teamId,
        };
      }
      case CommentEntityTypes.ACCOUNT_ACTIVITY: {
        const accountActivity &#x3D; entity as unknown as AccountActivity;
        return {
          accountActivityId: accountActivity.id,
          accountId: accountActivity.accountId,
        };
      }
      case CommentEntityTypes.ACCOUNT_NOTE: {
        const accountNote &#x3D; entity as unknown as AccountNote;
        return {
          accountNoteId: accountNote.id,
          accountId: accountNote.accountId,
        };
      }
      case CommentEntityTypes.ACCOUNT_TASK: {
        const accountTask &#x3D; entity as unknown as AccountTask;
        return {
          accountTaskId: accountTask.id,
          accountId: accountTask.accountId,
        };
      }
    }
  }

  /**
   * Helper function to check if a parent comment is valid
   *
   * @param parentComment The parent comment
   * @param entity The entity
   * @returns Whether the parent comment is valid
   */
  private isValidParentComment&lt;T extends { id: string; uid: string }&gt;(
    parentComment: Comment,
    entity: T,
    entityType: CommentEntityTypes,
  ): boolean {
    const entityProperties &#x3D; this.getEntityProperties(entity, entityType);
    return Object.entries(entityProperties).every(
      ([key, value]) &#x3D;&gt; parentComment?.[key] &amp;&amp; parentComment[key] &#x3D;&#x3D;&#x3D; value,
    );
  }

  /**
   * Create a comment on an entity
   * @param entity The ticket or account note
   * @param createCommentDto The comment data
   * @param currentUser The current user
   * @returns The newly created comment
   */
  async createCommentOnAnEntity&lt;T extends { id: string; uid: string }&gt;(
    entity: T,
    entityType: CommentEntityTypes,
    createCommentDto: CreateCommentDto,
    currentUser: CurrentUser,
  ) {
    // Check if the entity exists
    if (!entity) {
      throw new NotFoundException(&quot;Resource not found&quot;);
    }
    const orgId &#x3D; currentUser.orgId;

    const entityProperties &#x3D; this.getEntityProperties(entity, entityType);

    // Get the parent comment if it exists
    let parentComment: Comment | null;
    if (createCommentDto.parentCommentId) {
      const parentCommentId &#x3D; createCommentDto.parentCommentId;
      parentComment &#x3D; await this.commentsActionService.getPopulatedCommentById(
        parentCommentId,
        orgId,
      );

      // If the parent comment is not found, throw an error
      if (!parentComment) {
        throw new NotFoundException(&quot;Parent comment not found!&quot;);
      }

      // If the parent comment is deleted, throw an error
      if (parentComment.deletedAt) {
        throw new BadRequestException(
          &quot;This comment was deleted and therefore can no longer be replied to!&quot;,
        );
      }

      // Check if parent comment belongs to the same entity
      const isValidParent &#x3D; this.isValidParentComment(
        parentComment,
        entity,
        entityType,
      );

      if (!isValidParent) {
        throw new BadRequestException(
          &#x60;This comment is not on the same resource as the parent comment!&#x60;,
        );
      }

      // If the parent comment already has a parent comment, throw an error
      if (parentComment.parentCommentId) {
        throw new BadRequestException(&quot;Nested comments are not supported!&quot;);
      }
    }

    // Create the comment
    const newComment &#x3D; await this.transactionService.runInTransaction(
      async (txnContext) &#x3D;&gt; {
        // Create the comment
        const comment &#x3D; await this.commentsActionService.createComment({
          ...entityProperties,
          organizationId: orgId,
          source: createCommentDto.metadata?.source,
          content: createCommentDto.content,
          commentVisibility: createCommentDto.commentVisibility,
          commentType: createCommentDto.commentType,
          authorId: currentUser.sub,
          parentCommentId: parentComment?.id,
          commentThreadName: createCommentDto.threadName,
          metadata: createCommentDto.metadata,
        });

        if (createCommentDto.attachmentUrls?.length &gt; 0) {
          const attachments &#x3D; await this.storageService.attachFilesToEntity(
            &quot;comment&quot;,
            comment.id,
            createCommentDto.attachmentUrls,
            currentUser.orgId,
            &#x60;${currentUser.orgUid}/comments/${entityType}-${entity.uid}/${comment.uid}&#x60;,
          );

          await this.commentsActionService.updateCommentAttachments(
            comment,
            attachments,
          );
        }

        // Create the audit log
        const auditLog: DeepPartial&lt;AuditLog&gt; &#x3D; {
          organization: { id: currentUser.orgId },
          activityPerformedBy: { id: currentUser.sub },
          entityId: entity.id,
          entityUid: entity.uid,
          entityType: entityType as string as AuditLogEntityType,
          op: AuditLogOp.CREATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: &#x60;A new comment was created on ${entityType} ${entity.id}&#x60;,
          description: &#x60;A new comment was created by user ${currentUser.email} on ${entityType} ${entity.id}&#x60;,
          ...(entityProperties.teamId &amp;&amp; {
            team: { id: entityProperties.teamId },
          }),
        };

        // Emit the comment created event
        const message &#x3D; JSON.stringify({
          ...comment,
          eventName: &quot;COMMENT_CREATED&quot;,
          entityType: &quot;comment&quot;,
          entityId: comment.uid,
          organization: {
            uid: currentUser.orgUid,
            id: currentUser.orgId,
          },
          timestamp: new Date().getTime(),
          ...(entityProperties.ticketId &amp;&amp; {
            ticketUid: entity.uid,
            team: {
              uid: (entity as unknown as Ticket).team?.uid,
              id: (entity as unknown as Ticket).team?.id,
            },
          }),
          ...(entityProperties.accountNoteId &amp;&amp; {
            accountNoteUid: entity.uid,
          }),
          ...(entityProperties.accountActivityId &amp;&amp; {
            accountActivityUid: entity.uid,
          }),
          ...(entityProperties.accountTaskId &amp;&amp; {
            accountTaskUid: entity.uid,
          }),
        });

        const messageAttributes: EnforcedMessageAttributes &#x3D; {
          event_name: {
            DataType: &quot;String&quot;,
            StringValue: &quot;COMMENT_CREATED&quot;,
          },
          event_id: {
            DataType: &quot;String&quot;,
            StringValue: uuidv4(),
          },
          event_identifier_id: {
            DataType: &quot;String&quot;,
            StringValue: comment.id,
          },
        };

        // TODO: Sachin to remove this and consume this from SNS via a SQS Subscription
        await this.sqsProducerService.sendMessage(message, messageAttributes);

        // Add this to the Bull Queue
        await this.snsPublishQueue.add(
          QueueNames.COMMENT_SNS_PUBLISHER,
          {
            comment: comment.uid,
            user: currentUser,
            eventType: CommentOp.CREATED,
            entityType: entityType,
          },
          {
            attempts: 3,
            backoff: { type: &quot;exponential&quot;, delay: 1000 },
          },
        );

        // Record the audit log
        await this.activitiesService.recordAuditLog(auditLog, txnContext);

        return comment;
      },
    );

    return newComment;
  }

  /**
   * Get comments for an entity
   * @param entity The ticket or account note
   * @param getCommentsQuery The query for getting comments
   * @param currentUser The current user
   * @returns The comments and the total number of comments
   */
  async getCommentsForAnEntity&lt;T extends { id: string; uid: string }&gt;(
    entity: T,
    entityType: CommentEntityTypes,
    getCommentsQuery: GetCommentQuery,
    currentUser: CurrentUser,
  ) {
    // Check if the entity exists
    if (!entity) {
      throw new NotFoundException(&quot;Resource not found&quot;);
    }

    const { page, limit } &#x3D; getCommentsQuery;

    const queryPage &#x3D; page ?? 0;
    const queryLimit &#x3D; Math.min(limit ?? 10, 100);

    const entityProperties &#x3D; this.getEntityProperties(entity, entityType);

    // Get the comments
    const comments &#x3D; await this.commentsActionService.getComments(
      {
        ...entityProperties,
        page: queryPage,
        limit: queryLimit,
      },
      currentUser,
    );

    return comments;
  }

  /**
   * Get comments for a ticket
   * @param ticket The ticket
   * @param getCommentsQuery The query for getting comments
   * @param currentUser The current user
   * @returns The comments and the total number of comments
   */
  async getCommentForAnEntityByUserType&lt;T extends { id: string; uid: string }&gt;(
    entity: T,
    entityType: CommentEntityTypes,
    getCommentsQuery: GetCommentByUserTypeQuery,
    currentUser: CurrentUser,
  ) {
    // Check if the ticket exists
    if (!entity) throw new NotFoundException(&quot;Ticket not found&quot;);

    const entityProperties &#x3D; this.getEntityProperties(entity, entityType);

    const { userType } &#x3D; getCommentsQuery;

    // Get the comments on the ticket
    const comment &#x3D; await this.commentsActionService.getCommentByUserType(
      {
        ...entityProperties,
        organizationId: currentUser.orgId,
        userType,
      } as GetCommentByUserTypeQuery &amp; {
        organizationId: string;
        teamId?: string;
        ticketId?: string;
        accountId?: string;
        accountNoteId?: string;
        accountActivityId?: string;
        accountTaskId?: string;
      },
      currentUser,
    );

    return comment;
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'CommunicationsService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
