<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >TicketAnnotatorService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/tickets/services/ticket-annotator.service.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#getEntityFieldTypes" class="deprecated-name">getEntityFieldTypes</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getTicketData" >getTicketData</a>
                            </li>
                            <li>
                                <a href="#getTicketFieldMetadata" >getTicketFieldMetadata</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getTicketPriorityData" >getTicketPriorityData</a>
                            </li>
                            <li>
                                <a href="#getTicketPriorityFieldMetadata" >getTicketPriorityFieldMetadata</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getTicketStatusData" >getTicketStatusData</a>
                            </li>
                            <li>
                                <a href="#getTicketStatusFieldMetadata" >getTicketStatusFieldMetadata</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getTicketTypeData" >getTicketTypeData</a>
                            </li>
                            <li>
                                <a href="#getTicketTypeFieldMetadata" >getTicketTypeFieldMetadata</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(dataSource: DataSource, ticketRepository: TicketRepository, ticketStatusRepository: TicketStatusRepository, ticketTypeRepository: TicketTypeRepository, ticketPriorityRepository: TicketPriorityRepository, fieldMetadataService: <a href="../injectables/FieldMetadataService.html" target="_self">FieldMetadataService</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="36" class="link-to-prism">src/tickets/services/ticket-annotator.service.ts:36</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>dataSource</td>
                                                  
                                                        <td>
                                                                    <code>DataSource</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>ticketRepository</td>
                                                  
                                                        <td>
                                                                    <code>TicketRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>ticketStatusRepository</td>
                                                  
                                                        <td>
                                                                    <code>TicketStatusRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>ticketTypeRepository</td>
                                                  
                                                        <td>
                                                                    <code>TicketTypeRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>ticketPriorityRepository</td>
                                                  
                                                        <td>
                                                                    <code>TicketPriorityRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>fieldMetadataService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/FieldMetadataService.html" target="_self" >FieldMetadataService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getEntityFieldTypes"></a>
                    <span class="name">
                        <span class="deprecated-name"><b>getEntityFieldTypes</b></span>
                        <a href="#getEntityFieldTypes"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4 deprecated">
                    
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getEntityFieldTypes(slaEnabled: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank">boolean</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="50"
                                    class="link-to-prism">src/tickets/services/ticket-annotator.service.ts:50</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>slaEnabled</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../interfaces/FieldTypeInfo.html" target="_self" >FieldTypeInfo[]</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTicketData"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>getTicketData</b></span>
                        <a href="#getTicketData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTicketData(ticketId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, relations: string[], organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="156"
                                    class="link-to-prism">src/tickets/services/ticket-annotator.service.ts:156</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Get ticket data with relations</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>ticketId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>relations</td>
                                            <td>
                                                        <code>string[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;tickets.GetTicketDataResponse&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTicketFieldMetadata"></a>
                    <span class="name">
                        <span ><b>getTicketFieldMetadata</b></span>
                        <a href="#getTicketFieldMetadata"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getTicketFieldMetadata()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="128"
                                    class="link-to-prism">src/tickets/services/ticket-annotator.service.ts:128</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Get field metadata for tickets</p>
</div>

                        <div class="io-description">
                            <b>Returns : </b>    <code>tickets.GetTicketFieldMetadataResponse</code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTicketPriorityData"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>getTicketPriorityData</b></span>
                        <a href="#getTicketPriorityData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTicketPriorityData(priorityId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, relations: string[], organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="222"
                                    class="link-to-prism">src/tickets/services/ticket-annotator.service.ts:222</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Get ticket priority data with relations</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>priorityId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>relations</td>
                                            <td>
                                                        <code>string[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;tickets.GetTicketPriorityDataResponse&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTicketPriorityFieldMetadata"></a>
                    <span class="name">
                        <span ><b>getTicketPriorityFieldMetadata</b></span>
                        <a href="#getTicketPriorityFieldMetadata"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getTicketPriorityFieldMetadata()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="149"
                                    class="link-to-prism">src/tickets/services/ticket-annotator.service.ts:149</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Get field metadata for ticket priority</p>
</div>

                        <div class="io-description">
                            <b>Returns : </b>    <code>tickets.GetTicketPriorityFieldMetadataResponse</code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTicketStatusData"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>getTicketStatusData</b></span>
                        <a href="#getTicketStatusData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTicketStatusData(statusId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, relations: string[], organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="178"
                                    class="link-to-prism">src/tickets/services/ticket-annotator.service.ts:178</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Get ticket status data with relations</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>statusId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>relations</td>
                                            <td>
                                                        <code>string[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;tickets.GetTicketStatusDataResponse&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTicketStatusFieldMetadata"></a>
                    <span class="name">
                        <span ><b>getTicketStatusFieldMetadata</b></span>
                        <a href="#getTicketStatusFieldMetadata"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getTicketStatusFieldMetadata()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="135"
                                    class="link-to-prism">src/tickets/services/ticket-annotator.service.ts:135</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Get field metadata for ticket status</p>
</div>

                        <div class="io-description">
                            <b>Returns : </b>    <code>tickets.GetTicketStatusFieldMetadataResponse</code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTicketTypeData"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>getTicketTypeData</b></span>
                        <a href="#getTicketTypeData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTicketTypeData(typeId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, relations: string[], organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="200"
                                    class="link-to-prism">src/tickets/services/ticket-annotator.service.ts:200</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Get ticket type data with relations</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>typeId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>relations</td>
                                            <td>
                                                        <code>string[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;tickets.GetTicketTypeDataResponse&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTicketTypeFieldMetadata"></a>
                    <span class="name">
                        <span ><b>getTicketTypeFieldMetadata</b></span>
                        <a href="#getTicketTypeFieldMetadata"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getTicketTypeFieldMetadata()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="142"
                                    class="link-to-prism">src/tickets/services/ticket-annotator.service.ts:142</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Get field metadata for ticket type</p>
</div>

                        <div class="io-description">
                            <b>Returns : </b>    <code>tickets.GetTicketTypeFieldMetadataResponse</code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Injectable, NotFoundException } from &quot;@nestjs/common&quot;;
import { InjectDataSource } from &quot;@nestjs/typeorm&quot;;
import { tickets } from &quot;@repo/shared-proto&quot;;
import {
  Ticket,
  TicketPriority,
  TicketPriorityRepository,
  TicketRepository,
  TicketStatus,
  TicketStatusRepository,
  TicketType,
  TicketTypeRepository,
} from &quot;@repo/thena-platform-entities&quot;;
import &quot;reflect-metadata&quot;;
import { DataSource } from &quot;typeorm&quot;;
import { FieldMetadataService } from &quot;../../common/services/field-metadata.service&quot;;

interface FieldTypeInfo {
  fieldName: string;
  type: string;
  isNullable: boolean;
  isArray: boolean;
  isRelation: boolean;
  relationEntityName?: string;
  relationType?: &quot;ONE_TO_ONE&quot; | &quot;ONE_TO_MANY&quot; | &quot;MANY_TO_ONE&quot; | &quot;MANY_TO_MANY&quot;;
  metadata?: {
    label?: string;
    description?: string;
    required?: boolean;
    system?: boolean;
    slaEnabled?: boolean;
  };
}

@Injectable()
export class TicketAnnotatorService {
  constructor(
    @InjectDataSource()
    private readonly dataSource: DataSource,
    private readonly ticketRepository: TicketRepository,
    private readonly ticketStatusRepository: TicketStatusRepository,
    private readonly ticketTypeRepository: TicketTypeRepository,
    private readonly ticketPriorityRepository: TicketPriorityRepository,
    private readonly fieldMetadataService: FieldMetadataService,
  ) {}

  /**
   * @deprecated
   */
  getEntityFieldTypes(slaEnabled: boolean): FieldTypeInfo[] {
    const entity &#x3D; Ticket;
    const metadata &#x3D; this.dataSource.getMetadata(entity);
    const fields: FieldTypeInfo[] &#x3D; [];

    // Process columns
    metadata.columns.forEach((column) &#x3D;&gt; {
      const fieldMetadataReflect &#x3D; Reflect.getMetadata(
        &quot;fieldMetadata&quot;,
        entity.prototype,
        column.propertyName,
      );

      if (!slaEnabled || fieldMetadataReflect?.slaMetadata?.slaEnabled) {
        let metadata &#x3D; {};
        if (fieldMetadataReflect?.slaMetadata?.slaEnabled) {
          const { slaMetadata, ...rest } &#x3D; fieldMetadataReflect;
          metadata &#x3D; { ...rest, ...slaMetadata };
        }

        fields.push({
          fieldName: column.propertyName,
          type: fieldMetadataReflect?.type || column.type.toString(),
          isNullable: column.isNullable,
          isArray: false,
          isRelation: false,
          metadata,
        });
      }
    });

    // Process relations
    metadata.relations.forEach((relation) &#x3D;&gt; {
      const fieldMetadataReflect &#x3D; Reflect.getMetadata(
        &quot;fieldMetadata&quot;,
        entity.prototype,
        relation.propertyName,
      );

      if (!slaEnabled || fieldMetadataReflect?.slaMetadata?.slaEnabled) {
        let metadata &#x3D; {};
        if (fieldMetadataReflect?.slaMetadata?.slaEnabled) {
          const { slaMetadata, ...rest } &#x3D; fieldMetadataReflect;
          metadata &#x3D; { ...rest, ...slaMetadata };
        }

        // Determine relation type
        let relationType: FieldTypeInfo[&quot;relationType&quot;];
        if (relation.isOneToOne) relationType &#x3D; &quot;ONE_TO_ONE&quot;;
        else if (relation.isOneToMany) relationType &#x3D; &quot;ONE_TO_MANY&quot;;
        else if (relation.isManyToOne) relationType &#x3D; &quot;MANY_TO_ONE&quot;;
        else if (relation.isManyToMany) relationType &#x3D; &quot;MANY_TO_MANY&quot;;

        // Determine if it&#x27;s an array based on relation type
        const isArray &#x3D; relation.isOneToMany || relation.isManyToMany;

        fields.push({
          fieldName: relation.propertyName,
          type: &quot;relation&quot;,
          isNullable: relation.isNullable,
          isArray,
          isRelation: true,
          relationType,
          relationEntityName:
            relation.type instanceof Function
              ? relation.type.name
              : (relation.type as string),
          metadata,
        });
      }
    });

    return fields;
  }

  /**
   * Get field metadata for tickets
   */
  getTicketFieldMetadata(): tickets.GetTicketFieldMetadataResponse {
    return this.fieldMetadataService.getFieldMetadata(Ticket);
  }

  /**
   * Get field metadata for ticket status
   */
  getTicketStatusFieldMetadata(): tickets.GetTicketStatusFieldMetadataResponse {
    return this.fieldMetadataService.getFieldMetadata(TicketStatus);
  }

  /**
   * Get field metadata for ticket type
   */
  getTicketTypeFieldMetadata(): tickets.GetTicketTypeFieldMetadataResponse {
    return this.fieldMetadataService.getFieldMetadata(TicketType);
  }

  /**
   * Get field metadata for ticket priority
   */
  getTicketPriorityFieldMetadata(): tickets.GetTicketPriorityFieldMetadataResponse {
    return this.fieldMetadataService.getFieldMetadata(TicketPriority);
  }

  /**
   * Get ticket data with relations
   */
  async getTicketData(
    ticketId: string,
    relations: string[],
    organizationId: string,
  ): Promise&lt;tickets.GetTicketDataResponse&gt; {
    const ticket &#x3D; await this.ticketRepository.findByCondition({
      where: { uid: ticketId, organizationId },
      relations,
    });

    if (!ticket) {
      throw new NotFoundException(&quot;Ticket not found&quot;);
    }

    return {
      data: JSON.stringify(ticket),
    };
  }

  /**
   * Get ticket status data with relations
   */
  async getTicketStatusData(
    statusId: string,
    relations: string[],
    organizationId: string,
  ): Promise&lt;tickets.GetTicketStatusDataResponse&gt; {
    const status &#x3D; await this.ticketStatusRepository.findByCondition({
      where: { uid: statusId, organizationId },
      relations,
    });

    if (!status) {
      throw new NotFoundException(&quot;Ticket status not found&quot;);
    }

    return {
      data: JSON.stringify(status),
    };
  }

  /**
   * Get ticket type data with relations
   */
  async getTicketTypeData(
    typeId: string,
    relations: string[],
    organizationId: string,
  ): Promise&lt;tickets.GetTicketTypeDataResponse&gt; {
    const type &#x3D; await this.ticketTypeRepository.findByCondition({
      where: { uid: typeId, organizationId },
      relations,
    });

    if (!type) {
      throw new NotFoundException(&quot;Ticket type not found&quot;);
    }

    return {
      data: JSON.stringify(type),
    };
  }

  /**
   * Get ticket priority data with relations
   */
  async getTicketPriorityData(
    priorityId: string,
    relations: string[],
    organizationId: string,
  ): Promise&lt;tickets.GetTicketPriorityDataResponse&gt; {
    const priority &#x3D; await this.ticketPriorityRepository.findByCondition({
      where: { uid: priorityId, organizationId },
      relations,
    });

    if (!priority) {
      throw new NotFoundException(&quot;Ticket priority not found&quot;);
    }

    return {
      data: JSON.stringify(priority),
    };
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'TicketAnnotatorService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
