<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >CommentsActionService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/communications/services/comments.action.service.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createComment" >createComment</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#deleteComment" >deleteComment</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#getAllowedVisibilities" >getAllowedVisibilities</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getCommentByUserType" >getCommentByUserType</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getComments" >getComments</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getCommentThreads" >getCommentThreads</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getPopulatedCommentById" >getPopulatedCommentById</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateComment" >updateComment</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateCommentAttachments" >updateCommentAttachments</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(transactionService: TransactionService, commentRepository: CommentRepository, teamsService: <a href="../injectables/TeamsService.html" target="_self">TeamsService</a>, commentProcessorService: CommentProcessorService, activitiesService: <a href="../injectables/ActivitiesService.html" target="_self">ActivitiesService</a>, snsPublishQueue: Queue)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="43" class="link-to-prism">src/communications/services/comments.action.service.ts:43</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>transactionService</td>
                                                  
                                                        <td>
                                                                    <code>TransactionService</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>commentRepository</td>
                                                  
                                                        <td>
                                                                    <code>CommentRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>teamsService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/TeamsService.html" target="_self" >TeamsService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>commentProcessorService</td>
                                                  
                                                        <td>
                                                                    <code>CommentProcessorService</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>activitiesService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/ActivitiesService.html" target="_self" >ActivitiesService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>snsPublishQueue</td>
                                                  
                                                        <td>
                                                                    <code>Queue</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createComment"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>createComment</b></span>
                        <a href="#createComment"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createComment(comment: <a href="../interfaces/CreateComment.html" target="_self">CreateComment</a>, txnContext?: TransactionContext)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="124"
                                    class="link-to-prism">src/communications/services/comments.action.service.ts:124</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Create a new comment</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>comment</td>
                                            <td>
                                                            <code><a href="../interfaces/CreateComment.html" target="_self" >CreateComment</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The comment data</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>txnContext</td>
                                            <td>
                                                        <code>TransactionContext</code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The newly created comment</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteComment"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>deleteComment</b></span>
                        <a href="#deleteComment"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteComment(commentId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, currentUser: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="313"
                                    class="link-to-prism">src/communications/services/comments.action.service.ts:313</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Deletes a comment.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>commentId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The unique identifier of the comment</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>currentUser</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The currently logged in user</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getAllowedVisibilities"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>getAllowedVisibilities</b></span>
                        <a href="#getAllowedVisibilities"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getAllowedVisibilities(currentUser: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, accountId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="77"
                                    class="link-to-prism">src/communications/services/comments.action.service.ts:77</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Get the allowed visibilities for a comment based on the user type</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>currentUser</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The currently logged in user</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The unique identifier of the organization</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The unique identifier of the team</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>accountId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The allowed visibilities</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getCommentByUserType"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>getCommentByUserType</b></span>
                        <a href="#getCommentByUserType"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getCommentByUserType(getCommentByUserTypeQuery, currentUser: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="427"
                                    class="link-to-prism">src/communications/services/comments.action.service.ts:427</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Get comments for a ticket</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>getCommentByUserTypeQuery</td>
                                            <td>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>currentUser</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The currently logged in user</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The comments and the total number of comments</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getComments"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>getComments</b></span>
                        <a href="#getComments"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getComments(getCommentQuery, currentUser: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="370"
                                    class="link-to-prism">src/communications/services/comments.action.service.ts:370</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Get comments for a ticket</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>getCommentQuery</td>
                                            <td>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The query for getting comments</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>currentUser</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The currently logged in user</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The comments and the total number of comments</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getCommentThreads"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>getCommentThreads</b></span>
                        <a href="#getCommentThreads"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getCommentThreads(commentId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, getCommentQuery: <a href="../classes/GetCommentThreadsQuery.html" target="_self">GetCommentThreadsQuery</a>, currentUser: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="488"
                                    class="link-to-prism">src/communications/services/comments.action.service.ts:488</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Get the threads for a comment</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>commentId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The unique identifier of the comment</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>getCommentQuery</td>
                                            <td>
                                                            <code><a href="../classes/GetCommentThreadsQuery.html" target="_self" >GetCommentThreadsQuery</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The query for getting comments</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>currentUser</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The currently logged in user</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The threads for the comment</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getPopulatedCommentById"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>getPopulatedCommentById</b></span>
                        <a href="#getPopulatedCommentById"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getPopulatedCommentById(commentId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="61"
                                    class="link-to-prism">src/communications/services/comments.action.service.ts:61</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Get a comment by its unique identifier and eagerly load the relations</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>commentId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>The unique identifier of the comment</li>
</ul>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>The unique identifier of the organization</li>
</ul>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The comment with the relations eagerly loaded</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateComment"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>updateComment</b></span>
                        <a href="#updateComment"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateComment(commentId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, updateCommentDto: <a href="../classes/UpdateCommentDto.html" target="_self">UpdateCommentDto</a>, currentUser: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="220"
                                    class="link-to-prism">src/communications/services/comments.action.service.ts:220</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Update a comment</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>commentId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The unique identifier of the comment</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>updateCommentDto</td>
                                            <td>
                                                            <code><a href="../classes/UpdateCommentDto.html" target="_self" >UpdateCommentDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The update comment data</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>currentUser</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;Comment&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The updated comment</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateCommentAttachments"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>updateCommentAttachments</b></span>
                        <a href="#updateCommentAttachments"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateCommentAttachments(comment: Comment, attachments: Storage[])</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="528"
                                    class="link-to-prism">src/communications/services/comments.action.service.ts:528</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>comment</td>
                                            <td>
                                                        <code>Comment</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>attachments</td>
                                            <td>
                                                        <code>Storage[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { InjectQueue } from &quot;@nestjs/bullmq&quot;;
import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from &quot;@nestjs/common&quot;;
import {
  AuditLog,
  AuditLogEntityType,
  AuditLogOp,
  AuditLogVisibility,
  Comment,
  CommentEntityTypes,
  CommentProcessorService,
  CommentRepository,
  CommentType,
  CommentVisibility,
  MentionExtractor,
  Storage,
  TransactionContext,
  TransactionService,
  UserType,
} from &quot;@repo/thena-platform-entities&quot;;
import { Queue } from &quot;bullmq&quot;;
import { cloneDeep } from &quot;lodash&quot;;
import { DeepPartial, In, IsNull } from &quot;typeorm&quot;;
import { ActivitiesService } from &quot;../../activities/services/activities.service&quot;;
import { CurrentUser } from &quot;../../common/decorators&quot;;
import { QueueNames } from &quot;../../constants/queue.constants&quot;;
import { TeamsService } from &quot;../../teams/services/teams.service&quot;;
import { EAGERLY_LOAD_COMMENTS_RELATIONS } from &quot;../constants/comments.constants&quot;;
import {
  CreateComment,
  GetCommentByUserTypeQuery,
  GetCommentQuery,
  GetCommentThreadsQuery,
} from &quot;../dto/comment.queries&quot;;
import { UpdateCommentDto } from &quot;../dto/comments.dto&quot;;
import { CommentOp } from &quot;../utils&quot;;

@Injectable()
export class CommentsActionService {
  constructor(
    private readonly transactionService: TransactionService,
    private readonly commentRepository: CommentRepository,
    private readonly teamsService: TeamsService,
    private readonly commentProcessorService: CommentProcessorService,
    private readonly activitiesService: ActivitiesService,

    @InjectQueue(QueueNames.COMMENT_SNS_PUBLISHER)
    private readonly snsPublishQueue: Queue,
  ) {}

  /**
   * Get a comment by its unique identifier and eagerly load the relations
   * @param commentId - The unique identifier of the comment
   * @param orgId - The unique identifier of the organization
   * @returns The comment with the relations eagerly loaded
   */
  async getPopulatedCommentById(commentId: string, orgId: string) {
    const comment &#x3D; await this.commentRepository.findByCondition({
      where: { uid: commentId, organizationId: orgId },
      relations: EAGERLY_LOAD_COMMENTS_RELATIONS,
    });

    return comment;
  }

  /**
   * Get the allowed visibilities for a comment based on the user type
   * @param currentUser The currently logged in user
   * @param teamId The unique identifier of the team
   * @param organizationId The unique identifier of the organization
   * @returns The allowed visibilities
   */
  private async getAllowedVisibilities(
    currentUser: CurrentUser,
    organizationId: string,
    teamId: string,
    accountId: string,
  ) {
    if (accountId) {
      // Account notes are always internal comments
      return [CommentVisibility.PRIVATE];
    }

    // If the user is a customer user, only get public comments
    switch (currentUser.userType) {
      // Customer users can only see public comments
      case UserType.CUSTOMER_USER: {
        return [CommentVisibility.PUBLIC];
      }

      // Internal users can see all comments
      case UserType.ORG_ADMIN:
      case UserType.USER: {
        const userIsInTeam &#x3D; await this.teamsService.userBelongsToTeam(
          currentUser.sub,
          teamId,
          organizationId,
        );

        // If the user is in the team, they can see all comments
        if (userIsInTeam) {
          return [CommentVisibility.PUBLIC, CommentVisibility.PRIVATE];
        } else {
          // If the user is not in the team, they can only see public and internal comments
          return [CommentVisibility.PUBLIC];
        }
      }

      default: {
        throw new UnauthorizedException(&quot;Invalid user type!&quot;);
      }
    }
  }

  /**
   * Create a new comment
   * @param comment The comment data
   * @returns The newly created comment
   */
  async createComment(comment: CreateComment, txnContext?: TransactionContext) {
    const newComment &#x3D; await this.transactionService.runInTransaction(
      async (txnContext) &#x3D;&gt; {
        // Extract the user and customer IDs from the comment content
        const { userIds } &#x3D; MentionExtractor.extractMentions(comment.content);
        const { plainText, html, markdown } &#x3D;
          this.commentProcessorService.processComment(comment.content);

        const commentData: DeepPartial&lt;Comment&gt; &#x3D; {
          organizationId: comment.organizationId,
          content: plainText,
          contentHtml: html,
          contentMarkdown: markdown,
          commentVisibility:
            comment.commentVisibility ?? CommentVisibility.PUBLIC,
          commentType: comment.commentType ?? CommentType.COMMENT,
          authorId: comment.authorId,
          parentCommentId: comment.parentCommentId,
          metadata: {
            mentions: userIds,
            source: comment.source,
            ...comment.metadata,
          },
          ticketId: comment.ticketId ?? null,
          teamId: comment.teamId ?? null,
          accountNoteId: comment.accountNoteId ?? null,
          accountActivityId: comment.accountActivityId ?? null,
          accountTaskId: comment.accountTaskId ?? null,
          accountId: comment.accountId ?? null,
          commentThreadName: comment.commentThreadName ?? null,
        };

        // Create the comment
        const createdComment &#x3D; await this.commentRepository.createComment(
          commentData,
          txnContext,
        );

        // Create audit logs for comment
        const auditLog: DeepPartial&lt;AuditLog&gt; &#x3D; {
          ...(createdComment.teamId &amp;&amp; { team: { id: createdComment.teamId } }),
          organization: { id: createdComment.organizationId },
          activityPerformedBy: { id: createdComment.authorId },
          entityId: createdComment.id,
          entityUid: createdComment.uid,
          entityType: AuditLogEntityType.COMMENTS,
          op: AuditLogOp.CREATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: &#x60;A new comment ${createdComment.id} was created&#x60;,
          description: &#x60;A new comment ${createdComment.id} was created by ${createdComment.authorId} &#x60;,
        };

        // Record the audit log
        await this.activitiesService.recordAuditLog(auditLog, txnContext);

        // Update the parent comment&#x27;s metadata
        if (comment.parentCommentId) {
          const parentId &#x3D; comment.parentCommentId;
          const orgId &#x3D; comment.organizationId;
          const parentCommentCriteria &#x3D; { id: parentId, organizationId: orgId };

          // Update the parent comment&#x27;s metadata
          await this.commentRepository.updateWithTxn(
            txnContext,
            parentCommentCriteria,
            {
              metadata: () &#x3D;&gt; &#x60;
              jsonb_set(
                COALESCE(metadata, &#x27;{&quot;replies&quot;: []}&#x27;::jsonb),
                &#x27;{replies}&#x27;,
                COALESCE(metadata-&gt;&#x27;replies&#x27;, &#x27;[]&#x27;::jsonb) || &#x27;[&quot;${createdComment.uid}&quot;]&#x27;::jsonb
              )&#x60;,
            },
          );
        }

        return createdComment;
      },
      txnContext,
    );

    // Fetch the comment with populated relations
    const populatedComment &#x3D; await this.getPopulatedCommentById(
      newComment.uid,
      comment.organizationId,
    );

    return populatedComment;
  }

  /**
   * Update a comment
   * @param commentId The unique identifier of the comment
   * @param updateCommentDto The update comment data
   * @returns The updated comment
   */
  async updateComment(
    commentId: string,
    updateCommentDto: UpdateCommentDto,
    currentUser: CurrentUser,
  ): Promise&lt;Comment&gt; {
    // Check if the comment exists
    const comment &#x3D; await this.getPopulatedCommentById(
      commentId,
      currentUser.orgId,
    );

    // If the comment is not found, throw an error
    if (!comment) throw new NotFoundException(&quot;Comment not found!&quot;);
    if (comment.deletedAt)
      throw new BadRequestException(
        &quot;This comment was deleted and therefore can no longer be edited/updated!&quot;,
      );

    // If the current user is not the author of the comment, throw an error
    if (comment.authorId !&#x3D;&#x3D; currentUser.sub) {
      throw new UnauthorizedException(
        &quot;You are not authorized to update this comment!&quot;,
      );
    }

    // Run the update transaction
    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      // Extract the user IDs from the updated content
      const { userIds } &#x3D; MentionExtractor.extractMentions(
        updateCommentDto.content,
      );

      // Process the updated content
      const { content } &#x3D; updateCommentDto;
      const { plainText, html, markdown } &#x3D;
        this.commentProcessorService.processComment(content);

      const updateCriteria &#x3D; {
        id: comment.id,
        organizationId: comment.organizationId,
      };

      const refArray &#x3D; userIds.map((id) &#x3D;&gt; &#x60;&quot;${id}&quot;&#x60;).join(&quot;,&quot;);
      await this.commentRepository.updateWithTxn(txnContext, updateCriteria, {
        content: plainText,
        contentHtml: html,
        contentMarkdown: markdown,
        isEdited: true,
        commentThreadName: updateCommentDto.threadName ?? null,
        metadata: () &#x3D;&gt; &#x60;
            jsonb_set(
              COALESCE(metadata, &#x27;{&quot;mentions&quot;: []}&#x27;::jsonb),
              &#x27;{mentions}&#x27;,
              CASE 
                -- Note that we&#x27;re falling back to an empty object if the refArray is empty
                -- the reason being that Postgres is not able to determine the type of an empty array
                WHEN &#x27;${refArray}&#x27; &#x3D; &#x27;&#x27; THEN &#x27;{}&#x27;::jsonb
                ELSE &#x27;[${refArray}]&#x27;::jsonb
              END
            )
          &#x60;,
      });
    });

    // Emit comment updated event
    await this.snsPublishQueue.add(
      QueueNames.COMMENT_SNS_PUBLISHER,
      {
        comment: comment.uid,
        user: currentUser,
        eventType: CommentOp.UPDATED,
        entityType: CommentEntityTypes.TICKET,
      },
      {
        attempts: 3,
        backoff: { type: &quot;exponential&quot;, delay: 1000 },
      },
    );

    // Fetch the updated comment with populated relations
    const populatedComment &#x3D; await this.getPopulatedCommentById(
      comment.uid,
      currentUser.orgId,
    );

    return populatedComment;
  }

  /**
   * Deletes a comment.
   * @param commentId The unique identifier of the comment
   * @param currentUser The currently logged in user
   */
  async deleteComment(commentId: string, currentUser: CurrentUser) {
    // Check if the comment exists with full relations
    const comment &#x3D; await this.commentRepository.findByCondition({
      where: { uid: commentId, organizationId: currentUser.orgId },
      relations: [&quot;team&quot;, &quot;author&quot;, &quot;ticket&quot;, &quot;attachments&quot;],
    });

    const fullCommentData &#x3D; cloneDeep(comment);

    // If the comment is not found, throw an error
    if (!comment) throw new NotFoundException(&quot;Comment not found!&quot;);

    // If the current user is not the author of the comment, throw an error
    if (comment.authorId !&#x3D;&#x3D; currentUser.sub) {
      throw new UnauthorizedException(
        &quot;You are not authorized to delete this comment!&quot;,
      );
    }

    // Run the delete transaction
    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      // Delete the comment
      await this.commentRepository.softDeleteWithTxn(txnContext, {
        id: comment.id,
        uid: comment.uid,
        organizationId: currentUser.orgId,
      });

      fullCommentData.deletedAt &#x3D; new Date();

      // Add to SNS publish queue
      await this.snsPublishQueue.add(
        QueueNames.COMMENT_SNS_PUBLISHER,
        {
          comment: comment.uid,
          user: currentUser,
          eventType: CommentOp.DELETED,
          entityType: CommentEntityTypes.TICKET,
          previousComment: fullCommentData, // Pass the full comment data
        },
        {
          attempts: 3,
          backoff: {
            type: &quot;exponential&quot;,
            delay: 1000,
          },
        },
      );
    });
  }

  /**
   * Get comments for a ticket
   * @param getCommentQuery The query for getting comments
   * @param currentUser The currently logged in user
   * @returns The comments and the total number of comments
   */
  async getComments(
    getCommentQuery: GetCommentQuery &amp; {
      teamId?: string;
      ticketId?: string;
      accountId?: string;
      accountNoteId?: string;
      accountActivityId?: string;
      accountTaskId?: string;
    },
    currentUser: CurrentUser,
  ) {
    const {
      page,
      limit,
      teamId,
      ticketId,
      accountId,
      accountNoteId,
      accountActivityId,
      accountTaskId,
    } &#x3D; getCommentQuery;

    // Get the allowed visibilities
    const allowedVisibilities &#x3D; await this.getAllowedVisibilities(
      currentUser,
      currentUser.orgId,
      teamId,
      accountId,
    );

    const comments &#x3D; await this.commentRepository.fetchPaginatedResults(
      { page, limit },
      {
        where: {
          ...(ticketId &amp;&amp; { ticketId }),
          ...(teamId &amp;&amp; { teamId }),
          ...(accountId &amp;&amp; { accountId }),
          ...(accountNoteId &amp;&amp; { accountNoteId }),
          ...(accountActivityId &amp;&amp; { accountActivityId }),
          ...(accountTaskId &amp;&amp; { accountTaskId }),
          organizationId: currentUser.orgId,
          commentVisibility: In(allowedVisibilities),
          parentCommentId: IsNull(),
        },
        relations: [&quot;author&quot;, &quot;attachments&quot;],
      },
    );

    return comments;
  }

  /**
   * Get comments for a ticket
   * @param getCommentQuery The query for getting comments
   * @param currentUser The currently logged in user
   * @returns The comments and the total number of comments
   */
  async getCommentByUserType(
    getCommentByUserTypeQuery: GetCommentByUserTypeQuery &amp; {
      organizationId: string;
      teamId?: string;
      ticketId?: string;
      accountId?: string;
      accountNoteId?: string;
      accountActivityId?: string;
      accountTaskId?: string;
    },
    currentUser: CurrentUser,
  ) {
    const {
      teamId,
      ticketId,
      organizationId,
      userType,
      accountId,
      accountNoteId,
      accountActivityId,
      accountTaskId,
    } &#x3D; getCommentByUserTypeQuery;

    // Get the allowed visibilities
    const allowedVisibilities &#x3D; await this.getAllowedVisibilities(
      currentUser,
      organizationId,
      teamId,
      accountId,
    );
    const comments &#x3D; await this.commentRepository.findWithRelations({
      where: {
        ...(ticketId &amp;&amp; { ticketId }),
        ...(teamId &amp;&amp; { teamId }),
        ...(accountId &amp;&amp; { accountId }),
        ...(accountNoteId &amp;&amp; { accountNoteId }),
        ...(accountActivityId &amp;&amp; { accountActivityId }),
        ...(accountTaskId &amp;&amp; { accountTaskId }),
        organizationId,
        commentVisibility: In(allowedVisibilities),
        parentCommentId: IsNull(),
        author: {
          userType,
        },
      },
      relations: [&quot;author&quot;, &quot;team&quot;],
      order: { createdAt: &quot;DESC&quot; },
      take: 1,
    });

    // Since we&#x27;re expecting only one result, return the first item or null
    return comments[0] || null;
  }

  /**
   * Get the threads for a comment
   * @param commentId The unique identifier of the comment
   * @param getCommentQuery The query for getting comments
   * @param currentUser The currently logged in user
   * @returns The threads for the comment
   */
  async getCommentThreads(
    commentId: string,
    getCommentQuery: GetCommentThreadsQuery,
    currentUser: CurrentUser,
  ) {
    const { page, limit } &#x3D; getCommentQuery;

    // Check if the comment exists
    const comment &#x3D; await this.commentRepository.findByCondition({
      where: { uid: commentId, organizationId: currentUser.orgId },
    });

    // If the comment is not found, throw an error
    if (!comment) throw new NotFoundException(&quot;Comment not found!&quot;);

    // Get the allowed visibilities
    const allowedVisibilities &#x3D; await this.getAllowedVisibilities(
      currentUser,
      currentUser.orgId,
      comment.teamId,
      comment.accountId,
    );

    // Get the thread comments
    const threadComments &#x3D; await this.commentRepository.fetchPaginatedResults(
      { page: page ?? 0, limit: Math.max(limit ?? 10, 100) },
      {
        where: {
          organizationId: currentUser.orgId,
          commentVisibility: In(allowedVisibilities),
          parentCommentId: comment.id,
        },
        relations: [&quot;author&quot;],
        order: { createdAt: &quot;ASC&quot; },
      },
    );

    return threadComments;
  }

  async updateCommentAttachments(comment: Comment, attachments: Storage[]) {
    comment.attachments &#x3D; attachments;
    await this.commentRepository.save(comment);
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'CommentsActionService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
