<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >CustomerContactActionService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/accounts/services/customer-contact.action.service.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#addContactsMatchingDomainToAccount" >addContactsMatchingDomainToAccount</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#bulkCreateCustomerContacts" >bulkCreateCustomerContacts</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createCustomerContact" >createCustomerContact</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#deleteCustomerContact" >deleteCustomerContact</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#findCustomerContactByEmail" >findCustomerContactByEmail</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#findCustomerContactByUID" >findCustomerContactByUID</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findCustomerContacts" >findCustomerContacts</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#isContactTypeAttributeInUse" >isContactTypeAttributeInUse</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateContactTypeAttribute" >updateContactTypeAttribute</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateCustomerContact" >updateCustomerContact</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#validateAndGetContactType" >validateAndGetContactType</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(customerContactRepository: <a href="../interfaces/Customer.html" target="_self">CustomerContactRepository</a>, cachedCustomerContactRepository: <a href="../interfaces/Customer.html" target="_self">CachedCustomerContactRepository</a>, accountCommonService: <a href="../injectables/AccountCommonService.html" target="_self">AccountCommonService</a>, transactionService: TransactionService, activitiesService: <a href="../injectables/ActivitiesService.html" target="_self">ActivitiesService</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="32" class="link-to-prism">src/accounts/services/customer-contact.action.service.ts:32</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>customerContactRepository</td>
                                                  
                                                        <td>
                                                                        <code><a href="../interfaces/Customer.html" target="_self" >CustomerContactRepository</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>cachedCustomerContactRepository</td>
                                                  
                                                        <td>
                                                                        <code><a href="../interfaces/Customer.html" target="_self" >CachedCustomerContactRepository</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>accountCommonService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/AccountCommonService.html" target="_self" >AccountCommonService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>transactionService</td>
                                                  
                                                        <td>
                                                                    <code>TransactionService</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>activitiesService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/ActivitiesService.html" target="_self" >ActivitiesService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="addContactsMatchingDomainToAccount"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>addContactsMatchingDomainToAccount</b></span>
                        <a href="#addContactsMatchingDomainToAccount"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>addContactsMatchingDomainToAccount(organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, domain: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, accountId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="149"
                                    class="link-to-prism">src/accounts/services/customer-contact.action.service.ts:149</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Adds contacts matching a domain to an account</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>Organization ID</li>
</ul>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>domain</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>Email domain</li>
</ul>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>accountId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>Account ID</li>
</ul>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;void&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="bulkCreateCustomerContacts"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>bulkCreateCustomerContacts</b></span>
                        <a href="#bulkCreateCustomerContacts"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>bulkCreateCustomerContacts(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, createDto: <a href="../classes/BulkCreateCustomerContactsDto.html" target="_self">BulkCreateCustomerContactsDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="325"
                                    class="link-to-prism">src/accounts/services/customer-contact.action.service.ts:325</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Bulk create customer contacts</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>Current user</li>
</ul>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>createDto</td>
                                            <td>
                                                            <code><a href="../classes/BulkCreateCustomerContactsDto.html" target="_self" >BulkCreateCustomerContactsDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p><a href="../classes/BulkCreateCustomerContactsDto.html">BulkCreateCustomerContactsDto</a> - Bulk create customer contacts DTO</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../interfaces/Customer.html" target="_self" >Promise&lt;CustomerContactBulkResponseDto&gt;</a></code>

                        </div>
                            <div class="io-description">
                                <p>The created customer contacts</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createCustomerContact"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>createCustomerContact</b></span>
                        <a href="#createCustomerContact"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createCustomerContact(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, createDto: <a href="../classes/CreateCustomerContactDto.html" target="_self">CreateCustomerContactDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="228"
                                    class="link-to-prism">src/accounts/services/customer-contact.action.service.ts:228</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Create a new customer contact</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>Current user</li>
</ul>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>createDto</td>
                                            <td>
                                                            <code><a href="../classes/CreateCustomerContactDto.html" target="_self" >CreateCustomerContactDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p><a href="../classes/CreateCustomerContactDto.html">CreateCustomerContactDto</a> - Create customer contact DTO</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../interfaces/Customer.html" target="_self" >Promise&lt;CustomerContact&gt;</a></code>

                        </div>
                            <div class="io-description">
                                <p>The created customer contact</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteCustomerContact"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>deleteCustomerContact</b></span>
                        <a href="#deleteCustomerContact"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteCustomerContact(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, contactId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="547"
                                    class="link-to-prism">src/accounts/services/customer-contact.action.service.ts:547</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Delete a customer contact</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>Current user</li>
</ul>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>contactId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>Contact ID</li>
</ul>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;void&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findCustomerContactByEmail"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>findCustomerContactByEmail</b></span>
                        <a href="#findCustomerContactByEmail"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findCustomerContactByEmail(organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, email: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="117"
                                    class="link-to-prism">src/accounts/services/customer-contact.action.service.ts:117</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Find customer contact by email</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>Organization ID</li>
</ul>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>email</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>Email</li>
</ul>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../interfaces/Customer.html" target="_self" >Promise&lt;CustomerContact&gt;</a></code>

                        </div>
                            <div class="io-description">
                                <p>Customer contact</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findCustomerContactByUID"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>findCustomerContactByUID</b></span>
                        <a href="#findCustomerContactByUID"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findCustomerContactByUID(contactId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="133"
                                    class="link-to-prism">src/accounts/services/customer-contact.action.service.ts:133</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Find customer contact by uid</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>contactId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>Contact ID</li>
</ul>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../interfaces/Customer.html" target="_self" >Promise&lt;CustomerContact&gt;</a></code>

                        </div>
                            <div class="io-description">
                                <p>Customer contact</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findCustomerContacts"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findCustomerContacts</b></span>
                        <a href="#findCustomerContacts"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findCustomerContacts(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, query: <a href="../classes/FindAllCustomerContactsDto.html" target="_self">FindAllCustomerContactsDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="183"
                                    class="link-to-prism">src/accounts/services/customer-contact.action.service.ts:183</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Find customer contacts by account ID and / or contact type</p>
<ul>
<li>if accountId is provided, finds all contacts for the account</li>
<li>if accountId is not provided, finds all contacts for the organization</li>
<li>if contact type is provided, finds all contacts for the account with the given contact type</li>
<li>if contact type is not provided, finds all contacts for the account</li>
</ul>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>Current user</li>
</ul>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>query</td>
                                            <td>
                                                            <code><a href="../classes/FindAllCustomerContactsDto.html" target="_self" >FindAllCustomerContactsDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p><a href="../classes/FindAllCustomerContactsDto.html">FindAllCustomerContactsDto</a> - Query to find customer contacts</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../interfaces/Customer.html" target="_self" >Promise&lt;CustomerContact[]&gt;</a></code>

                        </div>
                            <div class="io-description">
                                <p>All contacts for the account</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isContactTypeAttributeInUse"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>isContactTypeAttributeInUse</b></span>
                        <a href="#isContactTypeAttributeInUse"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>isContactTypeAttributeInUse(contactTypeAttributeId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="65"
                                    class="link-to-prism">src/accounts/services/customer-contact.action.service.ts:65</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>contactTypeAttributeId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;boolean&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateContactTypeAttribute"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>updateContactTypeAttribute</b></span>
                        <a href="#updateContactTypeAttribute"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateContactTypeAttribute(prevContactTypeAttributeId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, newContactTypeAttributeId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="74"
                                    class="link-to-prism">src/accounts/services/customer-contact.action.service.ts:74</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>prevContactTypeAttributeId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>newContactTypeAttributeId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;void&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateCustomerContact"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>updateCustomerContact</b></span>
                        <a href="#updateCustomerContact"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateCustomerContact(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, contactId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, updateDto: <a href="../classes/UpdateCustomerContactDto.html" target="_self">UpdateCustomerContactDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="443"
                                    class="link-to-prism">src/accounts/services/customer-contact.action.service.ts:443</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Update a customer contact</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>Current user</li>
</ul>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>contactId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>Contact ID</li>
</ul>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>updateDto</td>
                                            <td>
                                                            <code><a href="../classes/UpdateCustomerContactDto.html" target="_self" >UpdateCustomerContactDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p><a href="../classes/UpdateCustomerContactDto.html">UpdateCustomerContactDto</a> - Update customer contact DTO</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../interfaces/Customer.html" target="_self" >Promise&lt;CustomerContact&gt;</a></code>

                        </div>
                            <div class="io-description">
                                <p>The updated customer contact</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateAndGetContactType"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>validateAndGetContactType</b></span>
                        <a href="#validateAndGetContactType"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateAndGetContactType(orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, contactTypeUID: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="48"
                                    class="link-to-prism">src/accounts/services/customer-contact.action.service.ts:48</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>contactTypeUID</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;AccountAttributeValue&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from &quot;@nestjs/common&quot;;
import {
  Account,
  AccountAttributeType,
  AccountAttributeValue,
  AuditLogEntityType,
  AuditLogOp,
  AuditLogVisibility,
  CachedCustomerContactRepository,
  CustomerContact,
  CustomerContactRepository,
  TransactionService,
} from &quot;@repo/thena-platform-entities&quot;;
import { cloneDeep } from &quot;lodash&quot;;
import { CurrentUser } from &quot;src/common/decorators&quot;;
import { In, Like } from &quot;typeorm&quot;;
import { ActivitiesService } from &quot;../../activities/services/activities.service&quot;;
import {
  BulkCreateCustomerContactsDto,
  CreateCustomerContactDto,
  FindAllCustomerContactsDto,
  UpdateCustomerContactDto,
} from &quot;../dtos/customer-contact.dto&quot;;
import { CustomerContactBulkResponseDto } from &quot;../dtos/response/customer-contact.dto&quot;;
import { AccountCommonService } from &quot;./account-commons.service&quot;;

@Injectable()
export class CustomerContactActionService {
  constructor(
    // Injected repositories
    private readonly customerContactRepository: CustomerContactRepository,
    private readonly cachedCustomerContactRepository: CachedCustomerContactRepository,

    // Accounts services
    private readonly accountCommonService: AccountCommonService,

    // Transaction service
    private readonly transactionService: TransactionService,

    // Activities service
    private readonly activitiesService: ActivitiesService,
  ) {}

  private async validateAndGetContactType(
    orgId: string,
    contactTypeUID: string,
  ): Promise&lt;AccountAttributeValue&gt; {
    const contactType &#x3D; await this.accountCommonService.getAttributeValue(
      contactTypeUID,
      orgId,
    );
    if (
      !contactType ||
      contactType.attribute !&#x3D;&#x3D; AccountAttributeType.CONTACT_TYPE
    ) {
      throw new NotFoundException(&quot;Contact type not found&quot;);
    }
    return contactType;
  }

  async isContactTypeAttributeInUse(
    contactTypeAttributeId: string,
  ): Promise&lt;boolean&gt; {
    const count &#x3D; await this.cachedCustomerContactRepository.count({
      where: { contactType: contactTypeAttributeId, isActive: true },
    });
    return count &gt; 0;
  }

  async updateContactTypeAttribute(
    prevContactTypeAttributeId: string,
    newContactTypeAttributeId: string,
  ): Promise&lt;void&gt; {
    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      // Find contacts using the previous contact type attribute
      const customerContacts &#x3D; await this.customerContactRepository.findAll({
        where: { contactType: prevContactTypeAttributeId },
      });

      if (customerContacts.length &#x3D;&#x3D;&#x3D; 0) {
        return;
      }

      // Update the contact type attribute for each contact
      for (const customerContact of customerContacts) {
        customerContact.contactType &#x3D; newContactTypeAttributeId;
      }

      // Save the contacts
      await this.customerContactRepository.saveManyWithTxn(
        txnContext,
        customerContacts,
      );

      // Invalidate the cache for contacts
      this.cachedCustomerContactRepository.invalidateCustomerContactCache({
        organizationId: customerContacts[0].organizationId,
        emails: customerContacts.map(
          (customerContact) &#x3D;&gt; customerContact.email,
        ),
        uids: customerContacts.map((customerContact) &#x3D;&gt; customerContact.uid),
      });
    });
  }

  /**
   * Find customer contact by email
   *
   * @param organizationId - Organization ID
   * @param email - Email
   * @returns Customer contact
   */
  private findCustomerContactByEmail(
    organizationId: string,
    email: string,
  ): Promise&lt;CustomerContact&gt; {
    return this.customerContactRepository.findByCondition({
      where: { email, organizationId, isActive: true },
      relations: [&quot;contactTypeAttribute&quot;, &quot;accounts&quot;],
    });
  }

  /**
   * Find customer contact by uid
   *
   * @param contactId - Contact ID
   * @returns Customer contact
   */
  private findCustomerContactByUID(
    contactId: string,
  ): Promise&lt;CustomerContact&gt; {
    return this.customerContactRepository.findByCondition({
      where: { uid: contactId, isActive: true },
      relations: [&quot;contactTypeAttribute&quot;, &quot;accounts&quot;],
    });
  }

  /**
   * Adds contacts matching a domain to an account
   *
   * @param organizationId - Organization ID
   * @param domain - Email domain
   * @param accountId - Account ID
   */
  async addContactsMatchingDomainToAccount(
    organizationId: string,
    domain: string,
    accountId: string,
  ): Promise&lt;void&gt; {
    const account &#x3D; await this.accountCommonService.validateAndGetAccount(
      accountId,
      organizationId,
    );

    const contacts &#x3D; await this.customerContactRepository.findAll({
      where: { email: Like(&#x60;%@${domain}&#x60;), organizationId, isActive: true },
      relations: [&quot;accounts&quot;],
    });

    contacts.map((contact) &#x3D;&gt; {
      contact.accounts.push(account);
    });

    await this.customerContactRepository.saveMany(contacts);
  }

  /**
   * Find customer contacts by account ID and / or contact type
   *
   * - if accountId is provided, finds all contacts for the account
   * - if accountId is not provided, finds all contacts for the organization
   * - if contact type is provided, finds all contacts for the account with the given contact type
   * - if contact type is not provided, finds all contacts for the account
   *
   * @param user - Current user
   * @param query {@link FindAllCustomerContactsDto} - Query to find customer contacts
   * @returns All contacts for the account
   */
  async findCustomerContacts(
    user: CurrentUser,
    query: FindAllCustomerContactsDto,
  ): Promise&lt;CustomerContact[]&gt; {
    const whereClause: any &#x3D; {
      organizationId: user.orgId,
      isActive: true,
    };

    if (query.accountId) {
      const account &#x3D; await this.accountCommonService.validateAndGetAccount(
        query.accountId,
        user.orgId,
      );
      whereClause.accounts &#x3D; { id: account.id };
    }

    if (query.contactType) {
      const contactType &#x3D; await this.validateAndGetContactType(
        user.orgId,
        query.contactType,
      );
      whereClause.contactType &#x3D; contactType.id;
    }

    const { results: contacts } &#x3D;
      await this.customerContactRepository.fetchPaginatedResults(
        { page: query.page ?? 0, limit: query.limit ?? 10 },
        {
          where: whereClause,
          order: { id: &quot;DESC&quot; },
          relations: [&quot;contactTypeAttribute&quot;, &quot;accounts&quot;],
        },
      );

    return contacts;
  }

  /**
   * Create a new customer contact
   *
   * @param user - Current user
   * @param createDto {@link CreateCustomerContactDto} - Create customer contact DTO
   * @returns The created customer contact
   */
  async createCustomerContact(
    user: CurrentUser,
    createDto: CreateCustomerContactDto,
  ): Promise&lt;CustomerContact&gt; {
    // Find account
    const accounts: Account[] &#x3D; [];
    if (createDto.accountIds) {
      for (const accountId of createDto.accountIds) {
        const account &#x3D; await this.accountCommonService.validateAndGetAccount(
          accountId,
          user.orgId,
        );
        accounts.push(account);
      }
    }

    let contactType: AccountAttributeValue;
    if (!createDto.contactType) {
      // Use default contact type if not provided
      const defaultContactType &#x3D;
        await this.accountCommonService.findDefaultAttributeValue(
          user.orgId,
          AccountAttributeType.CONTACT_TYPE,
        );

      contactType &#x3D; defaultContactType;
    } else {
      // Use provided contact type
      contactType &#x3D; await this.validateAndGetContactType(
        user.orgId,
        createDto.contactType,
      );
    }

    // Find existing contact by email and throw error if it exists
    const existingContact &#x3D; await this.findCustomerContactByEmail(
      user.orgId,
      createDto.email,
    );
    if (existingContact) {
      throw new BadRequestException(&quot;Contact with this email already exists&quot;);
    }

    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      const customerContact &#x3D; this.customerContactRepository.create({
        organizationId: user.orgId,
        firstName: createDto.firstName,
        lastName: createDto.lastName,
        email: createDto.email,
        phoneNumber: createDto.phoneNumber,
        accounts,
        contactType: contactType.id,
      });

      // Save customer contact
      await this.customerContactRepository.saveWithTxn(
        txnContext,
        customerContact,
      );

      // Record audit log
      await this.activitiesService.recordAuditLog(
        {
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityId: customerContact.id,
          entityUid: customerContact.uid,
          entityType: AuditLogEntityType.CUSTOMER_CONTACT,
          op: AuditLogOp.CREATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: &#x60;Customer contact ${customerContact.email} was created!&#x60;,
          description: &#x60;Customer contact ${customerContact.email}  was created by ${user.email}!&#x60;,
        },
        txnContext,
      );

      // Invalidate cache
      await this.cachedCustomerContactRepository.invalidateCustomerContactCache(
        {
          organizationId: user.orgId,
          emails: [createDto.email],
          uids: [customerContact.uid],
        },
      );
    });

    // Return from cache
    return this.findCustomerContactByEmail(user.orgId, createDto.email);
  }

  /**
   * Bulk create customer contacts
   *
   * @param user - Current user
   * @param createDto {@link BulkCreateCustomerContactsDto} - Bulk create customer contacts DTO
   * @returns The created customer contacts
   */
  async bulkCreateCustomerContacts(
    user: CurrentUser,
    createDto: BulkCreateCustomerContactsDto,
  ): Promise&lt;CustomerContactBulkResponseDto&gt; {
    if (!createDto.contacts.length) {
      throw new BadRequestException(&quot;No contacts to create&quot;);
    }

    const accounts: Account[] &#x3D; [];
    if (createDto.accountIds) {
      // Find account
      for (const accountId of createDto.accountIds) {
        const account &#x3D; await this.accountCommonService.validateAndGetAccount(
          accountId,
          user.orgId,
        );
        accounts.push(account);
      }
    }

    // Find contact type
    let contactType: AccountAttributeValue;
    if (createDto.contactType) {
      contactType &#x3D; await this.validateAndGetContactType(
        user.orgId,
        createDto.contactType,
      );
    } else {
      contactType &#x3D; await this.accountCommonService.findDefaultAttributeValue(
        user.orgId,
        AccountAttributeType.CONTACT_TYPE,
      );
    }

    // Find existing contacts by email and skip them if they exist
    const existingContacts &#x3D; await this.customerContactRepository.findAll({
      where: {
        email: In(createDto.contacts.map((contact) &#x3D;&gt; contact.email)),
        isActive: true,
      },
    });

    const contactsToCreate &#x3D; createDto.contacts.filter(
      (contact) &#x3D;&gt; !existingContacts.some((c) &#x3D;&gt; c.email &#x3D;&#x3D;&#x3D; contact.email),
    );

    const distinctEmails &#x3D; [
      ...new Set(contactsToCreate.map((contact) &#x3D;&gt; contact.email)),
    ];

    if (distinctEmails.length !&#x3D;&#x3D; contactsToCreate.length) {
      throw new BadRequestException(
        &quot;Duplicate emails found in the list of contacts to create&quot;,
      );
    }

    // Create contacts
    const createdContacts &#x3D; contactsToCreate.map((contact) &#x3D;&gt;
      this.customerContactRepository.create({
        organizationId: user.orgId,
        firstName: contact.firstName,
        lastName: contact.lastName,
        email: contact.email,
        phoneNumber: contact.phoneNumber,
        accounts,
        contactType: contactType.id,
      }),
    );

    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      // Save contacts
      const savedContacts &#x3D;
        await this.customerContactRepository.saveManyWithTxn(
          txnContext,
          createdContacts,
        );

      // Record audit logs only for newly created contacts
      await this.activitiesService.recordBulkAuditLog(
        savedContacts.map((contact) &#x3D;&gt; ({
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityId: contact.id,
          entityUid: contact.uid,
          entityType: AuditLogEntityType.CUSTOMER_CONTACT,
          op: AuditLogOp.CREATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: &#x60;Customer contact ${contact.email} was created&#x60;,
          description: &#x60;Customer contact ${contact.email} was created by ${user.email}!&#x60;,
        })),
        txnContext,
      );

      // Invalidate cache
      await this.cachedCustomerContactRepository.invalidateCustomerContactCache(
        {
          organizationId: user.orgId,
          emails: createdContacts.map((contact) &#x3D;&gt; contact.email),
          uids: createdContacts.map((contact) &#x3D;&gt; contact.uid),
        },
      );
    });

    return {
      total: createDto.contacts.length,
      created: createdContacts.length,
      skipped: createDto.contacts.length - createdContacts.length,
    };
  }

  /**
   * Update a customer contact
   *
   * @param user - Current user
   * @param contactId - Contact ID
   * @param updateDto {@link UpdateCustomerContactDto} - Update customer contact DTO
   * @returns The updated customer contact
   */
  async updateCustomerContact(
    user: CurrentUser,
    contactId: string,
    updateDto: UpdateCustomerContactDto,
  ): Promise&lt;CustomerContact&gt; {
    // Find existing contact by email and throw error if it exists
    const customerContact &#x3D; await this.findCustomerContactByUID(contactId);
    if (!customerContact) {
      throw new NotFoundException(&quot;Contact not found&quot;);
    }

    const existingContact &#x3D; cloneDeep(customerContact);

    if (updateDto.accountIds) {
      const newAccounts: Account[] &#x3D; [];
      for (const accountId of updateDto.accountIds) {
        const account &#x3D; await this.accountCommonService.validateAndGetAccount(
          accountId,
          user.orgId,
        );
        newAccounts.push(account);
      }

      customerContact.accounts &#x3D; newAccounts;
    }

    if (updateDto.contactType) {
      const contactType &#x3D; await this.validateAndGetContactType(
        user.orgId,
        updateDto.contactType,
      );

      customerContact.contactTypeAttribute &#x3D; contactType;
    }

    if (updateDto.email &amp;&amp; updateDto.email !&#x3D;&#x3D; customerContact.email) {
      const existingContactByEmail &#x3D; await this.findCustomerContactByEmail(
        user.orgId,
        updateDto.email,
      );

      if (existingContactByEmail) {
        throw new BadRequestException(&quot;Contact with this email already exists&quot;);
      }

      customerContact.email &#x3D; updateDto.email;
    }

    customerContact.firstName &#x3D;
      updateDto.firstName ?? customerContact.firstName;
    customerContact.lastName &#x3D; updateDto.lastName ?? customerContact.lastName;
    customerContact.phoneNumber &#x3D;
      updateDto.phoneNumber ?? customerContact.phoneNumber;

    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      // Save customer contact
      await this.customerContactRepository.saveWithTxn(
        txnContext,
        customerContact,
      );

      // Record audit log
      await this.activitiesService.recordAuditLog(
        {
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityId: customerContact.id,
          entityUid: customerContact.uid,
          entityType: AuditLogEntityType.CUSTOMER_CONTACT,
          op: AuditLogOp.UPDATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: &#x60;Customer contact ${customerContact.email} was updated!&#x60;,
          description: &#x60;Customer contact ${customerContact.email} was updated by ${user.email}!&#x60;,
          metadata: {
            updatedFields: Object.keys(updateDto).map((key) &#x3D;&gt; ({
              field: key,
              previousValue: existingContact[key],
              updatedToValue: customerContact[key],
            })),
          },
        },
        txnContext,
      );

      // Invalidate cache
      await this.cachedCustomerContactRepository.invalidateCustomerContactCache(
        {
          organizationId: user.orgId,
          emails: [customerContact.email],
          uids: [customerContact.uid],
        },
      );
    });

    // Return from cache
    return this.findCustomerContactByEmail(user.orgId, customerContact.email);
  }

  /**
   * Delete a customer contact
   *
   * @param user - Current user
   * @param contactId - Contact ID
   */
  async deleteCustomerContact(
    user: CurrentUser,
    contactId: string,
  ): Promise&lt;void&gt; {
    // Find existing contact by email and throw error if it exists
    const existingContact &#x3D; await this.findCustomerContactByUID(contactId);
    if (!existingContact) {
      throw new NotFoundException(&quot;Contact not found&quot;);
    }

    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      existingContact.isActive &#x3D; false;

      // Save customer contact
      await this.customerContactRepository.saveWithTxn(
        txnContext,
        existingContact,
      );

      // Record audit log
      await this.activitiesService.recordAuditLog(
        {
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityId: existingContact.id,
          entityUid: existingContact.uid,
          entityType: AuditLogEntityType.CUSTOMER_CONTACT,
          op: AuditLogOp.DELETED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: &#x60;Customer contact ${existingContact.email} was deleted!&#x60;,
          description: &#x60;Customer contact ${existingContact.email} was deleted by ${user.email}!&#x60;,
          metadata: {
            updatedFields: [
              {
                field: &quot;isActive&quot;,
                previousValue: &quot;true&quot;,
                updatedToValue: &quot;false&quot;,
              },
            ],
          },
        },
        txnContext,
      );

      // Invalidate cache
      await this.cachedCustomerContactRepository.invalidateCustomerContactCache(
        {
          organizationId: user.orgId,
          emails: [existingContact.email],
          uids: [existingContact.uid],
        },
      );
    });
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'CustomerContactActionService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
