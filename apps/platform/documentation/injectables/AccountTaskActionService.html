<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >AccountTaskActionService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/accounts/services/account-task.action.service.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createAccountTask" >createAccountTask</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#deleteAccountTask" >deleteAccountTask</a>
                            </li>
                            <li>
                                <a href="#findAccountTask" >findAccountTask</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findAllAccountTasks" >findAllAccountTasks</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#isTaskAttributeValueInUse" >isTaskAttributeValueInUse</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#removeTaskAttachment" >removeTaskAttachment</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateAccountTask" >updateAccountTask</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateTaskAttributeValue" >updateTaskAttributeValue</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#validateAndGetAttributeValue" >validateAndGetAttributeValue</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(accountTaskRepository: AccountTaskRepository, cachedAccountTaskRepository: CachedAccountTaskRepository, accountCommonService: <a href="../injectables/AccountCommonService.html" target="_self">AccountCommonService</a>, transactionService: TransactionService, activitiesService: <a href="../injectables/ActivitiesService.html" target="_self">ActivitiesService</a>, usersService: <a href="../injectables/UsersService.html" target="_self">UsersService</a>, storageService: <a href="../injectables/StorageService.html" target="_self">StorageService</a>, accountsSNSEventsFactory: <a href="../classes/AccountsSNSEventsFactory.html" target="_self">AccountsSNSEventsFactory</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="33" class="link-to-prism">src/accounts/services/account-task.action.service.ts:33</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>accountTaskRepository</td>
                                                  
                                                        <td>
                                                                    <code>AccountTaskRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>cachedAccountTaskRepository</td>
                                                  
                                                        <td>
                                                                    <code>CachedAccountTaskRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>accountCommonService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/AccountCommonService.html" target="_self" >AccountCommonService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>transactionService</td>
                                                  
                                                        <td>
                                                                    <code>TransactionService</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>activitiesService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/ActivitiesService.html" target="_self" >ActivitiesService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>usersService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/UsersService.html" target="_self" >UsersService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>storageService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/StorageService.html" target="_self" >StorageService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>accountsSNSEventsFactory</td>
                                                  
                                                        <td>
                                                                        <code><a href="../classes/AccountsSNSEventsFactory.html" target="_self" >AccountsSNSEventsFactory</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createAccountTask"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>createAccountTask</b></span>
                        <a href="#createAccountTask"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createAccountTask(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, createAccountTaskDto: <a href="../classes/CreateAccountTaskDto.html" target="_self">CreateAccountTaskDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="303"
                                    class="link-to-prism">src/accounts/services/account-task.action.service.ts:303</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Creates an account task.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>createAccountTaskDto</td>
                                            <td>
                                                            <code><a href="../classes/CreateAccountTaskDto.html" target="_self" >CreateAccountTaskDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The create account task DTO</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;AccountTask&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The created account task</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteAccountTask"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>deleteAccountTask</b></span>
                        <a href="#deleteAccountTask"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteAccountTask(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, taskId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="625"
                                    class="link-to-prism">src/accounts/services/account-task.action.service.ts:625</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Deletes an account task.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>taskId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the task</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;void&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The deleted account task</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAccountTask"></a>
                    <span class="name">
                        <span ><b>findAccountTask</b></span>
                        <a href="#findAccountTask"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findAccountTask(taskId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="171"
                                    class="link-to-prism">src/accounts/services/account-task.action.service.ts:171</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds an account task.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>taskId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the task</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;AccountTask&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The found account task</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAllAccountTasks"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findAllAccountTasks</b></span>
                        <a href="#findAllAccountTasks"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAllAccountTasks(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, findAccountTasksDto: <a href="../classes/FindAccountTaskDto.html" target="_self">FindAccountTaskDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="194"
                                    class="link-to-prism">src/accounts/services/account-task.action.service.ts:194</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds all account tasks.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>findAccountTasksDto</td>
                                            <td>
                                                            <code><a href="../classes/FindAccountTaskDto.html" target="_self" >FindAccountTaskDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The find account tasks DTO</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;AccountTask[]&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The found account tasks</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isTaskAttributeValueInUse"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>isTaskAttributeValueInUse</b></span>
                        <a href="#isTaskAttributeValueInUse"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>isTaskAttributeValueInUse(attributeValueId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, attributeType: AccountAttributeType)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="96"
                                    class="link-to-prism">src/accounts/services/account-task.action.service.ts:96</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Checks if an attribute value is in use.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>attributeValueId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the attribute value</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>attributeType</td>
                                            <td>
                                                        <code>AccountAttributeType</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The attribute type</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;boolean&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>Whether the attribute value is in use</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="removeTaskAttachment"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>removeTaskAttachment</b></span>
                        <a href="#removeTaskAttachment"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>removeTaskAttachment(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, taskId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, attachmentId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="686"
                                    class="link-to-prism">src/accounts/services/account-task.action.service.ts:686</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Remove an attachment from an account task</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>Current user</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>taskId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The Task ID</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>attachmentId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The Attachment ID</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;void&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateAccountTask"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>updateAccountTask</b></span>
                        <a href="#updateAccountTask"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateAccountTask(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, taskId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, updateAccountTaskDto: <a href="../classes/UpdateAccountTaskDto.html" target="_self">UpdateAccountTaskDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="480"
                                    class="link-to-prism">src/accounts/services/account-task.action.service.ts:480</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Updates an account task.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>taskId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the task</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>updateAccountTaskDto</td>
                                            <td>
                                                            <code><a href="../classes/UpdateAccountTaskDto.html" target="_self" >UpdateAccountTaskDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The update account task DTO</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;AccountTask&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The updated account task</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateTaskAttributeValue"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>updateTaskAttributeValue</b></span>
                        <a href="#updateTaskAttributeValue"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateTaskAttributeValue(attributeValueId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, defaultAttributeValueId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, attributeType: AccountAttributeType)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="125"
                                    class="link-to-prism">src/accounts/services/account-task.action.service.ts:125</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>attributeValueId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>defaultAttributeValueId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>attributeType</td>
                                            <td>
                                                        <code>AccountAttributeType</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;void&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateAndGetAttributeValue"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>validateAndGetAttributeValue</b></span>
                        <a href="#validateAndGetAttributeValue"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateAndGetAttributeValue(attributeValue: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, attributeType: AccountAttributeType)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="66"
                                    class="link-to-prism">src/accounts/services/account-task.action.service.ts:66</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Validates and gets an account task attribute value. (One of the task type, task status, or task priority)</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>attributeValue</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The UID of the attribute value</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>attributeType</td>
                                            <td>
                                                        <code>AccountAttributeType</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The attribute type</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;AccountAttributeValue&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The account attribute value</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from &quot;@nestjs/common&quot;;
import { AccountEvents } from &quot;@repo/thena-eventbridge&quot;;
import {
  AccountAttributeType,
  AccountAttributeValue,
  AccountTask,
  AccountTaskRepository,
  AuditLogEntityType,
  AuditLogOp,
  AuditLogVisibility,
  CachedAccountTaskRepository,
  TransactionService,
} from &quot;@repo/thena-platform-entities&quot;;
import { cloneDeep } from &quot;lodash&quot;;
import { FindOptionsWhere } from &quot;typeorm&quot;;
import { ActivitiesService } from &quot;../../activities/services/activities.service&quot;;
import { CurrentUser } from &quot;../../common/decorators/user.decorator&quot;;
import { StorageService } from &quot;../../storage/services/storage-service&quot;;
import { UsersService } from &quot;../../users/services/users.service&quot;;
import {
  CreateAccountTaskDto,
  FindAccountTaskDto,
  UpdateAccountTaskDto,
} from &quot;../dtos/account-task.dto&quot;;
import { AccountsSNSEventsFactory } from &quot;../events/accounts-sns-events.factory&quot;;
import { AccountCommonService } from &quot;./account-commons.service&quot;;

@Injectable()
export class AccountTaskActionService {
  constructor(
    // Inject repositories
    private readonly accountTaskRepository: AccountTaskRepository,
    private readonly cachedAccountTaskRepository: CachedAccountTaskRepository,

    // Accounts services
    private readonly accountCommonService: AccountCommonService,

    // Transaction service
    private readonly transactionService: TransactionService,

    // Activities service
    private readonly activitiesService: ActivitiesService,

    // Users service
    private readonly usersService: UsersService,

    // Storage service
    private readonly storageService: StorageService,

    // Event emitter
    private readonly accountsSNSEventsFactory: AccountsSNSEventsFactory,
  ) {}

  /**
   * Validates and gets an account task attribute value. (One of the task type, task status, or task priority)
   *
   * @param attributeValue The UID of the attribute value
   * @param organizationId The ID of the organization
   * @param attributeType The attribute type
   * @returns The account attribute value
   */
  private async validateAndGetAttributeValue(
    attributeValue: string,
    organizationId: string,
    attributeType: AccountAttributeType,
  ): Promise&lt;AccountAttributeValue&gt; {
    const attributeValueEntity &#x3D;
      await this.accountCommonService.getAttributeValue(
        attributeValue,
        organizationId,
      );

    if (
      !attributeValueEntity ||
      attributeValueEntity.attribute !&#x3D;&#x3D; attributeType
    ) {
      throw new NotFoundException(
        &#x60;Provided value for ${attributeType} not found&#x60;,
      );
    }

    return attributeValueEntity;
  }

  /**
   * Checks if an attribute value is in use.
   *
   * @param attributeValueId The ID of the attribute value
   * @param attributeType The attribute type
   * @returns Whether the attribute value is in use
   */
  async isTaskAttributeValueInUse(
    attributeValueId: string,
    attributeType: AccountAttributeType,
  ): Promise&lt;boolean&gt; {
    switch (attributeType) {
      case AccountAttributeType.TASK_TYPE: {
        const count &#x3D; await this.accountTaskRepository.count({
          where: { type: attributeValueId, isActive: true },
        });
        return count &gt; 0;
      }
      case AccountAttributeType.TASK_STATUS: {
        const count &#x3D; await this.accountTaskRepository.count({
          where: { status: attributeValueId, isActive: true },
        });
        return count &gt; 0;
      }
      case AccountAttributeType.TASK_PRIORITY: {
        const count &#x3D; await this.accountTaskRepository.count({
          where: { priority: attributeValueId, isActive: true },
        });
        return count &gt; 0;
      }
      default: {
        return false;
      }
    }
  }

  async updateTaskAttributeValue(
    attributeValueId: string,
    defaultAttributeValueId: string,
    attributeType: AccountAttributeType,
  ): Promise&lt;void&gt; {
    const attributeTypeColMapping &#x3D; {
      [AccountAttributeType.TASK_TYPE]: &quot;type&quot;,
      [AccountAttributeType.TASK_STATUS]: &quot;status&quot;,
      [AccountAttributeType.TASK_PRIORITY]: &quot;priority&quot;,
    };

    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      // Find account activities using the previous activity status attribute
      const accountTasks &#x3D; await this.accountTaskRepository.findAll({
        where: { [attributeTypeColMapping[attributeType]]: attributeValueId },
      });

      if (accountTasks.length &#x3D;&#x3D;&#x3D; 0) {
        return;
      }

      // Update the activity status attribute for each account activity
      for (const accountTask of accountTasks) {
        accountTask[attributeTypeColMapping[attributeType]] &#x3D;
          defaultAttributeValueId;
      }

      // Save the account activities
      await this.accountTaskRepository.saveManyWithTxn(
        txnContext,
        accountTasks,
      );

      // Invalidate the cache for each account
      this.cachedAccountTaskRepository.invalidateAccountTaskCache({
        uids: accountTasks.map((a) &#x3D;&gt; a.uid),
      });
    });
  }

  /**
   * Finds an account task.
   *
   * @param taskId The ID of the task
   * @returns The found account task
   */
  findAccountTask(taskId: string): Promise&lt;AccountTask&gt; {
    return this.cachedAccountTaskRepository.findByCondition({
      where: { uid: taskId, isActive: true },
      relations: [
        &quot;account&quot;,
        &quot;activity&quot;,
        &quot;assignee&quot;,
        &quot;typeAttribute&quot;,
        &quot;statusAttribute&quot;,
        &quot;priorityAttribute&quot;,
        &quot;creator&quot;,
        &quot;attachments&quot;,
      ],
    });
  }

  /**
   * Finds all account tasks.
   *
   * @param user The current user
   * @param findAccountTasksDto The find account tasks DTO
   * @returns The found account tasks
   */
  async findAllAccountTasks(
    user: CurrentUser,
    findAccountTasksDto: FindAccountTaskDto,
  ): Promise&lt;AccountTask[]&gt; {
    const where: FindOptionsWhere&lt;AccountTask&gt; &#x3D; {
      account: {
        organizationId: user.orgId,
      },
      isActive: true,
    };

    if (findAccountTasksDto.accountId) {
      // Validate and get account
      const account &#x3D; await this.accountCommonService.validateAndGetAccount(
        findAccountTasksDto.accountId,
        user.orgId,
      );

      where.accountId &#x3D; account.id;
    }

    if (findAccountTasksDto.activityId) {
      // Validate and get activity
      const activity &#x3D; await this.accountCommonService.validateAndGetActivity(
        findAccountTasksDto.activityId,
        user.orgId,
      );

      if (where.accountId &amp;&amp; activity.accountId !&#x3D;&#x3D; where.accountId) {
        throw new BadRequestException(
          &quot;Activity does not belong to the same account&quot;,
        );
      }

      where.activityId &#x3D; activity.id;
    }

    if (findAccountTasksDto.assigneeId) {
      const assignee &#x3D; await this.usersService.findOneByPublicId(
        findAccountTasksDto.assigneeId,
      );

      if (!assignee) {
        throw new NotFoundException(&quot;Assignee not found&quot;);
      }

      where.assigneeId &#x3D; assignee.id;
    }

    if (findAccountTasksDto.type) {
      const taskType &#x3D; await this.validateAndGetAttributeValue(
        findAccountTasksDto.type,
        user.orgId,
        AccountAttributeType.TASK_TYPE,
      );

      where.type &#x3D; taskType.id;
    }

    if (findAccountTasksDto.status) {
      const taskStatus &#x3D; await this.validateAndGetAttributeValue(
        findAccountTasksDto.status,
        user.orgId,
        AccountAttributeType.TASK_STATUS,
      );

      where.status &#x3D; taskStatus.id;
    }

    if (findAccountTasksDto.priority) {
      const taskPriority &#x3D; await this.validateAndGetAttributeValue(
        findAccountTasksDto.priority,
        user.orgId,
        AccountAttributeType.TASK_PRIORITY,
      );

      where.priority &#x3D; taskPriority.id;
    }

    const { results: tasks } &#x3D;
      await this.accountTaskRepository.fetchPaginatedResults(
        {
          page: findAccountTasksDto.page ?? 0,
          limit: Math.min(findAccountTasksDto.limit ?? 10, 100),
        },
        {
          where,
          relations: [
            &quot;account&quot;,
            &quot;activity&quot;,
            &quot;assignee&quot;,
            &quot;typeAttribute&quot;,
            &quot;statusAttribute&quot;,
            &quot;priorityAttribute&quot;,
            &quot;creator&quot;,
            &quot;attachments&quot;,
          ],
        },
      );
    return tasks;
  }

  /**
   * Creates an account task.
   *
   * @param user The current user
   * @param createAccountTaskDto The create account task DTO
   * @returns The created account task
   */
  async createAccountTask(
    user: CurrentUser,
    createAccountTaskDto: CreateAccountTaskDto,
  ): Promise&lt;AccountTask&gt; {
    // validate and get account
    const account &#x3D; await this.accountCommonService.validateAndGetAccount(
      createAccountTaskDto.accountId,
      user.orgId,
    );

    if (!account) {
      throw new NotFoundException(&quot;Account not found&quot;);
    }

    // get user for assignee
    const assignee &#x3D; await this.usersService.findOneByPublicId(
      createAccountTaskDto.assigneeId,
    );

    if (!assignee) {
      throw new NotFoundException(&quot;Assignee not found&quot;);
    }

    // get user for creator
    const creator &#x3D; await this.usersService.findOneByPublicId(user.uid);

    let taskType: AccountAttributeValue;
    let taskStatus: AccountAttributeValue;
    let taskPriority: AccountAttributeValue;

    if (!createAccountTaskDto.type) {
      // Use default task type if not provided
      const defaultTaskType &#x3D;
        await this.accountCommonService.findDefaultAttributeValue(
          user.orgId,
          AccountAttributeType.TASK_TYPE,
        );

      taskType &#x3D; defaultTaskType;
    } else {
      // Use provided task type
      taskType &#x3D; await this.validateAndGetAttributeValue(
        createAccountTaskDto.type,
        user.orgId,
        AccountAttributeType.TASK_TYPE,
      );
    }

    if (!createAccountTaskDto.status) {
      // Use default task status if not provided
      const defaultTaskStatus &#x3D;
        await this.accountCommonService.findDefaultAttributeValue(
          user.orgId,
          AccountAttributeType.TASK_STATUS,
        );

      taskStatus &#x3D; defaultTaskStatus;
    } else {
      // Use provided task status
      taskStatus &#x3D; await this.validateAndGetAttributeValue(
        createAccountTaskDto.status,
        user.orgId,
        AccountAttributeType.TASK_STATUS,
      );
    }

    if (!createAccountTaskDto.priority) {
      // Use default task priority if not provided
      const defaultTaskPriority &#x3D;
        await this.accountCommonService.findDefaultAttributeValue(
          user.orgId,
          AccountAttributeType.TASK_PRIORITY,
        );

      taskPriority &#x3D; defaultTaskPriority;
    } else {
      // Use provided task priority
      taskPriority &#x3D; await this.validateAndGetAttributeValue(
        createAccountTaskDto.priority,
        user.orgId,
        AccountAttributeType.TASK_PRIORITY,
      );
    }

    const task &#x3D; this.accountTaskRepository.create({
      accountId: account.id,
      assigneeId: assignee.id,
      title: createAccountTaskDto.title,
      description: createAccountTaskDto.description ?? &quot;&quot;,
      type: taskType.id,
      status: taskStatus.id,
      priority: taskPriority.id,
      createdBy: creator.id,
    });

    if (createAccountTaskDto.activityId) {
      // validate and get activity
      const activity &#x3D; await this.accountCommonService.validateAndGetActivity(
        createAccountTaskDto.activityId,
        user.orgId,
      );

      if (activity.accountId !&#x3D;&#x3D; account.id) {
        throw new BadRequestException(
          &quot;Activity does not belong to the same account&quot;,
        );
      }

      task.activity &#x3D; activity;
    }

    const taskId &#x3D; await this.transactionService.runInTransaction(
      async (txnContext) &#x3D;&gt; {
        // Save in db
        const savedTask &#x3D; await this.accountTaskRepository.saveWithTxn(
          txnContext,
          task,
        );

        // Save attachments
        if (createAccountTaskDto.attachmentUrls) {
          savedTask.attachments &#x3D; await this.storageService.attachFilesToEntity(
            &quot;account-task&quot;,
            savedTask.uid,
            createAccountTaskDto.attachmentUrls,
            user.orgId,
            &#x60;${user.orgUid}/accounts/${account.uid}/tasks/${savedTask.uid}&#x60;,
          );

          await this.accountTaskRepository.saveWithTxn(txnContext, savedTask);
        }

        // Record audit log
        await this.activitiesService.recordAuditLog({
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityId: savedTask.id,
          entityType: AuditLogEntityType.ACCOUNT_TASK,
          op: AuditLogOp.CREATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: &#x60;Account task ${savedTask.id} for account ${account.id} was created!&#x60;,
          description: &#x60;Account task ${savedTask.id} for account ${account.id} was created by ${user.email}!&#x60;,
        });

        // Invalidate cache
        await this.cachedAccountTaskRepository.invalidateAccountTaskCache({
          uids: [savedTask.uid],
        });

        // Publish account task created event to SNS
        const accountTaskCreatedEvent &#x3D;
          this.accountsSNSEventsFactory.createAccountTaskCreatedSNSEvent(
            user,
            savedTask,
          );

        this.accountCommonService.publishEventToSNSQueue(
          AccountEvents.ACCOUNT_TASK_CREATED,
          accountTaskCreatedEvent,
          user,
        );

        return savedTask.uid;
      },
    );

    return this.findAccountTask(taskId);
  }

  /**
   * Updates an account task.
   *
   * @param user The current user
   * @param taskId The ID of the task
   * @param updateAccountTaskDto The update account task DTO
   * @returns The updated account task
   */
  async updateAccountTask(
    user: CurrentUser,
    taskId: string,
    updateAccountTaskDto: UpdateAccountTaskDto,
  ): Promise&lt;AccountTask&gt; {
    // validate and get task
    const task &#x3D; await this.findAccountTask(taskId);

    if (!task) {
      throw new NotFoundException(&quot;Task not found&quot;);
    }

    const previousTask &#x3D; cloneDeep(task);

    task.title &#x3D; updateAccountTaskDto.title ?? previousTask.title;
    task.description &#x3D;
      updateAccountTaskDto.description ?? previousTask.description;

    // Update activity
    if (updateAccountTaskDto.activityId) {
      const activity &#x3D; await this.accountCommonService.validateAndGetActivity(
        updateAccountTaskDto.activityId,
        user.orgId,
      );

      if (activity.accountId !&#x3D;&#x3D; task.accountId) {
        throw new BadRequestException(
          &quot;Activity does not belong to the same account&quot;,
        );
      }

      task.activity &#x3D; activity;
    }

    // Update assignee
    if (updateAccountTaskDto.assigneeId) {
      const assignee &#x3D; await this.usersService.findOneByPublicId(
        updateAccountTaskDto.assigneeId,
      );

      if (!assignee) {
        throw new NotFoundException(&quot;Assignee not found&quot;);
      }

      task.assignee &#x3D; assignee;
    }

    // Update task type
    if (updateAccountTaskDto.type) {
      const taskType &#x3D; await this.validateAndGetAttributeValue(
        updateAccountTaskDto.type,
        user.orgId,
        AccountAttributeType.TASK_TYPE,
      );

      task.typeAttribute &#x3D; taskType;
    }

    // Update task status
    if (updateAccountTaskDto.status) {
      const taskStatus &#x3D; await this.validateAndGetAttributeValue(
        updateAccountTaskDto.status,
        user.orgId,
        AccountAttributeType.TASK_STATUS,
      );

      task.statusAttribute &#x3D; taskStatus;
    }

    // Update task priority
    if (updateAccountTaskDto.priority) {
      const taskPriority &#x3D; await this.validateAndGetAttributeValue(
        updateAccountTaskDto.priority,
        user.orgId,
        AccountAttributeType.TASK_PRIORITY,
      );

      task.priorityAttribute &#x3D; taskPriority;
    }

    if (updateAccountTaskDto.attachmentUrls) {
      const newAttachments &#x3D; await this.storageService.attachFilesToEntity(
        &quot;account-task&quot;,
        task.uid,
        updateAccountTaskDto.attachmentUrls,
        user.orgId,
        &#x60;${user.orgUid}/accounts/${task.account.uid}/tasks/${task.uid}&#x60;,
      );

      task.attachments &#x3D; [...(task.attachments ?? []), ...newAttachments];
    }

    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      // Save in db
      await this.accountTaskRepository.saveWithTxn(txnContext, task);

      // Record audit log
      await this.activitiesService.recordAuditLog({
        organization: { id: user.orgId },
        activityPerformedBy: { id: user.sub },
        entityId: task.id,
        entityType: AuditLogEntityType.ACCOUNT_TASK,
        op: AuditLogOp.UPDATED,
        visibility: AuditLogVisibility.ORGANIZATION,
        activity: &#x60;Account task ${task.id} for account ${task.accountId} was updated!&#x60;,
        description: &#x60;Account task ${task.id} for account ${task.accountId} was updated by ${user.email}!&#x60;,
        metadata: {
          updatedFields: Object.keys(updateAccountTaskDto).map((key) &#x3D;&gt; ({
            field: key,
            previousValue: previousTask[key],
            newValue: task[key],
          })),
        },
      });

      // Invalidate cache
      await this.cachedAccountTaskRepository.invalidateAccountTaskCache({
        uids: [taskId],
      });

      // Publish account task updated event to SNS
      const accountTaskUpdatedEvent &#x3D;
        this.accountsSNSEventsFactory.createAccountTaskUpdatedSNSEvent(
          user,
          previousTask,
          task,
        );

      this.accountCommonService.publishEventToSNSQueue(
        AccountEvents.ACCOUNT_TASK_UPDATED,
        accountTaskUpdatedEvent,
        user,
      );
    });

    return this.findAccountTask(taskId);
  }

  /**
   * Deletes an account task.
   *
   * @param user The current user
   * @param taskId The ID of the task
   * @returns The deleted account task
   */
  async deleteAccountTask(user: CurrentUser, taskId: string): Promise&lt;void&gt; {
    const task &#x3D; await this.findAccountTask(taskId);

    if (!task) {
      throw new NotFoundException(&quot;Task not found&quot;);
    }

    task.isActive &#x3D; false;

    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      // Save in db
      await this.accountTaskRepository.saveWithTxn(txnContext, task);

      // Record audit log
      await this.activitiesService.recordAuditLog({
        organization: { id: user.orgId },
        activityPerformedBy: { id: user.sub },
        entityId: task.id,
        entityType: AuditLogEntityType.ACCOUNT_TASK,
        op: AuditLogOp.DELETED,
        visibility: AuditLogVisibility.ORGANIZATION,
        activity: &#x60;Account task ${task.id} for account ${task.accountId} was deleted!&#x60;,
        description: &#x60;Account task ${task.id} for account ${task.accountId} was deleted by ${user.email}!&#x60;,
        metadata: {
          updatedFields: [
            {
              field: &quot;isActive&quot;,
              previousValue: &quot;true&quot;,
              updatedToValue: &quot;false&quot;,
            },
          ],
        },
      });

      // Invalidate cache
      await this.cachedAccountTaskRepository.invalidateAccountTaskCache({
        uids: [taskId],
      });

      // Publish account task deleted event to SNS
      const accountTaskDeletedEvent &#x3D;
        this.accountsSNSEventsFactory.createAccountTaskDeletedSNSEvent(
          user,
          task,
        );

      this.accountCommonService.publishEventToSNSQueue(
        AccountEvents.ACCOUNT_TASK_DELETED,
        accountTaskDeletedEvent,
        user,
      );
    });
  }

  /**
   * Remove an attachment from an account task
   *
   * @param user Current user
   * @param taskId The Task ID
   * @param attachmentId The Attachment ID
   */
  async removeTaskAttachment(
    user: CurrentUser,
    taskId: string,
    attachmentId: string,
  ): Promise&lt;void&gt; {
    const task &#x3D; await this.findAccountTask(taskId);
    if (!task) {
      throw new NotFoundException(&quot;Task not found!&quot;);
    }
    const previousTask &#x3D; cloneDeep(task);
    const existingAttachmentIds &#x3D; task.attachments?.map((a) &#x3D;&gt; a.uid);

    const attachment &#x3D; task.attachments?.find((a) &#x3D;&gt; a.uid &#x3D;&#x3D;&#x3D; attachmentId);
    if (!attachment) {
      throw new NotFoundException(&quot;Attachment not found!&quot;);
    }

    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      task.attachments &#x3D; task.attachments?.filter(
        (a) &#x3D;&gt; a.uid !&#x3D;&#x3D; attachmentId,
      );

      // Save in db
      await this.accountTaskRepository.saveWithTxn(txnContext, task);

      // Record audit log
      await this.activitiesService.recordAuditLog(
        {
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityId: task.id,
          entityUid: task.uid,
          entityType: AuditLogEntityType.ACCOUNT_TASK,
          op: AuditLogOp.UPDATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: &#x60;Attachment ${attachmentId} was removed from account task ${task.id}!&#x60;,
          description: &#x60;Attachment ${attachmentId} was removed from account task ${task.id} by ${user.email}!&#x60;,
          metadata: {
            updatedFields: [
              {
                field: &quot;attachments&quot;,
                previousValue: existingAttachmentIds?.join(&quot;,&quot;),
                updatedToValue: task.attachments?.map((a) &#x3D;&gt; a.uid).join(&quot;,&quot;),
              },
            ],
          },
        },
        txnContext,
      );

      // Publish account task deleted event to SNS
      const accountTaskUpdatedEvent &#x3D;
        this.accountsSNSEventsFactory.createAccountTaskUpdatedSNSEvent(
          user,
          previousTask,
          task,
        );

      this.accountCommonService.publishEventToSNSQueue(
        AccountEvents.ACCOUNT_TASK_UPDATED,
        accountTaskUpdatedEvent,
        user,
      );

      // Invalidate cache
      await this.cachedAccountTaskRepository.invalidateAccountTaskCache({
        uids: [task.uid],
      });
    });
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'AccountTaskActionService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
