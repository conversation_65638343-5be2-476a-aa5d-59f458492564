<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >ReactionsActionService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/communications/services/reactions.action.service.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#addReaction" >addReaction</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getCommentAndEmoji" >getCommentAndEmoji</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getEmojiByName" >getEmojiByName</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#removeReaction" >removeReaction</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(transactionService: TransactionService, logger: ILogger, reactionsRepository: ReactionsRepository, emojisRepository: EmojisRepository, commentsActionService: <a href="../injectables/CommentsActionService.html" target="_self">CommentsActionService</a>, commentRepository: CommentRepository)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="22" class="link-to-prism">src/communications/services/reactions.action.service.ts:22</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>transactionService</td>
                                                  
                                                        <td>
                                                                    <code>TransactionService</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>logger</td>
                                                  
                                                        <td>
                                                                    <code>ILogger</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>reactionsRepository</td>
                                                  
                                                        <td>
                                                                    <code>ReactionsRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>emojisRepository</td>
                                                  
                                                        <td>
                                                                    <code>EmojisRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>commentsActionService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/CommentsActionService.html" target="_self" >CommentsActionService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>commentRepository</td>
                                                  
                                                        <td>
                                                                    <code>CommentRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="addReaction"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>addReaction</b></span>
                        <a href="#addReaction"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>addReaction(commentId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, addReactionDto: <a href="../classes/AddReactionDto.html" target="_self">AddReactionDto</a>, currentUser: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="86"
                                    class="link-to-prism">src/communications/services/reactions.action.service.ts:86</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Adds a reaction to a comment</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>commentId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the comment</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>addReactionDto</td>
                                            <td>
                                                            <code><a href="../classes/AddReactionDto.html" target="_self" >AddReactionDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The DTO containing the emoji name</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>currentUser</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;boolean&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getCommentAndEmoji"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>getCommentAndEmoji</b></span>
                        <a href="#getCommentAndEmoji"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getCommentAndEmoji(commentId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, currentUser: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, emojiName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="57"
                                    class="link-to-prism">src/communications/services/reactions.action.service.ts:57</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Get the comment and emoji</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>commentId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the comment</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>currentUser</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>emojiName</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The name of the emoji</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The comment, emoji, and reaction count key</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getEmojiByName"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>getEmojiByName</b></span>
                        <a href="#getEmojiByName"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getEmojiByName(name: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="39"
                                    class="link-to-prism">src/communications/services/reactions.action.service.ts:39</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Get an emoji by name</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>name</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The name of the emoji</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The emoji and whether it is a global emoji</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="removeReaction"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>removeReaction</b></span>
                        <a href="#removeReaction"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>removeReaction(commentId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, reactionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, currentUser: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="179"
                                    class="link-to-prism">src/communications/services/reactions.action.service.ts:179</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Removes a reaction from a comment</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>commentId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the comment</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>reactionName</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>currentUser</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;boolean&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  ConflictException,
  Inject,
  Injectable,
  NotFoundException,
} from &quot;@nestjs/common&quot;;
import { ILogger } from &quot;@repo/nestjs-commons/logger&quot;;
import {
  CommentMetadata,
  CommentRepository,
  EmojisRepository,
  ReactionsRepository,
  TransactionService,
} from &quot;@repo/thena-platform-entities&quot;;
import { IsNull, QueryFailedError } from &quot;typeorm&quot;;
import { POSTGRES_ERROR_CODES } from &quot;../../common/constants/postgres-errors.constants&quot;;
import { CurrentUser } from &quot;../../common/decorators&quot;;
import { AddReactionDto } from &quot;../dto/reactions.dto&quot;;
import { CommentsActionService } from &quot;./comments.action.service&quot;;

@Injectable()
export class ReactionsActionService {
  constructor(
    private readonly transactionService: TransactionService,

    @Inject(&quot;CustomLogger&quot;) private readonly logger: ILogger,
    private readonly reactionsRepository: ReactionsRepository,
    private readonly emojisRepository: EmojisRepository,
    private readonly commentsActionService: CommentsActionService,
    private readonly commentRepository: CommentRepository,
  ) {}

  /**
   * Get an emoji by name
   * @param name The name of the emoji
   * @param orgId The ID of the organization
   * @returns The emoji and whether it is a global emoji
   */
  async getEmojiByName(name: string, orgId: string) {
    const reaction &#x3D; await this.emojisRepository.findByCondition({
      where: [
        { name, organizationId: IsNull() },
        { organizationId: orgId, name },
      ],
    });

    return { reaction, isGlobalEmoji: !reaction?.organizationId };
  }

  /**
   * Get the comment and emoji
   * @param commentId The ID of the comment
   * @param currentUser The current user
   * @param emojiName The name of the emoji
   * @returns The comment, emoji, and reaction count key
   */
  async getCommentAndEmoji(
    commentId: string,
    currentUser: CurrentUser,
    emojiName: string,
  ) {
    const orgId &#x3D; currentUser.orgId;

    // Get the comment and emoji
    const [comment, emojiResult] &#x3D; await Promise.all([
      this.commentsActionService.getPopulatedCommentById(commentId, orgId),
      this.getEmojiByName(emojiName, orgId),
    ]);

    // If the comment is not found, throw an error
    if (!comment) throw new NotFoundException(&quot;Comment not found!&quot;);

    // If the emoji is not found, throw an error
    const { reaction } &#x3D; emojiResult;
    if (!reaction) throw new NotFoundException(&quot;Emoji not found!&quot;);

    return { comment, reaction };
  }

  /**
   * Adds a reaction to a comment
   * @param commentId The ID of the comment
   * @param addReactionDto The DTO containing the emoji name
   * @param currentUser The current user
   */
  async addReaction(
    commentId: string,
    addReactionDto: AddReactionDto,
    currentUser: CurrentUser,
  ): Promise&lt;boolean&gt; {
    const orgId &#x3D; currentUser.orgId;

    // Get the comment and emoji
    const { comment, reaction } &#x3D; await this.getCommentAndEmoji(
      commentId,
      currentUser,
      addReactionDto.name,
    );

    // Perform the transaction for adding the reaction
    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      try {
        // Create the reaction
        await this.reactionsRepository.createReaction(
          {
            emojiId: reaction.id,
            commentId: comment.id,
            organizationId: orgId,
            reactionByUser: currentUser.sub,
          },
          txnContext,
        );

        // Update the users array
        const MAX_USERS_SHOWN &#x3D; 5;
        const users &#x3D; comment.metadata?.reactions?.[reaction.name]?.users || [];
        const filteredUsers &#x3D; users.filter(
          (userId) &#x3D;&gt; userId !&#x3D;&#x3D; currentUser.uid,
        );

        // If the users array is longer than the max, remove the last user
        if (users.length &gt; MAX_USERS_SHOWN) {
          filteredUsers.pop();
        }

        // Always expose public user id
        filteredUsers.unshift(currentUser.uid);

        // Get the current count
        const currentCount &#x3D;
          comment.metadata?.reactions?.[reaction.name]?.count || 0;

        // Create the metadata
        const metadata: CommentMetadata &#x3D; {
          ...comment.metadata,
          reactions: {
            ...comment.metadata?.reactions,
            [reaction.name]: {
              count: currentCount + 1,
              users: filteredUsers,
            },
          },
        };

        // Update the comment metadata
        const commentCriteria &#x3D; { id: comment.id, organizationId: orgId };
        await this.commentRepository.updateWithTxn(
          txnContext,
          commentCriteria,
          { metadata },
        );
      } catch (error) {
        if (error instanceof QueryFailedError) {
          if (error.code &#x3D;&#x3D;&#x3D; POSTGRES_ERROR_CODES.DUPLICATE_KEY_VALUE) {
            throw new ConflictException(
              &quot;You have already reacted to this comment!&quot;,
            );
          }
        }

        this.logger.error(
          &#x60;Failed to add reaction to comment: ${commentId}&#x60;,
          error,
        );

        throw error;
      }
    });

    return true;
  }

  /**
   * Removes a reaction from a comment
   * @param commentId The ID of the comment
   * @param addReactionDto The DTO containing the emoji name
   * @param currentUser The current user
   */
  async removeReaction(
    commentId: string,
    reactionName: string,
    currentUser: CurrentUser,
  ): Promise&lt;boolean&gt; {
    const orgId &#x3D; currentUser.orgId;

    // Get the comment and emoji
    const { comment, reaction } &#x3D; await this.getCommentAndEmoji(
      commentId,
      currentUser,
      reactionName,
    );

    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      try {
        // Delete the reaction
        const userReaction &#x3D; await this.reactionsRepository.findByCondition({
          where: {
            commentId: comment.id,
            emojiId: reaction.id,
            organizationId: orgId,
            reactionByUser: currentUser.sub,
          },
        });

        // If the user reaction is not found, throw an error
        if (!userReaction) {
          throw new NotFoundException(&quot;You have not reacted to this comment!&quot;);
        }

        await this.reactionsRepository.remove(userReaction);

        // Update the users array
        const users &#x3D; comment.metadata?.reactions?.[reaction.name]?.users || [];
        const updatedUsers &#x3D; users.filter(
          (userId) &#x3D;&gt; userId !&#x3D;&#x3D; currentUser.uid,
        );

        const currentCount &#x3D;
          comment.metadata?.reactions?.[reaction.name]?.count || 0;

        const newCount &#x3D; currentCount - 1;

        // Create the metadata
        const metadata: CommentMetadata &#x3D; {
          ...comment.metadata,
          reactions: {
            ...comment.metadata?.reactions,
          },
        };

        // If the count is greater than 0, update the count
        if (newCount &gt; 0) {
          metadata.reactions[reaction.name] &#x3D; {
            count: newCount,
            users: updatedUsers,
          };
        } else {
          delete metadata.reactions[reaction.name];
        }

        // Update the comment metadata
        const commentCriteria &#x3D; { id: comment.id, organizationId: orgId };
        await this.commentRepository.updateWithTxn(
          txnContext,
          commentCriteria,
          { metadata },
        );
      } catch (error) {
        this.logger.error(
          &#x60;Failed to remove reaction from comment: ${commentId}&#x60;,
          error,
        );

        throw error;
      }
    });

    return true;
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'ReactionsActionService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
