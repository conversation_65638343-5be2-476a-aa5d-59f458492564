<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >CustomObjectService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/custom-object/services/custom-object.service.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#create" >create</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#fetchPaginatedResults" >fetchPaginatedResults</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findByIds" >findByIds</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findByIdsWithTeamCheck" >findByIdsWithTeamCheck</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#search" >search</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#update" >update</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(logger: ILogger, customObjectRepository: CustomObjectRepository)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="17" class="link-to-prism">src/custom-object/services/custom-object.service.ts:17</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>logger</td>
                                                  
                                                        <td>
                                                                    <code>ILogger</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>customObjectRepository</td>
                                                  
                                                        <td>
                                                                    <code>CustomObjectRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="create"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>create</b></span>
                        <a href="#create"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>create(orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, createCustomObjectDto: <a href="../classes/CreateCustomObjectDto.html" target="_self">CreateCustomObjectDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="24"
                                    class="link-to-prism">src/custom-object/services/custom-object.service.ts:24</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>userId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>createCustomObjectDto</td>
                                            <td>
                                                            <code><a href="../classes/CreateCustomObjectDto.html" target="_self" >CreateCustomObjectDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="fetchPaginatedResults"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>fetchPaginatedResults</b></span>
                        <a href="#fetchPaginatedResults"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>fetchPaginatedResults(orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, limit: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>, offset: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>, teamId?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, onlyTeamFields?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank">boolean</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="205"
                                    class="link-to-prism">src/custom-object/services/custom-object.service.ts:205</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds all custom fields.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>limit</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>offset</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>onlyTeamFields</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>All custom fields.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findByIds"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findByIds</b></span>
                        <a href="#findByIds"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findByIds(orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, ids: string[])</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="102"
                                    class="link-to-prism">src/custom-object/services/custom-object.service.ts:102</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>ids</td>
                                            <td>
                                                        <code>string[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findByIdsWithTeamCheck"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findByIdsWithTeamCheck</b></span>
                        <a href="#findByIdsWithTeamCheck"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findByIdsWithTeamCheck(orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, ids: string[], teamIds?: string[])</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="118"
                                    class="link-to-prism">src/custom-object/services/custom-object.service.ts:118</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds custom objects by ids.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>ids</td>
                                            <td>
                                                        <code>string[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>Array of custom object IDs to fetch</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>teamIds</td>
                                            <td>
                                                        <code>string[]</code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>Custom objects matching the provided IDs</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="search"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>search</b></span>
                        <a href="#search"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>search(orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, term: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, teamId?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, onlyTeamFields?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank">boolean</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="165"
                                    class="link-to-prism">src/custom-object/services/custom-object.service.ts:165</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Search custom fields by name.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>term</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>term to search</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>onlyTeamFields</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>Custom fields matching the provided term</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="update"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>update</b></span>
                        <a href="#update"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>update(userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, updateCustomObjectDto: <a href="../classes/UpdateCustomObjectDto.html" target="_self">UpdateCustomObjectDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="62"
                                    class="link-to-prism">src/custom-object/services/custom-object.service.ts:62</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>userId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>updateCustomObjectDto</td>
                                            <td>
                                                            <code><a href="../classes/UpdateCustomObjectDto.html" target="_self" >UpdateCustomObjectDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { BadRequestException, Inject, Injectable } from &quot;@nestjs/common&quot;;
import { ILogger } from &quot;@repo/nestjs-commons/logger&quot;;
import {
  CustomObjectRepository,
  CustomValidationError,
} from &quot;@repo/thena-platform-entities&quot;;
import * as _ from &quot;lodash&quot;;
import { IdGeneratorUtils } from &quot;src/common/utils/id-generator.utils&quot;;
import { ILike, In, IsNull } from &quot;typeorm&quot;;
import { GetObjectRelations } from &quot;../constants&quot;;
import {
  CreateCustomObjectDto,
  UpdateCustomObjectDto,
} from &quot;../dto/custom-object.dto&quot;;

@Injectable()
export class CustomObjectService {
  constructor(
    @Inject(&quot;CustomLogger&quot;)
    private readonly logger: ILogger,
    private customObjectRepository: CustomObjectRepository,
  ) {}

  async create(
    orgId: string,
    userId: string,
    createCustomObjectDto: CreateCustomObjectDto,
  ) {
    const uid &#x3D; IdGeneratorUtils.generate(&quot;CO&quot;);
    // eslint-disable-next-line max-len
    const customObject &#x3D; this.customObjectRepository.create({
      organization: {
        id: orgId,
      },
      organizationId: orgId,
      name: createCustomObjectDto.name,
      uid,
      team: createCustomObjectDto.teamId
        ? {
            id: createCustomObjectDto.teamId,
          }
        : undefined,
      teamId: createCustomObjectDto.teamId,
      createdBy: {
        id: userId,
      },
      version: 1,
    });

    let res;
    try {
      res &#x3D; await this.customObjectRepository.save(customObject);
    } catch (error) {
      if (error instanceof CustomValidationError) {
        throw new BadRequestException(error.message);
      }
      throw error;
    }
    return res;
  }

  async update(
    userId: string,
    organizationId: string,
    updateCustomObjectDto: UpdateCustomObjectDto,
  ) {
    if (
      !updateCustomObjectDto.objects ||
      updateCustomObjectDto.objects.length &#x3D;&#x3D;&#x3D; 0
    ) {
      throw new BadRequestException(&quot;No objects to update&quot;);
    }

    const promises &#x3D; updateCustomObjectDto.objects.map(async (update) &#x3D;&gt; {
      await this.customObjectRepository.update(
        { organizationId, uid: update.id, version: update.version },
        {
          ...update.updates,
          version: update.version + 1,
          updatedBy: { id: userId },
        },
      );
    });

    const results &#x3D; await Promise.allSettled(promises);

    const failedUpdates &#x3D; results
      .map((result, index) &#x3D;&gt;
        result.status &#x3D;&#x3D;&#x3D; &quot;rejected&quot;
          ? updateCustomObjectDto.objects[index].id
          : null,
      )
      .filter((id) &#x3D;&gt; id !&#x3D;&#x3D; null);

    if (failedUpdates.length &gt; 0) {
      throw new BadRequestException(
        &#x60;Failed to update the following IDs: ${failedUpdates.join(&quot;, &quot;)}&#x60;,
      );
    }
  }

  async findByIds(orgId: string, ids: string[]) {
    const customObjects &#x3D; await this.customObjectRepository.findAll({
      relations: GetObjectRelations,
      where: { organizationId: orgId, uid: In(ids), isDeleted: false },
    });
    return {
      items: customObjects,
    };
  }

  /**
   * Finds custom objects by ids.
   * @param request The FastifyRequest object containing user info
   * @param ids Array of custom object IDs to fetch
   * @returns Custom objects matching the provided IDs
   */
  async findByIdsWithTeamCheck(
    orgId: string,
    ids: string[],
    teamIds?: string[],
  ) {
    teamIds &#x3D; teamIds.filter(Boolean);
    const query &#x3D; [
      {
        organizationId: orgId,
        uid: In(ids),
        isDeleted: false,
        teamId: IsNull(),
      },
    ];

    if (teamIds?.length &gt; 0) {
      query.push({
        organizationId: orgId,
        uid: In(ids),
        isDeleted: false,
        teamId: In(teamIds),
      });
    }
    const customObjects &#x3D; await this.customObjectRepository.findAll({
      relations: GetObjectRelations,
      where: query,
    });
    if (customObjects.length !&#x3D;&#x3D; ids.length) {
      const missingObjects &#x3D; ids.filter(
        (id) &#x3D;&gt; !customObjects.some((object) &#x3D;&gt; object.uid &#x3D;&#x3D;&#x3D; id),
      );
      throw new BadRequestException(
        &#x60;Some objects not found, either they are deleted or not found in your teams, missing objects: ${missingObjects.join(
          &quot;, &quot;,
        )}&#x60;,
      );
    }
    return {
      items: customObjects,
    };
  }

  /**
   * Search custom fields by name.
   * @param term term to search
   * @returns Custom fields matching the provided term
   */
  async search(
    orgId: string,
    term: string,
    teamId?: string,
    onlyTeamFields?: boolean,
  ) {
    if (_.isEmpty(term?.trim())) {
      throw new BadRequestException(&quot;No search term provided&quot;);
    }
    const query &#x3D; [];
    if (teamId) {
      query.push({
        name: ILike(&#x60;%${term.toLocaleLowerCase()}%&#x60;),
        organizationId: orgId,
        isDeleted: false,
        teamId: teamId,
      });
    }

    if (!teamId || !onlyTeamFields) {
      query.push({
        name: ILike(&#x60;%${term.toLocaleLowerCase()}%&#x60;),
        organizationId: orgId,
        isDeleted: false,
        teamId: IsNull(),
      });
    }
    const customObjects &#x3D; await this.customObjectRepository.findAll({
      relations: GetObjectRelations,
      where: query,
    });
    return {
      items: customObjects,
    };
  }

  /**
   * Finds all custom fields.
   * @returns All custom fields.
   */
  async fetchPaginatedResults(
    orgId: string,
    limit: number,
    offset: number,
    teamId?: string,
    onlyTeamFields?: boolean,
  ) {
    const query &#x3D; [];
    if (teamId) {
      query.push({
        organizationId: orgId,
        isDeleted: false,
        teamId: teamId,
      });
    }

    if (!teamId || !onlyTeamFields) {
      query.push({
        organizationId: orgId,
        isDeleted: false,
        teamId: IsNull(),
      });
    }

    if (query.length &#x3D;&#x3D;&#x3D; 0) {
      throw new BadRequestException(&quot;No fields to fetch&quot;);
    }

    const customObjects &#x3D;
      await this.customObjectRepository.fetchPaginatedResults(
        { limit, page: offset },
        {
          where: query,
        },
      );

    return {
      ...customObjects,
      offset: (customObjects.results || []).length &gt; 0 ? ++offset : undefined,
    };
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'CustomObjectService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
