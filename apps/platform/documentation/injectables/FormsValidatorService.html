<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >FormsValidatorService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/forms/validators/form.validator.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#applyConditions" >applyConditions</a>
                            </li>
                            <li>
                                <a href="#buildFormFieldValuesStructureFromTicket" >buildFormFieldValuesStructureFromTicket</a>
                            </li>
                            <li>
                                <a href="#getThenaRestrictedFieldIdToTicketKeyMap" >getThenaRestrictedFieldIdToTicketKeyMap</a>
                            </li>
                            <li>
                                <a href="#isValidTrigger" >isValidTrigger</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#validateBatchDeletePayload" >validateBatchDeletePayload</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#validateCreateFormPayload" >validateCreateFormPayload</a>
                            </li>
                            <li>
                                <a href="#validateFormConditions" >validateFormConditions</a>
                            </li>
                            <li>
                                <a href="#validateFormFields" >validateFormFields</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#validateFormValues" >validateFormValues</a>
                            </li>
                            <li>
                                <a href="#validateTeamFields" >validateTeamFields</a>
                            </li>
                            <li>
                                <a href="#validateThenaRestrictedFieldValues" >validateThenaRestrictedFieldValues</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#validateUpdatePayload" >validateUpdatePayload</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(logger: ILogger, formRepository: FormRepository, customFieldService: <a href="../injectables/CustomFieldService.html" target="_self">CustomFieldService</a>, thenaRestrictedFieldsService: <a href="../injectables/ThenaRestrictedFieldService.html" target="_self">ThenaRestrictedFieldService</a>, customFieldValidatorService: <a href="../injectables/CustomFieldvalidatorService.html" target="_self">CustomFieldvalidatorService</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="25" class="link-to-prism">src/forms/validators/form.validator.ts:25</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>logger</td>
                                                  
                                                        <td>
                                                                    <code>ILogger</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>formRepository</td>
                                                  
                                                        <td>
                                                                    <code>FormRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>customFieldService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/CustomFieldService.html" target="_self" >CustomFieldService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>thenaRestrictedFieldsService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/ThenaRestrictedFieldService.html" target="_self" >ThenaRestrictedFieldService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>customFieldValidatorService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/CustomFieldvalidatorService.html" target="_self" >CustomFieldvalidatorService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="applyConditions"></a>
                    <span class="name">
                        <span ><b>applyConditions</b></span>
                        <a href="#applyConditions"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>applyConditions(form: Form, body: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, formFieldValues: Map<string | any>, thenaRestrictedIdToKeyMap: Map<string | string>, teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="890"
                                    class="link-to-prism">src/forms/validators/form.validator.ts:890</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>form</td>
                                            <td>
                                                        <code>Form</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>body</td>
                                            <td>
                                                            <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>formFieldValues</td>
                                            <td>
                                                        <code>Map&lt;string | any&gt;</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>thenaRestrictedIdToKeyMap</td>
                                            <td>
                                                        <code>Map&lt;string | string&gt;</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="buildFormFieldValuesStructureFromTicket"></a>
                    <span class="name">
                        <span ><b>buildFormFieldValuesStructureFromTicket</b></span>
                        <a href="#buildFormFieldValuesStructureFromTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>buildFormFieldValuesStructureFromTicket(values: <a href="../classes/ExternalCustomFieldValuesDto.html" target="_self">ExternalCustomFieldValuesDto[]</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="800"
                                    class="link-to-prism">src/forms/validators/form.validator.ts:800</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>values</td>
                                            <td>
                                                            <code><a href="../classes/ExternalCustomFieldValuesDto.html" target="_self" >ExternalCustomFieldValuesDto[]</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getThenaRestrictedFieldIdToTicketKeyMap"></a>
                    <span class="name">
                        <span ><b>getThenaRestrictedFieldIdToTicketKeyMap</b></span>
                        <a href="#getThenaRestrictedFieldIdToTicketKeyMap"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getThenaRestrictedFieldIdToTicketKeyMap(thenaRestrictedFields: ThenaRestrictedField[])</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="687"
                                    class="link-to-prism">src/forms/validators/form.validator.ts:687</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>thenaRestrictedFields</td>
                                            <td>
                                                        <code>ThenaRestrictedField[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isValidTrigger"></a>
                    <span class="name">
                        <span ><b>isValidTrigger</b></span>
                        <a href="#isValidTrigger"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>isValidTrigger(triggerFieldValues: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, formFieldValue: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, conditionType: TargetFieldConditionsType)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="1027"
                                    class="link-to-prism">src/forms/validators/form.validator.ts:1027</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>triggerFieldValues</td>
                                            <td>
                                                            <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>formFieldValue</td>
                                            <td>
                                                            <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>conditionType</td>
                                            <td>
                                                        <code>TargetFieldConditionsType</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateBatchDeletePayload"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>validateBatchDeletePayload</b></span>
                        <a href="#validateBatchDeletePayload"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateBatchDeletePayload(orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, payload: <a href="../classes/DeleteFormsDto.html" target="_self">DeleteFormsDto</a>, teamIds: string[])</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="170"
                                    class="link-to-prism">src/forms/validators/form.validator.ts:170</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Validate batch delete form payload</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>payload</td>
                                            <td>
                                                            <code><a href="../classes/DeleteFormsDto.html" target="_self" >DeleteFormsDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>teamIds</td>
                                            <td>
                                                        <code>string[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>validated object</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateCreateFormPayload"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>validateCreateFormPayload</b></span>
                        <a href="#validateCreateFormPayload"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateCreateFormPayload(orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, createFormDto: <a href="../classes/CreateFormDto.html" target="_self">CreateFormDto</a>, teamsMap: Record<string | string>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="35"
                                    class="link-to-prism">src/forms/validators/form.validator.ts:35</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>createFormDto</td>
                                            <td>
                                                            <code><a href="../classes/CreateFormDto.html" target="_self" >CreateFormDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>teamsMap</td>
                                            <td>
                                                        <code>Record&lt;string | string&gt;</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateFormConditions"></a>
                    <span class="name">
                        <span ><b>validateFormConditions</b></span>
                        <a href="#validateFormConditions"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>validateFormConditions(undefined)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="432"
                                    class="link-to-prism">src/forms/validators/form.validator.ts:432</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>{}</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateFormFields"></a>
                    <span class="name">
                        <span ><b>validateFormFields</b></span>
                        <a href="#validateFormFields"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>validateFormFields(undefined)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="583"
                                    class="link-to-prism">src/forms/validators/form.validator.ts:583</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>{ updatedFields: {}; }</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateFormValues"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>validateFormValues</b></span>
                        <a href="#validateFormValues"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateFormValues(orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, form: Form, values: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, isTicketClosing?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank">boolean</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="825"
                                    class="link-to-prism">src/forms/validators/form.validator.ts:825</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>form</td>
                                            <td>
                                                        <code>Form</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>values</td>
                                            <td>
                                                            <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>isTicketClosing</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateTeamFields"></a>
                    <span class="name">
                        <span ><b>validateTeamFields</b></span>
                        <a href="#validateTeamFields"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>validateTeamFields(fields: CustomField[], teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="1064"
                                    class="link-to-prism">src/forms/validators/form.validator.ts:1064</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>fields</td>
                                            <td>
                                                        <code>CustomField[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateThenaRestrictedFieldValues"></a>
                    <span class="name">
                        <span ><b>validateThenaRestrictedFieldValues</b></span>
                        <a href="#validateThenaRestrictedFieldValues"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>validateThenaRestrictedFieldValues(form: Form, thenaRestrictedFields: ThenaRestrictedField[], values: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="730"
                                    class="link-to-prism">src/forms/validators/form.validator.ts:730</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>form</td>
                                            <td>
                                                        <code>Form</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>thenaRestrictedFields</td>
                                            <td>
                                                        <code>ThenaRestrictedField[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>values</td>
                                            <td>
                                                            <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateUpdatePayload"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>validateUpdatePayload</b></span>
                        <a href="#validateUpdatePayload"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateUpdatePayload(orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, payload: <a href="../classes/UpdateFormDto.html" target="_self">UpdateFormDto</a>, teamsMap: Record<string | string>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="229"
                                    class="link-to-prism">src/forms/validators/form.validator.ts:229</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>payload</td>
                                            <td>
                                                            <code><a href="../classes/UpdateFormDto.html" target="_self" >UpdateFormDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>teamsMap</td>
                                            <td>
                                                        <code>Record&lt;string | string&gt;</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { BadRequestException, Inject, Injectable } from &quot;@nestjs/common&quot;;
import { ILogger } from &quot;@repo/nestjs-commons/logger&quot;;
import {
  CustomField,
  FieldWithOptions,
  Form,
  FormFieldTriggers,
  FormFieldType,
  FormRepository,
  TargetFieldConditionsType,
  ThenaRestrictedField,
} from &quot;@repo/thena-platform-entities&quot;;
import { ILike, In } from &quot;typeorm&quot;;
import { ExternalCustomFieldValuesDto } from &quot;../../custom-field/dto&quot;;
import { CustomFieldService } from &quot;../../custom-field/services/custom-field.service&quot;;
import { ThenaRestrictedFieldService } from &quot;../../custom-field/services/thena-restricted-field.service&quot;;
import { CustomFieldvalidatorService } from &quot;../../custom-field/validators/custom-field.validator&quot;;
import {
  THENA_RESTRICTED_FIELDS,
  UpdatableThenaRestrictedFieldsProperties,
} from &quot;../constants&quot;;
import { CreateFormDto, DeleteFormsDto, UpdateFormDto } from &quot;../dto/form.dto&quot;;

@Injectable()
export class FormsValidatorService {
  constructor(
    @Inject(&quot;CustomLogger&quot;)
    private readonly logger: ILogger,
    private formRepository: FormRepository,
    private customFieldService: CustomFieldService,
    private thenaRestrictedFieldsService: ThenaRestrictedFieldService,
    private customFieldValidatorService: CustomFieldvalidatorService,
  ) {}

  async validateCreateFormPayload(
    orgId: string,
    createFormDto: CreateFormDto,
    teamsMap: Record&lt;string, string&gt;,
  ) {
    const teamId &#x3D; createFormDto.teamId;
    if (teamId) {
      const existingFieldsInTeam &#x3D; await this.formRepository.findAll({
        where: {
          organizationId: orgId,
          name: createFormDto.name,
          isDeleted: false,
          teamId: teamId,
        },
      });

      if (existingFieldsInTeam?.length &gt; 0) {
        throw new BadRequestException(
          &#x60;Form with the name &quot;${createFormDto.name}&quot; already exists in the team.&#x60;,
        );
      }
    }

    const thenaRestrictedFields &#x3D;
      await this.thenaRestrictedFieldsService.getAllFields();
    const thenaRestrictedFieldIdToValueMap &#x3D; new Map(
      thenaRestrictedFields.map((field) &#x3D;&gt; [field.uid, field]),
    );
    const existingForm &#x3D; await this.formRepository
      .findByCondition({
        where: {
          organizationId: orgId,
          name: ILike(createFormDto.name.toLocaleLowerCase()),
        },
      })
      .catch((err) &#x3D;&gt; {
        this.logger.error(
          &#x60;Error checking existing forms: ${err.message}, stack: ${err.stack}&#x60;,
        );
        throw new BadRequestException(&quot;Error checking existing forms&quot;);
      });

    if (existingForm) {
      throw new BadRequestException(
        &#x60;Form with name &#x27;${createFormDto.name}&#x27; already exists in your organization&#x60;,
      );
    }

    const fieldIds &#x3D; (createFormDto.fields || []).map((field) &#x3D;&gt; field.field);
    const fieldIdsSet &#x3D; new Set(fieldIds);

    if (fieldIdsSet.size !&#x3D;&#x3D; fieldIds.length) {
      throw new BadRequestException(
        &#x60;Duplicate field IDs found in the payload: ${fieldIds.join(&quot;, &quot;)}&#x60;,
      );
    }

    const allDefaultFields &#x3D; await this.customFieldService.getDefaultFields(
      orgId,
      createFormDto.teamId,
    );
    const defaultFields &#x3D; allDefaultFields.filter(
      (field) &#x3D;&gt; field.teamId &#x3D;&#x3D;&#x3D; teamId || field.teamId &#x3D;&#x3D;&#x3D; null,
    );

    if (!createFormDto.fields || createFormDto.fields.length &#x3D;&#x3D;&#x3D; 0) {
      createFormDto.fields &#x3D; [];
    }

    for (const field of defaultFields) {
      if (!fieldIdsSet.has(field.uid)) {
        fieldIds.push(field.uid);
        createFormDto.fields.push({
          field: field.uid,
          fieldType: FormFieldType.CUSTOM,
          mandatoryOnCreation: field.mandatoryOnCreation,
          mandatoryOnClose: field.mandatoryOnClose,
          visibleToCustomer: field.visibleToCustomer,
          editableByCustomer: field.editableByCustomer,
        });
      }
    }

    let updatedFields;
    let conditions;
    let fieldsMap &#x3D; new Map();

    if (createFormDto.fields &amp;&amp; createFormDto.fields.length &gt; 0) {
      const fields &#x3D; await this.customFieldService.findByIdsWithTeamCheck(
        orgId,
        fieldIds,
        [createFormDto.teamId],
      );

      this.validateTeamFields(fields.items, createFormDto.teamId);
      const missingFields &#x3D; fieldIds.filter(
        (id) &#x3D;&gt; !fields.items.some((field) &#x3D;&gt; field.uid &#x3D;&#x3D;&#x3D; id),
      );

      if (missingFields.length &gt; 0) {
        throw new BadRequestException(
          &#x60;Fields not found: ${missingFields.join(
            &quot;, &quot;,
          )}. Check if fields are deleted&#x60;,
        );
      }

      fieldsMap &#x3D; new Map(fields.items.map((field) &#x3D;&gt; [field.uid, field]));
      updatedFields &#x3D; this.validateFormFields({
        fields: createFormDto.fields,
        workspaceFieldsMap: fieldsMap,
        thenaRestrictedFieldsMap: thenaRestrictedFieldIdToValueMap,
      })?.updatedFields;
    }

    if (createFormDto.conditions &amp;&amp; createFormDto.conditions.length &gt; 0) {
      conditions &#x3D; this.validateFormConditions({
        existingForm,
        conditions: createFormDto.conditions,
        workspaceFieldsMap: fieldsMap,
        thenaRestrictedFieldsMap: thenaRestrictedFieldIdToValueMap,
        teamsMap,
      });
    }

    return {
      fields: updatedFields,
      conditions,
    };
  }

  /**
   * Validate batch delete form payload
   * @returns validated object
   */
  async validateBatchDeletePayload(
    orgId: string,
    payload: DeleteFormsDto,
    teamIds: string[],
  ) {
    const formIds &#x3D; payload.forms.map((form) &#x3D;&gt; form.formId);
    const existingForms &#x3D; await this.formRepository.findAll({
      where: {
        organizationId: orgId,
        uid: In(formIds),
        isDeleted: false,
      },
    });

    if (existingForms.length !&#x3D;&#x3D; formIds.length) {
      const foundFormIds &#x3D; new Set(existingForms.map((f) &#x3D;&gt; f.uid));
      const missingForms &#x3D; formIds.filter((id) &#x3D;&gt; !foundFormIds.has(id));
      throw new BadRequestException(
        &#x60;Forms not found: ${missingForms.join(&quot;, &quot;)}&#x60;,
      );
    }

    for (const form of existingForms) {
      if (form.teamId &amp;&amp; !teamIds.includes(form.teamId)) {
        throw new BadRequestException(
          &#x60;You are trying to delete forms of a team that you are not a part of, formId: ${form.uid}&#x60;,
        );
      }
    }

    const defaultForm &#x3D; existingForms.find((form) &#x3D;&gt; form.default);
    if (defaultForm) {
      throw new BadRequestException(
        &#x60;Cannot delete default form, default form: ${defaultForm.uid}&#x60;,
      );
    }

    const modifieldForms &#x3D; [];
    for (const form of payload.forms) {
      const existingForm &#x3D; existingForms.find((ef) &#x3D;&gt; ef.uid &#x3D;&#x3D;&#x3D; form.formId);

      if (existingForm.version !&#x3D;&#x3D; form.version) {
        modifieldForms.push(form.formId);
      }
    }

    if (modifieldForms.length &gt; 0) {
      throw new BadRequestException(
        &#x60;Some forms have been modified, Please try updating the latest version. Modified forms: ${modifieldForms.join(
          &quot;, &quot;,
        )}&#x60;,
      );
    }

    return {
      ...payload,
    };
  }

  async validateUpdatePayload(
    orgId: string,
    payload: UpdateFormDto,
    teamsMap: Record&lt;string, string&gt;,
  ) {
    const teamIds &#x3D; Object.keys(teamsMap);

    const formId &#x3D; payload.formId;
    if (payload.updates?.name?.trim() &#x3D;&#x3D;&#x3D; &quot;&quot;) {
      throw new BadRequestException(&quot;Form name cannot be empty&quot;);
    }

    if (payload.updates.default &#x3D;&#x3D;&#x3D; false) {
      throw new BadRequestException(&quot;Cannot mark form as not default&quot;);
    }

    const [existingForm] &#x3D; await Promise.all([
      this.formRepository.findByCondition({
        where: {
          organizationId: orgId,
          uid: formId,
          isDeleted: false,
        },
      }),
    ]);

    if (!existingForm) {
      throw new BadRequestException(&#x60;Forms not found&#x60;);
    }

    if (existingForm.teamId &amp;&amp; !teamIds.includes(existingForm.teamId)) {
      throw new BadRequestException(
        &#x60;You cannot update form ${existingForm.uid} as it is not in your teams&#x60;,
      );
    }

    if (existingForm.version !&#x3D;&#x3D; payload.version) {
      throw new BadRequestException(
        &#x60;Form has been modified, please update the latest version&#x60;,
      );
    }

    if (payload.updates.name &amp;&amp; payload.updates.name !&#x3D;&#x3D; existingForm.name) {
      const formWithSameName &#x3D; await this.formRepository.findByCondition({
        where: {
          organizationId: orgId,
          name: ILike(payload.updates.name.toLocaleLowerCase()),
        },
      });
      if (formWithSameName) {
        throw new BadRequestException(
          &quot;Form with the same name already exists for this organization&quot;,
        );
      }
    }

    const thenaRestrictedFields &#x3D;
      await this.thenaRestrictedFieldsService.getAllFields();
    const thenaRestrictedFieldIds &#x3D; new Set(
      thenaRestrictedFields.map((field) &#x3D;&gt; field.uid),
    );

    const existingCustomFieldIds &#x3D; new Set();
    const newCustomFieldIds &#x3D; new Set();
    let customFieldIdToValueMap &#x3D; new Map();
    let thenaRestrictedFieldIdToValueMap &#x3D; new Map();

    for (const field of existingForm.fields || []) {
      if (
        field.fieldType &#x3D;&#x3D;&#x3D; FormFieldType.CUSTOM ||
        !thenaRestrictedFieldIds.has(field.field)
      ) {
        existingCustomFieldIds.add(field.field);
      }
    }

    for (const field of payload.updates.fields || []) {
      if (!thenaRestrictedFieldIds.has(field.field)) {
        newCustomFieldIds.add(field.field);
      }
    }

    const allCustomFieldIds &#x3D; [
      ...new Set([...existingCustomFieldIds, ...newCustomFieldIds]),
    ];

    const customFields &#x3D; await this.customFieldService.findByIdsWithTeamCheck(
      orgId,
      allCustomFieldIds as string[],
      [existingForm.teamId],
    );

    this.validateTeamFields(customFields.items, existingForm.teamId);

    if (customFields.items.length !&#x3D;&#x3D; allCustomFieldIds.length) {
      const missingFields &#x3D; allCustomFieldIds.filter(
        (field) &#x3D;&gt; !customFields.items.some((f) &#x3D;&gt; f.uid &#x3D;&#x3D;&#x3D; field),
      );
      throw new BadRequestException(
        &#x60;Some fields not found in the organization: ${missingFields.join(
          &quot;, &quot;,
        )}&#x60;,
      );
    }

    customFieldIdToValueMap &#x3D; new Map(
      customFields.items.map((field) &#x3D;&gt; [field.uid, field]),
    );

    thenaRestrictedFieldIdToValueMap &#x3D; new Map(
      thenaRestrictedFields.map((field) &#x3D;&gt; [field.uid, field]),
    );

    if (payload.updates.fields) {
      const fieldsUsedInConditions &#x3D; new Set();
      const conditions &#x3D;
        payload.updates.conditions || existingForm.conditions || [];
      for (const condition of conditions) {
        fieldsUsedInConditions.add(condition.triggerFieldId);
        for (const target of condition.targetFields) {
          fieldsUsedInConditions.add(target.id);
        }
      }

      const fieldIds &#x3D; payload.updates.fields.map((field) &#x3D;&gt; field.field);
      const fieldIdsSet &#x3D; new Set(fieldIds);
      if (fieldIdsSet.size !&#x3D;&#x3D; fieldIds.length) {
        throw new BadRequestException(
          &#x60;Duplicate field IDs found in the payload for form: ${
            payload.formId
          }, field IDs: ${fieldIds.join(&quot;, &quot;)}&#x60;,
        );
      }

      for (const fieldId of thenaRestrictedFieldIds) {
        if (!fieldIds.includes(fieldId)) {
          throw new BadRequestException(
            &#x60;Field ${fieldId} is a thena restricted field and cannot be removed from the form&#x60;,
          );
        }
      }

      for (const field of customFields?.items || []) {
        if (field.autoAddToAllForms &amp;&amp; !newCustomFieldIds.has(field.uid)) {
          throw new BadRequestException(
            &#x60;Field ${field.uid} is an auto add field and must be present in the form&#x60;,
          );
        }
      }

      for (const fieldId of newCustomFieldIds) {
        if (!customFieldIdToValueMap.has(fieldId as string)) {
          throw new BadRequestException(
            &#x60;Field ${fieldId} not found in the organization&#x60;,
          );
        }
      }

      for (const fieldId of fieldsUsedInConditions) {
        if (
          !thenaRestrictedFieldIdToValueMap.has(fieldId as string) &amp;&amp;
          !newCustomFieldIds.has(fieldId as string)
        ) {
          throw new BadRequestException(
            &#x60;Field ${fieldId} is used in conditions, so it must be added to the form&#x60;,
          );
        }
      }

      const processedUserFields &#x3D; payload.updates.fields.filter((field) &#x3D;&gt; {
        const existingField &#x3D; existingForm.fields.find(
          (ef) &#x3D;&gt; ef.field &#x3D;&#x3D;&#x3D; field.field,
        );
        if (existingField) {
          return {
            ...field,
            field_type: existingField.fieldType,
          };
        }
        return {
          ...field,
          field_type: FormFieldType.CUSTOM,
        };
      });

      payload.updates.fields &#x3D; this.validateFormFields({
        fields: processedUserFields,
        workspaceFieldsMap: customFieldIdToValueMap,
        thenaRestrictedFieldsMap: thenaRestrictedFieldIdToValueMap,
      }).updatedFields;
    }

    if (payload.updates.conditions &amp;&amp; payload.updates.conditions.length &gt; 0) {
      payload.updates.conditions &#x3D; this.validateFormConditions({
        existingForm,
        conditions: payload.updates.conditions,
        workspaceFieldsMap: customFieldIdToValueMap,
        thenaRestrictedFieldsMap: thenaRestrictedFieldIdToValueMap,
        teamsMap,
      });
    }
  }

  validateFormConditions({
    existingForm,
    conditions &#x3D; [],
    workspaceFieldsMap,
    thenaRestrictedFieldsMap,
    teamsMap,
  }) {
    const teamUids &#x3D; Object.values(teamsMap);
    const processedCombinations &#x3D; new Set();

    for (const condition of conditions) {
      const triggerField &#x3D;
        workspaceFieldsMap.get(condition.triggerFieldId) ||
        thenaRestrictedFieldsMap.get(condition.triggerFieldId);
      if (!triggerField) {
        throw new BadRequestException(
          &#x60;Field ${condition.triggerFieldId} is not added in the form&#x60;,
        );
      }

      if (condition.teamId &amp;&amp; !teamUids.includes(condition.teamId)) {
        throw new BadRequestException(
          &#x60;You cannot create conditions for the team which you are not a part of&#x60;,
        );
      }

      if (
        !existingForm?.teamId &amp;&amp;
        thenaRestrictedFieldsMap.get(condition.triggerFieldId) &amp;&amp;
        !condition.teamId
      ) {
        throw new BadRequestException(
          &#x60;Field ${condition.triggerFieldId} is a thena restricted field and can be used for team related conditions&#x60;,
        );
      }

      if (
        existingForm?.teamId &amp;&amp;
        condition.teamId &amp;&amp;
        !teamUids.includes(condition.teamId)
      ) {
        throw new BadRequestException(
          &#x60;You are trying to create conditions for team ${condition.teamId}, but the form belongs to a different team.&#x60;,
        );
      }

      if (
        existingForm?.teamId &amp;&amp;
        condition.teamId &amp;&amp;
        condition.teamId !&#x3D;&#x3D; existingForm.teamId
      ) {
        throw new BadRequestException(
          &#x60;You cannot create conditions for some other team in some other teams&#x27;s form&#x60;,
        );
      }

      for (const target of condition.targetFields) {
        const targetField &#x3D;
          workspaceFieldsMap.get(target.id) ||
          thenaRestrictedFieldsMap.get(target.id);
        if (!targetField) {
          throw new BadRequestException(
            &#x60;Field ${target.id} is not added in the form&#x60;,
          );
        }
        if (targetField.uid &#x3D;&#x3D;&#x3D; triggerField.uid) {
          throw new BadRequestException(
            &#x60;Field ${
              targetField.name || targetField.uid
            } cannot trigger itself in the condition&#x60;,
          );
        }

        if (
          thenaRestrictedFieldsMap.get(targetField.uid) &amp;&amp;
          !condition.teamId
        ) {
          throw new BadRequestException(
            &#x60;Field ${
              targetField.name || targetField.uid
            } is a thena restricted field and can be used for team related conditions&#x60;,
          );
        }

        const combinationKey &#x3D; &#x60;${triggerField.uid}-${targetField.uid}-${target.type}&#x60;;
        if (processedCombinations.has(combinationKey)) {
          throw new BadRequestException(&#x60;Duplicate condition found: ${
            triggerField.name || triggerField.uid
          } -&gt; ${targetField.name || targetField.uid} with type ${target.type}.
						 Cannot have multiple conditions for the same trigger, target field and type combination&#x60;);
        }

        if (!Object.values(FormFieldTriggers).includes(target.type)) {
          throw new BadRequestException(&#x60;Invalid trigger type: ${
            target.type
          } for field ${targetField.name || targetField.uid}.
						 Allowed types are: ${Object.values(FormFieldTriggers).join(&quot;, &quot;)}&#x60;);
        }

        if (target.options &amp;&amp; target.options.length &gt; 0) {
          if (!FieldWithOptions.includes(targetField.fieldType)) {
            throw new BadRequestException(
              &#x60;Field ${
                targetField.name || targetField.uid
              } is not a field with options and cannot have options&#x60;,
            );
          }
        }

        if (!FieldWithOptions.includes(targetField.fieldType)) {
          if (!target.value &amp;&amp; target.type &#x3D;&#x3D;&#x3D; FormFieldTriggers.FILL_VALUE) {
            throw new BadRequestException(
              &#x60;Field ${targetField.name || targetField.uid} requires a value&#x60;,
            );
          }
          if (target.value?.length &gt; 1) {
            throw new BadRequestException(
              &#x60;Field ${
                targetField.name || targetField.uid
              } only accepts a single value&#x60;,
            );
          }
        }

        if (
          !thenaRestrictedFieldsMap.has(target.id) &amp;&amp;
          [
            FormFieldTriggers.FILL_VALUE,
            FormFieldTriggers.UPDATE_OPTIONS,
          ].includes(target.type)
        ) {
          this.customFieldValidatorService.validateFieldValue(
            targetField,
            target.value,
          );
        }

        processedCombinations.add(combinationKey);
      }

      for (const value of condition.triggerFieldValue || []) {
        if (!thenaRestrictedFieldsMap.has(condition.triggerFieldId)) {
          this.customFieldValidatorService.validateFieldValue(triggerField, [
            value,
          ]);
        }
      }
    }
    return conditions;
  }

  validateFormFields({
    fields &#x3D; [],
    workspaceFieldsMap,
    thenaRestrictedFieldsMap,
  }) {
    const updatedFields &#x3D; [];
    for (const formField of fields) {
      const fieldConfig &#x3D;
        workspaceFieldsMap.get(formField.field.toString()) ||
        thenaRestrictedFieldsMap.get(formField.field.toString());

      if (!fieldConfig) {
        throw new BadRequestException(&#x60;Field ${formField.field} not found&#x60;);
      }

      if (thenaRestrictedFieldsMap.get(formField.field)) {
        formField.fieldType &#x3D; FormFieldType.THENA_RESTRICTED;
        const providedProps &#x3D; Object.keys(formField).filter(
          (key) &#x3D;&gt;
            key !&#x3D;&#x3D; &quot;field&quot; &amp;&amp;
            key !&#x3D;&#x3D; &quot;fieldType&quot; &amp;&amp;
            ![undefined, null].includes(formField[key]),
        );

        const invalidProps &#x3D; providedProps.filter(
          (prop) &#x3D;&gt; !UpdatableThenaRestrictedFieldsProperties.includes(prop),
        );

        if (invalidProps.length &gt; 0) {
          throw new BadRequestException(
            &#x60;For Thena restricted field ${
              fieldConfig.name
            }, only ${UpdatableThenaRestrictedFieldsProperties.join(
              &quot;, &quot;,
            )} can be updated. Invalid properties: ${invalidProps.join(&quot;, &quot;)}&#x60;,
          );
        }
      }

      const mergedField &#x3D; {
        field: formField.field,
        fieldType: formField.fieldType || FormFieldType.CUSTOM,
        default_value:
          formField.default_value &#x3D;&#x3D;&#x3D; undefined
            ? fieldConfig.default_value
            : formField.default_value,
        mandatoryOnCreation:
          formField.mandatoryOnCreation &#x3D;&#x3D;&#x3D; undefined
            ? fieldConfig.mandatoryOnCreation
            : formField.mandatoryOnCreation,
        mandatoryOnClose:
          formField.mandatoryOnClose &#x3D;&#x3D;&#x3D; undefined
            ? fieldConfig.mandatoryOnClose
            : formField.mandatoryOnClose,
        visibleToCustomer:
          formField.visibleToCustomer &#x3D;&#x3D;&#x3D; undefined
            ? fieldConfig.visibleToCustomer
            : formField.visibleToCustomer,
        editableByCustomer:
          formField.editableByCustomer &#x3D;&#x3D;&#x3D; undefined
            ? fieldConfig.editableByCustomer
            : formField.editableByCustomer,
      };

      if (
        fieldConfig.mandatoryOnCreation &#x3D;&#x3D;&#x3D; true &amp;&amp;
        mergedField.mandatoryOnCreation &#x3D;&#x3D;&#x3D; false
      ) {
        throw new BadRequestException(
          &#x60;Field ${
            fieldConfig.name || fieldConfig.uid
          } is mandatory for creation and cannot be marked as non-mandatory in the form&#x60;,
        );
      }

      if (
        fieldConfig.mandatoryOnClose &#x3D;&#x3D;&#x3D; true &amp;&amp;
        mergedField.mandatoryOnClose &#x3D;&#x3D;&#x3D; false
      ) {
        throw new BadRequestException(
          &#x60;Field ${
            fieldConfig.name || fieldConfig.uid
          } is mandatory for closure and cannot be marked as non-mandatory in the form&#x60;,
        );
      }

      if (
        mergedField.mandatoryOnCreation &#x3D;&#x3D;&#x3D; true &amp;&amp;
        mergedField.visibleToCustomer &#x3D;&#x3D;&#x3D; false
      ) {
        throw new BadRequestException(
          &#x60;Field ${
            fieldConfig.name || fieldConfig.uid
          } is mandatory for creation and must be visible to customer&#x60;,
        );
      }

      // mergedField. &#x3D; formField.field_type;
      updatedFields.push(mergedField);
    }

    return { updatedFields };
  }

  getThenaRestrictedFieldIdToTicketKeyMap(
    thenaRestrictedFields: ThenaRestrictedField[],
  ) {
    const thenaRestrictedIdToKeyMap &#x3D; new Map();
    for (const field of thenaRestrictedFields) {
      switch (field.name.toLowerCase()) {
        case THENA_RESTRICTED_FIELDS.STATUS:
          thenaRestrictedIdToKeyMap.set(field.uid, &quot;statusId&quot;);
          break;
        case THENA_RESTRICTED_FIELDS.ASSIGNEE:
          thenaRestrictedIdToKeyMap.set(field.uid, &quot;assigneeId&quot;);
          break;
        case THENA_RESTRICTED_FIELDS.REQUESTOR:
          thenaRestrictedIdToKeyMap.set(field.uid, &quot;requestorEmail&quot;);
          break;
        case THENA_RESTRICTED_FIELDS.TITLE:
          thenaRestrictedIdToKeyMap.set(field.uid, &quot;title&quot;);
          break;
        case THENA_RESTRICTED_FIELDS.DESCRIPTION:
          thenaRestrictedIdToKeyMap.set(field.uid, &quot;description&quot;);
          break;
        case THENA_RESTRICTED_FIELDS.PRIORITY:
          thenaRestrictedIdToKeyMap.set(field.uid, &quot;priorityId&quot;);
          break;
        case THENA_RESTRICTED_FIELDS.ACCOUNT:
          thenaRestrictedIdToKeyMap.set(field.uid, &quot;accountId&quot;);
          break;
        case THENA_RESTRICTED_FIELDS.SUBMITTER:
          thenaRestrictedIdToKeyMap.set(field.uid, &quot;submitterEmail&quot;);
          break;
        case THENA_RESTRICTED_FIELDS.TEAM:
          thenaRestrictedIdToKeyMap.set(field.uid, &quot;teamId&quot;);
          break;
        case THENA_RESTRICTED_FIELDS.TYPE:
          thenaRestrictedIdToKeyMap.set(field.uid, &quot;typeId&quot;);
          break;
        default:
          break;
      }
    }
    return thenaRestrictedIdToKeyMap;
  }

  validateThenaRestrictedFieldValues(
    form: Form,
    thenaRestrictedFields: ThenaRestrictedField[],
    values: any,
  ) {
    const thenaRestrictedFieldsMap &#x3D; new Map(
      thenaRestrictedFields.map((field) &#x3D;&gt; [field.uid, field]),
    );

    for (const field of form.fields || []) {
      const thenaRestrictedField &#x3D; thenaRestrictedFieldsMap.get(field.field);
      if (thenaRestrictedField &amp;&amp; field.mandatoryOnCreation &#x3D;&#x3D;&#x3D; true) {
        switch (thenaRestrictedField.name.toLowerCase()) {
          case THENA_RESTRICTED_FIELDS.STATUS:
            if (!values.statusId) {
              throw new BadRequestException(&quot;Status is required&quot;);
            }
            break;
          case THENA_RESTRICTED_FIELDS.ASSIGNEE:
            if (!values.assigneeId) {
              throw new BadRequestException(&quot;Assignee is required&quot;);
            }
            break;
          case THENA_RESTRICTED_FIELDS.REQUESTOR:
            if (!values.requestorEmail) {
              throw new BadRequestException(&quot;Requestor email is required&quot;);
            }
            break;
          case THENA_RESTRICTED_FIELDS.TITLE:
            if (!values.title) {
              throw new BadRequestException(&quot;Title is required&quot;);
            }
            break;
          case THENA_RESTRICTED_FIELDS.DESCRIPTION:
            if (!values.description) {
              throw new BadRequestException(&quot;Description is required&quot;);
            }
            break;
          case THENA_RESTRICTED_FIELDS.PRIORITY:
            if (!values.priorityId) {
              throw new BadRequestException(&quot;Priority is required&quot;);
            }
            break;
          case THENA_RESTRICTED_FIELDS.ACCOUNT:
            if (!values.accountId) {
              throw new BadRequestException(&quot;Account is required&quot;);
            }
            break;
          case THENA_RESTRICTED_FIELDS.SUBMITTER:
            if (!values.submitterEmail) {
              throw new BadRequestException(&quot;Submitter email is required&quot;);
            }
            break;
          case THENA_RESTRICTED_FIELDS.TEAM:
            if (!values.teamId) {
              throw new BadRequestException(&quot;Team is required&quot;);
            }
            break;
          case THENA_RESTRICTED_FIELDS.TYPE:
            if (!values.typeId) {
              throw new BadRequestException(&quot;Type is required&quot;);
            }
            break;
          default:
            break;
        }
      }
    }
  }

  buildFormFieldValuesStructureFromTicket(
    values: ExternalCustomFieldValuesDto[],
  ) {
    const formFieldValues &#x3D; new Map&lt;string, any&gt;();
    for (const value of values || []) {
      if (!value?.customFieldId || !Array.isArray(value.data)) {
        throw new BadRequestException(
          &#x60;Invalid field value structure for field&#x60;,
        );
      }
      formFieldValues.set(
        value.customFieldId,
        value.data.map((v) &#x3D;&gt; {
          if (!v?.id &amp;&amp; !v?.value) {
            throw new BadRequestException(
              &#x60;Invalid field value structure for field ${value.customFieldId}&#x60;,
            );
          }
          return v.id || v.value;
        }),
      );
    }
    return formFieldValues;
  }

  async validateFormValues(
    orgId: string,
    form: Form,
    values: any,
    isTicketClosing?: boolean,
  ) {
    if (!form) {
      throw new BadRequestException(&quot;Form not found&quot;);
    }

    if (!form.fields || form.fields.length &#x3D;&#x3D;&#x3D; 0) {
      return;
    }

    const customFieldIds &#x3D; form.fields
      .map((field) &#x3D;&gt; {
        if (field.fieldType &#x3D;&#x3D;&#x3D; FormFieldType.CUSTOM) {
          return field.field;
        }
      })
      .filter(Boolean);

    const [customFields] &#x3D; await Promise.all([
      this.customFieldService.findByIdsWithTeamCheck(orgId, customFieldIds, [
        form.teamId,
      ]),
    ]);

    const customFieldsMap &#x3D; new Map(
      customFields.items.map((field) &#x3D;&gt; [field.uid, field]),
    );

    for (const f of form.fields || []) {
      const field &#x3D; customFieldsMap.get(f.field);

      if (!field) {
        continue;
      }

      field.mandatoryOnClose &#x3D; [true, false].includes(f.mandatoryOnClose)
        ? f.mandatoryOnClose
        : field.mandatoryOnClose;
      field.mandatoryOnCreation &#x3D; [true, false].includes(f.mandatoryOnCreation)
        ? f.mandatoryOnCreation
        : field.mandatoryOnCreation;
      field.visibleToCustomer &#x3D; [true, false].includes(f.visibleToCustomer)
        ? f.visibleToCustomer
        : field.visibleToCustomer;
      field.editableByCustomer &#x3D; [true, false].includes(f.editableByCustomer)
        ? f.editableByCustomer
        : field.editableByCustomer;

      let fieldValue &#x3D;
        values.get(field.uid) || values.get(field.name.toLowerCase());
      if (fieldValue &amp;&amp; !Array.isArray(fieldValue)) {
        fieldValue &#x3D; [fieldValue];
      }
      this.customFieldValidatorService.validateFieldValue(
        field,
        fieldValue,
        isTicketClosing,
      );
    }
  }

  applyConditions(
    form: Form,
    body: any,
    formFieldValues: Map&lt;string, any&gt;,
    thenaRestrictedIdToKeyMap: Map&lt;string, string&gt;,
    teamId: string,
  ) {
    const conditions &#x3D; form.conditions || [];

    const appliedConditionsMap &#x3D; new Map&lt;string, FormFieldTriggers[]&gt;();

    for (const condition of conditions) {
      if (teamId &amp;&amp; condition.teamId &amp;&amp; condition.teamId !&#x3D;&#x3D; teamId) {
        this.logger.log(
          &#x60;Skipping condition: teamId: ${teamId}, conditionTeamId: ${condition.teamId}&#x60;,
        );
        continue;
      }

      let fieldValue;
      if (thenaRestrictedIdToKeyMap.has(condition.triggerFieldId)) {
        fieldValue &#x3D;
          body[thenaRestrictedIdToKeyMap.get(condition.triggerFieldId)];
      } else {
        fieldValue &#x3D; formFieldValues.get(condition.triggerFieldId);
      }

      if (fieldValue &amp;&amp; !Array.isArray(fieldValue)) {
        fieldValue &#x3D; [fieldValue];
      }

      if (
        this.isValidTrigger(
          condition.triggerFieldValue,
          fieldValue,
          condition.conditionType,
        )
      ) {
        for (const targetField of condition.targetFields) {
          if (!appliedConditionsMap.has(targetField.id)) {
            appliedConditionsMap.set(targetField.id, []);
          }
          switch (targetField.type) {
            case FormFieldTriggers.ADD_FIELD:
              for (const field of form.fields || []) {
                if (
                  field.field &#x3D;&#x3D;&#x3D; targetField.id &amp;&amp;
                  !appliedConditionsMap
                    .get(targetField.id)
                    .includes(FormFieldTriggers.REMOVE_FIELD)
                ) {
                  appliedConditionsMap
                    .get(targetField.id)
                    .push(FormFieldTriggers.ADD_FIELD);
                  field.visibleToCustomer &#x3D; true;
                }
              }
              break;
            case FormFieldTriggers.REMOVE_FIELD:
              for (const field of form.fields || []) {
                if (
                  field.field &#x3D;&#x3D;&#x3D; targetField.id &amp;&amp;
                  !appliedConditionsMap
                    .get(targetField.id)
                    .includes(FormFieldTriggers.ADD_FIELD)
                ) {
                  appliedConditionsMap
                    .get(targetField.id)
                    .push(
                      FormFieldTriggers.REMOVE_FIELD,
                      FormFieldTriggers.MARK_NON_MANDATORY,
                    );
                  field.visibleToCustomer &#x3D; false;
                  field.mandatoryOnCreation &#x3D; false;
                  field.mandatoryOnClose &#x3D; false;
                }
              }
              break;
            case FormFieldTriggers.UPDATE_OPTIONS:
              break;
            case FormFieldTriggers.FILL_VALUE:
              if (
                thenaRestrictedIdToKeyMap.has(targetField.id) &amp;&amp;
                !body[thenaRestrictedIdToKeyMap.get(targetField.id)]
              ) {
                body[thenaRestrictedIdToKeyMap.get(targetField.id)] &#x3D;
                  targetField.value?.[0];
              } else if (!formFieldValues.get(targetField.id)) {
                formFieldValues.set(targetField.id, targetField.value);
                (body.customFieldValues || []).push({
                  customFieldId: targetField.id,
                  data: targetField.value.map((v) &#x3D;&gt; ({ value: v })),
                });
              }
              break;
            case FormFieldTriggers.MARK_MANDATORY:
              for (const field of form.fields || []) {
                if (
                  field.field &#x3D;&#x3D;&#x3D; targetField.id &amp;&amp;
                  !appliedConditionsMap
                    .get(targetField.id)
                    .includes(FormFieldTriggers.MARK_NON_MANDATORY)
                ) {
                  appliedConditionsMap
                    .get(targetField.id)
                    .push(
                      FormFieldTriggers.MARK_MANDATORY,
                      FormFieldTriggers.ADD_FIELD,
                    );
                  field.mandatoryOnCreation &#x3D; true;
                  field.mandatoryOnClose &#x3D; true;
                  field.editableByCustomer &#x3D; true;
                }
              }
              break;
            case FormFieldTriggers.MARK_NON_MANDATORY:
              for (const field of form.fields || []) {
                if (
                  field.field &#x3D;&#x3D;&#x3D; targetField.id &amp;&amp;
                  !appliedConditionsMap
                    .get(field.field)
                    .includes(FormFieldTriggers.MARK_MANDATORY)
                ) {
                  appliedConditionsMap
                    .get(field.field)
                    .push(FormFieldTriggers.MARK_NON_MANDATORY);
                  field.mandatoryOnCreation &#x3D; false;
                  field.mandatoryOnClose &#x3D; false;
                }
              }
              break;
          }
        }
      }
    }
  }

  isValidTrigger(
    triggerFieldValues: any,
    formFieldValue: any,
    conditionType: TargetFieldConditionsType,
  ): boolean {
    switch (conditionType) {
      case TargetFieldConditionsType.EQUALS:
        if (formFieldValue?.[0] &#x3D;&#x3D;&#x3D; triggerFieldValues[0]) {
          return true;
        }
        break;
      case TargetFieldConditionsType.INCLUDES:
        if (triggerFieldValues.includes(formFieldValue?.[0])) {
          return true;
        }
        break;
      case TargetFieldConditionsType.NOT_EQUALS:
        if (formFieldValue?.[0] !&#x3D;&#x3D; triggerFieldValues[0]) {
          return true;
        }
        break;
      case TargetFieldConditionsType.AND:
        break;
      case TargetFieldConditionsType.OR:
        if (triggerFieldValues.includes(formFieldValue?.[0])) {
          return true;
        }
        break;
      case TargetFieldConditionsType.NOT_INCLUDES:
        if (!triggerFieldValues.includes(formFieldValue?.[0])) {
          return true;
        }
        break;
    }
    return false;
  }

  validateTeamFields(fields: CustomField[], teamId: string) {
    for (const field of fields) {
      if (teamId &amp;&amp; field.teamId &amp;&amp; field.teamId !&#x3D;&#x3D; teamId) {
        throw new BadRequestException(
          &#x60;Field ${field.uid} is not in the team ${teamId}&#x60;,
        );
      }

      if (!teamId &amp;&amp; field.teamId) {
        throw new BadRequestException(
          &#x60;Field ${field.uid} belongs to a team and cannot be added in org level form&#x60;,
        );
      }
    }
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'FormsValidatorService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
