<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >ThenaRuleEvaluator</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/tickets/routing/providers/thena-request-router/rule-evaluator/evaluator.ts</code>
        </p>


            <p class="comment">
                <h3>Description</h3>
            </p>
            <p class="comment">
                <p>The rule evaluator for the thena request router.</p>

            </p>

            <p class="comment">
                <h3>Extends</h3>
            </p>
            <p class="comment">
                            <code><a href="../classes/RuleEvaluatorAbstract.html" target="_self" >RuleEvaluatorAbstract</a></code>
            </p>


            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Protected</span>
                                <a href="#operators" >operators</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#checkTeamAvailability" >checkTeamAvailability</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#evaluate" >evaluate</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#evaluateRuleGroup" >evaluateRuleGroup</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#isValidTicketField" >isValidTicketField</a>
                            </li>
                            <li>
                                <a href="#validateRule" >validateRule</a>
                            </li>
                            <li>
                                    <span class="modifier">Protected</span>
                                <a href="#evaluateSingleRule" >evaluateSingleRule</a>
                            </li>
                            <li>
                                    <span class="modifier">Protected</span>
                                <a href="#getTicketFieldValue" >getTicketFieldValue</a>
                            </li>
                            <li>
                                <a href="#initializeDefaultOperators" >initializeDefaultOperators</a>
                            </li>
                            <li>
                                    <span class="modifier">Protected</span>
                                <a href="#requiresValue" >requiresValue</a>
                            </li>
                            <li>
                                    <span class="modifier">Protected</span>
                                <a href="#validateValueType" >validateValueType</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(teamService: <a href="../injectables/TeamsService.html" target="_self">TeamsService</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="29" class="link-to-prism">src/tickets/routing/providers/thena-request-router/rule-evaluator/evaluator.ts:29</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>teamService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/TeamsService.html" target="_self" >TeamsService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="checkTeamAvailability"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>checkTeamAvailability</b></span>
                        <a href="#checkTeamAvailability"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>checkTeamAvailability(parentTeamConfig: <a href="../interfaces/CombinedTeamConfig.html" target="_self">CombinedTeamConfig</a>, currentTeamConfig: <a href="../interfaces/CombinedTeamConfig.html" target="_self">CombinedTeamConfig</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="172"
                                    class="link-to-prism">src/tickets/routing/providers/thena-request-router/rule-evaluator/evaluator.ts:172</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Checks if the team is available.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>parentTeamConfig</td>
                                            <td>
                                                            <code><a href="../interfaces/CombinedTeamConfig.html" target="_self" >CombinedTeamConfig</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The parent team configuration.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>currentTeamConfig</td>
                                            <td>
                                                            <code><a href="../interfaces/CombinedTeamConfig.html" target="_self" >CombinedTeamConfig</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current team configuration.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                        </div>
                            <div class="io-description">
                                <p>True if the team is available, false otherwise.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="evaluate"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>evaluate</b></span>
                        <a href="#evaluate"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>evaluate(ticket: Ticket, rules: RuleGroup[], user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Inherited from         <code><a href="../classes/RuleEvaluatorAbstract.html" target="_self" >RuleEvaluatorAbstract</a></code>
</div>
                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in         <code><a href="../classes/RuleEvaluatorAbstract.html#source" target="_self" >RuleEvaluatorAbstract:34</a></code>
</div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>ticket</td>
                                            <td>
                                                        <code>Ticket</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>rules</td>
                                            <td>
                                                        <code>RuleGroup[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;Team | null&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="evaluateRuleGroup"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>evaluateRuleGroup</b></span>
                        <a href="#evaluateRuleGroup"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>evaluateRuleGroup(ticket: Ticket, group: RuleGroup)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="111"
                                    class="link-to-prism">src/tickets/routing/providers/thena-request-router/rule-evaluator/evaluator.ts:111</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Evaluates a rule group against a ticket.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>ticket</td>
                                            <td>
                                                        <code>Ticket</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ticket to evaluate.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>group</td>
                                            <td>
                                                        <code>RuleGroup</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The rule group to evaluate.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;boolean&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>True if all rules in the group match (for AND) or any rule matches (for OR).</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isValidTicketField"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>isValidTicketField</b></span>
                        <a href="#isValidTicketField"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>isValidTicketField(field: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="153"
                                    class="link-to-prism">src/tickets/routing/providers/thena-request-router/rule-evaluator/evaluator.ts:153</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Validates if a field exists in the ticket schema.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>field</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The field to validate.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                        </div>
                            <div class="io-description">
                                <p>True if the field exists in the ticket schema, false otherwise.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateRule"></a>
                    <span class="name">
                        <span ><b>validateRule</b></span>
                        <a href="#validateRule"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>validateRule(rule: Rule)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Inherited from         <code><a href="../classes/RuleEvaluatorAbstract.html" target="_self" >RuleEvaluatorAbstract</a></code>
</div>
                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in         <code><a href="../classes/RuleEvaluatorAbstract.html#source" target="_self" >RuleEvaluatorAbstract:74</a></code>
</div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>rule</td>
                                            <td>
                                                        <code>Rule</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../interfaces/ValidationResult.html" target="_self" >ValidationResult</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="evaluateSingleRule"></a>
                    <span class="name">
                            <span class="modifier">Protected</span>
                        <span ><b>evaluateSingleRule</b></span>
                        <a href="#evaluateSingleRule"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>evaluateSingleRule(ticket: Ticket, rule: Rule)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Inherited from         <code><a href="../classes/RuleEvaluatorAbstract.html" target="_self" >RuleEvaluatorAbstract</a></code>
</div>
                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in         <code><a href="../classes/RuleEvaluatorAbstract.html#source" target="_self" >RuleEvaluatorAbstract:140</a></code>
</div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Evaluates a single rule.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>ticket</td>
                                            <td>
                                                        <code>Ticket</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ticket to evaluate the rule on.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>rule</td>
                                            <td>
                                                        <code>Rule</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The rule to evaluate.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                        </div>
                            <div class="io-description">
                                <p>True if the rule is valid, false otherwise.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTicketFieldValue"></a>
                    <span class="name">
                            <span class="modifier">Protected</span>
                        <span ><b>getTicketFieldValue</b></span>
                        <a href="#getTicketFieldValue"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTicketFieldValue(ticket: Ticket, field: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Inherited from         <code><a href="../classes/RuleEvaluatorAbstract.html" target="_self" >RuleEvaluatorAbstract</a></code>
</div>
                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in         <code><a href="../classes/RuleEvaluatorAbstract.html#source" target="_self" >RuleEvaluatorAbstract:130</a></code>
</div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Gets the value of a field from the ticket.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>ticket</td>
                                            <td>
                                                        <code>Ticket</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ticket to get the field value from.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>field</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The field to get the value from.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                <p>The value of the field.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="initializeDefaultOperators"></a>
                    <span class="name">
                        <span ><b>initializeDefaultOperators</b></span>
                        <a href="#initializeDefaultOperators"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>initializeDefaultOperators()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Inherited from         <code><a href="../classes/RuleEvaluatorAbstract.html" target="_self" >RuleEvaluatorAbstract</a></code>
</div>
                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in         <code><a href="../classes/RuleEvaluatorAbstract.html#source" target="_self" >RuleEvaluatorAbstract:23</a></code>
</div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="requiresValue"></a>
                    <span class="name">
                            <span class="modifier">Protected</span>
                        <span ><b>requiresValue</b></span>
                        <a href="#requiresValue"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>requiresValue(operator?: RuleOperator)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Inherited from         <code><a href="../classes/RuleEvaluatorAbstract.html" target="_self" >RuleEvaluatorAbstract</a></code>
</div>
                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in         <code><a href="../classes/RuleEvaluatorAbstract.html#source" target="_self" >RuleEvaluatorAbstract:71</a></code>
</div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Checks if the operator requires a value.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>operator</td>
                                            <td>
                                                        <code>RuleOperator</code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                                    <p>The operator to check.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                        </div>
                            <div class="io-description">
                                <p>True if the operator requires a value, false otherwise.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateValueType"></a>
                    <span class="name">
                            <span class="modifier">Protected</span>
                        <span ><b>validateValueType</b></span>
                        <a href="#validateValueType"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateValueType(rule: Rule)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Inherited from         <code><a href="../classes/RuleEvaluatorAbstract.html" target="_self" >RuleEvaluatorAbstract</a></code>
</div>
                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in         <code><a href="../classes/RuleEvaluatorAbstract.html#source" target="_self" >RuleEvaluatorAbstract:82</a></code>
</div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Validates the value type for the rule.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>rule</td>
                                            <td>
                                                        <code>Rule</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The rule to validate.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../interfaces/ValidationError.html" target="_self" >ValidationError | null</a></code>

                        </div>
                            <div class="io-description">
                                <p>The validation error if the value type is invalid, null otherwise.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>
            <section data-compodoc="block-properties">
    
    <h3 id="inputs">
        Properties
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="operators"></a>
                    <span class="name">
                            <span class="modifier">Protected</span>
                        <span ><b>operators</b></span>
                        <a href="#operators"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="../miscellaneous/typealiases.html#OperatorFunction" target="_self" >Map&lt;string | OperatorFunction&gt;</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>new Map()</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Inherited from         <code><a href="../classes/RuleEvaluatorAbstract.html" target="_self" >RuleEvaluatorAbstract</a></code>
</div>
                            </td>
                        </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in         <code><a href="../classes/RuleEvaluatorAbstract.html#source" target="_self" >RuleEvaluatorAbstract:17</a></code>
</div>
                        </td>
                    </tr>


        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Injectable } from &quot;@nestjs/common&quot;;
import type { DayConfig, Rule, RuleGroup } from &quot;@repo/thena-platform-entities&quot;;
import {
  BusinessHoursConfig,
  BusinessSlot,
  Team,
  Ticket,
} from &quot;@repo/thena-platform-entities&quot;;
import { DateTime, DateTimeOptions } from &quot;luxon&quot;;
import { CurrentUser } from &quot;../../../../../common/decorators&quot;;
import {
  CombinedTeamConfig,
  TeamsService,
} from &quot;../../../../../teams/services/teams.service&quot;;
import {
  RuleEvaluator,
  ValidationError,
  ValidationResult,
} from &quot;../../../../routing/interfaces&quot;;
import { RuleEvaluatorAbstract } from &quot;../../../abstract&quot;;

/**
 * The rule evaluator for the thena request router.
 */
@Injectable()
export class ThenaRuleEvaluator
  extends RuleEvaluatorAbstract
  implements RuleEvaluator
{
  constructor(private readonly teamService: TeamsService) {
    super(teamService);
  }

  async evaluate(
    ticket: Ticket,
    rules: RuleGroup[],
    user: CurrentUser,
  ): Promise&lt;Team | null&gt; {
    // TODO: Implement token bucket pattern for capacity
    // Sort the rule groups by priority
    const sortedGroups &#x3D; [...rules].sort((a, b) &#x3D;&gt; a.priority - b.priority);

    // Evaluate each rule group and match team&#x27;s if they are available
    for (const group of sortedGroups) {
      const matches &#x3D; await this.evaluateRuleGroup(ticket, group);
      if (matches) {
        // Get the team
        const team &#x3D; await this.teamService.findOneByTeamId(
          group.resultTeamId,
          user.orgId,
          group.parentTeamId,
        );

        // Get the parent team and current team configuration
        const [currentTeamConfig, parentTeamConfig] &#x3D; await Promise.all([
          this.teamService.getTeamConfigurations(team.uid, user),
          this.teamService.getTeamConfigurations(team.parentTeam.uid, user),
        ]);

        // Check if the team is available
        const isTeamAvailable &#x3D; this.checkTeamAvailability(
          parentTeamConfig,
          currentTeamConfig,
        );

        // If the team is available, return it
        if (isTeamAvailable) {
          return team;
        }
      }
    }
  }

  validateRule(rule: Rule): ValidationResult {
    // Run base validations
    const baseValidations &#x3D; super.validateRule(rule);
    if (!baseValidations.isValid) {
      return baseValidations;
    }

    // Run thena request router specific validations
    const errors: ValidationError[] &#x3D; [...baseValidations.errors];

    // Validate precedence
    if (typeof rule.precedence !&#x3D;&#x3D; &quot;number&quot; || rule.precedence &lt; 0) {
      errors.push({
        field: &quot;precedence&quot;,
        code: &quot;INVALID_PRECEDENCE&quot;,
        message: &quot;Precedence must be a non-negative number&quot;,
      });
    }

    // Validate field exists in ticket schema
    if (!this.isValidTicketField(rule.field)) {
      errors.push({
        field: &quot;field&quot;,
        code: &quot;INVALID_FIELD&quot;,
        message: &#x60;Field &#x27;${rule.field}&#x27; is not a valid ticket field&#x60;,
      });
    }

    return { isValid: true, errors: [] };
  }

  /**
   * Evaluates a rule group against a ticket.
   * @param ticket The ticket to evaluate.
   * @param group The rule group to evaluate.
   * @returns True if all rules in the group match (for AND) or any rule matches (for OR).
   */
  private async evaluateRuleGroup(
    ticket: Ticket,
    group: RuleGroup,
  ): Promise&lt;boolean&gt; {
    try {
      const isAndEmpty &#x3D; !group.andRules || group.andRules.length &#x3D;&#x3D;&#x3D; 0;
      const andRuleMatches &#x3D; await Promise.all(
        (group.andRules || []).map((rule) &#x3D;&gt;
          this.evaluateSingleRule(ticket, rule),
        ),
      );

      const isOrEmpty &#x3D; !group.orRules || group.orRules.length &#x3D;&#x3D;&#x3D; 0;
      const orRuleMatches &#x3D; await Promise.all(
        (group.orRules || []).map((rule) &#x3D;&gt;
          this.evaluateSingleRule(ticket, rule),
        ),
      );

      // Match all the AND rules
      const andRuleMatch &#x3D; isAndEmpty
        ? true
        : andRuleMatches.every((match) &#x3D;&gt; match);

      // Match at least one of the OR rules
      const orRuleMatch &#x3D; isOrEmpty
        ? true
        : orRuleMatches.some((match) &#x3D;&gt; match);

      // If the AND rules match and the OR rules match, then the rule group matches
      return andRuleMatch &amp;&amp; orRuleMatch;
    } catch (error) {
      console.error(&quot;Failed to evaluate rule group&quot;, error);
      return false;
    }
  }

  /**
   * Validates if a field exists in the ticket schema.
   * @param field The field to validate.
   * @returns True if the field exists in the ticket schema, false otherwise.
   */
  private isValidTicketField(field: string): boolean {
    const validFields &#x3D; [
      &quot;id&quot;,
      &quot;title&quot;,
      &quot;description&quot;,
      &quot;priority&quot;,
      &quot;status&quot;,
      &quot;category&quot;,
    ];

    return validFields.includes(field.split(&quot;.&quot;)[0]);
  }

  /**
   * Checks if the team is available.
   * @param parentTeamConfig The parent team configuration.
   * @param currentTeamConfig The current team configuration.
   * @returns True if the team is available, false otherwise.
   */
  private checkTeamAvailability(
    parentTeamConfig: CombinedTeamConfig,
    currentTeamConfig: CombinedTeamConfig,
  ): boolean {
    const { teamConfig: parentTeamConfigurations } &#x3D; parentTeamConfig;
    const { teamConfig: currentTeamConfigurations, businessHoursConfig } &#x3D;
      currentTeamConfig;

    // Check if the parent team config doesn&#x27;t respect timezone, then this team is always available
    if (!parentTeamConfigurations.routingRespectsTimezone) {
      return true;
    }

    // Check if the current team&#x27;s business hours are active
    const teamTimezone &#x3D; currentTeamConfigurations.timezone || &quot;UTC&quot;;
    const now &#x3D; DateTime.now().setZone(teamTimezone);

    // Check if today is a holiday
    const todayString &#x3D; now.toFormat(&quot;MM-dd&quot;);
    if (currentTeamConfigurations.holidays?.includes(todayString)) {
      return false;
    }

    // Get current day of the week in lowercase
    const currentDay &#x3D; now.weekdayLong.toLowerCase();

    // Get business hours for the current day
    const businessHours &#x3D; (
      businessHoursConfig[currentDay as keyof BusinessHoursConfig] as
        | DayConfig
        | undefined
    ).slots as BusinessSlot[] | undefined;

    // If no business hours are set for the current day, the team is unavailable
    if (!businessHours || businessHours.length &#x3D;&#x3D;&#x3D; 0) return false;

    // Current time in HH:mm format
    const currentTime &#x3D; now.toFormat(&quot;HH:mm&quot;);

    // Check if the current time is within any of the business hours slots
    return businessHours.some((slot) &#x3D;&gt; {
      // Convert slot times to DateTime objects in team&#x27;s timezone for proper comparison
      const zone: DateTimeOptions &#x3D; { zone: teamTimezone };
      const slotStart &#x3D; DateTime.fromFormat(slot.start, &quot;HH:mm&quot;, zone);
      const slotEnd &#x3D; DateTime.fromFormat(slot.end, &quot;HH:mm&quot;, zone);

      // Handle slots that span midnight
      if (slotEnd &lt; slotStart) {
        // If current time is before midnight, compare with start time
        if (currentTime &gt;&#x3D; slot.start) {
          return true;
        }

        // If current time is after midnight, compare with end time
        if (currentTime &lt;&#x3D; slot.end) {
          return true;
        }

        return false;
      }

      // Normal slot comparison
      return currentTime &gt;&#x3D; slot.start &amp;&amp; currentTime &lt;&#x3D; slot.end;
    });
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'ThenaRuleEvaluator.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
