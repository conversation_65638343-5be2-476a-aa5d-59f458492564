<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >TicketSnsPublisherConsumer</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/tickets/processors/ticket.sns-publish.processor.ts</code>
        </p>



            <p class="comment">
                <h3>Extends</h3>
            </p>
            <p class="comment">
                        <code>WorkerHost</code>
            </p>


            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#constructSnsPayload" >constructSnsPayload</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#process" >process</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#publishToSNS" >publishToSNS</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(snsPublisherService: <a href="../s/SNSPublisherService.html" target="_self">SNSPublisherService</a>, logger: ILogger, ticketRepository: TicketRepository, configService: <a href="../injectables/ConfigService.html" target="_self">ConfigService</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="20" class="link-to-prism">src/tickets/processors/ticket.sns-publish.processor.ts:20</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>snsPublisherService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../miscellaneous/variables.html#SNSPublisherService" target="_self" >SNSPublisherService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>logger</td>
                                                  
                                                        <td>
                                                                    <code>ILogger</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>ticketRepository</td>
                                                  
                                                        <td>
                                                                    <code>TicketRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>configService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/ConfigService.html" target="_self" >ConfigService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="constructSnsPayload"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>constructSnsPayload</b></span>
                        <a href="#constructSnsPayload"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>constructSnsPayload(ticketData: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, eventType: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, team: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, previousTicket: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="125"
                                    class="link-to-prism">src/tickets/processors/ticket.sns-publish.processor.ts:125</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>ticketData</td>
                                            <td>
                                                            <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>eventType</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>team</td>
                                            <td>
                                                            <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>previousTicket</td>
                                            <td>
                                                            <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../interfaces/SnsTicketCreatedPayload.html" target="_self" >SnsTicketCreatedPayload</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="process"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>process</b></span>
                        <a href="#process"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>process(job: Job)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="48"
                                    class="link-to-prism">src/tickets/processors/ticket.sns-publish.processor.ts:48</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>job</td>
                                            <td>
                                                        <code>Job</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="publishToSNS"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>publishToSNS</b></span>
                        <a href="#publishToSNS"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>publishToSNS(snsPayload: <a href="../interfaces/SnsTicketCreatedPayload.html" target="_self">SnsTicketCreatedPayload</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="32"
                                    class="link-to-prism">src/tickets/processors/ticket.sns-publish.processor.ts:32</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>snsPayload</td>
                                            <td>
                                                            <code><a href="../interfaces/SnsTicketCreatedPayload.html" target="_self" >SnsTicketCreatedPayload</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Processor, WorkerHost } from &quot;@nestjs/bullmq&quot;;
import { Inject, Injectable } from &quot;@nestjs/common&quot;;
import { ILogger } from &quot;@repo/nestjs-commons/logger&quot;;
import {
  ContextUserType,
  SNSPublisherService,
  TicketEvents,
} from &quot;@repo/thena-eventbridge&quot;;
import { TicketRepository } from &quot;@repo/thena-platform-entities&quot;;
import { Job } from &quot;bullmq&quot;;
import { ConfigKeys } from &quot;src/config/config.service&quot;;
import { v4 as uuidv4 } from &quot;uuid&quot;;
import { ConfigService } from &quot;../../config/config.service&quot;;
import { QueueNames } from &quot;../../constants/queue.constants&quot;;
import { SnsTicketCreatedPayload } from &quot;../interfaces/sns-ticket-created-payload.interface&quot;;
import { TICKET_SNS_PUBLISHER } from &quot;../utils/tickets.constants&quot;;

@Injectable()
@Processor(QueueNames.TICKET_SNS_PUBLISHER)
export class TicketSnsPublisherConsumer extends WorkerHost {
  constructor(
    @Inject(TICKET_SNS_PUBLISHER)
    private readonly snsPublisherService: SNSPublisherService,
    @Inject(&quot;CustomLogger&quot;)
    private readonly logger: ILogger,
    private readonly ticketRepository: TicketRepository,
    private readonly configService: ConfigService,
  ) {
    super();
  }

  private async publishToSNS(snsPayload: SnsTicketCreatedPayload) {
    await this.snsPublisherService.publishSNSMessage({
      subject: snsPayload.eventType,
      message: JSON.stringify(snsPayload),
      topicArn: this.configService.get(ConfigKeys.AWS_SNS_TICKET_TOPIC_ARN),
      messageAttributes: {
        event_name: snsPayload.eventType,
        event_id: snsPayload.eventId, // Unique identifier for the event
        event_timestamp: snsPayload.timestamp,
        context_user_id: snsPayload.actor.id,
        context_user_type: snsPayload.actor.type as ContextUserType,
        context_organization_id: snsPayload.orgId,
      },
    });
  }

  async process(job: Job) {
    try {
      this.logger.log(&#x60;Processing SNS message for job ${job.id}&#x60;, job.data);

      const { ticket, eventType, user, team, previousTicket } &#x3D; job.data;

      const ticketData &#x3D; await this.ticketRepository.findByCondition({
        where: { uid: ticket },
        relations: [
          &quot;organization&quot;,
          &quot;status&quot;,
          &quot;priority&quot;,
          &quot;customFieldValues&quot;,
          &quot;tags&quot;,
          &quot;assignedAgent&quot;,
        ],
      });

      if (!ticketData &amp;&amp; eventType !&#x3D;&#x3D; TicketEvents.DELETED) {
        const error &#x3D; new Error(&#x60;Ticket not found with id ${ticket}&#x60;);
        error.name &#x3D; &quot;TicketNotFoundError&quot;;
        throw error;
      }

      const snsPayload &#x3D; this.constructSnsPayload(
        eventType &#x3D;&#x3D;&#x3D; TicketEvents.DELETED ? previousTicket : ticketData,
        eventType,
        user,
        team,
        previousTicket,
      );

      // Add timeout to SNS publish operation
      await Promise.race([
        this.publishToSNS(snsPayload),
        new Promise((_, reject) &#x3D;&gt;
          setTimeout(() &#x3D;&gt; {
            const error &#x3D; new Error(&quot;SNS publish timeout&quot;);
            error.name &#x3D; &quot;SNSTimeoutError&quot;;
            reject(error);
          }, 5000),
        ),
      ]);

      return {
        success: true,
        processedAt: new Date(),
        ticketId: ticket,
      };
    } catch (error) {
      this.logger.error(
        &#x60;Error publishing message to SNS queue for job ${job.id}: ${error.message}&#x60;,
        error?.stack,
      );

      // Determine if we should retry based on error type
      if (error.name &#x3D;&#x3D;&#x3D; &quot;TicketNotFoundError&quot;) {
        // Permanent failure - don&#x27;t retry
        throw error;
      }

      // For transient errors (network issues, timeouts), allow BullMQ to retry
      if (job.attemptsMade &lt; job.opts.attempts - 1) {
        const retryError &#x3D; new Error(
          &#x60;SNS publishing failed (attempt ${job.attemptsMade + 1}): ${
            error.message
          }&#x60;,
        );
        retryError.name &#x3D; &quot;RetryableError&quot;;
        throw retryError;
      }

      // On final attempt, mark as permanent failure
      throw error;
    }
  }

  private constructSnsPayload(
    ticketData: any, // TODO: Add the ticketData Interface
    eventType: string,
    user: any,
    team: any,
    previousTicket: any, // TODO: Add the previousTicket Interface
  ): SnsTicketCreatedPayload {
    const { organization, status, priority, assignedAgent } &#x3D; ticketData;

    const payload: SnsTicketCreatedPayload &#x3D; {
      eventId: uuidv4(),
      eventType: eventType,
      timestamp: new Date().getTime().toString(),
      orgId: organization?.uid,
      teamId: team?.uid,
      actor: {
        id: user?.uid,
        type: user?.userType,
        email: user?.email,
      },
      payload: {
        ticket: {
          id: ticketData.uid,
          title: ticketData.title,
          description: ticketData.description,
          priorityId: priority?.uid,
          priorityName: priority?.name,
          statusId: status?.uid,
          statusName: status?.name,
          source: &quot;1&quot;,
          teamId: team?.uid,
          assignedTo: assignedAgent?.uid,
          customer: {
            id: ticketData.requestorId,
            email: ticketData.requestorEmail,
            name: ticketData.requestorName,
          },
          tags: ticketData.tags?.map((tag) &#x3D;&gt; tag.uid) || [],
          customFields:
            ticketData.customFieldValues?.map((customField) &#x3D;&gt; ({
              [customField.customField.uid]: customField.value,
            })) || [],
          createdAt: ticketData.createdAt,
          metadata: ticketData.metadata,
          isEscalated: ticketData.isEscalated,
          isArchived: ticketData.archivedAt ? true : false,
        },
      },
    };

    // Add previous ticket data if event type is update
    if (eventType &#x3D;&#x3D;&#x3D; TicketEvents.UPDATED &amp;&amp; previousTicket) {
      payload.payload.ticket.previousTicket &#x3D; {
        id: previousTicket.uid,
        title: previousTicket.title,
        description: previousTicket.description,
        priorityId: previousTicket.priority?.uid,
        priorityName: previousTicket.priority?.name,
        statusId: previousTicket.status?.uid,
        statusName: previousTicket.status?.name,
        source: &quot;1&quot;,
        teamId: previousTicket.team?.uid,
        assignedTo: previousTicket.assignedAgent?.uid,
        tags: previousTicket.tags?.map((tag) &#x3D;&gt; tag.uid) || [],
        customFields:
          previousTicket.customFieldValues?.map((customField) &#x3D;&gt; ({
            [customField.customField.uid]: customField.value,
          })) || [],
        metadata: previousTicket.metadata,
        isEscalated: previousTicket.isEscalated,
        isArchived: previousTicket.archivedAt ? true : false,
        createdAt: previousTicket.createdAt,
        customer: {
          id: previousTicket.requestorId,
          email: previousTicket.requestorEmail,
          name: previousTicket.requestorName,
        },
      };
    }

    return payload;
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'TicketSnsPublisherConsumer.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
