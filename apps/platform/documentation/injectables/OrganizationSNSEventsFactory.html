<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >OrganizationSNSEventsFactory</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/organization/events/organization-sns-events.factory.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#baseOrganizationSNSEvent" >baseOrganizationSNSEvent</a>
                            </li>
                            <li>
                                <a href="#createOrganizationCreatedSNSEvent" >createOrganizationCreatedSNSEvent</a>
                            </li>
                            <li>
                                <a href="#createOrganizationDeletedSNSEvent" >createOrganizationDeletedSNSEvent</a>
                            </li>
                            <li>
                                <a href="#createOrganizationUpdatedSNSEvent" >createOrganizationUpdatedSNSEvent</a>
                            </li>
                            <li>
                                <a href="#createUserJoinedOrganizationSNSEvent" >createUserJoinedOrganizationSNSEvent</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>


            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="baseOrganizationSNSEvent"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>baseOrganizationSNSEvent</b></span>
                        <a href="#baseOrganizationSNSEvent"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>baseOrganizationSNSEvent(user: literal type, payload: T, eventType: OrganizationEvents)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="16"
                                    class="link-to-prism">src/organization/events/organization-sns-events.factory.ts:16</a></div>
                        </td>
                    </tr>

                    <tr>
                        <td class="col-md-4">
                            <b>Type parameters :</b>
                            <ul class="type-parameters">
                                    <li>T</li>
                            </ul>
                        </td>
                    </tr>

            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                        <code>literal type</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>payload</td>
                                            <td>
                                                        <code>T</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>eventType</td>
                                            <td>
                                                        <code>OrganizationEvents</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>SNSEvent&lt;T&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createOrganizationCreatedSNSEvent"></a>
                    <span class="name">
                        <span ><b>createOrganizationCreatedSNSEvent</b></span>
                        <a href="#createOrganizationCreatedSNSEvent"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>createOrganizationCreatedSNSEvent(user: literal type, organization: Organization)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="40"
                                    class="link-to-prism">src/organization/events/organization-sns-events.factory.ts:40</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                        <code>literal type</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>organization</td>
                                            <td>
                                                        <code>Organization</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>SNSEvent&lt;OrganizationPayload&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createOrganizationDeletedSNSEvent"></a>
                    <span class="name">
                        <span ><b>createOrganizationDeletedSNSEvent</b></span>
                        <a href="#createOrganizationDeletedSNSEvent"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>createOrganizationDeletedSNSEvent(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, organization: Organization)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="78"
                                    class="link-to-prism">src/organization/events/organization-sns-events.factory.ts:78</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>organization</td>
                                            <td>
                                                        <code>Organization</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>SNSEvent&lt;OrganizationPayload&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createOrganizationUpdatedSNSEvent"></a>
                    <span class="name">
                        <span ><b>createOrganizationUpdatedSNSEvent</b></span>
                        <a href="#createOrganizationUpdatedSNSEvent"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>createOrganizationUpdatedSNSEvent(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, previousOrganization: Organization, organization: Organization)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="60"
                                    class="link-to-prism">src/organization/events/organization-sns-events.factory.ts:60</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>previousOrganization</td>
                                            <td>
                                                        <code>Organization</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>organization</td>
                                            <td>
                                                        <code>Organization</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>SNSEvent&lt;OrganizationPayload&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createUserJoinedOrganizationSNSEvent"></a>
                    <span class="name">
                        <span ><b>createUserJoinedOrganizationSNSEvent</b></span>
                        <a href="#createUserJoinedOrganizationSNSEvent"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>createUserJoinedOrganizationSNSEvent(user: literal type, organization: Organization, userPersona: User)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="93"
                                    class="link-to-prism">src/organization/events/organization-sns-events.factory.ts:93</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                        <code>literal type</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>organization</td>
                                            <td>
                                                        <code>Organization</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>userPersona</td>
                                            <td>
                                                        <code>User</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>SNSEvent&lt;UserJoinedOrganizationPayload&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Injectable } from &quot;@nestjs/common&quot;;
import { OrganizationEvents } from &quot;@repo/thena-eventbridge&quot;;
import { Organization, User, UserType } from &quot;@repo/thena-platform-entities&quot;;
import { UserResponseDto } from &quot;src/users/transformers/user.transformer&quot;;
import { v4 as uuidv4 } from &quot;uuid&quot;;
import { CurrentUser } from &quot;../../common/decorators&quot;;
import {
  OrganizationPayload,
  SNSEvent,
  UserJoinedOrganizationPayload,
} from &quot;../interfaces/sns-events.interface&quot;;
import { OrganizationResponseDto } from &quot;../transformers/organization.transformer&quot;;

@Injectable()
export class OrganizationSNSEventsFactory {
  private baseOrganizationSNSEvent&lt;T&gt;(
    user: {
      uid: string;
      email: string;
      userType: UserType;
      orgUid: string;
    },
    payload: T,
    eventType: OrganizationEvents,
  ): SNSEvent&lt;T&gt; {
    return {
      eventId: uuidv4(),
      eventType,
      timestamp: new Date().toISOString(),
      orgId: user.orgUid,
      actor: {
        id: user.uid,
        email: user.email,
        type: user.userType,
      },
      payload,
    };
  }

  createOrganizationCreatedSNSEvent(
    user: {
      uid: string;
      email: string;
      userType: UserType;
      orgUid: string;
    },
    organization: Organization,
  ): SNSEvent&lt;OrganizationPayload&gt; {
    const organizationPayload &#x3D; {
      organization: OrganizationResponseDto.fromEntity(organization),
    } as OrganizationPayload;

    return this.baseOrganizationSNSEvent(
      user,
      organizationPayload,
      OrganizationEvents.CREATED,
    );
  }

  createOrganizationUpdatedSNSEvent(
    user: CurrentUser,
    previousOrganization: Organization,
    organization: Organization,
  ): SNSEvent&lt;OrganizationPayload&gt; {
    const organizationPayload &#x3D; {
      organization: OrganizationResponseDto.fromEntity(organization),
      previousOrganization:
        OrganizationResponseDto.fromEntity(previousOrganization),
    } as OrganizationPayload;

    return this.baseOrganizationSNSEvent(
      user,
      organizationPayload,
      OrganizationEvents.UPDATED,
    );
  }

  createOrganizationDeletedSNSEvent(
    user: CurrentUser,
    organization: Organization,
  ): SNSEvent&lt;OrganizationPayload&gt; {
    const organizationPayload &#x3D; {
      previousOrganization: OrganizationResponseDto.fromEntity(organization),
    } as OrganizationPayload;

    return this.baseOrganizationSNSEvent(
      user,
      organizationPayload,
      OrganizationEvents.DELETED,
    );
  }

  createUserJoinedOrganizationSNSEvent(
    user: {
      uid: string;
      email: string;
      userType: UserType;
      orgUid: string;
    },
    organization: Organization,
    userPersona: User,
  ): SNSEvent&lt;UserJoinedOrganizationPayload&gt; {
    const payload &#x3D; {
      organization: OrganizationResponseDto.fromEntity(organization),
      user: UserResponseDto.fromEntity(userPersona),
    } as UserJoinedOrganizationPayload;

    return this.baseOrganizationSNSEvent(
      user,
      payload,
      OrganizationEvents.MEMBER_JOINED,
    );
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'OrganizationSNSEventsFactory.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
