<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >FormService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/forms/services/form.service.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#create" >create</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createDefaultForm" >createDefaultForm</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#delete" >delete</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#fetchPaginatedResults" >fetchPaginatedResults</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findByIds" >findByIds</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getDefaultForm" >getDefaultForm</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#search" >search</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#update" >update</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(logger: ILogger, formRepository: FormRepository, thenaRestrictedFieldService: <a href="../injectables/ThenaRestrictedFieldService.html" target="_self">ThenaRestrictedFieldService</a>, formFieldEventRepository: FormFieldEventRepository, transactionService: TransactionService)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="19" class="link-to-prism">src/forms/services/form.service.ts:19</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>logger</td>
                                                  
                                                        <td>
                                                                    <code>ILogger</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>formRepository</td>
                                                  
                                                        <td>
                                                                    <code>FormRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>thenaRestrictedFieldService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/ThenaRestrictedFieldService.html" target="_self" >ThenaRestrictedFieldService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>formFieldEventRepository</td>
                                                  
                                                        <td>
                                                                    <code>FormFieldEventRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>transactionService</td>
                                                  
                                                        <td>
                                                                    <code>TransactionService</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="create"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>create</b></span>
                        <a href="#create"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>create(userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, createFormDto: <a href="../classes/CreateFormDto.html" target="_self">CreateFormDto</a>, isDefault: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank">boolean</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="51"
                                    class="link-to-prism">src/forms/services/form.service.ts:51</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Creates a new custom field.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Default value</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>userId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>

                                            <td>
                                            </td>

                                        </tr>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>

                                            <td>
                                            </td>

                                        </tr>
                                        <tr>
                                                <td>createFormDto</td>
                                            <td>
                                                            <code><a href="../classes/CreateFormDto.html" target="_self" >CreateFormDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>

                                            <td>
                                            </td>

                                        </tr>
                                        <tr>
                                                <td>isDefault</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>

                                            <td>
                                                    <code>false</code>
                                            </td>

                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;Form&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The created custom field.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createDefaultForm"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>createDefaultForm</b></span>
                        <a href="#createDefaultForm"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createDefaultForm(orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="29"
                                    class="link-to-prism">src/forms/services/form.service.ts:29</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="delete"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>delete</b></span>
                        <a href="#delete"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>delete(userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, payload: <a href="../classes/DeleteFormsDto.html" target="_self">DeleteFormsDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="350"
                                    class="link-to-prism">src/forms/services/form.service.ts:350</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Delete fomrs by ids.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>userId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>payload</td>
                                            <td>
                                                            <code><a href="../classes/DeleteFormsDto.html" target="_self" >DeleteFormsDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="fetchPaginatedResults"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>fetchPaginatedResults</b></span>
                        <a href="#fetchPaginatedResults"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>fetchPaginatedResults(orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, limit: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>, offset: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>, teamId?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, onlyTeamForms?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank">boolean</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="151"
                                    class="link-to-prism">src/forms/services/form.service.ts:151</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds all forms.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>limit</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>offset</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>onlyTeamForms</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>All forms.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findByIds"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findByIds</b></span>
                        <a href="#findByIds"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findByIds(orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, ids: string[], teamIds?: string[])</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="197"
                                    class="link-to-prism">src/forms/services/form.service.ts:197</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds forms by ids.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>ids</td>
                                            <td>
                                                        <code>string[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>Array of form IDs to fetch</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>teamIds</td>
                                            <td>
                                                        <code>string[]</code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>Forms matching the provided IDs</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getDefaultForm"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>getDefaultForm</b></span>
                        <a href="#getDefaultForm"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getDefaultForm(orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="39"
                                    class="link-to-prism">src/forms/services/form.service.ts:39</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="search"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>search</b></span>
                        <a href="#search"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>search(orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, term: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, teamId?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, onlyTeamForms?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank">boolean</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="245"
                                    class="link-to-prism">src/forms/services/form.service.ts:245</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Search custom fields by name.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>term</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>term to search</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>onlyTeamForms</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>Custom fields matching the provided term</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="update"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>update</b></span>
                        <a href="#update"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>update(userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, payload: <a href="../classes/UpdateFormDto.html" target="_self">UpdateFormDto</a>, updateDefault: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank">boolean</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="286"
                                    class="link-to-prism">src/forms/services/form.service.ts:286</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Updates existing custom fields in batch.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Default value</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>userId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>

                                            <td>
                                            </td>

                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>

                                            <td>
                                            </td>

                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>payload</td>
                                            <td>
                                                            <code><a href="../classes/UpdateFormDto.html" target="_self" >UpdateFormDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>

                                            <td>
                                            </td>

                                            <td>
                                                    <p>Array of custom field update data.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>updateDefault</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>

                                            <td>
                                                    <code>false</code>
                                            </td>

                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                <p>The updated custom fields.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { BadRequestException, Inject, Injectable } from &quot;@nestjs/common&quot;;
import { ILogger } from &quot;@repo/nestjs-commons/logger&quot;;
import {
  CustomValidationError,
  Form,
  FormFieldEventRepository,
  FormFieldEventTypes,
  FormFieldType,
  FormRepository,
  TransactionService,
} from &quot;@repo/thena-platform-entities&quot;;
import _ from &quot;lodash&quot;;
import { ILike, In, IsNull } from &quot;typeorm&quot;;
import { IdGeneratorUtils } from &quot;../../common/utils&quot;;
import { ThenaRestrictedFieldService } from &quot;../../custom-field/services/thena-restricted-field.service&quot;;
import { CreateFormDto, DeleteFormsDto, UpdateFormDto } from &quot;../dto/form.dto&quot;;

@Injectable()
export class FormService {
  constructor(
    @Inject(&quot;CustomLogger&quot;)
    private readonly logger: ILogger,
    private formRepository: FormRepository,
    private thenaRestrictedFieldService: ThenaRestrictedFieldService,
    private formFieldEventRepository: FormFieldEventRepository,
    private transactionService: TransactionService,
  ) {}

  async createDefaultForm(orgId: string) {
    const createFormDto: CreateFormDto &#x3D; {
      name: &quot;Default Form&quot;,
      description: &quot;Default form for the organization&quot;,
      isActive: true,
    };

    await this.create(null, orgId, createFormDto, true);
  }

  async getDefaultForm(orgId: string) {
    const defaultForm &#x3D; await this.formRepository.findByCondition({
      where: { organizationId: orgId, default: true, isDeleted: false },
    });
    return defaultForm;
  }

  /**
   * Creates a new custom field.
   * @param createCustomFieldDto The custom field data to create.
   * @returns The created custom field.
   */
  async create(
    userId: string,
    orgId: string,
    createFormDto: CreateFormDto,
    isDefault: boolean &#x3D; false,
  ): Promise&lt;Form&gt; {
    if (!orgId) {
      throw new BadRequestException(
        &quot;Organization ID is required to create default form&quot;,
      );
    }
    const thenaRestrictedFields &#x3D;
      await this.thenaRestrictedFieldService.getAllFields();

    const fields &#x3D; [
      ...thenaRestrictedFields.map((field) &#x3D;&gt; ({
        field: field.uid,
        fieldType: FormFieldType.THENA_RESTRICTED,
        defaultValue: field.defaultValue,
        mandatoryOnCreation: field.mandatoryOnCreation,
        mandatoryOnClose: field.mandatoryOnClose,
        visibleToCustomer: field.visibleToCustomer,
        editableByCustomer: field.editableByCustomer,
      })),
      ...(createFormDto.fields || []).map((field) &#x3D;&gt; ({
        field: field.field,
        fieldType: FormFieldType.CUSTOM,
        defaultValue: field.defaultValue,
        mandatoryOnCreation: field.mandatoryOnCreation,
        mandatoryOnClose: field.mandatoryOnClose,
        visibleToCustomer: field.visibleToCustomer,
        editableByCustomer: field.editableByCustomer,
      })),
    ];

    const uid &#x3D; IdGeneratorUtils.generate(&quot;FO&quot;);
    const form &#x3D; this.formRepository.create({
      organization: {
        id: orgId,
      },
      organizationId: orgId,
      name: createFormDto.name,
      description: createFormDto.description,
      createdBy: {
        id: userId,
      },
      uid,
      version: 1,
      fields,
      conditions: createFormDto.conditions,
      channels: createFormDto.channels,
      team: createFormDto.teamId
        ? {
            id: createFormDto.teamId,
          }
        : undefined,
      teamId: createFormDto.teamId,
      isActive: createFormDto.isActive,
      default: isDefault,
    });

    let res;
    try {
      res &#x3D; await this.formRepository.save(form);
    } catch (error) {
      if (error instanceof CustomValidationError) {
        throw new BadRequestException(error.message);
      }
      throw error;
    }

    try {
      const fieldEvent &#x3D; this.formFieldEventRepository.create({
        organization: {
          id: orgId,
        },
        organizationId: orgId,
        entityId: res.uid,
        type: FormFieldEventTypes.FORM_CREATED,
        data: res,
        user: {
          id: userId,
        },
        userId: userId,
        version: 1,
        createdAt: new Date(),
      });
      await this.formFieldEventRepository.save(fieldEvent);
    } catch (error) {
      this.logger.error(
        &#x60;Error while trying to log form creation event: ${error.message}, stack: ${error.stack}&#x60;,
      );
    }
    return res;
  }

  /**
   * Finds all forms.
   * @returns All forms.
   */
  async fetchPaginatedResults(
    orgId: string,
    limit: number,
    offset: number,
    teamId?: string,
    onlyTeamForms?: boolean,
  ) {
    const query &#x3D; [];
    if (teamId) {
      query.push({
        organizationId: orgId,
        isDeleted: false,
        teamId: teamId,
      });
    }

    if (!teamId || !onlyTeamForms) {
      query.push({
        organizationId: orgId,
        isDeleted: false,
        teamId: IsNull(),
      });
    }

    if (query.length &#x3D;&#x3D;&#x3D; 0) {
      throw new BadRequestException(&quot;No fields to fetch&quot;);
    }

    const forms &#x3D; await this.formRepository.fetchPaginatedResults(
      { limit, page: offset },
      {
        where: query,
      },
    );
    return {
      ...forms,
      offset: (forms.results || []).length &gt; 0 ? ++offset : undefined,
    };
  }

  /**
   * Finds forms by ids.
   * @param request The FastifyRequest object containing user info
   * @param ids Array of form IDs to fetch
   * @returns Forms matching the provided IDs
   */
  async findByIds(orgId: string, ids: string[], teamIds?: string[]) {
    if (_.isEmpty(ids)) {
      throw new BadRequestException(&quot;No form ids provided&quot;);
    }
    const query &#x3D; [
      {
        organizationId: orgId,
        uid: In(ids),
        isDeleted: false,
        teamId: IsNull(),
      },
    ];

    if (teamIds?.length &gt; 0) {
      query.push({
        organizationId: orgId,
        uid: In(ids),
        isDeleted: false,
        teamId: In(teamIds),
      });
    }

    const forms &#x3D; await this.formRepository.findAll({
      where: query,
    });

    if (forms.length !&#x3D;&#x3D; ids.length) {
      const missingForms &#x3D; ids.filter(
        (id) &#x3D;&gt; !forms.some((form) &#x3D;&gt; form.uid &#x3D;&#x3D;&#x3D; id),
      );
      throw new BadRequestException(
        &#x60;Some forms not found, either they are deleted or not found in your teams, missing forms: ${missingForms.join(
          &quot;, &quot;,
        )}&#x60;,
      );
    }

    return {
      items: forms,
    };
  }

  /**
   * Search custom fields by name.
   * @param request The FastifyRequest object containing user info
   * @param term term to search
   * @returns Custom fields matching the provided term
   */
  async search(
    orgId: string,
    term: string,
    teamId?: string,
    onlyTeamForms?: boolean,
  ) {
    if (_.isEmpty(term?.trim())) {
      throw new BadRequestException(&quot;No search term provided&quot;);
    }

    const query &#x3D; [];
    if (teamId) {
      query.push({
        name: ILike(&#x60;%${term.toLocaleLowerCase()}%&#x60;),
        organizationId: orgId,
        isDeleted: false,
        teamId: teamId,
      });
    }

    if (!teamId || !onlyTeamForms) {
      query.push({
        name: ILike(&#x60;%${term.toLocaleLowerCase()}%&#x60;),
        organizationId: orgId,
        isDeleted: false,
        teamId: IsNull(),
      });
    }
    const forms &#x3D; await this.formRepository.findAll({
      where: query,
    });
    return {
      items: forms,
    };
  }

  /**
   * Updates existing custom fields in batch.
   * @param payload Array of custom field update data.
   * @returns The updated custom fields.
   */
  async update(
    userId: string,
    organizationId: string,
    payload: UpdateFormDto,
    updateDefault: boolean &#x3D; false,
  ) {
    if (!payload || !payload.updates) {
      throw new BadRequestException(&quot;No forms to update&quot;);
    }
    let res;

    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      if (updateDefault) {
        await this.formRepository.updateWithTxn(
          txnContext,
          { organizationId, default: true },
          { default: false, version: () &#x3D;&gt; &quot;version + 1&quot; },
        );
      }

      res &#x3D; await this.formRepository.updateWithTxn(
        txnContext,
        { organizationId, uid: payload.formId, version: payload.version },
        { ...payload.updates, version: () &#x3D;&gt; &quot;version + 1&quot; },
      );
    });

    if (res.affected &#x3D;&#x3D;&#x3D; 1) {
      try {
        const form &#x3D; await this.formRepository.findByCondition({
          where: { uid: payload.formId },
        });
        const formEvent &#x3D; this.formFieldEventRepository.create({
          organization: {
            id: organizationId,
          },
          organizationId: organizationId,
          entityId: payload.formId,
          type: FormFieldEventTypes.FIELD_UPDATED,
          data: form,
          user: {
            id: userId,
          },
          userId: userId,
          version: payload.version + 1,
          createdAt: new Date(),
        });
        await this.formFieldEventRepository.save(formEvent);
      } catch (err) {
        this.logger.error(
          &#x60;Error while trying to add form field event in batch: ${err.message}, stack: ${err.stack}&#x60;,
        );
      }
    } else {
      throw new BadRequestException(
        &#x60;Failed to update form ${payload.formId} due to version mismatch or concurrent modification&#x60;,
      );
    }
  }

  /**
   * Delete fomrs by ids.
   * @param deleteFormDto The form list to delete.
   */
  async delete(userId: string, orgId: string, payload: DeleteFormsDto) {
    if (_.isEmpty(orgId) || _.isEmpty(payload)) {
      throw new Error(&#x60;Params are missing: org: ${orgId}&#x60;);
    }
    const formEvents &#x3D; [];

    const promises &#x3D; payload.forms.map(async (op) &#x3D;&gt; {
      const res &#x3D; await this.formRepository.update(
        { organizationId: orgId, uid: op.formId, version: op.version },
        { isDeleted: true, version: op.version + 1 },
      );

      if (res.affected &#x3D;&#x3D;&#x3D; 1) {
        try {
          const form &#x3D; await this.formRepository.findByCondition({
            where: { uid: op.formId },
          });
          formEvents.push({
            organization: {
              id: orgId,
            },
            organizationId: orgId,
            entityId: op.formId,
            type: FormFieldEventTypes.FIELD_DELETED,
            data: form,
            user: {
              id: userId,
            },
            userId: userId,
            version: op.version + 1,
            createdAt: new Date(),
          });
        } catch (err) {
          this.logger.error(
            &#x60;Error while trying to add form field event in batch: ${err.message}, stack: ${err.stack}&#x60;,
          );
        }
      } else {
        throw new BadRequestException(
          &#x60;Failed to delete form ${op.formId} due to version mismatch or concurrent modification&#x60;,
        );
      }
    });

    const results &#x3D; await Promise.allSettled(promises);

    try {
      const formEventsDtos &#x3D;
        this.formFieldEventRepository.createMany(formEvents);
      await this.formFieldEventRepository.saveMany(formEventsDtos);
    } catch (err) {
      this.logger.error(
        &#x60;Error while trying to log custom field deletion event: ${err.message}, stack: ${err.stack}&#x60;,
      );
    }

    // Collect IDs of fields that failed to update
    const failedUpdates &#x3D; results
      .map((result, index) &#x3D;&gt;
        result.status &#x3D;&#x3D;&#x3D; &quot;rejected&quot; ? payload.forms[index].formId : null,
      )
      .filter((id) &#x3D;&gt; id !&#x3D;&#x3D; null);

    if (failedUpdates.length &gt; 0) {
      throw new BadRequestException(
        &#x60;Some forms could not be deleted due to version mismatch or concurrent modification, failed forms: ${failedUpdates.join(
          &quot;, &quot;,
        )}&#x60;,
      );
    }

    return payload.forms;
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'FormService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
