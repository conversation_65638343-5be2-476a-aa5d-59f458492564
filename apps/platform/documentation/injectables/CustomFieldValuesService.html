<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >CustomFieldValuesService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/custom-field/services/custom-field-values.service.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#create" >create</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createCustomFieldValues" >createCustomFieldValues</a>
                            </li>
                            <li>
                                <a href="#findAll" >findAll</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findOne" >findOne</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#remove" >remove</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#removeDuplicates" >removeDuplicates</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#update" >update</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(customFieldValuesRepository: CustomFieldValuesRepository, customFieldService: <a href="../injectables/CustomFieldService.html" target="_self">CustomFieldService</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="18" class="link-to-prism">src/custom-field/services/custom-field-values.service.ts:18</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>customFieldValuesRepository</td>
                                                  
                                                        <td>
                                                                    <code>CustomFieldValuesRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>customFieldService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/CustomFieldService.html" target="_self" >CustomFieldService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="create"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>create</b></span>
                        <a href="#create"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>create(orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, createCustomFieldValuesDto: <a href="../classes/CreateCustomFieldValuesDto.html" target="_self">CreateCustomFieldValuesDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="86"
                                    class="link-to-prism">src/custom-field/services/custom-field-values.service.ts:86</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Creates a new custom field.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>createCustomFieldValuesDto</td>
                                            <td>
                                                            <code><a href="../classes/CreateCustomFieldValuesDto.html" target="_self" >CreateCustomFieldValuesDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The custom field data to create.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;CustomFieldValues&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The created custom field.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createCustomFieldValues"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>createCustomFieldValues</b></span>
                        <a href="#createCustomFieldValues"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createCustomFieldValues(customFieldValues: Array<literal type>, organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="179"
                                    class="link-to-prism">src/custom-field/services/custom-field-values.service.ts:179</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Creates custom field values for a ticket</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>customFieldValues</td>
                                            <td>
                                                        <code>Array&lt;literal type&gt;</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The custom field values to create</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The organization ID</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;CustomFieldValues[]&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>Array of created custom field values</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAll"></a>
                    <span class="name">
                        <span ><b>findAll</b></span>
                        <a href="#findAll"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findAll()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="61"
                                    class="link-to-prism">src/custom-field/services/custom-field-values.service.ts:61</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds all custom fields.</p>
</div>

                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;CustomFieldValues[]&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>All custom fields.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findOne"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findOne</b></span>
                        <a href="#findOne"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findOne(id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="70"
                                    class="link-to-prism">src/custom-field/services/custom-field-values.service.ts:70</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds a custom field by ID.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the custom field to find.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;CustomFieldValues&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The custom field.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="remove"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>remove</b></span>
                        <a href="#remove"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>remove(id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="162"
                                    class="link-to-prism">src/custom-field/services/custom-field-values.service.ts:162</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Removes a custom field by ID.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the custom field to remove.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;void&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="removeDuplicates"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>removeDuplicates</b></span>
                        <a href="#removeDuplicates"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>removeDuplicates(data: <a href="../classes/CreateCustomFieldValuesDto.html" target="_self">CreateCustomFieldValuesDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="29"
                                    class="link-to-prism">src/custom-field/services/custom-field-values.service.ts:29</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Helper method to remove duplicates from data array based on value and id</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>data</td>
                                            <td>
                                                            <code><a href="../classes/CreateCustomFieldValuesDto.html" target="_self" >CreateCustomFieldValuesDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>Array of CreateCustomFieldValuesDto[&quot;data&quot;] objects</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="update"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>update</b></span>
                        <a href="#update"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>update(id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, updateCustomFieldValuesDto: <a href="../classes/UpdateCustomFieldValuesDto.html" target="_self">UpdateCustomFieldValuesDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="127"
                                    class="link-to-prism">src/custom-field/services/custom-field-values.service.ts:127</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Updates an existing custom field.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the custom field to update.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>updateCustomFieldValuesDto</td>
                                            <td>
                                                            <code><a href="../classes/UpdateCustomFieldValuesDto.html" target="_self" >UpdateCustomFieldValuesDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;CustomFieldValues&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The updated custom field.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from &quot;@nestjs/common&quot;;
import {
  CustomFieldValues,
  CustomFieldValuesRepository,
} from &quot;@repo/thena-platform-entities&quot;;
import * as _ from &quot;lodash&quot;;
import {
  CreateCustomFieldValuesDto,
  UpdateCustomFieldValuesDto,
} from &quot;../dto/custom-field-values.dto&quot;;
import { CustomFieldService } from &quot;./custom-field.service&quot;;

@Injectable()
export class CustomFieldValuesService {
  constructor(
    private customFieldValuesRepository: CustomFieldValuesRepository,
    private readonly customFieldService: CustomFieldService,
  ) {}

  /**
   * Helper method to remove duplicates from data array based on value and id
   * @param data Array of CreateCustomFieldValuesDto[&quot;data&quot;] objects
   * @returns Deduplicated array
   */
  private removeDuplicates(
    data: CreateCustomFieldValuesDto[&quot;data&quot;],
  ): CreateCustomFieldValuesDto[&quot;data&quot;] {
    // Create a map to track unique values and ids
    const valueMap &#x3D; new Map&lt;string, boolean&gt;();
    const idMap &#x3D; new Map&lt;string, boolean&gt;();

    return data.filter((item) &#x3D;&gt; {
      // For items with value
      if (item.value) {
        if (valueMap.has(item.value)) {
          return false; // Skip duplicate value
        }
        valueMap.set(item.value, true);
      }

      // For items with id
      if (item.id) {
        if (idMap.has(item.id)) {
          return false; // Skip duplicate id
        }
        idMap.set(item.id, true);
      }

      return true;
    });
  }

  /**
   * Finds all custom fields.
   * @returns All custom fields.
   */
  findAll(): Promise&lt;CustomFieldValues[]&gt; {
    return this.customFieldValuesRepository.findAll();
  }

  /**
   * Finds a custom field by ID.
   * @param id The ID of the custom field to find.
   * @returns The custom field.
   */
  async findOne(id: string): Promise&lt;CustomFieldValues&gt; {
    const customField &#x3D; await this.customFieldValuesRepository.findOneById(id);
    if (!customField) {
      throw new NotFoundException(
        &quot;Custom field with provided id was not found!&quot;,
      );
    }

    return customField;
  }

  /**
   * Creates a new custom field.
   * @param createCustomFieldValuesDto The custom field data to create.
   * @returns The created custom field.
   */
  async create(
    orgId: string,
    createCustomFieldValuesDto: CreateCustomFieldValuesDto,
  ): Promise&lt;CustomFieldValues&gt; {
    try {
      const customFields &#x3D; await this.customFieldService.findByIds(orgId, [
        createCustomFieldValuesDto.customFieldId,
      ]);
      const customField &#x3D; customFields.items[0];
      if (!customField) {
        throw new NotFoundException(
          &quot;Custom field with provided id was not found!&quot;,
        );
      }

      // Remove duplicates from data array
      const deduplicatedData &#x3D; this.removeDuplicates(
        createCustomFieldValuesDto.data,
      );

      const customFieldValues &#x3D; this.customFieldValuesRepository.create({
        organization: {
          id: createCustomFieldValuesDto.organizationId,
        },
        customField: customField,
        data: deduplicatedData,
        metadata: createCustomFieldValuesDto.metadata,
      });

      return this.customFieldValuesRepository.save(customFieldValues);
    } catch (err) {
      console.log(err);
    }
  }

  /**
   * Updates an existing custom field.
   * @param id The ID of the custom field to update.
   * @param updateCustomFieldDto The custom field data to update.
   * @returns The updated custom field.
   */
  async update(
    id: string,
    updateCustomFieldValuesDto: UpdateCustomFieldValuesDto,
  ): Promise&lt;CustomFieldValues&gt; {
    // Check if the custom field exists
    const customField &#x3D; await this.customFieldValuesRepository.findOneById(id);
    if (_.isEmpty(customField)) {
      throw new NotFoundException(
        &quot;Custom field with provided id was not found!&quot;,
      );
    }

    // Update the custom field name if provided
    if (updateCustomFieldValuesDto.data) {
      // Remove duplicates before updating
      const deduplicatedData &#x3D; this.removeDuplicates(
        updateCustomFieldValuesDto.data,
      );
      customField.data &#x3D; deduplicatedData.map((valueData) &#x3D;&gt; ({
        value: valueData.value,
        id: valueData.id,
      }));
    }

    if (updateCustomFieldValuesDto.metadata) {
      customField.metadata &#x3D; updateCustomFieldValuesDto.metadata;
    }

    return this.customFieldValuesRepository.save(customField);
  }

  /**
   * Removes a custom field by ID.
   * @param id The ID of the custom field to remove.
   */
  async remove(id: string): Promise&lt;void&gt; {
    const customField &#x3D; await this.customFieldValuesRepository.findOneById(id);
    if (_.isEmpty(customField)) {
      throw new NotFoundException(
        &quot;Custom field with provided id was not found!&quot;,
      );
    }

    await this.customFieldValuesRepository.remove(customField);
  }

  /**
   * Creates custom field values for a ticket
   * @param customFieldValues The custom field values to create
   * @param organizationId The organization ID
   * @returns Array of created custom field values
   */
  async createCustomFieldValues(
    customFieldValues: Array&lt;{
      customFieldId: string;
      data: CreateCustomFieldValuesDto[&quot;data&quot;];
      metadata?: Record&lt;string, any&gt;;
    }&gt;,
    organizationId: string,
  ): Promise&lt;CustomFieldValues[]&gt; {
    const newCustomFieldValues: CustomFieldValues[] &#x3D; [];

    const customFieldValuesMap &#x3D; new Map&lt;string, boolean&gt;();
    for (const customFieldValue of customFieldValues) {
      if (customFieldValuesMap.has(customFieldValue.customFieldId)) {
        throw new BadRequestException(&quot;Duplicate custom field values found!&quot;);
      }
      customFieldValuesMap.set(customFieldValue.customFieldId, true);
    }

    for (const customFieldValue of customFieldValues) {
      const customFieldValuesDto &#x3D; new CreateCustomFieldValuesDto();
      customFieldValuesDto.customFieldId &#x3D; customFieldValue.customFieldId;
      // Remove duplicates from data array
      customFieldValuesDto.data &#x3D; this.removeDuplicates(customFieldValue.data);
      customFieldValuesDto.metadata &#x3D; customFieldValue.metadata;
      customFieldValuesDto.organizationId &#x3D; organizationId;

      const customField &#x3D; await this.create(
        organizationId,
        customFieldValuesDto,
      );
      newCustomFieldValues.push(customField);
    }

    return newCustomFieldValues;
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'CustomFieldValuesService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
