<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >UsersGrpcService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/users/services/grpc/users-grpc.service.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createBotUser" >createBotUser</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#generateRandomPassword" >generateRandomPassword</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#rollbackBotInstallation" >rollbackBotInstallation</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(logger: ILogger, userRepository: UserRepository, transactionService: TransactionService, usersService: <a href="../injectables/UsersService.html" target="_self">UsersService</a>, authGrpcClient: <a href="../classes/AuthGrpcClient.html" target="_self">AuthGrpcClient</a>, sharedService: <a href="../injectables/SharedService.html" target="_self">SharedService</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="18" class="link-to-prism">src/users/services/grpc/users-grpc.service.ts:18</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>logger</td>
                                                  
                                                        <td>
                                                                    <code>ILogger</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>userRepository</td>
                                                  
                                                        <td>
                                                                    <code>UserRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>transactionService</td>
                                                  
                                                        <td>
                                                                    <code>TransactionService</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>usersService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/UsersService.html" target="_self" >UsersService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>authGrpcClient</td>
                                                  
                                                        <td>
                                                                        <code><a href="../classes/AuthGrpcClient.html" target="_self" >AuthGrpcClient</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>sharedService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/SharedService.html" target="_self" >SharedService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createBotUser"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>createBotUser</b></span>
                        <a href="#createBotUser"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createBotUser(data: users.CreateBotInstallationUserRequest, token: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="40"
                                    class="link-to-prism">src/users/services/grpc/users-grpc.service.ts:40</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Creates a bot user.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>data</td>
                                            <td>
                                                        <code>users.CreateBotInstallationUserRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The data for the bot user.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>token</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The created bot user.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="generateRandomPassword"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>generateRandomPassword</b></span>
                        <a href="#generateRandomPassword"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>generateRandomPassword(length: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="275"
                                    class="link-to-prism">src/users/services/grpc/users-grpc.service.ts:275</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Generates a secure password.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Default value</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>length</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>

                                            <td>
                                                    <code>16</code>
                                            </td>

                                            <td>
                                                    <p>The length of the password.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </div>
                            <div class="io-description">
                                <p>The generated password.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="rollbackBotInstallation"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>rollbackBotInstallation</b></span>
                        <a href="#rollbackBotInstallation"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>rollbackBotInstallation(data: users.RollbackBotInstallationUserRequest)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="204"
                                    class="link-to-prism">src/users/services/grpc/users-grpc.service.ts:204</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Rolls back the bot installation user creation.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>data</td>
                                            <td>
                                                        <code>users.RollbackBotInstallationUserRequest</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The data for the rollback.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                <p>The result of the rollback.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Inject, Injectable } from &quot;@nestjs/common&quot;;
import { RpcException } from &quot;@nestjs/microservices&quot;;
import { CUSTOM_LOGGER_TOKEN, ILogger } from &quot;@repo/nestjs-commons/logger&quot;;
import { users } from &quot;@repo/shared-proto&quot;;
import {
  Team,
  TransactionService,
  UserRepository,
  UserType,
} from &quot;@repo/thena-platform-entities&quot;;
import { Raw } from &quot;typeorm&quot;;
import { SharedService } from &quot;../../../shared/shared.service&quot;;
import { BOT_USER_EMAIL_DOMAIN } from &quot;../../../users/constants&quot;;
import { AuthGrpcClient } from &quot;../../utils&quot;;
import { UsersService } from &quot;../users.service&quot;;

@Injectable()
export class UsersGrpcService {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN)
    private readonly logger: ILogger,

    // User repositories
    private readonly userRepository: UserRepository,

    // Injected services
    private readonly transactionService: TransactionService,
    private readonly usersService: UsersService,
    private readonly authGrpcClient: AuthGrpcClient,

    // Shared module
    private readonly sharedService: SharedService,
  ) {}

  /**
   * Creates a bot user.
   * @param data The data for the bot user.
   * @returns The created bot user.
   */
  async createBotUser(
    data: users.CreateBotInstallationUserRequest,
    token: string,
  ) {
    try {
      const mappedData &#x3D; {
        name: data.name.split(&quot; &quot;).join(&quot;&quot;),
        appId: data.appId,
        teamIds: data.teamIds,
        userType: UserType.BOT_USER,
        organizationId: data.organizationId,
      };

      let userExists &#x3D; false;

      // Check if the organization exists
      const organization &#x3D; await this.sharedService.findOrganization(
        mappedData.organizationId,
      );

      // If the organization does not exist, throw an error
      if (!organization) {
        throw new RpcException(&quot;Organization not found!&quot;);
      }

      // Find all teams
      const teams &#x3D; await this.sharedService.findAllTeams(
        mappedData.teamIds,
        mappedData.organizationId,
      );

      // If no teams are found, throw an error
      if (!teams || teams.length &#x3D;&#x3D;&#x3D; 0) {
        throw new RpcException(&quot;No teams found for the organization!&quot;);
      }

      // If the number of teams found is not equal to the number of team IDs, throw an error
      if (teams.length !&#x3D;&#x3D; mappedData.teamIds.length) {
        throw new RpcException(
          &quot;Some teams were not found for the organization!&quot;,
        );
      }

      // Generate a random password
      const rngPassword &#x3D; this.generateRandomPassword();

      // Run the transaction
      const createdUser &#x3D; await this.transactionService.runInTransaction(
        async (txnContext) &#x3D;&gt; {
          // Check if the user already exists
          let user &#x3D; await this.userRepository.findByCondition({
            where: {
              metadata: Raw(
                (alias) &#x3D;&gt;
                  &#x60;&quot;${alias.split(&quot;.&quot;)[0]}&quot;.&quot;${
                    alias.split(&quot;.&quot;)[1]
                  }&quot;-&gt;&gt;&#x27;appId&#x27; &#x3D; :appId&#x60;,
                { appId: mappedData.appId },
              ),
              organizationId: organization.id,
              userType: UserType.BOT_USER,
            },
          });

          // If the user exists, set the userExists flag to true
          if (user) {
            userExists &#x3D; true;
          }

          // If the bot user does not exist, create it
          if (!user) {
            // Create and save the bot user
            user &#x3D; await this.usersService.createBotUser(
              {
                name: mappedData.name,
                userType: UserType.BOT_USER,
                organizationUid: organization.uid,
                email: &#x60;BOT_${mappedData.name}_${mappedData.appId}@${BOT_USER_EMAIL_DOMAIN}&#x60;,
                password: rngPassword,
                appId: mappedData.appId,
              },
              { authorization: token, org_id: organization.uid },
            );
          }

          // Add the user to the teams
          await this.sharedService.addTeamMemberAsBot(
            teams.map((team) &#x3D;&gt; team.id),
            user.id,
            organization.id,
            txnContext,
          );

          // Return the created user
          return user;
        },
      );

      // If the user exists, sign in the bot user
      if (userExists) {
        return {
          user: createdUser,
          teams,
          appKey: createdUser.metadata.secretKey,
          appSecretKey: createdUser.metadata.secretKey,
        };
      }

      // Sign in the bot user
      const { token: botToken } &#x3D; await this.authGrpcClient.signIn({
        email: createdUser.email,
        password: rngPassword,
        organizationId: organization.uid,
      });

      // Create the API key for the bot user
      let appKey: string;
      let appSecretKey: string;
      try {
        // Create the API key for the bot user
        const { apiKey, secretKey } &#x3D; await this.authGrpcClient.createNewAPIKey(
          botToken,
          organization.uid,
          {
            name: &#x60;BOT_${mappedData.name}_${mappedData.appId}&#x60;,
            description: &#x60;API key for the bot user ${mappedData.name}&#x60;,
          },
        );

        appKey &#x3D; apiKey;
        appSecretKey &#x3D; secretKey;

        await this.usersService.setUserApiKey(createdUser, appSecretKey);
      } catch (error) {
        this.logger.error(
          &#x60;Error encountered while creating API key for the bot user: ${error.message}&#x60;,
          error?.stack,
        );
      }

      // Set the bot token
      return {
        user: createdUser,
        teams,
        appKey,
        appSecretKey,
      };
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          &#x60;Error encountered while creating bot user: ${error.message}&#x60;,
          error?.stack,
        );
      }

      throw new RpcException(&quot;Failed to create the bot user!&quot;);
    }
  }

  /**
   * Rolls back the bot installation user creation.
   * @param data The data for the rollback.
   * @returns The result of the rollback.
   */
  async rollbackBotInstallation(
    data: users.RollbackBotInstallationUserRequest,
  ) {
    const { transactionId, teamIds, organizationId } &#x3D; data;

    let teams: Team[];
    const areTeamsProvided &#x3D; Array.isArray(teamIds) &amp;&amp; teamIds.length &gt; 0;

    // If the team IDs are provided, find all teams
    if (areTeamsProvided) {
      // Find all teams
      teams &#x3D; await this.sharedService.findAllTeams(teamIds, organizationId);

      // If no teams are found, throw an error
      if (!teams || teams.length &#x3D;&#x3D;&#x3D; 0) {
        throw new RpcException(&quot;No teams found for the organization!&quot;);
      }

      // If the number of teams found is not equal to the number of team IDs, throw an error
      if (teams.length !&#x3D;&#x3D; teamIds.length) {
        throw new RpcException(
          &quot;Some teams were not found for the organization!&quot;,
        );
      }
    }

    // Find the bot user
    const user &#x3D; await this.userRepository.findByCondition({
      where: {
        uid: transactionId,
        organizationId,
        userType: UserType.BOT_USER,
      },
    });

    // If the bot user does not exist, throw an error
    if (!user) {
      throw new RpcException(&quot;Bot user not found!&quot;);
    }

    // Check if the bot user is part of any teams
    const isBotPartOfTeams &#x3D; await this.sharedService.belongsToTeams(
      user.id,
      organizationId,
    );

    // Run the transaction
    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      // Delete the team member records
      await this.sharedService.removeTeamMembers(
        teams.map((team) &#x3D;&gt; team.id),
        user.id,
        organizationId,
        txnContext,
      );

      // If the team IDs are not provided, delete the bot user
      if (!areTeamsProvided) {
        // Delete the bot user
        await this.userRepository.softDeleteWithTxn(txnContext, user);
      } else if (isBotPartOfTeams &#x3D;&#x3D;&#x3D; 0) {
        await this.userRepository.softDeleteWithTxn(txnContext, user);
      }
    });
  }

  /**
   * Generates a secure password.
   * @param length The length of the password.
   * @returns The generated password.
   */
  private generateRandomPassword(length: number &#x3D; 16): string {
    // Define character sets
    const uppercase &#x3D; &quot;ABCDEFGHIJKLMNOPQRSTUVWXYZ&quot;;
    const lowercase &#x3D; &quot;abcdefghijklmnopqrstuvwxyz&quot;;
    const numbers &#x3D; &quot;0123456789&quot;;
    const special &#x3D; &quot;!@#$%^&amp;*()_+-&#x3D;[]{}|;:,.&lt;&gt;?&quot;;

    const allChars &#x3D; uppercase + lowercase + numbers + special;

    // Generate crypto-safe random values
    const array &#x3D; new Uint8Array(length);
    crypto.getRandomValues(array);

    // Ensure at least one of each character type
    let password &#x3D;
      uppercase[array[0] % uppercase.length] +
      lowercase[array[1] % lowercase.length] +
      numbers[array[2] % numbers.length] +
      special[array[3] % special.length];

    // Fill remaining length with random characters
    for (let i &#x3D; 4; i &lt; length; i++) {
      password +&#x3D; allChars[array[i] % allChars.length];
    }

    // Shuffle the password to avoid predictable character positions
    return password
      .split(&quot;&quot;)
      .sort(() &#x3D;&gt; crypto.getRandomValues(new Uint8Array(1))[0] - 128)
      .join(&quot;&quot;);
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'UsersGrpcService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
