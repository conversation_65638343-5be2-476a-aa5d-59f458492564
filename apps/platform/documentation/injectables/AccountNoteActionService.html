<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >AccountNoteActionService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/accounts/services/account-note.action.service.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createAccountNote" >createAccountNote</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#deleteAccountNote" >deleteAccountNote</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findAccountNote" >findAccountNote</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findAllAccountNotes" >findAllAccountNotes</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#isNoteTypeAttributeInUse" >isNoteTypeAttributeInUse</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#removeNoteAttachment" >removeNoteAttachment</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateAccountNote" >updateAccountNote</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateNoteTypeAttribute" >updateNoteTypeAttribute</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#validateAndGetNoteType" >validateAndGetNoteType</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(accountNoteRepository: AccountNoteRepository, cachedAccountNoteRepository: CachedAccountNoteRepository, accountCommonService: <a href="../injectables/AccountCommonService.html" target="_self">AccountCommonService</a>, transactionService: TransactionService, activitiesService: <a href="../injectables/ActivitiesService.html" target="_self">ActivitiesService</a>, usersService: <a href="../injectables/UsersService.html" target="_self">UsersService</a>, storageService: <a href="../injectables/StorageService.html" target="_self">StorageService</a>, accountsSNSEventsFactory: <a href="../classes/AccountsSNSEventsFactory.html" target="_self">AccountsSNSEventsFactory</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="34" class="link-to-prism">src/accounts/services/account-note.action.service.ts:34</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>accountNoteRepository</td>
                                                  
                                                        <td>
                                                                    <code>AccountNoteRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>cachedAccountNoteRepository</td>
                                                  
                                                        <td>
                                                                    <code>CachedAccountNoteRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>accountCommonService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/AccountCommonService.html" target="_self" >AccountCommonService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>transactionService</td>
                                                  
                                                        <td>
                                                                    <code>TransactionService</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>activitiesService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/ActivitiesService.html" target="_self" >ActivitiesService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>usersService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/UsersService.html" target="_self" >UsersService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>storageService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/StorageService.html" target="_self" >StorageService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>accountsSNSEventsFactory</td>
                                                  
                                                        <td>
                                                                        <code><a href="../classes/AccountsSNSEventsFactory.html" target="_self" >AccountsSNSEventsFactory</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createAccountNote"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>createAccountNote</b></span>
                        <a href="#createAccountNote"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createAccountNote(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, createAccountNoteDto: <a href="../classes/CreateAccountNoteDto.html" target="_self">CreateAccountNoteDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="226"
                                    class="link-to-prism">src/accounts/services/account-note.action.service.ts:226</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Create an account note</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>createAccountNoteDto</td>
                                            <td>
                                                            <code><a href="../classes/CreateAccountNoteDto.html" target="_self" >CreateAccountNoteDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p><a href="../classes/CreateAccountNoteDto.html">CreateAccountNoteDto</a> The DTO containing the note details</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;AccountNote&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The created account note</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteAccountNote"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>deleteAccountNote</b></span>
                        <a href="#deleteAccountNote"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteAccountNote(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, noteId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="441"
                                    class="link-to-prism">src/accounts/services/account-note.action.service.ts:441</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Delete an account note</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>noteId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The Note ID</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;void&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The deleted account note</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAccountNote"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findAccountNote</b></span>
                        <a href="#findAccountNote"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAccountNote(noteId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, authorId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="137"
                                    class="link-to-prism">src/accounts/services/account-note.action.service.ts:137</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Find an existing account note</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>noteId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The Note ID</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>authorId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The Author ID</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;AccountNote&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The account note</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAllAccountNotes"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findAllAccountNotes</b></span>
                        <a href="#findAllAccountNotes"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAllAccountNotes(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, findAccountNoteDto: <a href="../classes/FindAccountNoteDto.html" target="_self">FindAccountNoteDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="165"
                                    class="link-to-prism">src/accounts/services/account-note.action.service.ts:165</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Find all account notes</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>findAccountNoteDto</td>
                                            <td>
                                                            <code><a href="../classes/FindAccountNoteDto.html" target="_self" >FindAccountNoteDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p><a href="../classes/FindAccountNoteDto.html">FindAccountNoteDto</a> The DTO containing the search criteria</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;AccountNote[]&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The list of account notes</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isNoteTypeAttributeInUse"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>isNoteTypeAttributeInUse</b></span>
                        <a href="#isNoteTypeAttributeInUse"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>isNoteTypeAttributeInUse(noteTypeAttributeId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="89"
                                    class="link-to-prism">src/accounts/services/account-note.action.service.ts:89</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Checks if a note type attribute is in use.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>noteTypeAttributeId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the note type attribute.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;boolean&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>Whether the note type attribute is in use.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="removeNoteAttachment"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>removeNoteAttachment</b></span>
                        <a href="#removeNoteAttachment"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>removeNoteAttachment(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, noteId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, attachmentId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="505"
                                    class="link-to-prism">src/accounts/services/account-note.action.service.ts:505</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Remove an attachment from an account note</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>Current user</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>noteId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The Note ID</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>attachmentId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The Attachment ID</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;void&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateAccountNote"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>updateAccountNote</b></span>
                        <a href="#updateAccountNote"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateAccountNote(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, noteId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, updateAccountNoteDto: <a href="../classes/UpdateAccountNoteDto.html" target="_self">UpdateAccountNoteDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="336"
                                    class="link-to-prism">src/accounts/services/account-note.action.service.ts:336</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Update an account note</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>noteId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The Note ID</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>updateAccountNoteDto</td>
                                            <td>
                                                            <code><a href="../classes/UpdateAccountNoteDto.html" target="_self" >UpdateAccountNoteDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p><a href="../classes/UpdateAccountNoteDto.html">UpdateAccountNoteDto</a> The DTO containing the note details</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;AccountNote&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The updated account note</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateNoteTypeAttribute"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>updateNoteTypeAttribute</b></span>
                        <a href="#updateNoteTypeAttribute"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateNoteTypeAttribute(prevNoteTypeAttributeId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, newNoteTypeAttributeId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="98"
                                    class="link-to-prism">src/accounts/services/account-note.action.service.ts:98</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>prevNoteTypeAttributeId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>newNoteTypeAttributeId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;void&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateAndGetNoteType"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>validateAndGetNoteType</b></span>
                        <a href="#validateAndGetNoteType"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateAndGetNoteType(orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, noteType: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="66"
                                    class="link-to-prism">src/accounts/services/account-note.action.service.ts:66</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Validate and get a note type</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The Organization ID</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>noteType</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The Note Type UID</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;AccountAttributeValue&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The note type</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from &quot;@nestjs/common&quot;;
import { AccountEvents } from &quot;@repo/thena-eventbridge&quot;;
import {
  AccountAttributeType,
  AccountAttributeValue,
  AccountNote,
  AccountNoteRepository,
  AccountNoteVisibility,
  AuditLogEntityType,
  AuditLogOp,
  AuditLogVisibility,
  CachedAccountNoteRepository,
  TransactionService,
} from &quot;@repo/thena-platform-entities&quot;;
import { cloneDeep } from &quot;lodash&quot;;
import { FindOptionsWhere } from &quot;typeorm&quot;;
import { ActivitiesService } from &quot;../../activities/services/activities.service&quot;;
import { CurrentUser } from &quot;../../common/decorators/user.decorator&quot;;
import { StorageService } from &quot;../../storage/services/storage-service&quot;;
import { UsersService } from &quot;../../users/services/users.service&quot;;
import {
  CreateAccountNoteDto,
  FindAccountNoteDto,
  UpdateAccountNoteDto,
} from &quot;../dtos/account-note.dto&quot;;
import { AccountsSNSEventsFactory } from &quot;../events/accounts-sns-events.factory&quot;;
import { AccountCommonService } from &quot;./account-commons.service&quot;;

@Injectable()
export class AccountNoteActionService {
  constructor(
    // Inject repositories
    private readonly accountNoteRepository: AccountNoteRepository,
    private readonly cachedAccountNoteRepository: CachedAccountNoteRepository,

    // Accounts services
    private readonly accountCommonService: AccountCommonService,

    // Transaction service
    private readonly transactionService: TransactionService,

    // Activities service
    private readonly activitiesService: ActivitiesService,

    // Users service
    private readonly usersService: UsersService,

    // Storage service
    private readonly storageService: StorageService,

    // Event emitter
    private readonly accountsSNSEventsFactory: AccountsSNSEventsFactory,
  ) {}

  /**
   * Validate and get a note type
   *
   * @param orgId The Organization ID
   * @param noteType The Note Type UID
   * @returns The note type
   */
  private async validateAndGetNoteType(
    orgId: string,
    noteType: string,
  ): Promise&lt;AccountAttributeValue&gt; {
    const noteTypeAttribute &#x3D; await this.accountCommonService.getAttributeValue(
      noteType,
      orgId,
    );
    if (
      !noteTypeAttribute ||
      noteTypeAttribute.attribute !&#x3D;&#x3D; AccountAttributeType.NOTE_TYPE
    ) {
      throw new NotFoundException(&quot;Note type not found&quot;);
    }
    return noteTypeAttribute;
  }

  /**
   * Checks if a note type attribute is in use.
   *
   * @param noteTypeAttributeId The ID of the note type attribute.
   * @returns Whether the note type attribute is in use.
   */
  async isNoteTypeAttributeInUse(
    noteTypeAttributeId: string,
  ): Promise&lt;boolean&gt; {
    const count &#x3D; await this.accountNoteRepository.count({
      where: { noteType: noteTypeAttributeId, isActive: true },
    });
    return count &gt; 0;
  }

  async updateNoteTypeAttribute(
    prevNoteTypeAttributeId: string,
    newNoteTypeAttributeId: string,
  ): Promise&lt;void&gt; {
    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      // Find account activities using the previous activity status attribute
      const accountNotes &#x3D; await this.accountNoteRepository.findAll({
        where: { noteType: prevNoteTypeAttributeId },
      });

      if (accountNotes.length &#x3D;&#x3D;&#x3D; 0) {
        return;
      }

      // Update the activity status attribute for each account activity
      for (const accountNote of accountNotes) {
        accountNote.noteType &#x3D; newNoteTypeAttributeId;
      }

      // Save the account activities
      await this.accountNoteRepository.saveManyWithTxn(
        txnContext,
        accountNotes,
      );

      // Invalidate the cache for each account
      this.cachedAccountNoteRepository.invalidateAccountNoteCache({
        uids: accountNotes.map((a) &#x3D;&gt; a.uid),
      });
    });
  }

  /**
   * Find an existing account note
   *
   * @param noteId The Note ID
   * @param authorId The Author ID
   * @returns The account note
   */
  async findAccountNote(
    noteId: string,
    authorId: string,
  ): Promise&lt;AccountNote&gt; {
    const accountNote &#x3D; await this.cachedAccountNoteRepository.findByCondition({
      where: { uid: noteId, isActive: true },
      relations: [&quot;author&quot;, &quot;account&quot;, &quot;noteTypeAttribute&quot;, &quot;attachments&quot;],
    });

    if (
      accountNote &amp;&amp;
      accountNote.visibility &#x3D;&#x3D;&#x3D; AccountNoteVisibility.PRIVATE &amp;&amp;
      accountNote.authorId !&#x3D;&#x3D; authorId
    ) {
      // Not Found Error to be handled by the caller
      return null;
    }

    return accountNote;
  }

  /**
   * Find all account notes
   *
   * @param user The current user
   * @param findAccountNoteDto {@link FindAccountNoteDto} The DTO containing the search criteria
   * @returns The list of account notes
   */
  async findAllAccountNotes(
    user: CurrentUser,
    findAccountNoteDto: FindAccountNoteDto,
  ): Promise&lt;AccountNote[]&gt; {
    const whereClause: FindOptionsWhere&lt;AccountNote&gt; &#x3D; {
      account: {
        organizationId: user.orgId,
      },
      isActive: true,
    };

    if (findAccountNoteDto.accountId) {
      const account &#x3D; await this.accountCommonService.validateAndGetAccount(
        findAccountNoteDto.accountId,
        user.orgId,
      );

      whereClause.accountId &#x3D; account.id;
    }

    if (findAccountNoteDto.type) {
      const noteType &#x3D; await this.validateAndGetNoteType(
        user.orgId,
        findAccountNoteDto.type,
      );

      whereClause.noteType &#x3D; noteType.id;
    }

    const { results: accountNotes } &#x3D;
      await this.accountNoteRepository.fetchPaginatedResults(
        {
          page: findAccountNoteDto.page ?? 0,
          limit: Math.min(findAccountNoteDto.limit ?? 10, 100),
        },
        {
          where: [
            {
              ...whereClause,
              visibility: AccountNoteVisibility.ORGANIZATION,
            },
            {
              ...whereClause,
              visibility: AccountNoteVisibility.PRIVATE,
              authorId: user.sub,
            },
          ],
          relations: [&quot;author&quot;, &quot;account&quot;, &quot;noteTypeAttribute&quot;, &quot;attachments&quot;],
        },
      );

    return accountNotes;
  }

  /**
   * Create an account note
   *
   * @param user The current user
   * @param createAccountNoteDto {@link CreateAccountNoteDto} The DTO containing the note details
   * @returns The created account note
   */
  async createAccountNote(
    user: CurrentUser,
    createAccountNoteDto: CreateAccountNoteDto,
  ): Promise&lt;AccountNote&gt; {
    // Find account
    const account &#x3D; await this.accountCommonService.validateAndGetAccount(
      createAccountNoteDto.accountId,
      user.orgId,
    );

    // get user for author
    const author &#x3D; await this.usersService.findOneByPublicId(user.uid);

    let noteType: AccountAttributeValue;

    if (!createAccountNoteDto.type) {
      // Use default note type if not provided
      const defaultNoteType &#x3D;
        await this.accountCommonService.findDefaultAttributeValue(
          user.orgId,
          AccountAttributeType.NOTE_TYPE,
        );

      noteType &#x3D; defaultNoteType;
    } else {
      // Use provided note type
      noteType &#x3D; await this.validateAndGetNoteType(
        user.orgId,
        createAccountNoteDto.type,
      );
    }

    const note &#x3D; this.accountNoteRepository.create({
      accountId: account.id,
      noteType: noteType.id,
      authorId: author.id,
      visibility:
        createAccountNoteDto.visibility ?? AccountNoteVisibility.ORGANIZATION,
      content: createAccountNoteDto.content ?? &quot;&quot;,
    });

    const noteId &#x3D; await this.transactionService.runInTransaction(
      async (txnContext) &#x3D;&gt; {
        // Save in db
        const savedNote &#x3D; await this.accountNoteRepository.saveWithTxn(
          txnContext,
          note,
        );

        // Save attachments
        if (createAccountNoteDto.attachmentUrls) {
          savedNote.attachments &#x3D; await this.storageService.attachFilesToEntity(
            &quot;account-note&quot;,
            savedNote.uid,
            createAccountNoteDto.attachmentUrls,
            user.orgId,
            &#x60;${user.orgUid}/accounts/${account.uid}/notes/${savedNote.uid}&#x60;,
          );

          await this.accountNoteRepository.saveWithTxn(txnContext, savedNote);
        }

        // Record audit log
        await this.activitiesService.recordAuditLog(
          {
            organization: { id: user.orgId },
            activityPerformedBy: { id: user.sub },
            entityId: savedNote.id,
            entityType: AuditLogEntityType.ACCOUNT_NOTE,
            op: AuditLogOp.CREATED,
            visibility: AuditLogVisibility.ORGANIZATION,
            activity: &#x60;Account note ${savedNote.id} for account ${account.id} was created!&#x60;,
            description: &#x60;Account note ${savedNote.id} for account ${account.id} was created by ${user.email}!&#x60;,
          },
          txnContext,
        );

        // Invalidate cache
        await this.cachedAccountNoteRepository.invalidateAccountNoteCache({
          uids: [savedNote.uid],
        });

        // Publish account note created event to SNS
        const accountNoteCreatedEvent &#x3D;
          this.accountsSNSEventsFactory.createAccountNoteCreatedSNSEvent(
            user,
            savedNote,
          );

        this.accountCommonService.publishEventToSNSQueue(
          AccountEvents.ACCOUNT_NOTE_CREATED,
          accountNoteCreatedEvent,
          user,
        );

        return savedNote.uid;
      },
    );

    return this.findAccountNote(noteId, user.sub);
  }

  /**
   * Update an account note
   *
   * @param user The current user
   * @param noteId The Note ID
   * @param updateAccountNoteDto {@link UpdateAccountNoteDto} The DTO containing the note details
   * @returns The updated account note
   */
  async updateAccountNote(
    user: CurrentUser,
    noteId: string,
    updateAccountNoteDto: UpdateAccountNoteDto,
  ): Promise&lt;AccountNote&gt; {
    // Find account note
    const note &#x3D; await this.findAccountNote(noteId, user.sub);

    if (!note) {
      throw new NotFoundException(&quot;Account note not found&quot;);
    }

    const previousNote &#x3D; cloneDeep(note);

    if (updateAccountNoteDto.type) {
      const noteType &#x3D; await this.validateAndGetNoteType(
        user.orgId,
        updateAccountNoteDto.type,
      );

      note.noteTypeAttribute &#x3D; noteType;
    }

    note.content &#x3D; updateAccountNoteDto.content ?? previousNote.content;

    if (updateAccountNoteDto.visibility) {
      if (
        updateAccountNoteDto.visibility &#x3D;&#x3D;&#x3D; AccountNoteVisibility.PRIVATE &amp;&amp;
        previousNote.authorId !&#x3D;&#x3D; user.sub
      ) {
        throw new BadRequestException(
          &quot;Only the author can make a note private&quot;,
        );
      }
      note.visibility &#x3D; updateAccountNoteDto.visibility;
    }

    if (updateAccountNoteDto.attachmentUrls) {
      const newAttachments &#x3D; await this.storageService.attachFilesToEntity(
        &quot;account-note&quot;,
        note.uid,
        updateAccountNoteDto.attachmentUrls,
        user.orgId,
        &#x60;${user.orgUid}/accounts/${note.account.uid}/notes/${note.uid}&#x60;,
      );

      note.attachments &#x3D; [...(note.attachments ?? []), ...newAttachments];
    }

    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      // Save in db
      await this.accountNoteRepository.saveWithTxn(txnContext, note);

      // Record audit log
      await this.activitiesService.recordAuditLog(
        {
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityId: note.id,
          entityType: AuditLogEntityType.ACCOUNT_NOTE,
          op: AuditLogOp.UPDATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: &#x60;Account note ${note.id} for account ${note.accountId} was updated!&#x60;,
          description: &#x60;Account note ${note.id} for account ${note.accountId} was updated by ${user.email}!&#x60;,
          metadata: {
            updatedFields: Object.keys(updateAccountNoteDto).map((key) &#x3D;&gt; ({
              field: key,
              previousValue: previousNote[key],
              updatedToValue: note[key],
            })),
          },
        },
        txnContext,
      );

      // Invalidate cache
      await this.cachedAccountNoteRepository.invalidateAccountNoteCache({
        uids: [note.uid],
      });

      // Publish account note updated event to SNS
      const accountNoteUpdatedEvent &#x3D;
        this.accountsSNSEventsFactory.createAccountNoteUpdatedSNSEvent(
          user,
          previousNote,
          note,
        );

      this.accountCommonService.publishEventToSNSQueue(
        AccountEvents.ACCOUNT_NOTE_UPDATED,
        accountNoteUpdatedEvent,
        user,
      );
    });

    return this.findAccountNote(noteId, user.sub);
  }

  /**
   * Delete an account note
   *
   * @param user The current user
   * @param noteId The Note ID
   * @returns The deleted account note
   */
  async deleteAccountNote(user: CurrentUser, noteId: string): Promise&lt;void&gt; {
    const note &#x3D; await this.findAccountNote(noteId, user.sub);

    if (!note) {
      throw new NotFoundException(&quot;Account note not found&quot;);
    }

    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      note.isActive &#x3D; false;

      // Save in db
      await this.accountNoteRepository.saveWithTxn(txnContext, note);

      // Record audit log
      await this.activitiesService.recordAuditLog(
        {
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityId: note.id,
          entityType: AuditLogEntityType.ACCOUNT_NOTE,
          op: AuditLogOp.DELETED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: &#x60;Account note ${note.id} for account ${note.accountId} was deleted!&#x60;,
          description: &#x60;Account note ${note.id} for account ${note.accountId} was deleted by ${user.email}!&#x60;,
          metadata: {
            updatedFields: [
              {
                field: &quot;isActive&quot;,
                previousValue: &quot;true&quot;,
                updatedToValue: &quot;false&quot;,
              },
            ],
          },
        },
        txnContext,
      );

      // Invalidate cache
      await this.cachedAccountNoteRepository.invalidateAccountNoteCache({
        uids: [note.uid],
      });

      // Publish account note deleted event to SNS
      const accountNoteDeletedEvent &#x3D;
        this.accountsSNSEventsFactory.createAccountNoteDeletedSNSEvent(
          user,
          note,
        );

      this.accountCommonService.publishEventToSNSQueue(
        AccountEvents.ACCOUNT_NOTE_DELETED,
        accountNoteDeletedEvent,
        user,
      );
    });
  }

  /**
   * Remove an attachment from an account note
   *
   * @param user Current user
   * @param noteId The Note ID
   * @param attachmentId The Attachment ID
   */
  async removeNoteAttachment(
    user: CurrentUser,
    noteId: string,
    attachmentId: string,
  ): Promise&lt;void&gt; {
    const note &#x3D; await this.findAccountNote(noteId, user.sub);
    if (!note) {
      throw new NotFoundException(&quot;Note not found!&quot;);
    }
    const previousNote &#x3D; cloneDeep(note);
    const existingAttachmentIds &#x3D; note.attachments?.map((a) &#x3D;&gt; a.uid);

    const attachment &#x3D; note.attachments?.find((a) &#x3D;&gt; a.uid &#x3D;&#x3D;&#x3D; attachmentId);
    if (!attachment) {
      throw new NotFoundException(&quot;Attachment not found!&quot;);
    }

    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      note.attachments &#x3D; note.attachments?.filter(
        (a) &#x3D;&gt; a.uid !&#x3D;&#x3D; attachmentId,
      );

      // Save in db
      await this.accountNoteRepository.saveWithTxn(txnContext, note);

      // Record audit log
      await this.activitiesService.recordAuditLog(
        {
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityId: note.id,
          entityUid: note.uid,
          entityType: AuditLogEntityType.ACCOUNT_NOTE,
          op: AuditLogOp.UPDATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: &#x60;Attachment ${attachmentId} was removed from account note ${note.id}!&#x60;,
          description: &#x60;Attachment ${attachmentId} was removed from account note ${note.id} by ${user.email}!&#x60;,
          metadata: {
            updatedFields: [
              {
                field: &quot;attachments&quot;,
                previousValue: existingAttachmentIds?.join(&quot;,&quot;),
                updatedToValue: note.attachments?.map((a) &#x3D;&gt; a.uid).join(&quot;,&quot;),
              },
            ],
          },
        },
        txnContext,
      );

      // Publish account note updated event to SNS
      const accountNoteUpdatedEvent &#x3D;
        this.accountsSNSEventsFactory.createAccountNoteUpdatedSNSEvent(
          user,
          previousNote,
          note,
        );

      this.accountCommonService.publishEventToSNSQueue(
        AccountEvents.ACCOUNT_NOTE_UPDATED,
        accountNoteUpdatedEvent,
        user,
      );
    });
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'AccountNoteActionService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
