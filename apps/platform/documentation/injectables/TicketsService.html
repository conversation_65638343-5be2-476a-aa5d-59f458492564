<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >TicketsService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/tickets/services/tickets.service.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#archiveTicket" >archiveTicket</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#assignTicket" >assignTicket</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#attachFileToTicket" >attachFileToTicket</a>
                            </li>
                            <li>
                                <a href="#buildCustomFieldValuesFromTicket" >buildCustomFieldValuesFromTicket</a>
                            </li>
                            <li>
                                <a href="#buildFormValidationBodyFromTicket" >buildFormValidationBodyFromTicket</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#canPerformAction" >canPerformAction</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#createCustomFieldValues" >createCustomFieldValues</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#createTicket" >createTicket</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#deleteTicket" >deleteTicket</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#escalateTicket" >escalateTicket</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#fetchStatusPriorityAndType" >fetchStatusPriorityAndType</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#findTicketsByPriorityId" >findTicketsByPriorityId</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#findTicketsByStatusId" >findTicketsByStatusId</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#formValidations" >formValidations</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#getAttachments" >getAttachments</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#getTicketById" >getTicketById</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#getTicketRelated" >getTicketRelated</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#getTickets" >getTickets</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#getTicketsWithCursor" >getTicketsWithCursor</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#getTimeLogsForTicket" >getTimeLogsForTicket</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#getTotalTicketsForUser" >getTotalTicketsForUser</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#linkTickets" >linkTickets</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#logTimeForTicket" >logTimeForTicket</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#markDuplicateTicket" >markDuplicateTicket</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#markOrCreateSubTicket" >markOrCreateSubTicket</a>
                            </li>
                            <li>
                                <a href="#onModuleDestroy" >onModuleDestroy</a>
                            </li>
                            <li>
                                <a href="#onModuleInit" >onModuleInit</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#publishToSNS" class="deprecated-name">publishToSNS</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#reassignTeamToTicket" >reassignTeamToTicket</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#startConsumingMessages" >startConsumingMessages</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#ticketExists" >ticketExists</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#updateTicket" >updateTicket</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#validateAndFetchPriority" >validateAndFetchPriority</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#validateAndFetchStatus" >validateAndFetchStatus</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#validateAndFetchType" >validateAndFetchType</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(snsPublisherService: <a href="../s/SNSPublisherService.html" target="_self">SNSPublisherService</a>, logger: ILogger, sqsProducerService: <a href="../s/SQSProducerService.html" target="_self">SQSProducerService</a>, routingEngine: <a href="../interfaces/RequestRouterEngine.html" target="_self">RequestRouterEngine</a>, slaSqsConsumerService: <a href="../s/SQSConsumerService.html" target="_self">SQSConsumerService</a>, snsPublishQueue: Queue, eventEmitter: EventEmitter2, ticketsEventsFactory: <a href="../injectables/TicketsEventsFactory.html" target="_self">TicketsEventsFactory</a>, ticketRepository: TicketRepository, cachedTicketRepository: CachedTicketRepository, ticketRelationshipsRepository: TicketRelationshipsRepository, cachedTicketRelationshipsRepository: CachedTicketRelationshipsRepository, ticketTimeLogRepository: TicketTimeLogRepository, activitiesService: <a href="../injectables/ActivitiesService.html" target="_self">ActivitiesService</a>, transactionService: TransactionService, usersService: <a href="../injectables/UsersService.html" target="_self">UsersService</a>, teamsService: <a href="../injectables/TeamsService.html" target="_self">TeamsService</a>, accountsService: <a href="../injectables/AccountsService.html" target="_self">AccountsService</a>, customerContactActionService: <a href="../injectables/CustomerContactActionService.html" target="_self">CustomerContactActionService</a>, ticketTypeService: <a href="../injectables/TicketTypeActionService.html" target="_self">TicketTypeActionService</a>, ticketStatusService: <a href="../injectables/TicketStatusActionService.html" target="_self">TicketStatusActionService</a>, ticketPriorityService: <a href="../injectables/TicketPriorityActionService.html" target="_self">TicketPriorityActionService</a>, storageService: <a href="../injectables/StorageService.html" target="_self">StorageService</a>, configService: <a href="../injectables/ConfigService.html" target="_self">ConfigService</a>, customFieldValuesService: <a href="../injectables/CustomFieldValuesService.html" target="_self">CustomFieldValuesService</a>, formsValidatorService: <a href="../injectables/FormsValidatorService.html" target="_self">FormsValidatorService</a>, formService: <a href="../injectables/FormService.html" target="_self">FormService</a>, thenaRestrictedFieldsService: <a href="../injectables/ThenaRestrictedFieldService.html" target="_self">ThenaRestrictedFieldService</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="102" class="link-to-prism">src/tickets/services/tickets.service.ts:102</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>snsPublisherService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../miscellaneous/variables.html#SNSPublisherService" target="_self" >SNSPublisherService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>logger</td>
                                                  
                                                        <td>
                                                                    <code>ILogger</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>sqsProducerService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../miscellaneous/variables.html#SQSProducerService" target="_self" >SQSProducerService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>routingEngine</td>
                                                  
                                                        <td>
                                                                        <code><a href="../interfaces/RequestRouterEngine.html" target="_self" >RequestRouterEngine</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>slaSqsConsumerService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../miscellaneous/variables.html#SQSConsumerService" target="_self" >SQSConsumerService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>snsPublishQueue</td>
                                                  
                                                        <td>
                                                                    <code>Queue</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>eventEmitter</td>
                                                  
                                                        <td>
                                                                    <code>EventEmitter2</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>ticketsEventsFactory</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/TicketsEventsFactory.html" target="_self" >TicketsEventsFactory</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>ticketRepository</td>
                                                  
                                                        <td>
                                                                    <code>TicketRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>cachedTicketRepository</td>
                                                  
                                                        <td>
                                                                    <code>CachedTicketRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>ticketRelationshipsRepository</td>
                                                  
                                                        <td>
                                                                    <code>TicketRelationshipsRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>cachedTicketRelationshipsRepository</td>
                                                  
                                                        <td>
                                                                    <code>CachedTicketRelationshipsRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>ticketTimeLogRepository</td>
                                                  
                                                        <td>
                                                                    <code>TicketTimeLogRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>activitiesService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/ActivitiesService.html" target="_self" >ActivitiesService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>transactionService</td>
                                                  
                                                        <td>
                                                                    <code>TransactionService</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>usersService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/UsersService.html" target="_self" >UsersService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>teamsService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/TeamsService.html" target="_self" >TeamsService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>accountsService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/AccountsService.html" target="_self" >AccountsService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>customerContactActionService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/CustomerContactActionService.html" target="_self" >CustomerContactActionService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>ticketTypeService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/TicketTypeActionService.html" target="_self" >TicketTypeActionService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>ticketStatusService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/TicketStatusActionService.html" target="_self" >TicketStatusActionService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>ticketPriorityService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/TicketPriorityActionService.html" target="_self" >TicketPriorityActionService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>storageService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/StorageService.html" target="_self" >StorageService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>configService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/ConfigService.html" target="_self" >ConfigService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>customFieldValuesService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/CustomFieldValuesService.html" target="_self" >CustomFieldValuesService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>formsValidatorService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/FormsValidatorService.html" target="_self" >FormsValidatorService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>formService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/FormService.html" target="_self" >FormService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>thenaRestrictedFieldsService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/ThenaRestrictedFieldService.html" target="_self" >ThenaRestrictedFieldService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="archiveTicket"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>archiveTicket</b></span>
                        <a href="#archiveTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>archiveTicket(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="2064"
                                    class="link-to-prism">src/tickets/services/tickets.service.ts:2064</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Archives a ticket.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the ticket to archive.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The archived ticket.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="assignTicket"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>assignTicket</b></span>
                        <a href="#assignTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>assignTicket(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, body: <a href="../classes/AssignTicketBody.html" target="_self">AssignTicketBody</a>, query: <a href="../interfaces/AssignTicketQuery.html" target="_self">AssignTicketQuery</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="1706"
                                    class="link-to-prism">src/tickets/services/tickets.service.ts:1706</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Assigns a ticket to an agent.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the ticket to assign.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>body</td>
                                            <td>
                                                            <code><a href="../classes/AssignTicketBody.html" target="_self" >AssignTicketBody</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ticket payload.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>query</td>
                                            <td>
                                                            <code><a href="../interfaces/AssignTicketQuery.html" target="_self" >AssignTicketQuery</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The updated ticket.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="attachFileToTicket"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>attachFileToTicket</b></span>
                        <a href="#attachFileToTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>attachFileToTicket(request: <a href="../interfaces/FastifyRequest.html" target="_self">FastifyRequest</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="2111"
                                    class="link-to-prism">src/tickets/services/tickets.service.ts:2111</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../interfaces/FastifyRequest.html" target="_self" >FastifyRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="buildCustomFieldValuesFromTicket"></a>
                    <span class="name">
                        <span ><b>buildCustomFieldValuesFromTicket</b></span>
                        <a href="#buildCustomFieldValuesFromTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>buildCustomFieldValuesFromTicket(ticket: Ticket, body: CreateTicketBody | UpdateTicketBody)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="2356"
                                    class="link-to-prism">src/tickets/services/tickets.service.ts:2356</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>ticket</td>
                                            <td>
                                                        <code>Ticket</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>body</td>
                                            <td>
                                                        <code>CreateTicketBody | UpdateTicketBody</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>{}</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="buildFormValidationBodyFromTicket"></a>
                    <span class="name">
                        <span ><b>buildFormValidationBodyFromTicket</b></span>
                        <a href="#buildFormValidationBodyFromTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>buildFormValidationBodyFromTicket(ticket: Ticket, body: CreateTicketBody | UpdateTicketBody)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="2327"
                                    class="link-to-prism">src/tickets/services/tickets.service.ts:2327</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>ticket</td>
                                            <td>
                                                        <code>Ticket</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>body</td>
                                            <td>
                                                        <code>CreateTicketBody | UpdateTicketBody</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../classes/CreateTicketBody.html" target="_self" >CreateTicketBody</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="canPerformAction"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>canPerformAction</b></span>
                        <a href="#canPerformAction"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>canPerformAction(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, ticket: Ticket, team: Team, actions: literal type)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="378"
                                    class="link-to-prism">src/tickets/services/tickets.service.ts:378</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>ticket</td>
                                            <td>
                                                        <code>Ticket</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>team</td>
                                            <td>
                                                        <code>Team</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>actions</td>
                                            <td>
                                                        <code>literal type</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createCustomFieldValues"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>createCustomFieldValues</b></span>
                        <a href="#createCustomFieldValues"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createCustomFieldValues(customFieldValues: Array<literal type>, organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="687"
                                    class="link-to-prism">src/tickets/services/tickets.service.ts:687</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Creates custom field values for a ticket</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>customFieldValues</td>
                                            <td>
                                                        <code>Array&lt;literal type&gt;</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The custom field values to create</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The organization ID</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;CustomFieldValues[]&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>Array of created custom field values</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createTicket"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>createTicket</b></span>
                        <a href="#createTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createTicket(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, team: Team, body: <a href="../classes/CreateTicketBody.html" target="_self">CreateTicketBody</a>, source: RequestSource)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="720"
                                    class="link-to-prism">src/tickets/services/tickets.service.ts:720</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Creates a ticket.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>team</td>
                                            <td>
                                                        <code>Team</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>body</td>
                                            <td>
                                                            <code><a href="../classes/CreateTicketBody.html" target="_self" >CreateTicketBody</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ticket payload.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>source</td>
                                            <td>
                                                        <code>RequestSource</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The created ticket.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteTicket"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>deleteTicket</b></span>
                        <a href="#deleteTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteTicket(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="1966"
                                    class="link-to-prism">src/tickets/services/tickets.service.ts:1966</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Deletes a ticket.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the ticket to delete.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The deleted ticket.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="escalateTicket"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>escalateTicket</b></span>
                        <a href="#escalateTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>escalateTicket(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, body: <a href="../classes/EscalateTicketBody.html" target="_self">EscalateTicketBody</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="1609"
                                    class="link-to-prism">src/tickets/services/tickets.service.ts:1609</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Escalates a ticket.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The user.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the ticket to escalate.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>body</td>
                                            <td>
                                                            <code><a href="../classes/EscalateTicketBody.html" target="_self" >EscalateTicketBody</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The escalated ticket.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="fetchStatusPriorityAndType"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>fetchStatusPriorityAndType</b></span>
                        <a href="#fetchStatusPriorityAndType"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>fetchStatusPriorityAndType(statusId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, priorityId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, typeId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, validate?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank">boolean</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="324"
                                    class="link-to-prism">src/tickets/services/tickets.service.ts:324</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>statusId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>priorityId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>typeId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>validate</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findTicketsByPriorityId"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>findTicketsByPriorityId</b></span>
                        <a href="#findTicketsByPriorityId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findTicketsByPriorityId(priorityId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, count?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank">boolean</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="438"
                                    class="link-to-prism">src/tickets/services/tickets.service.ts:438</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds tickets by priority ID.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>priorityId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the priority.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>count</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The tickets.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findTicketsByStatusId"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>findTicketsByStatusId</b></span>
                        <a href="#findTicketsByStatusId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findTicketsByStatusId(statusId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, count?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank">boolean</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="413"
                                    class="link-to-prism">src/tickets/services/tickets.service.ts:413</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds tickets by status ID.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>statusId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the status.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>count</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The tickets.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="formValidations"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>formValidations</b></span>
                        <a href="#formValidations"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>formValidations(orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, body: CreateTicketBody | UpdateTicketBody, form: Form, teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, isTicketClosing?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank">boolean</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="2276"
                                    class="link-to-prism">src/tickets/services/tickets.service.ts:2276</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>body</td>
                                            <td>
                                                        <code>CreateTicketBody | UpdateTicketBody</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>form</td>
                                            <td>
                                                        <code>Form</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>isTicketClosing</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getAttachments"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>getAttachments</b></span>
                        <a href="#getAttachments"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getAttachments(request: <a href="../interfaces/FastifyRequest.html" target="_self">FastifyRequest</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="2201"
                                    class="link-to-prism">src/tickets/services/tickets.service.ts:2201</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>request</td>
                                            <td>
                                                            <code><a href="../interfaces/FastifyRequest.html" target="_self" >FastifyRequest</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../interfaces/SignedFolderContent.html" target="_self" >Promise&lt;SignedFolderContent[]&gt;</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTicketById"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>getTicketById</b></span>
                        <a href="#getTicketById"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTicketById(id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="463"
                                    class="link-to-prism">src/tickets/services/tickets.service.ts:463</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Gets a ticket by its ID.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the ticket.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The ticket.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTicketRelated"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>getTicketRelated</b></span>
                        <a href="#getTicketRelated"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTicketRelated(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, query: <a href="../classes/GetTicketRelatedQuery.html" target="_self">GetTicketRelatedQuery</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="507"
                                    class="link-to-prism">src/tickets/services/tickets.service.ts:507</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Gets the sub-types of a ticket.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the ticket.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>query</td>
                                            <td>
                                                            <code><a href="../classes/GetTicketRelatedQuery.html" target="_self" >GetTicketRelatedQuery</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The query object.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The sub-types of the ticket.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTickets"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>getTickets</b></span>
                        <a href="#getTickets"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTickets(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, team: Team | null, query: <a href="../classes/GetTicketQuery.html" target="_self">GetTicketQuery</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="571"
                                    class="link-to-prism">src/tickets/services/tickets.service.ts:571</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Gets all tickets for the user.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>team</td>
                                            <td>
                                                        <code>Team | null</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>query</td>
                                            <td>
                                                            <code><a href="../classes/GetTicketQuery.html" target="_self" >GetTicketQuery</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The query object.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>All tickets for the user.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTicketsWithCursor"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>getTicketsWithCursor</b></span>
                        <a href="#getTicketsWithCursor"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTicketsWithCursor(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, query: literal type)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="624"
                                    class="link-to-prism">src/tickets/services/tickets.service.ts:624</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Gets the tickets with cursor.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The user making the request.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>query</td>
                                            <td>
                                                        <code>literal type</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The query object.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The tickets and the cursor.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTimeLogsForTicket"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>getTimeLogsForTicket</b></span>
                        <a href="#getTimeLogsForTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTimeLogsForTicket(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="2229"
                                    class="link-to-prism">src/tickets/services/tickets.service.ts:2229</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Gets the time logs for a ticket</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the ticket</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The time logs for the ticket</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTotalTicketsForUser"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>getTotalTicketsForUser</b></span>
                        <a href="#getTotalTicketsForUser"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTotalTicketsForUser(userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="478"
                                    class="link-to-prism">src/tickets/services/tickets.service.ts:478</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Gets the total number of tickets for a user.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>userId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the user.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The total number of tickets for the user.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="linkTickets"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>linkTickets</b></span>
                        <a href="#linkTickets"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>linkTickets(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, body: <a href="../classes/LinkTicketsBody.html" target="_self">LinkTicketsBody</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="991"
                                    class="link-to-prism">src/tickets/services/tickets.service.ts:991</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Links two tickets.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>body</td>
                                            <td>
                                                            <code><a href="../classes/LinkTicketsBody.html" target="_self" >LinkTicketsBody</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ticket payload.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;Ticket&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The linked tickets.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="logTimeForTicket"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>logTimeForTicket</b></span>
                        <a href="#logTimeForTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>logTimeForTicket(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, body: <a href="../classes/TicketTimeLogDto.html" target="_self">TicketTimeLogDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="2248"
                                    class="link-to-prism">src/tickets/services/tickets.service.ts:2248</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Logs time for a ticket</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the ticket</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>body</td>
                                            <td>
                                                            <code><a href="../classes/TicketTimeLogDto.html" target="_self" >TicketTimeLogDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The time log body</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The logged ticket</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="markDuplicateTicket"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>markDuplicateTicket</b></span>
                        <a href="#markDuplicateTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>markDuplicateTicket(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, body: <a href="../classes/MarkDuplicateBody.html" target="_self">MarkDuplicateBody</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="1077"
                                    class="link-to-prism">src/tickets/services/tickets.service.ts:1077</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Marks a ticket as a duplicate.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>body</td>
                                            <td>
                                                            <code><a href="../classes/MarkDuplicateBody.html" target="_self" >MarkDuplicateBody</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ticket payload.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;Ticket&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The parent ticket.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="markOrCreateSubTicket"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>markOrCreateSubTicket</b></span>
                        <a href="#markOrCreateSubTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>markOrCreateSubTicket(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, body: <a href="../classes/MarkOrCreateSubTicketBody.html" target="_self">MarkOrCreateSubTicketBody</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="1174"
                                    class="link-to-prism">src/tickets/services/tickets.service.ts:1174</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Marks a ticket as a sub-ticket or creates a new sub-ticket.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>body</td>
                                            <td>
                                                            <code><a href="../classes/MarkOrCreateSubTicketBody.html" target="_self" >MarkOrCreateSubTicketBody</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ticket payload.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;Ticket&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The created sub-ticket.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="onModuleDestroy"></a>
                    <span class="name">
                        <span ><b>onModuleDestroy</b></span>
                        <a href="#onModuleDestroy"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>onModuleDestroy()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="153"
                                    class="link-to-prism">src/tickets/services/tickets.service.ts:153</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="onModuleInit"></a>
                    <span class="name">
                        <span ><b>onModuleInit</b></span>
                        <a href="#onModuleInit"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>onModuleInit()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="150"
                                    class="link-to-prism">src/tickets/services/tickets.service.ts:150</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="publishToSNS"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span class="deprecated-name"><b>publishToSNS</b></span>
                        <a href="#publishToSNS"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4 deprecated">
                    This method is deprecated and will be removed soon
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>publishToSNS(ticket: Ticket, event: <a href="../objects/TicketEvents.html" target="_self">TicketEvents</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, subject?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="196"
                                    class="link-to-prism">src/tickets/services/tickets.service.ts:196</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Publishes a ticket event to SNS.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>ticket</td>
                                            <td>
                                                        <code>Ticket</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ticket to publish.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>event</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#TicketEvents" target="_self" >TicketEvents</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The event to publish.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The user associated with the ticket.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>subject</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="reassignTeamToTicket"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>reassignTeamToTicket</b></span>
                        <a href="#reassignTeamToTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>reassignTeamToTicket(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, team: Team, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="1872"
                                    class="link-to-prism">src/tickets/services/tickets.service.ts:1872</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Reassigns a ticket to a team.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>team</td>
                                            <td>
                                                        <code>Team</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the ticket to assign.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The updated ticket.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="startConsumingMessages"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>startConsumingMessages</b></span>
                        <a href="#startConsumingMessages"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>startConsumingMessages()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="157"
                                    class="link-to-prism">src/tickets/services/tickets.service.ts:157</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ticketExists"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>ticketExists</b></span>
                        <a href="#ticketExists"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>ticketExists(id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="492"
                                    class="link-to-prism">src/tickets/services/tickets.service.ts:492</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Checks if a ticket exists.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the ticket.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>Whether the ticket exists.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateTicket"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>updateTicket</b></span>
                        <a href="#updateTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateTicket(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, body: <a href="../classes/UpdateTicketBody.html" target="_self">UpdateTicketBody</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="1322"
                                    class="link-to-prism">src/tickets/services/tickets.service.ts:1322</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Updates a ticket.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the ticket to update.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>body</td>
                                            <td>
                                                            <code><a href="../classes/UpdateTicketBody.html" target="_self" >UpdateTicketBody</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ticket payload.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The updated ticket.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateAndFetchPriority"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>validateAndFetchPriority</b></span>
                        <a href="#validateAndFetchPriority"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateAndFetchPriority(priorityId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, priorityName?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="282"
                                    class="link-to-prism">src/tickets/services/tickets.service.ts:282</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Validates and fetches a priority by its ID. Otherwise, returns default priority.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>priorityId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the priority to fetch.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the team.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>priorityName</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The priority.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateAndFetchStatus"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>validateAndFetchStatus</b></span>
                        <a href="#validateAndFetchStatus"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateAndFetchStatus(statusId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, teamId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, statusName?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="232"
                                    class="link-to-prism">src/tickets/services/tickets.service.ts:232</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Validates and fetches a status by its ID. Otherwise, returns default status.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>statusId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the status to fetch.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>teamId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>statusName</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The status.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateAndFetchType"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                        <span ><b>validateAndFetchType</b></span>
                        <a href="#validateAndFetchType"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateAndFetchType(typeId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="369"
                                    class="link-to-prism">src/tickets/services/tickets.service.ts:369</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Validates and fetches a type by its ID.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>typeId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the type to fetch.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The type.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { InjectQueue } from &quot;@nestjs/bullmq&quot;;
import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException,
  OnModuleInit,
} from &quot;@nestjs/common&quot;;
import { EventEmitter2 } from &quot;@nestjs/event-emitter&quot;;
import {
  SQSConsumerService,
  SQSMessage,
  SQSProducerService,
} from &quot;@repo/nestjs-commons/aws-utils/sqs&quot;;
import { ILogger } from &quot;@repo/nestjs-commons/logger&quot;;
import { RequestSource } from &quot;@repo/nestjs-commons/middlewares&quot;;
import {
  ContextUserType,
  SNSPublisherService,
  TicketEvents,
} from &quot;@repo/thena-eventbridge&quot;;
import {
  Account,
  AuditLog,
  AuditLogEntityType,
  AuditLogOp,
  AuditLogVisibility,
  CachedTicketRelationshipsRepository,
  CachedTicketRepository,
  CustomFieldValues,
  Form,
  Team,
  Ticket,
  TicketPriority,
  TicketRelations,
  TicketRelationshipsRepository,
  TicketRepository,
  TicketStatus,
  TicketTimeLogRepository,
  TicketType,
  TransactionService,
  User,
} from &quot;@repo/thena-platform-entities&quot;;
import { Queue } from &quot;bullmq&quot;;
import { FastifyRequest } from &quot;fastify&quot;;
import { cloneDeep, isArray, isEmpty } from &quot;lodash&quot;;
import { DeepPartial, FindManyOptions, In, IsNull } from &quot;typeorm&quot;;
import { v4 as uuidv4 } from &quot;uuid&quot;;
import { AccountsService } from &quot;../../accounts/services/accounts.service&quot;;
import { CustomerContactActionService } from &quot;../../accounts/services/customer-contact.action.service&quot;;
import { ActivitiesService } from &quot;../../activities/services/activities.service&quot;;
import { CurrentUser } from &quot;../../common/decorators&quot;;
import { getMaxLimit } from &quot;../../common/utils/api-helpers.utils&quot;;
import {
  extractEmailDetails,
  extractNameFromEmail,
} from &quot;../../common/utils/extract-email-details&quot;;
import { ConfigKeys, ConfigService } from &quot;../../config/config.service&quot;;
import { QueueNames } from &quot;../../constants/queue.constants&quot;;
import {
  CreateCustomFieldValuesDto,
  ExternalCustomFieldValuesDto,
} from &quot;../../custom-field/dto/custom-field-values.dto&quot;;
import { CustomFieldValuesService } from &quot;../../custom-field/services/custom-field-values.service&quot;;
import { ThenaRestrictedFieldService } from &quot;../../custom-field/services/thena-restricted-field.service&quot;;
import { FormService } from &quot;../../forms/services/form.service&quot;;
import { FormsValidatorService } from &quot;../../forms/validators/form.validator&quot;;
import { SignedFolderContent } from &quot;../../storage/interfaces/storage.interface&quot;;
import { StorageService } from &quot;../../storage/services/storage-service&quot;;
import { TeamsService } from &quot;../../teams/services/teams.service&quot;;
import { UsersService } from &quot;../../users/services/users.service&quot;;
import { GetTicketsRelations, TicketSelect } from &quot;../constants&quot;;
import {
  AssignTicketBody,
  AssignTicketQuery,
  CreateTicketBody,
  EscalateTicketBody,
  GetTicketQuery,
  GetTicketRelatedQuery,
  LinkTicketsBody,
  MarkDuplicateBody,
  MarkOrCreateSubTicketBody,
  TicketTimeLogDto,
  UpdateTicketBody,
} from &quot;../dto&quot;;
import { TicketsEventsFactory } from &quot;../events/tickets-events.factory&quot;;
import { EmittableTicketEvents } from &quot;../events/tickets.events&quot;;
import { SLAMessage } from &quot;../interfaces/sla.interface&quot;;
import { RequestRouterEngine } from &quot;../routing/interfaces/engine.interface&quot;;
import { REQUEST_ROUTER_ENGINE } from &quot;../routing/providers/request-router.provider&quot;;
import { updateSLAMetadata } from &quot;../utils/sla-metadata.utils&quot;;
import {
  EAGER_TICKET_RELATIONS,
  TICKET_SNS_PUBLISHER,
} from &quot;../utils/tickets.constants&quot;;
import { TicketPriorityActionService } from &quot;./ticket-priority.action.service&quot;;
import { TicketStatusActionService } from &quot;./ticket-status.action.service&quot;;
import { TicketTypeActionService } from &quot;./ticket-type.action.service&quot;;

@Injectable()
export class TicketsService implements OnModuleInit {
  constructor(
    @Inject(TICKET_SNS_PUBLISHER)
    private readonly snsPublisherService: SNSPublisherService,

    @Inject(&quot;CustomLogger&quot;)
    private readonly logger: ILogger,

    @Inject(&quot;SLA_SQS_PRODUCER&quot;)
    private readonly sqsProducerService: SQSProducerService,
    // Router
    @Inject(REQUEST_ROUTER_ENGINE)
    private readonly routingEngine: RequestRouterEngine,

    @Inject(&quot;SLA_SQS_CONSUMER&quot;)
    private readonly slaSqsConsumerService: SQSConsumerService,
    @InjectQueue(QueueNames.TICKET_SNS_PUBLISHER)
    private readonly snsPublishQueue: Queue,

    // Event emitter
    private readonly eventEmitter: EventEmitter2,
    private readonly ticketsEventsFactory: TicketsEventsFactory,

    // Injected Repositories
    private readonly ticketRepository: TicketRepository,
    private readonly cachedTicketRepository: CachedTicketRepository,
    private readonly ticketRelationshipsRepository: TicketRelationshipsRepository,
    private readonly cachedTicketRelationshipsRepository: CachedTicketRelationshipsRepository,
    private readonly ticketTimeLogRepository: TicketTimeLogRepository,

    // Injected Services
    private readonly activitiesService: ActivitiesService,
    private readonly transactionService: TransactionService,
    private readonly usersService: UsersService,
    private readonly teamsService: TeamsService,
    private readonly accountsService: AccountsService,
    private readonly customerContactActionService: CustomerContactActionService,
    private readonly ticketTypeService: TicketTypeActionService,
    private readonly ticketStatusService: TicketStatusActionService,
    private readonly ticketPriorityService: TicketPriorityActionService,
    private readonly storageService: StorageService,
    private readonly configService: ConfigService,
    private readonly customFieldValuesService: CustomFieldValuesService,
    private readonly formsValidatorService: FormsValidatorService,
    private readonly formService: FormService,
    private readonly thenaRestrictedFieldsService: ThenaRestrictedFieldService,
  ) {}

  onModuleInit() {
    this.startConsumingMessages();
  }
  onModuleDestroy() {
    this.slaSqsConsumerService.stopConsumer();
  }

  private startConsumingMessages() {
    const messageHandler &#x3D; async (message: SQSMessage) &#x3D;&gt; {
      try {
        const data &#x3D; message.message as Record&lt;string, unknown&gt;;
        const slaMessage: SLAMessage &#x3D; JSON.parse(data.Message as string);
        const user &#x3D; await this.usersService.findOneByPublicId(
          slaMessage.userId,
        );
        this.logger.log(JSON.stringify(user));
        const currentMetadata &#x3D; null; // null if doesn&#x27;t exist
        const updatedMetadata &#x3D; updateSLAMetadata(currentMetadata, slaMessage);
        const currentUser &#x3D; {
          ...user,
          orgUid: slaMessage.organizationId,
          orgId: user.organizationId,
          sub: user.id,
        };
        await this.updateTicket(currentUser, slaMessage.entityId, {
          metadata: {
            sla_details: updatedMetadata.metadata,
          },
        });
        this.logger.log(JSON.stringify(updatedMetadata));
      } catch (error) {
        this.logger.error(&quot;Error processing message:&quot;, error);
        throw error;
      }
    };
    this.slaSqsConsumerService.startConsumer(messageHandler);
  }

  /**
   * Publishes a ticket event to SNS.
   * @param ticket The ticket to publish.
   * @param event The event to publish.
   * @param user The user associated with the ticket.
   * @deprecated This method is deprecated and will be removed soon
   */

  private async publishToSNS(
    ticket: Ticket,
    event: TicketEvents,
    user: CurrentUser,
    subject?: string,
  ) {
    try {
      //TODO: Sourav remove this once this methode is out of use
      await this.snsPublisherService.publishSNSMessage({
        subject: subject ?? &quot;Ticket Event&quot;,
        message: JSON.stringify({ ticketId: ticket.uid }),
        topicArn: this.configService.get(ConfigKeys.AWS_SNS_TICKET_TOPIC_ARN),
        messageAttributes: {
          event_name: event,
          event_id: uuidv4(), // Unique identifier for the event
          event_timestamp: Math.floor(Date.now() / 1000).toString(),
          context_user_id: user.uid,
          context_user_type: ContextUserType.USER,
          context_organization_id: user.orgUid,
        },
      });
    } catch (error) {
      this.logger.error(
        &#x60;Error encountered while publishing ticket ${ticket.uid} for organization ${ticket.organizationId} and team ${ticket.teamId} event ${event} to SNS: ${error?.message}&#x60;,
      );

      throw error;
    }
  }

  /**
   * Validates and fetches a status by its ID. Otherwise, returns default status.
   * @param statusId The ID of the status to fetch.
   * @param orgId The ID of the organization.
   * @returns The status.
   */
  private async validateAndFetchStatus(
    statusId: string,
    orgId: string,
    teamId: string,
    statusName?: string,
  ) {
    // If the status ID and status name are not provided, try using closest match
    if (!statusId &amp;&amp; statusName &amp;&amp; teamId) {
      const closestMatch &#x3D;
        await this.ticketStatusService.findClosestMatchLevenshtein(
          statusName,
          orgId,
          teamId,
        );

      // If the closest match is found, return it
      if (closestMatch) {
        return closestMatch[0];
      }
    }

    // If the status ID is not provided, return the default status
    if (!statusId) {
      return this.ticketStatusService.internalGetDefaultTicketStatus(
        orgId,
        teamId,
      );
    }

    const providedStatus &#x3D;
      await this.ticketStatusService.findTicketStatusByPublicId(
        statusId,
        orgId,
        teamId,
      );

    if (!providedStatus) {
      throw new NotFoundException(&quot;Status not found!&quot;);
    }

    return providedStatus;
  }

  /**
   * Validates and fetches a priority by its ID. Otherwise, returns default priority.
   * @param priorityId The ID of the priority to fetch.
   * @param orgId The ID of the organization.
   * @param teamId The ID of the team.
   * @returns The priority.
   */
  private async validateAndFetchPriority(
    priorityId: string,
    orgId: string,
    teamId: string,
    priorityName?: string,
  ) {
    // If the status ID and status name are not provided, try using closest match
    if (!priorityId &amp;&amp; priorityName &amp;&amp; teamId) {
      const closestMatch &#x3D;
        await this.ticketPriorityService.findClosestMatchLevenshtein(
          priorityName,
          orgId,
          teamId,
        );

      // If the closest match is found, return it
      if (closestMatch) {
        return closestMatch[0];
      }
    }

    if (!priorityId) {
      return this.ticketPriorityService.internalGetDefaultTicketPriority(
        orgId,
        teamId,
      );
    }

    const providedPriority &#x3D;
      await this.ticketPriorityService.findTicketPriorityByPublicId(
        priorityId,
        orgId,
        teamId,
      );

    if (!providedPriority) {
      throw new NotFoundException(&quot;Priority not found!&quot;);
    }

    return providedPriority;
  }

  private async fetchStatusPriorityAndType(
    statusId: string,
    priorityId: string,
    typeId: string,
    orgId: string,
    teamId: string,
    validate?: boolean,
  ) {
    const promises: Array&lt;Promise&lt;TicketStatus | TicketPriority | TicketType&gt;&gt; &#x3D;
      [
        this.validateAndFetchStatus(statusId, orgId, teamId),
        this.validateAndFetchPriority(priorityId, orgId, teamId),
        typeId ? this.validateAndFetchType(typeId, orgId) : null,
      ];

    const [status, priority, type] &#x3D; await Promise.all(promises);

    // If the validate flag is true, validate the status and priority
    if (validate) {
      if (!status) {
        throw new BadRequestException(
          &quot;Default ticket status is required but not configured for the team.&quot;,
        );
      }

      if (!priority) {
        throw new BadRequestException(
          &quot;Default ticket priority is required but not configured for the team.&quot;,
        );
      }

      if (typeId &amp;&amp; !type) {
        throw new BadRequestException(&quot;Provided ticket type was not found!&quot;);
      }
    }

    return { status, priority, type };
  }

  /**
   * Validates and fetches a type by its ID.
   * @param typeId The ID of the type to fetch.
   * @param orgId The ID of the organization.
   * @returns The type.
   */
  private async validateAndFetchType(typeId: string, orgId: string) {
    const providedType &#x3D; await this.ticketTypeService.findTicketTypeByPublicId(
      typeId,
      orgId,
    );

    return providedType;
  }

  private async canPerformAction(
    user: CurrentUser,
    ticket: Ticket,
    team: Team,
    actions: { read: boolean; write: boolean },
  ) {
    // Check if the user belongs to the team
    const isAgentPartOfTeam &#x3D; await this.teamsService.userBelongsToTeam(
      user.sub,
      team.id,
      user.orgId,
    );

    // If the user does not belong to the team, throw an error
    if (!isAgentPartOfTeam) {
      // If the team is private, throw an error
      if (team.isPrivate) {
        throw new NotFoundException(&quot;Team not found!&quot;);
      }

      // If the user does not have write access, throw an error
      if (actions.write) {
        throw new ForbiddenException(&quot;You are not a member of this team!&quot;);
      }
    }

    return { isAgentPartOfTeam };
  }

  /**
   * Finds tickets by status ID.
   * @param statusId The ID of the status.
   * @param organizationId The ID of the organization.
   * @returns The tickets.
   */
  public async findTicketsByStatusId(
    statusId: string,
    organizationId: string,
    count?: boolean,
  ) {
    const query: FindManyOptions&lt;Ticket&gt; &#x3D; {
      where: { statusId, organizationId },
    };

    // If the count flag is true, return the count of tickets
    if (count) {
      return this.ticketRepository.count(query);
    }

    // Otherwise, return the tickets
    const tickets &#x3D; await this.ticketRepository.findAll(query);
    return tickets;
  }

  /**
   * Finds tickets by priority ID.
   * @param priorityId The ID of the priority.
   * @param organizationId The ID of the organization.
   * @returns The tickets.
   */
  public async findTicketsByPriorityId(
    priorityId: string,
    organizationId: string,
    count?: boolean,
  ) {
    const query: FindManyOptions&lt;Ticket&gt; &#x3D; {
      where: { priorityId, organizationId },
    };

    // If the count flag is true, return the count of tickets
    if (count) {
      return this.ticketRepository.count(query);
    }

    // Otherwise, return the tickets
    const tickets &#x3D; await this.ticketRepository.findAll(query);
    return tickets;
  }

  /**
   * Gets a ticket by its ID.
   * @param id The ID of the ticket.
   * @param organizationId The ID of the organization.
   * @returns The ticket.
   */
  public async getTicketById(id: string, organizationId: string) {
    const ticket &#x3D; await this.cachedTicketRepository.findByCondition({
      where: { uid: id, organizationId },
      relations: EAGER_TICKET_RELATIONS,
    });

    return ticket;
  }

  /**
   * Gets the total number of tickets for a user.
   * @param userId The ID of the user.
   * @param orgId The ID of the organization.
   * @returns The total number of tickets for the user.
   */
  public async getTotalTicketsForUser(userId: string, orgId: string) {
    const tickets &#x3D; await this.ticketRepository.count({
      where: { assignedAgentId: userId, organizationId: orgId },
    });

    return tickets;
  }

  /**
   * Checks if a ticket exists.
   * @param id The ID of the ticket.
   * @param organizationId The ID of the organization.
   * @returns Whether the ticket exists.
   */
  public async ticketExists(id: string, organizationId: string) {
    const exists &#x3D; await this.ticketRepository.exists({
      where: { uid: id, organizationId },
    });

    return exists;
  }

  /**
   * Gets the sub-types of a ticket.
   * @param request The request object.
   * @param id The ID of the ticket.
   * @param query The query object.
   * @returns The sub-types of the ticket.
   */
  public async getTicketRelated(
    user: CurrentUser,
    id: string,
    query: GetTicketRelatedQuery,
  ) {
    const { linked, subtickets, duplicate, page, limit } &#x3D; query;
    const MAX_PAGE_SIZE &#x3D; 100;
    const validatedLimit &#x3D; Math.min(limit ?? 50, MAX_PAGE_SIZE);
    const validatedPage &#x3D; Math.max(page ?? 0, 0);

    // Fetch the ticket
    const ticket &#x3D; await this.cachedTicketRepository.findByCondition({
      where: {
        uid: id,
        organizationId: user.orgId,
      },
    });

    // If the ticket is not found, throw an error
    if (!ticket) {
      throw new NotFoundException(&quot;Ticket not found!&quot;);
    }

    // Ensure only one relationship type is specified
    const relationshipTypes &#x3D; [linked, subtickets, duplicate].filter(Boolean);
    if (relationshipTypes.length !&#x3D;&#x3D; 1) {
      throw new BadRequestException(
        &quot;Specify exactly one relationship type: linked, subtickets, or duplicate.&quot;,
      );
    }

    let ticketType &#x3D; null;
    if (linked) {
      ticketType &#x3D; TicketRelations.RELATED;
    } else if (subtickets) {
      ticketType &#x3D; TicketRelations.CHILD;
    } else if (duplicate) {
      ticketType &#x3D; TicketRelations.DUPLICATE;
    } else {
      throw new BadRequestException(&quot;Invalid query parameters!&quot;);
    }

    const relatedTickets &#x3D;
      await this.ticketRelationshipsRepository.fetchPaginatedResults(
        { page: validatedPage, limit: validatedLimit },
        {
          where: {
            relationshipType: ticketType,
            sourceTicketId: ticket.id,
            organizationId: user.orgId,
          },
          relations: [&quot;targetTicket&quot;],
        },
      );

    return relatedTickets;
  }

  /**
   * Gets all tickets for the user.
   * @param request The request object.
   * @param query The query object.
   * @returns All tickets for the user.
   */
  public async getTickets(
    user: CurrentUser,
    team: Team | null,
    query: GetTicketQuery,
  ) {
    // Define the default find options
    const findOptions: FindManyOptions&lt;Ticket&gt; &#x3D; {
      where: {
        organizationId: user.orgId,
        archivedAt: IsNull(),
        deletedAt: IsNull(),
      },
      order: { createdAt: &quot;DESC&quot; },
      relations: [
        &quot;team&quot;,
        &quot;status&quot;,
        &quot;priority&quot;,
        &quot;assignedAgent&quot;,
        &quot;account&quot;,
        &quot;type&quot;,
        &quot;form&quot;,
      ],
    };

    // If the team ID is provided, filter the tickets by the team ID
    if (team) {
      findOptions.where &#x3D; { ...findOptions.where, teamId: team.id };
    } else {
      const userInTeams &#x3D; await this.teamsService.findAllTeams(user);
      findOptions.where &#x3D; {
        ...findOptions.where,
        teamId: In(userInTeams.map((team) &#x3D;&gt; team.id)),
      };
    }

    // Fetch the tickets
    const tickets &#x3D; await this.ticketRepository.fetchPaginatedResults(
      {
        page: query.page ?? 0,
        limit: query.limit ?? 50,
      },
      findOptions,
    );

    return tickets;
  }

  /**
   * Gets the tickets with cursor.
   * @param user The user making the request.
   * @param query The query object.
   * @returns The tickets and the cursor.
   */
  public async getTicketsWithCursor(
    user: CurrentUser,
    query: { limit: number; afterCursor?: string; teamId?: string },
  ) {
    const { limit, afterCursor } &#x3D; query || {};

    // Validate the limit
    const validatedLimit &#x3D; getMaxLimit(limit);

    // Get the teams that the user belongs to
    let teams: Array&lt;{ teamId: string }&gt; | null &#x3D; null;

    // If the team ID is provided, check if the user belongs to the team
    if (query.teamId) {
      // Check if the user belongs to the team
      const { userInTeam, team } &#x3D; await this.teamsService.canUserReadTeam(
        query.teamId,
        user,
      );

      // If the user does not belong to the team, throw an error
      if (!userInTeam) {
        throw new NotFoundException(
          &quot;Either you&#x27;re not a member of this team or this team does not exist!&quot;,
        );
      }

      // Add the team ID to the teams array
      teams &#x3D; [{ teamId: team.id }];
    } else {
      // Get the teams that the user belongs to
      teams &#x3D; await this.teamsService.getTeamsByUser(user, {
        teamId: true,
      });
    }

    // Fetch the tickets
    const { data, cursor } &#x3D; await this.ticketRepository.fetchWithCursor(
      { limit: validatedLimit, order: &quot;DESC&quot;, afterCursor },
      {
        organizationId: user.orgId,
        teamId: In(teams.map((team) &#x3D;&gt; team.teamId)),
      },
      &quot;tickets&quot;,
      [
        { key: &quot;tickets.team&quot;, alias: &quot;team&quot; },
        { key: &quot;tickets.status&quot;, alias: &quot;status&quot; },
        { key: &quot;tickets.priority&quot;, alias: &quot;priority&quot; },
        { key: &quot;tickets.assignedAgent&quot;, alias: &quot;assignedAgent&quot; },
        { key: &quot;tickets.type&quot;, alias: &quot;type&quot; },
        { key: &quot;tickets.account&quot;, alias: &quot;account&quot; },
      ],
    );

    return { data, cursor };
  }

  /**
   * Creates custom field values for a ticket
   * @param customFieldValues The custom field values to create
   * @param organizationId The organization ID
   * @returns Array of created custom field values
   */
  private async createCustomFieldValues(
    customFieldValues: Array&lt;{
      customFieldId: string;
      data: any;
      metadata?: Record&lt;string, any&gt;;
    }&gt;,
    organizationId: string,
  ): Promise&lt;CustomFieldValues[]&gt; {
    const newCustomFieldValues: CustomFieldValues[] &#x3D; [];

    for (const customFieldValue of customFieldValues) {
      const customFieldValuesDto &#x3D; new CreateCustomFieldValuesDto();
      customFieldValuesDto.customFieldId &#x3D; customFieldValue.customFieldId;
      customFieldValuesDto.data &#x3D; customFieldValue.data;
      customFieldValuesDto.metadata &#x3D; customFieldValue.metadata;
      customFieldValuesDto.organizationId &#x3D; organizationId;

      const customField &#x3D; await this.customFieldValuesService.create(
        organizationId,
        customFieldValuesDto,
      );
      newCustomFieldValues.push(customField);
    }

    return newCustomFieldValues;
  }

  /**
   * Creates a ticket.
   * @param request The request object.
   * @param body The ticket payload.
   * @returns The created ticket.
   */
  public async createTicket(
    user: CurrentUser,
    team: Team,
    body: CreateTicketBody,
    source: RequestSource,
  ) {
    // Validate the form
    let form: Form | null &#x3D; null;
    if (!body.formId) {
      form &#x3D; await this.formService.getDefaultForm(user.orgId);
    } else {
      const forms &#x3D; await this.formService.findByIds(
        user.orgId,
        [body.formId],
        [team.id],
      );
      form &#x3D; forms?.items?.[0];

      if (form?.teamId &amp;&amp; form.teamId !&#x3D;&#x3D; team.id) {
        throw new BadRequestException(
          &#x60;Form ${form.uid} does not belong to team ${team?.name || &quot;&quot;}&#x60;,
        );
      }
    }

    let updatedForm: Form | null &#x3D; null;
    if (form) {
      const formData &#x3D; await this.formValidations(
        user.orgId,
        body,
        form,
        body.teamId,
      );
      updatedForm &#x3D; formData.form;
    }

    // Initially code begins here
    const dataPromises: any &#x3D; [
      this.validateAndFetchStatus(
        body.statusId,
        user.orgId,
        team.parentTeam?.id ?? team.id,
        body.statusName,
      ),
      this.validateAndFetchPriority(
        body.priorityId,
        user.orgId,
        team.parentTeam?.id ?? team.id,
        body.priorityName,
      ),
    ];

    // Get type for the ticket if provided otherwise fallback to default
    if (body.typeId) {
      dataPromises.push(this.validateAndFetchType(body.typeId, user.orgId));
    } else {
      dataPromises.push(new Promise((resolve) &#x3D;&gt; resolve(null)));
    }

    // Fetch the account
    let account: Account | null;
    if (body.accountId) {
      account &#x3D; await this.accountsService.findOneByAccountId(
        body.accountId,
        user.orgId,
      );

      if (!account) {
        throw new NotFoundException(&quot;Account not found!&quot;);
      }
    } else {
      const { domain } &#x3D; extractEmailDetails(body.requestorEmail);
      account &#x3D; await this.accountsService.findOneByPrimaryDomain(
        domain,
        user.orgId,
      );
    }

    const { firstName, lastName } &#x3D; extractNameFromEmail(body.requestorEmail);
    try {
      await this.customerContactActionService.createCustomerContact(user, {
        accountIds: account ? [account.uid] : [],
        email: body.requestorEmail,
        firstName,
        lastName,
      });
    } catch (error) {
      // BadRequestException is expected if the contact already exists
      if (!(error instanceof BadRequestException)) {
        throw error;
      }
    }

    // If the assigned agent ID is provided, fetch the agent
    let agent: User | null;
    if (body?.assignedAgentId) {
      agent &#x3D; await this.usersService.findOneByPublicId(body.assignedAgentId);
      if (!agent) {
        throw new NotFoundException(&quot;Assigned agent not found!&quot;);
      }

      // Check if the assigned agent belongs to the team
      const teamMember &#x3D; await this.teamsService.userBelongsToTeam(
        agent.id,
        team.id,
        user.orgId,
      );

      // If the assigned agent does not belong to the team, throw an error
      if (!teamMember) {
        throw new BadRequestException(
          &quot;Assigned agent is not a member of this team!&quot;,
        );
      }
    }

    // Fetch the status and priority
    const [status, priority, type] &#x3D; await Promise.all(dataPromises);

    if (!status) {
      throw new BadRequestException(
        &quot;Default status not set! Please set a default status.&quot;,
      );
    }

    if (!priority) {
      throw new BadRequestException(
        &quot;Default priority not set! Please set a default priority.&quot;,
      );
    }

    // If the type ID is provided but the type is not found, throw an error
    if (body.typeId &amp;&amp; !type) {
      throw new BadRequestException(
        &quot;Default type not set! Please set a default type.&quot;,
      );
    }

    // Create custom field values
    let customFieldValues: CustomFieldValues[] | null &#x3D; null;
    if (body.customFieldValues?.length) {
      customFieldValues &#x3D; await this.createCustomFieldValues(
        body.customFieldValues,
        user.orgId,
      );
    }

    const savedTicket &#x3D; await this.transactionService.runInTransaction(
      async (txnContext) &#x3D;&gt; {
        // Create the ticket
        const ticket &#x3D; this.ticketRepository.create({
          title: body.title,
          description: body.description,
          accountId: account?.id,
          requestorEmail: body.requestorEmail,
          teamId: team.id,
          parentTeam: team.parentTeam
            ? { id: team.parentTeam.id }
            : { id: team.id },
          statusId: status.id,
          organizationId: user.orgId,
          isPrivate: body.isPrivate,
          priorityId: priority.id,
          assignedAgentId: agent?.id,
          dueDate: body.dueDate,
          typeId: type?.id,
          attachments: [], // Initialize empty attachments array
          customFieldValues,
          formId: updatedForm?.id,
          metadata: { external_metadata: body.metadata },
          source,
        });

        // Save the ticket first to get the ID
        const savedTicket &#x3D; await this.ticketRepository.saveWithTxn(
          txnContext,
          ticket,
        );

        // Create the audit log
        const auditLog: DeepPartial&lt;AuditLog&gt; &#x3D; {
          team: { id: team.id },
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityId: savedTicket.id,
          entityUid: savedTicket.uid,
          entityType: AuditLogEntityType.TICKET,
          op: AuditLogOp.CREATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: &#x60;A new ticket was created with id: ${ticket.ticketId}&#x60;,
          description: &#x60;A new ticket was created by ${user.email} in team ${team.name} with title: ${savedTicket.title}&#x60;,
        };

        // Record the audit log
        await this.activitiesService.recordAuditLog(auditLog, txnContext);

        // Process attachments if they exist
        if (body.attachmentUrls?.length &gt; 0) {
          savedTicket.attachments &#x3D;
            await this.storageService.attachFilesToEntity(
              &quot;ticket&quot;,
              savedTicket.id,
              body.attachmentUrls,
              user.orgId,
              &#x60;${user.orgUid}/${team.uid}/tickets/${savedTicket.uid}&#x60;,
            );

          // Save the ticket with attachments
          return this.ticketRepository.saveWithTxn(txnContext, savedTicket);
        }

        // Publish the ticket to SNS
        await this.snsPublishQueue.add(
          QueueNames.TICKET_SNS_PUBLISHER,
          {
            ticket: savedTicket.uid,
            eventType: TicketEvents.CREATED,
            user: user,
            team: team,
          },
          {
            attempts: 3,
            backoff: {
              type: &quot;exponential&quot;,
              delay: 1000, // 1 second
            },
          },
        );

        return savedTicket;
      },
    );

    // Fetch the returnable ticket
    const returnableTicket &#x3D; await this.cachedTicketRepository.findByCondition({
      select: TicketSelect,
      relations: GetTicketsRelations,
      where: { id: savedTicket.id, organizationId: user.orgId },
    });

    // Route this ticket
    this.routingEngine
      .executeRouting(returnableTicket, team, user)
      .catch((routingError) &#x3D;&gt; {
        this.logger.error(&quot;Failed to route ticket!&quot;, routingError);
      });

    // Emit the ticket assigned event
    if (returnableTicket.assignedAgentId) {
      const ticketAssignedEvent &#x3D;
        this.ticketsEventsFactory.createTicketAssignedEvent(
          savedTicket,
          user.orgId,
          team.uid,
        );

      this.eventEmitter.emit(
        EmittableTicketEvents.TICKET_ASSIGNED,
        ticketAssignedEvent,
      );
    }

    return returnableTicket;
  }

  /**
   * Links two tickets.
   * @param request The request object.
   * @param body The ticket payload.
   * @returns The linked tickets.
   */
  public async linkTickets(
    user: CurrentUser,
    body: LinkTicketsBody,
  ): Promise&lt;Ticket&gt; {
    // Fetch the user and team
    const { sourceTicketId, linkedTicketId } &#x3D; body;

    // Fetch the tickets
    const tickets &#x3D; await this.cachedTicketRepository.findAll({
      where: {
        uid: In([sourceTicketId, linkedTicketId]),
        organizationId: user.orgId,
      },
    });

    // Find the source ticket
    const sourceTicket &#x3D; tickets.find(
      (ticket) &#x3D;&gt; ticket.uid &#x3D;&#x3D;&#x3D; sourceTicketId,
    );

    if (!sourceTicket) {
      throw new NotFoundException(&quot;Source ticket not found!&quot;);
    }

    // Find the linked ticket
    const linkedTicket &#x3D; tickets.find(
      (ticket) &#x3D;&gt; ticket.uid &#x3D;&#x3D;&#x3D; linkedTicketId,
    );

    if (!linkedTicket) {
      throw new NotFoundException(&quot;Linked ticket not found!&quot;);
    }

    // Check if the source and linked ticket belong to the same team
    if (sourceTicket.teamId !&#x3D;&#x3D; linkedTicket.teamId) {
      throw new BadRequestException(&quot;Tickets must belong to the same team!&quot;);
    }

    const common &#x3D; {
      relationshipType: TicketRelations.RELATED,
      organizationId: user.orgId,
    };

    // Check if the tickets are already linked
    const alreadyExistingRelationship &#x3D;
      await this.cachedTicketRelationshipsRepository.findByCondition({
        where: {
          sourceTicketId: sourceTicket.id,
          targetTicketId: linkedTicket.id,
          ...common,
        },
      });

    if (alreadyExistingRelationship) {
      throw new BadRequestException(&quot;Tickets are already linked!&quot;);
    }

    // Create the relationship
    const ticketRelationships &#x3D;
      this.cachedTicketRelationshipsRepository.createMany([
        {
          sourceTicketId: sourceTicket.id,
          targetTicketId: linkedTicket.id,
          ...common,
        },
        {
          sourceTicketId: linkedTicket.id,
          targetTicketId: sourceTicket.id,
          ...common,
        },
      ]);

    // Save the relationship
    await this.cachedTicketRelationshipsRepository.saveMany(
      ticketRelationships,
    );

    return sourceTicket;
  }

  /**
   * Marks a ticket as a duplicate.
   * @param request The request object.
   * @param body The ticket payload.
   * @returns The parent ticket.
   */
  public async markDuplicateTicket(
    user: CurrentUser,
    body: MarkDuplicateBody,
  ): Promise&lt;Ticket&gt; {
    const { duplicateOfTicketId, duplicateTicketId } &#x3D; body;

    // Fetch the tickets
    const tickets &#x3D; await this.cachedTicketRepository.findAll({
      where: {
        uid: In([duplicateOfTicketId, duplicateTicketId]),
        organizationId: user.orgId,
      },
    });

    // Find the parent ticket
    const duplicateOfTicket &#x3D; tickets.find(
      (ticket) &#x3D;&gt; ticket.uid &#x3D;&#x3D;&#x3D; duplicateOfTicketId,
    );

    if (!duplicateOfTicket) {
      throw new NotFoundException(&quot;Parent ticket not found!&quot;);
    }

    // Find the duplicate ticket
    const duplicateTicket &#x3D; tickets.find(
      (ticket) &#x3D;&gt; ticket.uid &#x3D;&#x3D;&#x3D; duplicateTicketId,
    );

    if (!duplicateTicket) {
      throw new NotFoundException(&quot;Duplicate ticket not found!&quot;);
    }

    // Check if the parent and duplicate ticket belong to the same team
    if (duplicateOfTicket.teamId !&#x3D;&#x3D; duplicateTicket.teamId) {
      throw new BadRequestException(
        &quot;Parent and duplicate ticket must belong to the same team!&quot;,
      );
    }

    // Check if the duplicate ticket is archived or deleted
    if (duplicateTicket.archivedAt || duplicateTicket.deletedAt) {
      throw new BadRequestException(&quot;Duplicate ticket is archived or deleted!&quot;);
    }

    // Check for circular reference
    const circularReference &#x3D;
      await this.cachedTicketRelationshipsRepository.findByCondition({
        where: {
          sourceTicketId: duplicateTicket.id,
          targetTicketId: duplicateOfTicket.id,
          relationshipType: TicketRelations.DUPLICATE,
          organizationId: user.orgId,
        },
      });

    if (circularReference) {
      throw new BadRequestException(&quot;Circular reference detected!&quot;);
    }

    // Define the common body
    const commonBody &#x3D; {
      sourceTicketId: duplicateOfTicket.id,
      targetTicketId: duplicateTicket.id,
      relationshipType: TicketRelations.DUPLICATE,
      organizationId: user.orgId,
    };

    // Check if the duplicate ticket is already marked as a duplicate
    const isAlreadyMarkedAsDuplicate &#x3D;
      await this.cachedTicketRelationshipsRepository.findByCondition({
        where: commonBody,
      });

    if (isAlreadyMarkedAsDuplicate) {
      throw new BadRequestException(
        &quot;Tickets are already marked as duplicates!&quot;,
      );
    }

    // Archive the duplicate ticket
    await this.archiveTicket(user, duplicateTicketId);

    // Create the relationship
    const ticketRelationship &#x3D;
      this.cachedTicketRelationshipsRepository.create(commonBody);

    await this.cachedTicketRelationshipsRepository.save(ticketRelationship);

    return duplicateOfTicket;
  }

  /**
   * Marks a ticket as a sub-ticket or creates a new sub-ticket.
   * @param request The request object.
   * @param body The ticket payload.
   * @returns The created sub-ticket.
   */
  public async markOrCreateSubTicket(
    user: CurrentUser,
    body: MarkOrCreateSubTicketBody,
  ): Promise&lt;Ticket&gt; {
    // Fetch the user and team
    const { parentTicketId, subTicketId, ...rest } &#x3D; body;

    // Fetch the ticket
    const ticketsToFetch &#x3D; [parentTicketId];
    if (subTicketId) {
      ticketsToFetch.push(subTicketId);
    }

    const tickets &#x3D; await this.cachedTicketRepository.findAll({
      where: { uid: In(ticketsToFetch), organizationId: user.orgId },
    });

    // Find the parent ticket
    const parentTicket &#x3D; tickets.find(
      (ticket) &#x3D;&gt; ticket.uid &#x3D;&#x3D;&#x3D; parentTicketId,
    );

    if (!parentTicket) {
      throw new NotFoundException(&quot;Parent ticket not found!&quot;);
    }

    // Find the sub ticket
    let subTicket: Ticket | null;
    if (subTicketId) {
      const existingTicket &#x3D; tickets.find(
        (ticket) &#x3D;&gt; ticket.uid &#x3D;&#x3D;&#x3D; subTicketId,
      );

      if (!existingTicket) {
        throw new NotFoundException(&quot;Ticket to mark as sub-ticket not found!&quot;);
      }

      // Check if the parent and sub-ticket belong to the same team
      if (parentTicket.teamId !&#x3D;&#x3D; existingTicket.teamId) {
        throw new BadRequestException(
          &quot;Parent and sub-ticket must belong to the same team!&quot;,
        );
      }

      // Check if the sub-ticket is archived or deleted
      if (existingTicket.archivedAt || existingTicket.deletedAt) {
        throw new BadRequestException(&quot;Sub-ticket is archived or deleted!&quot;);
      }

      const existingParentRelation &#x3D;
        await this.ticketRelationshipsRepository.findByCondition({
          where: {
            targetTicketId: existingTicket.id,
            relationshipType: TicketRelations.CHILD,
            organizationId: user.orgId,
          },
        });

      if (existingParentRelation) {
        throw new BadRequestException(&quot;Ticket already has a parent ticket.&quot;);
      }

      // Check for circular parent-child relationship
      const circularRelation &#x3D;
        await this.ticketRelationshipsRepository.findByCondition({
          where: {
            sourceTicketId: existingTicket.id,
            targetTicketId: parentTicket.id,
            relationshipType: TicketRelations.CHILD,
            organizationId: user.orgId,
          },
        });

      if (circularRelation) {
        throw new BadRequestException(
          &quot;Circular parent-child relationship detected.&quot;,
        );
      }

      subTicket &#x3D; existingTicket;
    } else {
      if (!rest.title) {
        throw new BadRequestException(
          &quot;Title is required to create a new sub-ticket!&quot;,
        );
      }

      // Fetch the status and priority
      const { status, priority, type } &#x3D; await this.fetchStatusPriorityAndType(
        body.statusId,
        body.priorityId,
        body?.typeId,
        user.orgId,
        parentTicket.teamId,
        true,
      );

      // If the type is provided and does not belong to the same team as the parent ticket, throw an error
      if (type &amp;&amp; type?.teamId !&#x3D;&#x3D; parentTicket.teamId) {
        throw new BadRequestException(
          &quot;Ticket type must belong to the same team as the parent ticket!&quot;,
        );
      }

      // Create the sub ticket
      const newSubTicket &#x3D; this.cachedTicketRepository.create({
        teamId: parentTicket.teamId,
        accountId: parentTicket?.accountId,
        organizationId: user.orgId,
        title: rest.title,
        description: rest?.description,
        priorityId: priority.id,
        requestorEmail: parentTicket.requestorEmail,
        statusId: status.id,
        typeId: type?.id,
      });

      // Save the sub ticket
      subTicket &#x3D; await this.cachedTicketRepository.save(newSubTicket);
    }

    // Create the relationship
    const ticketRelationship &#x3D; this.cachedTicketRelationshipsRepository.create({
      sourceTicketId: parentTicket.id,
      targetTicketId: subTicket.id,
      relationshipType: TicketRelations.CHILD,
      organizationId: user.orgId,
    });

    // Save the relationship
    await this.cachedTicketRelationshipsRepository.save(ticketRelationship);

    // Fetch the returnable ticket
    const returnableTicket &#x3D; await this.ticketRepository.findByCondition({
      where: { id: subTicket.id, organizationId: user.orgId },
      relations: EAGER_TICKET_RELATIONS,
    });

    return returnableTicket;
  }

  /**
   * Updates a ticket.
   * @param request The request object.
   * @param id The ID of the ticket to update.
   * @param body The ticket payload.
   * @returns The updated ticket.
   */
  public async updateTicket(
    user: CurrentUser,
    id: string,
    body: UpdateTicketBody,
  ) {
    let oldTicket: Ticket | null &#x3D; null;
    // If the body is empty, throw an error
    if (isEmpty(body)) {
      throw new BadRequestException(&quot;No fields provided to update!&quot;);
    }

    // Fetch the ticket
    const ticket &#x3D; await this.cachedTicketRepository.findByCondition({
      where: { uid: id, organizationId: user.orgId },
      relations: EAGER_TICKET_RELATIONS,
    });

    oldTicket &#x3D; cloneDeep(ticket);

    // If the ticket is not found, throw an error
    if (!ticket) {
      throw new NotFoundException(&quot;Ticket not found!&quot;);
    }

    const updatedBody &#x3D; this.buildFormValidationBodyFromTicket(ticket, body);

    // Validate and fetch the status
    let status: TicketStatus | null;
    if (body.statusId) {
      status &#x3D; await this.validateAndFetchStatus(
        body.statusId,
        user.orgId,
        ticket.parentTeam?.id ?? ticket.teamId,
      );

      // If the status is not found, throw an error
      if (!status) {
        throw new NotFoundException(&quot;Provided status was not found!&quot;);
      }

      // Add the status ID to the update set
      ticket.statusId &#x3D; status.id;
      ticket.status &#x3D; status;
    }

    if (ticket.form?.uid) {
      const forms &#x3D; await this.formService.findByIds(user.orgId, [
        ticket.form?.uid,
      ]);
      if (!forms?.items?.length) {
        throw new BadRequestException(&quot;Form not found!&quot;);
      }

      if (forms.items[0].teamId &amp;&amp; forms.items[0].teamId !&#x3D;&#x3D; body.teamId) {
        throw new BadRequestException(
          &quot;Form is not applicable for the provided team!&quot;,
        );
      }

      const isStatusClosed &#x3D;
        ticket?.status?.parentStatus?.name?.toLowerCase() &#x3D;&#x3D;&#x3D; &quot;closed&quot; ||
        ticket?.status?.name?.toLowerCase() &#x3D;&#x3D;&#x3D; &quot;closed&quot;;

      await this.formValidations(
        user.orgId,
        updatedBody,
        forms.items[0],
        body.teamId,
        isStatusClosed,
      );
    }

    if (body.metadata?.sla_details) {
      const slaDetails &#x3D; body.metadata?.sla_details;
      ticket.metadata &#x3D; {
        ...ticket.metadata,
        sla_details: slaDetails,
      };
    }
    // Check if the user can perform the action
    await this.canPerformAction(user, ticket, ticket.team, {
      read: true,
      write: true,
    });

    // Check if the sub-team exists
    if (body.teamId) {
      const subTeam &#x3D; await this.teamsService.findOneByTeamId(
        body.teamId,
        user.orgId,
        ticket.parentTeam.id,
      );

      // If the sub-team is not found, throw an error
      if (!subTeam) {
        throw new NotFoundException(&quot;Provided sub-team was not found.&quot;);
      }
    }

    // Update the ticket
    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      if (body.title) {
        ticket.title &#x3D; body.title;
      }

      if (body.description) {
        ticket.description &#x3D; body.description;
      }

      if (body.dueDate) {
        ticket.dueDate &#x3D; body.dueDate;
      }

      if (body.isPrivate) {
        ticket.isPrivate &#x3D; body.isPrivate;
      }

      if (body.submitterEmail) {
        ticket.submitterEmail &#x3D; body.submitterEmail;
      }

      // Validate and fetch the priority
      let priority: TicketPriority | null;
      if (body.priorityId) {
        priority &#x3D; await this.validateAndFetchPriority(
          body.priorityId,
          user.orgId,
          ticket.parentTeam?.id ?? ticket.teamId,
        );

        // If the priority is not found, throw an error
        if (!priority) {
          throw new NotFoundException(&quot;Provided priority was not found!&quot;);
        }

        // Add the priority ID to the update set
        ticket.priorityId &#x3D; priority.id;
        ticket.priority &#x3D; priority;
      }

      // Get type for the ticket if provided otherwise fallback to default
      let type: TicketType | null;
      if (body.typeId) {
        type &#x3D; await this.validateAndFetchType(body.typeId, user.orgId);

        // If the type is not found, throw an error
        if (!type) {
          throw new BadRequestException(&quot;Ticket type not found!&quot;);
        }

        // Add the type ID to the update set
        ticket.typeId &#x3D; type.id;
        ticket.type &#x3D; type;
      }

      let assignedAgent: User | null;
      if (body.assignedAgentId &#x3D;&#x3D;&#x3D; &quot;UNASSIGN&quot;) {
        // If the assigned agent ID is &quot;UNASSIGN&quot;, set the assigned agent to null
        ticket.assignedAgentId &#x3D; null;
        ticket.assignedAgent &#x3D; null;
      } else if (body.assignedAgentId) {
        assignedAgent &#x3D; await this.usersService.findOneByPublicId(
          body.assignedAgentId,
        );

        // If the assigned agent is not found, throw an error
        if (!assignedAgent) {
          throw new NotFoundException(&quot;Assigned agent not found!&quot;);
        }

        // If the ticket is already assigned to the same user, throw an error
        if (ticket.assignedAgentId &#x3D;&#x3D;&#x3D; assignedAgent.id) {
          throw new BadRequestException(
            &quot;This ticket is already assigned to this user!&quot;,
          );
        }

        // Check if the assigned agent belongs to the team
        const isAgentPartOfTeam &#x3D; await this.teamsService.userBelongsToTeam(
          assignedAgent.id,
          ticket.teamId,
          user.orgId,
        );

        // If the assigned agent does not belong to the team, throw an error
        if (!isAgentPartOfTeam) {
          throw new BadRequestException(
            &quot;Assigned agent was either not a member of this team or not found!&quot;,
          );
        }

        // Add the assigned agent ID to the update set
        ticket.assignedAgentId &#x3D; assignedAgent.id;
        ticket.assignedAgent &#x3D; assignedAgent;
      }

      if (isArray(body.customFieldValues)) {
        const newCustomFieldValues: CustomFieldValues[] &#x3D;
          await this.customFieldValuesService.createCustomFieldValues(
            body.customFieldValues,
            user.orgId,
          );

        const allowedPrevCustomFieldValues: CustomFieldValues[] &#x3D; [];
        const existingCustomFieldValues: CustomFieldValues[] &#x3D;
          ticket.customFieldValues ?? [];

        for (const customFieldValue of existingCustomFieldValues) {
          if (
            !newCustomFieldValues.find(
              (value) &#x3D;&gt;
                value.customField.id &#x3D;&#x3D;&#x3D; customFieldValue.customField.id,
            )
          ) {
            allowedPrevCustomFieldValues.push(customFieldValue);
          }
        }

        ticket.customFieldValues &#x3D; [
          ...allowedPrevCustomFieldValues,
          ...newCustomFieldValues,
        ];
      }

      // Create the audit log
      const auditLog: DeepPartial&lt;AuditLog&gt; &#x3D; {
        team: { id: ticket.teamId },
        organization: { id: user.orgId },
        activityPerformedBy: { id: user.sub },
        entityId: ticket.id,
        entityUid: ticket.uid,
        entityType: AuditLogEntityType.TICKET,
        op: AuditLogOp.UPDATED,
        visibility: AuditLogVisibility.ORGANIZATION,
        activity: &#x60;Ticket ${ticket.ticketId} was updated!&#x60;,
        description: &#x60;Ticket ${ticket.ticketId} was updated by ${user.email}!&#x60;,
      };

      // Record the audit log
      await this.activitiesService.recordAuditLog(auditLog, txnContext);

      // Update the ticket
      const updatedTicket &#x3D; await this.ticketRepository.saveWithTxn(
        txnContext,
        { ...ticket },
      );

      // Publish the ticket to SNS - BULL MQ
      await this.snsPublishQueue.add(
        QueueNames.TICKET_SNS_PUBLISHER,
        {
          ticket: updatedTicket.uid,
          eventType: TicketEvents.UPDATED,
          user: user,
          team: body.teamId,
          previousTicket: oldTicket,
        },
        {
          attempts: 3,
          backoff: {
            type: &quot;exponential&quot;,
            delay: 1000, // 1 second
          },
        },
      );

      return updatedTicket;
    });

    // Invalidate cache for the ticket
    await this.cachedTicketRepository.invalidateTicketCacheWithKey({
      where: { uid: id, organizationId: user.orgId },
      relations: EAGER_TICKET_RELATIONS,
    });

    // Fetch the returnable ticket
    const returnableTicket &#x3D; await this.getTicketById(id, user.orgId);

    return returnableTicket;
  }

  /**
   * Escalates a ticket.
   * @param user The user.
   * @param id The ID of the ticket to escalate.
   * @returns The escalated ticket.
   */
  public async escalateTicket(
    user: CurrentUser,
    id: string,
    body: EscalateTicketBody,
  ) {
    // Fetch the ticket
    const ticket &#x3D; await this.getTicketById(id, user.orgId);
    if (!ticket) {
      throw new NotFoundException(&quot;Ticket not found!&quot;);
    }

    // Check if the user belongs to the team
    const userInTeam &#x3D; await this.teamsService.userBelongsToTeam(
      user.sub,
      ticket.teamId,
      user.orgId,
    );

    // If the user does not belong to the team, throw an error
    if (!userInTeam) {
      throw new ForbiddenException(
        &quot;You are not authorized to escalate this ticket!&quot;,
      );
    }

    // If the ticket is already escalated, throw an error
    if (ticket.isEscalated) {
      throw new BadRequestException(&quot;Ticket already escalated!&quot;);
    }

    // Update the ticket in a transaction
    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      // Update the ticket
      await this.ticketRepository.updateWithTxn(
        txnContext,
        { id: ticket.id },
        {
          isEscalated: true,
          metadata: {
            escalation_details: {
              escalated_by: { userId: user.sub },
              reason: body.reason,
              escalation_time: Date.now(),
              details: body.details,
            },
          },
        },
      );

      // Create the audit log
      const auditLog: DeepPartial&lt;AuditLog&gt; &#x3D; {
        team: { id: ticket.teamId },
        organization: { id: user.orgId },
        activityPerformedBy: { id: user.sub },
        entityId: ticket.id,
        entityUid: ticket.uid,
        entityType: AuditLogEntityType.TICKET,
        op: AuditLogOp.UPDATED,
        visibility: AuditLogVisibility.ORGANIZATION,
        activity: &#x60;Ticket ${ticket.ticketId} was escalated!&#x60;,
        description: &#x60;Ticket ${ticket.ticketId} was escalated by ${user.email}! With reason: ${body.reason}&#x60;,
      };

      // Record the audit log
      await this.activitiesService.recordAuditLog(auditLog, txnContext);

      // Publish the ticket to SNS - BULL MQ
      await this.snsPublishQueue.add(
        QueueNames.TICKET_SNS_PUBLISHER,
        {
          ticket: ticket.uid,
          eventType: TicketEvents.ESCALATED,
          user: user,
          team: ticket.teamId,
        },
        {
          attempts: 3,
          backoff: {
            type: &quot;exponential&quot;,
            delay: 1000, // 1 second
          },
        },
      );
    });

    // Fetch the returnable ticket
    const returnableTicket &#x3D; await this.getTicketById(ticket.uid, user.orgId);
    return returnableTicket;
  }

  /**
   * Assigns a ticket to an agent.
   * @param request The request object.
   * @param id The ID of the ticket to assign.
   * @param body The ticket payload.
   * @returns The updated ticket.
   */
  public async assignTicket(
    user: CurrentUser,
    id: string,
    body: AssignTicketBody,
    query: AssignTicketQuery,
  ) {
    // Assign the ticket
    const staleTicket &#x3D; await this.transactionService.runInTransaction(
      async (txnContext) &#x3D;&gt; {
        // Fetch the ticket
        const ticket &#x3D; await this.cachedTicketRepository.findByCondition({
          where: { uid: id, organizationId: user.orgId },
          relations: EAGER_TICKET_RELATIONS,
        });

        // If the ticket is not found, throw an error
        if (!ticket) {
          throw new NotFoundException(&quot;Ticket not found!&quot;);
        }

        const invalidationQuery &#x3D; {
          where: { uid: id, organizationId: user.orgId },
          relations: EAGER_TICKET_RELATIONS,
        };

        // If the unassign query is provided, unassign the ticket
        if (query.unassign) {
          await this.ticketRepository.updateWithTxn(
            txnContext,
            { id: ticket.id },
            { assignedAgentId: null },
          );

          await this.cachedTicketRepository.invalidateTicketCacheWithKey(
            invalidationQuery,
          );

          return ticket;
        }

        // If the assigned agent ID is provided, fetch the agent
        const agent &#x3D; await this.usersService.findOneByPublicId(
          body.assignedAgentId,
        );

        // If the agent is not found, throw an error
        if (!agent) {
          throw new NotFoundException(&quot;Assigned agent not found!&quot;);
        }

        // Check if the assigned agent belongs to the team
        const teamMember &#x3D; await this.teamsService.userBelongsToTeam(
          agent.id,
          ticket.teamId,
          user.orgId,
        );

        // If the assigned agent does not belong to the team, throw an error
        if (!teamMember) {
          throw new BadRequestException(
            &quot;Assigned agent is not a member of this team!&quot;,
          );
        }

        // If the ticket is already assigned to the agent, return the ticket
        if (ticket.assignedAgentId &#x3D;&#x3D;&#x3D; agent.id) {
          throw new BadRequestException(
            &quot;This ticket is already assigned to this user!&quot;,
          );
        }

        // Create the audit log
        const auditLog: DeepPartial&lt;AuditLog&gt; &#x3D; {
          team: { id: ticket.teamId },
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityId: ticket.id,
          entityUid: ticket.uid,
          entityType: AuditLogEntityType.TICKET,
          op: AuditLogOp.UPDATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: &#x60;Ticket ${ticket.ticketId} was assigned to ${agent.email}!&#x60;,
          description: &#x60;Ticket ${ticket.ticketId} was assigned to ${agent.email} by ${user.email}!&#x60;,
          metadata: {
            updatedFields: [
              {
                field: &quot;assignedAgentId&quot;,
                previousValue: ticket.assignedAgentId,
                updatedToValue: agent.id,
              },
            ],
          },
        };

        // Record the audit log
        await this.activitiesService.recordAuditLog(auditLog, txnContext);

        // Update the ticket
        await this.ticketRepository.updateWithTxn(
          txnContext,
          { id: ticket.id },
          { assignedAgentId: agent.id },
        );

        // Invalidate cache for the ticket
        await this.cachedTicketRepository.invalidateTicketCacheWithKey(
          invalidationQuery,
        );

        // Publish the ticket to SNS - BULL MQ
        await this.snsPublishQueue.add(QueueNames.TICKET_SNS_PUBLISHER, {
          ticket: ticket.uid,
          eventType: TicketEvents.ASSIGNED,
          user: user,
          team: ticket.teamId,
          previousTicket: [],
        });
        return ticket;
      },
    );

    // Fetch the returnable ticket
    const returnableTicket &#x3D; await this.getTicketById(
      staleTicket.uid,
      user.orgId,
    );

    // Emit event for ticket assigned or unassigned
    if (query.unassign) {
      const ticketUnassignedEvent &#x3D;
        this.ticketsEventsFactory.createTicketUnassignedEvent(
          staleTicket,
          user.orgId,
          staleTicket.team.uid,
        );

      // Emit the event
      this.eventEmitter.emit(
        EmittableTicketEvents.TICKET_UNASSIGNED,
        ticketUnassignedEvent,
      );
    } else {
      const ticketAssignedEvent &#x3D;
        this.ticketsEventsFactory.createTicketAssignedEvent(
          returnableTicket,
          user.orgId,
          returnableTicket.team.uid,
        );

      // Emit the event
      this.eventEmitter.emit(
        EmittableTicketEvents.TICKET_ASSIGNED,
        ticketAssignedEvent,
      );
    }

    return returnableTicket;
  }

  /**
   * Reassigns a ticket to a team.
   * @param request The request object.
   * @param id The ID of the ticket to assign.
   * @param body The ticket payload.
   * @returns The updated ticket.
   */
  public async reassignTeamToTicket(user: CurrentUser, team: Team, id: string) {
    const updatedTicket &#x3D; await this.transactionService.runInTransaction(
      async (txnContext) &#x3D;&gt; {
        // Fetch the ticket
        const ticket &#x3D; await this.cachedTicketRepository.findByCondition({
          where: { uid: id, organizationId: user.orgId },
        });

        // If the ticket is not found, throw an error
        if (!ticket) {
          throw new NotFoundException(&quot;Ticket not found!&quot;);
        }

        // If the ticket is assigned to an agent, unassign it
        if (ticket.assignedAgentId) {
          const ticketUnassignedEvent &#x3D;
            this.ticketsEventsFactory.createTicketUnassignedEvent(
              ticket,
              user.orgId,
              team.uid,
            );

          this.eventEmitter.emit(
            EmittableTicketEvents.TICKET_UNASSIGNED,
            ticketUnassignedEvent,
          );
        }

        // Update the ticket
        const updatedTicket &#x3D; this.ticketRepository.saveWithTxn(txnContext, {
          ...ticket,
          teamId: team.id,
          assignedAgentId: null,
        });

        // Create the audit log
        const auditLog: DeepPartial&lt;AuditLog&gt; &#x3D; {
          team: { id: ticket.teamId },
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityId: ticket.id,
          entityUid: ticket.uid,
          entityType: AuditLogEntityType.TICKET,
          op: AuditLogOp.UPDATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: &#x60;Ticket ${ticket.ticketId} was reassigned to team ${team.name}!&#x60;,
          description: &#x60;Ticket ${ticket.ticketId} was reassigned to team ${team.name} by ${user.email}!&#x60;,
          metadata: {
            updatedFields: [
              {
                field: &quot;teamId&quot;,
                previousValue: ticket.teamId,
                updatedToValue: team.id,
              },
            ],
          },
        };

        // Record the audit log
        await this.activitiesService.recordAuditLog(auditLog, txnContext);

        // Invalidate the ticket cache
        await this.cachedTicketRepository.invalidateTicketCacheWithKey({
          where: { uid: id, organizationId: user.orgId },
          relations: EAGER_TICKET_RELATIONS,
        });

        // Publish the ticket to SNS
        await this.publishToSNS(
          { ...ticket, teamId: team.id, assignedAgentId: null },
          TicketEvents.UPDATED,
          user,
          &quot;Ticket Reassigned to Team&quot;,
        );

        return updatedTicket;
      },
    );

    // Fetch the returnable ticket
    const returnableTicket &#x3D; await this.getTicketById(
      updatedTicket.uid,
      user.orgId,
    );

    return returnableTicket;
  }

  /**
   * Deletes a ticket.
   * @param request The request object.
   * @param id The ID of the ticket to delete.
   * @returns The deleted ticket.
   */
  public async deleteTicket(user: CurrentUser, id: string) {
    // Fetch the ticket
    const ticket &#x3D; await this.cachedTicketRepository.findByCondition({
      where: { uid: id, organizationId: user.orgId },
    });

    if (!ticket) {
      throw new NotFoundException(&quot;Ticket not found!&quot;);
    }

    if (ticket.deletedAt) {
      throw new BadRequestException(&quot;Ticket already deleted!&quot;);
    }

    // Check if the user belongs to the team
    const userBelongsToTeam &#x3D; await this.teamsService.userBelongsToTeam(
      user.sub,
      ticket.teamId,
      user.orgId,
    );

    // If the user does not belong to the team, throw an error
    if (!userBelongsToTeam) {
      throw new ForbiddenException(
        &quot;You are not authorized to delete this ticket!&quot;,
      );
    }

    const deletedTicket &#x3D; await this.transactionService.runInTransaction(
      async (txnContext) &#x3D;&gt; {
        // Create audit log
        const auditLog: DeepPartial&lt;AuditLog&gt; &#x3D; {
          team: { id: ticket.teamId },
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityId: ticket.id,
          entityUid: ticket.uid,
          entityType: AuditLogEntityType.TICKET,
          op: AuditLogOp.DELETED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: &#x60;Ticket ${ticket.ticketId} was deleted!&#x60;,
          description: &#x60;Ticket ${ticket.ticketId} was deleted by ${user.email}!&#x60;,
        };

        // Record the audit log
        await this.activitiesService.recordAuditLog(auditLog, txnContext);

        // Get full ticket data before deletion
        const fullTicketData &#x3D; await this.ticketRepository.findByCondition({
          where: { uid: ticket.uid },
          relations: [
            &quot;organization&quot;,
            &quot;status&quot;,
            &quot;priority&quot;,
            &quot;customFieldValues&quot;,
            &quot;tags&quot;,
            &quot;assignedAgent&quot;,
          ],
        });

        // Soft delete the ticket
        const deleted &#x3D; await this.ticketRepository.softDeleteWithTxn(
          txnContext,
          {
            id: ticket.id,
            uid: ticket.uid,
            organizationId: ticket.organizationId,
          },
        );

        // Publish to SNS with full ticket data
        await this.snsPublishQueue.add(QueueNames.TICKET_SNS_PUBLISHER, {
          ticket: fullTicketData.uid,
          eventType: TicketEvents.DELETED,
          user: user,
          team: ticket.teamId,
          previousTicket: fullTicketData,
        });

        // Invalidate the ticket cache
        await this.cachedTicketRepository.invalidateTicketCacheWithKey({
          where: { uid: id, organizationId: user.orgId },
          relations: EAGER_TICKET_RELATIONS,
        });

        return deleted;
      },
    );

    return deletedTicket;
  }

  /**
   * Archives a ticket.
   * @param request The request object.
   * @param id The ID of the ticket to archive.
   * @returns The archived ticket.
   */
  public async archiveTicket(user: CurrentUser, id: string) {
    // Fetch the ticket
    const ticket &#x3D; await this.cachedTicketRepository.findByCondition({
      where: { uid: id, organizationId: user.orgId },
    });

    if (!ticket) {
      throw new NotFoundException(&quot;Ticket not found!&quot;);
    }

    if (ticket.archivedAt) {
      throw new BadRequestException(&quot;Ticket already archived!&quot;);
    }

    // Check if the user belongs to the team
    const userBelongsToTeam &#x3D; await this.teamsService.userBelongsToTeam(
      user.sub,
      ticket.teamId,
      user.orgId,
    );

    // If the user does not belong to the team, throw an error
    if (!userBelongsToTeam) {
      throw new ForbiddenException(
        &quot;You are not authorized to archive this ticket!&quot;,
      );
    }

    // Archive the ticket
    const archivedTicket &#x3D; await this.cachedTicketRepository.save({
      ...ticket,
      id: ticket.id,
      archivedAt: new Date(),
    });

    // Publish the ticket to SNS - BULL MQ
    await this.snsPublishQueue.add(QueueNames.TICKET_SNS_PUBLISHER, {
      ticket: archivedTicket.uid,
      eventType: TicketEvents.ARCHIVED,
      user: user,
      team: archivedTicket.teamId,
      previousTicket: [],
    });

    return archivedTicket;
  }

  public async attachFileToTicket(
    request: FastifyRequest,
    id: string,
    user: CurrentUser,
  ) {
    try {
      const files &#x3D; await request.files();
      if (!files) {
        throw new BadRequestException(&quot;No files were uploaded&quot;);
      }

      const ticket: Ticket &#x3D; await this.ticketRepository.findByCondition({
        where: { uid: id },
      });

      const type &#x3D; &quot;s3&quot;;
      const bucket &#x3D; this.configService.get(ConfigKeys.TICKET_BUCKET);
      const attachments &#x3D; [];
      const fileUrls &#x3D; [];

      // Process each file
      for await (const data of files) {
        const path &#x3D; &#x60;${ticket.organizationId}/${
          ticket.teamId
        }/${id}/${Date.now()}-${data.filename}&#x60;;

        const buffer &#x3D; await data.toBuffer();

        const result &#x3D; await this.storageService.write(
          path,
          buffer,
          type,
          {
            bucket,
            contentType: data.mimetype,
            entityId: ticket.id,
            entityType: &quot;ticket&quot;,
            metadata: {
              originalName: data.filename,
              encoding: data.encoding,
              size: buffer.length.toString(),
              mimeType: data.mimetype,
            },
          },
          ticket.organizationId,
        );

        const fileUrl &#x3D; await this.storageService.getUrl(path, type, {
          bucket,
        });
        attachments.push(result);
        fileUrls.push(fileUrl);
      }

      // Update ticket with all attachments
      ticket.attachments &#x3D; [...(ticket.attachments || []), ...attachments];
      await this.ticketRepository.save(ticket);

      const auditLog: DeepPartial&lt;AuditLog&gt; &#x3D; {
        team: { id: ticket.teamId },
        organization: { id: ticket.organizationId },
        activityPerformedBy: { id: user.sub },
        entityId: ticket.id,
        entityUid: ticket.uid,
        entityType: AuditLogEntityType.TICKET,
        op: AuditLogOp.UPDATED,
        visibility: AuditLogVisibility.ORGANIZATION,
        activity: &#x60;Ticket ${ticket.ticketId} was updated with attachments!&#x60;,
        description: &#x60;Ticket ${ticket.ticketId} was updated with attachments by ${user.email}!&#x60;,
      };

      // Record the audit log
      await this.activitiesService.recordAuditLog(auditLog);

      return {
        id: ticket.uid,
        description: ticket.description,
        status: ticket.status?.displayName ?? ticket.status?.name,
        priority: ticket.priority?.displayName ?? ticket.priority?.name,
        account: ticket.account?.name,
        isPrivate: ticket.isPrivate,
        assignedAgent: ticket.assignedAgent?.name,
        fileUrls,
        attachments: ticket.attachments,
      };
    } catch (error) {
      throw new BadRequestException(&#x60;Failed to upload files: ${error.message}&#x60;);
    }
  }

  public async getAttachments(
    request: FastifyRequest,
    id: string,
  ): Promise&lt;SignedFolderContent[]&gt; {
    const type &#x3D; &quot;s3&quot;;
    const bucket &#x3D; this.configService.get(ConfigKeys.TICKET_BUCKET);
    const ticket &#x3D; await this.ticketRepository.findByCondition({
      where: { uid: id, organizationId: request.user.orgId },
    });

    if (!ticket) {
      throw new NotFoundException(&quot;Ticket not found!&quot;);
    }

    const folderPrefix &#x3D; &#x60;${ticket.organizationId}/${ticket.teamId}/${id}&#x60;;
    return this.storageService.getFolderContentsWithSignedUrls(
      bucket,
      folderPrefix,
      type,
    );
  }

  /**
   * Gets the time logs for a ticket
   * @param user The current user
   * @param id The ID of the ticket
   * @returns The time logs for the ticket
   */
  public async getTimeLogsForTicket(user: CurrentUser, id: string) {
    const ticket &#x3D; await this.getTicketById(id, user.orgId);
    if (!ticket) {
      throw new NotFoundException(&quot;Ticket not found!&quot;);
    }

    return this.ticketTimeLogRepository.findAll({
      where: { ticketId: ticket.id, organizationId: user.orgId },
      relations: [&quot;user&quot;, &quot;ticket&quot;],
    });
  }

  /**
   * Logs time for a ticket
   * @param user The current user
   * @param id The ID of the ticket
   * @param body The time log body
   * @returns The logged ticket
   */
  public async logTimeForTicket(
    user: CurrentUser,
    id: string,
    body: TicketTimeLogDto,
  ) {
    const ticket &#x3D; await this.getTicketById(id, user.orgId);
    if (!ticket) {
      throw new NotFoundException(&quot;Ticket not found!&quot;);
    }

    // Record the time log
    const recordedTimeLog &#x3D; await this.ticketTimeLogRepository.recordTimeLog({
      ticketId: ticket.id,
      userId: user.sub,
      description: body.description,
      timeSpentMinutes: body.timeSpentMinutes,
      organizationId: user.orgId,
    });

    // Fetch the logged time log
    const loggedTimeLog &#x3D; await this.ticketTimeLogRepository.findByCondition({
      where: { id: recordedTimeLog.id },
      relations: [&quot;user&quot;, &quot;ticket&quot;],
    });

    return loggedTimeLog;
  }

  async formValidations(
    orgId: string,
    body: CreateTicketBody | UpdateTicketBody,
    form: Form,
    teamId: string,
    isTicketClosing?: boolean,
  ) {
    if (!form) {
      throw new BadRequestException(&quot;Form is required!&quot;);
    }

    const thenaRestrictedFields &#x3D;
      await this.thenaRestrictedFieldsService.getAllFields();

    const thenaRestrictedIdToKeyMap &#x3D;
      this.formsValidatorService.getThenaRestrictedFieldIdToTicketKeyMap(
        thenaRestrictedFields,
      );

    // Build the form field values structure from the ticket
    const formFieldValues &#x3D;
      this.formsValidatorService.buildFormFieldValuesStructureFromTicket(
        body.customFieldValues,
      );

    if (form.conditions?.length) {
      this.formsValidatorService.applyConditions(
        form,
        body,
        formFieldValues,
        thenaRestrictedIdToKeyMap,
        teamId,
      );
    }

    await Promise.all([
      this.formsValidatorService.validateThenaRestrictedFieldValues(
        form,
        thenaRestrictedFields,
        body,
      ),
      this.formsValidatorService.validateFormValues(
        orgId,
        form,
        formFieldValues,
        isTicketClosing,
      ),
    ]);
    return { form };
  }

  buildFormValidationBodyFromTicket(
    ticket: Ticket,
    body: CreateTicketBody | UpdateTicketBody,
  ) {
    const updatedTicketBody: CreateTicketBody &#x3D; {
      requestorEmail: ticket.requestorEmail,
      title: body.title || ticket.title,
      description: body.description || ticket.description,
      statusId: body.statusId || ticket.status?.uid,
      priorityId: body.priorityId || ticket.priority?.uid,
      assignedAgentId: body.assignedAgentId || ticket.assignedAgent?.uid,
      accountId: ticket.account?.uid,
      teamId: body.teamId || ticket.team?.uid,
      dueDate: body.dueDate || ticket.dueDate,
      submitterEmail: body.submitterEmail || ticket.submitterEmail,
      typeId: body.typeId || ticket.type?.uid,
      isPrivate: body.isPrivate || ticket.isPrivate,
      attachmentUrls:
        body.attachmentUrls ||
        ticket.attachments?.map((attachment) &#x3D;&gt; attachment.url),
      customFieldValues: this.buildCustomFieldValuesFromTicket(ticket, body),
      // metadata: body.metadata || ticket.metadata,
      // metadata: {
      //   sla_details: body.metadata?.sla_details,
      // },
    };
    return updatedTicketBody;
  }

  buildCustomFieldValuesFromTicket(
    ticket: Ticket,
    body: CreateTicketBody | UpdateTicketBody,
  ) {
    const customFieldValues: ExternalCustomFieldValuesDto[] &#x3D; [];
    const updatedCustomFieldIds &#x3D; [];
    for (const customField of body.customFieldValues || []) {
      customFieldValues.push({
        customFieldId: customField.customFieldId,
        data: customField.data,
      });
      updatedCustomFieldIds.push(customField.customFieldId);
    }

    for (const customField of ticket.customFieldValues || []) {
      if (!updatedCustomFieldIds.includes(customField.customField.uid)) {
        customFieldValues.push({
          customFieldId: customField.customField.uid,
          data: customField.data,
        });
      }
    }
    return customFieldValues;
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'TicketsService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
