<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >UsersService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/users/services/users.service.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#addTimeOff" >addTimeOff</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#attachSkillToUser" >attachSkillToUser</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#canManageSkills" >canManageSkills</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#create" >create</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createBotUser" >createBotUser</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createOrganizationAdmin" >createOrganizationAdmin</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createUserPersona" >createUserPersona</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createUserWithoutAuth" >createUserWithoutAuth</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#deleteTimeOff" >deleteTimeOff</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#detachSkillFromUser" >detachSkillFromUser</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findAllOrgsForUser" >findAllOrgsForUser</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findAllOrgsForUserEmail" >findAllOrgsForUserEmail</a>
                            </li>
                            <li>
                                <a href="#findAllUsersByEmailDomain" >findAllUsersByEmailDomain</a>
                            </li>
                            <li>
                                <a href="#findManyByPublicIds" >findManyByPublicIds</a>
                            </li>
                            <li>
                                <a href="#findOne" >findOne</a>
                            </li>
                            <li>
                                <a href="#findOneByEmail" >findOneByEmail</a>
                            </li>
                            <li>
                                <a href="#findOneByPublicId" >findOneByPublicId</a>
                            </li>
                            <li>
                                <a href="#findOneByUserId" >findOneByUserId</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#generateBusinessHoursUpdates" >generateBusinessHoursUpdates</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#generateUserIdentifier" >generateUserIdentifier</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#getAllTimeOffs" >getAllTimeOffs</a>
                            </li>
                            <li>
                                <a href="#getSupabaseUser" >getSupabaseUser</a>
                            </li>
                            <li>
                                <a href="#getUserByUserId" >getUserByUserId</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#getUserSkills" >getUserSkills</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#hasOverlappingTimeOffs" >hasOverlappingTimeOffs</a>
                            </li>
                            <li>
                                <a href="#isUserPartOfOrganization" >isUserPartOfOrganization</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#listUsers" >listUsers</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#setUserApiKey" >setUserApiKey</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateBusinessHours" >updateBusinessHours</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#updateTimeOff" >updateTimeOff</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateUserAvailability" >updateUserAvailability</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                    <span class="modifier">Async</span>
                                <a href="#updateUserWorkload" >updateUserWorkload</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(userRepository: UserRepository, cachedUserRepository: CachedUserRepository, authGrpcClient: <a href="../classes/AuthGrpcClient.html" target="_self">AuthGrpcClient</a>, businessHoursValidationService: <a href="../injectables/BusinessHoursValidatorService.html" target="_self">BusinessHoursValidatorService</a>, businessHoursConfigRepository: BusinessHoursConfigRepository, cachedBusinessHoursConfigRepository: CachedBusinessHoursConfigRepository, userSkillsRepository: UserSkillsRepository, timeOffRepository: TimeOffRepository, transactionService: TransactionService, cacheProvider: RedisCacheProvider, sharedService: <a href="../injectables/SharedService.html" target="_self">SharedService</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="64" class="link-to-prism">src/users/services/users.service.ts:64</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>userRepository</td>
                                                  
                                                        <td>
                                                                    <code>UserRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>cachedUserRepository</td>
                                                  
                                                        <td>
                                                                    <code>CachedUserRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>authGrpcClient</td>
                                                  
                                                        <td>
                                                                        <code><a href="../classes/AuthGrpcClient.html" target="_self" >AuthGrpcClient</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>businessHoursValidationService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/BusinessHoursValidatorService.html" target="_self" >BusinessHoursValidatorService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>businessHoursConfigRepository</td>
                                                  
                                                        <td>
                                                                    <code>BusinessHoursConfigRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>cachedBusinessHoursConfigRepository</td>
                                                  
                                                        <td>
                                                                    <code>CachedBusinessHoursConfigRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>userSkillsRepository</td>
                                                  
                                                        <td>
                                                                    <code>UserSkillsRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>timeOffRepository</td>
                                                  
                                                        <td>
                                                                    <code>TimeOffRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>transactionService</td>
                                                  
                                                        <td>
                                                                    <code>TransactionService</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>cacheProvider</td>
                                                  
                                                        <td>
                                                                    <code>RedisCacheProvider</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>sharedService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/SharedService.html" target="_self" >SharedService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="addTimeOff"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>addTimeOff</b></span>
                        <a href="#addTimeOff"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>addTimeOff(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, timeOffData: <a href="../classes/CreateTimeOffDto.html" target="_self">CreateTimeOffDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="610"
                                    class="link-to-prism">src/users/services/users.service.ts:610</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Updates a user&#39;s time off.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>timeOffData</td>
                                            <td>
                                                            <code><a href="../classes/CreateTimeOffDto.html" target="_self" >CreateTimeOffDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="attachSkillToUser"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>attachSkillToUser</b></span>
                        <a href="#attachSkillToUser"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>attachSkillToUser(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, skillId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="691"
                                    class="link-to-prism">src/users/services/users.service.ts:691</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Attaches a skill to a user.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>skillId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the skill to attach.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>userId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the user to attach the skill to.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="canManageSkills"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>canManageSkills</b></span>
                        <a href="#canManageSkills"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>canManageSkills(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="293"
                                    class="link-to-prism">src/users/services/users.service.ts:293</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Checks if the user can attach skills.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                        </div>
                            <div class="io-description">
                                <p>True if the user can attach skills, false otherwise.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="create"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>create</b></span>
                        <a href="#create"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>create(user: <a href="../classes/CreateUserDto.html" target="_self">CreateUserDto</a>, headers: <a href="../interfaces/AuthHeaders.html" target="_self">AuthHeaders</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="373"
                                    class="link-to-prism">src/users/services/users.service.ts:373</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Creates a new user.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../classes/CreateUserDto.html" target="_self" >CreateUserDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The user object to create.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>headers</td>
                                            <td>
                                                            <code><a href="../interfaces/AuthHeaders.html" target="_self" >AuthHeaders</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;User&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>A promise that resolves to the created user object.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createBotUser"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>createBotUser</b></span>
                        <a href="#createBotUser"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createBotUser(user: <a href="../classes/CreateUserDto.html" target="_self">CreateUserDto</a>, headers: <a href="../interfaces/AuthHeaders.html" target="_self">AuthHeaders</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="347"
                                    class="link-to-prism">src/users/services/users.service.ts:347</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Creates a new user.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../classes/CreateUserDto.html" target="_self" >CreateUserDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The user object to create.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>headers</td>
                                            <td>
                                                            <code><a href="../interfaces/AuthHeaders.html" target="_self" >AuthHeaders</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;User&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>A promise that resolves to the created user object.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createOrganizationAdmin"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>createOrganizationAdmin</b></span>
                        <a href="#createOrganizationAdmin"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createOrganizationAdmin(user: <a href="../classes/CreateOrganizationAdminDto.html" target="_self">CreateOrganizationAdminDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="313"
                                    class="link-to-prism">src/users/services/users.service.ts:313</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../classes/CreateOrganizationAdminDto.html" target="_self" >CreateOrganizationAdminDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;User&gt;</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createUserPersona"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>createUserPersona</b></span>
                        <a href="#createUserPersona"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createUserPersona(user: DeepPartial<User>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="419"
                                    class="link-to-prism">src/users/services/users.service.ts:419</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Creates a user persona.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                        <code>DeepPartial&lt;User&gt;</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The user to create.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The created user.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createUserWithoutAuth"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>createUserWithoutAuth</b></span>
                        <a href="#createUserWithoutAuth"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createUserWithoutAuth(user: <a href="../classes/CreateUserDto.html" target="_self">CreateUserDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="396"
                                    class="link-to-prism">src/users/services/users.service.ts:396</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Creates a new user.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../classes/CreateUserDto.html" target="_self" >CreateUserDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The user object to create.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;User&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>A promise that resolves to the created user object.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteTimeOff"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>deleteTimeOff</b></span>
                        <a href="#deleteTimeOff"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteTimeOff(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, timeOffId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="856"
                                    class="link-to-prism">src/users/services/users.service.ts:856</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Deletes a time off entry.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>timeOffId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the time off entry to delete.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="detachSkillFromUser"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>detachSkillFromUser</b></span>
                        <a href="#detachSkillFromUser"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>detachSkillFromUser(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, skillId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="750"
                                    class="link-to-prism">src/users/services/users.service.ts:750</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Detaches a skill from a user.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>skillId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the skill to detach.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>userId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the user to detach the skill from.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAllOrgsForUser"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findAllOrgsForUser</b></span>
                        <a href="#findAllOrgsForUser"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAllOrgsForUser(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="243"
                                    class="link-to-prism">src/users/services/users.service.ts:243</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds all organizations for a user.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;Organization[]&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The organizations.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAllOrgsForUserEmail"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>findAllOrgsForUserEmail</b></span>
                        <a href="#findAllOrgsForUserEmail"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAllOrgsForUserEmail(email: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, select?: FindOptionsSelect<User>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="260"
                                    class="link-to-prism">src/users/services/users.service.ts:260</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds all organizations for a user by their email.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>email</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The email of the user.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>select</td>
                                            <td>
                                                        <code>FindOptionsSelect&lt;User&gt;</code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                                    <p>The select object.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The organizations.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAllUsersByEmailDomain"></a>
                    <span class="name">
                        <span ><b>findAllUsersByEmailDomain</b></span>
                        <a href="#findAllUsersByEmailDomain"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findAllUsersByEmailDomain(domain: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="157"
                                    class="link-to-prism">src/users/services/users.service.ts:157</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds all users by their email domain.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>domain</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The email domain to find the users by.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization to find the users in.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;User[]&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The users.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findManyByPublicIds"></a>
                    <span class="name">
                        <span ><b>findManyByPublicIds</b></span>
                        <a href="#findManyByPublicIds"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findManyByPublicIds(ids: string[])</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="205"
                                    class="link-to-prism">src/users/services/users.service.ts:205</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds all users by their public IDs.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>ids</td>
                                            <td>
                                                        <code>string[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The public IDs of the users to find.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;User[]&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The users.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findOne"></a>
                    <span class="name">
                        <span ><b>findOne</b></span>
                        <a href="#findOne"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findOne(id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="97"
                                    class="link-to-prism">src/users/services/users.service.ts:97</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds a user by their ID.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the user to find.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;User | undefined&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>A promise that resolves to the user object if found, or undefined if not found.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findOneByEmail"></a>
                    <span class="name">
                        <span ><b>findOneByEmail</b></span>
                        <a href="#findOneByEmail"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findOneByEmail(email: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, orgId?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="137"
                                    class="link-to-prism">src/users/services/users.service.ts:137</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds a user by their email address.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>email</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The email address of the user to find.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;User | undefined&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>A promise that resolves to the user object if found, or undefined if not found.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findOneByPublicId"></a>
                    <span class="name">
                        <span ><b>findOneByPublicId</b></span>
                        <a href="#findOneByPublicId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findOneByPublicId(id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, orgId?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="118"
                                    class="link-to-prism">src/users/services/users.service.ts:118</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds a user by their public ID.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>id</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The public ID of the user to find.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;User | undefined&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>A promise that resolves to the user object if found, or undefined if not found.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findOneByUserId"></a>
                    <span class="name">
                        <span ><b>findOneByUserId</b></span>
                        <a href="#findOneByUserId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findOneByUserId(userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="106"
                                    class="link-to-prism">src/users/services/users.service.ts:106</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Finds a user by their user ID.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>userId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the user to find.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;User | undefined&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>A promise that resolves to the user object if found, or undefined if not found.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="generateBusinessHoursUpdates"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>generateBusinessHoursUpdates</b></span>
                        <a href="#generateBusinessHoursUpdates"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>generateBusinessHoursUpdates(businessHours: <a href="../classes/BusinessHoursConfigDto.html" target="_self">BusinessHoursConfigDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="892"
                                    class="link-to-prism">src/users/services/users.service.ts:892</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Generates the business hours updates for a user.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>businessHours</td>
                                            <td>
                                                            <code><a href="../classes/BusinessHoursConfigDto.html" target="_self" >BusinessHoursConfigDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The business hours configurations to update.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>{}</code>

                        </div>
                            <div class="io-description">
                                <p>The business hours updates.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="generateUserIdentifier"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>generateUserIdentifier</b></span>
                        <a href="#generateUserIdentifier"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>generateUserIdentifier(userType: UserType)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="956"
                                    class="link-to-prism">src/users/services/users.service.ts:956</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Generates a team identifier.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>userType</td>
                                            <td>
                                                        <code>UserType</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </div>
                            <div class="io-description">
                                <p>The generated team identifier.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getAllTimeOffs"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>getAllTimeOffs</b></span>
                        <a href="#getAllTimeOffs"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getAllTimeOffs(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, query: <a href="../classes/GetAllTimeOffsQuery.html" target="_self">GetAllTimeOffsQuery</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="585"
                                    class="link-to-prism">src/users/services/users.service.ts:585</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Gets all time offs for a user.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>query</td>
                                            <td>
                                                            <code><a href="../classes/GetAllTimeOffsQuery.html" target="_self" >GetAllTimeOffsQuery</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The query object.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>All time offs for the user.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getSupabaseUser"></a>
                    <span class="name">
                        <span ><b>getSupabaseUser</b></span>
                        <a href="#getSupabaseUser"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getSupabaseUser(token: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="309"
                                    class="link-to-prism">src/users/services/users.service.ts:309</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Gets the supabase user.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>token</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The token to use.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                <p>The supabase user.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getUserByUserId"></a>
                    <span class="name">
                        <span ><b>getUserByUserId</b></span>
                        <a href="#getUserByUserId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getUserByUserId(userId: string | Array<string>, organizationId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="278"
                                    class="link-to-prism">src/users/services/users.service.ts:278</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Gets a user by their user ID.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>userId</td>
                                            <td>
                                                        <code>string | Array&lt;string&gt;</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the user.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>organizationId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                <p>The user.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getUserSkills"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>getUserSkills</b></span>
                        <a href="#getUserSkills"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getUserSkills(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="644"
                                    class="link-to-prism">src/users/services/users.service.ts:644</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Gets all skills for a user.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>userId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the user to fetch the skills for.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>All skills for the user.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="hasOverlappingTimeOffs"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>hasOverlappingTimeOffs</b></span>
                        <a href="#hasOverlappingTimeOffs"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>hasOverlappingTimeOffs(userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, startDate: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date" target="_blank">Date</a>, endDate: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date" target="_blank">Date</a>, excludeTimeOffId?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="917"
                                    class="link-to-prism">src/users/services/users.service.ts:917</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Checks if there are overlapping time offs for a user.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>userId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the user.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>startDate</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date" target="_blank" >Date</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The start date to check for overlapping time offs.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>endDate</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date" target="_blank" >Date</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The end date to check for overlapping time offs.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>excludeTimeOffId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                                    <p>The ID of the time off entry to exclude from the check.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                <p>True if there are overlapping time offs, false otherwise.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isUserPartOfOrganization"></a>
                    <span class="name">
                        <span ><b>isUserPartOfOrganization</b></span>
                        <a href="#isUserPartOfOrganization"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>isUserPartOfOrganization(details: literal type, orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, options?: literal type)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="172"
                                    class="link-to-prism">src/users/services/users.service.ts:172</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Checks if a user is part of an organization.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>details</td>
                                            <td>
                                                        <code>literal type</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>options</td>
                                            <td>
                                                        <code>literal type</code>
                                            </td>

                                            <td>
                                                    Yes
                                            </td>


                                            <td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                <p>True if the user is part of the organization, false otherwise.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="listUsers"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>listUsers</b></span>
                        <a href="#listUsers"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>listUsers(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="228"
                                    class="link-to-prism">src/users/services/users.service.ts:228</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Lists all users for a given organization.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>The users.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="setUserApiKey"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>setUserApiKey</b></span>
                        <a href="#setUserApiKey"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>setUserApiKey(user: User, secretKey: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="211"
                                    class="link-to-prism">src/users/services/users.service.ts:211</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                        <code>User</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>secretKey</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateBusinessHours"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>updateBusinessHours</b></span>
                        <a href="#updateBusinessHours"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateBusinessHours(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, body: <a href="../classes/UpdateTimezoneWorkingHoursDto.html" target="_self">UpdateTimezoneWorkingHoursDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="442"
                                    class="link-to-prism">src/users/services/users.service.ts:442</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Updates a user&#39;s business hours.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>body</td>
                                            <td>
                                                            <code><a href="../classes/UpdateTimezoneWorkingHoursDto.html" target="_self" >UpdateTimezoneWorkingHoursDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The updates to perform on the user.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                <p>A promise that resolves to the updated user and business hours configuration.</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateTimeOff"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>updateTimeOff</b></span>
                        <a href="#updateTimeOff"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateTimeOff(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, timeOffId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, timeOffData: <a href="../classes/UpdateTimeOffDto.html" target="_self">UpdateTimeOffDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="801"
                                    class="link-to-prism">src/users/services/users.service.ts:801</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Updates a time off entry.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The current user.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>timeOffId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the time off entry to update.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>timeOffData</td>
                                            <td>
                                                            <code><a href="../classes/UpdateTimeOffDto.html" target="_self" >UpdateTimeOffDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The updated time off data.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateUserAvailability"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>updateUserAvailability</b></span>
                        <a href="#updateUserAvailability"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateUserAvailability(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, availability: UserStatus)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="427"
                                    class="link-to-prism">src/users/services/users.service.ts:427</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>availability</td>
                                            <td>
                                                        <code>UserStatus</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>unknown</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateUserWorkload"></a>
                    <span class="name">
                            <span class="modifier">Public</span>
                            <span class="modifier">Async</span>
                        <span ><b>updateUserWorkload</b></span>
                        <a href="#updateUserWorkload"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateUserWorkload(userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, orgId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, updatePayload: <a href="../classes/UpdateUserWorkloadDto.html" target="_self">UpdateUserWorkloadDto</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="549"
                                    class="link-to-prism">src/users/services/users.service.ts:549</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Updates a user&#39;s workload.</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>userId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the user.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>orgId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The ID of the organization.</p>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>updatePayload</td>
                                            <td>
                                                            <code><a href="../classes/UpdateUserWorkloadDto.html" target="_self" >UpdateUserWorkloadDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <p>The payload to update the user&#39;s workload with.</p>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  Injectable,
  NotFoundException,
} from &quot;@nestjs/common&quot;;
import { RedisCacheProvider } from &quot;@repo/nestjs-commons/cache&quot;;
import {
  BusinessHoursConfigRepository,
  CachedBusinessHoursConfigRepository,
  CachedUserRepository,
  IDX_UNIQUE_USER_SKILL_ORG_CONSTRAINT,
  Organization,
  Tag,
  TimeOff,
  TimeOffRepository,
  TransactionService,
  User,
  UserRepository,
  UserSkillsRepository,
  UserStatus,
  UserType,
} from &quot;@repo/thena-platform-entities&quot;;
import { cloneDeep } from &quot;lodash&quot;;
import {
  Between,
  DeepPartial,
  FindOptionsSelect,
  FindOptionsWhere,
  In,
  LessThanOrEqual,
  Like,
  MoreThanOrEqual,
  Not,
  QueryFailedError,
} from &quot;typeorm&quot;;
import { IdGeneratorUtils } from &quot;../../common&quot;;
import { CACHE_TTL } from &quot;../../common/constants/cache.constants&quot;;
import { POSTGRES_ERROR_CODES } from &quot;../../common/constants/postgres-errors.constants&quot;;
import { CurrentUser } from &quot;../../common/decorators&quot;;
import {
  BusinessDayDto,
  BusinessHoursConfigDto,
  UpdateTimezoneWorkingHoursDto,
} from &quot;../../common/dto&quot;;
import { BusinessHoursValidatorService } from &quot;../../common/services/business-hours-validation.service&quot;;
import { SharedService } from &quot;../../shared/shared.service&quot;;
import { SELECT_FROM_USERS } from &quot;../constants&quot;;
import {
  CreateTimeOffDto,
  GetAllTimeOffsQuery,
  UpdateTimeOffDto,
} from &quot;../dto/time-off.dto&quot;;
import {
  CreateOrganizationAdminDto,
  CreateUserDto,
  UpdateUserWorkloadDto,
} from &quot;../dto/users.dto&quot;;
import { AuthGrpcClient } from &quot;../utils&quot;;
import { AuthHeaders } from &quot;../utils/user-grpc-client.interface&quot;;

@Injectable()
export class UsersService {
  constructor(
    // User repositories
    private readonly userRepository: UserRepository,
    private readonly cachedUserRepository: CachedUserRepository,

    // gRPC client
    private readonly authGrpcClient: AuthGrpcClient,

    // Business hours configuration
    private readonly businessHoursValidationService: BusinessHoursValidatorService,
    private readonly businessHoursConfigRepository: BusinessHoursConfigRepository,
    private readonly cachedBusinessHoursConfigRepository: CachedBusinessHoursConfigRepository,

    // User skills repository
    private readonly userSkillsRepository: UserSkillsRepository,

    // Time off repository
    private readonly timeOffRepository: TimeOffRepository,

    // Injected services
    private readonly transactionService: TransactionService,
    private readonly cacheProvider: RedisCacheProvider,

    // Shared module
    private readonly sharedService: SharedService,
  ) {}

  /**
   * Finds a user by their ID.
   * @param id The ID of the user to find.
   * @returns A promise that resolves to the user object if found, or undefined if not found.
   */
  findOne(id: string): Promise&lt;User | undefined&gt; {
    return this.cachedUserRepository.findOneById(id);
  }

  /**
   * Finds a user by their user ID.
   * @param userId The ID of the user to find.
   * @returns A promise that resolves to the user object if found, or undefined if not found.
   */
  findOneByUserId(userId: string): Promise&lt;User | undefined&gt; {
    return this.userRepository.findByCondition({
      where: { uid: userId, userType: In([UserType.USER, UserType.ORG_ADMIN]) },
      relations: [&quot;organization&quot;],
    });
  }

  /**
   * Finds a user by their public ID.
   * @param id The public ID of the user to find.
   * @returns A promise that resolves to the user object if found, or undefined if not found.
   */
  findOneByPublicId(id: string, orgId?: string): Promise&lt;User | undefined&gt; {
    const whereClause: FindOptionsWhere&lt;User&gt; &#x3D; { uid: id, isActive: true };

    // If the organization ID is provided, add it to the where clause
    if (orgId) {
      whereClause.organizationId &#x3D; orgId;
    }

    return this.userRepository.findByCondition({
      where: whereClause,
      relations: [&quot;organization&quot;],
    });
  }

  /**
   * Finds a user by their email address.
   * @param email The email address of the user to find.
   * @returns A promise that resolves to the user object if found, or undefined if not found.
   */
  findOneByEmail(email: string, orgId?: string): Promise&lt;User | undefined&gt; {
    const whereClause: FindOptionsWhere&lt;User&gt; &#x3D; { email };

    // If the organization ID is provided, add it to the where clause
    if (orgId) {
      whereClause.organizationId &#x3D; orgId;
    }

    return this.userRepository.findByCondition({
      where: whereClause,
      relations: [&quot;organization&quot;],
    });
  }

  /**
   * Finds all users by their email domain.
   * @param domain The email domain to find the users by.
   * @param organizationId The ID of the organization to find the users in.
   * @returns The users.
   */
  findAllUsersByEmailDomain(
    domain: string,
    organizationId: string,
  ): Promise&lt;User[]&gt; {
    return this.cachedUserRepository.findAll({
      where: { email: Like(&#x60;%@${domain}&#x60;), organizationId, isActive: true },
    });
  }

  /**
   * Checks if a user is part of an organization.
   * @param userId The ID of the user.
   * @param orgId The ID of the organization.
   * @returns True if the user is part of the organization, false otherwise.
   */
  isUserPartOfOrganization(
    details: { userId?: string; email?: string },
    orgId: string,
    options?: { exists?: boolean },
  ) {
    const whereClause: FindOptionsWhere&lt;User&gt; &#x3D; { organizationId: orgId };

    // If the user ID is provided, add it to the where clause
    if (details.userId) {
      whereClause.id &#x3D; details.userId;
    }

    // If the email is provided, add it to the where clause
    if (details.email) {
      whereClause.email &#x3D; details.email;
    }

    if (options?.exists) {
      return this.cachedUserRepository.exists({
        where: whereClause,
      });
    }

    return this.cachedUserRepository.findByCondition({
      where: whereClause,
    });
  }

  /**
   * Finds all users by their public IDs.
   * @param ids The public IDs of the users to find.
   * @returns The users.
   */
  findManyByPublicIds(ids: string[]): Promise&lt;User[]&gt; {
    return this.userRepository.findAll({
      where: { uid: In(ids), isActive: true },
    });
  }

  async setUserApiKey(user: User, secretKey: string) {
    const updateUser &#x3D; await this.userRepository.findByCondition({
      where: { id: user.id },
      select: [&quot;id&quot;, &quot;metadata&quot;],
    });

    // Update the user&#x27;s metadata
    return this.userRepository.update(updateUser.id, {
      metadata: { ...updateUser.metadata, secretKey },
    });
  }

  /**
   * Lists all users for a given organization.
   * @param user The current user.
   * @returns The users.
   */
  async listUsers(user: CurrentUser) {
    const users &#x3D; await this.userRepository.findAll({
      where: { organizationId: user.orgId },
      select: SELECT_FROM_USERS,
    });

    // Return the users
    return users;
  }

  /**
   * Finds all organizations for a user.
   * @param user The current user.
   * @returns The organizations.
   */
  async findAllOrgsForUser(user: CurrentUser): Promise&lt;Organization[]&gt; {
    // Fetch the orgs the user is part of
    const orgs &#x3D; await this.userRepository.findAll({
      where: { id: user.sub },
      relations: { organization: true },
    });

    // Return the orgs
    return orgs.map((u) &#x3D;&gt; u.organization);
  }

  /**
   * Finds all organizations for a user by their email.
   * @param email The email of the user.
   * @param select The select object.
   * @returns The organizations.
   */
  async findAllOrgsForUserEmail(
    email: string,
    select?: FindOptionsSelect&lt;User&gt;,
  ) {
    const orgs &#x3D; await this.userRepository.findAll({
      where: { email },
      relations: { organization: true },
      select,
    });

    return orgs.map((u) &#x3D;&gt; u.organization);
  }

  /**
   * Gets a user by their user ID.
   * @param userId The ID of the user.
   * @returns The user.
   */
  getUserByUserId(userId: string | Array&lt;string&gt;, organizationId: string) {
    return this.cachedUserRepository.findAll({
      where: {
        uid: In(Array.isArray(userId) ? userId : [userId]),
        organizationId,
      },
    });
  }

  /**
   * @internal
   * Checks if the user can attach skills.
   * @param user The current user.
   * @returns True if the user can attach skills, false otherwise.
   */
  private canManageSkills(user: CurrentUser) {
    // If the user is not an organization admin, throw an error
    if (user.userType !&#x3D;&#x3D; UserType.ORG_ADMIN) {
      throw new ForbiddenException(
        &quot;You are not authorized to perform this operation&quot;,
      );
    }

    return true;
  }

  /**
   * Gets the supabase user.
   * @param token The token to use.
   * @returns The supabase user.
   */
  getSupabaseUser(token: string) {
    return this.authGrpcClient.getSupabaseUser(token);
  }

  async createOrganizationAdmin(
    user: CreateOrganizationAdminDto,
  ): Promise&lt;User&gt; {
    const rpcUser &#x3D; await this.authGrpcClient.createOrganizationAdmin({
      email: user.email,
      password: user.password,
      organizationUid: user.organizationUid,
    });

    // Fetch the created user
    const createdUser &#x3D; await this.cachedUserRepository.findByCondition({
      where: { uid: rpcUser.id, organization: { uid: user.organizationUid } },
      relations: { organization: true },
      select: {
        id: true,
        uid: true,
        name: true,
        authId: true,
        organization: { uid: true },
        email: true,
        userType: true,
        isActive: true,
        status: true,
      },
    });

    return createdUser;
  }

  /**
   * Creates a new user.
   * @param user The user object to create.
   * @returns A promise that resolves to the created user object.
   */
  async createBotUser(
    user: CreateUserDto,
    headers: AuthHeaders,
  ): Promise&lt;User&gt; {
    // Create the user
    const rpcUser &#x3D; await this.authGrpcClient.createBotUser(headers, {
      name: user.name,
      email: user.email,
      password: user.password,
      organizationUid: user.organizationUid,
      appId: user.appId,
    });

    // Fetch the created user
    const createdUser &#x3D; await this.cachedUserRepository.findByCondition({
      where: { uid: rpcUser.id },
    });

    return createdUser;
  }

  /**
   * Creates a new user.
   * @param user The user object to create.
   * @returns A promise that resolves to the created user object.
   */
  async create(user: CreateUserDto, headers: AuthHeaders): Promise&lt;User&gt; {
    // Create the user
    const rpcUser &#x3D; await this.authGrpcClient.createUser(headers, {
      name: user.name,
      email: user.email,
      password: user.password,
      organizationUid: user.organizationUid,
      isCustomer: user.userType &#x3D;&#x3D;&#x3D; UserType.CUSTOMER_USER,
    });

    // Fetch the created user
    const createdUser &#x3D; await this.cachedUserRepository.findByCondition({
      where: { uid: rpcUser.id },
    });

    return createdUser;
  }

  /**
   * Creates a new user.
   * @param user The user object to create.
   * @returns A promise that resolves to the created user object.
   */
  async createUserWithoutAuth(user: CreateUserDto): Promise&lt;User&gt; {
    // Create the user
    const rpcUser &#x3D; await this.authGrpcClient.createUserWithoutAuth({
      name: user.name,
      email: user.email,
      password: user.password,
      organizationUid: user.organizationUid,
      isCustomer: user.userType &#x3D;&#x3D;&#x3D; UserType.CUSTOMER_USER,
    });

    // Fetch the created user
    const createdUser &#x3D; await this.cachedUserRepository.findByCondition({
      where: { uid: rpcUser.id },
    });

    return createdUser;
  }

  /**
   * Creates a user persona.
   * @param user The user to create.
   * @returns The created user.
   */
  async createUserPersona(user: DeepPartial&lt;User&gt;) {
    const uid &#x3D; this.generateUserIdentifier(user.userType);

    const createdUser &#x3D; this.userRepository.create({ ...user, uid });
    const savedUser &#x3D; await this.userRepository.save(createdUser);
    return savedUser;
  }

  async updateUserAvailability(user: CurrentUser, availability: UserStatus) {
    await this.userRepository.update(
      { id: user.sub },
      { status: availability },
    );

    return { ok: true };
  }

  /**
   * Updates a user&#x27;s business hours.
   * @param user The current user.
   * @param body The updates to perform on the user.
   * @returns A promise that resolves to the updated user and business hours configuration.
   */
  async updateBusinessHours(
    user: CurrentUser,
    body: UpdateTimezoneWorkingHoursDto,
  ) {
    const { timezone, dailyConfig } &#x3D; body;

    // If no updates to perform on the user, throw an error
    if (!timezone &amp;&amp; !dailyConfig) {
      throw new BadRequestException(
        &quot;Updates to perform on the user were not provided!&quot;,
      );
    }

    // Find the user&#x27;s business hours configuration
    const currentUser &#x3D; await this.cachedUserRepository.findOneById(user.sub);
    if (!currentUser) {
      throw new NotFoundException(&quot;User not found!&quot;);
    }

    // If the business hours are being updated, validate them
    if (dailyConfig) {
      const tz &#x3D; timezone || currentUser.timezone || &quot;UTC&quot;;
      const validationResult &#x3D;
        this.businessHoursValidationService.validateBusinessHours(
          dailyConfig,
          tz,
        );

      // If the business hours are invalid, throw an error
      if (!validationResult.isValid) {
        throw new BadRequestException(validationResult.error);
      }
    }

    // Check if the user already has a business hours configuration
    const existingBusinessHoursConfig &#x3D;
      await this.cachedBusinessHoursConfigRepository.findByCondition({
        where: { user: { id: user.sub } },
      });

    // Update the user&#x27;s timezone and business hours in a transaction
    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      // Update the user&#x27;s timezone
      if (timezone) {
        await this.userRepository.updateWithTxn(
          txnContext,
          { id: user.sub },
          { timezone },
        );
      }

      // Update the user&#x27;s business hours
      if (dailyConfig) {
        if (!existingBusinessHoursConfig) {
          // Create a new business hours configuration
          const newBusinessHoursConfig &#x3D;
            await this.businessHoursConfigRepository.saveWithTxn(txnContext, {
              ...dailyConfig,
              user: currentUser,
              organizationId: user.orgId,
            });

          // Update the user&#x27;s business hours configuration
          await this.userRepository.updateWithTxn(
            txnContext,
            { id: user.sub },
            { businessHoursConfig: { id: newBusinessHoursConfig.id } },
          );
        } else {
          // Update the existing business hours configuration
          await this.businessHoursConfigRepository.updateWithTxn(
            txnContext,
            {
              user: { id: user.sub },
            },
            dailyConfig,
          );
        }

        // Invalidate the user&#x27;s business hours cache
        await this.cachedBusinessHoursConfigRepository.invalidateBusinessHoursCache(
          { userId: user.sub },
        );
      }
    });

    // Find the updated user and business hours configuration
    const [updatedUser, businessHoursConfig] &#x3D; await Promise.all([
      this.findOneByUserId(currentUser.uid),
      this.cachedBusinessHoursConfigRepository.findByCondition({
        where: { user: { id: user.sub } },
      }),
    ]);

    return {
      userId: currentUser.uid,
      user: updatedUser,
      businessHoursConfig,
    };
  }

  /**
   * Updates a user&#x27;s workload.
   * @param userId The ID of the user.
   * @param orgId The ID of the organization.
   * @param updatePayload The payload to update the user&#x27;s workload with.
   */
  public async updateUserWorkload(
    userId: string,
    orgId: string,
    updatePayload: UpdateUserWorkloadDto,
  ) {
    // Fetch the user
    const user &#x3D; await this.cachedUserRepository.findByCondition({
      where: { id: userId, organizationId: orgId },
    });

    // If the user is not found, throw an error
    if (!user) {
      throw new NotFoundException(&quot;User not found!&quot;);
    }

    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      const updatedPayload &#x3D; cloneDeep(user.metadata?.workload || {});
      updatedPayload[updatePayload.forTeamId] &#x3D; {
        count: updatePayload.newTotalTickets,
      };

      // Update the user&#x27;s workload
      await this.userRepository.updateWithTxn(
        txnContext,
        { id: userId },
        { metadata: { workload: updatedPayload } },
      );
    });
  }

  /**
   * Gets all time offs for a user.
   * @param user The current user.
   * @param query The query object.
   * @returns All time offs for the user.
   */
  public async getAllTimeOffs(user: CurrentUser, query: GetAllTimeOffsQuery) {
    const limit &#x3D; Math.min(Math.max(query.limit || 50, 1), 100);

    // Fetch the time offs
    const timeOffs &#x3D; await this.timeOffRepository.fetchPaginatedResults(
      {
        page: query.page || 0,
        limit,
      },
      {
        where: {
          user: { id: user.sub },
        },
        order: { createdAt: &quot;DESC&quot; },
      },
    );

    return timeOffs;
  }

  /**
   * Updates a user&#x27;s time off.
   * @param user The current user.
   * @param payload The payload to update the user&#x27;s time off with.
   */
  public async addTimeOff(user: CurrentUser, timeOffData: CreateTimeOffDto) {
    // First check for any overlapping time off entries
    const existingTimeOff &#x3D; await this.hasOverlappingTimeOffs(
      user.sub,
      new Date(timeOffData.startDate),
      new Date(timeOffData.endDate),
    );

    // If there is already an overlapping time off entry, throw an error
    if (existingTimeOff) {
      throw new BadRequestException(
        &quot;There is already a time off entry that overlaps with the requested dates&quot;,
      );
    }

    // Create the time off entry
    const timeOff &#x3D; await this.timeOffRepository.createTimeOff({
      user: { id: user.sub },
      startDate: new Date(timeOffData.startDate),
      endDate: new Date(timeOffData.endDate),
      description: timeOffData.description,
      type: timeOffData.type,
      organization: { id: user.orgId },
    });

    return timeOff;
  }

  /**
   * Gets all skills for a user.
   * @param user The current user.
   * @param userId The ID of the user to fetch the skills for.
   * @returns All skills for the user.
   */
  public async getUserSkills(user: CurrentUser, userId: string) {
    // Fetch user
    const requestedUser &#x3D; await this.cachedUserRepository.exists({
      where: { uid: userId, organizationId: user.orgId },
    });

    // If the user does not exist, throw an error
    if (!requestedUser) {
      throw new NotFoundException(&quot;User not found!&quot;);
    }

    const cacheKey &#x3D; &#x60;user-skills:${userId}:${user.orgId}&#x60;;
    const cachedUserSkills &#x3D; await this.cacheProvider.get&lt;string&gt;(cacheKey);

    // If the user skills are cached, return them
    if (cachedUserSkills) {
      return JSON.parse(cachedUserSkills) as Tag[];
    }

    // Fetch the user skills
    const junctionRecords &#x3D; await this.userSkillsRepository.findAll({
      where: {
        user: { uid: userId },
        organization: { id: user.orgId },
      },
      relations: [&quot;skill&quot;],
    });

    // Map the junction records to the skill IDs
    const userSkills &#x3D; junctionRecords.map((record) &#x3D;&gt; record.skill);

    // Cache the user skills
    await this.cacheProvider.set(
      cacheKey,
      JSON.stringify(userSkills),
      CACHE_TTL.MONTH * 2,
    );

    return userSkills;
  }

  /**
   * Attaches a skill to a user.
   * @param user The current user.
   * @param skillId The ID of the skill to attach.
   * @param userId The ID of the user to attach the skill to.
   */
  public async attachSkillToUser(
    user: CurrentUser,
    skillId: string,
    userId: string,
  ) {
    // Check if the user can manage skills
    this.canManageSkills(user);

    // Fetch user
    const requestedUser &#x3D; await this.cachedUserRepository.findByCondition({
      where: { uid: userId, organizationId: user.orgId },
    });

    // If the user does not exist, throw an error
    if (!requestedUser) {
      throw new NotFoundException(&quot;User not found!&quot;);
    }

    // Find the skill or throw an error if it&#x27;s not found
    const skill &#x3D; await this.sharedService.findOneTag(skillId, user);
    if (!skill) {
      throw new NotFoundException(&quot;Provided skill was not found!&quot;);
    }

    // Try and attach the skill to the user in a transaction
    try {
      await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
        // Attach the skill to the user
        await this.userSkillsRepository.saveWithTxn(txnContext, {
          skill: { id: skill.id },
          user: { id: requestedUser.id },
          organization: { id: user.orgId },
        });

        // Delete the cached user skills
        await this.cacheProvider.del(&#x60;user-skills:${userId}:${user.orgId}&#x60;);
      });
    } catch (error) {
      // Handle errors thrown by the database
      if (error instanceof QueryFailedError) {
        if (error.code &#x3D;&#x3D;&#x3D; POSTGRES_ERROR_CODES.DUPLICATE_KEY_VALUE) {
          const constraint &#x3D; (error as any)?.constraint;
          // If the error is due to duplicate skill assignment for the user, throw an error
          if (constraint &#x3D;&#x3D;&#x3D; IDX_UNIQUE_USER_SKILL_ORG_CONSTRAINT) {
            throw new ConflictException(&quot;Skill already attached to user!&quot;);
          }
        }
      }

      throw error;
    }
  }

  /**
   * Detaches a skill from a user.
   * @param user The current user.
   * @param skillId The ID of the skill to detach.
   * @param userId The ID of the user to detach the skill from.
   */
  public async detachSkillFromUser(
    user: CurrentUser,
    skillId: string,
    userId: string,
  ) {
    // Check if the user can manage skills
    this.canManageSkills(user);

    // Fetch user
    const requestedUser &#x3D; await this.cachedUserRepository.exists({
      where: { uid: userId, organizationId: user.orgId },
    });

    // If the user does not exist, throw an error
    if (!requestedUser) {
      throw new NotFoundException(&quot;User not found!&quot;);
    }

    // Find the user skills junction record
    const userSkillsJunction &#x3D; await this.userSkillsRepository.findByCondition({
      where: {
        skill: { uid: skillId },
        user: { uid: userId },
        organization: { id: user.orgId },
      },
    });

    // If the user skills junction record does not exist, throw an error
    if (!userSkillsJunction) {
      throw new NotFoundException(&quot;Provided skill not found on the user!&quot;);
    }

    // Remove the user skills junction record in a transaction
    await this.transactionService.runInTransaction(async (txnContext) &#x3D;&gt; {
      // Delete the user skills junction record
      await this.userSkillsRepository.removeWithTxn(
        txnContext,
        userSkillsJunction,
      );

      // Delete the cached user skills
      await this.cacheProvider.del(&#x60;user-skills:${userId}:${user.orgId}&#x60;);
    });
  }

  /**
   * Updates a time off entry.
   * @param user The current user.
   * @param timeOffId The ID of the time off entry to update.
   * @param timeOffData The updated time off data.
   */
  public async updateTimeOff(
    user: CurrentUser,
    timeOffId: string,
    timeOffData: UpdateTimeOffDto,
  ) {
    // Find the time off entry
    const existingTimeOff &#x3D; await this.timeOffRepository.exists({
      where: {
        id: timeOffId,
        user: { id: user.sub },
        organization: { id: user.orgId },
        deletedAt: null,
      },
    });

    // If the time off entry is not found, throw an error
    if (!existingTimeOff) {
      throw new NotFoundException(&quot;Time off entry not found&quot;);
    }

    // Check for overlapping time offs, excluding the current one
    const overlappingTimeOff &#x3D; await this.hasOverlappingTimeOffs(
      user.sub,
      new Date(timeOffData.startDate),
      new Date(timeOffData.endDate),
      timeOffId,
    );

    // If there is an overlapping time off entry, throw an error
    if (overlappingTimeOff) {
      throw new BadRequestException(
        &quot;There is already a time off entry that overlaps with the requested dates&quot;,
      );
    }

    // Update the time off entry
    await this.timeOffRepository.update(
      { id: timeOffId },
      {
        startDate: new Date(timeOffData.startDate),
        endDate: new Date(timeOffData.endDate),
        description: timeOffData.description,
        type: timeOffData.type,
      },
    );

    const updatedTimeOff &#x3D; await this.timeOffRepository.findOneById(timeOffId);
    return updatedTimeOff;
  }

  /**
   * Deletes a time off entry.
   * @param user The current user.
   * @param timeOffId The ID of the time off entry to delete.
   */
  public async deleteTimeOff(user: CurrentUser, timeOffId: string) {
    // Find the time off entry with startDate
    const timeOff &#x3D; await this.timeOffRepository.findByCondition({
      where: {
        id: timeOffId,
        user: { id: user.sub },
        organization: { id: user.orgId },
        deletedAt: null,
      },
      select: [&quot;id&quot;, &quot;startDate&quot;],
    });

    // If the time off entry is not found, throw an error
    if (!timeOff) {
      throw new NotFoundException(&quot;Time off entry not found&quot;);
    }

    // Check if the time off has already started
    if (new Date(timeOff.startDate).getTime() &lt;&#x3D; Date.now()) {
      throw new BadRequestException(
        &quot;Cannot delete a time off entry that has already started&quot;,
      );
    }

    // Soft delete the time off entry
    await this.timeOffRepository.softDelete({
      id: timeOffId,
      organizationId: user.orgId,
    });
  }

  /**
   * Generates the business hours updates for a user.
   * @param businessHours The business hours configurations to update.
   * @returns The business hours updates.
   */
  private generateBusinessHoursUpdates(businessHours: BusinessHoursConfigDto) {
    const updateClause &#x3D; {};

    for (const day in businessHours) {
      const dayProperty: BusinessDayDto &#x3D; businessHours[day];

      // If the day is active, update the business hours
      if (dayProperty.isActive) {
        updateClause[day] &#x3D; dayProperty.slots;
      } else {
        updateClause[day] &#x3D; null;
      }
    }

    return updateClause;
  }

  /**
   * Checks if there are overlapping time offs for a user.
   * @param userId The ID of the user.
   * @param startDate The start date to check for overlapping time offs.
   * @param endDate The end date to check for overlapping time offs.
   * @param excludeTimeOffId The ID of the time off entry to exclude from the check.
   * @returns True if there are overlapping time offs, false otherwise.
   */
  private hasOverlappingTimeOffs(
    userId: string,
    startDate: Date,
    endDate: Date,
    excludeTimeOffId?: string,
  ) {
    const whereConditions: FindOptionsWhere&lt;TimeOff&gt;[] &#x3D; [
      {
        user: { id: userId },
        startDate: Between(new Date(startDate), new Date(endDate)),
        deletedAt: null,
      },
      {
        user: { id: userId },
        endDate: Between(new Date(startDate), new Date(endDate)),
        deletedAt: null,
      },
      {
        user: { id: userId },
        startDate: LessThanOrEqual(new Date(startDate)),
        endDate: MoreThanOrEqual(new Date(endDate)),
        deletedAt: null,
      },
    ];

    // If an excludeTimeOffId is provided, exclude it from the where conditions
    if (excludeTimeOffId) {
      whereConditions.forEach((condition) &#x3D;&gt; {
        condition.id &#x3D; Not(excludeTimeOffId);
      });
    }

    return this.timeOffRepository.exists({ where: whereConditions });
  }

  /**
   * Generates a team identifier.
   * @returns The generated team identifier.
   */
  private generateUserIdentifier(userType: UserType): string {
    switch (userType) {
      case UserType.USER:
      case UserType.ORG_ADMIN: {
        return IdGeneratorUtils.generate(&quot;U&quot;);
      }

      case UserType.CUSTOMER_USER: {
        return IdGeneratorUtils.generate(&quot;C&quot;);
      }

      case UserType.APP_USER: {
        return IdGeneratorUtils.generate(&quot;A&quot;);
      }

      case UserType.BOT_USER: {
        return IdGeneratorUtils.generate(&quot;O&quot;);
      }

      default: {
        throw new Error(&quot;Invalid user type&quot;);
      }
    }
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'UsersService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
