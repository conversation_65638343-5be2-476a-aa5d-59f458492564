<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >TicketDraftService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/tickets/services/ticket-draft.action.service.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createDraftTicket" >createDraftTicket</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#deleteDraftTicket" >deleteDraftTicket</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getDraftTicketByUid" >getDraftTicketByUid</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getDraftTickets" >getDraftTickets</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#publishDraftTicket" >publishDraftTicket</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateDraftTicket" >updateDraftTicket</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(logger: ILogger, draftRepository: DraftRepository, ticketValidationService: <a href="../injectables/TicketValidationService.html" target="_self">TicketValidationService</a>, transactionService: TransactionService, activitiesService: <a href="../injectables/ActivitiesService.html" target="_self">ActivitiesService</a>, usersService: <a href="../injectables/UsersService.html" target="_self">UsersService</a>, ticketService: <a href="../injectables/TicketsService.html" target="_self">TicketsService</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="38" class="link-to-prism">src/tickets/services/ticket-draft.action.service.ts:38</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>logger</td>
                                                  
                                                        <td>
                                                                    <code>ILogger</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>draftRepository</td>
                                                  
                                                        <td>
                                                                    <code>DraftRepository</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>ticketValidationService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/TicketValidationService.html" target="_self" >TicketValidationService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>transactionService</td>
                                                  
                                                        <td>
                                                                    <code>TransactionService</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>activitiesService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/ActivitiesService.html" target="_self" >ActivitiesService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>usersService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/UsersService.html" target="_self" >UsersService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>ticketService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/TicketsService.html" target="_self" >TicketsService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createDraftTicket"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>createDraftTicket</b></span>
                        <a href="#createDraftTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createDraftTicket(draftTicketData: <a href="../classes/CreateDraftTicketDto.html" target="_self">CreateDraftTicketDto</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, team: Team, draftScope: DraftScope)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="94"
                                    class="link-to-prism">src/tickets/services/ticket-draft.action.service.ts:94</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Creates a new draft ticket</p>
<p>Creation Rules:</p>
<ul>
<li>Initially created with IN_PROGRESS status</li>
<li>Content is validated before creation</li>
<li>Activity is recorded after creation</li>
<li>Draft scope (personal/team) is set during creation</li>
</ul>
<p>Content Requirements:</p>
<ul>
<li>Must contain all required ticket fields</li>
<li>Fields are validated using ticketValidationService</li>
<li>Metadata is optional</li>
</ul>
<p>Access Control:</p>
<ul>
<li><p>Users can create drafts in their own organization</p>
</li>
<li><p>Draft is automatically assigned to creator</p>
</li>
<li><p>Organization context is preserved</p>
<ul>
<li><p>title: Ticket title</p>
</li>
<li><p>description?: Ticket description</p>
</li>
<li><p>requestorEmail: Email of the requestor</p>
</li>
<li><p>statusId?: Ticket status ID</p>
</li>
<li><p>priorityId?: Ticket priority ID</p>
</li>
<li><p>typeId?: Ticket type ID</p>
</li>
<li><p>assignedAgentId?: Assigned agent ID</p>
</li>
<li><p>metadata?: Additional metadata</p>
</li>
<li><p>user.sub: User ID</p>
</li>
<li><p>user.orgId: Organization ID</p>
</li>
<li><p>team.id: Team ID</p>
</li>
<li><p>Validation fails</p>
</li>
<li><p>Required fields are missing</p>
</li>
<li><p>User doesn&#39;t belong to the team</p>
</li>
<li><p>Organization mismatch</p>
</li>
</ul>
</li>
</ul>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>draftTicketData</td>
                                            <td>
                                                            <code><a href="../classes/CreateDraftTicketDto.html" target="_self" >CreateDraftTicketDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>Data for the draft ticket</li>
<li>title: Ticket title</li>
<li>description?: Ticket description</li>
<li>requestorEmail: Email of the requestor</li>
<li>statusId?: Ticket status ID</li>
<li>priorityId?: Ticket priority ID</li>
<li>typeId?: Ticket type ID</li>
<li>assignedAgentId?: Assigned agent ID</li>
<li>metadata?: Additional metadata</li>
</ul>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>Current user creating the draft</li>
<li>user.sub: User ID</li>
<li>user.orgId: Organization ID</li>
</ul>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>team</td>
                                            <td>
                                                        <code>Team</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>Team context for the draft</li>
<li>team.id: Team ID</li>
</ul>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>draftScope</td>
                                            <td>
                                                        <code>DraftScope</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>Scope of the draft (PERSONAL/TEAM)</li>
</ul>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;DraftTicketResponseDto&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>Created draft ticket</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteDraftTicket"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>deleteDraftTicket</b></span>
                        <a href="#deleteDraftTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteDraftTicket(uid: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="482"
                                    class="link-to-prism">src/tickets/services/ticket-draft.action.service.ts:482</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Deletes (soft-delete) a draft ticket with scope-based access control</p>
<p>Access Control Rules:</p>
<ul>
<li>Admins can delete:<ul>
<li>Any team-scoped drafts</li>
<li>Their own personal drafts</li>
</ul>
</li>
<li>Regular users can only delete:<ul>
<li>Their own drafts (both personal and team-scoped)</li>
</ul>
</li>
</ul>
<p>Deletion Rules:</p>
<ul>
<li><p>Soft deletes the draft (sets deletedAt)</p>
</li>
<li><p>Changes status to DISCARDED</p>
</li>
<li><p>Cannot delete already published drafts</p>
</li>
<li><p>Cannot delete already discarded drafts</p>
<ul>
<li><p>user.sub: User ID</p>
</li>
<li><p>user.userType: Type of user (admin/regular)</p>
</li>
<li><p>user.orgId: Organization ID</p>
</li>
<li><p>Draft is already published</p>
</li>
<li><p>Draft is already discarded</p>
</li>
</ul>
</li>
</ul>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>uid</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>Unique identifier of the draft ticket to delete</li>
</ul>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>Current user attempting the deletion</li>
<li>user.sub: User ID</li>
<li>user.userType: Type of user (admin/regular)</li>
<li>user.orgId: Organization ID</li>
</ul>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;Draft&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The deleted (discarded) draft</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getDraftTicketByUid"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>getDraftTicketByUid</b></span>
                        <a href="#getDraftTicketByUid"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getDraftTicketByUid(uid: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="284"
                                    class="link-to-prism">src/tickets/services/ticket-draft.action.service.ts:284</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Retrieves a specific draft ticket with comprehensive access control</p>
<p>Access Control Rules:</p>
<ul>
<li>Admins can access:<ul>
<li>Any team-scoped drafts</li>
<li>Their own personal drafts</li>
</ul>
</li>
<li>Regular users can only access:<ul>
<li><p>Their own drafts</p>
</li>
<li><p>user.sub: User ID</p>
</li>
<li><p>user.userType: Type of user (admin/regular)</p>
</li>
<li><p>user.orgId: Organization ID</p>
</li>
</ul>
</li>
</ul>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>uid</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>Unique identifier of the draft ticket</li>
</ul>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>Current user making the request</li>
<li>user.sub: User ID</li>
<li>user.userType: Type of user (admin/regular)</li>
<li>user.orgId: Organization ID</li>
</ul>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;DraftTicketResponseDto&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>Formatted draft ticket response</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getDraftTickets"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>getDraftTickets</b></span>
                        <a href="#getDraftTickets"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getDraftTickets(user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>, query: <a href="../classes/GetDraftTicketQuery.html" target="_self">GetDraftTicketQuery</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="184"
                                    class="link-to-prism">src/tickets/services/ticket-draft.action.service.ts:184</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Retrieves a paginated list of draft tickets based on user permissions</p>
<p>Access Control Rules:</p>
<ul>
<li>Admins can view:<ul>
<li>All team-scoped drafts</li>
<li>Their own personal drafts</li>
<li>Cannot view other users&#39; personal drafts</li>
</ul>
</li>
<li>Regular users can view:<ul>
<li>Their own personal drafts</li>
<li>Their own team-scoped drafts</li>
</ul>
</li>
</ul>
<p>Filtering Rules:</p>
<ul>
<li><p>Excludes published and discarded drafts</p>
</li>
<li><p>Orders by last updated date (newest first)</p>
</li>
<li><p>Supports pagination</p>
<ul>
<li><p>user.sub: User ID</p>
</li>
<li><p>user.orgId: Organization ID</p>
</li>
<li><p>user.userType: Type of user (admin/regular)</p>
</li>
<li><p>page: Page number (0-based)</p>
</li>
<li><p>limit: Number of items per page</p>
</li>
<li><p>User not found</p>
</li>
<li><p>User tries to access unauthorized drafts</p>
</li>
<li><p>items: Array of formatted draft tickets</p>
</li>
<li><p>meta: Pagination metadata</p>
<ul>
<li>totalItems: Total number of items</li>
<li>currentPage: Current page number</li>
<li>itemsPerPage: Items per page</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>Current user requesting drafts</li>
<li>user.sub: User ID</li>
<li>user.orgId: Organization ID</li>
<li>user.userType: Type of user (admin/regular)</li>
</ul>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>query</td>
                                            <td>
                                                            <code><a href="../classes/GetDraftTicketQuery.html" target="_self" >GetDraftTicketQuery</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>Query parameters</li>
<li>page: Page number (0-based)</li>
<li>limit: Number of items per page</li>
</ul>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;PaginatedResponseDto&lt;DraftTicketResponseDto&gt;&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>Paginated list of drafts</p>
<ul>
<li>items: Array of formatted draft tickets</li>
<li>meta: Pagination metadata</li>
<li>totalItems: Total number of items</li>
<li>currentPage: Current page number</li>
<li>itemsPerPage: Items per page</li>
</ul>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="publishDraftTicket"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>publishDraftTicket</b></span>
                        <a href="#publishDraftTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>publishDraftTicket(uid: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="568"
                                    class="link-to-prism">src/tickets/services/ticket-draft.action.service.ts:568</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Publishes a draft ticket by creating an actual ticket</p>
<p>Access Control Rules:</p>
<ul>
<li>Only the creator can publish their own drafts</li>
<li>Admins can publish team-scoped drafts</li>
<li>No one can publish others&#39; personal drafts</li>
</ul>
<p>Publishing Rules:</p>
<ul>
<li><p>Draft must be in READY_TO_PUBLISH status</p>
</li>
<li><p>Cannot publish already published drafts</p>
</li>
<li><p>Cannot publish discarded drafts</p>
</li>
<li><p>All required ticket fields must be valid</p>
</li>
<li><p>Creates a new ticket and marks draft as published</p>
<ul>
<li><p>user.sub: User ID</p>
</li>
<li><p>user.userType: Type of user (admin/regular)</p>
</li>
<li><p>user.orgId: Organization ID</p>
</li>
<li><p>User doesn&#39;t have publish permission</p>
</li>
<li><p>Trying to publish others&#39; personal drafts</p>
</li>
<li><p>Draft is not in READY_TO_PUBLISH status</p>
</li>
<li><p>Draft is already published</p>
</li>
<li><p>Draft is incomplete</p>
</li>
<li><p>Validation fails</p>
</li>
</ul>
</li>
</ul>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>uid</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>Unique identifier of the draft ticket</li>
</ul>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>Current user attempting to publish</li>
<li>user.sub: User ID</li>
<li>user.userType: Type of user (admin/regular)</li>
<li>user.orgId: Organization ID</li>
</ul>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;DraftTicketResponseDto&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>The published draft</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateDraftTicket"></a>
                    <span class="name">
                            <span class="modifier">Async</span>
                        <span ><b>updateDraftTicket</b></span>
                        <a href="#updateDraftTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateDraftTicket(uid: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, draftTicketData: <a href="../classes/UpdateDraftTicketDto.html" target="_self">UpdateDraftTicketDto</a>, user: <a href="../s/CurrentUser.html" target="_self">CurrentUser</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="364"
                                    class="link-to-prism">src/tickets/services/ticket-draft.action.service.ts:364</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>Updates a draft ticket with comprehensive access control and validation</p>
<p>Access Control Rules:</p>
<ul>
<li>Admins can update:<ul>
<li>Any team-scoped drafts</li>
<li>Their own personal drafts</li>
</ul>
</li>
<li>Regular users can only update:<ul>
<li>Their own drafts (both personal and team-scoped)</li>
</ul>
</li>
</ul>
<p>Update Rules:</p>
<ul>
<li><p>Original content is preserved before update</p>
</li>
<li><p>Cannot update published drafts</p>
</li>
<li><p>Cannot update discarded drafts</p>
</li>
<li><p>Status changes are validated</p>
</li>
<li><p>Team/organization context is preserved</p>
<ul>
<li><p>user.sub: User ID</p>
</li>
<li><p>user.userType: Type of user (admin/regular)</p>
</li>
<li><p>user.orgId: Organization ID</p>
</li>
<li><p>Draft is published/discarded</p>
</li>
<li><p>Invalid status transition</p>
</li>
<li><p>Required fields are missing</p>
</li>
<li><p>Invalid data format</p>
</li>
</ul>
</li>
</ul>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                                <td>Description</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>uid</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>Unique identifier of the draft ticket</li>
</ul>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>draftTicketData</td>
                                            <td>
                                                            <code><a href="../classes/UpdateDraftTicketDto.html" target="_self" >UpdateDraftTicketDto</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>Data to update the draft with</li>
</ul>

                                            </td>
                                        </tr>
                                        <tr>
                                                <td>user</td>
                                            <td>
                                                            <code><a href="../miscellaneous/variables.html#CurrentUser" target="_self" >CurrentUser</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                            <td>
                                                    <ul>
<li>Current user making the request</li>
<li>user.sub: User ID</li>
<li>user.userType: Type of user (admin/regular)</li>
<li>user.orgId: Organization ID</li>
</ul>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Promise&lt;DraftTicketResponseDto&gt;</code>

                        </div>
                            <div class="io-description">
                                <p>Updated draft ticket</p>

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException,
} from &quot;@nestjs/common&quot;;
import { ILogger } from &quot;@repo/nestjs-commons/logger&quot;;
import { RequestSource } from &quot;@repo/nestjs-commons/middlewares&quot;;
import {
  AuditLogEntityType,
  AuditLogOp,
  AuditLogVisibility,
  Draft,
  DraftRepository,
  DraftScope,
  DraftStatus,
  DraftType,
  Team,
  TransactionService,
  UserType,
} from &quot;@repo/thena-platform-entities&quot;;
import { FindManyOptions, FindOptionsWhere, In, IsNull, Not } from &quot;typeorm&quot;;
import { ActivitiesService } from &quot;../../activities/services/activities.service&quot;;
import { CurrentUser } from &quot;../../common/decorators/user.decorator&quot;;
import { UsersService } from &quot;../../users/services/users.service&quot;;
import { GetDraftTicketQuery } from &quot;../dto/queries.dto&quot;;
import {
  CreateDraftTicketDto,
  DraftTicketResponseDto,
  PaginatedResponseDto,
  UpdateDraftTicketDto,
} from &quot;../interfaces/draft.ticket.interface&quot;;
import { TicketValidationService } from &quot;./ticket-validation.service&quot;;
import { TicketsService } from &quot;./tickets.service&quot;;

@Injectable()
export class TicketDraftService {
  constructor(
    @Inject(&quot;CustomLogger&quot;)
    private readonly logger: ILogger,
    private readonly draftRepository: DraftRepository,
    private readonly ticketValidationService: TicketValidationService,
    private readonly transactionService: TransactionService,
    private readonly activitiesService: ActivitiesService,
    private readonly usersService: UsersService,
    private readonly ticketService: TicketsService,
  ) {}

  /**
   * Creates a new draft ticket
   *
   * Creation Rules:
   * - Initially created with IN_PROGRESS status
   * - Content is validated before creation
   * - Activity is recorded after creation
   * - Draft scope (personal/team) is set during creation
   *
   * Content Requirements:
   * - Must contain all required ticket fields
   * - Fields are validated using ticketValidationService
   * - Metadata is optional
   *
   * Access Control:
   * - Users can create drafts in their own organization
   * - Draft is automatically assigned to creator
   * - Organization context is preserved
   *
   * @param draftTicketData - Data for the draft ticket
   *   - title: Ticket title
   *   - description?: Ticket description
   *   - requestorEmail: Email of the requestor
   *   - statusId?: Ticket status ID
   *   - priorityId?: Ticket priority ID
   *   - typeId?: Ticket type ID
   *   - assignedAgentId?: Assigned agent ID
   *   - metadata?: Additional metadata
   * @param user - Current user creating the draft
   *   - user.sub: User ID
   *   - user.orgId: Organization ID
   * @param team - Team context for the draft
   *   - team.id: Team ID
   * @param draftScope - Scope of the draft (PERSONAL/TEAM)
   *
   * @throws {BadRequestException} When:
   *   - Validation fails
   *   - Required fields are missing
   * @throws {ForbiddenException} When:
   *   - User doesn&#x27;t belong to the team
   *   - Organization mismatch
   *
   * @returns {Promise&lt;DraftTicketResponseDto&gt;} Created draft ticket
   */
  async createDraftTicket(
    draftTicketData: CreateDraftTicketDto,
    user: CurrentUser,
    team: Team,
    draftScope: DraftScope,
  ): Promise&lt;DraftTicketResponseDto&gt; {
    return await this.transactionService.runInTransaction(
      async (txnContext) &#x3D;&gt; {
        const validatedDraft: CreateDraftTicketDto &#x3D;
          await this.ticketValidationService.validateTicketFields(
            draftTicketData,
            user,
            team,
          );

        const draft &#x3D; this.draftRepository.create({
          entityType: DraftType.TICKET,
          draftScope,
          status: DraftStatus.IN_PROGRESS,
          content: validatedDraft,
          metadata: draftTicketData.metadata || {},
          organizationId: user.orgId,
          createdBy: user.sub,
        });

        const savedDraft &#x3D; await this.draftRepository.saveWithTxn(
          txnContext,
          draft,
        );

        await this.activitiesService.recordAuditLog({
          team: { id: team.id },
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityType: AuditLogEntityType.DRAFTS,
          entityId: savedDraft.id,
          entityUid: savedDraft.uid,
          op: AuditLogOp.CREATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: &#x60;A draft was created with id: ${savedDraft.id}&#x60;,
          description: &#x60;A draft of type ${savedDraft.entityType} with scope ${savedDraft.draftScope} was created by ${user.email}!&#x60;,
        });
        const modifiedDraft &#x3D; {
          ...savedDraft,
          createdBy: user.uid,
        } as Draft;

        return this.ticketValidationService.draftResponseFormatter(
          modifiedDraft,
        );
      },
    );
  }

  /**
   * Retrieves a paginated list of draft tickets based on user permissions
   *
   * Access Control Rules:
   * - Admins can view:
   *   - All team-scoped drafts
   *   - Their own personal drafts
   *   - Cannot view other users&#x27; personal drafts
   * - Regular users can view:
   *   - Their own personal drafts
   *   - Their own team-scoped drafts
   *
   * Filtering Rules:
   * - Excludes published and discarded drafts
   * - Orders by last updated date (newest first)
   * - Supports pagination
   *
   * @param user - Current user requesting drafts
   *   - user.sub: User ID
   *   - user.orgId: Organization ID
   *   - user.userType: Type of user (admin/regular)
   * @param query - Query parameters
   *   - page: Page number (0-based)
   *   - limit: Number of items per page
   *
   * @throws {ForbiddenException} When:
   *   - User not found
   *   - User tries to access unauthorized drafts
   *
   * @returns {Promise&lt;PaginatedResponseDto&lt;DraftTicketResponseDto&gt;&gt;} Paginated list of drafts
   *   - items: Array of formatted draft tickets
   *   - meta: Pagination metadata
   *     - totalItems: Total number of items
   *     - currentPage: Current page number
   *     - itemsPerPage: Items per page
   */
  async getDraftTickets(
    user: CurrentUser,
    query: GetDraftTicketQuery,
  ): Promise&lt;PaginatedResponseDto&lt;DraftTicketResponseDto&gt;&gt; {
    const { orgId, sub } &#x3D; user;
    const { page, limit } &#x3D; query;

    const userDetails &#x3D; await this.usersService.findOne(sub);
    if (!userDetails) {
      throw new ForbiddenException(&quot;User not found&quot;);
    }

    const baseQuery: FindManyOptions&lt;Draft&gt; &#x3D; {
      where: {
        organizationId: orgId,
        entityType: DraftType.TICKET,
        status: Not(In([DraftStatus.PUBLISHED, DraftStatus.DISCARDED])),
      },
      order: { updatedAt: &quot;DESC&quot; },
      relations: [&quot;createdByUser&quot;, &quot;lastModifiedByUser&quot;, &quot;organization&quot;],
    };

    let whereClause: FindOptionsWhere&lt;Draft&gt; | FindOptionsWhere&lt;Draft&gt;[];

    if (userDetails.userType &#x3D;&#x3D;&#x3D; UserType.ORG_ADMIN) {
      // Org admin can see both team drafts and their own personal drafts
      whereClause &#x3D; [
        {
          ...baseQuery.where,
          draftScope: DraftScope.TEAM,
        },
        {
          ...baseQuery.where,
          draftScope: DraftScope.PERSONAL,
          createdBy: sub,
        },
      ];
    } else {
      // Regular users can only see their own personal drafts
      whereClause &#x3D; {
        ...baseQuery.where,
        draftScope: DraftScope.PERSONAL,
        createdBy: sub,
      };
    }

    const finalQuery: FindManyOptions&lt;Draft&gt; &#x3D; {
      ...baseQuery,
      where: whereClause,
    };

    this.logger.log(&#x60;Final query: ${JSON.stringify(finalQuery)}&#x60;);

    const { results } &#x3D; await this.draftRepository.fetchPaginatedResults(
      {
        page: page ?? 0,
        limit: limit ?? 50,
      },
      finalQuery,
    );
    const paginatedResponse &#x3D; {
      items: results.map((draft) &#x3D;&gt; ({
        ...draft,
        createdBy: draft.createdByUser?.uid || draft.createdBy,
      })),
      meta: {
        totalItems: results.length,
        currentPage: page ?? 0,
        itemsPerPage: limit ?? 50,
      },
    };
    const formattedResponse &#x3D;
      this.ticketValidationService.formatPaginatedDraftResponse(
        paginatedResponse,
      );
    return formattedResponse;
  }

  /**
   * Retrieves a specific draft ticket with comprehensive access control
   *
   * Access Control Rules:
   * - Admins can access:
   *   - Any team-scoped drafts
   *   - Their own personal drafts
   * - Regular users can only access:
   *   - Their own drafts
   *
   * @param uid - Unique identifier of the draft ticket
   * @param user - Current user making the request
   *   - user.sub: User ID
   *   - user.userType: Type of user (admin/regular)
   *   - user.orgId: Organization ID
   *
   * @throws {NotFoundException} When draft is not found
   * @throws {ForbiddenException} When user doesn&#x27;t have access permission
   * @throws {BadRequestException} When uid is invalid
   *
   * @returns {Promise&lt;DraftTicketResponseDto&gt;} Formatted draft ticket response
   */
  async getDraftTicketByUid(
    uid: string,
    user: CurrentUser,
  ): Promise&lt;DraftTicketResponseDto&gt; {
    const draft &#x3D; await this.draftRepository.findByCondition({
      where: {
        uid,
        entityType: DraftType.TICKET,
        organizationId: user.orgId,
        deletedAt: IsNull(),
      },
      relations: [&quot;createdByUser&quot;, &quot;lastModifiedByUser&quot;],
    });

    if (!draft) {
      this.logger.debug(&#x60;Draft not found with uid: ${uid}&#x60;);
      throw new NotFoundException(&quot;Draft ticket not found&quot;);
    }

    // Access control logic
    const isAdmin &#x3D; user.userType &#x3D;&#x3D;&#x3D; UserType.ORG_ADMIN;
    const isCreator &#x3D; draft.createdBy &#x3D;&#x3D;&#x3D; user.sub;
    const isPersonalDraft &#x3D; draft.draftScope &#x3D;&#x3D;&#x3D; DraftScope.PERSONAL;

    const hasAccess &#x3D;
      isCreator || // Creator always has access
      (!isPersonalDraft &amp;&amp; isAdmin); // Admin has access to non-personal drafts

    if (!hasAccess) {
      this.logger.warn(
        &#x60;Access denied to draft ${uid} for user ${user.sub}. &#x60; +
          &#x60;Admin: ${isAdmin}, Creator: ${isCreator}, Personal: ${isPersonalDraft}&#x60;,
      );
      throw new ForbiddenException(&quot;Access denied to draft&quot;);
    }
    try {
      return this.ticketValidationService.draftResponseFormatter(draft);
    } catch (error) {
      this.logger.error(
        &#x60;Error formatting draft response for ${uid}: ${error.message}&#x60;,
        error.stack,
      );
      throw new BadRequestException(&quot;Error formatting draft response&quot;);
    }
  }

  /**
   * Updates a draft ticket with comprehensive access control and validation
   *
   * Access Control Rules:
   * - Admins can update:
   *   - Any team-scoped drafts
   *   - Their own personal drafts
   * - Regular users can only update:
   *   - Their own drafts (both personal and team-scoped)
   *
   * Update Rules:
   * - Original content is preserved before update
   * - Cannot update published drafts
   * - Cannot update discarded drafts
   * - Status changes are validated
   * - Team/organization context is preserved
   *
   * @param uid - Unique identifier of the draft ticket
   * @param draftTicketData - Data to update the draft with
   * @param user - Current user making the request
   *   - user.sub: User ID
   *   - user.userType: Type of user (admin/regular)
   *   - user.orgId: Organization ID
   *
   * @throws {NotFoundException} When draft is not found
   * @throws {ForbiddenException} When user doesn&#x27;t have access permission
   * @throws {BadRequestException} When:
   *   - Draft is published/discarded
   *   - Invalid status transition
   *   - Required fields are missing
   *   - Invalid data format
   *
   * @returns {Promise&lt;DraftTicketResponseDto&gt;} Updated draft ticket
   */
  async updateDraftTicket(
    uid: string,
    draftTicketData: UpdateDraftTicketDto,
    user: CurrentUser,
  ): Promise&lt;DraftTicketResponseDto&gt; {
    return await this.transactionService.runInTransaction(
      async (txnContext) &#x3D;&gt; {
        try {
          const draft &#x3D; await this.draftRepository.findByCondition({
            where: {
              uid,
              entityType: DraftType.TICKET,
              organizationId: user.orgId,
              deletedAt: IsNull(),
            },
          });

          if (!draft) {
            this.logger.debug(&#x60;Draft not found with uid: ${uid}&#x60;);
            throw new NotFoundException(&quot;Draft ticket not found&quot;);
          }

          // Status validation
          if (draft.status &#x3D;&#x3D;&#x3D; DraftStatus.PUBLISHED) {
            throw new BadRequestException(&quot;Cannot update published draft&quot;);
          }

          if (draft.status &#x3D;&#x3D;&#x3D; DraftStatus.DISCARDED) {
            throw new BadRequestException(&quot;Cannot update discarded draft&quot;);
          }

          // Access control logic
          const isAdmin &#x3D; user.userType &#x3D;&#x3D;&#x3D; UserType.ORG_ADMIN;
          const isCreator &#x3D; draft.createdBy &#x3D;&#x3D;&#x3D; user.sub;
          const isPersonalDraft &#x3D; draft.draftScope &#x3D;&#x3D;&#x3D; DraftScope.PERSONAL;

          const hasAccess &#x3D;
            isCreator || // Creator always has access
            (!isPersonalDraft &amp;&amp; isAdmin); // Admin has access to non-personal drafts

          if (!hasAccess) {
            this.logger.warn(
              &#x60;Update access denied to draft ${uid} for user ${user.sub}. &#x60; +
                &#x60;Admin: ${isAdmin}, Creator: ${isCreator}, Personal: ${isPersonalDraft}&#x60;,
            );
            throw new ForbiddenException(&quot;Access denied to update draft&quot;);
          }

          // Validate status transition if status is being updated
          if (draftTicketData.status) {
            this.ticketValidationService.validateStatusTransition(
              draft.status,
              draftTicketData.status,
            );
          }

          // Update content and metadata
          Object.assign(draft, {
            originalContent: draft.content,
            content: { ...draft.content, ...draftTicketData },
            metadata: { ...draft.metadata, ...draftTicketData.metadata },
            status: draftTicketData.status || draft.status,
            lastModifiedBy: user.sub,
          });

          const updatedDraft &#x3D; await this.draftRepository.saveWithTxn(
            txnContext,
            draft,
          );

          const modifiedDraft &#x3D; {
            ...updatedDraft,
            createdBy: user.uid,
          } as Draft;

          return this.ticketValidationService.draftResponseFormatter(
            modifiedDraft,
          );
        } catch (error) {
          this.logger.error(&#x60;Error updating draft ticket: ${error.message}&#x60;);
          throw new BadRequestException(
            &#x60;Error updating draft: ${error.message}&#x60;,
          );
        }
      },
    );
  }

  /**
   * Deletes (soft-delete) a draft ticket with scope-based access control
   *
   * Access Control Rules:
   * - Admins can delete:
   *   - Any team-scoped drafts
   *   - Their own personal drafts
   * - Regular users can only delete:
   *   - Their own drafts (both personal and team-scoped)
   *
   * Deletion Rules:
   * - Soft deletes the draft (sets deletedAt)
   * - Changes status to DISCARDED
   * - Cannot delete already published drafts
   * - Cannot delete already discarded drafts
   *
   * @param uid - Unique identifier of the draft ticket to delete
   * @param user - Current user attempting the deletion
   *   - user.sub: User ID
   *   - user.userType: Type of user (admin/regular)
   *   - user.orgId: Organization ID
   *
   * @throws {NotFoundException} When draft is not found
   * @throws {ForbiddenException} When user doesn&#x27;t have delete permission
   * @throws {BadRequestException} When:
   *   - Draft is already published
   *   - Draft is already discarded
   *
   * @returns {Promise&lt;Draft&gt;} The deleted (discarded) draft
   */
  async deleteDraftTicket(uid: string, user: CurrentUser): Promise&lt;Draft&gt; {
    return await this.transactionService.runInTransaction(
      async (txnContext) &#x3D;&gt; {
        const draft &#x3D; await this.draftRepository.findByCondition({
          where: {
            uid,
            entityType: DraftType.TICKET,
            deletedAt: IsNull(),
            organizationId: user.orgId,
          },
          relations: [&quot;createdByUser&quot;, &quot;lastModifiedByUser&quot;],
        });

        if (!draft) {
          throw new NotFoundException(&quot;Draft ticket not found&quot;);
        }

        // Access control logic
        const isAdmin &#x3D; user.userType &#x3D;&#x3D;&#x3D; UserType.ORG_ADMIN;
        const isCreator &#x3D; draft.createdBy &#x3D;&#x3D;&#x3D; user.sub;
        const isPersonalDraft &#x3D; draft.draftScope &#x3D;&#x3D;&#x3D; DraftScope.PERSONAL;

        if (
          (!isAdmin &amp;&amp; !isCreator) || // Non-admin can only delete their own drafts
          (isAdmin &amp;&amp; isPersonalDraft &amp;&amp; !isCreator) // Admin can&#x27;t delete others&#x27; personal drafts
        ) {
          this.logger.warn(
            &#x60;Delete access denied to draft ${uid} for user ${user.sub}. &#x60; +
              &#x60;Admin: ${isAdmin}, Creator: ${isCreator}, Personal: ${isPersonalDraft}&#x60;,
          );
          throw new ForbiddenException(&quot;Access denied to delete this draft&quot;);
        }

        // Status checks
        if (draft.status &#x3D;&#x3D;&#x3D; DraftStatus.DISCARDED) {
          throw new BadRequestException(&quot;Draft is already discarded&quot;);
        }

        if (draft.status &#x3D;&#x3D;&#x3D; DraftStatus.PUBLISHED) {
          throw new BadRequestException(&quot;Cannot delete published draft&quot;);
        }

        draft.deletedAt &#x3D; new Date();
        draft.status &#x3D; DraftStatus.DISCARDED;
        const deletedDraft &#x3D; await this.draftRepository.saveWithTxn(
          txnContext,
          draft,
        );
        return deletedDraft;
      },
    );
  }

  /**
   * Publishes a draft ticket by creating an actual ticket
   *
   * Access Control Rules:
   * - Only the creator can publish their own drafts
   * - Admins can publish team-scoped drafts
   * - No one can publish others&#x27; personal drafts
   *
   * Publishing Rules:
   * - Draft must be in READY_TO_PUBLISH status
   * - Cannot publish already published drafts
   * - Cannot publish discarded drafts
   * - All required ticket fields must be valid
   * - Creates a new ticket and marks draft as published
   *
   * @param uid - Unique identifier of the draft ticket
   * @param user - Current user attempting to publish
   *   - user.sub: User ID
   *   - user.userType: Type of user (admin/regular)
   *   - user.orgId: Organization ID
   *
   * @throws {NotFoundException} When draft is not found
   * @throws {ForbiddenException} When:
   *   - User doesn&#x27;t have publish permission
   *   - Trying to publish others&#x27; personal drafts
   * @throws {BadRequestException} When:
   *   - Draft is not in READY_TO_PUBLISH status
   *   - Draft is already published
   *   - Draft is incomplete
   *   - Validation fails
   *
   * @returns {Promise&lt;DraftTicketResponseDto&gt;} The published draft
   */
  async publishDraftTicket(
    uid: string,
    user: CurrentUser,
  ): Promise&lt;DraftTicketResponseDto&gt; {
    return await this.transactionService.runInTransaction(
      async (txnContext) &#x3D;&gt; {
        // Get the raw draft entity instead of the formatted response
        const draft &#x3D; await this.draftRepository.findByCondition({
          where: {
            uid,
            entityType: DraftType.TICKET,
            deletedAt: IsNull(),
          },
          relations: [&quot;createdByUser&quot;, &quot;lastModifiedByUser&quot;],
        });

        if (!draft) {
          throw new NotFoundException(&quot;Draft ticket not found&quot;);
        }

        // Status validation
        if (draft.status &#x3D;&#x3D;&#x3D; DraftStatus.PUBLISHED) {
          throw new BadRequestException(&quot;Draft is already published&quot;);
        }

        if (draft.status &#x3D;&#x3D;&#x3D; DraftStatus.DISCARDED) {
          throw new BadRequestException(&quot;Cannot publish discarded draft&quot;);
        }

        if (draft.status !&#x3D;&#x3D; DraftStatus.READY_TO_PUBLISH) {
          throw new BadRequestException(
            &quot;Draft must be in READY_TO_PUBLISH status before publishing&quot;,
          );
        }

        // Access control logic
        const isAdmin &#x3D; user.userType &#x3D;&#x3D;&#x3D; UserType.ORG_ADMIN;
        const isCreator &#x3D; draft.createdBy &#x3D;&#x3D;&#x3D; user.sub;
        const isPersonalDraft &#x3D; draft.draftScope &#x3D;&#x3D;&#x3D; DraftScope.PERSONAL;

        if (
          (!isAdmin &amp;&amp; !isCreator) || // Non-admin can only publish their own drafts
          (isAdmin &amp;&amp; isPersonalDraft &amp;&amp; !isCreator) // Admin can&#x27;t publish others&#x27; personal drafts
        ) {
          this.logger.warn(
            &#x60;Publish access denied to draft ${uid} for user ${user.sub}. &#x60; +
              &#x60;Admin: ${isAdmin}, Creator: ${isCreator}, Personal: ${isPersonalDraft}&#x60;,
          );
          throw new ForbiddenException(&quot;Access denied to publish this draft&quot;);
        }

        // Validate draft completeness
        if (!this.ticketValidationService.isValidTicketDraft(draft)) {
          this.logger.error(&#x60;Draft is incomplete: ${draft.uid}&#x60;);
          throw new BadRequestException(&quot;Draft is incomplete&quot;);
        }

        try {
          //Create new ticket body and call the service
          const team &#x3D; await this.ticketValidationService.validateTeam(
            draft.content.teamId,
            user.orgId,
          );

          const ticketBody &#x3D;
            this.ticketValidationService.createTicketFromDraft(draft);
          const savedTicket &#x3D; await this.ticketService.createTicket(
            user,
            team,
            ticketBody,
            RequestSource.WEB,
          );

          // Update draft status
          draft.status &#x3D; DraftStatus.PUBLISHED;
          await this.draftRepository.saveWithTxn(txnContext, draft);

          // Record activity
          await this.activitiesService.recordAuditLog({
            team: { id: team.id },
            organization: { id: user.orgId },
            activityPerformedBy: { id: user.sub },
            entityType: AuditLogEntityType.DRAFTS,
            entityId: draft.id,
            entityUid: draft.uid,
            op: AuditLogOp.UPDATED,
            visibility: AuditLogVisibility.ORGANIZATION,
            activity: &#x60;A ticket with id: ${savedTicket.id} was published from draft with id: ${draft.id}&#x60;,
            description: &#x60;A ticket with id: ${savedTicket.id} was published from draft with id: ${draft.id} by ${user.email}!&#x60;,
            metadata: {
              updatedFields: [
                {
                  field: &quot;status&quot;,
                  previousValue: DraftStatus.READY_TO_PUBLISH,
                  updatedToValue: DraftStatus.PUBLISHED,
                },
              ],
            },
          });

          const modifiedDraft &#x3D; {
            ...draft,
            createdBy: user.uid,
          } as Draft;

          return this.ticketValidationService.draftResponseFormatter(
            modifiedDraft,
          );
          // return savedTicket;
        } catch (error) {
          this.logger.error(&#x60;Failed to publish draft ticket: ${error.message}&#x60;);
          throw new BadRequestException(
            &#x60;Failed to publish draft: ${error.message}&#x60;,
          );
        }
      },
    );
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'TicketDraftService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
