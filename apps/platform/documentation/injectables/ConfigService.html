<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>@thena-backend/platform documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">@thena-backend/platform documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">








<ol class="breadcrumb">
  <li class="breadcrumb-item">Injectables</li>
  <li class="breadcrumb-item" >ConfigService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/config/config.service.ts</code>
        </p>





            <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Readonly</span>
                                <a href="#envConfig" >envConfig</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#get" >get</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#validateEnv" >validateEnv</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(filePath: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="69" class="link-to-prism">src/config/config.service.ts:69</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>filePath</td>
                                                  
                                                        <td>
                                                                        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section data-compodoc="block-methods">
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="get"></a>
                    <span class="name">
                        <span ><b>get</b></span>
                        <a href="#get"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>get(key: <a href="../undefineds/ConfigKeys.html" target="_self">ConfigKeys</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="81"
                                    class="link-to-prism">src/config/config.service.ts:81</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>key</td>
                                            <td>
                                                            <code><a href="../miscellaneous/enumerations.html#ConfigKeys" target="_self" >ConfigKeys</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateEnv"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>validateEnv</b></span>
                        <a href="#validateEnv"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateEnv(config: literal type)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="88"
                                    class="link-to-prism">src/config/config.service.ts:88</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>
                                
                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>config</td>
                                            <td>
                                                        <code>literal type</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>literal type</code>

                        </div>
                            <div class="io-description">
                                
                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>
            <section data-compodoc="block-properties">
    
    <h3 id="inputs">
        Properties
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="envConfig"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                            <span class="modifier">Readonly</span>
                        <span ><b>envConfig</b></span>
                        <a href="#envConfig"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>literal type</code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="69" class="link-to-prism">src/config/config.service.ts:69</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Injectable } from &quot;@nestjs/common&quot;;
import { ValidationError } from &quot;@repo/nestjs-commons/errors&quot;;
import * as dotenv from &quot;dotenv&quot;;
import * as fs from &quot;fs&quot;;
import * as Joi from &quot;joi&quot;;
import { ERROR_CODES } from &quot;../constants/error-codes.constants&quot;;
import { TYPESENSE_CONSTANTS } from &quot;./typesense/constants/typesense.constants&quot;;

export enum ConfigKeys {
  APP_TAG &#x3D; &quot;APP_TAG&quot;,
  SERVICE_TAG &#x3D; &quot;SERVICE_TAG&quot;,
  NODE_ENV &#x3D; &quot;NODE_ENV&quot;,

  // Auth
  JWT_SECRET &#x3D; &quot;JWT_SECRET&quot;,

  // Database
  THENA_PLATFORM_DB_HOST &#x3D; &quot;THENA_PLATFORM_DB_HOST&quot;,
  THENA_PLATFORM_DB_PORT &#x3D; &quot;THENA_PLATFORM_DB_PORT&quot;,
  THENA_PLATFORM_DB_NAME &#x3D; &quot;THENA_PLATFORM_DB_NAME&quot;,
  THENA_PLATFORM_DB_USER &#x3D; &quot;THENA_PLATFORM_DB_USER&quot;,
  THENA_PLATFORM_DB_PASSWORD &#x3D; &quot;THENA_PLATFORM_DB_PASSWORD&quot;,
  SUPABASE_STAGING_DB_URL &#x3D; &quot;SUPABASE_STAGING_DB_URL&quot;,

  // Redis
  REDIS_HOST &#x3D; &quot;REDIS_HOST&quot;,
  REDIS_PORT &#x3D; &quot;REDIS_PORT&quot;,
  REDIS_USERNAME &#x3D; &quot;REDIS_USERNAME&quot;,
  REDIS_PASSWORD &#x3D; &quot;REDIS_PASSWORD&quot;,

  // AWS Secrets
  AWS_ACCESS_KEY &#x3D; &quot;AWS_ACCESS_KEY&quot;,
  AWS_SECRET_KEY &#x3D; &quot;AWS_SECRET_KEY&quot;,
  AWS_REGION &#x3D; &quot;AWS_REGION&quot;,
  AWS_SQS_QUEUE_URL &#x3D; &quot;AWS_SQS_QUEUE_URL&quot;,
  AWS_SQS_EMBEDDING_QUEUE_URL &#x3D; &quot;AWS_SQS_EMBEDDING_QUEUE_URL&quot;,
  AWS_SQS_SLA_QUEUE_URL &#x3D; &quot;AWS_SQS_SLA_QUEUE_URL&quot;,
  AWS_SQS_SLA_CONSUMER_QUEUE_URL &#x3D; &quot;AWS_SQS_SLA_CONSUMER_QUEUE_URL&quot;,
  AWS_SNS_TICKET_TOPIC_ARN &#x3D; &quot;AWS_SNS_TICKET_TOPIC_ARN&quot;,
  AWS_SNS_ACCOUNTS_TOPIC_ARN &#x3D; &quot;AWS_SNS_ACCOUNTS_TOPIC_ARN&quot;,
  AWS_SNS_ORGANIZATION_TOPIC_ARN &#x3D; &quot;AWS_SNS_ORGANIZATION_TOPIC_ARN&quot;,

  // Typesense
  TYPESENSE_HOST &#x3D; &quot;TYPESENSE_HOST&quot;,
  TYPESENSE_PORT &#x3D; &quot;TYPESENSE_PORT&quot;,
  TYPESENSE_PROTOCOL &#x3D; &quot;TYPESENSE_PROTOCOL&quot;,
  TYPESENSE_API_KEY &#x3D; &quot;TYPESENSE_API_KEY&quot;,
  TYPESENSE_ADMIN_API_KEY &#x3D; &quot;TYPESENSE_ADMIN_API_KEY&quot;,
  TYPESENSE_TIMEOUT &#x3D; &quot;TYPESENSE_TIMEOUT&quot;,

  // Sentry
  SENTRY_DSN &#x3D; &quot;SENTRY_DSN&quot;,

  // S3
  TICKET_BUCKET &#x3D; &quot;TICKET_BUCKET&quot;,

  // gRPC
  PLATFORM_GRPC_URL &#x3D; &quot;PLATFORM_GRPC_URL&quot;,
  WORKFLOWS_GRPC_URL &#x3D; &quot;WORKFLOWS_GRPC_URL&quot;,
  AUTH_GRPC_URL &#x3D; &quot;AUTH_GRPC_URL&quot;,

  // Bull
  BULL_ADMIN_USER &#x3D; &quot;BULL_ADMIN_USER&quot;,
  BULL_ADMIN_PASSWORD &#x3D; &quot;BULL_ADMIN_PASSWORD&quot;,
}

@Injectable()
export class ConfigService {
  private readonly envConfig: { [key: string]: string };

  constructor(filePath: string) {
    // Load the .env file
    const config &#x3D; fs.existsSync(filePath)
      ? dotenv.parse(fs.readFileSync(filePath))
      : process.env;

    // Load the secrets from AWS Secrets Manager
    this.envConfig &#x3D; this.validateEnv(config);
  }

  get(key: ConfigKeys): string {
    if (!(key in this.envConfig)) {
      throw new Error(&#x60;Configuration key &quot;${key}&quot; does not exist&#x60;);
    }
    return String(this.envConfig[key]);
  }

  private validateEnv(config: { [key: string]: string }): {
    [key: string]: string;
  } {
    const envVarsSchema &#x3D; Joi.object({
      [ConfigKeys.APP_TAG]: Joi.string().required(),
      [ConfigKeys.SERVICE_TAG]: Joi.string().required(),
      [ConfigKeys.NODE_ENV]: Joi.string()
        .valid(&quot;development&quot;, &quot;production&quot;, &quot;test&quot;, &quot;staging&quot;)
        .required(),
      // Database
      [ConfigKeys.THENA_PLATFORM_DB_HOST]: Joi.string().required(),
      [ConfigKeys.THENA_PLATFORM_DB_PORT]: Joi.number().required(),
      [ConfigKeys.THENA_PLATFORM_DB_NAME]: Joi.string().required(),
      [ConfigKeys.THENA_PLATFORM_DB_USER]: Joi.string().required(),
      [ConfigKeys.THENA_PLATFORM_DB_PASSWORD]: Joi.string().required(),
      [ConfigKeys.SUPABASE_STAGING_DB_URL]: Joi.string().required(),
      // AWS
      [ConfigKeys.AWS_ACCESS_KEY]: Joi.string().required(),
      [ConfigKeys.AWS_SECRET_KEY]: Joi.string().required(),
      [ConfigKeys.AWS_REGION]: Joi.string().required(),
      [ConfigKeys.AWS_SQS_QUEUE_URL]: Joi.string().required(),
      [ConfigKeys.AWS_SQS_EMBEDDING_QUEUE_URL]: Joi.string().required(),
      [ConfigKeys.AWS_SQS_SLA_QUEUE_URL]: Joi.string().required(),
      [ConfigKeys.AWS_SNS_TICKET_TOPIC_ARN]: Joi.string().required(),
      [ConfigKeys.AWS_SNS_ACCOUNTS_TOPIC_ARN]: Joi.string().required(),
      [ConfigKeys.AWS_SQS_SLA_CONSUMER_QUEUE_URL]: Joi.string().required(),
      // Redis
      [ConfigKeys.REDIS_HOST]: Joi.string().required(),
      [ConfigKeys.REDIS_PORT]: Joi.number().required().default(18283),
      [ConfigKeys.REDIS_USERNAME]: Joi.string().default(&quot;default&quot;),
      [ConfigKeys.REDIS_PASSWORD]: Joi.string().required(),
      [ConfigKeys.TICKET_BUCKET]: Joi.string().required(),
      // Sentry
      [ConfigKeys.SENTRY_DSN]: Joi.string().required(),
      // Typesense validations
      [ConfigKeys.TYPESENSE_HOST]: Joi.string().required(),
      [ConfigKeys.TYPESENSE_PORT]: Joi.number().required(),
      [ConfigKeys.TYPESENSE_PROTOCOL]: Joi.string().required(),
      [ConfigKeys.TYPESENSE_API_KEY]: Joi.string().required(),
      [ConfigKeys.TYPESENSE_ADMIN_API_KEY]: Joi.string().optional(),
      [ConfigKeys.TYPESENSE_TIMEOUT]: Joi.number().default(
        TYPESENSE_CONSTANTS.CONNECTION_TIMEOUT,
      ),
      // gRPC
      [ConfigKeys.PLATFORM_GRPC_URL]: Joi.string().required(),
      [ConfigKeys.WORKFLOWS_GRPC_URL]: Joi.string().required(),
      [ConfigKeys.AUTH_GRPC_URL]: Joi.string().required(),
      // Bull
      [ConfigKeys.BULL_ADMIN_USER]: Joi.string().required(),
      [ConfigKeys.BULL_ADMIN_PASSWORD]: Joi.string().required(),
    }).unknown();

    const { error, value: validatedEnvConfig } &#x3D; envVarsSchema.validate(config);
    if (error) {
      throw new ValidationError(
        &#x60;Config validation error: ${error.message}&#x60;,
        ERROR_CODES.CONFIG_VALIDATION_ERROR_CODE,
        true,
      );
    }

    return validatedEnvConfig;
  }
}
</code></pre>
    </div>

</div>













                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'ConfigService.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
