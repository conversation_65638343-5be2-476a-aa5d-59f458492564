.nav-tabs > li > a {
    text-decoration: none;
}

.navbar-default .navbar-brand {
    color: #f4645f;
    text-decoration: none;
    font-size: 16px;
}

.menu ul.list li a[data-type='chapter-link'],
.menu ul.list li.chapter .simple {
    color: #525252;
    border-bottom: 1px dashed rgba(0, 0, 0, 0.1);
}

.content h1,
.content h2,
.content h3,
.content h4,
.content h5 {
    color: #292e31;
    font-weight: normal;
}

.content {
    color: #4c555a;
}

a {
    color: #f4645f;
    text-decoration: underline;
}
a:hover {
    color: #f1362f;
}

.menu ul.list li:nth-child(2) {
    margin-top: 0;
}

.menu ul.list li.title a {
    color: #f4645f;
    text-decoration: none;
    font-size: 16px;
}

.menu ul.list li a {
    color: #f4645f;
    text-decoration: none;
}
.menu ul.list li a.active {
    color: #f4645f;
    font-weight: bold;
}

code {
    box-sizing: border-box;
    display: inline-block;
    padding: 0 5px;
    background: #f0f2f1;
    border-radius: 3px;
    color: #b93d6a;
    font-size: 13px;
    line-height: 20px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.125);
}

pre {
    margin: 0;
    padding: 12px 12px;
    background: rgba(238, 238, 238, 0.35);
    border-radius: 3px;
    font-size: 13px;
    line-height: 1.5em;
    font-weight: 500;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.125);
}

.dark body {
    color: #fafafa;
}
.dark .content h1,
.dark .content h2,
.dark .content h3,
.dark .content h4,
.dark .content h5 {
    color: #fafafa;
}

.dark code {
    background: none;
}

.dark .content {
    color: #fafafa;
}

.dark .menu ul.list li a[data-type='chapter-link'],
.dark .menu ul.list li.chapter .simple {
    color: #fafafa;
}

.dark .menu ul.list li.title a {
    color: #fafafa;
}

.dark .menu ul.list li a {
    color: #fafafa;
}
.dark .menu ul.list li a.active {
    color: #7fc9ff;
}
