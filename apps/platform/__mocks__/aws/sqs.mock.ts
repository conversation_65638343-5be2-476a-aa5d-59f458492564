export const SQSModule = {
  Producer: jest.fn().mockReturnValue({
    module: class {},
    providers: [],
  }),
  Consumer: jest.fn().mockReturnValue({
    module: class {},
    providers: [],
  }),
};

export const SQSProducerService = jest.fn().mockImplementation(() => ({
  sendMessage: jest.fn().mockResolvedValue({}),
}));

export const SQSConsumerService = jest.fn().mockImplementation(() => ({
  start: jest.fn(),
  stop: jest.fn(),
  onModuleInit: jest.fn(),
  onModuleDestroy: jest.fn(),
  initializeSQSClient: jest.fn(),
  receiveMessages: jest.fn().mockResolvedValue([]),
  deleteMessage: jest.fn().mockResolvedValue({}),
  startConsumer: jest.fn(),
  stopConsumer: jest.fn(),
  poll: jest.fn(),
  processMessage: jest.fn(),
}));
