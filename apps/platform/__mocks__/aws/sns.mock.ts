export const SNSModule = {
  forRootAsync: jest.fn().mockReturnValue({
    module: class {},
    providers: [],
  }),
};

export const SNSPublisherService = jest.fn().mockImplementation(() => ({
  publishSNSMessage: jest.fn().mockResolvedValue({}),
}));

export const TicketEvents = {
  CREATED: "ticket:created",
  UPDATED: "ticket:updated",
  ARCHIVED: "ticket:archived",
  DELETED: "ticket:deleted",
};
export const ContextUserType = {
  USER: "user",
  ORGANIZATION: "organization",
};

export const SNSService = jest.fn().mockImplementation(() => ({
  onModuleInit: jest.fn(),
  publish: jest.fn().mockResolvedValue({}),
  snsClient: {
    send: jest.fn().mockResolvedValue({}),
  },
}));
