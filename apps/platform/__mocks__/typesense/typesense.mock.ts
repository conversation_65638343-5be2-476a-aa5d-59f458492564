export const Client = jest.fn().mockImplementation(() => ({
  collections: jest.fn().mockReturnValue({
    documents: jest.fn().mockReturnValue({
      search: jest.fn().mockImplementation((searchParameters) => {
        const hitsPerPage = parseInt(searchParameters.hits_per_page) || 20;
        const page = parseInt(searchParameters.page) || 1;

        // Generate base results
        const allResults =
          searchParameters.q === "network"
            ? [
                {
                  document: {
                    title: "Network connectivity issues",
                    organizationId: global.testOrganization.uid,
                  },
                },
              ]
            : Array(5)
                .fill({})
                .map((_, i) => ({
                  document: {
                    title: `Test Ticket ${i}`,
                    organizationId: global.testOrganization.uid,
                  },
                }));

        // Apply pagination
        const start = (page - 1) * hitsPerPage;
        const paginatedHits = allResults.slice(start, start + hitsPerPage);

        return {
          hits: paginatedHits,
          page,
          found: allResults.length, // Total number of results
          found_docs: hitsPerPage, // Results per page
        };
      }),
      create: jest.fn().mockResolvedValue({ id: "mock-id" }),
      update: jest.fn().mockResolvedValue({ id: "mock-id" }),
      delete: jest.fn().mockResolvedValue({ id: "mock-id" }),
      upsert: jest.fn().mockResolvedValue({ id: "mock-id" }),
    }),
  }),
}));

export const TypesenseModule = {
  forRoot: jest.fn().mockReturnValue({
    module: class {},
    providers: [],
  }),
};

export const TypesenseService = jest.fn().mockImplementation(() => ({
  client: new Client(),
}));
