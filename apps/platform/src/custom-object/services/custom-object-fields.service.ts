import {
  BadRequestException,
  Inject,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import { ILogger } from "@repo/nestjs-commons/logger";
import {
  CustomObjectFieldsRepository,
  CustomValidationError,
} from "@repo/thena-platform-entities";
import { IsNull } from "typeorm";
import { IdGeneratorUtils } from "../../common/utils/id-generator.utils";
import { GetObjectFieldsRelations } from "../constants";
import { AddFieldDto, RemoveFieldDto } from "../dto/custom-object-fields.dto";

@Injectable()
export class CustomObjectFieldsService {
  constructor(
    @Inject("CustomLogger")
    private readonly logger: ILogger,
    private customObjectFieldsRepository: CustomObjectFieldsRepository,
  ) {}

  async addField(userId: string, addFieldDto: AddFieldDto) {
    const uid = IdGeneratorUtils.generate("COF");

    const customObjectField = this.customObjectFieldsRepository.create({
      uid,
      parentObjectId: addFieldDto.parentObjectId,
      parentObject: {
        id: addFieldDto.parentObjectId,
      },
      fieldId: addFieldDto.fieldId,
      field: {
        id: addFieldDto.fieldId,
      },
      childObject: addFieldDto.childObjectId
        ? {
            id: addFieldDto.childObjectId,
          }
        : null,
      childObjectId: addFieldDto.childObjectId,
      childEntityId: addFieldDto.childEntityId,
      relationshipType: addFieldDto.relationshipType,
      defaultValue: addFieldDto.defaultValue,
      version: 1,
      createdBy: {
        id: userId,
      },
    });

    let res;
    try {
      res = await this.customObjectFieldsRepository.save(customObjectField);
    } catch (error) {
      if (error instanceof CustomValidationError) {
        throw new BadRequestException(error.message);
      }
      throw error;
    }
    return res;
  }

  async removeField(userId: string, removeFieldDto: RemoveFieldDto) {
    const customObjectField =
      await this.customObjectFieldsRepository.findByCondition({
        where: {
          parentObjectId: removeFieldDto.parentObjectId,
          fieldId: removeFieldDto.fieldId,
          deletedAt: IsNull(),
        },
      });

    if (!customObjectField) {
      throw new NotFoundException("Custom object field not found!");
    }

    await this.customObjectFieldsRepository.update(customObjectField.id, {
      deletedAt: new Date(),
      version: customObjectField.version + 1,
      updatedBy: {
        id: userId,
      },
    });
  }

  async getFields(customObjectId: string) {
    const fields = await this.customObjectFieldsRepository.findAll({
      where: { parentObjectId: customObjectId, deletedAt: IsNull() },
      relations: GetObjectFieldsRelations,
    });

    return fields;
  }
}
