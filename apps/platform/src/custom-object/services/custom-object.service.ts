import { BadRequestException, Inject, Injectable } from "@nestjs/common";
import { ILogger } from "@repo/nestjs-commons/logger";
import {
  CustomObjectRepository,
  CustomValidationError,
} from "@repo/thena-platform-entities";
import * as _ from "lodash";
import { ILike, In, IsNull } from "typeorm";
import { IdGeneratorUtils } from "../../common/utils/id-generator.utils";
import { GetObjectRelations } from "../constants";
import {
  CreateCustomObjectDto,
  UpdateCustomObjectDto,
} from "../dto/custom-object.dto";

@Injectable()
export class CustomObjectService {
  constructor(
    @Inject("CustomLogger")
    private readonly logger: ILogger,
    private customObjectRepository: CustomObjectRepository,
  ) {}

  async create(
    orgId: string,
    userId: string,
    createCustomObjectDto: CreateCustomObjectDto,
  ) {
    const uid = IdGeneratorUtils.generate("CO");
    // eslint-disable-next-line max-len
    const customObject = this.customObjectRepository.create({
      organization: {
        id: orgId,
      },
      organizationId: orgId,
      name: createCustomObjectDto.name,
      uid,
      team: createCustomObjectDto.teamId
        ? {
            id: createCustomObjectDto.teamId,
          }
        : undefined,
      teamId: createCustomObjectDto.teamId,
      createdBy: {
        id: userId,
      },
      version: 1,
    });

    let res;
    try {
      res = await this.customObjectRepository.save(customObject);
    } catch (error) {
      if (error instanceof CustomValidationError) {
        throw new BadRequestException(error.message);
      }
      throw error;
    }
    return res;
  }

  async update(
    userId: string,
    organizationId: string,
    updateCustomObjectDto: UpdateCustomObjectDto,
  ) {
    if (
      !updateCustomObjectDto.objects ||
      updateCustomObjectDto.objects.length === 0
    ) {
      throw new BadRequestException("No objects to update");
    }

    const promises = updateCustomObjectDto.objects.map(async (update) => {
      await this.customObjectRepository.update(
        { organizationId, uid: update.id, version: update.version },
        {
          ...update.updates,
          version: update.version + 1,
          updatedBy: { id: userId },
        },
      );
    });

    const results = await Promise.allSettled(promises);

    const failedUpdates = results
      .map((result, index) =>
        result.status === "rejected"
          ? updateCustomObjectDto.objects[index].id
          : null,
      )
      .filter((id) => id !== null);

    if (failedUpdates.length > 0) {
      throw new BadRequestException(
        `Failed to update the following IDs: ${failedUpdates.join(", ")}`,
      );
    }
  }

  async findByIds(orgId: string, ids: string[]) {
    const customObjects = await this.customObjectRepository.findAll({
      relations: GetObjectRelations,
      where: { organizationId: orgId, uid: In(ids), deletedAt: IsNull() },
    });
    return {
      data: customObjects,
    };
  }

  /**
   * Finds custom objects by ids.
   * @param request The FastifyRequest object containing user info
   * @param ids Array of custom object IDs to fetch
   * @returns Custom objects matching the provided IDs
   */
  async findByIdsWithTeamCheck(
    orgId: string,
    ids: string[],
    teamIds?: string[],
  ) {
    teamIds = teamIds.filter(Boolean);
    const query = [
      {
        organizationId: orgId,
        uid: In(ids),
        deletedAt: IsNull(),
        teamId: IsNull(),
      },
    ];

    if (teamIds?.length > 0) {
      query.push({
        organizationId: orgId,
        uid: In(ids),
        deletedAt: IsNull(),
        teamId: In(teamIds),
      });
    }
    const customObjects = await this.customObjectRepository.findAll({
      relations: GetObjectRelations,
      where: query,
    });
    if (customObjects.length !== ids.length) {
      const missingObjects = ids.filter(
        (id) => !customObjects.some((object) => object.uid === id),
      );
      throw new BadRequestException(
        `Some objects not found, either they are deleted or not found in your teams, missing objects: ${missingObjects.join(
          ", ",
        )}`,
      );
    }
    return {
      data: customObjects,
    };
  }

  /**
   * Search custom fields by name.
   * @param term term to search
   * @returns Custom fields matching the provided term
   */
  async search(
    orgId: string,
    term: string,
    teamId?: string,
    onlyTeamObjects?: boolean,
  ) {
    if (_.isEmpty(term?.trim())) {
      throw new BadRequestException("No search term provided");
    }
    const query = [];
    if (teamId) {
      query.push({
        name: ILike(`%${term.toLocaleLowerCase()}%`),
        organizationId: orgId,
        deletedAt: IsNull(),
        teamId: teamId,
      });
    }

    if (!teamId || !onlyTeamObjects) {
      query.push({
        name: ILike(`%${term.toLocaleLowerCase()}%`),
        organizationId: orgId,
        deletedAt: IsNull(),
        teamId: IsNull(),
      });
    }

    const customObjects = await this.customObjectRepository.findAll({
      relations: GetObjectRelations,
      where: query,
    });
    return {
      data: customObjects,
    };
  }

  /**
   * Finds all custom fields.
   * @returns All custom fields.
   */
  async fetchPaginatedResults(
    orgId: string,
    limit: number,
    page: number,
    teamId?: string,
    onlyTeamObjects?: boolean,
  ) {
    const query = [];
    if (teamId) {
      query.push({
        organizationId: orgId,
        deletedAt: IsNull(),
        teamId: teamId,
      });
    }

    if (!teamId || !onlyTeamObjects) {
      query.push({
        organizationId: orgId,
        deletedAt: IsNull(),
        teamId: IsNull(),
      });
    }

    if (query.length === 0) {
      throw new BadRequestException("No fields to fetch");
    }

    const customObjects =
      await this.customObjectRepository.fetchPaginatedResults(
        { limit, page },
        {
          where: query,
        },
      );

    return {
      ...customObjects,
      page: (customObjects.results || []).length > 0 ? ++page : undefined,
    };
  }
}
