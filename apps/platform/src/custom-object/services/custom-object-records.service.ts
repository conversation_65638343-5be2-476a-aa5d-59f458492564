import { InjectQueue } from "@nestjs/bullmq";
import {
  BadRequestException,
  Inject,
  Injectable,
  InternalServerErrorException,
} from "@nestjs/common";
import { ILogger } from "@repo/nestjs-commons/logger";
import {
  CustomFieldValues,
  CustomObjectRecordsRepository,
  Team,
} from "@repo/thena-platform-entities";
import { CustomObjectEvents } from "@repo/thena-shared-interfaces";
import { Queue } from "bullmq";
import * as rTracer from "cls-rtracer";
import { isArray } from "lodash";
import { In, IsNull } from "typeorm";
import { CurrentUser } from "../../common/decorators/user.decorator";
import { IdGeneratorUtils } from "../../common/utils/id-generator.utils";
import { JobConstants } from "../../constants/job.constants";
import { QueueNames } from "../../constants/queue.constants";
import { CreateCustomFieldValuesDto } from "../../custom-field/dto/custom-field-values.dto";
import { CustomFieldValuesService } from "../../custom-field/services/custom-field-values.service";
import { GetObjectRecordRelations } from "../constants";
import {
  CreateObjectRecordDto,
  UpdateObjectRecordDto,
} from "../dto/custom-object-records.dto";

@Injectable()
export class CustomObjectRecordsService {
  constructor(
    @Inject("CustomLogger")
    private readonly logger: ILogger,

    @InjectQueue(QueueNames.CUSTOM_OBJECT_SNS_PUBLISHER)
    private readonly snsPublishQueue: Queue,

    private customObjectRecordsRepository: CustomObjectRecordsRepository,
    private customFieldValuesService: CustomFieldValuesService,
  ) {}

  async create(
    orgId: string,
    user: CurrentUser,
    createObjectRecordDto: CreateObjectRecordDto,
    team: Team,
  ) {
    const uid = IdGeneratorUtils.generate("COR");

    let customFieldValues: CustomFieldValues[] | null = null;
    if (createObjectRecordDto.customFieldValues?.length) {
      customFieldValues = await this.createCustomFieldValues(
        createObjectRecordDto.customFieldValues,
        orgId,
      );
    }

    const record = this.customObjectRecordsRepository.create({
      teamId: team?.id,
      uid,
      customObjectId: createObjectRecordDto.customObjectId,
      customObject: {
        id: createObjectRecordDto.customObjectId,
      },
      team: team ? { id: team.id } : undefined,
      customFieldValues,
      createdBy: {
        id: user.sub,
      },
      version: 1,
    });

    try {
      const result = await this.customObjectRecordsRepository.save(record);

      // Publish the record to SNS
      await this.publishSNS({
        eventType: CustomObjectEvents.RECORD_CREATED,
        user: user,
        team: team,
        eventData: {
          payload: {
            record: {
              id: result.uid,
              teamId: team?.uid,
              customFieldValues: createObjectRecordDto.customFieldValues,
            },
          },
          eventType: CustomObjectEvents.RECORD_CREATED,
          timestamp: Date.now(),
        },
        reqId: rTracer.id(),
        messageGroupId: result.uid,
      });
      return result;
    } catch (error) {
      this.logger.error(`Error creating record: ${error.message}`);
      throw new InternalServerErrorException("Failed to create record");
    }
  }

  async update(
    updateObjectRecordDto: UpdateObjectRecordDto,
    user: CurrentUser,
    orgId: string,
  ) {
    if (!updateObjectRecordDto.customObjectId) {
      throw new BadRequestException("No ID provided for update");
    }

    const existingRecord =
      await this.customObjectRecordsRepository.findByCondition({
        where: { id: updateObjectRecordDto.recordId, deletedAt: IsNull() },
        relations: GetObjectRecordRelations,
      });

    if (!existingRecord) {
      throw new BadRequestException("Record not found!");
    }

    if (isArray(updateObjectRecordDto.customFieldValues)) {
      const newCustomFieldValues: CustomFieldValues[] =
        await this.customFieldValuesService.createCustomFieldValues(
          updateObjectRecordDto.customFieldValues,
          orgId,
        );

      const allowedPrevCustomFieldValues: CustomFieldValues[] = [];
      const existingCustomFieldValues: CustomFieldValues[] =
        existingRecord.customFieldValues ?? [];

      for (const customFieldValue of existingCustomFieldValues) {
        if (
          !newCustomFieldValues.find(
            (value) => value.customField.id === customFieldValue.customField.id,
          )
        ) {
          allowedPrevCustomFieldValues.push(customFieldValue);
        }
      }

      existingRecord.customFieldValues = [
        ...allowedPrevCustomFieldValues,
        ...newCustomFieldValues,
      ];
    }

    const result = await this.customObjectRecordsRepository.save({
      ...existingRecord,
      updatedBy: { id: user.sub },
    });

    // Publish the record to SNS
    await this.publishSNS({
      eventType: CustomObjectEvents.RECORD_UPDATED,
      user: user,
      team: result.team,
      reqId: rTracer.id(),
      messageGroupId: result.uid,
      eventData: {
        payload: {
          record: {
            id: result.uid,
            teamId: result.team?.uid,
            customFieldValues: updateObjectRecordDto.customFieldValues,
          },
        },
        eventType: CustomObjectEvents.RECORD_UPDATED,
        timestamp: Date.now(),
      },
    });

    return result;
  }

  async delete(recordId: string, user: CurrentUser) {
    const result = await this.customObjectRecordsRepository.update(
      { uid: recordId },
      { deletedAt: new Date() },
    );

    if (result.affected === 0) {
      throw new BadRequestException("Record not found or not deleted");
    }

    await this.publishSNS({
      eventType: CustomObjectEvents.RECORD_DELETED,
      user: user,
      reqId: rTracer.id(),
      messageGroupId: recordId,
      eventData: {
        payload: {
          record: {
            id: recordId,
          },
        },
        eventType: CustomObjectEvents.RECORD_DELETED,
        timestamp: Date.now(),
      },
    });
    return result;
  }

  async findAll(
    customObjectId: string,
    teamId: string,
    limit: number,
    page: number,
  ) {
    const records =
      await this.customObjectRecordsRepository.fetchPaginatedResults(
        { limit: Math.min(limit ?? 10, 100), page: page ?? 0 },
        {
          where: {
            customObjectId: customObjectId,
            teamId: teamId ?? IsNull(),
          },
          relations: GetObjectRecordRelations,
        },
      );

    return {
      ...records,
      page: (records.results.length > 0 ? ++page : undefined) ?? undefined,
    };
  }

  async findByIds(ids: string[]) {
    const records = await this.customObjectRecordsRepository.findAll({
      where: { uid: In(ids), deletedAt: IsNull() },
      relations: GetObjectRecordRelations,
    });

    return {
      items: records,
    };
  }

  /**
   * Creates custom field values for a ticket
   * @param customFieldValues The custom field values to create
   * @param organizationId The organization ID
   * @returns Array of created custom field values
   */
  private async createCustomFieldValues(
    customFieldValues: Array<{
      customFieldId: string;
      data: any;
      metadata?: Record<string, any>;
    }>,
    organizationId: string,
  ): Promise<CustomFieldValues[]> {
    const newCustomFieldValues: CustomFieldValues[] = [];

    for (const customFieldValue of customFieldValues) {
      const customFieldValuesDto = new CreateCustomFieldValuesDto();
      customFieldValuesDto.customFieldId = customFieldValue.customFieldId;
      customFieldValuesDto.data = customFieldValue.data;
      customFieldValuesDto.metadata = customFieldValue.metadata;
      customFieldValuesDto.organizationId = organizationId;

      const customField = await this.customFieldValuesService.create(
        organizationId,
        customFieldValuesDto,
      );
      newCustomFieldValues.push(customField);
    }

    return newCustomFieldValues;
  }

  private async publishSNS(event: any) {
    try {
      // Publish the record to SNS
      await this.snsPublishQueue.add(
        JobConstants.CUSTOM_OBJECT_SNS_PUBLISHER_JOB,
        event,
        {
          attempts: 3,
          backoff: {
            type: "exponential",
            delay: 1000, // 1 second
          },
        },
      );
    } catch (error) {
      this.logger.error(
        `Error publishing SNS event, event: ${JSON.stringify(
          event,
        )}, error: ${error}`,
      );
    }
  }
}
