import { BullModule } from "@nestjs/bullmq";
import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { SentryService } from "@repo/nestjs-commons/filters";
import { ILogger } from "@repo/nestjs-commons/logger";
import { SNSPublisherService } from "@repo/thena-eventbridge";
import {
  CustomObject,
  CustomObjectFields,
  CustomObjectFieldsRepository,
  CustomObjectRecords,
  CustomObjectRecordsRepository,
  CustomObjectRepository,
} from "@repo/thena-platform-entities";
import { CommonModule } from "../common/common.module";
import { ConfigModule } from "../config/config.module";
import { ConfigKeys, ConfigService } from "../config/config.service";
import { QueueNames } from "../constants/queue.constants";
import { CustomFieldModule } from "../custom-field/custom-field.module";
import { OrganizationModule } from "../organization/organization.module";
import { TeamsModule } from "../teams/teams.module";
import { CustomObjectFieldsController } from "./controllers/custom-object-fields.controller";
import { CustomObjectRecordsController } from "./controllers/custom-object-records.controller";
import { CustomObjectController } from "./controllers/custom-object.controller";
import { CustomObjectSetupService } from "./custom-object-setup";
import { CustomObjectSNSPublisher } from "./processors/sns-publisher.processor";
import { CustomObjectFieldsService } from "./services/custom-object-fields.service";
import { CustomObjectRecordsService } from "./services/custom-object-records.service";
import { CustomObjectService } from "./services/custom-object.service";
import { CustomObjectFieldsValidatorService } from "./validators/custom-object-fields.validator";
import { CustomObjectRecordValidatorService } from "./validators/custom-object-records.validator";
import { CustomObjectValidatorService } from "./validators/custom-object.validator";
@Module({
  imports: [
    CommonModule,
    TypeOrmModule.forFeature([
      CustomObject,
      CustomObjectFields,
      CustomObjectRepository,
      CustomObjectFieldsRepository,
      CustomObjectRecords,
      CustomObjectRecordsRepository,
    ]),
    OrganizationModule,
    TeamsModule,
    CustomFieldModule,
    BullModule.registerQueueAsync({
      name: QueueNames.CUSTOM_OBJECT_SNS_PUBLISHER,
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        redis: {
          host: configService.get(ConfigKeys.REDIS_HOST),
          port: Number(configService.get(ConfigKeys.REDIS_PORT)),
          password: configService.get(ConfigKeys.REDIS_PASSWORD),
          username: configService.get(ConfigKeys.REDIS_USERNAME),
        },
        defaultJobOptions: {
          removeOnComplete: {
            age: 24 * 3600, // keep completed jobs for 24 hours
            count: 1000, // keep last 1000 completed jobs
          },
          removeOnFail: {
            age: 24 * 3600, // keep up to 24 hours
          },
        },
      }),
    }),
  ],
  controllers: [
    CustomObjectController,
    CustomObjectFieldsController,
    CustomObjectRecordsController,
  ],
  providers: [
    // SNS publisher
    {
      provide: "CustomObjectSNSPublisherService",
      useFactory: (
        configService: ConfigService,
        sentryService: SentryService,
        loggerService: ILogger,
      ) => {
        return new SNSPublisherService(
          {
            topicArn: configService.get(
              ConfigKeys.AWS_SNS_CUSTOM_OBJECT_TOPIC_ARN,
            ),
            region: configService.get(ConfigKeys.AWS_REGION),
            credentials: {
              accessKeyId: configService.get(ConfigKeys.AWS_ACCESS_KEY),
              secretAccessKey: configService.get(ConfigKeys.AWS_SECRET_KEY),
            },
          },
          sentryService,
          loggerService,
        );
      },
      inject: [ConfigService, "Sentry", "CustomLogger"],
    },
    CustomObjectService,
    CustomObjectRepository,
    CustomObjectValidatorService,
    CustomObjectFieldsService,
    CustomObjectFieldsRepository,
    CustomObjectFieldsValidatorService,
    CustomObjectRecordsService,
    CustomObjectRecordsRepository,
    CustomObjectRecordValidatorService,
    CustomObjectSetupService,
    CustomObjectSNSPublisher,
  ],
  exports: [
    CustomObjectService,
    CustomObjectFieldsService,
    CustomObjectRecordsService,
  ],
})
export class CustomObjectModule {}
