import { BadRequestException, Injectable } from "@nestjs/common";
import { CustomObjectRepository } from "@repo/thena-platform-entities";
import { ILike, In, IsNull } from "typeorm";
import {
  CreateCustomObjectDto,
  UpdateCustomObjectMetaDTO,
} from "../dto/custom-object.dto";

@Injectable()
export class CustomObjectValidatorService {
  constructor(private customObjectRepository: CustomObjectRepository) {}

  async validateCreatePayload(
    orgId: string,
    createCustomObjectDto: CreateCustomObjectDto,
  ) {
    const customObjects = await this.customObjectRepository.findAll({
      where: {
        organizationId: orgId,
        name: ILike(createCustomObjectDto.name.toLocaleLowerCase()),
        deletedAt: IsNull(),
      },
    });

    if (createCustomObjectDto.teamId && customObjects.length > 0) {
      for (const customObject of customObjects) {
        if (customObject.teamId === createCustomObjectDto.teamId) {
          throw new BadRequestException(
            `Custom object with the name "${createCustomObjectDto.name}" already exists in the team.`,
          );
        }
      }
    }

    if (customObjects.length > 0) {
      throw new BadRequestException(
        `Custom object with the name "${createCustomObjectDto.name}" already exists in your organization.`,
      );
    }
  }

  async validateUpdatePayload(
    orgId: string,
    objects: UpdateCustomObjectMetaDTO[],
    teamIds: string[],
  ) {
    const names = (objects || [])
      .map((object) => object.updates?.name?.toLocaleLowerCase())
      .filter(Boolean);

    const namesSet = new Set(names);
    if (namesSet.size !== names.length) {
      throw new BadRequestException(
        "Duplicate custom object names are not allowed",
      );
    }

    const objectIds = (objects || []).map((object) => object.id);
    const objectIdsSet = new Set(objectIds);
    if (objectIdsSet.size !== objectIds.length) {
      throw new BadRequestException(
        "Duplicate custom object IDs found in the payload",
      );
    }

    const customObjects = await this.customObjectRepository.findAll({
      where: {
        organizationId: orgId,
        uid: In(objectIds),
        deletedAt: IsNull(),
      },
    });

    const foundCustomObjectIds = customObjects.map((obj) => obj.uid);

    if (foundCustomObjectIds.length !== objectIds.length) {
      const missingIds = objectIds.filter(
        (id) => !foundCustomObjectIds.includes(id),
      );

      throw new BadRequestException(
        `One or more custom objects not found in the database: ${missingIds.join(
          ", ",
        )}`,
      );
    }

    const modifiedObjects = [];
    for (const customObject of customObjects) {
      const existingObject = objects.find((obj) => obj.id === customObject.uid);
      if (customObject.teamId && !teamIds.includes(customObject.teamId)) {
        throw new BadRequestException(
          `You are trying to update object of a team that you are not a part of, objectId: ${customObject.uid}`,
        );
      }

      if (existingObject.version !== customObject.version) {
        modifiedObjects.push(customObject.uid);
      }
    }

    if (modifiedObjects.length > 0) {
      throw new BadRequestException(
        `Some objects have been modified, Please try updating the latest version. Modified objects: ${modifiedObjects.join(
          ", ",
        )}`,
      );
    }

    if (names.length > 0) {
      const objectsWithSameName = await this.customObjectRepository.findAll({
        where: {
          organizationId: orgId,
          name: ILike(In(names)),
          deletedAt: IsNull(),
        },
      });

      if (objectsWithSameName.length > 0) {
        const alreadyExists = objectsWithSameName.map((obj) => obj.name);
        throw new BadRequestException(
          `Custom object with the name "${alreadyExists.join(
            ", ",
          )}" already exists in your organization.`,
        );
      }
    }
  }
}
