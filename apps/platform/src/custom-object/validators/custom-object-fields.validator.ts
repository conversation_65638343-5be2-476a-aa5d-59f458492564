import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import {
  CustomFieldSource,
  CustomFieldType,
  CustomObjectFieldsRepository,
  CustomObjectRepository,
  ThenaLookupEntities,
} from "@repo/thena-platform-entities";
import { IsNull } from "typeorm";
import { CustomFieldService } from "../../custom-field/services/custom-field.service";
import {
  AddFieldDto,
  GetFieldsDto,
  RemoveFieldDto,
} from "../dto/custom-object-fields.dto";

@Injectable()
export class CustomObjectFieldsValidatorService {
  constructor(
    private customObjectFieldsRepository: CustomObjectFieldsRepository,
    private customObjectRepository: CustomObjectRepository,
    private customFieldService: CustomFieldService,
  ) {}

  async validateAddFieldPayload(
    orgId: string,
    customObjectId: string,
    fieldId: string,
    addFieldDto: AddFieldDto,
    teamIds: string[],
  ) {
    const customObject = await this.customObjectRepository.findByCondition({
      where: {
        uid: customObjectId,
        organizationId: orgId,
        deletedAt: IsNull(),
      },
    });

    if (!customObject) {
      throw new NotFoundException("Custom object not found!");
    }

    if (customObject.teamId && !teamIds.includes(customObject.teamId)) {
      throw new BadRequestException(
        "You are not authorized to add field to this custom object!",
      );
    }

    const fields = await this.customFieldService.findByIds(orgId, [fieldId]);
    const field = fields?.items?.[0];
    if (!field || field.source !== CustomFieldSource.CUSTOM_OBJECT) {
      throw new NotFoundException("Field not found!");
    }

    if (field.teamId && !customObject.teamId) {
      throw new BadRequestException(
        "You cannot add team specific field to the org level custom object!",
      );
    }

    if (
      field.teamId &&
      customObject.teamId &&
      field.teamId !== customObject.teamId
    ) {
      throw new BadRequestException(
        "Team of the field and custom object does not match!",
      );
    }

    if (field.fieldType === CustomFieldType.LOOKUP) {
      if (!addFieldDto.relationshipType) {
        throw new BadRequestException(
          "Relationship type is required for lookup field!",
        );
      }

      const thenaLookupEntities = Object.values(ThenaLookupEntities);
      if (!thenaLookupEntities.includes(field.lookup as ThenaLookupEntities)) {
        const lookupObject = await this.customObjectRepository.findByCondition({
          where: {
            uid: field.lookup,
            deletedAt: IsNull(),
          },
        });

        if (!lookupObject) {
          throw new NotFoundException("Wrong lookup object provided!");
        }

        if (lookupObject.teamId && !teamIds.includes(lookupObject.teamId)) {
          throw new BadRequestException(
            "You are not authorized to add field to this custom object whose lookup object is not in your team!",
          );
        }

        if (
          lookupObject.teamId &&
          lookupObject.teamId !== customObject.teamId
        ) {
          throw new BadRequestException(
            "Team of the lookup object and custom object does not match!",
          );
        }

        addFieldDto.childObjectId = lookupObject.id;
        addFieldDto.childEntityId = ThenaLookupEntities.CUSTOM_OBJECT;
        // addFieldDto.childObject = {
        //   id: lookupObject.id,
        // };
      } else {
        addFieldDto.childEntityId = field.lookup as ThenaLookupEntities;
      }
    }

    addFieldDto.parentObjectId = customObject.id;
    addFieldDto.fieldId = field.id;
  }

  async validateRemoveFieldPayload(
    orgId: string,
    customObjectId: string,
    fieldId: string,
    teamIds: string[],
    removeFieldDto: RemoveFieldDto,
  ) {
    const customObject = await this.customObjectRepository.findByCondition({
      where: {
        uid: customObjectId,
        deletedAt: IsNull(),
      },
    });

    if (!customObject) {
      throw new NotFoundException("Custom object not found!");
    }

    if (customObject.teamId && !teamIds.includes(customObject.teamId)) {
      throw new BadRequestException(
        "You are not authorized to remove this field!",
      );
    }

    const customField = await this.customFieldService.findByIds(orgId, [
      fieldId,
    ]);
    const field = customField?.items?.[0];
    if (!field || field.source !== CustomFieldSource.CUSTOM_OBJECT) {
      throw new NotFoundException("Field not found!");
    }

    const customObjectField =
      await this.customObjectFieldsRepository.findByCondition({
        where: {
          parentObjectId: customObject.id,
          fieldId: field.id,
          deletedAt: IsNull(),
        },
      });

    if (!customObjectField) {
      throw new NotFoundException("Custom object field not found!");
    }

    removeFieldDto.fieldId = customObjectField.fieldId;
    removeFieldDto.parentObjectId = customObjectField.parentObjectId;
  }

  async validateGetFieldsPayload(
    orgId: string,
    getFieldsDto: GetFieldsDto,
    teamIds: string[],
  ) {
    const customObject = await this.customObjectRepository.findByCondition({
      where: {
        uid: getFieldsDto.customObjectId,
        organizationId: orgId,
        deletedAt: IsNull(),
      },
    });

    if (!customObject) {
      throw new NotFoundException("Custom object not found!");
    }

    if (customObject.teamId && !teamIds.includes(customObject.teamId)) {
      throw new BadRequestException(
        "You do not have access to this custom object!",
      );
    }

    getFieldsDto.customObjectId = customObject.id;
  }
}
