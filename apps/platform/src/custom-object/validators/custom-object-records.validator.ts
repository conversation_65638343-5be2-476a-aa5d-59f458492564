import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import {
  CreateObjectRecordDto,
  GetAllRecordsDto,
  UpdateObjectRecordDto,
} from "../dto/custom-object-records.dto";

import {
  CustomObjectFieldRelationshipType,
  CustomObjectFields,
  CustomObjectFieldsRepository,
  CustomObjectRecordsRepository,
  CustomObjectRepository,
  Team,
} from "@repo/thena-platform-entities";
import { IsNull } from "typeorm";
import { ExternalCustomFieldValuesDto } from "../../custom-field/dto";
import { CustomFieldService } from "../../custom-field/services/custom-field.service";
import { CustomFieldvalidatorService } from "../../custom-field/validators/custom-field.validator";
import { GetObjectFieldsRelations } from "../constants";

@Injectable()
export class CustomObjectRecordValidatorService {
  constructor(
    private readonly customObjectRepository: CustomObjectRepository,
    private readonly customObjectRecordsRepository: CustomObjectRecordsRepository,
    private readonly customFieldService: CustomFieldService,
    private readonly customFieldValidatorService: CustomFieldvalidatorService,
    private readonly customObjectFieldsRepository: CustomObjectFieldsRepository,
  ) {}
  async validateCreateRecordPayload(
    orgId: string,
    customObjectId: string,
    createObjectRecordDto: CreateObjectRecordDto,
    team: Team,
    teamIds: string[],
  ) {
    const customObject = await this.customObjectRepository.findByCondition({
      where: {
        uid: customObjectId,
        organizationId: orgId,
        deletedAt: IsNull(),
      },
    });

    if (!customObject) {
      throw new NotFoundException("Custom object not found!");
    }

    if (customObject.teamId && !team) {
      throw new BadRequestException(
        "Custom object is associated with a team, so record must be added to a team!",
      );
    }

    if (customObject.teamId && customObject.teamId !== team.id) {
      throw new BadRequestException(
        "Custom object is not associated with the team!",
      );
    }

    if (team?.id && !teamIds.includes(team.id)) {
      throw new BadRequestException(
        "You are not authorized to add record to this custom object!",
      );
    }

    const customObjectFields = await this.customObjectFieldsRepository.findAll({
      where: {
        parentObjectId: customObject.id,
        deletedAt: IsNull(),
      },
      relations: GetObjectFieldsRelations,
    });

    this.validateCustomFields(
      createObjectRecordDto.customFieldValues,
      customObjectFields,
    );

    await this.validateCustomFieldValues(
      orgId,
      createObjectRecordDto.customFieldValues,
      customObjectFields,
    );

    createObjectRecordDto.customObjectId = customObject.id;
  }

  async validateDeleteRecordPayload(
    customObjectId: string,
    recordId: string,
    teamIds: string[],
  ) {
    if (!recordId) {
      throw new BadRequestException("Record ID is required!");
    }

    if (!customObjectId) {
      throw new BadRequestException("Custom object ID is required!");
    }

    const customObject = await this.customObjectRepository.findByCondition({
      where: { uid: customObjectId, deletedAt: IsNull() },
    });

    if (!customObject) {
      throw new NotFoundException("Custom object not found!");
    }

    const customObjectRecord =
      await this.customObjectRecordsRepository.findByCondition({
        where: { uid: recordId, deletedAt: IsNull() },
      });

    if (!customObjectRecord) {
      throw new NotFoundException("Record not found!");
    }

    if (
      customObjectRecord.teamId &&
      !teamIds.includes(customObjectRecord.teamId)
    ) {
      throw new BadRequestException(
        "You can not delete this record, because it is not associated with the team you are not part of!",
      );
    }

    // if (customObjectRecord.version !== version) {
    //   throw new BadRequestException(
    //     "Record has been updated, please try again",
    //   );
    // }
  }

  async validateGetAllRecordsPayload(
    orgId: string,
    getAllRecordsDto: GetAllRecordsDto,
    teamIds: string[],
  ) {
    if (getAllRecordsDto.teamId && !teamIds.includes(getAllRecordsDto.teamId)) {
      throw new BadRequestException(
        "You can not fetch records, because you are not part of the team!",
      );
    }

    const customObject = await this.customObjectRepository.findByCondition({
      where: {
        organizationId: orgId,
        uid: getAllRecordsDto.customObjectId,
        deletedAt: IsNull(),
      },
    });

    if (!customObject) {
      throw new NotFoundException("Custom object not found!");
    }

    getAllRecordsDto.customObjectId = customObject.id;
  }

  async validateUpdateRecordPayload(
    orgId: string,
    customObjectId: string,
    recordId: string,
    updateObjectRecordDto: UpdateObjectRecordDto,
    teamIds: string[],
  ) {
    if (!recordId) {
      throw new BadRequestException("Record ID is required!");
    }

    const customObject = await this.customObjectRepository.findByCondition({
      where: {
        organizationId: orgId,
        uid: customObjectId,
        deletedAt: IsNull(),
      },
    });

    if (!customObject) {
      throw new NotFoundException("Custom object not found!");
    }

    updateObjectRecordDto.customObjectId = customObject.id;

    const customObjectRecord =
      await this.customObjectRecordsRepository.findByCondition({
        where: { uid: recordId, deletedAt: IsNull() },
      });

    if (!customObjectRecord) {
      throw new NotFoundException("Record not found!");
    }

    if (
      customObjectRecord.teamId &&
      !teamIds.includes(customObjectRecord.teamId)
    ) {
      throw new BadRequestException(
        "You can not update this record, because you are not part of the team!",
      );
    }

    const customObjectFields = await this.customObjectFieldsRepository.findAll({
      where: {
        parentObjectId: customObject.id,
        deletedAt: IsNull(),
      },
      relations: GetObjectFieldsRelations,
    });

    this.validateCustomFields(
      updateObjectRecordDto.customFieldValues,
      customObjectFields,
    );

    await this.validateCustomFieldValues(
      orgId,
      updateObjectRecordDto.customFieldValues,
      customObjectFields,
    );

    updateObjectRecordDto.recordId = customObjectRecord.id;
  }

  async validateCustomFieldValues(
    orgId: string,
    customFieldValues: ExternalCustomFieldValuesDto[],
    customObjectFields: CustomObjectFields[],
  ) {
    const customFieldIds = customFieldValues.map(
      (value) => value.customFieldId,
    );

    const customFields = await this.customFieldService.findByIds(
      orgId,
      customFieldIds,
    );

    if (customFields.items.length !== customFieldValues.length) {
      throw new BadRequestException("Invalid custom field values!");
    }

    const fieldValues =
      this.customFieldValidatorService.buildFieldValuesStructure(
        customFieldValues,
      );

    for (const customObjectField of customObjectFields) {
      const fieldValue = fieldValues.get(customObjectField.field.uid);
      const needsSingleValue =
        customObjectField.relationshipType ===
        CustomObjectFieldRelationshipType.ONE_TO_ONE;
      await this.customFieldValidatorService.validateFieldValue(
        customObjectField.field,
        fieldValue,
        false,
        needsSingleValue,
      );
    }
  }

  validateCustomFields(
    customFieldValues: ExternalCustomFieldValuesDto[],
    customObjectFields: CustomObjectFields[],
  ) {
    const customFieldIdsInObject = customObjectFields.map(
      (field) => field.field.uid,
    );

    const invalidFields = customFieldValues.filter(
      (value) => !customFieldIdsInObject.includes(value.customFieldId),
    );

    if (invalidFields.length > 0) {
      throw new BadRequestException(
        `Some fields are not associated with the custom object!`,
      );
    }

    return true;
  }
}
