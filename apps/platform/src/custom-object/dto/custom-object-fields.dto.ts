import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
  CustomObjectFieldRelationshipType,
  CustomObjectFields,
  ThenaLookupEntities,
} from "@repo/thena-platform-entities";
import { IsEnum, IsOptional, IsString } from "class-validator";

export class AddFieldDto {
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: "The id of the field to add" })
  fieldId: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: "The id of the parent object to add" })
  parentObjectId: string;

  @IsString()
  @IsOptional()
  childObjectId: string;

  @IsEnum(ThenaLookupEntities)
  @IsOptional()
  childEntityId: ThenaLookupEntities;

  @IsEnum(CustomObjectFieldRelationshipType)
  @IsOptional()
  @ApiPropertyOptional({
    description: "The relationship type of the field to add",
  })
  relationshipType: CustomObjectFieldRelationshipType;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The default value of the field to add",
  })
  defaultValue: string;
}

export class RemoveFieldDto {
  @IsString()
  @IsOptional()
  fieldId: string;

  @IsString()
  @IsOptional()
  parentObjectId: string;
}

export class GetFieldsDto {
  @IsString()
  @IsOptional()
  customObjectId: string;
}

export class CustomObjectFieldResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  relationshipType: CustomObjectFieldRelationshipType;

  @ApiProperty()
  childObjectId: string;

  @ApiProperty()
  childEntityId: ThenaLookupEntities;

  @ApiProperty()
  defaultValue: string;

  @ApiProperty()
  createdOn: Date;

  @ApiProperty()
  updatedOn: Date;

  static fromEntity(entity: CustomObjectFields) {
    if (!entity) return null;
    return {
      id: entity.uid,
      relationshipType: entity.relationshipType ?? undefined,
      childObjectId: entity.childObjectId ?? undefined,
      childEntityId: entity.childEntityId ?? undefined,
      defaultValue: entity.defaultValue ?? undefined,
      createdOn: entity.createdOn,
      updatedOn: entity.updatedOn,
      field: {
        id: entity.field.uid,
        name: entity.field.name,
        description: entity.field.description ?? undefined,
      },
    };
  }
}

export class GetFieldsResponse {
  @ApiProperty({ type: [CustomObjectFieldResponseDto] })
  data: CustomObjectFieldResponseDto[];

  @ApiProperty()
  status: boolean;

  @ApiProperty()
  message: string;

  @ApiProperty()
  timestamp: Date;
}

export class AddFieldResponse {
  @ApiProperty()
  status: boolean;

  @ApiProperty()
  message: string;
}
