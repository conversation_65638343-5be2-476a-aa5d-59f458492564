import {
  <PERSON><PERSON>rray,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from "class-validator";

import { Type } from "class-transformer";

import { ApiProperty } from "@nestjs/swagger";
import { CustomObjectRecords } from "@repo/thena-platform-entities";
import { ExternalCustomFieldValuesDto } from "../../custom-field/dto/custom-field-values.dto";

export class CreateObjectRecordDto {
  @IsString()
  @IsOptional()
  @ApiProperty({ description: "The ID of the team" })
  teamId?: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ExternalCustomFieldValuesDto)
  @ApiProperty({
    description: "The custom field values",
    type: [ExternalCustomFieldValuesDto],
  })
  customFieldValues?: ExternalCustomFieldValuesDto[];

  @IsString()
  @IsOptional()
  customObjectId?: string;
}
export class UpdateObjectRecordDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ExternalCustomFieldValuesDto)
  @ApiProperty({
    description: "The custom field values",
    type: [ExternalCustomFieldValuesDto],
  })
  customFieldValues?: ExternalCustomFieldValuesDto[];

  @IsString()
  @IsOptional()
  customObjectId?: string;

  @IsString()
  @IsOptional()
  recordId?: string;
}
export class DeleteRecordDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: "The ID of the record" })
  id: string;

  @IsNumber()
  @IsNotEmpty()
  @ApiProperty({ description: "The version of the record" })
  version: number;
}

export class CustomObjectRecordResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  teamId: string;

  @ApiProperty()
  version: number;

  @ApiProperty()
  customFieldValues: ExternalCustomFieldValuesDto[];

  static fromEntity(entity: CustomObjectRecords) {
    if (!entity) return null;
    return {
      id: entity.uid,
      teamId: entity.team ? entity.team.uid : undefined,
      // version: entity.version,
      customFieldValues: entity?.customFieldValues?.map((cfv) => ({
        customFieldId: cfv.customField.uid,
        data: cfv.data,
        metadata: cfv.metadata ?? undefined,
      })),
    };
  }
}

export class GetAllRecordsDto {
  @IsString()
  teamId: string;

  @IsString()
  customObjectId: string;
}

export class GetAllObjectRecordsResponse {
  @ApiProperty({ type: [CustomObjectRecordResponseDto] })
  data: {
    items: CustomObjectRecordResponseDto[];
  };

  @ApiProperty()
  status: boolean;

  @ApiProperty()
  message: string;

  @ApiProperty()
  timestamp: Date;
}
