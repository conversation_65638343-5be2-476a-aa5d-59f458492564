import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { CustomObject } from "@repo/thena-platform-entities";
import { Type } from "class-transformer";
import {
  IsInt,
  IsOptional,
  IsString,
  <PERSON>Length,
  <PERSON>Length,
  ValidateNested,
} from "class-validator";

export class CreateCustomObjectDto {
  @IsString()
  @MinLength(1)
  @MaxLength(100)
  @ApiProperty({ description: "The name of the custom object" })
  name: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: "The description of the custom object" })
  description?: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: "The team id of the custom object" })
  teamId?: string;
}

export class CustomObjectUpdateData {
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: "The updated name of the custom object" })
  name?: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The updated description of the custom object",
  })
  description?: string;
}

export class UpdateCustomObjectMetaDTO {
  @ApiProperty()
  @IsString()
  @MinLength(1, { message: "id must be a non-empty string" })
  id: string;

  @ApiProperty()
  @IsInt({ message: "version must be an integer" })
  version: number;

  @ApiProperty()
  @ValidateNested({ each: true })
  @Type(() => CustomObjectUpdateData)
  updates: CustomObjectUpdateData;
}

export class UpdateCustomObjectDto {
  @ApiProperty({ type: [UpdateCustomObjectMetaDTO] })
  @ValidateNested()
  @Type(() => UpdateCustomObjectMetaDTO)
  objects: UpdateCustomObjectMetaDTO[];
}

export class CustomObjectResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  description: string;

  @ApiProperty()
  organizationId: string;

  @ApiProperty()
  teamId: string;

  static fromEntity(entity: CustomObject) {
    if (!entity) return null;
    return {
      id: entity.uid,
      name: entity.name,
      description: entity.description ? entity.description : undefined,
      organizationId: entity.organization?.uid,
      teamId: entity?.team?.uid ? entity.team.uid : undefined,
      version: entity.version,
    };
  }
}

export class GetAllCustomObjectsResponse {
  @ApiProperty({ type: [CustomObjectResponseDto] })
  data: CustomObjectResponseDto[];

  @ApiProperty()
  status: boolean;

  @ApiProperty()
  message: string;

  @ApiProperty()
  timestamp: Date;
}
