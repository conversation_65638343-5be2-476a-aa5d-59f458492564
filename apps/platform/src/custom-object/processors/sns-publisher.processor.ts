import { Processor, WorkerHost } from "@nestjs/bullmq";
import { Inject, Injectable } from "@nestjs/common";
import { ILogger } from "@repo/nestjs-commons/logger";
import { ContextUserType, SNSPublisherService } from "@repo/thena-eventbridge";
import { Job } from "bullmq";
import * as rTracer from "cls-rtracer";
import { v4 as uuidv4 } from "uuid";
import { ConfigKeys, ConfigService } from "../../config/config.service";
import { QueueNames } from "../../constants/queue.constants";

export interface CustomObjectSNSJob {
  eventType: string;
  eventData: unknown;
  user: { uid: string; userType: string; orgUid?: string };
  reqId?: string;
  messageGroupId?: string;
}

@Injectable()
@Processor(QueueNames.CUSTOM_OBJECT_SNS_PUBLISHER)
export class CustomObjectSNSPublisher extends WorkerHost {
  constructor(
    @Inject("CustomObjectSNSPublisherService")
    private readonly snsPublisherService: SNSPublisherService,
    @Inject("CustomLogger")
    private readonly logger: ILogger,
    private readonly configService: ConfigService,
  ) {
    super();
  }

  async process(job: Job<CustomObjectSNSJob>) {
    try {
      const { eventType, eventData, user, reqId, messageGroupId } = job.data;
      await rTracer.runWithId(async () => {
        this.logger.log(
          `BullMQ Queue: ${QueueNames.CUSTOM_OBJECT_SNS_PUBLISHER} - Processing SNS message for job ${job.id}`,
        );

        await this.snsPublisherService.publishSNSMessage({
          subject: eventType,
          message: JSON.stringify(eventData),
          topicArn: this.configService.get(
            ConfigKeys.AWS_SNS_CUSTOM_OBJECT_TOPIC_ARN,
          ),
          messageAttributes: {
            event_name: eventType,
            event_id:
              (reqId as unknown as { reqId: string })?.reqId ?? uuidv4(),
            event_timestamp: Math.floor(Date.now() / 1000).toString(),
            context_user_id: user.uid,
            context_user_type: user.userType as ContextUserType,
            context_organization_id: user.orgUid,
          },
          messageGroupId: messageGroupId ?? uuidv4(),
        });
      }, reqId);
      this.logger.log(
        `BullMQ Queue: ${QueueNames.CUSTOM_OBJECT_SNS_PUBLISHER} - Processed SNS message for job ${job.id}`,
      );
    } catch (error) {
      this.logger.error(
        `BullMQ Queue: ${QueueNames.CUSTOM_OBJECT_SNS_PUBLISHER} - Error processing SNS message for job ${job.id}: ${error}`,
      );

      // Determine if we should retry based on error type
      const maxAttempts = job.opts.attempts ?? 1;
      if (job.attemptsMade < maxAttempts - 1) {
        const retryError = new Error(
          `SNS publishing failed (attempt ${job.attemptsMade + 1}): ${
            error.message
          }`,
        );
        retryError.name = "RetryableError";
        throw retryError;
      }

      // On final attempt, mark as permanent failure
      throw error;
    }
  }
}
