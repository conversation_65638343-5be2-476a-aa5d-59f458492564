import {
  HttpStatus,
  UnprocessableEntityException,
  ValidationPipe,
} from "@nestjs/common";
import {
  FastifyAdapter,
  NestFastifyApplication,
} from "@nestjs/platform-fastify";
import { Test } from "@nestjs/testing";
import { t__loginIntoAuthService } from "@repo/thena-shared-libs";
import { AppModule } from "../../app.module";
import { injectWithOrgId } from "../../utils/test-utils";
import { CreateCustomObjectDto } from "../dto/custom-object.dto";

describe("Custom Objects", () => {
  let app: NestFastifyApplication;
  let userAuthToken: string;
  let userAuthTokenOfUser2: string;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = module.createNestApplication<NestFastifyApplication>(
      new FastifyAdapter(),
    );

    app.useGlobalPipes(
      new ValidationPipe({
        transform: true,
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        exceptionFactory: (errors) => {
          return new UnprocessableEntityException(
            errors.map((error) => ({
              property: error.property,
              constraints: error.constraints,
            })),
          );
        },
      }),
    );

    await app.init();
    await app.getHttpAdapter().getInstance().ready();

    // Log the admin user in
    userAuthToken = await t__loginIntoAuthService(
      global.testUser.email,
      global.testUser.password,
    );

    userAuthTokenOfUser2 = await t__loginIntoAuthService(
      global.testUser2.email,
      global.testUser2.password,
    );
  });

  afterAll(async () => {
    await app.close();
  });

  let totalObjects = 0;
  let teamSpecificObjects = 0;
  let createdCustomObject;
  let createdCustomObject2;
  let teamSpecificCustomObject;

  describe("POST /v1/custom-object", () => {
    it("should create a new custom object", async () => {
      const createCustomObjectDto: CreateCustomObjectDto = {
        name: "Test Custom Object",
      };

      const response = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/custom-object",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: createCustomObjectDto,
      });

      expect(response.statusCode).toBe(HttpStatus.CREATED);

      const result = response.json();
      expect(result.name).toBe(createCustomObjectDto.name);
      expect(result.id).toBeDefined();
      expect(result.id).toMatch(/^CO/);

      totalObjects += 1;
      createdCustomObject = result;
    });

    it("should not create a custom object with duplicate name", async () => {
      const duplicateObjectDto: CreateCustomObjectDto = {
        name: "Test Custom Object",
      };

      const response = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/custom-object",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: duplicateObjectDto,
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);

      const result = response.json();
      expect(result.message).toContain("already exists in your organization.");
    });

    it("should create a custom object for a team", async () => {
      const teamSpecificObjectDto: CreateCustomObjectDto = {
        name: "Team Custom Object",
        teamId: global.testTeam.uid,
      };

      const response = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/custom-object",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: teamSpecificObjectDto,
      });

      expect(response.statusCode).toBe(HttpStatus.CREATED);

      const result = response.json();
      expect(result.name).toBe(teamSpecificObjectDto.name);
      expect(result.id).toBeDefined();
      expect(result.id).toMatch(/^CO/);
      expect(result.version).toBe(1);

      teamSpecificObjects += 1;
      teamSpecificCustomObject = result;
    });

    it("should not create a custom object for a team with duplicate name", async () => {
      const duplicateObjectDto: CreateCustomObjectDto = {
        name: "Team Custom Object",
        teamId: global.testTeam.uid,
      };

      const response = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/custom-object",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: duplicateObjectDto,
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);

      const result = response.json();
      expect(result.message).toContain("already exists in the team.");
    });

    it("should create a custom object", async () => {
      const createCustomObjectDto: CreateCustomObjectDto = {
        name: "Test Custom Object 2",
      };

      const response = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/custom-object",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: createCustomObjectDto,
      });

      expect(response.statusCode).toBe(HttpStatus.CREATED);
      totalObjects += 1;
      createdCustomObject2 = response.json();
    });
  });

  describe("PATCH /v1/custom-object", () => {
    it("should throw an error for duplicate objects in payload", async () => {
      const response = await injectWithOrgId(app, {
        method: "PATCH",
        url: "/v1/custom-object",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          objects: [
            {
              id: createdCustomObject.id,
              version: 1,
              updates: {
                description: "Test object 2 description",
              },
            },
            {
              id: createdCustomObject.id,
              version: 1,
              updates: {
                description: "Test object 2 description",
              },
            },
          ],
        },
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toContain(
        "Duplicate custom object IDs found in the payload",
      );
    });

    it("should throw an error for duplicate names in payload", async () => {
      const response = await injectWithOrgId(app, {
        method: "PATCH",
        url: "/v1/custom-object",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          objects: [
            {
              id: createdCustomObject.id,
              version: 1,
              updates: {
                name: "updated name",
              },
            },
            {
              id: teamSpecificCustomObject.id,
              version: 1,
              updates: {
                name: "updated name",
              },
            },
          ],
        },
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toContain(
        "Duplicate custom object names are not allowed",
      );
    });

    it("should throw an error for invalid custom object ids", async () => {
      const response = await injectWithOrgId(app, {
        method: "PATCH",
        url: "/v1/custom-object",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          objects: [
            {
              id: "invalid-id",
              version: 1,
              updates: {
                name: "updated name",
              },
            },
          ],
        },
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toContain(
        "One or more custom objects not found in the database",
      );
    });

    it("should throw an error for invalid version", async () => {
      const response = await injectWithOrgId(app, {
        method: "PATCH",
        url: "/v1/custom-object",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          objects: [
            {
              id: createdCustomObject.id,
              version: 999,
              updates: {
                name: "updated name",
              },
            },
          ],
        },
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toContain(
        `Some objects have been modified, Please try updating the latest version. Modified objects: ${createdCustomObject.id}`,
      );
    });

    it("should throw an error if trying to update a custom object of a team that you are not a part of", async () => {
      const response = await injectWithOrgId(app, {
        method: "PATCH",
        url: "/v1/custom-object",
        headers: { Authorization: `Bearer ${userAuthTokenOfUser2}` },
        payload: {
          objects: [
            {
              id: teamSpecificCustomObject.id,
              version: 1,
              updates: {
                name: "updated name",
              },
            },
          ],
        },
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toContain(
        `You are trying to update object of a team that you are not a part of, objectId: ${teamSpecificCustomObject.id}`,
      );
    });

    it("should throw an error if name already exists", async () => {
      const response = await injectWithOrgId(app, {
        method: "PATCH",
        url: "/v1/custom-object",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          objects: [
            {
              id: createdCustomObject2.id,
              version: 1,
              updates: {
                name: createdCustomObject.name,
              },
            },
          ],
        },
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toContain(
        `Custom object with the name "${createdCustomObject.name}" already exists in your organization.`,
      );
    });

    it("should update a custom object", async () => {
      const response = await injectWithOrgId(app, {
        method: "PATCH",
        url: "/v1/custom-object",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          objects: [
            {
              id: createdCustomObject.id,
              version: 1,
              updates: {
                name: "updated object name",
              },
            },
          ],
        },
      });

      expect(response.statusCode).toBe(HttpStatus.OK);
      expect(response.json().data[0].name).toBe("updated object name");
    });
  });

  describe("GET /v1/custom-object/fetchByIds", () => {
    it("should fetch multiple custom objects by IDs", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/custom-object/fetchByIds?ids=${createdCustomObject.id}&ids=${createdCustomObject2.id}`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(response.statusCode).toBe(HttpStatus.OK);
      expect(response.json().data.length).toBe(2);
    });

    it("should throw an error if invalid ids are provided", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/custom-object/fetchByIds?ids=random-id`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toContain(
        "Some objects not found, either they are deleted or not found in your teams, missing objects: random-id",
      );
    });

    it("should throw an error if trying to fetch a custom object of a team that you are not a part of", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/custom-object/fetchByIds?ids=${teamSpecificCustomObject.id}`,
        headers: { Authorization: `Bearer ${userAuthTokenOfUser2}` },
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toContain(
        `Some objects not found, either they are deleted or not found in your teams, missing objects: ${teamSpecificCustomObject.id}`,
      );
    });
  });

  describe("GET /v1/custom-object/search", () => {
    it("should return custom objects matching the search term", async () => {
      const searchTerm = "Object";
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/custom-object/search?term=${searchTerm}`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(response.statusCode).toBe(HttpStatus.OK);
      expect(response.json().data.length).toBe(totalObjects);
    });

    it("should return custom objects matching the search term for a team", async () => {
      const searchTerm = "Team";
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/custom-object/search?term=${searchTerm}&teamId=${global.testTeam.uid}&onlyTeamObjects=true`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(response.statusCode).toBe(HttpStatus.OK);
      expect(response.json().data.length).toBe(teamSpecificObjects);
    });
  });

  describe("GET /v1/custom-object", () => {
    it("should fetch all custom objects", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/custom-object",
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(response.statusCode).toBe(HttpStatus.OK);
      expect(response.json().results.length).toBe(totalObjects);
    });
  });
});
