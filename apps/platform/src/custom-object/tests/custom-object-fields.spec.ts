import {
  HttpStatus,
  UnprocessableEntityException,
  ValidationPipe,
} from "@nestjs/common";
import {
  FastifyAdapter,
  NestFastifyApplication,
} from "@nestjs/platform-fastify";
import { Test } from "@nestjs/testing";
import { t__loginIntoAuthService } from "@repo/thena-shared-libs";
import { DataSource } from "typeorm";
import { AppModule } from "../../app.module";
import { injectWithOrgId } from "../../utils/test-utils";
import { AddFieldDto } from "../dto/custom-object-fields.dto";

describe("Custom Object Fields", () => {
  let app: NestFastifyApplication;
  let userAuthToken: string;
  let conn: DataSource;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = module.createNestApplication<NestFastifyApplication>(
      new FastifyAdapter(),
    );

    app.useGlobalPipes(
      new ValidationPipe({
        transform: true,
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        exceptionFactory: (errors) => {
          return new UnprocessableEntityException(
            errors.map((error) => ({
              property: error.property,
              constraints: error.constraints,
            })),
          );
        },
      }),
    );

    conn = app.get(DataSource);
    await app.init();
    await app.getHttpAdapter().getInstance().ready();

    await conn.query(`
			INSERT INTO "custom_field" ("name", "field_type", "source", "organization_id", uid) VALUES ('field-1', 'rich_text', 'custom_object', ${global.testUser.organization_id}, 'cf-1')`);
    await conn.query(`
			INSERT INTO "custom_field" ("name", "field_type", "source", "organization_id", uid) VALUES ('field-2', 'rich_text', 'custom_object', ${global.testUser.organization_id}, 'cf-2')`);
    await conn.query(`
				INSERT INTO "custom_field" ("name", "field_type", "source", "organization_id", uid, team_id) VALUES ('team-field-1', 'rich_text', 'custom_object', ${global.testUser.organization_id}, 'tf-1', ${global.testTeam.id})`);
    await conn.query(
      `INSERT INTO "custom_object" ("name", "organization_id", "uid", "version") VALUES ('custom-object-1', ${global.testUser.organization_id}, 'co-1', 1)`,
    );

    // Log the admin user in
    userAuthToken = await t__loginIntoAuthService(
      global.testUser.email,
      global.testUser.password,
    );
  });

  afterAll(async () => {
    await conn.query(`DELETE from "custom_object_fields"`);
    await conn.query(`DELETE FROM "custom_object" WHERE "uid" = 'co-1'`);
    await conn.query(`DELETE FROM "custom_field" WHERE "uid" = 'cf-1'`);
    await conn.query(`DELETE FROM "custom_field" WHERE "uid" = 'cf-2'`);
    await conn.query(`DELETE FROM "custom_field" WHERE "uid" = 'tf-1'`);
    await app.close();
  });

  describe("POST /v1/custom-object/co-1/fields", () => {
    it("should create a new custom object field", async () => {
      const addFieldDto: AddFieldDto = {} as AddFieldDto;

      const response = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/custom-object/co-1/fields/cf-1",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: addFieldDto,
      });

      expect(response.statusCode).toBe(HttpStatus.CREATED);
    });
  });

  describe("GET /v1/custom-object/co-1/fields", () => {
    it("should remove field from custom object", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/custom-object/co-1/fields",
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(response.statusCode).toBe(HttpStatus.OK);
      expect(response.json().data.length).toBe(1);
    });
  });

  describe("PATCH /v1/custom-object/co-1/fields", () => {
    it("should remove field from custom object", async () => {
      const response = await injectWithOrgId(app, {
        method: "DELETE",
        url: "/v1/custom-object/co-1/fields/cf-1",
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(response.statusCode).toBe(HttpStatus.OK);
    });
  });
});
