import {
  HttpStatus,
  UnprocessableEntityException,
  ValidationPipe,
} from "@nestjs/common";
import {
  FastifyAdapter,
  NestFastifyApplication,
} from "@nestjs/platform-fastify";
import { Test } from "@nestjs/testing";
import { t__loginIntoAuthService } from "@repo/thena-shared-libs";
import { AppModule } from "../../app.module";
import { injectWithOrgId } from "../../utils/test-utils";
import { CustomObjectSetupService } from "../custom-object-setup";
import { CreateObjectRecordDto } from "../dto/custom-object-records.dto";

describe("Custom Object Records", () => {
  let app: NestFastifyApplication;
  let userAuthToken: string;
  let customObject;
  let teamsCustomObject;
  let fieldNameToId: Record<string, string>;
  let userAuthTokenOfUser2: string;
  let createdRecord;
  let teamSpecificeRecord;
  let totalRecords = 0;
  let totalTeamSpecificRecords = 0;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = module.createNestApplication<NestFastifyApplication>(
      new FastifyAdapter(),
    );

    app.useGlobalPipes(
      new ValidationPipe({
        transform: true,
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        exceptionFactory: (errors) => {
          return new UnprocessableEntityException(
            errors.map((error) => ({
              property: error.property,
              constraints: error.constraints,
            })),
          );
        },
      }),
    );

    await app.init();
    await app.getHttpAdapter().getInstance().ready();

    // Log the admin user in
    userAuthToken = await t__loginIntoAuthService(
      global.testUser.email,
      global.testUser.password,
    );

    userAuthTokenOfUser2 = await t__loginIntoAuthService(
      global.testUser2.email,
      global.testUser2.password,
    );

    const { createdCustomObject, teamCustomObject, fieldNameToIdMap } =
      await app.get(CustomObjectSetupService).run();

    customObject = createdCustomObject;
    teamsCustomObject = teamCustomObject;
    fieldNameToId = fieldNameToIdMap;
  });

  afterAll(async () => {
    await app.close();
  });

  describe(`POST /v1/custom-object/:customObjectId/records`, () => {
    it("should create a new custom object record", async () => {
      const createRecordDto: CreateObjectRecordDto = {
        customFieldValues: [
          {
            customFieldId: fieldNameToId["Simple Text field"],
            data: [
              {
                value: "Test value",
              },
            ],
          },
        ],
      };

      const response = await injectWithOrgId(app, {
        method: "POST",
        url: `/v1/custom-object/${customObject.uid}/records`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: createRecordDto,
      });

      expect(response.statusCode).toBe(HttpStatus.CREATED);
      const result = response.json();
      createdRecord = result;
      expect(result).toHaveProperty("id");
      expect(result.customFieldValues[0].customFieldId).toBe(
        fieldNameToId["Simple Text field"],
      );
      expect(result.customFieldValues[0].data[0].value).toBe("Test value");
      totalRecords += 1;
    });

    it("should not create a new custom object record if the custom object is not found", async () => {
      const response = await injectWithOrgId(app, {
        method: "POST",
        url: `/v1/custom-object/123/records`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          customFieldValues: [],
        },
      });

      expect(response.statusCode).toBe(HttpStatus.NOT_FOUND);
    });

    it("should not create a new custom object record if the custom object is associated with the team and no team is provided", async () => {
      const response = await injectWithOrgId(app, {
        method: "POST",
        url: `/v1/custom-object/${teamsCustomObject.uid}/records`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          customFieldValues: [],
        },
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toBe(
        "Custom object is associated with a team, so record must be added to a team!",
      );
    });

    it("should not create a new custom object record if the custom object is associated with the different team than the one provided", async () => {
      const response = await injectWithOrgId(app, {
        method: "POST",
        url: `/v1/custom-object/${teamsCustomObject.uid}/records`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          teamId: global.testTeam.uid,
          customFieldValues: [],
        },
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toBe(
        "Custom object is not associated with the team!",
      );
    });

    it("should not create a new custom object record if the user is not authorized to add record to the custom object of a team", async () => {
      const response = await injectWithOrgId(app, {
        method: "POST",
        url: `/v1/custom-object/${teamsCustomObject.uid}/records`,
        headers: { Authorization: `Bearer ${userAuthTokenOfUser2}` },
        payload: {
          teamId: global.testTeam2.uid,
          customFieldValues: [],
        },
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toBe(
        "You are not authorized to add record to this custom object!",
      );
    });

    it("should not create a new custom object record if the custom field is not associated with the custom object", async () => {
      const response = await injectWithOrgId(app, {
        method: "POST",
        url: `/v1/custom-object/${teamsCustomObject.uid}/records`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          teamId: global.testTeam2.uid,
          customFieldValues: [
            {
              customFieldId: "123",
              data: [],
            },
          ],
        },
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toBe(
        "Some fields are not associated with the custom object!",
      );
    });

    it("should not create a new custom object record if the custom field value is not valid", async () => {
      const response = await injectWithOrgId(app, {
        method: "POST",
        url: `/v1/custom-object/${customObject.uid}/records`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          customFieldValues: [
            {
              customFieldId: fieldNameToId["Phone Number field"],
              data: [
                {
                  value: "abc",
                },
              ],
            },
          ],
        },
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toBe(
        "Field Phone Number field contains invalid phone number",
      );
    });

    it("should create a new custom object record if the custom object is associated with the team and the team is provided", async () => {
      const response = await injectWithOrgId(app, {
        method: "POST",
        url: `/v1/custom-object/${teamsCustomObject.uid}/records`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          teamId: global.testTeam2.uid,
          customFieldValues: [],
        },
      });

      expect(response.statusCode).toBe(HttpStatus.CREATED);
      teamSpecificeRecord = response.json();
      totalTeamSpecificRecords += 1;
    });
  });

  describe(`PATCH /v1/custom-object/:customObjectId/records/:recordId`, () => {
    it("should update a custom object record", async () => {
      const response = await injectWithOrgId(app, {
        method: "PATCH",
        url: `/v1/custom-object/${customObject.uid}/records/${createdRecord.id}`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          customFieldValues: [
            {
              customFieldId: fieldNameToId["Simple Text field"],
              data: [
                {
                  value: "Updated value",
                },
              ],
            },
          ],
        },
      });

      expect(response.statusCode).toBe(HttpStatus.OK);
      expect(response.json().customFieldValues[0].data[0].value).toBe(
        "Updated value",
      );
    });
    it("should not update a custom object record if the record id is not provided", async () => {
      const response = await injectWithOrgId(app, {
        method: "PATCH",
        url: `/v1/custom-object/${customObject.uid}/records`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          customFieldValues: [],
        },
      });

      expect(response.statusCode).toBe(HttpStatus.NOT_FOUND);
    });

    it("should not update a custom object record if the record is not found", async () => {
      const response = await injectWithOrgId(app, {
        method: "PATCH",
        url: `/v1/custom-object/${customObject.uid}/records/${teamSpecificeRecord.id}`,
        headers: { Authorization: `Bearer ${userAuthTokenOfUser2}` },
        payload: {
          customFieldValues: [],
        },
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toBe(
        "You can not update this record, because you are not part of the team!",
      );
    });

    it("should not update a new custom object record if the custom field is not associated with the custom object", async () => {
      const response = await injectWithOrgId(app, {
        method: "PATCH",
        url: `/v1/custom-object/${customObject.uid}/records/${teamSpecificeRecord.id}`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          customFieldValues: [
            {
              customFieldId: "123",
              data: [],
            },
          ],
        },
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toBe(
        "Some fields are not associated with the custom object!",
      );
    });

    it("should not update a new custom object record if the custom field value is not valid", async () => {
      const response = await injectWithOrgId(app, {
        method: "PATCH",
        url: `/v1/custom-object/${customObject.uid}/records/${createdRecord.id}`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          customFieldValues: [
            {
              customFieldId: fieldNameToId["Phone Number field"],
              data: [
                {
                  value: "abc",
                },
              ],
            },
          ],
        },
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toBe(
        "Field Phone Number field contains invalid phone number",
      );
    });
  });

  describe(`GET /v1/custom-object/:customObjectId/records`, () => {
    it("should get all the records for a custom object", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/custom-object/${customObject.uid}/records`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(response.statusCode).toBe(HttpStatus.OK);
      expect(response.json().results.length).toBe(totalRecords);
    });

    it("should get all the records for a custom object for a team", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/custom-object/${teamsCustomObject.uid}/records?teamId=${global.testTeam2.uid}`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(response.statusCode).toBe(HttpStatus.OK);
      expect(response.json().results.length).toBe(totalTeamSpecificRecords);
    });
  });

  describe(`GET /v1/custom-object/:customObjectId/records/fetchByIds`, () => {
    it("should get all the records for a custom object for a team", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/custom-object/${teamsCustomObject.uid}/records/fetchByIds?ids=${createdRecord.id}`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(response.statusCode).toBe(HttpStatus.OK);
      expect(response.json().data.length).toBe(1);
      expect(response.json().data[0].id).toBe(createdRecord.id);
    });

    it("should throw an error if the ids are not provided", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/custom-object/${teamsCustomObject.uid}/records/fetchByIds`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toBe("No IDs provided");
    });

    it("should throw an error if trying to fetch records for a custom object that is not associated with the team", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/custom-object/${teamsCustomObject.uid}/records/fetchByIds?ids=${teamSpecificeRecord.id}`,
        headers: { Authorization: `Bearer ${userAuthTokenOfUser2}` },
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toBe(
        "You can not fetch records for this custom object, because it is not associated with your team!",
      );
    });

    it("should throw an error for invalid ids", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/custom-object/${teamsCustomObject.uid}/records/fetchByIds?ids=123`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toBe(
        "Some records not either deleted or wrong IDs provided!",
      );
    });
  });

  describe(`DELETE /v1/custom-object/:customObjectId/records/:recordId`, () => {
    it("should delete a custom object record", async () => {
      const response = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/custom-object/${customObject.uid}/records/${createdRecord.id}`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      console.log("Sdfsf", response.json());
      expect(response.statusCode).toBe(HttpStatus.OK);
      expect(response.json().message).toBe("Record deleted successfully!");
    });

    it("should throw an error if the custom object is not found", async () => {
      const response = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/custom-object/123/records/123`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(response.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(response.json().message).toBe("Custom object not found!");
    });

    it("should throw an error if the record is not found", async () => {
      const response = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/custom-object/${customObject.uid}/records/123`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(response.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(response.json().message).toBe("Record not found!");
    });

    it("should throw an error if the user is not part of the team for which the record is associated", async () => {
      const response = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/custom-object/${teamsCustomObject.uid}/records/${teamSpecificeRecord.id}`,
        headers: { Authorization: `Bearer ${userAuthTokenOfUser2}` },
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toBe(
        "You can not delete this record, because it is not associated with the team you are not part of!",
      );
    });
  });
});
