// apps/platform/src/forms/form-setup.ts
import { Injectable, OnModuleInit } from "@nestjs/common";
import {
  CustomFieldSource,
  CustomFieldType,
} from "@repo/thena-platform-entities";
import { CreateCustomFieldDto } from "../custom-field/dto";
import { CustomFieldService } from "../custom-field/services/custom-field.service";
import { AddFieldDto } from "./dto/custom-object-fields.dto";
import { CreateCustomObjectDto } from "./dto/custom-object.dto";
import { CustomObjectFieldsService } from "./services/custom-object-fields.service";
import { CustomObjectService } from "./services/custom-object.service";

const phoneNoFieldBody: CreateCustomFieldDto = {
  name: "Phone Number field",
  fieldType: CustomFieldType.PHONE_NUMBER,
  source: CustomFieldSource.CUSTOM_OBJECT,
  placeholderText: "Enter your phone number",
  hintText: "Enter your phone number",
  mandatoryOnCreation: false,
  mandatoryOnClose: false,
  visibleToCustomer: false,
  editableByCustomer: false,
  autoAddToAllForms: false,
  defaultValue: null,
};

const singleChoiceFieldBody: CreateCustomFieldDto = {
  name: "Single Choice field",
  fieldType: CustomFieldType.SINGLE_CHOICE,
  source: CustomFieldSource.CUSTOM_OBJECT,
  options: [
    {
      value: "option-1",
      is_disabled: false,
      order: 1,
      id: "1",
      platformField: true,
    },
    {
      value: "option-2",
      is_disabled: false,
      order: 2,
      id: "2",
      platformField: true,
    },
    {
      value: "option-3",
      is_disabled: false,
      order: 3,
      id: "3",
      platformField: true,
    },
  ],
  placeholderText: "Select an option",
  hintText: "Select an option",
  mandatoryOnCreation: false,
  mandatoryOnClose: false,
  visibleToCustomer: false,
  editableByCustomer: false,
  autoAddToAllForms: false,
  defaultValue: null,
};

const multiChoiceFieldBody: CreateCustomFieldDto = {
  name: "Multi Choice field",
  fieldType: CustomFieldType.MULTI_CHOICE,
  source: CustomFieldSource.CUSTOM_OBJECT,
  options: [
    {
      value: "option-1",
      is_disabled: false,
      order: 1,
      id: "1",
      platformField: true,
    },
    {
      value: "option-2",
      is_disabled: false,
      order: 2,
      id: "2",
      platformField: true,
    },
    {
      value: "option-3",
      is_disabled: false,
      order: 3,
      id: "3",
      platformField: true,
    },
    {
      value: "option-4",
      is_disabled: false,
      order: 4,
      id: "4",
      platformField: true,
    },
  ],
  placeholderText: "Select multiple options",
  hintText: "Select multiple options",
  mandatoryOnCreation: false,
  mandatoryOnClose: false,
  visibleToCustomer: false,
  editableByCustomer: false,
  autoAddToAllForms: false,
  defaultValue: null,
};

const createdCustomFields = [];

const simpleTextFieldBody: CreateCustomFieldDto = {
  name: "Simple Text field",
  fieldType: CustomFieldType.SINGLE_LINE,
  source: CustomFieldSource.CUSTOM_OBJECT,
  placeholderText: "Enter your text",
  hintText: "Enter your text",
  mandatoryOnCreation: false,
  mandatoryOnClose: false,
  visibleToCustomer: false,
  editableByCustomer: false,
  autoAddToAllForms: false,
  defaultValue: null,
};

let phoneNoField;
let singleChoiceField;
let multiChoiceField;
let simpleTextField;

@Injectable()
export class CustomObjectSetupService implements OnModuleInit {
  constructor(
    private readonly customObjectService: CustomObjectService,
    private readonly customFieldService: CustomFieldService,
    private readonly customObjectFieldService: CustomObjectFieldsService,
  ) {}
  onModuleInit() {
    return;
  }

  async run() {
    await this.createRandomCustomFields(
      global?.testOrganization?.id,
      global?.testUser?.id,
    );
    const customObject = await this.createCustomObject(
      global?.testOrganization?.id,
      global?.testUser?.id,
    );
    return customObject;
  }

  private async createRandomCustomFields(orgId: string, userId: string) {
    if (!orgId || !userId) {
      return;
    }
    const customFields: CreateCustomFieldDto[] = [
      phoneNoFieldBody,
      singleChoiceFieldBody,
      multiChoiceFieldBody,
      simpleTextFieldBody,
    ];

    for (const field of customFields) {
      const createdField = await this.customFieldService.create(
        orgId,
        userId,
        field,
      );

      switch (field.name) {
        case "Phone Number field":
          phoneNoField = createdField;
          break;
        case "Single Choice field":
          singleChoiceField = createdField;
          break;
        case "Multi Choice field":
          multiChoiceField = createdField;
          break;
        case "Simple Text field":
          simpleTextField = createdField;
          break;
      }

      createdCustomFields.push(createdField);
    }
  }

  private async createCustomObject(orgId: string, userId: string) {
    if (!orgId || !userId) {
      return;
    }
    const customObjectDto: CreateCustomObjectDto = {
      name: "Custom Object",
    };
    const teamCustomObjectDto: CreateCustomObjectDto = {
      name: "Team Custom Object",
      teamId: global.testTeam2.id,
    };
    const createdCustomObject = await this.customObjectService.create(
      orgId,
      userId,
      customObjectDto,
    );
    const teamCustomObject = await this.customObjectService.create(
      orgId,
      userId,
      teamCustomObjectDto,
    );

    await this.customObjectFieldService.addField(userId, {
      fieldId: simpleTextField.id,
      parentObjectId: createdCustomObject.id,
    } as AddFieldDto);

    await this.customObjectFieldService.addField(userId, {
      fieldId: phoneNoField.id,
      parentObjectId: createdCustomObject.id,
    } as AddFieldDto);

    return {
      createdCustomObject,
      teamCustomObject,
      fieldNameToIdMap: {
        "Phone Number field": phoneNoField.uid,
        "Single Choice field": singleChoiceField.uid,
        "Multi Choice field": multiChoiceField.uid,
        "Simple Text field": simpleTextField.uid,
      },
    };
  }
}
