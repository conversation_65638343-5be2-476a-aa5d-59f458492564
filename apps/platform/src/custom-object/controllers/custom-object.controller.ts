import {
  BadRequestException,
  Body,
  Controller,
  DefaultValuePipe,
  Get,
  Inject,
  NotFoundException,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Req,
} from "@nestjs/common";
import { ApiBody, ApiOperation, ApiTags } from "@nestjs/swagger";
import {
  ApiCreateEndpoint,
  ApiGetEndpoint,
  ApiResponseMessage,
  ApiUpdateEndpoint,
} from "@repo/nestjs-commons/decorators";
import { RequiredTier } from "@repo/nestjs-commons/guards/organization-tier/index";
import { ILogger } from "@repo/nestjs-commons/logger";
import { CustomValidationError, OrganizationTier } from "@repo/thena-platform-entities";
import { FastifyRequest } from "fastify";
import { SkipAllThrottler } from "../../common/decorators/throttler.decorator";
import { TeamsService } from "../../teams/services/teams.service";
import {
  CreateCustomObjectDto,
  CustomObjectResponseDto,
  GetAllCustomObjectsResponse,
  UpdateCustomObjectDto,
} from "../dto/custom-object.dto";
import { CustomObjectService } from "../services/custom-object.service";
import { CustomObjectValidatorService } from "../validators/custom-object.validator";

@ApiTags("Custom objects")
@Controller("v1/custom-object")
@SkipAllThrottler()
export class CustomObjectController {
  constructor(
    @Inject("CustomLogger")
    private readonly logger: ILogger,

    private readonly customObjectService: CustomObjectService,
    private readonly teamsService: TeamsService,
    private readonly customObjectValidatorService: CustomObjectValidatorService,
  ) {}

  @Post()
  @RequiredTier(OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for enterprise tier organizations.",
  })
  @ApiBody({ type: CreateCustomObjectDto })
  @ApiResponseMessage("Custom object created successfully!")
  @ApiCreateEndpoint({
    summary: "Create a custom object",
    responseType: CustomObjectResponseDto,
  })
  async create(
    @Body() createCustomObjectDto: CreateCustomObjectDto,
    @Req() request: FastifyRequest,
  ) {
    try {
      if (createCustomObjectDto.teamId) {
        const team = await this.validateAndFetchTeam(
          createCustomObjectDto.teamId,
          request.user.orgId,
        );
        createCustomObjectDto.teamId = team?.id;

        await this.checkIfUserBelongsToTeam(
          request.user.sub,
          team.id,
          request.user.orgId,
        );
      }

      await this.customObjectValidatorService.validateCreatePayload(
        request.user.orgId,
        createCustomObjectDto,
      );

      const customObject = await this.customObjectService.create(
        request.user.orgId,
        request.user.sub,
        createCustomObjectDto,
      );

      const returnableCustomObject = await this.customObjectService.findByIds(
        request.user.orgId,
        [customObject.uid],
      );

      return CustomObjectResponseDto.fromEntity(returnableCustomObject.data[0]);
    } catch (err) {
      if (err instanceof CustomValidationError) {
        throw new BadRequestException(err.message);
      }
      this.logger.error(
        `Error while trying to create custom object: ${err.message}, stack: ${err.stack}`,
      );
      throw err;
    }
  }

  @Patch()
  @RequiredTier(OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for enterprise tier organizations.",
  })
  @ApiResponseMessage("Custom objects updated successfully!")
  @ApiUpdateEndpoint({
    summary: "Update custom objects",
    responseType: GetAllCustomObjectsResponse,
  })
  async update(
    @Body() updateCustomObjectDto: UpdateCustomObjectDto,
    @Req() request: FastifyRequest,
  ) {
    try {
      const teams = await this.teamsService.getTeamsByUser(request.user);
      const teamIds = teams.map((team) => team.teamId);

      await this.customObjectValidatorService.validateUpdatePayload(
        request.user.orgId,
        updateCustomObjectDto.objects,
        teamIds,
      );
      await this.customObjectService.update(
        request.user.sub,
        request.user.orgId,
        updateCustomObjectDto,
      );

      const customObjects = await this.customObjectService.findByIds(
        request.user.orgId,
        updateCustomObjectDto.objects.map((obj) => obj.id),
      );

      const responseItems = customObjects.data.map((obj) =>
        CustomObjectResponseDto.fromEntity(obj),
      );
      return { ...customObjects, data: responseItems };
    } catch (err) {
      this.logger.error(
        `Error while trying to update custom object: ${err.message}, stack: ${err.stack}`,
      );
      throw err;
    }
  }

  @Get("/fetchByIds")
  @RequiredTier(OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for enterprise tier organizations.",
  })
  @ApiResponseMessage("Custom objects fetched successfully!")
  @ApiGetEndpoint({
    summary: "Get custom objects by IDs",
    responseType: GetAllCustomObjectsResponse,
  })
  async findByIds(
    @Req() request: FastifyRequest,
    @Query("ids") ids: string | string[],
  ) {
    try {
      if (!ids) {
        throw new BadRequestException("No IDs provided");
      }

      const idArray = typeof ids === "string" ? [ids] : ids;

      if (idArray.length === 0) {
        throw new BadRequestException("No IDs provided");
      }
      const teams = await this.teamsService.getTeamsByUser(request.user);
      const teamIds = teams.map((team) => team.teamId);
      const customObjects =
        await this.customObjectService.findByIdsWithTeamCheck(
          request.user.orgId,
          idArray,
          teamIds,
        );
      const responseItems = customObjects.data.map((item) =>
        CustomObjectResponseDto.fromEntity(item),
      );
      return { ...customObjects, data: responseItems };
    } catch (err) {
      this.logger.error(
        `Error while trying to fetch custom objects by IDs: ${err.message}, stack: ${err.stack}`,
      );
      throw err;
    }
  }

  @Get("/search")
  @RequiredTier(OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for enterprise tier organizations.",
  })
  @ApiGetEndpoint({
    summary: "Search custom object using name",
    responseType: GetAllCustomObjectsResponse,
  })
  async search(
    @Req() request: FastifyRequest,
    @Query("term") term: string,
    @Query("teamId") teamId?: string,
    @Query("onlyTeamObjects") onlyTeamObjects?: boolean,
  ) {
    try {
      let team;
      if (teamId) {
        team = await this.validateAndFetchTeam(teamId, request.user.orgId);
        await this.checkIfUserBelongsToTeam(
          request.user.sub,
          team?.id,
          request.user.orgId,
        );
      }

      const customObjects = await this.customObjectService.search(
        request.user.orgId,
        term,
        team?.id,
        onlyTeamObjects,
      );

      const responseItems = customObjects.data.map((item) =>
        CustomObjectResponseDto.fromEntity(item),
      );
      return { ...customObjects, data: responseItems };
    } catch (err) {
      this.logger.error(
        `Error while trying to search custom objects: ${err.message}, stack: ${err.stack}`,
      );
      throw err;
    }
  }

  @Get()
  @RequiredTier(OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for enterprise tier organizations.",
  })
  @ApiResponseMessage("Custom objects fetched successfully!")
  @ApiGetEndpoint({
    summary: "Get all custom objects",
    responseType: GetAllCustomObjectsResponse,
  })
  async findAll(
    @Req() request: FastifyRequest,
    @Query("limit", new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query("page", new DefaultValuePipe(0), ParseIntPipe) page: number,
    @Query("teamId") teamId?: string,
    @Query("onlyTeamObjects") onlyTeamObjects?: boolean,
  ) {
    try {
      let team;
      if (teamId) {
        team = await this.validateAndFetchTeam(teamId, request.user.orgId);
        await this.checkIfUserBelongsToTeam(
          request.user.sub,
          team?.id,
          request.user.orgId,
        );
      }

      const customObjects =
        await this.customObjectService.fetchPaginatedResults(
          request.user.orgId,
          limit,
          page,
          team?.id,
          onlyTeamObjects,
        );

      const responseItems = (customObjects.results || []).map((item) =>
        CustomObjectResponseDto.fromEntity(item),
      );

      return { ...customObjects, results: responseItems };
    } catch (err) {
      this.logger.error(
        `Error while trying to fetch custom objects: ${err.message}, stack: ${err.stack}`,
      );
      throw err;
    }
  }

  /**
   * Validates and fetches a team by its ID.
   * @param teamId The ID of the team to fetch.
   * @returns The team.
   */
  private async validateAndFetchTeam(teamId: string, organizationId: string) {
    const team = await this.teamsService.findOneByTeamId(
      teamId,
      organizationId,
    );

    if (!team) {
      throw new NotFoundException("Team not found!");
    }

    return team;
  }

  private async checkIfUserBelongsToTeam(
    userId: string,
    teamId: string,
    organizationId: string,
  ) {
    const teamMember = await this.teamsService.userBelongsToTeam(
      userId,
      teamId,
      organizationId,
    );

    if (!teamMember) {
      throw new BadRequestException("User is not a member of this team!");
    }
  }
}
