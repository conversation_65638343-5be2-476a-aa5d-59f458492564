import {
  BadRequestException,
  Body,
  Controller,
  DefaultValuePipe,
  Delete,
  Get,
  Inject,
  NotFoundException,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Req,
} from "@nestjs/common";
import { ApiBody, ApiOperation, ApiParam, ApiTags } from "@nestjs/swagger";
import {
  ApiGetEndpoint,
  ApiResponseMessage,
} from "@repo/nestjs-commons/decorators";
import { RequiredTier } from "@repo/nestjs-commons/guards/organization-tier/index";
import { ILogger } from "@repo/nestjs-commons/logger";
import { OrganizationTier } from "@repo/thena-platform-entities";
import { FastifyRequest } from "fastify";
import { SkipAllThrottler } from "../../common/decorators/throttler.decorator";
import { CurrentUser } from "../../common/decorators/user.decorator";
import { TeamsService } from "../../teams/services/teams.service";
import {
  CreateObjectRecordDto,
  CustomObjectRecordResponseDto,
  GetAllObjectRecordsResponse,
  GetAllRecordsDto,
  UpdateObjectRecordDto,
} from "../dto/custom-object-records.dto";
import { CustomObjectRecordsService } from "../services/custom-object-records.service";
import { CustomObjectRecordValidatorService } from "../validators/custom-object-records.validator";

@ApiTags("Custom objects")
@Controller("v1/custom-object/:customObjectId/records")
@SkipAllThrottler()
export class CustomObjectRecordsController {
  constructor(
    @Inject("CustomLogger")
    private readonly logger: ILogger,
    private readonly customObjectRecordsService: CustomObjectRecordsService,
    private readonly teamsService: TeamsService,
    private readonly customObjectRecordValidator: CustomObjectRecordValidatorService,
  ) {}

  @Post()
  @ApiBody({ type: CreateObjectRecordDto })
  @RequiredTier(OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for enterprise tier organizations.",
  })
  async addRecord(
    @CurrentUser() user: CurrentUser,
    @Param("customObjectId") customObjectId: string,
    @Body() createObjectRecordDto: CreateObjectRecordDto,
    @Req() request: FastifyRequest,
  ) {
    try {
      let team;
      if (createObjectRecordDto.teamId) {
        team = await this.validateAndFetchTeam(
          createObjectRecordDto.teamId,
          request.user.orgId,
        );
      }

      const teams = await this.teamsService.getTeamsByUser(request.user);
      const teamIds = teams.map((team) => team.teamId);

      await this.customObjectRecordValidator.validateCreateRecordPayload(
        request.user.orgId,
        customObjectId,
        createObjectRecordDto,
        team,
        teamIds,
      );

      const record = await this.customObjectRecordsService.create(
        request.user.orgId,
        user,
        createObjectRecordDto,
        team,
      );
      return CustomObjectRecordResponseDto.fromEntity(record);
    } catch (err) {
      this.logger.error(
        `Error while trying to add record: ${err.message}, stack: ${err.stack}`,
      );
      throw err;
    }
  }

  @Patch("/:id")
  @RequiredTier(OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for enterprise tier organizations.",
  })
  @ApiBody({ type: UpdateObjectRecordDto })
  async updateRecord(
    @CurrentUser() user: CurrentUser,
    @Body() updateObjectRecordDto: UpdateObjectRecordDto,
    @Param("customObjectId") customObjectId: string,
    @Param("id") recordId: string,
    @Req() request: FastifyRequest,
  ) {
    try {
      const teams = await this.teamsService.getTeamsByUser(request.user);
      const teamIds = teams.map((team) => team.teamId);

      await this.customObjectRecordValidator.validateUpdateRecordPayload(
        request.user.orgId,
        customObjectId,
        recordId,
        updateObjectRecordDto,
        teamIds,
      );
      const updatedRecord = await this.customObjectRecordsService.update(
        updateObjectRecordDto,
        user,
        request.user.orgId,
      );
      return CustomObjectRecordResponseDto.fromEntity(updatedRecord);
    } catch (err) {
      this.logger.error(
        `Error while trying to update record: ${err.message}, stack: ${err.stack}`,
      );
      throw err;
    }
  }

  @Delete("/:id")
  @RequiredTier(OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for enterprise tier organizations.",
  })
  async deleteRecord(
    @CurrentUser() user: CurrentUser,
    @Param("customObjectId") customObjectId: string,
    @Param("id") recordId: string,
    @Req() request: FastifyRequest,
  ) {
    try {
      const teams = await this.teamsService.getTeamsByUser(request.user);
      const teamIds = teams.map((team) => team.teamId);

      await this.customObjectRecordValidator.validateDeleteRecordPayload(
        customObjectId,
        recordId,
        teamIds,
      );
      await this.customObjectRecordsService.delete(recordId, user);
      return { message: "Record deleted successfully!" };
    } catch (err) {
      this.logger.error(
        `Error while trying to delete record: ${err.message}, stack: ${err.stack}`,
      );
      throw err;
    }
  }

  @Get()
  @RequiredTier(OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for enterprise tier organizations.",
  })
  async getAllRecords(
    @Param("customObjectId") customObjectId: string,
    @Query("teamId") teamId: string,
    @Query("limit", new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query("page", new DefaultValuePipe(0), ParseIntPipe) page: number,
    @Req() request: FastifyRequest,
  ) {
    try {
      const teams = await this.teamsService.getTeamsByUser(request.user);
      const teamIds = teams.map((team) => team.teamId);

      let team;
      if (teamId) {
        team = await this.validateAndFetchTeam(teamId, request.user.orgId);
      }

      const getAllRecordsDto = new GetAllRecordsDto();
      getAllRecordsDto.customObjectId = customObjectId;
      getAllRecordsDto.teamId = team?.id;

      await this.customObjectRecordValidator.validateGetAllRecordsPayload(
        request.user.orgId,
        getAllRecordsDto,
        teamIds,
      );

      const records = await this.customObjectRecordsService.findAll(
        getAllRecordsDto.customObjectId,
        getAllRecordsDto.teamId,
        limit,
        page,
      );

      const responseItems = (records.results || []).map((item) =>
        CustomObjectRecordResponseDto.fromEntity(item),
      );
      return { ...records, results: responseItems };
    } catch (err) {
      this.logger.error(
        `Error while trying to fetch all records: ${err.message}, stack: ${err.stack}`,
      );
      throw err;
    }
  }

  @Post("/fetchByIds")
  @RequiredTier(OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for enterprise tier organizations.",
  })
  @ApiParam({ name: "customObjectId", required: true, type: String })
  @ApiResponseMessage("Records fetched successfully!")
  @ApiGetEndpoint({
    summary: "Get records by IDs",
    responseType: GetAllObjectRecordsResponse,
  })
  async findByIds(
    @Req() request: FastifyRequest,
    @Body("ids") ids: string | string[],
  ) {
    try {
      if (!ids) {
        throw new BadRequestException("No IDs provided");
      }

      const idArray = typeof ids === "string" ? [ids] : ids;

      if (idArray.length === 0) {
        throw new BadRequestException("No IDs provided");
      }
      const teams = await this.teamsService.getTeamsByUser(request.user);
      const teamIds = teams.map((team) => team.teamId);

      const records = await this.customObjectRecordsService.findByIds(idArray);

      records.items.forEach((record) => {
        if (record.teamId && !teamIds.includes(record.teamId)) {
          throw new BadRequestException(
            "You can not fetch records for this custom object, because it is not associated with your team!",
          );
        }
      });

      if (records.items.length !== idArray.length) {
        throw new BadRequestException(
          "Some records not either deleted or wrong IDs provided!",
        );
      }

      return {
        data: (records.items || []).map(
          CustomObjectRecordResponseDto.fromEntity,
        ),
        status: true,
        message: "Records fetched successfully!",
        timestamp: new Date(),
      };
    } catch (err) {
      this.logger.error(
        `Error while trying to fetch records by IDs: ${err.message}, stack: ${err.stack}`,
      );
      throw err;
    }
  }

  /**
   * Validates and fetches a team by its ID.
   * @param teamId The ID of the team to fetch.
   * @returns The team.
   */
  private async validateAndFetchTeam(teamId: string, organizationId: string) {
    if (!teamId) {
      throw new BadRequestException("Team ID is required!");
    }
    const team = await this.teamsService.findOneByTeamId(
      teamId,
      organizationId,
    );

    if (!team) {
      throw new NotFoundException("Team not found!");
    }

    return team;
  }
}
