import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Inject,
  NotFoundException,
  Param,
  Post,
  Req,
} from "@nestjs/common";
import { ApiExcludeEndpoint, ApiOperation, ApiTags } from "@nestjs/swagger";
import { ApiResponseMessage } from "@repo/nestjs-commons/decorators/response-decorators/index";
import {
  ApiCreateEndpoint,
  ApiGetEndpoint,
} from "@repo/nestjs-commons/decorators/swagger-api.decorator";
import { RequiredTier } from "@repo/nestjs-commons/guards/organization-tier/index";
import { ILogger } from "@repo/nestjs-commons/logger";
import { CustomValidationError, OrganizationTier } from "@repo/thena-platform-entities";
import { FastifyRequest } from "fastify";
import { SkipAllThrottler } from "../../common/decorators/throttler.decorator";
import { TeamsService } from "../../teams/services/teams.service";
import {
  AddFieldDto,
  AddFieldResponse,
  CustomObjectFieldResponseDto,
  GetFieldsDto,
  GetFieldsResponse,
  RemoveFieldDto,
} from "../dto/custom-object-fields.dto";
import { CustomObjectFieldsService } from "../services/custom-object-fields.service";
import { CustomObjectFieldsValidatorService } from "../validators/custom-object-fields.validator";

@ApiTags("Custom objects")
@Controller("v1/custom-object/:customObjectId")
@SkipAllThrottler()
export class CustomObjectFieldsController {
  constructor(
    @Inject("CustomLogger")
    private readonly logger: ILogger,
    private readonly customObjectFieldsService: CustomObjectFieldsService,
    private readonly teamsService: TeamsService,
    private readonly customObjectFieldsValidatorService: CustomObjectFieldsValidatorService,
  ) {}

  @RequiredTier(OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for enterprise tier organizations.",
  })
  @Get("/fields")
  @ApiResponseMessage("Fields fetched successfully!")
  @ApiGetEndpoint({
    summary: "Get fields for a custom object",
    responseType: GetFieldsResponse,
  })
  async getFields(
    @Param("customObjectId") customObjectId: string,
    @Req() request: FastifyRequest,
  ) {
    try {
      const teams = await this.teamsService.getTeamsByUser(request.user);
      const teamIds = teams.map((team) => team.teamId);

      const getFieldsDto = new GetFieldsDto();
      getFieldsDto.customObjectId = customObjectId;

      await this.customObjectFieldsValidatorService.validateGetFieldsPayload(
        request.user.orgId,
        getFieldsDto,
        teamIds,
      );

      const fields = await this.customObjectFieldsService.getFields(
        getFieldsDto.customObjectId,
      );

      const response = new GetFieldsResponse();
      response.data = fields.map((field) =>
        CustomObjectFieldResponseDto.fromEntity(field),
      );

      return response;
    } catch (error) {
      this.logger.error(
        `Error while getting fields for custom object, error: ${error.message}, stack: ${error.stack}`,
      );
      throw error;
    }
  }

  @RequiredTier(OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for enterprise tier organizations.",
  })
  @Post("/fields/:fieldId")
  @ApiResponseMessage("Field added successfully!")
  @ApiCreateEndpoint({
    summary: "Add a field to a custom object",
    responseType: AddFieldResponse,
  })
  async addField(
    @Param("customObjectId") customObjectId: string,
    @Param("fieldId") fieldId: string,
    @Body() addFieldDto: AddFieldDto,
    @Req() request: FastifyRequest,
  ) {
    try {
      const teams = await this.teamsService.getTeamsByUser(request.user);
      const teamIds = teams.map((team) => team.teamId);

      await this.customObjectFieldsValidatorService.validateAddFieldPayload(
        request.user.orgId,
        customObjectId,
        fieldId,
        addFieldDto,
        teamIds,
      );

      await this.customObjectFieldsService.addField(
        request.user.sub,
        addFieldDto,
      );
      return { status: true, message: "Field added successfully!" };
    } catch (error) {
      if (error instanceof CustomValidationError) {
        throw new BadRequestException(error.message);
      }
      this.logger.error(
        `Error while adding field to custom object, error: ${error.message}, stack: ${error.stack}`,
      );
      throw error;
    }
  }

  @RequiredTier(OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for enterprise tier organizations.",
  })
  @Delete("/fields/:fieldId")
  @ApiExcludeEndpoint() // Excluding because in OpenAPI, BODY is not allowed in Delete endpoints; to include, we need to change the logic to not use Body
  async removeField(
    @Param("customObjectId") customObjectId: string,
    @Param("fieldId") fieldId: string,
    @Body() removeFieldDto: RemoveFieldDto,
    @Req() request: FastifyRequest,
  ) {
    try {
      const teams = await this.teamsService.getTeamsByUser(request.user);
      const teamIds = teams.map((team) => team.teamId);

      await this.customObjectFieldsValidatorService.validateRemoveFieldPayload(
        request.user.orgId,
        customObjectId,
        fieldId,
        teamIds,
        removeFieldDto,
      );

      await this.customObjectFieldsService.removeField(
        request.user.sub,
        removeFieldDto,
      );
    } catch (error) {
      this.logger.error(
        `Error while removing field from custom object, error: ${error.message}, stack: ${error.stack}`,
      );
      throw error;
    }
  }

  /**
   * Validates and fetches a team by its ID.
   * @param teamId The ID of the team to fetch.
   * @returns The team.
   */
  private async validateAndFetchTeam(teamId: string, organizationId: string) {
    const team = await this.teamsService.findOneByTeamId(
      teamId,
      organizationId,
    );

    if (!team) {
      throw new NotFoundException("Team not found!");
    }

    return team;
  }

  private async checkIfUserBelongsToTeam(
    userId: string,
    teamId: string,
    organizationId: string,
  ) {
    const teamMember = await this.teamsService.userBelongsToTeam(
      userId,
      teamId,
      organizationId,
    );

    if (!teamMember) {
      throw new BadRequestException("User is not a member of this team!");
    }
  }
}
