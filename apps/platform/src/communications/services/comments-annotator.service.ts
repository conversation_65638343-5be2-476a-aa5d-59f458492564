import { Injectable, NotFoundException } from "@nestjs/common";
import { InjectDataSource } from "@nestjs/typeorm";
import { communication } from "@repo/shared-proto";
import { Comment, CommentRepository } from "@repo/thena-platform-entities";
import "reflect-metadata";
import { DataSource } from "typeorm";
import { FieldMetadataService } from "../../common/services/field-metadata.service";

@Injectable()
export class CommentsAnnotatorService {
  constructor(
    @InjectDataSource()
    private readonly dataSource: DataSource,
    private readonly commentRepository: CommentRepository,
    private readonly fieldMetadataService: FieldMetadataService,
  ) {}

  /**
   * Get field metadata for comments
   */
  getCommentFieldMetadata(): communication.GetCommentFieldMetadataResponse {
    return this.fieldMetadataService.getFieldMetadata(Comment);
  }

  /**
   * Get comment data with relations
   */
  async getCommentData(
    commentId: string,
    relations: string[],
    organizationId: string,
  ): Promise<communication.GetCommentDataResponse> {
    const comment = await this.commentRepository.findByCondition({
      where: { uid: commentId, organizationId },
      relations,
    });

    if (!comment) {
      throw new NotFoundException("Comment not found");
    }

    return {
      data: JSON.stringify(comment),
    };
  }
}
