import { InjectQueue } from "@nestjs/bullmq";
import {
  BadRequestException,
  ConflictException,
  Inject,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import { ILogger } from "@repo/nestjs-commons/logger";
import {
  Comment,
  CommentEntityTypes,
  CommentMetadata,
  CommentRepository,
  CustomerContact,
  EmojiActionFlow,
  EmojiActionMappingsRepository,
  Emojis,
  EmojisRepository,
  ReactionsRepository,
  TicketStatus,
  TransactionService,
  User,
  UserType,
} from "@repo/thena-platform-entities";
import { Queue } from "bullmq";
import * as rTracer from "cls-rtracer";
import { isArray, mergeWith } from "lodash";
import * as emoji from "node-emoji";
import { DataSource, IsNull, QueryFailedError } from "typeorm";
import { CustomerContactActionService } from "../../accounts/services/customer-contact.action.service";
import { POSTGRES_ERROR_CODES } from "../../common/constants/postgres-errors.constants";
import { CurrentUser } from "../../common/decorators";
import { extractNameFromEmail } from "../../common/utils/extract-email-details";
import { QueueNames } from "../../constants/queue.constants";
import { OrganizationService } from "../../organization/services/organization.service";
import { statusesEnum } from "../../shared/constants";
import { TicketStatusActionService } from "../../tickets/services/ticket-status.action.service";
import { UsersService } from "../../users/services/users.service";
import { REACTIONS_RELATIONS } from "../constants/comments.constants";
import { emojiMappings } from "../constants/emoji-mappings";
import { AddReactionDto } from "../dto/reactions.dto";
import { CommentOp } from "../utils";
import { CommentsActionService } from "./comments.action.service";
import { TicketToClosedHandler } from "./emoji-actions-flows/ticket-status/ticket-to-closed.handler";
import { TicketToInProgressHandler } from "./emoji-actions-flows/ticket-status/ticket-to-in-progress.handler";
import { TicketToOnHoldHandler } from "./emoji-actions-flows/ticket-status/ticket-to-on-hold.handler";
@Injectable()
export class ReactionsActionService {
  constructor(
    private readonly transactionService: TransactionService,

    @Inject("CustomLogger") private readonly logger: ILogger,
    private readonly reactionsRepository: ReactionsRepository,
    private readonly emojisRepository: EmojisRepository,
    private readonly commentsActionService: CommentsActionService,
    private readonly commentRepository: CommentRepository,
    private readonly emojiActionMappingsRepository: EmojiActionMappingsRepository,
    private readonly organizationService: OrganizationService,
    private readonly customerContactActionService: CustomerContactActionService,
    private readonly ticketStatusService: TicketStatusActionService,

    @InjectQueue(QueueNames.REACTION_SNS_PUBLISHER)
    private readonly snsPublishQueue: Queue,

    // Actions
    private readonly ticketToClosedHandler: TicketToClosedHandler,
    private readonly ticketToInProgressHandler: TicketToInProgressHandler,
    private readonly ticketToOnHoldHandler: TicketToOnHoldHandler,
    private readonly usersService: UsersService,

    private readonly datasource: DataSource,
  ) {}

  async findAndExecuteEmojiActionInverse(
    comment: Comment,
    currentUser: CurrentUser,
    status: TicketStatus,
  ) {
    try {
      // Get the emoji action
      const mappedActions = await this.emojiActionMappingsRepository.findAll({
        where: {
          team: { id: comment.teamId },
          flow: EmojiActionFlow.OUTBOUND,
          organization: { id: comment.organizationId },
        },
      });

      // If no mapped action is found, return
      if (!mappedActions.length) {
        this.logger.debug(`No mapped actions found for comment: ${comment.id}`);
        return;
      }

      const options = { comment, currentUser };

      let parentStatus = status;

      // Check is the status is parentStatus or childStatus
      if (status.parentStatusId) {
        parentStatus = await this.ticketStatusService.findTicketParentStatus(
          status.parentStatusId,
          comment.teamId,
          comment.organizationId,
        );
      }

      // Extracting status name for comparision
      const ticketParentStatusName = parentStatus.name as string;

      //Extracting parent statuses from statuses

      if (ticketParentStatusName == statusesEnum.IN_PROGRESS) {
        await this.ticketToInProgressHandler.executeActionReverse(options);
      } else if (ticketParentStatusName == statusesEnum.CLOSED) {
        await this.ticketToClosedHandler.executeActionReverse(options);
      } else if (ticketParentStatusName == statusesEnum.ON_HOLD) {
        await this.ticketToOnHoldHandler.executeActionReverse(options);
      }
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Failed to find and execute emoji action: ${comment.id}`,
          error.stack,
        );
      } else {
        console.error(
          `Failed to find and execute emoji action: ${comment.id}`,
          error,
        );
      }
    }
  }

  async findAndExecuteEmojiAction(
    emoji: string,
    comment: Comment,
    currentUser: CurrentUser,
  ) {
    try {
      // Get the emoji action
      const mappedAction =
        await this.emojiActionMappingsRepository.findByCondition({
          where: {
            emoji,
            team: { id: comment.teamId },
            flow: EmojiActionFlow.INBOUND,
            organization: { id: comment.organizationId },
          },
        });

      // If no mapped action is found, return
      if (!mappedAction) {
        this.logger.debug(
          `No mapped action found for emoji: ${emoji} on comment: ${comment.id}`,
        );
        return;
      }

      const options = { comment, currentUser };

      // Execute the emoji action
      if (mappedAction.action === this.ticketToClosedHandler.getActionName()) {
        await this.ticketToClosedHandler.executeAction(options);
      } else if (
        mappedAction.action === this.ticketToInProgressHandler.getActionName()
      ) {
        await this.ticketToInProgressHandler.executeAction(options);
      } else if (
        mappedAction.action === this.ticketToOnHoldHandler.getActionName()
      ) {
        await this.ticketToOnHoldHandler.executeAction(options);
      }
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Failed to find and execute emoji action: ${comment.id}`,
          error.stack,
        );
      } else {
        console.error(
          `Failed to find and execute emoji action: ${comment.id}`,
          error,
        );
      }
    }
  }

  /**
   * ! In general emojis are light records and sending them all together [is/should] not [be] a problem
   *
   * List all emojis
   * @returns All emojis
   */
  async listAllEmojis(
    currentUser: CurrentUser,
  ): Promise<Array<Omit<Emojis, "organization">>> {
    let slackEmojis: Record<string, string | null>[] = [];
    try {
      slackEmojis = await this.datasource.query(
        `SELECT
          external_data.slack_emojis.*
        FROM external_data.slack_emojis
          INNER JOIN external_data.organizations ON external_data.organizations.id = external_data.slack_emojis.organization_id
          WHERE external_data.organizations.uid = $1;`,
        [currentUser.orgUid],
      );
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Failed to fetch slack emojis: ${error.message}`,
          error.stack,
        );
      } else {
        console.error("Failed to fetch slack emojis", error);
      }

      slackEmojis = [];
    }

    // Fetch all global emojis
    const emojis = await this.emojisRepository.findAll({
      where: [
        // Fetch all global emojis
        { organization: IsNull() },

        // Fetch all organization emojis
        { organization: { id: currentUser.orgId } },
      ],
    });

    // Format the slack emojis
    const formattedSlackEmojis: Array<Omit<Emojis, "organization">> =
      slackEmojis.map((emoji) => ({
        id: emoji.id,
        name: emoji.name,
        url: emoji.url,
        unicode: emoji.unicode,
        aliases: "",
        keywords: [],
        createdAt: emoji.created_at,
        updatedAt: emoji.updated_at,
      }));

    return [...formattedSlackEmojis, ...emojis];
  }

  /**
   * Get an emoji by name
   * @param name The name of the emoji
   * @param orgId The ID of the organization
   * @returns The emoji and whether it is a global emoji
   */
  async getEmojiByName(name: string, orgId: string) {
    // not using keywords we have in db as we have same strings in multiple emojis.
    let normalizedName = name;
    if (emojiMappings[name]) {
      normalizedName = emojiMappings[name];
    }

    let reaction = await this.emojisRepository.findByCondition({
      where: [
        { name: normalizedName, organization: IsNull() },
        { organization: { id: orgId }, name: normalizedName },
      ],
    });

    // If the emoji is not found then we'll create one using the `node-emoji` library
    if (!reaction) {
      // Find the emoji data, this will return a `key` and `emoji`
      // { emoji: '🦄', key: 'unicorn' } <-- Example data
      const emojiData = emoji.find(name);
      if (emojiData) {
        reaction = await this.createEmoji(name, emojiData.emoji);
      }
    }

    return { reaction, isGlobalEmoji: !reaction?.organization };
  }

  /**
   * Create an emoji
   * @param name The name of the emoji
   * @param unicode The unicode of the emoji
   * @returns The created emoji
   */
  async createEmoji(name: string, unicode: string) {
    return await this.transactionService.runInTransaction((txnContext) => {
      return this.emojisRepository.saveWithTxn(txnContext, {
        name,
        unicode,
      });
    });
  }

  /**
   * Get the comment and emoji
   * @param commentId The ID of the comment
   * @param currentUser The current user
   * @param emojiName The name of the emoji
   * @returns The comment, emoji, and reaction count key
   */
  async getCommentAndEmoji(
    commentId: string,
    currentUser: CurrentUser,
    emojiName: string,
  ) {
    const orgId = currentUser.orgId;

    // Get the comment and emoji
    const [comment, emojiResult] = await Promise.all([
      this.commentsActionService.getPopulatedCommentById(commentId, orgId),
      this.getEmojiByName(emojiName, orgId),
    ]);

    // If the comment is not found, throw an error
    if (!comment) throw new NotFoundException("Comment not found!");

    // If the emoji is not found, throw an error
    const { reaction } = emojiResult;
    if (!reaction) throw new NotFoundException("Emoji not found!");

    return { comment, reaction };
  }

  /**
   * Adds a reaction to a comment
   * @param commentId The ID of the comment
   * @param addReactionDto The DTO containing the emoji name
   * @param currentUser The current user
   */
  async addReaction(
    commentId: string,
    addReactionDto: AddReactionDto,
    currentUser: CurrentUser,
    workflowContext?: Record<string, any>,
  ): Promise<boolean> {
    const orgId = currentUser.orgId;
    let userType: "CUSTOMER" | "USER";
    let author: User;
    let customerContact: CustomerContact;

    // Create the reaction with metadata for impersonated user if provided
    const reactionMetadata: Record<string, unknown> = Object.assign(
      {},
      addReactionDto.metadata,
    );

    // Handle impersonated user if email is provided
    if (addReactionDto.impersonatedUserEmail) {
      const { firstName, lastName } = extractNameFromEmail(
        addReactionDto.impersonatedUserEmail,
      );

      this.logger.log(
        `Creating customer contact for impersonated user ${addReactionDto.impersonatedUserEmail}`,
      );

      // Get the user type for the impersonated user
      userType = await this.organizationService.getUserType(
        addReactionDto.impersonatedUserEmail,
        currentUser.orgId,
      );

      // If the user type is customer, create a customer contact
      if (userType === "CUSTOMER") {
        customerContact =
          await this.customerContactActionService.createCustomerContact(
            currentUser,
            {
              email: addReactionDto.impersonatedUserEmail,
              firstName,
              lastName,
            },
            { returnIfExists: true },
          );
      } else {
        author = await this.usersService.findOneByEmail(
          addReactionDto.impersonatedUserEmail,
          currentUser.orgId,
        );
      }

      // Store impersonation details in metadata
      reactionMetadata.impersonatedUserEmail =
        addReactionDto.impersonatedUserEmail;

      // Use provided name or fall back to contact's first name
      let impersonatedUserName = addReactionDto.impersonatedUserName;
      if (!impersonatedUserName && customerContact?.firstName) {
        this.logger.log(
          `Using customer contact's first_name as fallback for impersonatedUserName: ${customerContact.firstName}`,
        );
        impersonatedUserName = customerContact.firstName;
      }

      reactionMetadata.impersonatedUserName = impersonatedUserName;
      reactionMetadata.impersonatedUserAvatar =
        addReactionDto.impersonatedUserAvatar;

      // Add customer contact ID if available
      if (customerContact) {
        reactionMetadata.customerId = customerContact.id;
      }
    } else if (
      currentUser.userType === UserType.USER ||
      currentUser.userType === UserType.ORG_ADMIN
    ) {
      author = await this.usersService.findOneByEmail(
        currentUser.email,
        currentUser.orgId,
      );
    } else {
      throw new BadRequestException(
        "Need impersonated user email to react as a bot user!",
      );
    }

    // Get the comment and emojiusersService
    const { comment, reaction } = await this.getCommentAndEmoji(
      commentId,
      currentUser,
      addReactionDto.name,
    );

    // Perform the transaction for adding the reaction
    await this.transactionService.runInTransaction(async (txnContext) => {
      try {
        if (addReactionDto.impersonatedUserEmail) {
          reactionMetadata.impersonatedUserEmail =
            addReactionDto.impersonatedUserEmail;
          reactionMetadata.impersonatedUserName =
            addReactionDto.impersonatedUserName;
          reactionMetadata.impersonatedUserAvatar =
            addReactionDto.impersonatedUserAvatar;
        }

        // Create the reaction
        await this.reactionsRepository.createReaction(
          {
            emojiId: reaction.id,
            commentId: comment.id,
            organizationId: orgId,
            reactionByUserId: author ? author.id : currentUser.sub,
            reactionByCustomerContactId: customerContact
              ? customerContact.id
              : null,
            metadata:
              Object.keys(reactionMetadata).length > 0
                ? reactionMetadata
                : undefined,
          },
          txnContext,
        );

        // Update the users array
        const MAX_USERS_SHOWN = 5;

        // TODO: Here, we are not adding customer contacts to users array. We need one more metadta field for customer contacts
        const users = comment.metadata?.reactions?.[reaction.name]?.users || [];

        // Determine which user ID to use in the reaction list
        // If we're impersonating a non-customer user, use their UID instead
        const reactingUserUid = author
          ? author?.uid
          : currentUser.userType !== UserType.BOT_USER
          ? currentUser.uid
          : null;

        // Filter out the reacting user's ID from existing users to avoid duplicates
        const filteredUsers = users.filter(
          (userId) => userId !== reactingUserUid,
        );

        // If the users array is longer than the max, remove the last user
        if (users.length > MAX_USERS_SHOWN) {
          filteredUsers.pop();
        }

        // Always expose public user id
        if (reactingUserUid) {
          filteredUsers.unshift(reactingUserUid);
        }

        // Get the current count
        const currentCount =
          comment.metadata?.reactions?.[reaction.name]?.count || 0;

        // Create the metadata
        let metadata: CommentMetadata | Record<string, any> = {
          ...comment.metadata,
          reactions: {
            ...comment.metadata?.reactions,
            [reaction.name]: {
              count: currentCount + 1,
              users: filteredUsers,
              ...(reactionMetadata.impersonatedUserEmail && {
                impersonatedUserEmail: reactionMetadata.impersonatedUserEmail,
                impersonatedUserName: reactionMetadata.impersonatedUserName,
                impersonatedUserAvatar: reactionMetadata.impersonatedUserAvatar,
                customerId: customerContact?.id,
              }),
            },
          },
        };

        if (workflowContext) {
          metadata = mergeWith(metadata, workflowContext, (a, b) => {
            if (isArray(a)) {
              return a.concat(b);
            }
          });
        }

        // Update the comment metadata
        const commentCriteria = { id: comment.id, organizationId: orgId };
        await this.commentRepository.updateWithTxn(
          txnContext,
          commentCriteria,
          { metadata },
        );
      } catch (error) {
        if (error instanceof QueryFailedError) {
          if (error.code === POSTGRES_ERROR_CODES.DUPLICATE_KEY_VALUE) {
            throw new ConflictException(
              "You have already reacted to this comment!",
            );
          }
        }

        this.logger.error(
          `Failed to add reaction to comment: ${commentId}`,
          error,
        );

        throw error;
      }
    });

    // Execute the emoji action
    this.findAndExecuteEmojiAction(reaction.name, comment, currentUser);

    // Add this to the Bull Queue
    await this.snsPublishQueue.add(
      QueueNames.REACTION_SNS_PUBLISHER,
      {
        commentId: comment.id,
        emojiId: reaction.id,
        orgId: currentUser.orgId,
        author: author ? author : currentUser,
        customerContact,
        eventType: CommentOp.REACTION_ADDED,
        entityType: CommentEntityTypes.TICKET,
        reqId: rTracer.id(),
        metadata: reactionMetadata,
      },
      {
        attempts: 3,
        backoff: { type: "exponential", delay: 1000 },
      },
    );

    return true;
  }

  /**
   * Removes a reaction from a comment
   * @param commentId The ID of the comment
   * @param addReactionDto The DTO containing the emoji name
   * @param currentUser The current user
   */
  async removeReaction(
    commentId: string,
    reactionName: string,
    currentUser: CurrentUser,
    workflowContext?: Record<string, any>,
    impersonatedUserEmail?: string,
  ): Promise<boolean> {
    const orgId = currentUser.orgId;
    let userType: "CUSTOMER" | "USER";
    let author: User;
    let customerContact: CustomerContact;
    // Get the comment and emoji
    const { comment, reaction } = await this.getCommentAndEmoji(
      commentId,
      currentUser,
      reactionName,
    );

    if (impersonatedUserEmail) {
      const { firstName, lastName } = extractNameFromEmail(
        impersonatedUserEmail,
      );

      // Get the user type for the impersonated user
      userType = await this.organizationService.getUserType(
        impersonatedUserEmail,
        currentUser.orgId,
      );

      // If the user type is customer, create a customer contact
      if (userType === "CUSTOMER") {
        customerContact =
          await this.customerContactActionService.createCustomerContact(
            currentUser,
            {
              email: impersonatedUserEmail,
              firstName,
              lastName,
            },
            { returnIfExists: true },
          );
      } else {
        author = await this.usersService.findOneByEmail(
          impersonatedUserEmail,
          currentUser.orgId,
        );
      }
    } else if (
      currentUser.userType === UserType.USER ||
      currentUser.userType === UserType.ORG_ADMIN
    ) {
      author = await this.usersService.findOneByEmail(
        currentUser.email,
        currentUser.orgId,
      );
    } else {
      throw new BadRequestException(
        "Need impersonated user email to remove reaction as a bot user!",
      );
    }

    let userReaction;

    await this.transactionService.runInTransaction(async (txnContext) => {
      try {
        // Delete the reaction
        userReaction = await this.reactionsRepository.findByCondition({
          where: {
            commentId: comment.id,
            emojiId: reaction.id,
            organizationId: orgId,
            reactionByUserId: author ? author.id : currentUser.sub,
            reactionByCustomerContactId: customerContact
              ? customerContact.id
              : null,
          },
          relations: REACTIONS_RELATIONS,
        });

        // If the user reaction is not found, throw an error
        if (!userReaction) {
          throw new NotFoundException("You have not reacted to this comment!");
        }

        await this.reactionsRepository.remove(userReaction);

        // Update the users array
        const users = comment.metadata?.reactions?.[reaction.name]?.users || [];

        const reactingUserUid = author
          ? author?.uid
          : currentUser.userType !== UserType.BOT_USER
          ? currentUser.uid
          : null;

        const updatedUsers = users.filter(
          (userId) => userId !== reactingUserUid,
        );

        const currentCount =
          comment.metadata?.reactions?.[reaction.name]?.count || 0;

        const newCount = currentCount - 1;

        // Create the metadata
        let metadata: CommentMetadata | Record<string, any> = {
          ...comment.metadata,
          reactions: {
            ...comment.metadata?.reactions,
          },
        };

        // If the count is greater than 0, update the count
        if (newCount > 0) {
          metadata.reactions[reaction.name] = {
            count: newCount,
            users: updatedUsers,
          };
        } else {
          delete metadata.reactions[reaction.name];
        }

        if (workflowContext) {
          metadata = mergeWith(metadata, workflowContext, (a, b) => {
            if (isArray(a)) {
              return a.concat(b);
            }
          });
        }

        // Update the comment metadata
        const commentCriteria = { id: comment.id, organizationId: orgId };
        await this.commentRepository.updateWithTxn(
          txnContext,
          commentCriteria,
          { metadata },
        );
      } catch (error) {
        this.logger.error(
          `Failed to remove reaction from comment: ${commentId}`,
          error,
        );

        throw error;
      }
    });

    // Add this to the Bull Queue
    await this.snsPublishQueue.add(
      QueueNames.REACTION_SNS_PUBLISHER,
      {
        commentId: comment.id,
        emojiId: reaction.id,
        previousReaction: userReaction,
        orgId: currentUser.orgId,
        author: author ? author : currentUser,
        customerContact,
        eventType: CommentOp.REACTION_REMOVED,
        entityType: CommentEntityTypes.TICKET,
        reqId: rTracer.id(),
      },
      {
        attempts: 3,
        backoff: { type: "exponential", delay: 1000 },
      },
    );

    return true;
  }
}
