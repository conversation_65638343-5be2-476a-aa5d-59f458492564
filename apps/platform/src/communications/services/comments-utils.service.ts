import { Injectable } from "@nestjs/common";
import {
  OrganizationDomainsRepository,
  UserType,
} from "@repo/thena-platform-entities";
import { J<PERSON><PERSON> } from "jsdom";
import { CurrentUser } from "../../common/decorators/user.decorator";
import { generateRandomPassword } from "../../common/utils/random-password-generator.utils";
import { UsersService } from "../../users/services/users.service";

type CreateUserRecord = {
  userId: string;
  email: string;
};

@Injectable()
export class CommentsUtilsService {
  constructor(
    private readonly usersService: UsersService,
    private readonly organizationDomainsRepository: OrganizationDomainsRepository,
  ) {}

  getMentionedUsersFromText(content: string): Array<CreateUserRecord> {
    // Regex for internal users: <@USERID|email>
    const internalMentionRegex = /<@([A-Z0-9]+)\|([^>]+)>/g;

    // Regex for external users: <^ext!:USERID|email>
    const externalMentionRegex = /<\^ext!:([A-Z0-9]+)\|([^>]+)>/g;

    const mentions: Array<CreateUserRecord> = [];

    // Extract internal mentions
    let internalMatch: RegExpExecArray | null;
    while ((internalMatch = internalMentionRegex.exec(content)) !== null) {
      const userId = internalMatch[1];
      const email = internalMatch[2];
      
      // Skip mentions with null values
      if (userId && userId !== 'null' && email && email !== 'null') {
        const mentionObj = { userId, email, isExternal: false };
        mentions.push(mentionObj);
      }
    }

    // Extract external mentions
    let externalMatch: RegExpExecArray | null;
    while ((externalMatch = externalMentionRegex.exec(content)) !== null) {
      const userId = externalMatch[1];
      const email = externalMatch[2];
      
      // Skip mentions with null values
      if (userId && userId !== 'null' && email && email !== 'null') {
        const mentionObj = { userId, email, isExternal: true };
        mentions.push(mentionObj);
      }
    }

    return mentions;
  }

  /**
   * Get the mentioned users in a comment, returns an array of objects with the user
   * id as the key and the email as the value
   *
   * @param content The content of the comment
   * @returns The mentioned users
   */
  getMentionedUsersFromHTML(content: string): Array<CreateUserRecord> {
    const mentions: Array<CreateUserRecord> = [];

    // Create a virtual DOM using jsdom
    const dom = new JSDOM(content);
    const document = dom.window.document;

    // Find all mention spans, including both those with and without data-type attribute
    // to handle both new and existing formats
    const mentionSpans = document.querySelectorAll(
      'span.mention'
    );

    // Iterate over the mention spans
    mentionSpans.forEach((span) => {
      const userId = span.getAttribute("data-id");
      const email = span.getAttribute("data-email");

      // Skip mentions with null values
      if (userId && userId !== 'null' && email && email !== 'null') {
        mentions.push({
          userId,
          email,
        });
      }
    });

    return mentions;
  }

  /**
   * Creates multiple users from a list of provided user ids and emails
   *
   * @param createUserRecords The list of user ids and emails to create
   * @param currentUser The current user
   * @returns The created users
   */
  async createUsers(
    createUserRecords: Array<CreateUserRecord>,
    currentUser: CurrentUser,
  ) {
    const userCreationPromises = [];

    // Get all the domains for the organization
    const orgDomainsCols = await this.organizationDomainsRepository.findAll({
      where: { organization: { id: currentUser.orgId } },
    });

    // Get all the domains for the organization
    const domains = orgDomainsCols.map((col) => col.domain);

    for (const createUserRecord of createUserRecords) {
      // Create this user IF AND ONLY IF the domain is in the list of domains
      const isUser = domains.includes(createUserRecord.email.split("@")[1]);
      if (isUser) {
        userCreationPromises.push(
          this.usersService.create(
            {
              email: createUserRecord.email,
              name: createUserRecord.email.split("@")[0],
              organizationUid: currentUser.orgUid,
              userType: UserType.ORG_ADMIN,
              password: generateRandomPassword(16),
              externalId: createUserRecord.userId,
            },
            { user_id: currentUser.uid, org_id: currentUser.orgUid },
          ),
        );
      }
    }

    // Create the users, we'll use `allSettled` to avoid throwing an error if one of the users already exists
    const users = await Promise.allSettled(userCreationPromises);

    // Return the users
    return users;
  }
}
