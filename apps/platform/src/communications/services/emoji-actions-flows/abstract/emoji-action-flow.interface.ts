export interface EmojiActionFlowExecutor {
  /**
   * Get the name of the action
   * @returns {string}
   */
  getActionName(): string;

  /**
   * Execute the action
   * @param {any} data - The data to execute the action with
   * @returns {Promise<void>}
   */
  executeAction(data: any): Promise<void>;

  /**
   * Execute the reverse action
   * @param {any} data - The data to execute the reverse action with
   * @returns {Promise<void>}
   */
  executeActionReverse(data: any): Promise<void>;
}
