import { InjectQueue } from "@nestjs/bullmq";
import {
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import { CUSTOM_LOGGER_TOKEN, ILogger } from "@repo/nestjs-commons/logger";
import {
  Comment,
  CommentEntityTypes,
  CommentMetadata,
  CommentRepository,
  CommentType,
  CommentVisibility,
  EmojiActionFlow,
  EmojiActionMappingsRepository,
  EmojisRepository,
  ReactionsRepository,
} from "@repo/thena-platform-entities";
import { Queue } from "bullmq";
import * as rTracer from "cls-rtracer";
import { IsNull } from "typeorm";
import { CurrentUser } from "../../../../common/decorators";
import { QueueNames } from "../../../../constants/queue.constants";
import { TicketStatusActionService } from "../../../../tickets/services/ticket-status.action.service";
import { TicketsService } from "../../../../tickets/services/tickets.service";
import { UsersService } from "../../../../users/services/users.service";
import { CommentOp } from "../../../utils";
import { EmojiActionFlowExecutor } from "../abstract";

const TICKET_TO_ON_HOLD_ACTION_NAME = "ticket-to-on-hold";

interface TicketToOnHoldAction {
  comment: Comment;
  currentUser: CurrentUser;
}

interface TicketToOnHoldActionReverse {
  comment: Comment;
  currentUser: CurrentUser;
}

@Injectable()
export class TicketToOnHoldHandler implements EmojiActionFlowExecutor {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // Database Repositories
    private readonly commentRepository: CommentRepository,
    private readonly emojiActionRepository: EmojiActionMappingsRepository,
    private readonly emojisRepository: EmojisRepository,
    private readonly reactionsRepository: ReactionsRepository,

    // Services
    @Inject(forwardRef(() => TicketsService))
    private readonly ticketService: TicketsService,
    private readonly ticketStatusService: TicketStatusActionService,
    private readonly usersService: UsersService,

    @InjectQueue(QueueNames.REACTION_SNS_PUBLISHER)
    private readonly snsPublishQueue: Queue,
  ) {}

  getActionName(): string {
    return TICKET_TO_ON_HOLD_ACTION_NAME;
  }

  private async getCommentTicketData(comment: Comment) {
    // Get the comment data
    const commentData = await this.commentRepository.findByCondition({
      where: { id: comment.id },
      relations: { ticket: { team: true }, organization: true },
    });

    // Get the ticket comments
    const ticketComments = await this.commentRepository.findAll({
      where: {
        commentType: CommentType.COMMENT, // Must be a comment
        commentVisibility: CommentVisibility.PUBLIC, // Must be a public comment
        ticket: { id: commentData.ticketId },
        organization: { id: comment.organizationId },
      },
      take: 1,
      order: { createdAt: "ASC" },
    });

    // Get the ticket data
    const ticketData = commentData.ticket;

    let shouldRun = true;

    // If there are no ticket comments, do not run the action
    if (ticketComments.length === 0) {
      shouldRun = false;
    }

    // If the first ticket comment is not the same as the comment, do not run the action
    if (ticketComments[0].id !== comment.id) {
      shouldRun = false;
    }

    return {
      shouldRun,
      ticketData,
      commentData,
    };
  }

  async executeAction(data: TicketToOnHoldAction): Promise<void> {
    const { comment, currentUser } = data || {};

    try {
      // Get the comment data
      const { ticketData } = await this.getCommentTicketData(comment);

      // Update the ticket status to on hold
      const statuses = await this.ticketStatusService.findAllTicketStatuses(
        { orgId: currentUser.orgId },
        ticketData.team.uid,
      );

      // Get the parent on hold status
      const onHoldStatus = statuses.find(
        (status) =>
          status.name.toLowerCase() === "on hold" && !status.parentStatusId,
      );

      // If the on hold status is not found, log a warning and return
      if (!onHoldStatus) {
        this.logger.warn(
          `No on hold status found for ticket ${ticketData.uid} in team ${ticketData.team.uid}`,
        );
        return;
      }

      // Update the ticket status to on hold
      await this.ticketService.updateTicket(currentUser, ticketData.uid, {
        statusId: onHoldStatus.uid,
      });
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error adding reaction to comment ${comment?.id}: ${error.message}`,
          error.stack,
        );
      } else {
        console.error(`Error adding reaction to comment ${comment?.id}`, error);
      }
    }
  }

  async executeActionReverse(data: TicketToOnHoldActionReverse): Promise<void> {
    const { comment, currentUser } = data || {};

    try {
      // Get the comment data
      const { ticketData, shouldRun } = await this.getCommentTicketData(
        comment,
      );
      if (!shouldRun) {
        this.logger.debug(
          `Skipping ticket to on hold action for comment ${comment.id}`,
        );
        return;
      }

      // Get the mapped emoji actions
      const emojiActions = await this.emojiActionRepository.findAll({
        where: {
          action: TICKET_TO_ON_HOLD_ACTION_NAME,
          flow: EmojiActionFlow.OUTBOUND,
          team: ticketData.team,
          organization: ticketData.organization,
        },
      });

      // If no emoji actions are found, log a warning and return
      if (emojiActions.length === 0) {
        this.logger.warn(
          `No emoji actions found for ticket ${ticketData.uid} in team ${ticketData.team.uid}`,
        );
        return;
      }

      // Keep track of the updated metadata across iterations
      let updatedMetadata: CommentMetadata = { ...comment.metadata };

      const author = await this.usersService.findOneByEmail(
        currentUser.email,
        currentUser.orgId,
      );

      // Execute the emoji actions
      for (const emojiAction of emojiActions) {
        const orgId = currentUser.orgId;

        // Get the comment and emoji
        const { reaction } = await this.getEmojiByName(
          emojiAction.emoji,
          orgId,
        );

        // If the emoji is not found, throw an error
        if (!reaction) throw new NotFoundException("Emoji not found!");

        // Add the reaction to the comment
        await this.reactionsRepository.createReaction({
          emojiId: reaction.id,
          commentId: comment.id,
          organizationId: currentUser.orgId,
          reactionByUserId: currentUser.sub,
        });

        await this.snsPublishQueue.add(
          QueueNames.REACTION_SNS_PUBLISHER,
          {
            commentId: comment.id,
            emojiId: reaction.id,
            orgId: currentUser.orgId,
            author: author ? author : currentUser,
            eventType: CommentOp.REACTION_ADDED,
            entityType: CommentEntityTypes.TICKET,
            reqId: rTracer.id(),
          },
          {
            attempts: 3,
            backoff: { type: "exponential", delay: 1000 },
          },
        );

        const MAX_USERS_SHOWN = 5;
        const users = updatedMetadata?.reactions?.[reaction.name]?.users || [];
        const filteredUsers = users.filter(
          (userId) => userId !== currentUser.uid,
        );

        // If the users array is longer than the max, remove the last user
        if (users.length > MAX_USERS_SHOWN) {
          filteredUsers.pop();
        }

        // Always expose public user id
        filteredUsers.unshift(currentUser.uid);

        // Get the current count from the updated metadata
        const currentCount =
          updatedMetadata?.reactions?.[reaction.name]?.count || 0;

        // Update the metadata
        updatedMetadata = {
          ...updatedMetadata,
          reactions: {
            ...updatedMetadata.reactions,
            [reaction.name]: {
              count: currentCount + 1,
              users: filteredUsers,
            },
          },
        };
      }

      // Update the comment metadata once after all reactions are processed
      const commentCriteria = {
        id: comment.id,
        organizationId: currentUser.orgId,
      };
      await this.commentRepository.update(commentCriteria, {
        metadata: updatedMetadata,
      });
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error adding reaction to comment ${comment?.id}: ${error.message}`,
          error.stack,
        );
      } else {
        console.error(`Error adding reaction to comment ${comment?.id}`, error);
      }
    }
  }

  async getEmojiByName(name: string, orgId: string) {
    const reaction = await this.emojisRepository.findByCondition({
      where: [
        { name, organization: IsNull() },
        { organization: { id: orgId }, name },
      ],
    });

    return { reaction, isGlobalEmoji: !reaction?.organization };
  }
}
