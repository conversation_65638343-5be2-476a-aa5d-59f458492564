import {
  BadRequestException,
  Inject,
  Injectable,
  OnModuleInit,
} from "@nestjs/common";
import { CUSTOM_LOGGER_TOKEN, ILogger } from "@repo/nestjs-commons/logger";
import {
  EmojiActionMappingsRepository,
  EmojiActions,
  IDX_UNIQ_EMOJI_ACTION_MAPPING,
  TeamRepository,
  TransactionService,
} from "@repo/thena-platform-entities";
import * as nodeEmoji from "node-emoji";
import { DeepPartial, In, QueryFailedError } from "typeorm";
import { CurrentUser } from "../../common/decorators";
import {
  MapEmojisActionDTO,
  UnmapEmojiActionDTO,
  UpdateEmojiActionDTO,
  UpdateEmojiActionIndividual,
} from "../dto/emoji-actions.dto";
import { EmojiActionRegistry } from "../registry/emoji-action.registry";
import { TicketToClosedHandler } from "./emoji-actions-flows/ticket-status/ticket-to-closed.handler";
import { TicketToInProgressHandler } from "./emoji-actions-flows/ticket-status/ticket-to-in-progress.handler";
import { TicketToOnHoldHandler } from "./emoji-actions-flows/ticket-status/ticket-to-on-hold.handler";

@Injectable()
export class EmojiActionsService implements OnModuleInit {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // Utility services
    private readonly transactionService: TransactionService,

    // Database Repositories
    private readonly emojiActionRepository: EmojiActionMappingsRepository,
    private readonly teamsRepository: TeamRepository,

    // Registry
    private readonly emojiActionsRegistry: EmojiActionRegistry,

    // Event handlers
    private readonly ticketToClosedHandler: TicketToClosedHandler,
    private readonly ticketToInProgressHandler: TicketToInProgressHandler,
    private readonly ticketToOnHoldHandler: TicketToOnHoldHandler,
  ) {}

  onModuleInit() {
    // Register ticket to resolved action
    this.emojiActionsRegistry.register(
      this.ticketToClosedHandler.getActionName(),
      this.ticketToClosedHandler,
    );

    // Register ticket to in progress action
    this.emojiActionsRegistry.register(
      this.ticketToInProgressHandler.getActionName(),
      this.ticketToInProgressHandler,
    );

    // Register ticket to on hold action
    this.emojiActionsRegistry.register(
      this.ticketToOnHoldHandler.getActionName(),
      this.ticketToOnHoldHandler,
    );
  }

  /**
   * Validates the emojis to ensure they are valid
   * @param emojis - The emojis to validate
   */
  private validateEmojis(emojis: Array<string>) {
    // Check if the emojis are valid
    for (const emoji of emojis) {
      const validEmoji = nodeEmoji.find(emoji);
      if (!validEmoji || validEmoji.key === undefined) {
        const msg = `Invalid emoji ${emoji}`;
        this.logger.error(msg);
        throw new BadRequestException(msg);
      }
    }

    return true;
  }

  getAllEmojiActions() {
    return this.emojiActionsRegistry.getAllActions();
  }

  getAllEmojiActionMappingsForTeam(teamId: string, currentUser: CurrentUser) {
    return this.emojiActionRepository.findAll({
      where: {
        team: { uid: teamId },
        organization: { id: currentUser.orgId },
      },
    });
  }

  /**
   * Get all the emoji actions mappings for a given bot context
   * @param botCtx - The bot context
   */
  getAllEmojiActionsMappings(currentUser: CurrentUser) {
    const { orgId } = currentUser;

    return this.emojiActionRepository.findAll({
      where: {
        organization: { id: orgId },
      },
    });
  }

  async deleteEmojiActionById(
    currentUser: CurrentUser,
    emojiActionId: string,
    teamId: string,
  ) {
    const teamCount = await this.teamsRepository.count({
      where: { uid: teamId, organization: { id: currentUser.orgId } },
    });

    // If the team does not exist, throw an error
    if (teamCount === 0) {
      throw new BadRequestException("Team not found!");
    }

    // Get the emoji action
    const emojiAction = await this.emojiActionRepository.findByCondition({
      where: {
        uid: emojiActionId,
        team: { uid: teamId },
        organization: { id: currentUser.orgId },
      },
    });

    // If the emoji action does not exist, throw an error
    if (!emojiAction) {
      throw new BadRequestException("Emoji action not found!");
    }

    // Delete the emoji action
    await this.emojiActionRepository.remove(emojiAction);
    return emojiAction;
  }

  async updateEmojiActionById(
    currentUser: CurrentUser,
    emojiActionId: string,
    teamId: string,
    data: UpdateEmojiActionIndividual,
  ) {
    const teamCount = await this.teamsRepository.count({
      where: { uid: teamId, organization: { id: currentUser.orgId } },
    });

    // If the team does not exist, throw an error
    if (teamCount === 0) {
      throw new BadRequestException("Team not found!");
    }

    // Get the emoji action
    const emojiAction = await this.emojiActionRepository.findByCondition({
      where: {
        uid: emojiActionId,
        team: { uid: teamId },
        organization: { id: currentUser.orgId },
      },
    });

    // If the emoji action does not exist, throw an error
    if (!emojiAction) {
      throw new BadRequestException("Emoji action not found!");
    }

    // Check if the possible action exists
    const possibleAction = await this.emojiActionRepository.findByCondition({
      where: {
        emoji: data.emoji,
        action: data.action,
        flow: emojiAction.flow,
        team: { uid: teamId },
        organization: { id: currentUser.orgId },
      },
    });

    // If the possible action exists, throw an error
    if (possibleAction) {
      throw new BadRequestException("Emoji action mapping already exists");
    }

    // Update the emoji action
    await this.emojiActionRepository.update(
      { uid: emojiAction.uid, organization: { id: currentUser.orgId } },
      { emoji: data.emoji, action: data.action },
    );

    // Get the updated emoji action
    const updatedAction = await this.emojiActionRepository.findByCondition({
      where: { uid: emojiAction.uid, organization: { id: currentUser.orgId } },
    });

    return updatedAction;
  }

  async updateEmojiAction(
    currentUser: CurrentUser,
    data: UpdateEmojiActionDTO,
    teamId: string,
  ) {
    try {
      const { emojis, action } = data;
      const { orgId } = currentUser;

      // Validate the emojis
      this.validateEmojis(emojis);

      // Get the action from the registry
      const emojiAction = this.emojiActionsRegistry.getAction(action);
      if (!emojiAction) {
        throw new BadRequestException(`Invalid action ${action}`);
      }

      // Get the platform team
      const team = await this.teamsRepository.findByCondition({
        where: {
          uid: teamId,
          organization: { id: orgId },
        },
      });

      // If the platform team is not found, throw an error
      if (!team) {
        throw new BadRequestException(`Invalid team ${teamId}`);
      }

      // Get the existing emoji action mappings
      const existingMappings = await this.emojiActionRepository.findAll({
        where: {
          emoji: In(emojis),
          team: { id: team.id },
          action: emojiAction.getActionName(),
          organization: { id: orgId },
        },
      });

      // Create the new mappings
      const newMappings: Array<DeepPartial<EmojiActions>> = emojis.map(
        (emoji) => {
          return {
            emoji,
            team: { id: team.id },
            action: emojiAction.getActionName(),
            organization: { id: orgId },
          };
        },
      );

      // Get the existing emoji action mappings
      const updatedMappings = await this.transactionService.runInTransaction(
        async (txnContext) => {
          // TODO: This can be optimized by constructing a bulk update query with difference sets
          // Remove the existing mappings
          await this.emojiActionRepository.removeManyWithTxn(
            txnContext,
            existingMappings,
          );

          // Create the new mappings
          const updatedMappings =
            await this.emojiActionRepository.saveManyWithTxn(
              txnContext,
              newMappings,
            );

          return updatedMappings;
        },
      );

      return updatedMappings;
    } catch (error) {
      if (error instanceof QueryFailedError) {
        // Check if the error is due to the unique constraint violation
        if (
          "constraint" in error &&
          error.constraint === IDX_UNIQ_EMOJI_ACTION_MAPPING
        ) {
          throw new BadRequestException("Emoji action mapping already exists");
        }
      }

      throw error;
    }
  }

  async unmapEmojiAction(
    currentUser: CurrentUser,
    data: UnmapEmojiActionDTO,
    teamId: string,
  ) {
    const { emoji, action, flow } = data;
    const { orgId } = currentUser;

    // Get the platform team
    const team = await this.teamsRepository.findByCondition({
      where: { uid: teamId, organization: { id: orgId } },
    });

    // If the platform team is not found, throw an error
    if (!team) {
      throw new BadRequestException(`Invalid team ID ${teamId}`);
    }

    // Get mapping for the emoji and action
    const mapping = await this.emojiActionRepository.findByCondition({
      where: {
        emoji,
        action,
        flow,
        team: { id: team.id },
        organization: { id: orgId },
      },
    });

    // If the mapping is not found, throw an error
    if (!mapping) {
      throw new BadRequestException("Emoji action mapping not found");
    }

    // Delete the mapping
    await this.emojiActionRepository.remove(mapping);
    return mapping;
  }
  /**
   * Map the emojis to the action
   * @param botCtx - The bot context
   * @param data - The data to map the emojis to the action
   */
  async mapEmojisToAction(
    currentUser: CurrentUser,
    data: MapEmojisActionDTO,
    teamId: string,
  ) {
    try {
      const { emojis, action, flow } = data;
      const { orgId } = currentUser;

      // Validate the emojis
      this.validateEmojis(emojis);

      // Get the action from the registry
      const emojiAction = this.emojiActionsRegistry.getAction(action);
      if (!emojiAction) {
        const msg = `Invalid action ${action}`;
        this.logger.debug(msg);
        throw new BadRequestException(msg);
      }

      // Get the platform team
      const team = await this.teamsRepository.findByCondition({
        where: {
          uid: teamId,
          organization: { id: orgId },
        },
      });

      // If the platform team is not found, throw an error
      if (!team) {
        const msg = `Invalid team ID ${teamId}`;
        this.logger.debug(msg);
        throw new BadRequestException(msg);
      }

      // If the team is a sub-team, throw an error
      if (team.parentTeamId) {
        this.logger.debug(
          `Team ${teamId} is a sub-team and cannot have emoji action mappings`,
        );
        throw new BadRequestException(
          "Please map emoji actions to the parent team!",
        );
      }

      // Create the emoji action mappings
      const emojiActionMappings: Array<DeepPartial<EmojiActions>> = emojis.map(
        (emoji) => {
          return {
            flow,
            emoji,
            team: { id: team.id },
            action: emojiAction.getActionName(),
            organization: { id: orgId },
          };
        },
      );

      // Map the action to the emoji(s)
      const result = await this.transactionService.runInTransaction(
        async (txnContext) => {
          const mapping = await this.emojiActionRepository.saveManyWithTxn(
            txnContext,
            emojiActionMappings,
          );

          return mapping;
        },
      );

      return result;
    } catch (error) {
      if (error instanceof QueryFailedError) {
        if (
          "constraint" in error &&
          error.constraint === IDX_UNIQ_EMOJI_ACTION_MAPPING
        ) {
          throw new BadRequestException("Emoji action mapping already exists");
        }
      }

      throw error;
    }
  }
}
