import { CommentEntityTypes } from "@repo/thena-platform-entities";
import { AccountEvents, TicketEvents } from "@repo/thena-shared-interfaces";

export enum CommentOp {
  CREATED = "created",
  UPDATED = "updated",
  DELETED = "deleted",
  REACTION_ADDED = "reaction_added",
  REACTION_REMOVED = "reaction_removed",
}

export function getCommentEventType(
  entityType: CommentEntityTypes,
  op: CommentOp,
) {
  // Get the event type
  switch (entityType) {
    // Ticket comments
    case CommentEntityTypes.TICKET: {
      switch (op) {
        case CommentOp.CREATED:
          return TicketEvents.COMMENT_ADDED;
        case CommentOp.UPDATED:
          return TicketEvents.COMMENT_UPDATED;
        case CommentOp.DELETED:
          return TicketEvents.COMMENT_DELETED;
        case CommentOp.REACTION_ADDED:
          return TicketEvents.REACTION_ADDED;
        case CommentOp.REACTION_REMOVED:
          return TicketEvents.REACTION_REMOVED;
        default:
          throw new Error(
            "Invalid operation for ticket comments or not supported!",
          );
      }
    }

    // Account activity comments
    case CommentEntityTypes.ACCOUNT_ACTIVITY: {
      switch (op) {
        case CommentOp.CREATED:
          return AccountEvents.ACCOUNT_ACTIVITY_COMMENT_CREATED;
        case CommentOp.UPDATED:
          return AccountEvents.ACCOUNT_ACTIVITY_COMMENT_UPDATED;
        case CommentOp.DELETED:
          return AccountEvents.ACCOUNT_ACTIVITY_COMMENT_DELETED;
        default:
          throw new Error(
            "Invalid operation for account activity comments or not supported!",
          );
      }
    }

    // Account note comments
    case CommentEntityTypes.ACCOUNT_NOTE: {
      switch (op) {
        case CommentOp.CREATED:
          return AccountEvents.ACCOUNT_NOTE_COMMENT_CREATED;
        case CommentOp.UPDATED:
          return AccountEvents.ACCOUNT_NOTE_COMMENT_UPDATED;
        case CommentOp.DELETED:
          return AccountEvents.ACCOUNT_NOTE_COMMENT_DELETED;
        default:
          throw new Error(
            "Invalid operation for account note comments or not supported!",
          );
      }
    }

    // Account task comments
    case CommentEntityTypes.ACCOUNT_TASK: {
      switch (op) {
        case CommentOp.CREATED:
          return AccountEvents.ACCOUNT_TASK_COMMENT_CREATED;
        case CommentOp.UPDATED:
          return AccountEvents.ACCOUNT_TASK_COMMENT_UPDATED;
        case CommentOp.DELETED:
          return AccountEvents.ACCOUNT_TASK_COMMENT_DELETED;
        default:
          throw new Error(
            "Invalid operation for account task comments or not supported!",
          );
      }
    }

    // Invalid entity type
    default:
      throw new Error("Invalid entity type or not supported!");
  }
}
