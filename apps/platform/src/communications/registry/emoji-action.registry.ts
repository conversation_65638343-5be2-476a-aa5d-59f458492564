import { Injectable } from "@nestjs/common";
import { EmojiActionFlowExecutor } from "../services/emoji-actions-flows";

@Injectable()
export class EmojiActionRegistry {
  private emojiActions = new Map<string, EmojiActionFlowExecutor>();

  register(actionName: string, action: EmojiActionFlowExecutor) {
    this.emojiActions.set(actionName, action);
  }

  getAction(actionName: string): EmojiActionFlowExecutor {
    return this.emojiActions.get(actionName);
  }

  getAllActions() {
    const actions = [];

    // Iterate over the emoji actions
    for (const action of this.emojiActions.values()) {
      actions.push({ name: action.getActionName(), action: action });
    }

    return actions;
  }
}
