import { ApiProperty } from "@nestjs/swagger";
import {
  Comment,
  CommentMetadata,
  CommentType,
  CommentVisibility,
  UserType,
} from "@repo/thena-platform-entities";
import { ExternalStorageResponseDto } from "../../storage/dto/storage.dto";

export class CommentResponseDto {
  @ApiProperty({ description: "The unique identifier of the comment" })
  id: string;

  @ApiProperty({ description: "The content of the comment" })
  content: string;

  @ApiProperty({ description: "The HTML content of the comment" })
  contentHtml: string;

  @ApiProperty({ description: "The JSON content of the comment" })
  contentJson: string;

  @ApiProperty({ description: "The markdown content of the comment" })
  contentMarkdown: string;

  @ApiProperty({ description: "Indicates if the comment is edited" })
  isEdited: boolean;

  @ApiProperty({ description: "The name of the comment thread" })
  threadName: string;

  @ApiProperty({ description: "The visibility of the comment" })
  commentVisibility: CommentVisibility;

  @ApiProperty({ description: "The type of the comment" })
  commentType: CommentType;

  @ApiProperty({ description: "Indicates if the comment is pinned" })
  isPinned: boolean;

  @ApiProperty({ description: "The source email ID of the comment" })
  sourceEmailId: string;

  @ApiProperty({ description: "The metadata of the comment" })
  metadata: CommentMetadata;

  @ApiProperty({ description: "The parent comment ID of the comment" })
  parentCommentId: string;

  @ApiProperty({ description: "The creation date of the comment" })
  createdAt: string;

  @ApiProperty({ description: "The update date of the comment" })
  updatedAt: string;

  @ApiProperty({ description: "The author of the comment" })
  author: string;

  @ApiProperty({
    description: "The unique identifier of the author of the comment",
  })
  authorId: string;

  @ApiProperty({ description: "The user type of the author of the comment" })
  authorUserType: UserType;

  @ApiProperty({
    description: "The customer contact ID of the author of the comment",
  })
  customerContactId: string;

  @ApiProperty({
    description:
      "The email of the customer contact of the author of the comment",
  })
  customerContactEmail: string;

  @ApiProperty({
    description:
      "The first name of the customer contact of the author of the comment",
  })
  customerContactFirstName: string;

  @ApiProperty({
    description:
      "The last name of the customer contact of the author of the comment",
  })
  customerContactLastName: string;

  @ApiProperty({
    description:
      "The avatar url of the customer contact of the author of the comment",
  })
  customerContactAvatarUrl: string;

  @ApiProperty({
    description: "The avatar url of the author of the comment",
  })
  authorAvatarUrl: string;

  @ApiProperty({ description: "The impersonated user email" })
  impersonatedUserEmail: string;

  @ApiProperty({ description: "The impersonated user name" })
  impersonatedUserName: string;

  @ApiProperty({ description: "The impersonated user avatar" })
  impersonatedUserAvatar: string;

  @ApiProperty({ description: "The attachments of the comment" })
  attachments: ExternalStorageResponseDto[];

  static fromEntity(entity: Comment) {
    const dto = new CommentResponseDto();

    dto.id = entity.uid;
    dto.content = entity.content;
    dto.contentHtml = entity.contentHtml;
    dto.contentMarkdown = entity.contentMarkdown;
    dto.contentJson = entity.contentJson;
    dto.isEdited = entity.isEdited;
    dto.threadName = entity.commentThreadName;
    dto.commentVisibility = entity.commentVisibility;
    dto.commentType = entity.commentType;
    dto.isPinned = entity.isPinned;
    dto.sourceEmailId = entity.sourceEmailId;
    dto.metadata = entity.metadata;
    dto.parentCommentId = entity.parentComment?.uid;
    dto.createdAt = entity.createdAt
      ? new Date(entity.createdAt).toISOString()
      : undefined;
    dto.updatedAt = entity.updatedAt
      ? new Date(entity.updatedAt).toISOString()
      : undefined;
    dto.author = entity.author?.name;
    dto.authorAvatarUrl = entity.author?.avatarUrl;
    dto.attachments = entity.attachments?.map((attachment) =>
      ExternalStorageResponseDto.fromEntity(attachment),
    );
    dto.authorId = entity.author?.uid;
    dto.authorUserType = entity.author?.userType as unknown as UserType;
    dto.customerContactId = entity.customerContact?.uid;
    dto.customerContactEmail = entity.customerContact?.email;
    dto.customerContactAvatarUrl = entity.customerContact?.avatarUrl;
    dto.customerContactFirstName = entity.customerContact?.firstName;
    dto.customerContactLastName = entity.customerContact?.lastName;
    dto.impersonatedUserEmail = entity.impersonatedUserEmail;
    dto.impersonatedUserName = entity.impersonatedUserName;
    dto.impersonatedUserAvatar = entity.impersonatedUserAvatar;

    return dto;
  }
}

export class BulkCommentResponseDto extends CommentResponseDto {
  ticketId: string;
  teamId: string;
  accountId: string;
  accountNoteId: string;
  accountActivityId: string;
  accountTaskId: string;

  static fromEntity(entity: Comment) {
    const dto = new BulkCommentResponseDto();

    const baseDto = CommentResponseDto.fromEntity(entity);
    Object.assign(dto, baseDto);

    dto.ticketId = entity.ticket?.uid;
    dto.teamId = entity.team?.uid;
    dto.accountId = entity.account?.uid;
    dto.accountNoteId = entity.accountNote?.uid;
    dto.accountActivityId = entity.accountActivity?.uid;
    dto.accountTaskId = entity.accountTask?.uid;

    return dto;
  }
}
