import { ApiProperty } from "@nestjs/swagger";
import { Emojis } from "@repo/thena-platform-entities";

export class EmojiResponseDto {
  @ApiProperty({ description: "The unique identifier of the emoji" })
  id: string;

  @ApiProperty({ description: "The name of the emoji" })
  name: string;

  @ApiProperty({ description: "The unicode of the emoji" })
  unicode: string;

  @ApiProperty({ description: "The url of the emoji" })
  url: string;

  @ApiProperty({ description: "The aliases of the emoji" })
  aliases: string;

  @ApiProperty({ description: "The keywords of the emoji" })
  keywords: string[];

  @ApiProperty({ description: "The creation date of the comment" })
  createdAt: string;

  @ApiProperty({ description: "The update date of the comment" })
  updatedAt: string;

  static fromEntity(entity: Emojis) {
    const dto = new EmojiResponseDto();

    dto.id = entity.id;
    dto.name = entity.name;
    dto.unicode = entity.unicode;
    dto.url = entity.url;
    dto.aliases = entity.aliases;
    dto.keywords = entity.keywords;
    dto.createdAt = entity.createdAt
      ? new Date(entity.createdAt).toISOString()
      : undefined;
    dto.updatedAt = entity.updatedAt
      ? new Date(entity.updatedAt).toISOString()
      : undefined;

    return dto;
  }
}
