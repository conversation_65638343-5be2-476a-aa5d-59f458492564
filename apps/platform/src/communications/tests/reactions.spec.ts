import {
  HttpStatus,
  UnprocessableEntityException,
  ValidationPipe,
} from "@nestjs/common";
import {
  FastifyAdapter,
  NestFastifyApplication,
} from "@nestjs/platform-fastify";
import { Test } from "@nestjs/testing";
import {
  Comment,
  Team,
  Ticket,
  TicketPriority,
} from "@repo/thena-platform-entities";
import { t__loginIntoAuthService } from "@repo/thena-shared-libs";
import { AppModule } from "../../app.module";
import { CreateCommentDto } from "../../common/dto/comments.dto";
import { injectWithOrgId } from "../../utils";
import { AddReactionDto } from "../dto/reactions.dto";

jest.mock("@repo/nestjs-commons/aws-utils/sqs");
jest.mock("@repo/thena-eventbridge");

jest.mock("typesense", () => {
  const mockTypesense = jest.requireActual(
    "../../../__mocks__/typesense/typesense.mock",
  );
  return {
    Client: mockTypesense.Client,
    TypesenseModule: mockTypesense.TypesenseModule,
    TypesenseService: mockTypesense.TypesenseService,
  };
});

describe("Tickets", () => {
  let app: NestFastifyApplication;
  let userAuthToken: string;
  let imposterAuthToken: string;

  let reactionsTeam: Team;
  let ticket: Ticket;
  let comment: Comment;
  let mediumTicketPriority: TicketPriority;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = module.createNestApplication<NestFastifyApplication>(
      new FastifyAdapter(),
    );

    app.useGlobalPipes(
      new ValidationPipe({
        transform: true,
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        exceptionFactory: (errors) => {
          return new UnprocessableEntityException(
            errors.map((error) => ({
              property: error.property,
              constraints: error.constraints,
            })),
          );
        },
      }),
    );

    await app.init();
    await app.getHttpAdapter().getInstance().ready();

    // Log the user in to get the auth token
    userAuthToken = await t__loginIntoAuthService(
      global.testUser.email,
      global.testUser.password,
    );

    imposterAuthToken = await t__loginIntoAuthService(
      global.testUser1.email,
      global.testUser1.password,
    );

    const createTeamResponse = await injectWithOrgId(app, {
      method: "POST",
      url: "/v1/teams",
      headers: { Authorization: `Bearer ${userAuthToken}` },
      payload: {
        name: "Reactions Team",
      },
    });

    reactionsTeam = createTeamResponse.json().data;

    const mediumTicketPriorityResponse = await injectWithOrgId(app, {
      method: "POST",
      url: "/v1/tickets/priority",
      headers: { Authorization: `Bearer ${userAuthToken}` },
      payload: {
        name: "Medium Priority",
        teamId: reactionsTeam.id,
      },
    });

    mediumTicketPriority = mediumTicketPriorityResponse.json().data;

    const createTicketResponse = await injectWithOrgId(app, {
      method: "POST",
      url: "/v1/tickets",
      headers: { Authorization: `Bearer ${userAuthToken}` },
      payload: {
        title: "Need help with my account!",
        requestorEmail: "<EMAIL>",
        teamId: reactionsTeam.id,
        priorityId: mediumTicketPriority.id,
      },
    });

    ticket = createTicketResponse.json().data;

    const createCommentResponse = await injectWithOrgId(app, {
      method: "POST",
      url: `/v1/tickets/${ticket.id}/comment`,
      headers: { Authorization: `Bearer ${userAuthToken}` },
      payload: {
        content: "This is a test comment",
      } as CreateCommentDto,
    });

    comment = createCommentResponse.json().data;
  });

  afterAll(async () => {
    await app.close();
  });

  describe("Create reaction", () => {
    it("should be able to create a reaction", async () => {
      const createReactionResponse = await injectWithOrgId(app, {
        method: "POST",
        url: `/v1/reactions/${comment.id}`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          name: global.globalEmojiHeart.name,
        } as AddReactionDto,
      });

      expect(createReactionResponse.statusCode).toBe(HttpStatus.CREATED);

      const result = createReactionResponse.json().data;

      expect(result).toBe(true);

      const getCommentResponse = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/comments/${comment.id}`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(getCommentResponse.statusCode).toBe(HttpStatus.OK);

      const commentData = getCommentResponse.json().data;

      const reactionsMeta = commentData.metadata.reactions;

      expect(reactionsMeta).toBeDefined();
      expect(reactionsMeta.heart).toBeDefined();
      expect(reactionsMeta.heart.count).toBe(1);
      expect(reactionsMeta.heart.users).toEqual([global.testUser.uid]);
    });

    it("should not be able to create a reaction again for the same user", async () => {
      const createReactionResponse = await injectWithOrgId(app, {
        method: "POST",
        url: `/v1/reactions/${comment.id}`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          name: global.globalEmojiHeart.name,
        } as AddReactionDto,
      });

      expect(createReactionResponse.statusCode).toBe(HttpStatus.CONFLICT);

      const error = createReactionResponse.json();

      expect(error).toBeDefined();
      expect(error.message).toBe("You have already reacted to this comment!");
    });

    it("should be able to create reaction for another user", async () => {
      const createReactionResponse = await injectWithOrgId(app, {
        method: "POST",
        url: `/v1/reactions/${comment.id}`,
        headers: { Authorization: `Bearer ${imposterAuthToken}` },
        payload: {
          name: global.globalEmojiHeart.name,
        } as AddReactionDto,
      });

      expect(createReactionResponse.statusCode).toBe(HttpStatus.CREATED);

      const result = createReactionResponse.json().data;

      expect(result).toBe(true);

      const getCommentResponse = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/comments/${comment.id}`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(getCommentResponse.statusCode).toBe(HttpStatus.OK);

      const commentData = getCommentResponse.json().data;

      const reactionsMeta = commentData.metadata.reactions;

      expect(reactionsMeta).toBeDefined();
      expect(reactionsMeta.heart).toBeDefined();
      expect(reactionsMeta.heart.count).toBe(2);
      expect(reactionsMeta.heart.users).toEqual([
        global.testUser1.uid,
        global.testUser.uid,
      ]);
    });

    it("should be able to create multiple reactions", async () => {
      const createReactionResponse = await injectWithOrgId(app, {
        method: "POST",
        url: `/v1/reactions/${comment.id}`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          name: global.globalEmojiThumbsUp.name,
        } as AddReactionDto,
      });

      expect(createReactionResponse.statusCode).toBe(HttpStatus.CREATED);

      const result = createReactionResponse.json().data;

      expect(result).toBe(true);

      const getCommentResponse = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/comments/${comment.id}`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(getCommentResponse.statusCode).toBe(HttpStatus.OK);

      const commentData = getCommentResponse.json().data;

      const reactionsMeta = commentData.metadata.reactions;

      expect(reactionsMeta).toBeDefined();
      expect(reactionsMeta.heart).toBeDefined();
      expect(reactionsMeta.heart.count).toBe(2);
      expect(reactionsMeta.heart.users).toEqual([
        global.testUser1.uid,
        global.testUser.uid,
      ]);
      expect(reactionsMeta.thumbs_up).toBeDefined();
      expect(reactionsMeta.thumbs_up.count).toBe(1);
      expect(reactionsMeta.thumbs_up.users).toEqual([global.testUser.uid]);
    });
  });

  describe("Remove reaction", () => {
    it("should be able to remove a reaction", async () => {
      const removeReactionResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/reactions/remove/${comment.id}/${global.globalEmojiHeart.name}`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(removeReactionResponse.statusCode).toBe(HttpStatus.NO_CONTENT);

      const getCommentResponse = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/comments/${comment.id}`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(getCommentResponse.statusCode).toBe(HttpStatus.OK);

      const commentData = getCommentResponse.json().data;

      const reactionsMeta = commentData.metadata.reactions;

      expect(reactionsMeta).toBeDefined();
      expect(reactionsMeta.heart).toBeDefined();
      expect(reactionsMeta.heart.count).toBe(1);
      expect(reactionsMeta.heart.users).toEqual([global.testUser1.uid]);
    });

    it("should not be able to remove a reaction which was not added", async () => {
      const removeReactionResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/reactions/remove/${comment.id}/${global.globalEmojiHeart.name}`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(removeReactionResponse.statusCode).toBe(HttpStatus.NOT_FOUND);

      const error = removeReactionResponse.json();

      expect(error).toBeDefined();
      expect(error.message).toBe("You have not reacted to this comment!");
    });
  });
});
