import { faker } from "@faker-js/faker";
import {
  HttpStatus,
  UnprocessableEntityException,
  ValidationPipe,
} from "@nestjs/common";
import {
  FastifyAdapter,
  NestFastifyApplication,
} from "@nestjs/platform-fastify";
import { Test } from "@nestjs/testing";
import {
  Account,
  AccountAttributeType,
  AccountNote,
  Comment,
  CommentEntityTypes,
  CommentType,
  CommentVisibility,
  Team,
  Ticket,
  TicketPriority,
} from "@repo/thena-platform-entities";
import { t__loginIntoAuthService } from "@repo/thena-shared-libs";
import { AppModule } from "../../app.module";
import { CreateCommentDto } from "../../common/dto/comments.dto";
import { injectWithOrgId } from "../../utils";
import { UpdateCommentDto } from "../dto/comments.dto";

jest.mock("@repo/nestjs-commons/aws-utils/sqs");
jest.mock("@repo/thena-eventbridge");
jest.mock("../../storage/services/storage.service", () => ({
  StorageService: jest.fn().mockImplementation(() => ({
    attachFilesToEntity: jest.fn().mockImplementation((fileUids) => {
      return fileUids.map((id) => ({
        id: id,
        uid: id,
        originalName: "test.txt",
        contentType: "text/plain",
        size: 1024,
        url: `https://storage.test/${id}`,
        createdAt: new Date(),
        updatedAt: new Date(),
      }));
    }),
  })),
}));

jest.mock("typesense", () => {
  const mockTypesense = jest.requireActual(
    "../../../__mocks__/typesense/typesense.mock",
  );
  return {
    Client: mockTypesense.Client,
    TypesenseModule: mockTypesense.TypesenseModule,
    TypesenseService: mockTypesense.TypesenseService,
  };
});

describe("Communications", () => {
  let app: NestFastifyApplication;
  let userAuthToken: string;
  let imposterAuthToken: string;

  let commentsTeam: Team;
  let ticket: Ticket;
  let account: Account;
  let accountNote: AccountNote;
  let mediumTicketPriority: TicketPriority;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = module.createNestApplication<NestFastifyApplication>(
      new FastifyAdapter(),
    );

    app.useGlobalPipes(
      new ValidationPipe({
        transform: true,
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        exceptionFactory: (errors) => {
          return new UnprocessableEntityException(
            errors.map((error) => ({
              property: error.property,
              constraints: error.constraints,
            })),
          );
        },
      }),
    );

    await app.init();
    await app.getHttpAdapter().getInstance().ready();

    userAuthToken = await t__loginIntoAuthService(
      global.testUser.email,
      global.testUser.password,
    );

    imposterAuthToken = await t__loginIntoAuthService(
      global.testUser2.email,
      global.testUser2.password,
    );

    const createTeamResponse = await injectWithOrgId(app, {
      method: "POST",
      url: "/v1/teams",
      headers: { Authorization: `Bearer ${userAuthToken}` },
      payload: {
        name: "Communications Comments Teams",
      },
    });

    commentsTeam = createTeamResponse.json().data;

    const mediumTicketPriorityResponse = await injectWithOrgId(app, {
      method: "POST",
      url: "/v1/tickets/priority",
      headers: { Authorization: `Bearer ${userAuthToken}` },
      payload: {
        name: "Medium",
        teamId: commentsTeam.id,
      },
    });

    mediumTicketPriority = mediumTicketPriorityResponse.json().data;

    const createTicketResponse = await injectWithOrgId(app, {
      method: "POST",
      url: `/v1/tickets`,
      headers: { Authorization: `Bearer ${userAuthToken}` },
      payload: {
        title: "Need help with my account!",
        requestorEmail: "<EMAIL>",
        teamId: commentsTeam.id,
        priorityId: mediumTicketPriority.id,
      },
    });

    ticket = createTicketResponse.json().data;

    const createAccountResponse = await injectWithOrgId(app, {
      method: "POST",
      url: "/v1/accounts",
      headers: { Authorization: `Bearer ${userAuthToken}` },
      payload: {
        name: "Test Account",
        primaryDomain: faker.internet.domainName(),
        source: "manual",
        accountOwnerId: global.testUser.uid,
      },
    });

    account = createAccountResponse.json().data;

    const createAccountNoteTypeResponse = await injectWithOrgId(app, {
      method: "POST",
      url: "/v1/accounts/attributes",
      headers: { Authorization: `Bearer ${userAuthToken}` },
      payload: {
        value: "ACTIVITY_NOTE",
        attribute: AccountAttributeType.NOTE_TYPE,
        isDefault: true,
      },
    });

    const accountNoteType = createAccountNoteTypeResponse.json().data;

    const createAccountNoteResponse = await injectWithOrgId(app, {
      method: "POST",
      url: "/v1/accounts/notes",
      headers: { Authorization: `Bearer ${userAuthToken}` },
      payload: {
        content: "This is a test account note",
        accountId: account.id,
        typeAttributeId: accountNoteType.id,
      },
    });

    accountNote = createAccountNoteResponse.json().data;
  });

  afterAll(async () => {
    await app.close();
  });

  let updatableComment: Comment;

  describe("Create comment", () => {
    let parentComment: Comment;
    let firstChildComment: Comment;

    it("should be able to create a new comment on a ticket", async () => {
      const createCommentResponse = await injectWithOrgId(app, {
        method: "POST",
        url: `/v1/tickets/${ticket.id}/comment`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          content: "This is a test comment",
        } as CreateCommentDto,
      });

      expect(createCommentResponse.statusCode).toBe(HttpStatus.CREATED);

      const result = createCommentResponse.json().data;

      expect(result.content).toBe("This is a test comment");
      expect(result.authorId).toBe(global.testUser.uid);

      parentComment = result;
    });
    
    it("should use customer contact's first_name as fallback for impersonatedUserName", async () => {
      const testEmail = "<EMAIL>";
      
      const createCommentResponse = await injectWithOrgId(app, {
        method: "POST",
        url: `/v1/tickets/${ticket.id}/comment`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          content: "This is a comment from an impersonated user",
          impersonatedUserEmail: testEmail,
        } as CreateCommentDto,
      });

      expect(createCommentResponse.statusCode).toBe(HttpStatus.CREATED);

      const result = createCommentResponse.json().data;

      expect(result.impersonatedUserEmail).toBe(testEmail);
      expect(result.impersonatedUserName).not.toBeNull();
      
      const getContactsResponse = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/accounts/contacts`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
        query: {
          email: testEmail,
        },
      });
      
      expect(getContactsResponse.statusCode).toBe(HttpStatus.OK);
      const contacts = getContactsResponse.json().data;
      expect(contacts.length).toBeGreaterThan(0);
      
      expect(result.impersonatedUserName).toBe(contacts[0].firstName);
    });

    it("should be able to create a new comment on an account note", async () => {
      const createAccountNoteCommentResponse = await injectWithOrgId(app, {
        method: "POST",
        url: `/v1/comments`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          entityId: accountNote.id,
          entityType: CommentEntityTypes.ACCOUNT_NOTE,
          content: "This is a test comment on an account note",
        } as CreateCommentDto,
      });

      expect(createAccountNoteCommentResponse.statusCode).toBe(
        HttpStatus.CREATED,
      );

      const result = createAccountNoteCommentResponse.json().data;
      expect(result.content).toBe("This is a test comment on an account note");
      expect(result.authorId).toBe(global.testUser.uid);

      const getAccountNoteCommentsResponse = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/comments`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
        query: {
          entityId: accountNote.id,
          entityType: CommentEntityTypes.ACCOUNT_NOTE,
        },
      });

      expect(getAccountNoteCommentsResponse.statusCode).toBe(HttpStatus.OK);

      const getResult = getAccountNoteCommentsResponse.json().data;

      expect(getResult.length).toBe(1);
      expect(getResult[0].id).toBe(result.id);
      expect(getResult[0].content).toBe(
        "This is a test comment on an account note",
      );
      expect(getAccountNoteCommentsResponse.json().data[0].authorId).toBe(
        global.testUser.uid,
      );
    });

    it("should be able to create a reply to a comment", async () => {
      const createCommentResponse = await injectWithOrgId(app, {
        method: "POST",
        url: `/v1/tickets/${ticket.id}/comment`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          content: "This is a test reply",
          parentCommentId: parentComment.id,
        } as CreateCommentDto,
      });

      expect(createCommentResponse.statusCode).toBe(HttpStatus.CREATED);

      const result = createCommentResponse.json().data;

      expect(result.content).toBe("This is a test reply");
      expect(result.parentCommentId).toBe(parentComment.id);
      expect(result.authorId).toBe(global.testUser.uid);

      firstChildComment = result;

      const parentCommentResponse = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/comments/${parentComment.id}`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(parentCommentResponse.statusCode).toBe(HttpStatus.OK);

      const parentCommentResult = parentCommentResponse.json().data;

      expect(parentCommentResult.metadata.replies).toEqual([result.id]);
    });

    it("should be able to create another nested comment with metadata", async () => {
      const createCommentResponse = await injectWithOrgId(app, {
        method: "POST",
        url: `/v1/tickets/${ticket.id}/comment`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          content: "This is a test reply",
          parentCommentId: parentComment.id,
        } as CreateCommentDto,
      });

      expect(createCommentResponse.statusCode).toBe(HttpStatus.CREATED);

      const result = createCommentResponse.json().data;

      expect(result.content).toBe("This is a test reply");
      expect(result.parentCommentId).toBe(parentComment.id);
      expect(result.authorId).toBe(global.testUser.uid);

      const parentCommentResponse = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/comments/${parentComment.id}`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(parentCommentResponse.statusCode).toBe(HttpStatus.OK);

      const parentCommentResult = parentCommentResponse.json().data;

      expect(parentCommentResult.metadata.replies).toEqual([
        firstChildComment.id,
        result.id,
      ]);
    });

    it("should be able to create mentions in a comment", async () => {
      const mockAttachmentId = "mock-storage-id-123";

      const createCommentResponse = await injectWithOrgId(app, {
        method: "POST",
        url: `/v1/tickets/${ticket.id}/comment`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          content: `Hey <@${global.testUser.uid}> is there any update on this? CC <@${global.testUser2.uid}>`,
          attachmentIds: [mockAttachmentId],
        } as CreateCommentDto,
      });

      expect(createCommentResponse.statusCode).toBe(HttpStatus.CREATED);

      const result = createCommentResponse.json().data;

      expect(result.content).toBe(
        `Hey <@${global.testUser.uid}> is there any update on this? CC <@${global.testUser2.uid}>`
      );

      const expectedMentions = [global.testUser.uid, global.testUser2.uid];
      expect(result.metadata.mentions).toEqual(expectedMentions);

      expect(result.attachments[0].id).toBe(mockAttachmentId);

      updatableComment = result;
    });
  });

  describe("Update comment", () => {
    it("should be able to update a comment with empty mentions", async () => {
      const updateCommentResponse = await injectWithOrgId(app, {
        method: "PATCH",
        url: `/v1/comments/${updatableComment.id}`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          content: "I have updated the ticket!",
        } as UpdateCommentDto,
      });

      expect(updateCommentResponse.statusCode).toBe(HttpStatus.OK);

      const result = updateCommentResponse.json().data;

      expect(result.content).toBe("I have updated the ticket!");
      expect(result.isEdited).toBe(true);
      expect(result.metadata.mentions).toEqual({});
    });

    it("should be able to update a comment with mentions", async () => {
      const updateCommentResponse = await injectWithOrgId(app, {
        method: "PATCH",
        url: `/v1/comments/${updatableComment.id}`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          content: `Hey <@${global.testUser.uid}> is there any update on this? CC <@${global.testUser2.uid}>`,
        } as UpdateCommentDto,
      });

      expect(updateCommentResponse.statusCode).toBe(HttpStatus.OK);

      const result = updateCommentResponse.json().data;

      expect(result.content).toBe(
        `Hey <@${global.testUser.uid}> is there any update on this? CC <@${global.testUser2.uid}>`,
      );
      expect(result.isEdited).toBe(true);
      expect(result.metadata.mentions).toEqual([
        global.testUser.uid,
        global.testUser2.uid,
      ]);
    });

    it("should not be able to update a comment if I am not the author", async () => {
      const updateCommentResponse = await injectWithOrgId(app, {
        method: "PATCH",
        url: `/v1/comments/${updatableComment.id}`,
        headers: { Authorization: `Bearer ${imposterAuthToken}` },
        payload: {
          content: "I have updated the ticket!",
        } as UpdateCommentDto,
      });

      expect(updateCommentResponse.statusCode).toBe(HttpStatus.UNAUTHORIZED);
      expect(updateCommentResponse.json().message).toBe(
        "You are not authorized to update this comment!",
      );
    });

    it("should be able to update a comment with attachments", async () => {
      const mockAttachmentId = "mock-storage-id-123";

      const updateCommentResponse = await injectWithOrgId(app, {
        method: "PATCH",
        url: `/v1/comments/${updatableComment.id}`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          attachments: [mockAttachmentId],
        } as UpdateCommentDto,
      });

      expect(updateCommentResponse.statusCode).toBe(HttpStatus.OK);

      const result = updateCommentResponse.json().data;

      expect(result.attachments[0].id).toBe(mockAttachmentId);
    });

    it("should be able to update a comment with a thread name", async () => {
      const updateCommentResponse = await injectWithOrgId(app, {
        method: "PATCH",
        url: `/v1/comments/${updatableComment.id}`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          threadName: "Test Thread",
        } as UpdateCommentDto,
      });

      expect(updateCommentResponse.statusCode).toBe(HttpStatus.OK);

      const result = updateCommentResponse.json().data;

      expect(result.threadName).toBe("Test Thread");
    });

    it("should be able to update a comment with a comment visibility", async () => {
      const updateCommentResponse = await injectWithOrgId(app, {
        method: "PATCH",
        url: `/v1/comments/${updatableComment.id}`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          commentVisibility: CommentVisibility.PRIVATE,
        } as UpdateCommentDto,
      });

      expect(updateCommentResponse.statusCode).toBe(HttpStatus.OK);

      const result = updateCommentResponse.json().data;

      expect(result.commentVisibility).toBe(
        CommentVisibility.PRIVATE,
      );
    });

    it("should be able to update a comment with a comment type", async () => {
      const updateCommentResponse = await injectWithOrgId(app, {
        method: "PATCH",
        url: `/v1/comments/${updatableComment.id}`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          commentType: CommentType.NOTE,
        } as UpdateCommentDto,
      });

      expect(updateCommentResponse.statusCode).toBe(HttpStatus.OK);

      const result = updateCommentResponse.json().data;

      expect(result.commentType).toBe(CommentType.NOTE);
    });

    it("should be able to update a comment with a isPinned", async () => {
      const updateCommentResponse = await injectWithOrgId(app, {
        method: "PATCH",
        url: `/v1/comments/${updatableComment.id}`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          isPinned: true,
        } as UpdateCommentDto,
      });

      expect(updateCommentResponse.statusCode).toBe(HttpStatus.OK);

      const result = updateCommentResponse.json().data;

      expect(result.isPinned).toBe(true);
    });
  });

  describe("Delete comment", () => {
    it("should not be able to delete a comment if I am not the author", async () => {
      const deleteCommentResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/comments/${updatableComment.id}`,
        headers: { Authorization: `Bearer ${imposterAuthToken}` },
      });

      expect(deleteCommentResponse.statusCode).toBe(HttpStatus.UNAUTHORIZED);
      expect(deleteCommentResponse.json().message).toBe(
        "You are not authorized to delete this comment!",
      );

      const getCommentResponse = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/comments/${updatableComment.id}`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(getCommentResponse.statusCode).toBe(HttpStatus.OK);
    });

    it("should be able to delete a comment", async () => {
      const deleteCommentResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/comments/${updatableComment.id}`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(deleteCommentResponse.statusCode).toBe(HttpStatus.NO_CONTENT);

      const getCommentResponse = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/comments/${updatableComment.id}`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(getCommentResponse.statusCode).toBe(HttpStatus.NOT_FOUND);
    });
  });
});
