import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
    CommentEntityTypes,
    CommentType,
    CommentVisibility,
} from "@repo/thena-platform-entities";
import { Transform } from "class-transformer";
import {
    IsArray,
    IsBoolean,
    IsEmail,
    IsEnum,
    IsNotEmpty,
    IsObject,
    IsOptional,
    IsString,
    MaxLength,
    MinLength,
} from "class-validator";

export class UpdateCommentDto {
  @IsString()
  @IsOptional()
  @MaxLength(5000)
  @MinLength(1)
  @Transform(({ value }) => value.trim())
  @ApiProperty({
    description: "The content of the comment",
    example: "This is a comment",
    minLength: 1,
    maxLength: 5000,
  })
  content: string;

  @IsString()
  @IsOptional()
  @MaxLength(5000)
  @MinLength(1)
  @Transform(({ value }) => value.trim())
  @ApiPropertyOptional({ description: "The HTML content of the comment" })
  contentHtml?: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: "The JSON content of the comment" })
  contentJson?: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: "The name of the comment thread" })
  threadName?: string;

  @IsEnum(CommentVisibility)
  @IsOptional()
  @ApiPropertyOptional({ description: "The visibility of the comment" })
  commentVisibility?: CommentVisibility;

  @IsOptional()
  @IsEnum([CommentType.COMMENT, CommentType.NOTE])
  @ApiPropertyOptional({ description: "The type of the comment" })
  commentType?: CommentType;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ description: "Whether the comment is pinned" })
  isPinned?: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ description: "Whether to preserve mentions when updating content" })
  preserveMentions?: boolean;

  @IsArray()
  @IsOptional()
  @ApiProperty({
    description: "The attachments of the comment",
    example: ["attachment1", "attachment2"],
  })
  attachments: string[];

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ description: "The comment as a string" })
  commentAs?: string;

  @IsOptional()
  @IsObject()
  @ApiPropertyOptional({ description: "The metadata of the comment" })
  metadata?: Record<string, any>;
}

export class CreateCommentOnAnEntityDto {
  @IsString()
  @IsOptional()
  @MaxLength(5000)
  @ApiPropertyOptional({ description: "The content of the comment" })
  content?: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: "The HTML content of the comment" })
  contentHtml?: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: "The JSON content of the comment" })
  contentJson?: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: "The name of the comment thread" })
  threadName?: string;

  @IsEnum(CommentEntityTypes)
  @ApiProperty({ description: "The type of the entity" })
  entityType: CommentEntityTypes;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: "The identifier of the entity" })
  entityId: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ description: "The parent comment ID" })
  parentCommentId?: string;

  @IsOptional()
  @IsEnum(CommentVisibility)
  @ApiPropertyOptional({ description: "The visibility of the comment" })
  commentVisibility?: CommentVisibility;

  @IsOptional()
  @IsEnum([CommentType.COMMENT, CommentType.NOTE])
  @ApiPropertyOptional({ description: "The type of the comment" })
  commentType?: CommentType;

  @IsOptional()
  @IsObject()
  @ApiPropertyOptional({ description: "The metadata of the comment" })
  metadata?: Record<string, any>;

  @IsArray()
  @IsOptional()
  @ApiPropertyOptional({ description: "The attachment IDs" })
  attachmentIds?: string[];

  @IsOptional()
  @IsEmail()
  @ApiPropertyOptional({ description: "The comment as a string" })
  commentAs?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ description: "The customer contact's email address" })
  customerEmail?: string;

  @IsOptional()
  @IsString()
  impersonatedUserEmail?: string;

  @IsOptional()
  @IsString()
  impersonatedUserName?: string;

  @IsOptional()
  @IsString()
  impersonatedUserAvatar?: string;
}
