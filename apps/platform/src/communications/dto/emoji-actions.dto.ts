import { ApiProperty } from "@nestjs/swagger";
import { EmojiActionFlow } from "@repo/thena-platform-entities";
import { IsArray, IsEnum, IsNotEmpty, IsString } from "class-validator";

export class MapEmojisActionDTO {
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty({ message: "Emoji(s) to map against the action are required" })
  @ApiProperty({
    type: [String],
    description: "Emojis to map against the action",
    example: ["white_check_mark"],
  })
  emojis: string[];

  @IsEnum(EmojiActionFlow)
  @IsNotEmpty({ message: "Flow to map against the emojis is required" })
  @ApiProperty({
    type: String,
    description: "Flow to map against the emojis",
    example: "OUTBOUND",
  })
  flow: EmojiActionFlow;

  @IsString()
  @IsNotEmpty({ message: "Action to map against the emojis is required" })
  @ApiProperty({
    type: String,
    description: "Action to map against the emojis",
    example: "create_ticket",
  })
  action: string;
}

export class UpdateEmojiActionIndividual {
  @IsString()
  @IsNotEmpty({ message: "Emoji to map against the action is required" })
  @ApiProperty({
    type: String,
    description: "Emoji to map against the action",
    example: "white_check_mark",
  })
  emoji: string;

  @IsString()
  @IsNotEmpty({ message: "Action to map against the emoji is required" })
  @ApiProperty({
    type: String,
    description: "Action to map against the emoji",
    example: "create_ticket",
  })
  action: string;
}

export class UpdateEmojiActionDTO {
  @IsString()
  @IsNotEmpty({ message: "Action to map against the emojis is required" })
  @ApiProperty({
    type: String,
    description: "Action to map against the emojis",
    example: "create_ticket",
  })
  action: string;

  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty({ message: "Emoji(s) to map against the action are required" })
  @ApiProperty({
    type: [String],
    description: "Emojis to map against the action",
    example: ["white_check_mark"],
  })
  emojis: string[];
}

export class UnmapEmojiActionDTO {
  @IsString()
  @IsNotEmpty({ message: "Emoji to unmap from the action is required" })
  @ApiProperty({
    type: String,
    description: "Emoji to unmap from the action",
    example: "white_check_mark",
  })
  emoji: string;

  @IsEnum(EmojiActionFlow)
  @IsNotEmpty({ message: "Flow to unmap from the emoji is required" })
  @ApiProperty({
    type: String,
    description: "Flow to unmap from the emoji",
    example: "OUTBOUND",
  })
  flow: EmojiActionFlow;

  @IsString()
  @IsNotEmpty({ message: "Action to unmap from the emoji is required" })
  @ApiProperty({
    type: String,
    description: "Action to unmap from the emoji",
    example: "create_ticket",
  })
  action: string;
}
