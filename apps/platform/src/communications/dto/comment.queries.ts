import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
  CommentEntityTypes,
  CommentType,
  CommentVisibility,
} from "@repo/thena-platform-entities";
import { Transform } from "class-transformer";
import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from "class-validator";

export interface CreateComment {
  content: string;
  contentHtml?: string;
  contentJson?: string;
  source?: string;
  teamId?: string;
  authorId: string;
  ticketId?: string;
  accountActivityId?: string;
  accountNoteId?: string;
  accountTaskId?: string;
  accountId?: string;
  organizationId: string;
  commentVisibility: CommentVisibility;
  commentType: CommentType;
  parentCommentId?: string;
  commentThreadName?: string;
  metadata?: Record<string, any>;
  customerId?: string;
  impersonatedUserEmail?: string;
  impersonatedUserName?: string;
  impersonatedUserAvatar?: string;
  shouldSendEmail?: boolean;
}

export class GetCommentQuery {
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  @ApiPropertyOptional({ description: "The page number" })
  page?: number;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  @ApiPropertyOptional({ description: "The limit number of comments to fetch" })
  limit?: number;
}

export class GetCommentThreadsQuery extends GetCommentQuery {}

export class GetCommentsForAnEntityQuery extends GetCommentQuery {
  @IsEnum(CommentEntityTypes)
  @ApiProperty({ description: "The type of the entity" })
  entityType: CommentEntityTypes;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: "The identifier of the entity" })
  entityId: string;
}

export class GetCommentByUserTypeQuery {
  @IsEnum(CommentEntityTypes)
  entityType: CommentEntityTypes;

  @IsString()
  @IsNotEmpty()
  entityId: string;

  @IsString()
  @IsNotEmpty()
  userType: "agent" | "customer" | "all";

  @IsOptional()
  @IsBoolean()
  firstComment: boolean;
}

export class GetAllCommentsForAnEntityQuery {
  @IsEnum(CommentEntityTypes)
  @ApiProperty({ description: "The type of the entity" })
  entityType: CommentEntityTypes;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: "The identifier of the entity" })
  entityId: string;

  @IsString()
  @IsOptional()
  @ApiProperty({ description: "The identifier of the team" })
  teamId: string;

  @IsString()
  @IsOptional()
  @ApiProperty({ description: "The identifier of the account" })
  accountId: string;
}

export class HasCustomerCommentQuery {
  @IsOptional()
  @IsString()
  @ApiProperty({
    description: "The timestamp after which to check for comments",
  })
  since?: string;

  @IsOptional()
  @IsString()
  @ApiProperty({ description: "The identifier of the parent comment" })
  parentCommentId?: string;
}

export class HasInternalMemberCommentQuery {
  @IsOptional()
  @IsString()
  @ApiProperty({
    description: "The timestamp after which to check for comments",
  })
  since?: string;

  @IsOptional()
  @IsString()
  @ApiProperty({ description: "The identifier of the parent comment" })
  parentCommentId?: string;

  @IsEnum(CommentType)
  @IsOptional()
  @ApiProperty({ description: "The type of the comment" })
  commentType?: CommentType;

  @IsEnum(CommentVisibility)
  @IsOptional()
  @ApiProperty({ description: "The visibility of the comment" })
  commentVisibility?: CommentVisibility;
}
