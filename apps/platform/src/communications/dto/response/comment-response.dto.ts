import { ApiProperty } from "@nestjs/swagger";
import { Expose, Type } from "class-transformer";
import { ValidateNested } from "class-validator";
import { ResponseMessage } from "../../../common/utils/response-dto.utils";
import { CommentResponseDto } from "../../transformers/comment-response.transformer";

export class GetAllCommentsResponse extends ResponseMessage {
  @ApiProperty({
    description: "The comments fetched",
    type: [CommentResponseDto],
  })
  @ValidateNested({ each: true })
  @Type(() => CommentResponseDto)
  @Expose()
  data: CommentResponseDto[];
}

export class CommonCommentResponse extends ResponseMessage {
  @ApiProperty({
    description: "The response for create/update/delete comment operations",
    type: CommentResponseDto,
  })
  @ValidateNested()
  @Type(() => CommentResponseDto)
  @Expose()
  data: CommentResponseDto;
}
