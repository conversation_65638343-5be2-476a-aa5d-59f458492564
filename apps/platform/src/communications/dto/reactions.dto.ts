import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsEmail, IsNotEmpty, IsObject, IsOptional, IsString } from "class-validator";

export class AddReactionDto {
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }) => value.trim())
  @ApiProperty({
    description: "The name of the reaction",
    example: "heart",
  })
  name: string;

  @IsOptional()
  @IsEmail()
  @ApiPropertyOptional({ description: "The impersonated user email" })
  impersonatedUserEmail?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ description: "The impersonated user name" })
  impersonatedUserName?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ description: "The impersonated user avatar" })
  impersonatedUserAvatar?: string;

  @IsOptional()
  @IsObject()
  @ApiPropertyOptional({ description: "The metadata for the reaction" })
  metadata?: Record<string, any>;
}

export class RemoveReactionDto extends AddReactionDto { }
