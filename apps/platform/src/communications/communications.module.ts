import { BullModule } from "@nestjs/bullmq";
import { forwardRef, Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { SentryService } from "@repo/nestjs-commons/filters";
import { ILogger } from "@repo/nestjs-commons/logger";
import { SNSPublisherService } from "@repo/thena-eventbridge";
import {
  Comment,
  CommentProcessorService,
  CommentRepository,
  EmojiActionMappingsRepository,
  EmojiActions,
  Emojis,
  EmojisRepository,
  Mentions,
  MentionsRepository,
  Reactions,
  ReactionsRepository,
  TransactionService,
} from "@repo/thena-platform-entities";
import { AccountsModule } from "../accounts/accounts.module";
import { ActivitiesModule } from "../activities/activities.module";
import { AuthModule } from "../auth/auth.module";
import { CommonModule } from "../common/common.module";
import { ConfigModule } from "../config/config.module";
import { ConfigKeys, ConfigService } from "../config/config.service";
import { QueueNames } from "../constants/queue.constants";
import { OrganizationModule } from "../organization/organization.module";
import { StorageModule } from "../storage/storage.module";
import { TeamsModule } from "../teams/teams.module";
import { TicketsModule } from "../tickets/tickets.module";
import { UsersModule } from "../users/users.module";
import {
  COMMENT_SNS_PUBLISHER,
  REACTION_SNS_PUBLISHER,
} from "./constants/comments.constants";
import { CommentsActionController } from "./controllers/comments.action.controller";
import { CommunicationsController } from "./controllers/communications.controller";
import { EmojiActionsController } from "./controllers/emoji-actions-mappings.action.controller";
import { CommentsGrpcController } from "./controllers/grpc/comments-grpc.controller";
import { ReactionsGrpcController } from "./controllers/grpc/reactions-grpc.controller";
import { ReactionsActionController } from "./controllers/reactions.action.controller";
import { CommentSnsConsumer } from "./processors/comment.sns-publish.processor";
import { ReactionSnsConsumer } from "./processors/reaction.sns-publish.processor";
import { EmojiActionRegistry } from "./registry/emoji-action.registry";
import { CommentsAnnotatorService } from "./services/comments-annotator.service";
import { CommentsUtilsService } from "./services/comments-utils.service";
import { CommentsActionService } from "./services/comments.action.service";
import { CommunicationsService } from "./services/communications.service";
import { TicketToClosedHandler } from "./services/emoji-actions-flows/ticket-status/ticket-to-closed.handler";
import { TicketToInProgressHandler } from "./services/emoji-actions-flows/ticket-status/ticket-to-in-progress.handler";
import { TicketToOnHoldHandler } from "./services/emoji-actions-flows/ticket-status/ticket-to-on-hold.handler";
import { EmojiActionsService } from "./services/emoji-actions-mappings.action.service";
import { ReactionsActionService } from "./services/reactions.action.service";
import { CommentResponseDto } from "./transformers/comment-response.transformer";

@Module({
  imports: [
    AuthModule,
    UsersModule,
    TypeOrmModule.forFeature([
      Comment,
      CommentRepository,
      Mentions,
      MentionsRepository,
      Reactions,
      ReactionsRepository,
      Emojis,
      EmojisRepository,
      EmojiActions,
      EmojiActionMappingsRepository,
    ]),
    CommonModule,
    TeamsModule,
    ActivitiesModule,
    OrganizationModule,
    StorageModule,
    forwardRef(() => TicketsModule),
    AccountsModule,
    BullModule.registerQueueAsync({
      name: QueueNames.COMMENT_SNS_PUBLISHER,
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        redis: {
          host: configService.get(ConfigKeys.REDIS_HOST),
          port: Number(configService.get(ConfigKeys.REDIS_PORT)),
          password: configService.get(ConfigKeys.REDIS_PASSWORD),
          username: configService.get(ConfigKeys.REDIS_USERNAME),
        },
        defaultJobOptions: {
          removeOnComplete: {
            age: 24 * 3600, // keep completed jobs for 24 hours
            count: 1000, // keep last 1000 completed jobs
          },
          removeOnFail: {
            age: 24 * 3600, // keep up to 24 hours
          },
        },
      }),
    }),

    BullModule.registerQueueAsync({
      name: QueueNames.REACTION_SNS_PUBLISHER,
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        redis: {
          host: configService.get(ConfigKeys.REDIS_HOST),
          port: Number(configService.get(ConfigKeys.REDIS_PORT)),
          password: configService.get(ConfigKeys.REDIS_PASSWORD),
          username: configService.get(ConfigKeys.REDIS_USERNAME),
        },
        defaultJobOptions: {
          removeOnComplete: {
            age: 24 * 3600, // keep completed jobs for 24 hours
            count: 1000, // keep last 1000 completed jobs
          },
          removeOnFail: {
            age: 24 * 3600, // keep up to 24 hours
          },
        },
      }),
    }),
  ],
  controllers: [
    CommunicationsController,
    CommentsActionController,
    ReactionsActionController,
    CommentsGrpcController,
    ReactionsGrpcController,
    EmojiActionsController,
  ],
  providers: [
    EmojiActionsService,
    TransactionService,
    EmojiActionMappingsRepository,
    CommentProcessorService,
    CommunicationsService,
    CommentRepository,
    MentionsRepository,
    CommentsActionService,
    ReactionsActionService,
    ReactionsRepository,
    EmojisRepository,
    CommentSnsConsumer,
    ReactionSnsConsumer,
    CommentsAnnotatorService,
    CommentsUtilsService,

    // Actions
    TicketToClosedHandler,
    TicketToInProgressHandler,
    TicketToOnHoldHandler,
    EmojiActionRegistry,

    CommentResponseDto,

    {
      provide: COMMENT_SNS_PUBLISHER,
      useFactory: (
        configService: ConfigService,
        sentryService: SentryService,
        loggerService: ILogger,
      ) => {
        return new SNSPublisherService(
          {
            topicArn: configService.get(ConfigKeys.AWS_SNS_TICKET_TOPIC_ARN),
            region: configService.get(ConfigKeys.AWS_REGION),
            credentials: {
              accessKeyId: configService.get(ConfigKeys.AWS_ACCESS_KEY),
              secretAccessKey: configService.get(ConfigKeys.AWS_SECRET_KEY),
            },
          },
          sentryService,
          loggerService,
        );
      },
      inject: [ConfigService, "Sentry", "CustomLogger"],
    },

    {
      provide: REACTION_SNS_PUBLISHER,
      useFactory: (
        configService: ConfigService,
        sentryService: SentryService,
        loggerService: ILogger,
      ) => {
        return new SNSPublisherService(
          {
            topicArn: configService.get(ConfigKeys.AWS_SNS_TICKET_TOPIC_ARN),
            region: configService.get(ConfigKeys.AWS_REGION),
            credentials: {
              accessKeyId: configService.get(ConfigKeys.AWS_ACCESS_KEY),
              secretAccessKey: configService.get(ConfigKeys.AWS_SECRET_KEY),
            },
          },
          sentryService,
          loggerService,
        );
      },
      inject: [ConfigService, "Sentry", "CustomLogger"],
    },

    {
      provide: "COMMUNICATIONS_SNS_PUBLISHER",
      useFactory: (
        configService: ConfigService,
        sentryService: SentryService,
        loggerService: ILogger,
      ) => {
        return new SNSPublisherService(
          {
            topicArn: configService.get(ConfigKeys.AWS_SNS_TICKET_TOPIC_ARN),
            region: configService.get(ConfigKeys.AWS_REGION),
            credentials: {
              accessKeyId: configService.get(ConfigKeys.AWS_ACCESS_KEY),
              secretAccessKey: configService.get(ConfigKeys.AWS_SECRET_KEY),
            },
          },
          sentryService,
          loggerService,
        );
      },
      inject: [ConfigService, "Sentry", "CustomLogger"],
    },
  ],
  exports: [
    CommunicationsService,
    ReactionsActionService,
    CommentRepository,
    CommentsActionService,
    CommentResponseDto,
  ],
})
export class CommunicationsModule {}
