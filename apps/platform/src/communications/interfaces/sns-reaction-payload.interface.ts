export interface SnsReactionPayload {
  eventId: string;
  eventType: string;
  timestamp: string;
  orgId: string;
  actor: {
    id: string;
    type: string;
    email: string;
  };
  payload: {
    reaction: {
      name: string;
      author: {
        id: string;
        name: string;
        avatarUrl: string;
        email: string;
      };
      metadata: Record<string, any>;
    };
    comment: {
      id: string;
    };
  };
}
