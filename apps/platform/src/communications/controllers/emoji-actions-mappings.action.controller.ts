import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  Inject,
  InternalServerErrorException,
  Param,
  Patch,
  Post,
  UseInterceptors,
} from "@nestjs/common";
import { ApiOperation, ApiTags } from "@nestjs/swagger";
import {
  ApiCreateEndpoint,
  ApiDeleteEndpoint,
  ApiGetEndpoint,
  ApiResponseMessage,
  ApiUpdateEndpoint,
  ResponseTransformInterceptor,
} from "@repo/nestjs-commons/decorators";
import { RequiredTier } from "@repo/nestjs-commons/guards/organization-tier/index";
import { CUSTOM_LOGGER_TOKEN, ILogger } from "@repo/nestjs-commons/logger";
import { OrganizationTier } from "@repo/thena-platform-entities";
import { CurrentUser } from "../../common/decorators";
import { SkipAllThrottler } from "../../common/decorators/throttler.decorator";
import {
  MapEmojisActionDTO,
  UnmapEmojiActionDTO,
  UpdateEmojiActionDTO,
  UpdateEmojiActionIndividual,
} from "../dto/emoji-actions.dto";
import { EmojiActionsService } from "../services/emoji-actions-mappings.action.service";

@SkipAllThrottler()
@ApiTags("Emoji Actions")
@Controller("v1/emojis/actions")
@UseInterceptors(ResponseTransformInterceptor)
export class EmojiActionsController {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // Services
    private readonly emojiActionService: EmojiActionsService,
  ) {}

  @Get()
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Emoji actions fetched successfully!")
  @ApiGetEndpoint({
    summary: "Get all emoji actions",
  })
  getAllEmojiActions() {
    try {
      const actions = this.emojiActionService.getAllEmojiActions();
      const names = actions.map((action) => action.name);
      return { ok: true, data: names };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      if (error instanceof Error) {
        this.logger.error("Failed to get all emoji actions", error.stack);
      } else {
        console.error("Failed to get all emoji actions", error);
      }

      throw new InternalServerErrorException("Failed to get all emoji actions");
    }
  }

  @Get("/:teamId")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Emoji actions fetched successfully!")
  @ApiGetEndpoint({
    summary: "Get all emoji actions for a team",
  })
  async getEmojiAction(
    @Param("teamId") teamId: string,
    @CurrentUser() currentUser: CurrentUser,
  ) {
    try {
      const action =
        await this.emojiActionService.getAllEmojiActionMappingsForTeam(
          teamId,
          currentUser,
        );

      return { ok: true, data: action };
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error("Failed to get emoji action", error.stack);
      } else {
        console.error("Failed to get emoji action", error);
      }

      if (error instanceof HttpException) {
        throw error;
      }

      throw new InternalServerErrorException("Failed to get emoji action");
    }
  }

  @Delete("/:teamId/:emojiActionId")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Emoji action deleted successfully!")
  @ApiDeleteEndpoint({
    summary: "Delete an emoji action by ID",
  })
  async deleteEmojiActionById(
    @CurrentUser() currentUser: CurrentUser,
    @Param("teamId") teamId: string,
    @Param("emojiActionId") emojiActionId: string,
  ) {
    try {
      // Validate the team ID
      if (!teamId) {
        throw new BadRequestException("Team ID is required");
      }

      // Validate the emoji action ID
      if (!emojiActionId) {
        throw new BadRequestException("Emoji action ID is required");
      }

      // Delete the emoji action
      await this.emojiActionService.deleteEmojiActionById(
        currentUser,
        emojiActionId,
        teamId,
      );
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error("Failed to update emoji action", error.stack);
      } else {
        console.error("Failed to update emoji action", error);
      }

      // If the error is an HTTP exception, throw it
      if (error instanceof HttpException) {
        throw error;
      }

      throw new InternalServerErrorException("Failed to update emoji action");
    }
  }

  @Patch("/:teamId/:emojiActionId")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Emoji action updated successfully!")
  @ApiUpdateEndpoint({
    summary: "Update an emoji action by ID",
  })
  async updateEmojiActionById(
    @Body() data: UpdateEmojiActionIndividual,
    @CurrentUser() currentUser: CurrentUser,
    @Param("teamId") teamId: string,
    @Param("emojiActionId") emojiActionId: string,
  ) {
    try {
      // Validate the team ID
      if (!teamId) {
        throw new BadRequestException("Team ID is required");
      }

      // Validate the emoji action ID
      if (!emojiActionId) {
        throw new BadRequestException("Emoji action ID is required");
      }

      // Update the emoji action
      const result = await this.emojiActionService.updateEmojiActionById(
        currentUser,
        emojiActionId,
        teamId,
        data,
      );

      return { ok: true, data: result };
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error("Failed to update emoji action", error.stack);
      } else {
        console.error("Failed to update emoji action", error);
      }

      // If the error is an HTTP exception, throw it
      if (error instanceof HttpException) {
        throw error;
      }

      throw new InternalServerErrorException("Failed to update emoji action");
    }
  }

  @Patch("/:teamId")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Emoji action updated successfully!")
  @ApiUpdateEndpoint({
    summary: "Update an emoji action",
  })
  async updateEmojiAction(
    @Body() data: UpdateEmojiActionDTO,
    @CurrentUser() currentUser: CurrentUser,
    @Param("teamId") teamId: string,
  ) {
    try {
      // Validate the team ID
      if (!teamId) {
        throw new BadRequestException("Team ID is required");
      }

      // Update the emoji action
      const result = await this.emojiActionService.updateEmojiAction(
        currentUser,
        data,
        teamId,
      );

      return { ok: true, data: result };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      if (error instanceof Error) {
        this.logger.error("Failed to update emoji action", error.stack);
      } else {
        console.error("Failed to update emoji action", error);
      }

      throw new InternalServerErrorException("Failed to update emoji action");
    }
  }

  @Patch("/:teamId/unmap")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Emoji action unmapped successfully!")
  @ApiUpdateEndpoint({
    summary: "Unmap an emoji action",
  })
  async deleteEmojiAction(
    @Body() data: UnmapEmojiActionDTO,
    @CurrentUser() currentUser: CurrentUser,
    @Param("teamId") teamId: string,
  ) {
    try {
      if (!teamId) {
        throw new BadRequestException("Team ID is required");
      }

      // Unmap the emoji action
      const result = await this.emojiActionService.unmapEmojiAction(
        currentUser,
        data,
        teamId,
      );

      return { ok: true, data: result };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      if (error instanceof Error) {
        this.logger.error("Failed to unmap emoji action", error.stack);
      } else {
        console.error("Failed to unmap emoji action", error);
      }

      throw new InternalServerErrorException("Failed to unmap emoji action");
    }
  }

  @Post("/:teamId")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Emoji action mapped successfully!")
  @ApiCreateEndpoint({
    summary: "Map an emoji action",
  })
  async mapEmojiAction(
    @Body() data: MapEmojisActionDTO,
    @CurrentUser() currentUser: CurrentUser,
    @Param("teamId") teamId: string,
  ) {
    try {
      if (!teamId) {
        throw new BadRequestException("Team ID is required");
      }

      // Map the emoji actions
      const mappedActions = await this.emojiActionService.mapEmojisToAction(
        currentUser,
        data,
        teamId,
      );

      return { ok: true, data: mappedActions };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      if (error instanceof Error) {
        this.logger.error("Failed to map emoji actions", error.stack);
      } else {
        console.error("Failed to map emoji actions", error);
      }

      throw new InternalServerErrorException("Failed to map emoji actions");
    }
  }
}
