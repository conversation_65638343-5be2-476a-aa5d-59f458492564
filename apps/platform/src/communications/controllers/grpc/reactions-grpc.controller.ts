import { Metadata } from "@grpc/grpc-js";
import { Controller, UseGuards } from "@nestjs/common";
import { GrpcMethod } from "@nestjs/microservices";
import { GrpcAuthGuard } from "@repo/nestjs-commons/guards";
import {
  extractUserMetadata,
  extractWorkflowContextFromMetadata,
} from "@repo/nestjs-commons/utils";
import { communication } from "@repo/shared-proto";
import { ReactionsActionService } from "../../services/reactions.action.service";

@Controller("v1/reactions")
@UseGuards(GrpcAuthGuard)
export class ReactionsGrpcController {
  constructor(private readonly reactionsService: ReactionsActionService) {}

  @GrpcMethod(communication.COMMUNICATION_SERVICE_NAME, "AddReaction")
  async addReaction(
    request: communication.AddReactionRequest,
    metadata: Metadata,
  ): Promise<communication.ReactionResponse> {
    const user = extractUserMetadata(metadata);

    const workflowContext = extractWorkflowContextFromMetadata(metadata);

    const success = await this.reactionsService.addReaction(
      request.commentId,
      { name: request.reactionName },
      user,
      workflowContext.workflowId
        ? { workflowExecutions: [workflowContext] }
        : {},
    );

    return { success };
  }

  @GrpcMethod(communication.COMMUNICATION_SERVICE_NAME, "RemoveReaction")
  async removeReaction(
    request: communication.RemoveReactionRequest,
    metadata: Metadata,
  ): Promise<communication.ReactionResponse> {
    const user = extractUserMetadata(metadata);

    const workflowContext = extractWorkflowContextFromMetadata(metadata);

    const success = await this.reactionsService.removeReaction(
      request.commentId,
      request.reactionName,
      user,
      workflowContext.workflowId
        ? { workflowExecutions: [workflowContext] }
        : {},
    );

    return { success };
  }
}
