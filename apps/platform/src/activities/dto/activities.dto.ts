import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
} from "class-validator";
import {
  AuditLogEntityType,
  AuditLogOp,
  AuditLogVisibility,
} from "@repo/thena-platform-entities";

export class CreateAuditLogDto {
  @ApiProperty({
    description: "Type of entity being audited",
    enum: AuditLogEntityType,
  })
  @IsEnum(AuditLogEntityType)
  @IsNotEmpty()
  entityType: AuditLogEntityType;

  @ApiProperty({
    description: "UID of the entity being audited",
  })
  @IsString()
  @IsNotEmpty()
  entityUid: string;

  @ApiProperty({
    description: "Operation performed on the entity",
    enum: AuditLogOp,
  })
  @IsEnum(AuditLogOp)
  @IsNotEmpty()
  op: AuditLogOp;

  @ApiProperty({
    description: "Visibility level of the audit log",
    enum: AuditLogVisibility,
  })
  @IsEnum(AuditLogVisibility)
  @IsNotEmpty()
  visibility: AuditLogVisibility;

  @ApiProperty({
    description: "Team ID associated with the audit log",
    required: false,
  })
  @IsString()
  @IsOptional()
  teamId?: string;

  @ApiProperty({
    description: "Additional metadata for the audit log",
    required: false,
    type: 'object',
  })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;

  @ApiProperty({
    description: "Main activity text",
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  activity: string;

  @ApiProperty({
    description: "Detailed description of the activity",
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: "Source of the activity",
    required: false,
  })
  @IsString()
  @IsOptional()
  source?: string;

  @ApiProperty({
    description: "Whether the activity was automated",
    required: false,
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  isAutomated?: boolean;

  @ApiProperty({
    description: "Organization performing the activity",
    required: true,
    type: 'string',
  })
  @IsString()
  @IsNotEmpty()
  organization: string;

  @ApiProperty({
    description: "User performing the activity",
    required: true,
    type: 'string',
  })
  @IsString()
  @IsNotEmpty()
  activityPerformedBy:string;
}

export class GetAuditLogsQueryDto {
  @ApiProperty({
    description: "Entity type filter",
    enum: AuditLogEntityType,
    required: false,
  })
  @IsEnum(AuditLogEntityType)
  @IsOptional()
  entityType?: AuditLogEntityType;

  @ApiProperty({
    description: "Entity ID filter",
    required: false,
  })
  @IsString()
  @IsOptional()
  entityId?: string;

  @ApiProperty({
    description: "Operation type filter",
    enum: AuditLogOp,
    required: false,
  })
  @IsEnum(AuditLogOp)
  @IsOptional()
  op?: AuditLogOp;

  @ApiProperty({
    description: "Team ID filter",
    required: false,
  })
  @IsString()
  @IsOptional()
  teamId?: string;

  @ApiProperty({
    description: "Page number for pagination",
    default: 1,
    required: false,
  })
  @Type(() => Number)
  @IsOptional()
  page?: number = 1;

  @ApiProperty({
    description: "Number of items per page",
    default: 10,
    required: false,
  })
  @Type(() => Number)
  @IsOptional()
  limit?: number = 10;
}
