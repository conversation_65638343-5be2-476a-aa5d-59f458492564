import { BadRequestException, Injectable } from "@nestjs/common";
import {
  AuditLog,
  AuditLogEntityType,
  AuditLogOp,
  AuditLogsRepository,
  TransactionContext,
  TransactionService,
} from "@repo/thena-platform-entities";
import { DeepPartial, FindOptionsWhere, MoreThanOrEqual } from "typeorm";

@Injectable()
export class ActivitiesService {
  constructor(
    private readonly transactionService: TransactionService,

    // Repositories
    private readonly auditLogsRepository: AuditLogsRepository,
  ) {}

  /**
   * Records an audit log.
   * @param log The audit log data to record.
   * @returns The recorded audit log data.
   */
  async recordAuditLog(
    log: DeepPartial<AuditLog>,
    existingTxnContext?: TransactionContext,
  ) {
    const auditLog = await this.transactionService.runInTransaction(
      async (txnContext) => {
        // Create the audit log data
        const auditLog = this.auditLogsRepository.create(log);

        // Save the audit log
        const savedAuditLog = await this.auditLogsRepository.saveWithTxn(
          txnContext,
          auditLog,
        );

        return savedAuditLog;
      },
      existingTxnContext,
    );

    return auditLog;
  }

  async recordBulkAuditLog(
    logs: DeepPartial<AuditLog>[],
    existingTxnContext?: TransactionContext,
  ) {
    return await this.transactionService.runInTransaction(
      async (txnContext) => {
        // Create the audit logs
        const auditLogs = logs.map((log) =>
          this.auditLogsRepository.create(log),
        );

        // Save the audit logs
        await this.auditLogsRepository.saveManyWithTxn(txnContext, auditLogs);
      },
      existingTxnContext,
    );
  }

  /**
   * Get audit logs for an entity.
   * @param entityType The type of entity to get audit logs for.
   * @param organizationId The ID of the organization to get audit logs for.
   * @param entityUid The UID of the entity to get audit logs for.
   * @param entityId The ID of the entity to get audit logs for.
   * @param op The operation to get audit logs for.
   * @param since The date to get audit logs for.
   * @returns The audit logs for the entity.
   */
  async getAuditLogs(
    entityType: AuditLogEntityType,
    organizationId: string,
    entityUid?: string,
    entityId?: string,
    op?: AuditLogOp,
    since?: Date,
  ): Promise<AuditLog[]> {
    if (!entityUid && !entityId) {
      throw new BadRequestException(
        "Either entityUid or entityId must be provided",
      );
    }

    return await this.auditLogsRepository.findAll({
      where: {
        organization: { id: organizationId },
        ...(entityUid && { entityUid }),
        ...(entityId && { entityId }),
        entityType,
        ...(op && { op }),
        ...(since && { createdAt: MoreThanOrEqual(since) }),
      },
      order: { createdAt: "DESC" },
    });
  }

  /**
   * Get audit logs with optional filtering
   * @param options Query options for filtering audit logs
   * @returns Audit logs and total count
   */
  async getAuditLogsPaginated(options: {
    organizationId: string;
    entityType?: AuditLogEntityType;
    entityId?: string;
    op?: AuditLogOp;
    teamId?: string;
    page?: number;
    limit?: number;
  }) {
    const {
      organizationId,
      entityType,
      entityId,
      op,
      teamId,
      page = 1,
      limit = 10,
    } = options;

    // Build where conditions
    const where: FindOptionsWhere<AuditLog> = {
      organization: { id: organizationId },
    };

    if (entityType) {
      where.entityType = entityType;
    }

    if (entityId) {
      where.entityId = entityId;
    }

    if (op) {
      where.op = op;
    }

    if (teamId) {
      where.team = { id: teamId };
    }

    // Get total count
    const total = await this.auditLogsRepository.count({
      where,
    });

    // Get paginated results
    const data = await this.auditLogsRepository.findAll({
      where,
      relations: ["organization", "activityPerformedBy", "team"],
      skip: (page - 1) * limit,
      take: limit,
      order: { createdAt: "DESC" },
    });

    return { data, total };
  }

  /**
   * Get a specific audit log by ID
   * @param id Audit log ID
   * @param organizationId Organization ID for security check
   * @returns The audit log if found
   */
  async getAuditLogById(id: string, organizationId: string) {
    const auditLog = await this.auditLogsRepository.findByCondition({
      where: {
        id,
        organization: { id: organizationId },
      },
      relations: ["organization", "activityPerformedBy", "team"],
    });

    if (!auditLog) {
      return null;
    }

    return auditLog;
  }
}
