import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import {
  AuditLog,
  AuditLogsRepository,
  TransactionService,
} from "@repo/thena-platform-entities";
import { AuthModule } from "../auth/auth.module";
import { CommonModule } from "../common/common.module";
import { SharedModule } from "../shared/shared.module";
import { ActivitiesController } from "./controllers/activities.controller";
import { ActivitiesGrpcController } from "./controllers/activities.grpc-controller";
import { ActivitiesService } from "./services/activities.service";
import { SharedService } from "../shared/shared.service";

@Module({
  imports: [
    CommonModule,
    AuthModule,
    TypeOrmModule.forFeature([AuditLog, AuditLogsRepository]),
    SharedModule,
  ],
  controllers: [ActivitiesGrpcController, ActivitiesController],
  providers: [ActivitiesService, TransactionService, AuditLogsRepository,SharedService],
  exports: [ActivitiesService],
})
export class ActivitiesModule {}
