import { Controller, UseGuards } from "@nestjs/common";
import { GrpcMethod } from "@nestjs/microservices";
import { handleRpcError } from "@repo/nestjs-commons/errors";
import { GrpcAuthGuard } from "@repo/nestjs-commons/guards";
import { activities } from "@repo/shared-proto";
import {
  AuditLog,
  AuditLogEntityType,
  AuditLogOp,
  AuditLogVisibility,
  Team,
  User,
} from "@repo/thena-platform-entities";
import { DeepPartial } from "typeorm";
import { SharedService } from "../../shared/shared.service";
import { ActivitiesService } from "../services/activities.service";

@Controller()
@UseGuards(GrpcAuthGuard)
export class ActivitiesGrpcController {
  constructor(
    private readonly activitiesService: ActivitiesService,
    private readonly sharedService: SharedService,
  ) {}

  @GrpcMethod(activities.ACTIVITIES_SERVICE_NAME, "RecordAuditLog")
  async recordAuditLog(
    request: activities.RecordAuditLogRequest,
  ): Promise<activities.RecordAuditLogResponse> {
    try {
      const organization = await this.sharedService.getOrganization(
        request.organizationId,
      );

      let team: Team;
      if (request.teamId) {
        team = await this.sharedService.getTeam(
          request.teamId,
          organization.id,
        );
      }

      let activityPerformedBy: User;
      if (request.activityPerformedById) {
        activityPerformedBy = await this.sharedService.getUser(
          request.activityPerformedById,
          organization.id,
        );
      }

      const log: DeepPartial<AuditLog> = {
        ...request,
        organization: organization ? { id: organization.id } : undefined,
        activityPerformedBy: activityPerformedBy
          ? { id: activityPerformedBy.id }
          : undefined,
        team: team ? { id: team.id } : undefined,
        entityType: request.entityType as AuditLogEntityType,
        visibility: request.visibility as AuditLogVisibility,
        op: request.op as AuditLogOp,
      };
      await this.activitiesService.recordAuditLog(log);
      return { success: true };
    } catch (error) {
      handleRpcError(error);
    }
  }
}
