import { ApiProperty } from "@nestjs/swagger";
import { AuditLog, AuditLogEntityType, AuditLogOp, AuditLogVisibility } from "@repo/thena-platform-entities";

export class AuditLogResponseDto {
  @ApiProperty({
    description: "Unique identifier for the audit log",
  })
  id: string;

  @ApiProperty({
    description: "Type of entity being audited",
    enum: AuditLogEntityType,
  })
  entityType: AuditLogEntityType;

  @ApiProperty({
    description: "ID of the entity being audited",
  })
  entityId: string;

  @ApiProperty({
    description: "Operation performed on the entity",
    enum: AuditLogOp,
  })
  op: AuditLogOp;

  @ApiProperty({
    description: "Visibility level of the audit log",
    enum: AuditLogVisibility,
  })
  visibility: AuditLogVisibility;

  @ApiProperty({
    description: "Description of the activity",
  })
  description: string;

  @ApiProperty({
    description: "Activity performed",
  })
  activity: string;

  @ApiProperty({
    description: "ID of the user who performed the activity",
  })
  activityPerformedById?: string;

  @ApiProperty({
    description: "Name of the user who performed the activity",
  })
  activityPerformedByName?: string;

  @ApiProperty({
    description: "ID of the team associated with the audit log",
  })
  teamId?: string;

  @ApiProperty({
    description: "Name of the team associated with the audit log",
  })
  teamName?: string;

  @ApiProperty({
    description: "ID of the organization associated with the audit log",
  })
  organizationId: string;

  @ApiProperty({
    description: "Additional metadata for the audit log",
    type: 'object',
  })
  metadata?: Record<string, any>;

  @ApiProperty({
    description: "Timestamp when the audit log was created",
  })
  createdAt: Date;

  @ApiProperty({
    description: "Timestamp when the audit log was last updated",
  })
  updatedAt: Date;

  static fromEntity(entity: AuditLog): AuditLogResponseDto {
    const dto = new AuditLogResponseDto();
    dto.id = entity.id;
    dto.entityType = entity.entityType;
    dto.entityId = entity.entityId;
    dto.op = entity.op;
    dto.visibility = entity.visibility;
    dto.description = entity.description;
    dto.activity = entity.activity;
    dto.metadata = entity.metadata;
    dto.createdAt = entity.createdAt;
    dto.updatedAt = entity.updatedAt;
    
    // Handle relations
    if (entity.activityPerformedBy) 
      { dto.activityPerformedById = entity.activityPerformedBy?.id; dto.activityPerformedByName = entity.activityPerformedBy?.name; } 
    
    if (entity.team) 
      { dto.teamId = entity.team?.id; dto.teamName = entity.team?.name; } 
    
    if (entity.organization) 
      { dto.organizationId = entity.organization?.id; }
    
    return dto;
  }
}
