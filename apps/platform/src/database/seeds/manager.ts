import { Injectable } from "@nestjs/common";
import { InjectDataSource } from "@nestjs/typeorm";
import { glob } from "glob";
import * as path from "node:path";
import { DataSource, QueryRunner } from "typeorm";

export interface Seed {
  id: string;
  name: string;
  timestamp: number;
  up(queryRunner: QueryRunner): Promise<void>;
  down?(queryRunner: QueryRunner): Promise<void>;
}

interface SeedRecord {
  id: string;
  name: string;
  executedAt: Date;
}

@Injectable()
export class SeedsManager {
  private readonly seedsTableName = "platform_seeds";
  private readonly seedsDirectory = path.join(__dirname);

  constructor(
    @InjectDataSource()
    private readonly dataSource: DataSource,
  ) {}

  /**
   * Initialize seeds tracking table if it doesn't exist
   */
  private async initializeSeedsTable(): Promise<void> {
    const tableExists = await this.dataSource.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = '${this.seedsTableName}'
      );
    `);

    if (!tableExists[0].exists) {
      await this.dataSource.query(`
        CREATE TABLE ${this.seedsTableName} (
          id VARCHAR(255) PRIMARY KEY,
          name VARCHAR(255) NOT NULL,
          executed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );
      `);
    }
  }

  /**
   * Get list of executed seeds
   */
  private async getExecutedSeeds(): Promise<SeedRecord[]> {
    const seeds = await this.dataSource.query(
      `SELECT * FROM ${this.seedsTableName} ORDER BY executed_at ASC;`,
    );
    return seeds;
  }

  /**
   * Get all seed files
   */
  private async getSeedFiles(): Promise<string[]> {
    const seedFiles = await glob("./sql/V[0-9]*-*.js", {
      cwd: this.seedsDirectory,
      absolute: true,
    });
    return seedFiles;
  }

  /**
   * Load seed instance from file
   */
  private async loadSeed(filePath: string): Promise<Seed> {
    const seedModule = await import(filePath);
    if (!seedModule.seed) {
      throw new Error(`Seed file ${filePath} must export a 'seed' instance`);
    }
    return seedModule.seed;
  }

  /**
   * Execute a single seed
   */
  private async executeSeed(seed: Seed): Promise<void> {
    // Start a transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Execute the seed
      await seed.up(queryRunner);

      // Record the execution
      await queryRunner.query(
        `INSERT INTO ${this.seedsTableName} (id, name) VALUES ($1, $2);`,
        [seed.id, seed.name],
      );

      // Commit the transaction
      await queryRunner.commitTransaction();
    } catch (error) {
      // Rollback on error
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      // Release the query runner
      await queryRunner.release();
    }
  }

  /**
   * Run all pending seeds
   */
  public async runSeeds(): Promise<void> {
    // Initialize seeds table
    await this.initializeSeedsTable();

    // Get executed seeds
    const executedSeeds = await this.getExecutedSeeds();
    const executedSeedIds = new Set(executedSeeds.map((seed) => seed.id));

    // Get all seed files
    const seedFiles = await this.getSeedFiles();
    seedFiles.sort(); // Ensure ordered execution

    // Load and execute pending seeds
    for (const filePath of seedFiles) {
      const seed = await this.loadSeed(filePath);

      if (!executedSeedIds.has(seed.id)) {
        try {
          await this.executeSeed(seed);
          console.log(`Successfully executed seed: ${seed.name}`);
        } catch (error) {
          console.error(`Failed to execute seed ${seed.name}:`, error);
          throw error;
        }
      }
    }
  }

  /**
   * Get seeds status
   */
  public async getSeedsStatus(): Promise<{
    executed: SeedRecord[];
    pending: Seed[];
  }> {
    await this.initializeSeedsTable();

    const [executedSeeds, seedFiles] = await Promise.all([
      this.getExecutedSeeds(),
      this.getSeedFiles(),
    ]);

    const executedSeedIds = new Set(executedSeeds.map((seed) => seed.id));
    const pendingSeeds = await Promise.all(
      seedFiles
        .filter(async (file) => {
          const seed = await this.loadSeed(file);
          return !executedSeedIds.has(seed.id);
        })
        .map((file) => this.loadSeed(file)),
    );

    return {
      executed: executedSeeds,
      pending: pendingSeeds,
    };
  }
}
