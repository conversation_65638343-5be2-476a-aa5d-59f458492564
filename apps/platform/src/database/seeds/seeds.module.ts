import { Mo<PERSON><PERSON>, OnApplicationBootstrap } from "@nestjs/common";
import { SeedsManager } from "./manager";
import { RunSeedsCommand } from "./seeds.command";

@Module({
  providers: [SeedsManager, RunSeedsCommand],
  exports: [SeedsManager],
})
export class SeedsModule implements OnApplicationBootstrap {
  constructor(private readonly seedsManager: SeedsManager) {}

  async onApplicationBootstrap() {
    // Only run seeds if enabled in config
    // Can possible move this to ENV but still feels like we can keep this on forever
    const shouldRunSeeds = true;

    if (shouldRunSeeds) {
      try {
        console.log("Running database seeds...");
        await this.seedsManager.runSeeds();
        console.log("Database seeds completed successfully");
      } catch (error) {
        console.error("Failed to run database seeds:", error);
        throw error;
      }
    }
  }
}
