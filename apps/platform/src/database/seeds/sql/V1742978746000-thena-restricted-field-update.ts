import { QueryRunner } from "typeorm";
import { Seed } from "../manager";

class UpdateThenaRestrictedField implements Seed {
  public id = "*************";
  public name = "UpdateThenaRestrictedField";
  public timestamp = *************;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      UPDATE "public"."thena_restricted_field"
      SET "accessible_in_ticket_creation_form" =
        CASE
          WHEN "name" IN ('Team', 'Submitter') THEN false
          ELSE true
        END,
        "type" = 'ticket_creation',
        "api_for_options" =
        CASE
          WHEN "name" = 'Assignee' THEN '/v1/users/list'
          WHEN "name" = 'Account' THEN '/v1/accounts'
          ELSE NULL
        END
      WHERE "type" IS NULL;
    `);

    await queryRunner.query(`
      UPDATE "public"."thena_restricted_field"
      SET "field_type" = 'choice',
          "options" = '[{"id": "bug", "value": "Bug"}, {"id": "feature-request", "value": "Feature Request"}, {"id": "question", "value": "Question"}, {"id": "task", "value": "Task"}]'
      WHERE "name" = 'Type';
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      UPDATE "public"."thena_restricted_field"
      SET "accessible_in_ticket_creation_form" = NULL,
          "api_for_options" = NULL
      WHERE "type" = 'ticket_creation';
    `);

    await queryRunner.query(`
      UPDATE "public"."thena_restricted_field"
      SET "field_type" = 'text',
          "options" = NULL
      WHERE "name" = 'Type';
    `);
  }
}

export const seed = new UpdateThenaRestrictedField();
