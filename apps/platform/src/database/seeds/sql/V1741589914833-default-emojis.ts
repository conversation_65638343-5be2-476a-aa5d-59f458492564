import { QueryRunner } from "typeorm";
import { Seed } from "../manager";

/**
 * We have/had a hosted emoji JSON which contains emojis for TipTap editor this was generated using that
 * @see https://dwua6jkk624sw.cloudfront.net/tiptap_emojis.json
 *
 * The reason why we have this written out here in a SQL Query is to prevent the case where we might end up
 * with either a mismatch in case the above URL is updated with more emojis or we might end up where the above
 * URL is no longer available.
 *
 * Therefore this is the safest option which also keeps the data clean and simple.
 * Please make sure you notify someone if you see that the above URL is no longer available. This doesn't impact this
 * seed in anyway but it's good to keep an eye on it.
 */

class DefaultEmojisSeed implements Seed {
  public id = "1741589914833";
  public name = "DefaultEmojisSeed";
  public timestamp = 1741589914833;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      -- Insert the default emojis
      INSERT INTO "emojis" ("name", "unicode", "keywords")
      VALUES
        ('100', '💯', Array ['100','score','perfect','numbers','century','exam','quiz','test','pass','100','hundred_points']),
        ('1234', '🔢', Array ['1234','blue','square','1','2','3','4','1234','input_numbers']),
        ('grinning', '😀', Array ['smile','happy','joy',':D','grin','grinning','grinning_face']),
        ('smiley', '😃', Array ['smiley','happy','joy','haha',':D',':)','smile','funny','smiley','grinning_face_with_big_eyes']),
        ('grin', '😁', Array ['grin','happy','smile','joy','kawaii','grin','beaming_face_with_smiling_eyes']),
        ('sweat_smile', '😅', Array ['smile','hot','happy','laugh','relief','sweat_smile','grinning_face_with_sweat']),
        ('rolling_on_the_floor_laughing', '🤣', Array ['face','lol','haha','rofl','rolling_on_the_floor_laughing','rolling_on_the_floor_laughing']),
        ('slightly_smiling_face', '🙂', Array ['smile','slightly_smiling_face','slightly_smiling_face']),
        ('upside_down_face', '🙃', Array ['upside','down','flipped','silly','smile','upside_down_face','upside-down_face']),
        ('melting_face', '🫠', Array ['hot','heat','melting_face','melting_face']),
        ('wink', '😉', Array ['wink','happy','mischievous','secret',';)','smile','eye','wink','winking_face']),
        ('laughing', '😆', Array ['laughing','satisfied','happy','joy','lol','haha','glad','XD','laugh','laughing','grinning_squinting_face']),
        ('innocent', '😇', Array ['innocent','angel','heaven','innocent','smiling_face_with_halo']),
        ('smiling_face_with_3_hearts', '🥰', Array ['3','love','like','affection','valentines','infatuation','crush','adore','smiling_face_with_3_hearts','smiling_face_with_hearts']),
        ('heart_eyes', '😍', Array ['heart','eyes','love','like','affection','valentines','infatuation','crush','heart_eyes','smiling_face_with_heart-eyes']),
        ('smile', '😄', Array ['smile','happy','joy','funny','haha','laugh','like',':D',':)','smile','grinning_face_with_smiling_eyes']),
        ('blush', '😊', Array ['blush','smile','happy','flushed','crush','embarrassed','shy','joy','blush','smiling_face_with_smiling_eyes']),
        ('joy', '😂', Array ['cry','weep','happy','happytears','haha','joy','face_with_tears_of_joy']),
        ('star-struck', '🤩', Array ['star','struck','grinning','face','with','eyes','smile','starry','star-struck','star-struck']),
        ('relaxed', '☺️', Array ['relaxed','blush','massage','happiness','relaxed','smiling_face']),
        ('kissing_smiling_eyes', '😙', Array ['affection','valentines','infatuation','kiss','kissing_smiling_eyes','kissing_face_with_smiling_eyes']),
        ('smiling_face_with_tear', '🥲', Array ['sad','cry','pretend','smiling_face_with_tear','smiling_face_with_tear']),
        ('kissing', '😗', Array ['love','like','3','valentines','infatuation','kiss','kissing','kissing_face']),
        ('stuck_out_tongue', '😛', Array ['stuck','out','prank','childish','playful','mischievous','smile','stuck_out_tongue','face_with_tongue']),
        ('zany_face', '🤪', Array ['grinning','with','one','large','and','small','eye','goofy','crazy','zany_face','zany_face']),
        ('stuck_out_tongue_winking_eye', '😜', Array ['stuck','out','eye','prank','childish','playful','mischievous','smile','wink','stuck_out_tongue_winking_eye','winking_face_with_tongue']),
        ('kissing_heart', '😘', Array ['kissing','heart','love','like','affection','valentines','infatuation','kissing_heart','face_blowing_a_kiss']),
        ('money_mouth_face', '🤑', Array ['money','mouth','rich','dollar','money_mouth_face','money-mouth_face']),
        ('hugging_face', '🤗', Array ['smile','hug','hugging_face','hugging_face']),
        ('face_with_hand_over_mouth', '🤭', Array ['smiling','eyes','and','covering','whoops','shock','surprise','face_with_hand_over_mouth','face_with_hand_over_mouth']),
        ('face_with_open_eyes_and_hand_over_mouth', '🫢', Array ['silence','secret','shock','surprise','face_with_open_eyes_and_hand_over_mouth','face_with_open_eyes_and_hand_over_mouth']),
        ('face_with_peeking_eye', '🫣', Array ['scared','frightening','embarrassing','shy','face_with_peeking_eye','face_with_peeking_eye']),
        ('yum', '😋', Array ['yum','happy','joy','tongue','smile','silly','yummy','nom','delicious','savouring','yum','face_savoring_food']),
        ('thinking_face', '🤔', Array ['hmmm','think','consider','thinking_face','thinking_face']),
        ('saluting_face', '🫡', Array ['respect','salute','saluting_face','saluting_face']),
        ('zipper_mouth_face', '🤐', Array ['zipper','mouth','sealed','secret','zipper_mouth_face','zipper-mouth_face']),
        ('face_with_raised_eyebrow', '🤨', Array ['one','distrust','scepticism','disapproval','disbelief','surprise','face_with_raised_eyebrow','face_with_raised_eyebrow']),
        ('stuck_out_tongue_closed_eyes', '😝', Array ['stuck','out','closed','eyes','prank','playful','mischievous','smile','stuck_out_tongue_closed_eyes','squinting_face_with_tongue']),
        ('expressionless', '😑', Array ['indifferent','-','','meh','deadpan','expressionless','expressionless_face']),
        ('no_mouth', '😶', Array ['no','hellokitty','no_mouth','face_without_mouth']),
        ('kissing_closed_eyes', '😚', Array ['love','like','affection','valentines','infatuation','kiss','kissing_closed_eyes','kissing_face_with_closed_eyes']),
        ('face_in_clouds', '😶‍🌫️', Array ['shower','steam','dream','face_in_clouds','face_in_clouds']),
        ('unamused', '😒', Array ['indifference','bored','straight','serious','sarcasm','unimpressed','skeptical','dubious','side','eye','unamused','unamused_face']),
        ('face_with_rolling_eyes', '🙄', Array ['eyeroll','frustrated','face_with_rolling_eyes','face_with_rolling_eyes']),
        ('grimacing', '😬', Array ['grimace','teeth','grimacing','grimacing_face']),
        ('face_exhaling', '😮‍💨', Array ['relieve','relief','tired','sigh','face_exhaling','face_exhaling']),
        ('lying_face', '🤥', Array ['lie','pinocchio','lying_face','lying_face']),
        ('relieved', '😌', Array ['relaxed','phew','massage','happiness','relieved','relieved_face']),
        ('pensive', '😔', Array ['sad','depressed','upset','pensive','pensive_face']),
        ('shushing_face', '🤫', Array ['with','finger','covering','closed','lips','quiet','shhh','shushing_face','shushing_face']),
        ('sleepy', '😪', Array ['tired','rest','nap','sleepy','sleepy_face']),
        ('drooling_face', '🤤', Array ['drooling_face','drooling_face']),
        ('sleeping', '😴', Array ['tired','sleepy','night','zzz','sleeping','sleeping_face']),
        ('mask', '😷', Array ['sick','ill','disease','covid','mask','face_with_medical_mask']),
        ('face_with_thermometer', '🤒', Array ['sick','temperature','cold','fever','covid','face_with_thermometer','face_with_thermometer']),
        ('face_with_head_bandage', '🤕', Array ['head','bandage','injured','clumsy','hurt','face_with_head_bandage','face_with_head-bandage']),
        ('dotted_line_face', '🫥', Array ['invisible','lonely','isolation','depression','dotted_line_face','dotted_line_face']),
        ('face_vomiting', '🤮', Array ['with','open','mouth','sick','face_vomiting','face_vomiting']),
        ('sneezing_face', '🤧', Array ['gesundheit','sneeze','sick','allergy','sneezing_face','sneezing_face']),
        ('hot_face', '🥵', Array ['feverish','heat','red','sweating','hot_face','hot_face']),
        ('cold_face', '🥶', Array ['blue','freezing','frozen','frostbite','icicles','cold_face','cold_face']),
        ('smirk', '😏', Array ['smirk','smile','mean','prank','smug','sarcasm','smirk','smirking_face']),
        ('dizzy_face', '😵', Array ['spent','unconscious','xox','dizzy_face','dizzy_face']),
        ('face_with_spiral_eyes', '😵‍💫', Array ['sick','ill','confused','nauseous','nausea','face_with_spiral_eyes','face_with_spiral_eyes']),
        ('exploding_head', '🤯', Array ['shocked','face','with','mind','blown','exploding_head','exploding_head']),
        ('face_with_cowboy_hat', '🤠', Array ['with','cowgirl','face_with_cowboy_hat','cowboy_hat_face']),
        ('neutral_face', '😐', Array ['indifference','meh',':','','neutral_face','neutral_face']),
        ('disguised_face', '🥸', Array ['pretent','brows','glasses','moustache','disguised_face','disguised_face']),
        ('sunglasses', '😎', Array ['cool','smile','summer','beach','sunglass','sunglasses','smiling_face_with_sunglasses']),
        ('nerd_face', '🤓', Array ['nerdy','geek','dork','nerd_face','nerd_face']),
        ('face_with_monocle', '🧐', Array ['stuffy','wealthy','face_with_monocle','face_with_monocle']),
        ('confused', '😕', Array ['indifference','huh','weird','hmmm',':/','confused','confused_face']),
        ('face_with_diagonal_mouth', '🫤', Array ['skeptic','confuse','frustrated','indifferent','face_with_diagonal_mouth','face_with_diagonal_mouth']),
        ('worried', '😟', Array ['concern','nervous',':(','worried','worried_face']),
        ('nauseated_face', '🤢', Array ['vomit','gross','green','sick','throw','up','ill','nauseated_face','nauseated_face']),
        ('white_frowning_face', '☹️', Array ['white','sad','upset','frown','white_frowning_face','frowning_face']),
        ('open_mouth', '😮', Array ['surprise','impressed','wow','whoa',':O','open_mouth','face_with_open_mouth']),
        ('hushed', '😯', Array ['woo','shh','hushed','hushed_face']),
        ('astonished', '😲', Array ['xox','surprised','poisoned','astonished','astonished_face']),
        ('partying_face', '🥳', Array ['celebration','woohoo','partying_face','partying_face']),
        ('pleading_face', '🥺', Array ['begging','mercy','cry','tears','sad','grievance','pleading_face','pleading_face']),
        ('face_holding_back_tears', '🥹', Array ['touched','gratitude','cry','face_holding_back_tears','face_holding_back_tears']),
        ('frowning', '😦', Array ['aw','what','frowning','frowning_face_with_open_mouth']),
        ('anguished', '😧', Array ['stunned','nervous','anguished','anguished_face']),
        ('woozy_face', '🥴', Array ['dizzy','intoxicated','tipsy','wavy','woozy_face','woozy_face']),
        ('cold_sweat', '😰', Array ['cold','nervous','cold_sweat','anxious_face_with_sweat']),
        ('disappointed_relieved', '😥', Array ['disappointed','phew','sweat','nervous','disappointed_relieved','sad_but_relieved_face']),
        ('cry', '😢', Array ['cry','tears','sad','depressed','upset','cry','crying_face']),
        ('sob', '😭', Array ['sob','cry','tears','sad','upset','depressed','sob','loudly_crying_face']),
        ('slightly_frowning_face', '🙁', Array ['disappointed','sad','upset','slightly_frowning_face','slightly_frowning_face']),
        ('confounded', '😖', Array ['confused','sick','unwell','oops',':S','confounded','confounded_face']),
        ('persevere', '😣', Array ['persevere','sick','no','upset','oops','persevere','persevering_face']),
        ('disappointed', '😞', Array ['sad','upset','depressed',':(','disappointed','disappointed_face']),
        ('sweat', '😓', Array ['downcast','hot','sad','tired','exercise','sweat','face_with_cold_sweat']),
        ('flushed', '😳', Array ['blush','shy','flattered','flushed','flushed_face']),
        ('tired_face', '😫', Array ['sick','whine','upset','frustrated','tired_face','tired_face']),
        ('yawning_face', '🥱', Array ['tired','sleepy','yawning_face','yawning_face']),
        ('triumph', '😤', Array ['steam','from','nose','gas','phew','proud','pride','triumph','face_with_look_of_triumph']),
        ('rage', '😡', Array ['rage','angry','mad','hate','despise','rage','pouting_face']),
        ('angry', '😠', Array ['mad','annoyed','frustrated','angry','angry_face']),
        ('weary', '😩', Array ['tired','sleepy','sad','frustrated','upset','weary','weary_face']),
        ('smiling_imp', '😈', Array ['imp','devil','smiling_imp','smiling_face_with_horns']),
        ('imp', '👿', Array ['angry','face','with','horns','devil','imp','imp']),
        ('skull', '💀', Array ['dead','skeleton','creepy','death','skull','skull']),
        ('skull_and_crossbones', '☠️', Array ['poison','danger','deadly','scary','death','pirate','evil','skull_and_crossbones','skull_and_crossbones']),
        ('fearful', '😨', Array ['scared','terrified','nervous','fearful','fearful_face']),
        ('clown_face', '🤡', Array ['clown_face','clown_face']),
        ('japanese_ogre', '👹', Array ['japanese','monster','red','mask','halloween','scary','creepy','devil','demon','japanese_ogre','ogre']),
        ('japanese_goblin', '👺', Array ['japanese','red','evil','mask','monster','scary','creepy','japanese_goblin','goblin']),
        ('ghost', '👻', Array ['halloween','spooky','scary','ghost','ghost']),
        ('scream', '😱', Array ['scream','munch','scared','omg','scream','face_screaming_in_fear']),
        ('space_invader', '👾', Array ['space','invader','game','arcade','play','space_invader','alien_monster']),
        ('robot_face', '🤖', Array ['face','computer','machine','bot','robot_face','robot']),
        ('smiley_cat', '😺', Array ['smiley','animal','cats','happy','smile','smiley_cat','grinning_cat']),
        ('alien', '👽', Array ['UFO','paul','weird','outer','space','alien','alien']),
        ('joy_cat', '😹', Array ['animal','cats','haha','happy','joy_cat','cat_with_tears_of_joy']),
        ('heart_eyes_cat', '😻', Array ['heart','eyes','animal','love','like','affection','cats','valentines','heart_eyes_cat','smiling_cat_with_heart-eyes']),
        ('smirk_cat', '😼', Array ['smirk','animal','cats','smirk_cat','cat_with_wry_smile']),
        ('kissing_cat', '😽', Array ['animal','cats','kiss','kissing_cat','kissing_cat']),
        ('scream_cat', '🙀', Array ['scream','animal','cats','munch','scared','scream_cat','weary_cat']),
        ('face_with_symbols_on_mouth', '🤬', Array ['serious','covering','swearing','cursing','cussing','profanity','expletive','face_with_symbols_on_mouth','face_with_symbols_on_mouth']),
        ('pouting_cat', '😾', Array ['animal','cats','pouting_cat','pouting_cat']),
        ('see_no_evil', '🙈', Array ['see','no','evil','animal','nature','haha','see_no_evil','see-no-evil_monkey']),
        ('hear_no_evil', '🙉', Array ['hear','no','evil','animal','nature','hear_no_evil','hear-no-evil_monkey']),
        ('speak_no_evil', '🙊', Array ['speak','no','evil','animal','nature','omg','speak_no_evil','speak-no-evil_monkey']),
        ('love_letter', '💌', Array ['email','like','affection','envelope','valentines','love_letter','love_letter']),
        ('cupid', '💘', Array ['cupid','love','like','affection','valentines','cupid','heart_with_arrow']),
        ('gift_heart', '💝', Array ['gift','love','valentines','gift_heart','heart_with_ribbon']),
        ('sparkling_heart', '💖', Array ['love','like','affection','valentines','sparkling_heart','sparkling_heart']),
        ('heartpulse', '💗', Array ['heartpulse','like','love','affection','valentines','pink','heartpulse','growing_heart']),
        ('heartbeat', '💓', Array ['heartbeat','love','like','affection','valentines','pink','heartbeat','beating_heart']),
        ('smile_cat', '😸', Array ['smile','animal','cats','smile_cat','grinning_cat_with_smiling_eyes']),
        ('two_hearts', '💕', Array ['love','like','affection','valentines','heart','two_hearts','two_hearts']),
        ('heart_decoration', '💟', Array ['purple','square','love','like','heart_decoration','heart_decoration']),
        ('heavy_heart_exclamation_mark_ornament', '❣️', Array ['heavy','mark','ornament','decoration','love','heavy_heart_exclamation_mark_ornament','heart_exclamation']),
        ('revolving_hearts', '💞', Array ['love','like','affection','valentines','revolving_hearts','revolving_hearts']),
        ('heart_on_fire', '❤️‍🔥', Array ['passionate','enthusiastic','heart_on_fire','heart_on_fire']),
        ('mending_heart', '❤️‍🩹', Array ['broken','bandage','wounded','mending_heart','mending_heart']),
        ('heart', '❤️', Array ['love','like','valentines','heart','red_heart']),
        ('orange_heart', '🧡', Array ['love','like','affection','valentines','orange_heart','orange_heart']),
        ('crying_cat_face', '😿', Array ['face','animal','tears','weep','sad','cats','upset','cry','crying_cat_face','crying_cat']),
        ('green_heart', '💚', Array ['love','like','affection','valentines','green_heart','green_heart']),
        ('blue_heart', '💙', Array ['love','like','affection','valentines','blue_heart','blue_heart']),
        ('purple_heart', '💜', Array ['love','like','affection','valentines','purple_heart','purple_heart']),
        ('brown_heart', '🤎', Array ['coffee','brown_heart','brown_heart']),
        ('black_heart', '🖤', Array ['evil','black_heart','black_heart']),
        ('white_heart', '🤍', Array ['pure','white_heart','white_heart']),
        ('kiss', '💋', Array ['face','lips','love','like','affection','valentines','kiss','kiss_mark']),
        ('anger', '💢', Array ['angry','mad','anger','anger_symbol']),
        ('boom', '💥', Array ['boom','bomb','explode','explosion','blown','boom','collision']),
        ('yellow_heart', '💛', Array ['love','like','affection','valentines','yellow_heart','yellow_heart']),
        ('sweat_drops', '💦', Array ['drops','water','drip','oops','sweat_drops','sweat_droplets']),
        ('dash', '💨', Array ['dashing','away','wind','air','fast','shoo','fart','smoke','puff','dash','dash_symbol']),
        ('hole', '🕳️', Array ['embarrassing','hole','hole']),
        ('speech_balloon', '💬', Array ['bubble','words','message','talk','chatting','speech_balloon','speech_balloon']),
        ('eye-in-speech-bubble', '👁️‍🗨️', Array ['in-speech-bubble','info','eye-in-speech-bubble','eye_in_speech_bubble']),
        ('left_speech_bubble', '🗨️', Array ['words','message','talk','chatting','left_speech_bubble','left_speech_bubble']),
        ('right_anger_bubble', '🗯️', Array ['caption','speech','thinking','mad','right_anger_bubble','right_anger_bubble']),
        ('thought_balloon', '💭', Array ['bubble','cloud','speech','thinking','dream','thought_balloon','thought_balloon']),
        ('zzz', '💤', Array ['sleepy','tired','dream','zzz','zzz']),
        ('dizzy', '💫', Array ['star','sparkle','shoot','magic','dizzy','dizzy']),
        ('raised_back_of_hand', '🤚', Array ['fingers','backhand','raised_back_of_hand','raised_back_of_hand']),
        ('raised_hand_with_fingers_splayed', '🖐️', Array ['raised','palm','raised_hand_with_fingers_splayed','hand_with_fingers_splayed']),
        ('hand', '✋', Array ['fingers','stop','highfive','high','five','palm','ban','hand','raised_hand']),
        ('spock-hand', '🖖', Array ['spock','hand','fingers','star','trek','spock-hand','vulcan_salute']),
        ('rightwards_hand', '🫱', Array ['palm','offer','rightwards_hand','rightwards_hand']),
        ('leftwards_hand', '🫲', Array ['palm','offer','leftwards_hand','leftwards_hand']),
        ('palm_down_hand', '🫳', Array ['drop','palm_down_hand','palm_down_hand']),
        ('palm_up_hand', '🫴', Array ['lift','offer','demand','palm_up_hand','palm_up_hand']),
        ('wave', '👋', Array ['wave','hands','gesture','goodbye','solong','farewell','hello','hi','palm','wave','waving_hand']),
        ('ok_hand', '👌', Array ['fingers','limbs','perfect','okay','ok_hand','ok_hand']),
        ('pinched_fingers', '🤌', Array ['size','tiny','small','pinched_fingers','pinched_fingers']),
        ('pinching_hand', '🤏', Array ['tiny','small','size','pinching_hand','pinching_hand']),
        ('broken_heart', '💔', Array ['sad','sorry','break','heartbreak','broken_heart','broken_heart']),
        ('crossed_fingers', '🤞', Array ['hand','with','index','and','middle','good','lucky','crossed_fingers','crossed_fingers']),
        ('hand_with_index_finger_and_thumb_crossed', '🫰', Array ['heart','love','money','expensive','hand_with_index_finger_and_thumb_crossed','hand_with_index_finger_and_thumb_crossed']),
        ('i_love_you_hand_sign', '🤟', Array ['i','love','you','hand','sign','fingers','i_love_you_hand_sign','love-you_gesture']),
        ('the_horns', '🤘', Array ['hand','fingers','evil','eye','rock','on','the_horns','sign_of_the_horns']),
        ('call_me_hand', '🤙', Array ['hands','gesture','shaka','call_me_hand','call_me_hand']),
        ('point_left', '👈', Array ['point','direction','fingers','hand','point_left','backhand_index_pointing_left']),
        ('point_up_2', '👆', Array ['point','2','fingers','hand','direction','point_up_2','backhand_index_pointing_up']),
        ('middle_finger', '🖕', Array ['reversed','hand','with','extended','fingers','rude','flipping','middle_finger','middle_finger']),
        ('point_down', '👇', Array ['point','fingers','hand','direction','point_down','backhand_index_pointing_down']),
        ('point_up', '☝️', Array ['point','hand','fingers','direction','point_up','index_pointing_up']),
        ('v', '✌️', Array ['v','fingers','ohyeah','peace','two','v','victory_hand']),
        ('+1', '👍', Array ['+1','thumbsup','yes','awesome','good','agree','accept','cool','hand','like','+1','thumbs_up']),
        ('-1', '👎', Array ['-1','thumbsdown','no','dislike','hand','-1','thumbs_down']),
        ('fist', '✊', Array ['fingers','hand','grasp','fist','raised_fist']),
        ('facepunch', '👊', Array ['facepunch','punch','angry','violence','hit','attack','hand','facepunch','oncoming_fist']),
        ('hankey', '💩', Array ['hankey','poop','shit','shitface','fail','turd','hankey','pile_of_poo']),
        ('right-facing_fist', '🤜', Array ['right','facing','hand','fistbump','right-facing_fist','right-facing_fist']),
        ('clap', '👏', Array ['clap','praise','applause','congrats','yay','clap','clapping_hands']),
        ('point_right', '👉', Array ['point','fingers','hand','direction','point_right','backhand_index_pointing_right']),
        ('raised_hands', '🙌', Array ['raised','gesture','hooray','yea','celebration','raised_hands','raising_hands']),
        ('open_hands', '👐', Array ['fingers','butterfly','open_hands','open_hands']),
        ('palms_up_together', '🤲', Array ['hands','gesture','cupped','prayer','palms_up_together','palms_up_together']),
        ('handshake', '🤝', Array ['agreement','shake','handshake','handshake']),
        ('pray', '🙏', Array ['pray','please','hope','wish','namaste','highfive','high','five','thank','you','thanks','appreciate','pray','folded_hands']),
        ('writing_hand', '✍️', Array ['lower','left','ballpoint','pen','stationery','write','compose','writing_hand','writing_hand']),
        ('nail_care', '💅', Array ['care','beauty','manicure','finger','fashion','nail_care','nail_polish']),
        ('selfie', '🤳', Array ['camera','phone','selfie','selfie']),
        ('index_pointing_at_the_viewer', '🫵', Array ['you','recruit','index_pointing_at_the_viewer','index_pointing_at_the_viewer']),
        ('mechanical_arm', '🦾', Array ['accessibility','mechanical_arm','mechanical_arm']),
        ('mechanical_leg', '🦿', Array ['accessibility','mechanical_leg','mechanical_leg']),
        ('leg', '🦵', Array ['kick','limb','leg','leg']),
        ('foot', '🦶', Array ['kick','stomp','foot','foot']),
        ('heart_hands', '🫶', Array ['love','appreciation','support','heart_hands','heart_hands']),
        ('ear_with_hearing_aid', '🦻', Array ['accessibility','ear_with_hearing_aid','ear_with_hearing_aid']),
        ('nose', '👃', Array ['smell','sniff','nose','nose']),
        ('brain', '🧠', Array ['smart','intelligent','brain','brain']),
        ('anatomical_heart', '🫀', Array ['health','heartbeat','anatomical_heart','anatomical_heart']),
        ('lungs', '🫁', Array ['breathe','lungs','lungs']),
        ('tooth', '🦷', Array ['teeth','dentist','tooth','tooth']),
        ('muscle', '💪', Array ['muscle','arm','flex','hand','summer','strong','muscle','flexed_biceps']),
        ('eyes', '👀', Array ['look','watch','stalk','peek','see','eyes','eyes']),
        ('eye', '👁️', Array ['face','look','see','watch','stare','eye','eye']),
        ('tongue', '👅', Array ['mouth','playful','tongue','tongue']),
        ('lips', '👄', Array ['lips','kiss','lips','mouth']),
        ('ear', '👂', Array ['face','hear','sound','listen','ear','ear']),
        ('baby', '👶', Array ['child','boy','girl','toddler','baby','baby']),
        ('child', '🧒', Array ['gender','neutral','young','child','child']),
        ('boy', '👦', Array ['man','male','guy','teenager','boy','boy']),
        ('girl', '👧', Array ['female','woman','teenager','girl','girl']),
        ('left-facing_fist', '🤛', Array ['left','facing','hand','fistbump','left-facing_fist','left-facing_fist']),
        ('man', '👨', Array ['mustache','father','dad','guy','classy','sir','moustache','man','man']),
        ('man_with_beard', '🧔‍♂️', Array ['man','with','facial','hair','man_with_beard','man:_beard']),
        ('woman_with_beard', '🧔‍♀️', Array ['woman','with','facial','hair','woman_with_beard','woman:_beard']),
        ('adult', '🧑', Array ['person','gender','neutral','adult','adult']),
        ('curly_haired_man', '👨‍🦱', Array ['haired','man','hairstyle','curly_haired_man','man:_curly_hair']),
        ('white_haired_man', '👨‍🦳', Array ['haired','man','old','elder','white_haired_man','man:_white_hair']),
        ('bald_man', '👨‍🦲', Array ['man','hairless','bald_man','man:_bald']),
        ('red_haired_man', '👨‍🦰', Array ['haired','man','hairstyle','red_haired_man','man:_red_hair']),
        ('red_haired_woman', '👩‍🦰', Array ['haired','woman','hairstyle','red_haired_woman','woman:_red_hair']),
        ('red_haired_person', '🧑‍🦰', Array ['haired','person','hairstyle','red_haired_person','person:_red_hair']),
        ('curly_haired_woman', '👩‍🦱', Array ['haired','woman','hairstyle','curly_haired_woman','woman:_curly_hair']),
        ('curly_haired_person', '🧑‍🦱', Array ['haired','person','hairstyle','curly_haired_person','person:_curly_hair']),
        ('woman', '👩', Array ['female','girls','lady','woman','woman']),
        ('white_haired_person', '🧑‍🦳', Array ['haired','person','elder','old','white_haired_person','person:_white_hair']),
        ('bald_woman', '👩‍🦲', Array ['woman','hairless','bald_woman','woman:_bald']),
        ('bald_person', '🧑‍🦲', Array ['person','hairless','bald_person','person:_bald']),
        ('blond-haired-woman', '👱‍♀️', Array ['haired-woman','woman','female','girl','blonde','person','blond-haired-woman','woman:_blond_hair']),
        ('blond-haired-man', '👱‍♂️', Array ['haired-man','man','male','boy','blonde','guy','person','blond-haired-man','man:_blond_hair']),
        ('older_adult', '🧓', Array ['person','human','elder','senior','gender','neutral','older_adult','older_adult']),
        ('older_man', '👴', Array ['older','human','male','men','elder','senior','older_man','old_man']),
        ('older_woman', '👵', Array ['older','human','female','women','lady','elder','senior','older_woman','old_woman']),
        ('man-frowning', '🙍‍♂️', Array ['male','boy','sad','depressed','discouraged','unhappy','man-frowning','man_frowning']),
        ('woman-frowning', '🙍‍♀️', Array ['female','girl','sad','depressed','discouraged','unhappy','woman-frowning','woman_frowning']),
        ('man-pouting', '🙎‍♂️', Array ['male','boy','man-pouting','man_pouting']),
        ('woman-pouting', '🙎‍♀️', Array ['female','girl','woman-pouting','woman_pouting']),
        ('man-gesturing-no', '🙅‍♂️', Array ['gesturing-no','male','boy','nope','man-gesturing-no','man_gesturing_no']),
        ('woman-gesturing-no', '🙅‍♀️', Array ['gesturing-no','female','girl','nope','woman-gesturing-no','woman_gesturing_no']),
        ('white_haired_woman', '👩‍🦳', Array ['haired','woman','old','elder','white_haired_woman','woman:_white_hair']),
        ('man-gesturing-ok', '🙆‍♂️', Array ['gesturing-ok','men','boy','male','blue','human','man-gesturing-ok','man_gesturing_ok']),
        ('woman-gesturing-ok', '🙆‍♀️', Array ['gesturing-ok','women','girl','female','pink','human','woman-gesturing-ok','woman_gesturing_ok']),
        ('man-tipping-hand', '💁‍♂️', Array ['tipping-hand','male','boy','human','information','man-tipping-hand','man_tipping_hand']),
        ('woman-tipping-hand', '💁‍♀️', Array ['tipping-hand','female','girl','human','information','woman-tipping-hand','woman_tipping_hand']),
        ('man-raising-hand', '🙋‍♂️', Array ['raising-hand','male','boy','man-raising-hand','man_raising_hand']),
        ('woman-raising-hand', '🙋‍♀️', Array ['raising-hand','female','girl','woman-raising-hand','woman_raising_hand']),
        ('deaf_man', '🧏‍♂️', Array ['accessibility','deaf_man','deaf_man']),
        ('deaf_woman', '🧏‍♀️', Array ['accessibility','deaf_woman','deaf_woman']),
        ('bow', '🙇', Array ['male','boy','bow','bowing']),
        ('man-bowing', '🙇‍♂️', Array ['male','boy','man-bowing','man_bowing']),
        ('woman-bowing', '🙇‍♀️', Array ['female','girl','woman-bowing','woman_bowing']),
        ('man-facepalming', '🤦‍♂️', Array ['male','boy','disbelief','man-facepalming','man_facepalming']),
        ('biting_lip', '🫦', Array ['flirt','sexy','pain','worry','biting_lip','biting_lip']),
        ('man-shrugging', '🤷‍♂️', Array ['male','boy','confused','indifferent','doubt','man-shrugging','man_shrugging']),
        ('woman-facepalming', '🤦‍♀️', Array ['female','girl','disbelief','woman-facepalming','woman_facepalming']),
        ('health_worker', '🧑‍⚕️', Array ['hospital','health_worker','health_worker']),
        ('male-doctor', '👨‍⚕️', Array ['male','doctor','nurse','therapist','healthcare','human','male-doctor','man_health_worker']),
        ('female-doctor', '👩‍⚕️', Array ['female','doctor','nurse','therapist','healthcare','human','female-doctor','woman_health_worker']),
        ('student', '🧑‍🎓', Array ['learn','student','student']),
        ('bone', '🦴', Array ['skeleton','bone','bone']),
        ('female-student', '👩‍🎓', Array ['female','graduate','human','female-student','woman_student']),
        ('teacher', '🧑‍🏫', Array ['professor','teacher','teacher']),
        ('male-teacher', '👨‍🏫', Array ['male','instructor','professor','human','male-teacher','man_teacher']),
        ('female-teacher', '👩‍🏫', Array ['female','instructor','professor','human','female-teacher','woman_teacher']),
        ('male-judge', '👨‍⚖️', Array ['male','justice','court','human','male-judge','man_judge']),
        ('female-judge', '👩‍⚖️', Array ['female','justice','court','human','female-judge','woman_judge']),
        ('farmer', '🧑‍🌾', Array ['crops','farmer','farmer']),
        ('judge', '🧑‍⚖️', Array ['law','judge','judge']),
        ('female-farmer', '👩‍🌾', Array ['female','rancher','gardener','human','female-farmer','woman_farmer']),
        ('cook', '🧑‍🍳', Array ['food','kitchen','culinary','cook','cook']),
        ('male-cook', '👨‍🍳', Array ['male','chef','human','male-cook','man_cook']),
        ('woman-shrugging', '🤷‍♀️', Array ['female','girl','confused','indifferent','doubt','woman-shrugging','woman_shrugging']),
        ('mechanic', '🧑‍🔧', Array ['worker','technician','mechanic','mechanic']),
        ('male-mechanic', '👨‍🔧', Array ['male','plumber','human','wrench','male-mechanic','man_mechanic']),
        ('female-mechanic', '👩‍🔧', Array ['female','plumber','human','wrench','female-mechanic','woman_mechanic']),
        ('factory_worker', '🧑‍🏭', Array ['labor','factory_worker','factory_worker']),
        ('female-cook', '👩‍🍳', Array ['female','chef','human','female-cook','woman_cook']),
        ('female-factory-worker', '👩‍🏭', Array ['female','factory-worker','assembly','industrial','human','female-factory-worker','woman_factory_worker']),
        ('office_worker', '🧑‍💼', Array ['business','office_worker','office_worker']),
        ('male-office-worker', '👨‍💼', Array ['male','office-worker','business','manager','human','male-office-worker','man_office_worker']),
        ('female-office-worker', '👩‍💼', Array ['female','office-worker','business','manager','human','female-office-worker','woman_office_worker']),
        ('scientist', '🧑‍🔬', Array ['chemistry','scientist','scientist']),
        ('male-scientist', '👨‍🔬', Array ['male','biologist','chemist','engineer','physicist','human','male-scientist','man_scientist']),
        ('female-scientist', '👩‍🔬', Array ['female','biologist','chemist','engineer','physicist','human','female-scientist','woman_scientist']),
        ('technologist', '🧑‍💻', Array ['computer','technologist','technologist']),
        ('male-technologist', '👨‍💻', Array ['male','coder','developer','engineer','programmer','software','human','laptop','computer','male-technologist','man_technologist']),
        ('male-student', '👨‍🎓', Array ['male','graduate','human','male-student','man_student']),
        ('male-farmer', '👨‍🌾', Array ['male','rancher','gardener','human','male-farmer','man_farmer']),
        ('male-singer', '👨‍🎤', Array ['male','rockstar','entertainer','human','male-singer','man_singer']),
        ('female-singer', '👩‍🎤', Array ['female','rockstar','entertainer','human','female-singer','woman_singer']),
        ('artist', '🧑‍🎨', Array ['painting','draw','creativity','artist','artist']),
        ('male-artist', '👨‍🎨', Array ['male','painter','human','male-artist','man_artist']),
        ('female-artist', '👩‍🎨', Array ['female','painter','human','female-artist','woman_artist']),
        ('pilot', '🧑‍✈️', Array ['fly','plane','airplane','pilot','pilot']),
        ('male-pilot', '👨‍✈️', Array ['male','aviator','plane','human','male-pilot','man_pilot']),
        ('female-pilot', '👩‍✈️', Array ['female','aviator','plane','human','female-pilot','woman_pilot']),
        ('astronaut', '🧑‍🚀', Array ['outerspace','astronaut','astronaut']),
        ('male-astronaut', '👨‍🚀', Array ['male','space','rocket','human','male-astronaut','man_astronaut']),
        ('female-astronaut', '👩‍🚀', Array ['female','space','rocket','human','female-astronaut','woman_astronaut']),
        ('firefighter', '🧑‍🚒', Array ['fire','firefighter','firefighter']),
        ('male-firefighter', '👨‍🚒', Array ['male','fireman','human','male-firefighter','man_firefighter']),
        ('female-firefighter', '👩‍🚒', Array ['female','fireman','human','female-firefighter','woman_firefighter']),
        ('male-police-officer', '👮‍♂️', Array ['male','police-officer','law','legal','enforcement','arrest','911','male-police-officer','man_police_officer']),
        ('singer', '🧑‍🎤', Array ['song','artist','performer','singer','singer']),
        ('male-detective', '🕵️‍♂️', Array ['male','crime','male-detective','man_detective']),
        ('female-detective', '🕵️‍♀️', Array ['female','human','spy','female-detective','woman_detective']),
        ('male-guard', '💂‍♂️', Array ['male','uk','gb','british','guy','royal','male-guard','man_guard']),
        ('male-factory-worker', '👨‍🏭', Array ['male','factory-worker','assembly','industrial','human','male-factory-worker','man_factory_worker']),
        ('ninja', '🥷', Array ['ninjutsu','skills','japanese','ninja','ninja']),
        ('male-construction-worker', '👷‍♂️', Array ['male','construction-worker','human','wip','guy','build','labor','male-construction-worker','man_construction_worker']),
        ('female-police-officer', '👮‍♀️', Array ['female','police-officer','law','legal','enforcement','arrest','911','female-police-officer','woman_police_officer']),
        ('person_with_crown', '🫅', Array ['royalty','power','person_with_crown','person_with_crown']),
        ('prince', '🤴', Array ['boy','man','male','crown','royal','king','prince','prince']),
        ('princess', '👸', Array ['girl','woman','female','blond','crown','royal','queen','princess','princess']),
        ('man-wearing-turban', '👳‍♂️', Array ['wearing-turban','male','indian','hinduism','arabs','man-wearing-turban','man_wearing_turban']),
        ('female-construction-worker', '👷‍♀️', Array ['female','construction-worker','human','wip','build','labor','female-construction-worker','woman_construction_worker']),
        ('man_with_gua_pi_mao', '👲', Array ['skullcap','male','boy','chinese','man_with_gua_pi_mao','man_with_gua_pi_mao']),
        ('person_with_headscarf', '🧕', Array ['person','female','hijab','mantilla','tichel','person_with_headscarf','woman_with_headscarf']),
        ('man_in_tuxedo', '🤵‍♂️', Array ['formal','fashion','man_in_tuxedo','man_in_tuxedo']),
        ('woman_in_tuxedo', '🤵‍♀️', Array ['formal','fashion','woman_in_tuxedo','woman_in_tuxedo']),
        ('woman-wearing-turban', '👳‍♀️', Array ['wearing-turban','female','indian','hinduism','arabs','woman-wearing-turban','woman_wearing_turban']),
        ('man_with_veil', '👰‍♂️', Array ['wedding','marriage','man_with_veil','man_with_veil']),
        ('woman_with_veil', '👰‍♀️', Array ['wedding','marriage','woman_with_veil','woman_with_veil']),
        ('pregnant_woman', '🤰', Array ['baby','pregnant_woman','pregnant_woman']),
        ('pregnant_man', '🫃', Array ['baby','belly','pregnant_man','pregnant_man']),
        ('pregnant_person', '🫄', Array ['baby','belly','pregnant_person','pregnant_person']),
        ('breast-feeding', '🤱', Array ['breast','feeding','nursing','baby','breast-feeding','breast-feeding']),
        ('woman_feeding_baby', '👩‍🍼', Array ['birth','food','woman_feeding_baby','woman_feeding_baby']),
        ('man_feeding_baby', '👨‍🍼', Array ['birth','food','man_feeding_baby','man_feeding_baby']),
        ('person_feeding_baby', '🧑‍🍼', Array ['birth','food','person_feeding_baby','person_feeding_baby']),
        ('female-technologist', '👩‍💻', Array ['female','coder','developer','engineer','programmer','software','human','laptop','computer','female-technologist','woman_technologist']),
        ('santa', '🎅', Array ['festival','man','male','xmas','father','christmas','santa','santa_claus']),
        ('mrs_claus', '🤶', Array ['mrs','mother','christmas','woman','female','xmas','mrs_claus','mrs._claus']),
        ('mx_claus', '🧑‍🎄', Array ['christmas','mx_claus','mx_claus']),
        ('male_superhero', '🦸‍♂️', Array ['male','good','hero','superpowers','male_superhero','man_superhero']),
        ('female_superhero', '🦸‍♀️', Array ['female','good','heroine','superpowers','female_superhero','woman_superhero']),
        ('male_supervillain', '🦹‍♂️', Array ['male','evil','bad','criminal','hero','superpowers','male_supervillain','man_supervillain']),
        ('female_supervillain', '🦹‍♀️', Array ['female','evil','bad','criminal','heroine','superpowers','female_supervillain','woman_supervillain']),
        ('angel', '👼', Array ['heaven','wings','halo','angel','baby_angel']),
        ('male_mage', '🧙‍♂️', Array ['male','sorcerer','male_mage','man_mage']),
        ('female_mage', '🧙‍♀️', Array ['female','witch','female_mage','woman_mage']),
        ('male_fairy', '🧚‍♂️', Array ['male','male_fairy','man_fairy']),
        ('female_fairy', '🧚‍♀️', Array ['female','female_fairy','woman_fairy']),
        ('male_vampire', '🧛‍♂️', Array ['male','dracula','male_vampire','man_vampire']),
        ('female-guard', '💂‍♀️', Array ['female','uk','gb','british','royal','female-guard','woman_guard']),
        ('mermaid', '🧜‍♀️', Array ['woman','female','merwoman','ariel','mermaid','mermaid']),
        ('male_elf', '🧝‍♂️', Array ['male','male_elf','man_elf']),
        ('female_elf', '🧝‍♀️', Array ['female','female_elf','woman_elf']),
        ('male_genie', '🧞‍♂️', Array ['male','male_genie','man_genie']),
        ('female_genie', '🧞‍♀️', Array ['female','female_genie','woman_genie']),
        ('male_zombie', '🧟‍♂️', Array ['male','dracula','undead','walking','dead','male_zombie','man_zombie']),
        ('female_zombie', '🧟‍♀️', Array ['female','undead','walking','dead','female_zombie','woman_zombie']),
        ('troll', '🧌', Array ['mystical','monster','troll','troll']),
        ('man-getting-massage', '💆‍♂️', Array ['getting-massage','male','boy','head','man-getting-massage','man_getting_massage']),
        ('woman-getting-massage', '💆‍♀️', Array ['getting-massage','female','girl','head','woman-getting-massage','woman_getting_massage']),
        ('man-getting-haircut', '💇‍♂️', Array ['getting-haircut','male','boy','man-getting-haircut','man_getting_haircut']),
        ('woman-getting-haircut', '💇‍♀️', Array ['getting-haircut','female','girl','woman-getting-haircut','woman_getting_haircut']),
        ('man-walking', '🚶‍♂️', Array ['human','feet','steps','man-walking','man_walking']),
        ('woman-walking', '🚶‍♀️', Array ['human','feet','steps','female','woman-walking','woman_walking']),
        ('man_standing', '🧍‍♂️', Array ['still','man_standing','man_standing']),
        ('woman_standing', '🧍‍♀️', Array ['still','woman_standing','woman_standing']),
        ('man_kneeling', '🧎‍♂️', Array ['pray','respectful','man_kneeling','man_kneeling']),
        ('woman_kneeling', '🧎‍♀️', Array ['respectful','pray','woman_kneeling','woman_kneeling']),
        ('person_with_probing_cane', '🧑‍🦯', Array ['probing','blind','person_with_probing_cane','person_with_white_cane']),
        ('man_with_probing_cane', '👨‍🦯', Array ['probing','blind','man_with_probing_cane','man_with_white_cane']),
        ('woman_with_probing_cane', '👩‍🦯', Array ['probing','blind','woman_with_probing_cane','woman_with_white_cane']),
        ('person_in_motorized_wheelchair', '🧑‍🦼', Array ['disability','accessibility','person_in_motorized_wheelchair','person_in_motorized_wheelchair']),
        ('man_in_motorized_wheelchair', '👨‍🦼', Array ['disability','accessibility','man_in_motorized_wheelchair','man_in_motorized_wheelchair']),
        ('woman_in_motorized_wheelchair', '👩‍🦼', Array ['disability','accessibility','woman_in_motorized_wheelchair','woman_in_motorized_wheelchair']),
        ('person_in_manual_wheelchair', '🧑‍🦽', Array ['disability','accessibility','person_in_manual_wheelchair','person_in_manual_wheelchair']),
        ('man_in_manual_wheelchair', '👨‍🦽', Array ['disability','accessibility','man_in_manual_wheelchair','man_in_manual_wheelchair']),
        ('woman_in_manual_wheelchair', '👩‍🦽', Array ['disability','accessibility','woman_in_manual_wheelchair','woman_in_manual_wheelchair']),
        ('man-running', '🏃‍♂️', Array ['walking','exercise','race','man-running','man_running']),
        ('woman-running', '🏃‍♀️', Array ['walking','exercise','race','female','woman-running','woman_running']),
        ('female_vampire', '🧛‍♀️', Array ['female','female_vampire','woman_vampire']),
        ('man_dancing', '🕺', Array ['male','boy','fun','dancer','man_dancing','man_dancing']),
        ('man_in_business_suit_levitating', '🕴️', Array ['man','business','levitate','hover','jump','man_in_business_suit_levitating','person_in_suit_levitating']),
        ('men-with-bunny-ears-partying', '👯‍♂️', Array ['with-bunny-ears-partying','man','male','boys','men-with-bunny-ears-partying','men_with_bunny_ears']),
        ('women-with-bunny-ears-partying', '👯‍♀️', Array ['with-bunny-ears-partying','woman','female','girls','women-with-bunny-ears-partying','women_with_bunny_ears']),
        ('dancer', '💃', Array ['woman','dancing','female','girl','fun','dancer','dancer']),
        ('man_in_steamy_room', '🧖‍♂️', Array ['male','spa','steamroom','sauna','man_in_steamy_room','man_in_steamy_room']),
        ('woman_in_steamy_room', '🧖‍♀️', Array ['female','spa','steamroom','sauna','woman_in_steamy_room','woman_in_steamy_room']),
        ('man_climbing', '🧗‍♂️', Array ['sports','hobby','male','rock','man_climbing','man_climbing']),
        ('woman_climbing', '🧗‍♀️', Array ['sports','hobby','female','rock','woman_climbing','woman_climbing']),
        ('fencer', '🤺', Array ['person','fencing','sports','sword','fencer','fencer']),
        ('horse_racing', '🏇', Array ['animal','betting','competition','gambling','luck','horse_racing','horse_racing']),
        ('snowboarder', '🏂', Array ['sports','winter','snowboarder','snowboarder']),
        ('skier', '⛷️', Array ['sports','winter','snow','skier','skier']),
        ('woman-golfing', '🏌️‍♀️', Array ['sports','business','female','woman-golfing','woman_golfing']),
        ('man-surfing', '🏄‍♂️', Array ['sports','ocean','sea','summer','beach','man-surfing','man_surfing']),
        ('woman-surfing', '🏄‍♀️', Array ['sports','ocean','sea','summer','beach','female','woman-surfing','woman_surfing']),
        ('man-golfing', '🏌️‍♂️', Array ['sport','man-golfing','man_golfing']),
        ('woman-rowing-boat', '🚣‍♀️', Array ['rowing-boat','sports','hobby','water','ship','female','woman-rowing-boat','woman_rowing_boat']),
        ('merman', '🧜‍♂️', Array ['man','male','triton','merman','merman']),
        ('man-bouncing-ball', '⛹️‍♂️', Array ['bouncing-ball','sport','man-bouncing-ball','man_bouncing_ball']),
        ('woman-bouncing-ball', '⛹️‍♀️', Array ['bouncing-ball','sports','human','female','woman-bouncing-ball','woman_bouncing_ball']),
        ('man-lifting-weights', '🏋️‍♂️', Array ['lifting-weights','sport','man-lifting-weights','man_lifting_weights']),
        ('woman-lifting-weights', '🏋️‍♀️', Array ['lifting-weights','sports','training','exercise','female','woman-lifting-weights','woman_lifting_weights']),
        ('man-biking', '🚴‍♂️', Array ['sports','bike','exercise','hipster','man-biking','man_biking']),
        ('woman-biking', '🚴‍♀️', Array ['sports','bike','exercise','hipster','female','woman-biking','woman_biking']),
        ('man-mountain-biking', '🚵‍♂️', Array ['mountain-biking','transportation','sports','human','race','bike','man-mountain-biking','man_mountain_biking']),
        ('woman-mountain-biking', '🚵‍♀️', Array ['mountain-biking','transportation','sports','human','race','bike','female','woman-mountain-biking','woman_mountain_biking']),
        ('man-cartwheeling', '🤸‍♂️', Array ['gymnastics','man-cartwheeling','man_cartwheeling']),
        ('woman-cartwheeling', '🤸‍♀️', Array ['gymnastics','woman-cartwheeling','woman_cartwheeling']),
        ('man-wrestling', '🤼‍♂️', Array ['man','sports','wrestlers','man-wrestling','men_wrestling']),
        ('woman-wrestling', '🤼‍♀️', Array ['woman','sports','wrestlers','woman-wrestling','women_wrestling']),
        ('man-playing-water-polo', '🤽‍♂️', Array ['playing-water-polo','sports','pool','man-playing-water-polo','man_playing_water_polo']),
        ('woman-playing-water-polo', '🤽‍♀️', Array ['playing-water-polo','sports','pool','woman-playing-water-polo','woman_playing_water_polo']),
        ('man-playing-handball', '🤾‍♂️', Array ['playing-handball','sports','man-playing-handball','man_playing_handball']),
        ('woman-playing-handball', '🤾‍♀️', Array ['playing-handball','sports','woman-playing-handball','woman_playing_handball']),
        ('man-juggling', '🤹‍♂️', Array ['juggle','balance','skill','multitask','man-juggling','man_juggling']),
        ('woman-juggling', '🤹‍♀️', Array ['juggle','balance','skill','multitask','woman-juggling','woman_juggling']),
        ('man_in_lotus_position', '🧘‍♂️', Array ['male','meditation','yoga','serenity','zen','mindfulness','man_in_lotus_position','man_in_lotus_position']),
        ('woman_in_lotus_position', '🧘‍♀️', Array ['female','meditation','yoga','serenity','zen','mindfulness','woman_in_lotus_position','woman_in_lotus_position']),
        ('bath', '🛀', Array ['person','taking','clean','shower','bathroom','bath','bath']),
        ('sleeping_accommodation', '🛌', Array ['sleeping','accommodation','rest','sleeping_accommodation','person_in_bed']),
        ('people_holding_hands', '🧑‍🤝‍🧑', Array ['friendship','people_holding_hands','people_holding_hands']),
        ('two_women_holding_hands', '👭', Array ['two','pair','friendship','couple','love','like','female','people','human','two_women_holding_hands','women_holding_hands']),
        ('man_and_woman_holding_hands', '👫', Array ['couple','pair','people','human','love','date','dating','like','affection','valentines','marriage','man_and_woman_holding_hands','man_and_woman_holding_hands']),
        ('two_men_holding_hands', '👬', Array ['two','pair','couple','love','like','bromance','friendship','people','human','two_men_holding_hands','men_holding_hands']),
        ('couplekiss', '💏', Array ['couplekiss','pair','valentines','love','like','dating','marriage','couplekiss','kiss']),
        ('woman-kiss-man', '👩‍❤️‍💋‍👨', Array ['woman','kiss-man','kiss','love','woman-kiss-man','kiss:_woman,_man']),
        ('woman-swimming', '🏊‍♀️', Array ['sports','exercise','human','athlete','water','summer','female','woman-swimming','woman_swimming']),
        ('woman-kiss-woman', '👩‍❤️‍💋‍👩', Array ['kiss-woman','kiss','pair','valentines','love','like','dating','marriage','woman-kiss-woman','kiss:_woman,_woman']),
        ('couple_with_heart', '💑', Array ['pair','love','like','affection','human','dating','valentines','marriage','couple_with_heart','couple_with_heart']),
        ('woman-heart-man', '👩‍❤️‍👨', Array ['woman','heart-man','heart','love','woman-heart-man','couple_with_heart:_woman,_man']),
        ('man-heart-man', '👨‍❤️‍👨', Array ['heart-man','heart','pair','love','like','affection','human','dating','valentines','marriage','man-heart-man','couple_with_heart:_man,_man']),
        ('woman-heart-woman', '👩‍❤️‍👩', Array ['heart-woman','heart','pair','love','like','affection','human','dating','valentines','marriage','woman-heart-woman','couple_with_heart:_woman,_woman']),
        ('man-swimming', '🏊‍♂️', Array ['sports','exercise','human','athlete','water','summer','man-swimming','man_swimming']),
        ('man-woman-girl', '👨‍👩‍👧', Array ['man','woman-girl','family','woman','home','parents','people','human','child','man-woman-girl','family:_man,_woman,_girl']),
        ('man-woman-girl-boy', '👨‍👩‍👧‍👦', Array ['man','woman-girl-boy','family','woman','girl','home','parents','people','human','children','man-woman-girl-boy','family:_man,_woman,_girl,_boy']),
        ('man-woman-boy-boy', '👨‍👩‍👦‍👦', Array ['man','woman-boy-boy','family','woman','home','parents','people','human','children','man-woman-boy-boy','family:_man,_woman,_boy,_boy']),
        ('man-woman-girl-girl', '👨‍👩‍👧‍👧', Array ['man','woman-girl-girl','family','woman','home','parents','people','human','children','man-woman-girl-girl','family:_man,_woman,_girl,_girl']),
        ('man-man-boy', '👨‍👨‍👦', Array ['man','man-boy','family','home','parents','people','human','children','man-man-boy','family:_man,_man,_boy']),
        ('man-woman-boy', '👨‍👩‍👦', Array ['man','woman-boy','family','woman','love','man-woman-boy','family:_man,_woman,_boy']),
        ('man-man-girl-boy', '👨‍👨‍👧‍👦', Array ['man','man-girl-boy','family','girl','home','parents','people','human','children','man-man-girl-boy','family:_man,_man,_girl,_boy']),
        ('man-man-boy-boy', '👨‍👨‍👦‍👦', Array ['man','man-boy-boy','family','home','parents','people','human','children','man-man-boy-boy','family:_man,_man,_boy,_boy']),
        ('man-man-girl-girl', '👨‍👨‍👧‍👧', Array ['man','man-girl-girl','family','home','parents','people','human','children','man-man-girl-girl','family:_man,_man,_girl,_girl']),
        ('woman-woman-boy', '👩‍👩‍👦', Array ['woman','woman-boy','family','home','parents','people','human','children','woman-woman-boy','family:_woman,_woman,_boy']),
        ('woman-woman-girl', '👩‍👩‍👧', Array ['woman','woman-girl','family','home','parents','people','human','children','woman-woman-girl','family:_woman,_woman,_girl']),
        ('man-man-girl', '👨‍👨‍👧', Array ['man','man-girl','family','home','parents','people','human','children','man-man-girl','family:_man,_man,_girl']),
        ('woman-woman-boy-boy', '👩‍👩‍👦‍👦', Array ['woman','woman-boy-boy','family','home','parents','people','human','children','woman-woman-boy-boy','family:_woman,_woman,_boy,_boy']),
        ('woman-woman-girl-girl', '👩‍👩‍👧‍👧', Array ['woman','woman-girl-girl','family','home','parents','people','human','children','woman-woman-girl-girl','family:_woman,_woman,_girl,_girl']),
        ('man-boy', '👨‍👦', Array ['man','family','home','parent','people','human','child','man-boy','family:_man,_boy']),
        ('man-boy-boy', '👨‍👦‍👦', Array ['man','boy-boy','family','home','parent','people','human','children','man-boy-boy','family:_man,_boy,_boy']),
        ('man-girl', '👨‍👧', Array ['man','family','home','parent','people','human','child','man-girl','family:_man,_girl']),
        ('man-girl-boy', '👨‍👧‍👦', Array ['man','girl-boy','family','girl','home','parent','people','human','children','man-girl-boy','family:_man,_girl,_boy']),
        ('man-girl-girl', '👨‍👧‍👧', Array ['man','girl-girl','family','home','parent','people','human','children','man-girl-girl','family:_man,_girl,_girl']),
        ('woman-boy', '👩‍👦', Array ['woman','family','home','parent','people','human','child','woman-boy','family:_woman,_boy']),
        ('woman-boy-boy', '👩‍👦‍👦', Array ['woman','boy-boy','family','home','parent','people','human','children','woman-boy-boy','family:_woman,_boy,_boy']),
        ('woman-girl', '👩‍👧', Array ['woman','family','home','parent','people','human','child','woman-girl','family:_woman,_girl']),
        ('woman-girl-boy', '👩‍👧‍👦', Array ['woman','girl-boy','family','girl','home','parent','people','human','children','woman-girl-boy','family:_woman,_girl,_boy']),
        ('woman-girl-girl', '👩‍👧‍👧', Array ['woman','girl-girl','family','home','parent','people','human','children','woman-girl-girl','family:_woman,_girl,_girl']),
        ('speaking_head_in_silhouette', '🗣️', Array ['in','silhouette','user','person','human','sing','say','talk','speaking_head_in_silhouette','speaking_head']),
        ('bust_in_silhouette', '👤', Array ['user','person','human','bust_in_silhouette','bust_in_silhouette']),
        ('busts_in_silhouette', '👥', Array ['user','person','human','group','team','busts_in_silhouette','busts_in_silhouette']),
        ('people_hugging', '🫂', Array ['care','people_hugging','people_hugging']),
        ('footprints', '👣', Array ['feet','tracking','walking','beach','footprints','footprints']),
        ('monkey_face', '🐵', Array ['animal','nature','circus','monkey_face','monkey_face']),
        ('monkey', '🐒', Array ['animal','nature','banana','circus','monkey','monkey']),
        ('gorilla', '🦍', Array ['animal','nature','circus','gorilla','gorilla']),
        ('orangutan', '🦧', Array ['animal','orangutan','orangutan']),
        ('man-rowing-boat', '🚣‍♂️', Array ['rowing-boat','sports','hobby','water','ship','man-rowing-boat','man_rowing_boat']),
        ('dog2', '🐕', Array ['dog2','animal','nature','friend','doge','pet','faithful','dog2','dog']),
        ('woman-woman-girl-boy', '👩‍👩‍👧‍👦', Array ['woman','woman-girl-boy','family','girl','home','parents','people','human','children','woman-woman-girl-boy','family:_woman,_woman,_girl,_boy']),
        ('service_dog', '🐕‍🦺', Array ['blind','animal','service_dog','service_dog']),
        ('poodle', '🐩', Array ['dog','animal','101','nature','pet','poodle','poodle']),
        ('wolf', '🐺', Array ['animal','nature','wild','wolf','wolf']),
        ('guide_dog', '🦮', Array ['animal','blind','guide_dog','guide_dog']),
        ('raccoon', '🦝', Array ['animal','nature','raccoon','raccoon']),
        ('cat', '🐱', Array ['animal','meow','nature','pet','kitten','cat','cat_face']),
        ('cat2', '🐈', Array ['cat2','animal','meow','pet','cats','cat2','cat']),
        ('black_cat', '🐈‍⬛', Array ['superstition','luck','black_cat','black_cat']),
        ('lion_face', '🦁', Array ['face','animal','nature','lion_face','lion']),
        ('tiger', '🐯', Array ['animal','cat','danger','wild','nature','roar','tiger','tiger_face']),
        ('tiger2', '🐅', Array ['tiger2','animal','nature','roar','tiger2','tiger']),
        ('dog', '🐶', Array ['animal','friend','nature','woof','puppy','pet','faithful','dog','dog_face']),
        ('horse', '🐴', Array ['animal','brown','nature','horse','horse_face']),
        ('racehorse', '🐎', Array ['racehorse','animal','gamble','luck','racehorse','horse']),
        ('unicorn_face', '🦄', Array ['face','animal','nature','mystical','unicorn_face','unicorn']),
        ('zebra_face', '🦓', Array ['face','animal','nature','stripes','safari','zebra_face','zebra']),
        ('leopard', '🐆', Array ['animal','nature','leopard','leopard']),
        ('bison', '🦬', Array ['ox','bison','bison']),
        ('cow', '🐮', Array ['beef','ox','animal','nature','moo','milk','cow','cow_face']),
        ('ox', '🐂', Array ['animal','cow','beef','ox','ox']),
        ('water_buffalo', '🐃', Array ['animal','nature','ox','cow','water_buffalo','water_buffalo']),
        ('cow2', '🐄', Array ['cow2','beef','ox','animal','nature','moo','milk','cow2','cow']),
        ('pig', '🐷', Array ['animal','oink','nature','pig','pig_face']),
        ('pig2', '🐖', Array ['pig2','animal','nature','pig2','pig']),
        ('boar', '🐗', Array ['animal','nature','boar','boar']),
        ('pig_nose', '🐽', Array ['animal','oink','pig_nose','pig_nose']),
        ('ram', '🐏', Array ['animal','sheep','nature','ram','ram']),
        ('sheep', '🐑', Array ['sheep','animal','nature','wool','shipit','sheep','ewe']),
        ('goat', '🐐', Array ['animal','nature','goat','goat']),
        ('dromedary_camel', '🐪', Array ['dromedary','animal','hot','desert','hump','dromedary_camel','camel']),
        ('camel', '🐫', Array ['two','hump','animal','nature','hot','desert','camel','bactrian_camel']),
        ('llama', '🦙', Array ['animal','nature','alpaca','llama','llama']),
        ('fox_face', '🦊', Array ['face','animal','nature','fox_face','fox']),
        ('elephant', '🐘', Array ['animal','nature','nose','th','circus','elephant','elephant']),
        ('mammoth', '🦣', Array ['elephant','tusks','mammoth','mammoth']),
        ('rhinoceros', '🦏', Array ['animal','nature','horn','rhinoceros','rhinoceros']),
        ('hippopotamus', '🦛', Array ['animal','nature','hippopotamus','hippopotamus']),
        ('mouse', '🐭', Array ['animal','nature','cheese','wedge','rodent','mouse','mouse_face']),
        ('mouse2', '🐁', Array ['mouse2','animal','nature','rodent','mouse2','mouse']),
        ('rat', '🐀', Array ['animal','mouse','rodent','rat','rat']),
        ('hamster', '🐹', Array ['animal','nature','hamster','hamster']),
        ('rabbit', '🐰', Array ['animal','nature','pet','spring','magic','bunny','rabbit','rabbit_face']),
        ('rabbit2', '🐇', Array ['rabbit2','animal','nature','pet','magic','spring','rabbit2','rabbit']),
        ('chipmunk', '🐿️', Array ['animal','nature','rodent','squirrel','chipmunk','chipmunk']),
        ('beaver', '🦫', Array ['animal','rodent','beaver','beaver']),
        ('hedgehog', '🦔', Array ['animal','nature','spiny','hedgehog','hedgehog']),
        ('man-kiss-man', '👨‍❤️‍💋‍👨', Array ['kiss-man','kiss','pair','valentines','love','like','dating','marriage','man-kiss-man','kiss:_man,_man']),
        ('bear', '🐻', Array ['animal','nature','wild','bear','bear']),
        ('polar_bear', '🐻‍❄️', Array ['animal','arctic','polar_bear','polar_bear']),
        ('bat', '🦇', Array ['animal','nature','blind','vampire','bat','bat']),
        ('panda_face', '🐼', Array ['face','animal','nature','panda_face','panda']),
        ('sloth', '🦥', Array ['animal','sloth','sloth']),
        ('otter', '🦦', Array ['animal','otter','otter']),
        ('skunk', '🦨', Array ['animal','skunk','skunk']),
        ('kangaroo', '🦘', Array ['animal','nature','australia','joey','hop','marsupial','kangaroo','kangaroo']),
        ('badger', '🦡', Array ['animal','nature','honey','badger','badger']),
        ('deer', '🦌', Array ['animal','nature','horns','venison','deer','deer']),
        ('turkey', '🦃', Array ['animal','bird','turkey','turkey']),
        ('chicken', '🐔', Array ['animal','cluck','nature','bird','chicken','chicken']),
        ('rooster', '🐓', Array ['animal','nature','chicken','rooster','rooster']),
        ('hatching_chick', '🐣', Array ['animal','chicken','egg','born','baby','bird','hatching_chick','hatching_chick']),
        ('baby_chick', '🐤', Array ['animal','chicken','bird','baby_chick','baby_chick']),
        ('hatched_chick', '🐥', Array ['hatched','front','facing','animal','chicken','bird','hatched_chick','front-facing_baby_chick']),
        ('giraffe_face', '🦒', Array ['face','animal','nature','spots','safari','giraffe_face','giraffe']),
        ('penguin', '🐧', Array ['animal','nature','penguin','penguin']),
        ('dove_of_peace', '🕊️', Array ['of','peace','animal','bird','dove_of_peace','dove']),
        ('eagle', '🦅', Array ['animal','nature','bird','eagle','eagle']),
        ('duck', '🦆', Array ['animal','nature','bird','mallard','duck','duck']),
        ('feet', '🐾', Array ['feet','animal','tracking','footprints','dog','cat','pet','feet','paw_prints']),
        ('owl', '🦉', Array ['animal','nature','bird','hoot','owl','owl']),
        ('dodo', '🦤', Array ['animal','bird','dodo','dodo']),
        ('swan', '🦢', Array ['animal','nature','bird','swan','swan']),
        ('flamingo', '🦩', Array ['animal','flamingo','flamingo']),
        ('peacock', '🦚', Array ['animal','nature','peahen','bird','peacock','peacock']),
        ('parrot', '🦜', Array ['animal','nature','bird','pirate','talk','parrot','parrot']),
        ('frog', '🐸', Array ['animal','nature','croak','toad','frog','frog']),
        ('crocodile', '🐊', Array ['animal','nature','reptile','lizard','alligator','crocodile','crocodile']),
        ('turtle', '🐢', Array ['animal','slow','nature','tortoise','turtle','turtle']),
        ('lizard', '🦎', Array ['animal','nature','reptile','lizard','lizard']),
        ('snake', '🐍', Array ['animal','evil','nature','hiss','python','snake','snake']),
        ('dragon_face', '🐲', Array ['animal','myth','nature','chinese','green','dragon_face','dragon_face']),
        ('dragon', '🐉', Array ['animal','myth','nature','chinese','green','dragon','dragon']),
        ('sauropod', '🦕', Array ['animal','nature','dinosaur','brachiosaurus','brontosaurus','diplodocus','extinct','sauropod','sauropod']),
        ('t-rex', '🦖', Array ['t','rex','animal','nature','dinosaur','tyrannosaurus','extinct','t-rex','t-rex']),
        ('whale', '🐳', Array ['animal','nature','sea','ocean','whale','spouting_whale']),
        ('whale2', '🐋', Array ['whale2','animal','nature','sea','ocean','whale2','whale']),
        ('dolphin', '🐬', Array ['flipper','animal','nature','fish','sea','ocean','fins','beach','dolphin','dolphin']),
        ('seal', '🦭', Array ['animal','creature','sea','seal','seal']),
        ('fish', '🐟', Array ['animal','food','nature','fish','fish']),
        ('tropical_fish', '🐠', Array ['animal','swim','ocean','beach','nemo','tropical_fish','tropical_fish']),
        ('blowfish', '🐡', Array ['animal','nature','food','sea','ocean','blowfish','blowfish']),
        ('shark', '🦈', Array ['animal','nature','fish','sea','ocean','jaws','fins','beach','shark','shark']),
        ('octopus', '🐙', Array ['animal','creature','ocean','sea','nature','beach','octopus','octopus']),
        ('shell', '🐚', Array ['nature','sea','beach','shell','spiral_shell']),
        ('coral', '🪸', Array ['ocean','sea','reef','coral','coral']),
        ('snail', '🐌', Array ['slow','animal','shell','snail','snail']),
        ('butterfly', '🦋', Array ['animal','insect','nature','caterpillar','butterfly','butterfly']),
        ('bug', '🐛', Array ['animal','insect','nature','worm','bug','bug']),
        ('ant', '🐜', Array ['animal','insect','nature','bug','ant','ant']),
        ('bee', '🐝', Array ['bee','animal','insect','nature','bug','spring','honey','bee','honeybee']),
        ('beetle', '🪲', Array ['insect','beetle','beetle']),
        ('ladybug', '🐞', Array ['ladybug','animal','insect','nature','ladybug','lady_beetle']),
        ('cricket', '🦗', Array ['animal','chirp','cricket','cricket']),
        ('cockroach', '🪳', Array ['insect','pests','cockroach','cockroach']),
        ('spider', '🕷️', Array ['animal','arachnid','spider','spider']),
        ('spider_web', '🕸️', Array ['animal','insect','arachnid','silk','spider_web','spider_web']),
        ('scorpion', '🦂', Array ['animal','arachnid','scorpion','scorpion']),
        ('mosquito', '🦟', Array ['animal','nature','insect','malaria','mosquito','mosquito']),
        ('fly', '🪰', Array ['insect','fly','fly']),
        ('worm', '🪱', Array ['animal','worm','worm']),
        ('microbe', '🦠', Array ['amoeba','bacteria','germs','virus','covid','microbe','microbe']),
        ('koala', '🐨', Array ['animal','nature','koala','koala']),
        ('cherry_blossom', '🌸', Array ['nature','plant','spring','flower','cherry_blossom','cherry_blossom']),
        ('white_flower', '💮', Array ['japanese','spring','white_flower','white_flower']),
        ('lotus', '🪷', Array ['flower','calm','meditation','lotus','lotus']),
        ('rosette', '🏵️', Array ['flower','decoration','military','rosette','rosette']),
        ('rose', '🌹', Array ['flowers','valentines','love','spring','rose','rose']),
        ('feather', '🪶', Array ['bird','fly','feather','feather']),
        ('hibiscus', '🌺', Array ['plant','vegetable','flowers','beach','hibiscus','hibiscus']),
        ('sunflower', '🌻', Array ['nature','plant','fall','sunflower','sunflower']),
        ('wilted_flower', '🥀', Array ['plant','nature','rose','wilted_flower','wilted_flower']),
        ('tulip', '🌷', Array ['flowers','plant','nature','summer','spring','tulip','tulip']),
        ('blossom', '🌼', Array ['nature','flowers','yellow','blossom','blossom']),
        ('evergreen_tree', '🌲', Array ['plant','nature','evergreen_tree','evergreen_tree']),
        ('potted_plant', '🪴', Array ['greenery','house','potted_plant','potted_plant']),
        ('seedling', '🌱', Array ['plant','nature','grass','lawn','spring','seedling','seedling']),
        ('palm_tree', '🌴', Array ['plant','vegetable','nature','summer','beach','mojito','tropical','palm_tree','palm_tree']),
        ('cactus', '🌵', Array ['vegetable','plant','nature','cactus','cactus']),
        ('ear_of_rice', '🌾', Array ['sheaf','nature','plant','ear_of_rice','ear_of_rice']),
        ('herb', '🌿', Array ['vegetable','plant','medicine','weed','grass','lawn','herb','herb']),
        ('shamrock', '☘️', Array ['vegetable','plant','nature','irish','clover','shamrock','shamrock']),
        ('four_leaf_clover', '🍀', Array ['vegetable','plant','nature','lucky','irish','four_leaf_clover','four_leaf_clover']),
        ('maple_leaf', '🍁', Array ['nature','plant','vegetable','ca','fall','maple_leaf','maple_leaf']),
        ('fallen_leaf', '🍂', Array ['nature','plant','vegetable','leaves','fallen_leaf','fallen_leaf']),
        ('leaves', '🍃', Array ['leaves','nature','plant','tree','vegetable','grass','lawn','spring','leaves','leaf_fluttering_in_wind']),
        ('empty_nest', '🪹', Array ['bird','empty_nest','empty_nest']),
        ('bird', '🐦', Array ['animal','nature','fly','tweet','spring','bird','bird']),
        ('mushroom', '🍄', Array ['plant','vegetable','mushroom','mushroom']),
        ('grapes', '🍇', Array ['fruit','food','wine','grapes','grapes']),
        ('melon', '🍈', Array ['fruit','nature','food','melon','melon']),
        ('watermelon', '🍉', Array ['fruit','food','picnic','summer','watermelon','watermelon']),
        ('tangerine', '🍊', Array ['food','fruit','nature','orange','tangerine','tangerine']),
        ('nest_with_eggs', '🪺', Array ['bird','nest_with_eggs','nest_with_eggs']),
        ('banana', '🍌', Array ['fruit','food','monkey','banana','banana']),
        ('pineapple', '🍍', Array ['fruit','nature','food','pineapple','pineapple']),
        ('mango', '🥭', Array ['fruit','food','tropical','mango','mango']),
        ('apple', '🍎', Array ['fruit','mac','school','apple','red_apple']),
        ('green_apple', '🍏', Array ['fruit','nature','green_apple','green_apple']),
        ('pear', '🍐', Array ['fruit','nature','food','pear','pear']),
        ('peach', '🍑', Array ['fruit','nature','food','peach','peach']),
        ('cherries', '🍒', Array ['food','fruit','cherries','cherries']),
        ('strawberry', '🍓', Array ['fruit','food','nature','strawberry','strawberry']),
        ('blueberries', '🫐', Array ['fruit','blueberries','blueberries']),
        ('bouquet', '💐', Array ['flowers','nature','spring','bouquet','bouquet']),
        ('tomato', '🍅', Array ['fruit','vegetable','nature','food','tomato','tomato']),
        ('olive', '🫒', Array ['fruit','olive','olive']),
        ('coconut', '🥥', Array ['fruit','nature','food','palm','coconut','coconut']),
        ('avocado', '🥑', Array ['fruit','food','avocado','avocado']),
        ('eggplant', '🍆', Array ['vegetable','nature','food','aubergine','eggplant','eggplant']),
        ('kiwifruit', '🥝', Array ['kiwi','fruit','food','kiwifruit','kiwifruit']),
        ('carrot', '🥕', Array ['vegetable','food','orange','carrot','carrot']),
        ('corn', '🌽', Array ['food','vegetable','plant','corn','ear_of_corn']),
        ('hot_pepper', '🌶️', Array ['food','spicy','chilli','chili','hot_pepper','hot_pepper']),
        ('bell_pepper', '🫑', Array ['fruit','plant','bell_pepper','bell_pepper']),
        ('cucumber', '🥒', Array ['fruit','food','pickle','cucumber','cucumber']),
        ('potato', '🥔', Array ['food','tuber','vegatable','starch','potato','potato']),
        ('broccoli', '🥦', Array ['fruit','food','vegetable','broccoli','broccoli']),
        ('garlic', '🧄', Array ['food','spice','cook','garlic','garlic']),
        ('onion', '🧅', Array ['cook','food','spice','onion','onion']),
        ('peanuts', '🥜', Array ['food','nut','peanuts','peanuts']),
        ('beans', '🫘', Array ['food','beans','beans']),
        ('deciduous_tree', '🌳', Array ['plant','nature','deciduous_tree','deciduous_tree']),
        ('bread', '🍞', Array ['food','wheat','breakfast','toast','bread','bread']),
        ('croissant', '🥐', Array ['food','bread','french','croissant','croissant']),
        ('baguette_bread', '🥖', Array ['food','french','france','bakery','baguette_bread','baguette_bread']),
        ('flatbread', '🫓', Array ['flour','food','bakery','flatbread','flatbread']),
        ('pretzel', '🥨', Array ['food','bread','twisted','germany','bakery','pretzel','pretzel']),
        ('bagel', '🥯', Array ['food','bread','bakery','schmear','jewish','bagel','bagel']),
        ('pancakes', '🥞', Array ['food','breakfast','flapjacks','hotcakes','brunch','pancakes','pancakes']),
        ('waffle', '🧇', Array ['food','breakfast','brunch','waffle','waffle']),
        ('cheese_wedge', '🧀', Array ['food','chadder','swiss','cheese_wedge','cheese_wedge']),
        ('meat_on_bone', '🍖', Array ['good','food','drumstick','meat_on_bone','meat_on_bone']),
        ('poultry_leg', '🍗', Array ['food','meat','drumstick','bird','chicken','turkey','poultry_leg','poultry_leg']),
        ('cut_of_meat', '🥩', Array ['food','cow','chop','lambchop','porkchop','cut_of_meat','cut_of_meat']),
        ('bacon', '🥓', Array ['food','breakfast','pork','pig','meat','brunch','bacon','bacon']),
        ('hamburger', '🍔', Array ['meat','fast','food','beef','cheeseburger','mcdonalds','burger','king','hamburger','hamburger']),
        ('fries', '🍟', Array ['chips','snack','fast','food','potato','fries','french_fries']),
        ('pizza', '🍕', Array ['food','party','italy','pizza','pizza']),
        ('hotdog', '🌭', Array ['hotdog','food','frankfurter','america','hotdog','hot_dog']),
        ('sandwich', '🥪', Array ['food','lunch','bread','toast','bakery','sandwich','sandwich']),
        ('taco', '🌮', Array ['food','mexican','taco','taco']),
        ('burrito', '🌯', Array ['food','mexican','burrito','burrito']),
        ('tamale', '🫔', Array ['food','masa','tamale','tamale']),
        ('stuffed_flatbread', '🥙', Array ['food','gyro','mediterranean','stuffed_flatbread','stuffed_flatbread']),
        ('falafel', '🧆', Array ['food','mediterranean','falafel','falafel']),
        ('egg', '🥚', Array ['food','chicken','breakfast','egg','egg']),
        ('fried_egg', '🍳', Array ['fried','egg','food','breakfast','kitchen','skillet','fried_egg','cooking']),
        ('shallow_pan_of_food', '🥘', Array ['cooking','casserole','paella','skillet','shallow_pan_of_food','shallow_pan_of_food']),
        ('stew', '🍲', Array ['stew','meat','soup','hot','stew','pot_of_food']),
        ('fondue', '🫕', Array ['cheese','pot','food','fondue','fondue']),
        ('bowl_with_spoon', '🥣', Array ['food','breakfast','cereal','oatmeal','porridge','bowl_with_spoon','bowl_with_spoon']),
        ('green_salad', '🥗', Array ['food','healthy','lettuce','vegetable','green_salad','green_salad']),
        ('popcorn', '🍿', Array ['food','movie','theater','films','snack','drama','popcorn','popcorn']),
        ('butter', '🧈', Array ['food','cook','butter','butter']),
        ('lemon', '🍋', Array ['fruit','nature','lemon','lemon']),
        ('canned_food', '🥫', Array ['soup','tomatoes','canned_food','canned_food']),
        ('bento', '🍱', Array ['food','japanese','lunch','bento','bento_box']),
        ('rice_cracker', '🍘', Array ['food','japanese','snack','rice_cracker','rice_cracker']),
        ('rice_ball', '🍙', Array ['food','japanese','rice_ball','rice_ball']),
        ('leafy_green', '🥬', Array ['food','vegetable','plant','bok','choy','cabbage','kale','lettuce','leafy_green','leafy_green']),
        ('curry', '🍛', Array ['food','spicy','hot','indian','curry','curry_rice']),
        ('ramen', '🍜', Array ['ramen','food','japanese','noodle','chopsticks','ramen','steaming_bowl']),
        ('spaghetti', '🍝', Array ['food','italian','pasta','noodle','spaghetti','spaghetti']),
        ('sweet_potato', '🍠', Array ['food','nature','plant','sweet_potato','roasted_sweet_potato']),
        ('oden', '🍢', Array ['food','japanese','oden','oden']),
        ('sushi', '🍣', Array ['food','fish','japanese','rice','sushi','sushi']),
        ('fried_shrimp', '🍤', Array ['food','animal','appetizer','summer','fried_shrimp','fried_shrimp']),
        ('fish_cake', '🍥', Array ['food','japan','sea','beach','narutomaki','pink','kamaboko','surimi','ramen','fish_cake','fish_cake_with_swirl']),
        ('moon_cake', '🥮', Array ['food','autumn','dessert','moon_cake','moon_cake']),
        ('dango', '🍡', Array ['food','dessert','sweet','japanese','barbecue','meat','dango','dango']),
        ('dumpling', '🥟', Array ['food','empanada','pierogi','potsticker','gyoza','dumpling','dumpling']),
        ('fortune_cookie', '🥠', Array ['food','prophecy','dessert','fortune_cookie','fortune_cookie']),
        ('takeout_box', '🥡', Array ['food','leftovers','takeout_box','takeout_box']),
        ('crab', '🦀', Array ['animal','crustacean','crab','crab']),
        ('lobster', '🦞', Array ['animal','nature','bisque','claws','seafood','lobster','lobster']),
        ('shrimp', '🦐', Array ['animal','ocean','nature','seafood','shrimp','shrimp']),
        ('squid', '🦑', Array ['animal','nature','ocean','sea','squid','squid']),
        ('shaved_ice', '🍧', Array ['hot','dessert','summer','shaved_ice','shaved_ice']),
        ('oyster', '🦪', Array ['food','oyster','oyster']),
        ('icecream', '🍦', Array ['icecream','food','hot','dessert','summer','icecream','soft_ice_cream']),
        ('ice_cream', '🍨', Array ['food','hot','dessert','ice_cream','ice_cream']),
        ('doughnut', '🍩', Array ['food','dessert','snack','sweet','donut','doughnut','doughnut']),
        ('cookie', '🍪', Array ['food','snack','oreo','chocolate','sweet','dessert','cookie','cookie']),
        ('birthday', '🎂', Array ['food','dessert','birthday','birthday_cake']),
        ('cake', '🍰', Array ['cake','food','dessert','cake','shortcake']),
        ('cupcake', '🧁', Array ['food','dessert','bakery','sweet','cupcake','cupcake']),
        ('pie', '🥧', Array ['food','dessert','pastry','pie','pie']),
        ('chocolate_bar', '🍫', Array ['food','snack','dessert','sweet','chocolate_bar','chocolate_bar']),
        ('candy', '🍬', Array ['snack','dessert','sweet','lolly','candy','candy']),
        ('lollipop', '🍭', Array ['food','snack','candy','sweet','lollipop','lollipop']),
        ('custard', '🍮', Array ['dessert','food','custard','custard']),
        ('honey_pot', '🍯', Array ['bees','sweet','kitchen','honey_pot','honey_pot']),
        ('baby_bottle', '🍼', Array ['food','container','milk','baby_bottle','baby_bottle']),
        ('salt', '🧂', Array ['condiment','shaker','salt','salt']),
        ('coffee', '☕', Array ['coffee','caffeine','latte','espresso','mug','coffee','hot_beverage']),
        ('teapot', '🫖', Array ['drink','hot','teapot','teapot']),
        ('tea', '🍵', Array ['tea','drink','bowl','breakfast','green','british','tea','teacup_without_handle']),
        ('sake', '🍶', Array ['wine','drink','drunk','beverage','japanese','alcohol','booze','sake','sake']),
        ('champagne', '🍾', Array ['champagne','drink','wine','celebration','champagne','bottle_with_popping_cork']),
        ('wine_glass', '🍷', Array ['drink','beverage','drunk','alcohol','booze','wine_glass','wine_glass']),
        ('cocktail', '🍸', Array ['drink','drunk','alcohol','beverage','booze','mojito','cocktail','cocktail_glass']),
        ('tropical_drink', '🍹', Array ['beverage','cocktail','summer','beach','alcohol','booze','mojito','tropical_drink','tropical_drink']),
        ('beer', '🍺', Array ['relax','beverage','drink','drunk','party','pub','summer','alcohol','booze','beer','beer_mug']),
        ('beers', '🍻', Array ['beers','relax','beverage','drink','drunk','party','pub','summer','alcohol','booze','beers','clinking_beer_mugs']),
        ('clinking_glasses', '🥂', Array ['beverage','drink','party','alcohol','celebrate','cheers','wine','champagne','toast','clinking_glasses','clinking_glasses']),
        ('tumbler_glass', '🥃', Array ['drink','beverage','drunk','alcohol','liquor','booze','bourbon','scotch','whisky','shot','tumbler_glass','tumbler_glass']),
        ('chestnut', '🌰', Array ['food','squirrel','chestnut','chestnut']),
        ('cup_with_straw', '🥤', Array ['drink','soda','cup_with_straw','cup_with_straw']),
        ('bubble_tea', '🧋', Array ['taiwan','boba','milk','straw','bubble_tea','bubble_tea']),
        ('beverage_box', '🧃', Array ['drink','beverage_box','beverage_box']),
        ('mate_drink', '🧉', Array ['drink','tea','beverage','mate_drink','mate']),
        ('ice_cube', '🧊', Array ['cube','water','cold','ice_cube','ice']),
        ('pouring_liquid', '🫗', Array ['cup','water','pouring_liquid','pouring_liquid']),
        ('knife_fork_plate', '🍽️', Array ['food','eat','meal','lunch','dinner','restaurant','knife_fork_plate','fork_and_knife_with_plate']),
        ('fork_and_knife', '🍴', Array ['cutlery','kitchen','fork_and_knife','fork_and_knife']),
        ('spoon', '🥄', Array ['cutlery','kitchen','tableware','spoon','spoon']),
        ('rice', '🍚', Array ['food','asian','rice','cooked_rice']),
        ('jar', '🫙', Array ['container','sauce','jar','jar']),
        ('amphora', '🏺', Array ['vase','jar','amphora','amphora']),
        ('earth_africa', '🌍', Array ['africa','showing','europe','world','international','earth_africa','earth_globe_europe-africa']),
        ('earth_americas', '🌎', Array ['showing','world','USA','international','earth_americas','earth_globe_americas']),
        ('chopsticks', '🥢', Array ['food','chopsticks','chopsticks']),
        ('globe_with_meridians', '🌐', Array ['earth','international','world','internet','interweb','i18n','globe_with_meridians','globe_with_meridians']),
        ('world_map', '🗺️', Array ['location','direction','world_map','world_map']),
        ('japan', '🗾', Array ['nation','country','japanese','asia','japan','map_of_japan']),
        ('compass', '🧭', Array ['magnetic','navigation','orienteering','compass','compass']),
        ('snow_capped_mountain', '🏔️', Array ['snow','capped','photo','nature','environment','winter','cold','snow_capped_mountain','snow-capped_mountain']),
        ('earth_asia', '🌏', Array ['asia','showing','australia','world','east','international','earth_asia','earth_globe_asia-australia']),
        ('volcano', '🌋', Array ['photo','nature','disaster','volcano','volcano']),
        ('mount_fuji', '🗻', Array ['photo','mountain','nature','japanese','mount_fuji','mount_fuji']),
        ('camping', '🏕️', Array ['photo','outdoors','tent','camping','camping']),
        ('beach_with_umbrella', '🏖️', Array ['weather','summer','sunny','sand','mojito','beach_with_umbrella','beach_with_umbrella']),
        ('desert', '🏜️', Array ['photo','warm','saharah','desert','desert']),
        ('mountain', '⛰️', Array ['photo','nature','environment','mountain','mountain']),
        ('national_park', '🏞️', Array ['photo','environment','nature','national_park','national_park']),
        ('stadium', '🏟️', Array ['photo','place','sports','concert','venue','stadium','stadium']),
        ('desert_island', '🏝️', Array ['photo','tropical','mojito','desert_island','desert_island']),
        ('classical_building', '🏛️', Array ['art','culture','history','classical_building','classical_building']),
        ('glass_of_milk', '🥛', Array ['beverage','drink','cow','glass_of_milk','glass_of_milk']),
        ('rock', '🪨', Array ['stone','rock','rock']),
        ('wood', '🪵', Array ['nature','timber','trunk','wood','wood']),
        ('hut', '🛖', Array ['house','structure','hut','hut']),
        ('house_buildings', '🏘️', Array ['house','buildings','photo','house_buildings','houses']),
        ('derelict_house_building', '🏚️', Array ['building','abandon','evict','broken','derelict_house_building','derelict_house']),
        ('bricks', '🧱', Array ['bricks','bricks','brick']),
        ('house_with_garden', '🏡', Array ['home','plant','nature','house_with_garden','house_with_garden']),
        ('office', '🏢', Array ['bureau','work','office','office_building']),
        ('post_office', '🏣', Array ['building','envelope','communication','post_office','japanese_post_office']),
        ('european_post_office', '🏤', Array ['european','building','email','european_post_office','post_office']),
        ('hospital', '🏥', Array ['building','health','surgery','doctor','hospital','hospital']),
        ('bank', '🏦', Array ['building','money','sales','cash','business','enterprise','bank','bank']),
        ('building_construction', '🏗️', Array ['wip','working','progress','building_construction','building_construction']),
        ('love_hotel', '🏩', Array ['like','affection','dating','love_hotel','love_hotel']),
        ('convenience_store', '🏪', Array ['building','shopping','groceries','convenience_store','convenience_store']),
        ('hotel', '🏨', Array ['building','accomodation','checkin','hotel','hotel']),
        ('department_store', '🏬', Array ['building','shopping','mall','department_store','department_store']),
        ('factory', '🏭', Array ['building','industry','pollution','smoke','factory','factory']),
        ('school', '🏫', Array ['building','student','education','learn','teach','school','school']),
        ('european_castle', '🏰', Array ['european','building','royalty','history','european_castle','castle']),
        ('wedding', '💒', Array ['love','like','affection','couple','marriage','bride','groom','wedding','wedding']),
        ('tokyo_tower', '🗼', Array ['photo','japanese','tokyo_tower','tokyo_tower']),
        ('statue_of_liberty', '🗽', Array ['american','newyork','statue_of_liberty','statue_of_liberty']),
        ('church', '⛪', Array ['building','religion','christ','church','church']),
        ('mosque', '🕌', Array ['islam','worship','minaret','mosque','mosque']),
        ('hindu_temple', '🛕', Array ['religion','hindu_temple','hindu_temple']),
        ('house', '🏠', Array ['building','home','house','house']),
        ('shinto_shrine', '⛩️', Array ['temple','japan','kyoto','shinto_shrine','shinto_shrine']),
        ('kaaba', '🕋', Array ['mecca','mosque','islam','kaaba','kaaba']),
        ('fountain', '⛲', Array ['photo','summer','water','fresh','fountain','fountain']),
        ('tent', '⛺', Array ['photo','camping','outdoors','tent','tent']),
        ('foggy', '🌁', Array ['photo','mountain','foggy','foggy']),
        ('synagogue', '🕍', Array ['judaism','worship','temple','jewish','synagogue','synagogue']),
        ('cityscape', '🏙️', Array ['photo','night','life','urban','cityscape','cityscape']),
        ('sunrise_over_mountains', '🌄', Array ['view','vacation','photo','sunrise_over_mountains','sunrise_over_mountains']),
        ('sunrise', '🌅', Array ['morning','view','vacation','photo','sunrise','sunrise']),
        ('city_sunset', '🌆', Array ['city','sunset','photo','evening','sky','buildings','city_sunset','cityscape_at_dusk']),
        ('city_sunrise', '🌇', Array ['city','sunrise','photo','good','morning','dawn','city_sunrise','sunset']),
        ('night_with_stars', '🌃', Array ['evening','city','downtown','night_with_stars','night_with_stars']),
        ('carousel_horse', '🎠', Array ['photo','carnival','carousel_horse','carousel_horse']),
        ('hotsprings', '♨️', Array ['hotsprings','bath','warm','relax','hotsprings','hot_springs']),
        ('playground_slide', '🛝', Array ['fun','park','playground_slide','playground_slide']),
        ('ferris_wheel', '🎡', Array ['photo','carnival','londoneye','ferris_wheel','ferris_wheel']),
        ('roller_coaster', '🎢', Array ['carnival','playground','photo','fun','roller_coaster','roller_coaster']),
        ('bridge_at_night', '🌉', Array ['photo','sanfrancisco','bridge_at_night','bridge_at_night']),
        ('circus_tent', '🎪', Array ['festival','carnival','party','circus_tent','circus_tent']),
        ('steam_locomotive', '🚂', Array ['steam','transportation','vehicle','train','steam_locomotive','locomotive']),
        ('railway_car', '🚃', Array ['transportation','vehicle','railway_car','railway_car']),
        ('bullettrain_side', '🚄', Array ['bullettrain','side','high','speed','transportation','vehicle','bullettrain_side','high-speed_train']),
        ('bullettrain_front', '🚅', Array ['bullettrain','front','transportation','vehicle','speed','fast','public','travel','bullettrain_front','bullet_train']),
        ('barber', '💈', Array ['hair','salon','style','barber','barber_pole']),
        ('metro', '🚇', Array ['transportation','blue','square','mrt','underground','tube','metro','metro']),
        ('light_rail', '🚈', Array ['transportation','vehicle','light_rail','light_rail']),
        ('station', '🚉', Array ['transportation','vehicle','public','station','station']),
        ('tram', '🚊', Array ['transportation','vehicle','tram','tram']),
        ('monorail', '🚝', Array ['transportation','vehicle','monorail','monorail']),
        ('train2', '🚆', Array ['train2','transportation','vehicle','train2','train']),
        ('train', '🚋', Array ['train','transportation','vehicle','carriage','public','travel','train','tram_car']),
        ('bus', '🚌', Array ['car','vehicle','transportation','bus','bus']),
        ('oncoming_bus', '🚍', Array ['vehicle','transportation','oncoming_bus','oncoming_bus']),
        ('trolleybus', '🚎', Array ['bart','transportation','vehicle','trolleybus','trolleybus']),
        ('minibus', '🚐', Array ['vehicle','car','transportation','minibus','minibus']),
        ('ambulance', '🚑', Array ['health','911','hospital','ambulance','ambulance']),
        ('fire_engine', '🚒', Array ['transportation','cars','vehicle','fire_engine','fire_engine']),
        ('japanese_castle', '🏯', Array ['photo','building','japanese_castle','japanese_castle']),
        ('oncoming_police_car', '🚔', Array ['vehicle','law','legal','enforcement','911','oncoming_police_car','oncoming_police_car']),
        ('taxi', '🚕', Array ['uber','vehicle','cars','transportation','taxi','taxi']),
        ('oncoming_taxi', '🚖', Array ['vehicle','cars','uber','oncoming_taxi','oncoming_taxi']),
        ('car', '🚗', Array ['car','red','transportation','vehicle','car','automobile']),
        ('oncoming_automobile', '🚘', Array ['car','vehicle','transportation','oncoming_automobile','oncoming_automobile']),
        ('police_car', '🚓', Array ['vehicle','cars','transportation','law','legal','enforcement','police_car','police_car']),
        ('pickup_truck', '🛻', Array ['car','transportation','pickup_truck','pickup_truck']),
        ('truck', '🚚', Array ['cars','transportation','truck','delivery_truck']),
        ('articulated_lorry', '🚛', Array ['vehicle','cars','transportation','express','articulated_lorry','articulated_lorry']),
        ('tractor', '🚜', Array ['vehicle','car','farming','agriculture','tractor','tractor']),
        ('racing_car', '🏎️', Array ['sports','race','fast','formula','f1','racing_car','racing_car']),
        ('racing_motorcycle', '🏍️', Array ['racing','race','sports','fast','racing_motorcycle','motorcycle']),
        ('mountain_railway', '🚞', Array ['transportation','vehicle','mountain_railway','mountain_railway']),
        ('manual_wheelchair', '🦽', Array ['accessibility','manual_wheelchair','manual_wheelchair']),
        ('motorized_wheelchair', '🦼', Array ['accessibility','motorized_wheelchair','motorized_wheelchair']),
        ('motor_scooter', '🛵', Array ['vehicle','vespa','sasha','motor_scooter','motor_scooter']),
        ('bike', '🚲', Array ['bike','sports','exercise','hipster','bike','bicycle']),
        ('scooter', '🛴', Array ['kick','vehicle','razor','scooter','scooter']),
        ('skateboard', '🛹', Array ['board','skateboard','skateboard']),
        ('roller_skate', '🛼', Array ['footwear','sports','roller_skate','roller_skate']),
        ('blue_car', '🚙', Array ['blue','car','sport','utility','transportation','blue_car','recreational_vehicle']),
        ('motorway', '🛣️', Array ['road','cupertino','interstate','highway','motorway','motorway']),
        ('railway_track', '🛤️', Array ['train','transportation','railway_track','railway_track']),
        ('oil_drum', '🛢️', Array ['barrell','oil_drum','oil_drum']),
        ('busstop', '🚏', Array ['busstop','transportation','wait','busstop','bus_stop']),
        ('wheel', '🛞', Array ['car','transport','wheel','wheel']),
        ('rotating_light', '🚨', Array ['rotating','ambulance','911','emergency','alert','error','pinged','law','legal','rotating_light','police_car_light']),
        ('traffic_light', '🚥', Array ['transportation','signal','traffic_light','horizontal_traffic_light']),
        ('vertical_traffic_light', '🚦', Array ['transportation','driving','vertical_traffic_light','vertical_traffic_light']),
        ('octagonal_sign', '🛑', Array ['octagonal','octagonal_sign','stop_sign']),
        ('auto_rickshaw', '🛺', Array ['move','transportation','auto_rickshaw','auto_rickshaw']),
        ('anchor', '⚓', Array ['ship','ferry','sea','boat','anchor','anchor']),
        ('ring_buoy', '🛟', Array ['life','saver','preserver','ring_buoy','ring_buoy']),
        ('boat', '⛵', Array ['boat','ship','summer','transportation','water','sailing','boat','sailboat']),
        ('canoe', '🛶', Array ['boat','paddle','water','ship','canoe','canoe']),
        ('speedboat', '🚤', Array ['ship','transportation','vehicle','summer','speedboat','speedboat']),
        ('construction', '🚧', Array ['wip','progress','caution','warning','construction','construction']),
        ('ferry', '⛴️', Array ['boat','ship','yacht','ferry','ferry']),
        ('motor_boat', '🛥️', Array ['ship','motor_boat','motor_boat']),
        ('passenger_ship', '🛳️', Array ['yacht','cruise','ferry','passenger_ship','passenger_ship']),
        ('airplane', '✈️', Array ['vehicle','transportation','flight','fly','airplane','airplane']),
        ('small_airplane', '🛩️', Array ['flight','transportation','fly','vehicle','small_airplane','small_airplane']),
        ('airplane_departure', '🛫', Array ['airport','flight','landing','airplane_departure','airplane_departure']),
        ('airplane_arriving', '🛬', Array ['arriving','airport','flight','boarding','airplane_arriving','airplane_arrival']),
        ('fuelpump', '⛽', Array ['fuelpump','gas','station','petroleum','fuelpump','fuel_pump']),
        ('seat', '💺', Array ['sit','airplane','transport','bus','flight','fly','seat','seat']),
        ('helicopter', '🚁', Array ['transportation','vehicle','fly','helicopter','helicopter']),
        ('suspension_railway', '🚟', Array ['vehicle','transportation','suspension_railway','suspension_railway']),
        ('parachute', '🪂', Array ['fly','glide','parachute','parachute']),
        ('aerial_tramway', '🚡', Array ['transportation','vehicle','ski','aerial_tramway','aerial_tramway']),
        ('satellite', '🛰️', Array ['communication','gps','orbit','spaceflight','NASA','ISS','satellite','satellite']),
        ('ship', '🚢', Array ['transportation','titanic','deploy','ship','ship']),
        ('flying_saucer', '🛸', Array ['transportation','vehicle','ufo','flying_saucer','flying_saucer']),
        ('bellhop_bell', '🛎️', Array ['service','bellhop_bell','bellhop_bell']),
        ('luggage', '🧳', Array ['packing','travel','luggage','luggage']),
        ('hourglass', '⌛', Array ['done','time','clock','oldschool','limit','exam','quiz','test','hourglass','hourglass']),
        ('hourglass_flowing_sand', '⏳', Array ['flowing','sand','oldschool','time','countdown','hourglass_flowing_sand','hourglass_not_done']),
        ('rocket', '🚀', Array ['launch','ship','staffmode','NASA','outer','space','fly','rocket','rocket']),
        ('alarm_clock', '⏰', Array ['time','wake','alarm_clock','alarm_clock']),
        ('stopwatch', '⏱️', Array ['time','deadline','stopwatch','stopwatch']),
        ('timer_clock', '⏲️', Array ['alarm','timer_clock','timer_clock']),
        ('mantelpiece_clock', '🕰️', Array ['time','mantelpiece_clock','mantelpiece_clock']),
        ('mountain_cableway', '🚠', Array ['transportation','vehicle','ski','mountain_cableway','mountain_cableway']),
        ('clock1230', '🕧', Array ['clock1230','twelve','thirty','00:30','0030','12:30','1230','time','late','early','schedule','clock1230','twelve-thirty']),
        ('clock1', '🕐', Array ['clock1','o','clock','1','1:00','100','13:00','1300','time','late','early','schedule','clock1','one_o’clock']),
        ('clock130', '🕜', Array ['clock130','one','thirty','1:30','130','13:30','1330','time','late','early','schedule','clock130','one-thirty']),
        ('clock2', '🕑', Array ['clock2','o','clock','2','2:00','200','14:00','1400','time','late','early','schedule','clock2','two_o’clock']),
        ('hocho', '🔪', Array ['knife','kitchen','blade','cutlery','weapon','hocho','hocho']),
        ('clock3', '🕒', Array ['clock3','o','clock','3','3:00','300','15:00','1500','time','late','early','schedule','clock3','three_o’clock']),
        ('clock330', '🕞', Array ['clock330','three','thirty','3:30','330','15:30','1530','time','late','early','schedule','clock330','three-thirty']),
        ('clock4', '🕓', Array ['clock4','o','clock','4','4:00','400','16:00','1600','time','late','early','schedule','clock4','four_o’clock']),
        ('clock230', '🕝', Array ['clock230','two','thirty','2:30','230','14:30','1430','time','late','early','schedule','clock230','two-thirty']),
        ('clock5', '🕔', Array ['clock5','o','clock','5','5:00','500','17:00','1700','time','late','early','schedule','clock5','five_o’clock']),
        ('clock530', '🕠', Array ['clock530','five','thirty','5:30','530','17:30','1730','time','late','early','schedule','clock530','five-thirty']),
        ('clock6', '🕕', Array ['clock6','o','clock','6','6:00','600','18:00','1800','time','late','early','schedule','dawn','dusk','clock6','six_o’clock']),
        ('clock630', '🕡', Array ['clock630','six','thirty','6:30','630','18:30','1830','time','late','early','schedule','clock630','six-thirty']),
        ('clock7', '🕖', Array ['clock7','o','clock','7','7:00','700','19:00','1900','time','late','early','schedule','clock7','seven_o’clock']),
        ('watch', '⌚', Array ['time','accessories','watch','watch']),
        ('clock8', '🕗', Array ['clock8','o','clock','8','8:00','800','20:00','2000','time','late','early','schedule','clock8','eight_o’clock']),
        ('clock830', '🕣', Array ['clock830','eight','thirty','8:30','830','20:30','2030','time','late','early','schedule','clock830','eight-thirty']),
        ('clock9', '🕘', Array ['clock9','o','clock','9','9:00','900','21:00','2100','time','late','early','schedule','clock9','nine_o’clock']),
        ('clock930', '🕤', Array ['clock930','nine','thirty','9:30','930','21:30','2130','time','late','early','schedule','clock930','nine-thirty']),
        ('clock10', '🕙', Array ['clock10','o','clock','10','10:00','1000','22:00','2200','time','late','early','schedule','clock10','ten_o’clock']),
        ('clock730', '🕢', Array ['clock730','seven','thirty','7:30','730','19:30','1930','time','late','early','schedule','clock730','seven-thirty']),
        ('clock11', '🕚', Array ['clock11','o','clock','11','11:00','1100','23:00','2300','time','late','early','schedule','clock11','eleven_o’clock']),
        ('clock1130', '🕦', Array ['clock1130','eleven','thirty','11:30','1130','23:30','2330','time','late','early','schedule','clock1130','eleven-thirty']),
        ('new_moon', '🌑', Array ['nature','twilight','planet','space','night','evening','sleep','new_moon','new_moon']),
        ('waxing_crescent_moon', '🌒', Array ['nature','twilight','planet','space','night','evening','sleep','waxing_crescent_moon','waxing_crescent_moon']),
        ('first_quarter_moon', '🌓', Array ['nature','twilight','planet','space','night','evening','sleep','first_quarter_moon','first_quarter_moon']),
        ('clock1030', '🕥', Array ['clock1030','ten','thirty','10:30','1030','22:30','2230','time','late','early','schedule','clock1030','ten-thirty']),
        ('moon', '🌔', Array ['nature','night','sky','gray','twilight','planet','space','evening','sleep','moon','waxing_gibbous_moon']),
        ('waning_gibbous_moon', '🌖', Array ['nature','twilight','planet','space','night','evening','sleep','waxing','waning_gibbous_moon','waning_gibbous_moon']),
        ('last_quarter_moon', '🌗', Array ['nature','twilight','planet','space','night','evening','sleep','last_quarter_moon','last_quarter_moon']),
        ('waning_crescent_moon', '🌘', Array ['nature','twilight','planet','space','night','evening','sleep','waning_crescent_moon','waning_crescent_moon']),
        ('crescent_moon', '🌙', Array ['night','sleep','sky','evening','magic','crescent_moon','crescent_moon']),
        ('new_moon_with_face', '🌚', Array ['with','nature','twilight','planet','space','night','evening','sleep','new_moon_with_face','new_moon_face']),
        ('first_quarter_moon_with_face', '🌛', Array ['with','nature','twilight','planet','space','night','evening','sleep','first_quarter_moon_with_face','first_quarter_moon_face']),
        ('last_quarter_moon_with_face', '🌜', Array ['with','nature','twilight','planet','space','night','evening','sleep','last_quarter_moon_with_face','last_quarter_moon_face']),
        ('full_moon', '🌕', Array ['nature','yellow','twilight','planet','space','night','evening','sleep','full_moon','full_moon']),
        ('sunny', '☀️', Array ['sunny','weather','nature','brightness','summer','beach','spring','sunny','sun']),
        ('full_moon_with_face', '🌝', Array ['with','nature','twilight','planet','space','night','evening','sleep','full_moon_with_face','full_moon_face']),
        ('sun_with_face', '🌞', Array ['nature','morning','sky','sun_with_face','sun_with_face']),
        ('ringed_planet', '🪐', Array ['outerspace','ringed_planet','ringed_planet']),
        ('star', '⭐', Array ['night','yellow','star','star']),
        ('star2', '🌟', Array ['star2','night','sparkle','awesome','good','magic','star2','glowing_star']),
        ('clock12', '🕛', Array ['clock12','o','clock','12','00:00','0000','12:00','1200','time','noon','midnight','midday','late','early','schedule','clock12','twelve_o’clock']),
        ('milky_way', '🌌', Array ['photo','space','stars','milky_way','milky_way']),
        ('cloud', '☁️', Array ['weather','sky','cloud','cloud']),
        ('partly_sunny', '⛅', Array ['partly','sunny','weather','nature','cloudy','morning','fall','spring','partly_sunny','sun_behind_cloud']),
        ('thunder_cloud_and_rain', '⛈️', Array ['thunder','weather','thunder_cloud_and_rain','cloud_with_lightning_and_rain']),
        ('mostly_sunny', '🌤️', Array ['mostly','sunny','weather','mostly_sunny','sun_behind_small_cloud']),
        ('barely_sunny', '🌥️', Array ['barely','sunny','weather','barely_sunny','sun_behind_large_cloud']),
        ('partly_sunny_rain', '🌦️', Array ['partly','sunny','weather','partly_sunny_rain','sun_behind_rain_cloud']),
        ('rain_cloud', '🌧️', Array ['weather','rain_cloud','cloud_with_rain']),
        ('snow_cloud', '🌨️', Array ['weather','snow_cloud','cloud_with_snow']),
        ('lightning', '🌩️', Array ['weather','thunder','lightning','cloud_with_lightning']),
        ('tornado', '🌪️', Array ['cloud','weather','cyclone','twister','tornado','tornado']),
        ('fog', '🌫️', Array ['weather','fog','fog']),
        ('wind_blowing_face', '🌬️', Array ['blowing','gust','air','wind_blowing_face','wind_face']),
        ('cyclone', '🌀', Array ['weather','swirl','blue','cloud','vortex','spiral','whirlpool','spin','tornado','hurricane','typhoon','cyclone','cyclone']),
        ('closed_umbrella', '🌂', Array ['weather','rain','drizzle','closed_umbrella','closed_umbrella']),
        ('umbrella', '☂️', Array ['weather','spring','umbrella','umbrella']),
        ('umbrella_with_rain_drops', '☔', Array ['rainy','weather','spring','umbrella_with_rain_drops','umbrella_with_rain_drops']),
        ('umbrella_on_ground', '⛱️', Array ['weather','summer','umbrella_on_ground','umbrella_on_ground']),
        ('zap', '⚡', Array ['zap','thunder','weather','lightning','bolt','fast','zap','high_voltage']),
        ('snowflake', '❄️', Array ['winter','season','cold','weather','christmas','xmas','snowflake','snowflake']),
        ('snowman', '☃️', Array ['winter','season','cold','weather','christmas','xmas','frozen','snowman','snowman']),
        ('snowman_without_snow', '⛄', Array ['winter','season','cold','weather','christmas','xmas','frozen','snowman_without_snow','snowman_without_snow']),
        ('comet', '☄️', Array ['space','comet','comet']),
        ('rainbow', '🌈', Array ['nature','happy','unicorn','face','photo','sky','spring','rainbow','rainbow']),
        ('fire', '🔥', Array ['hot','cook','flame','fire','fire']),
        ('droplet', '💧', Array ['water','drip','faucet','spring','droplet','droplet']),
        ('ocean', '🌊', Array ['ocean','sea','nature','tsunami','disaster','ocean','water_wave']),
        ('christmas_tree', '🎄', Array ['festival','vacation','december','xmas','celebration','christmas_tree','christmas_tree']),
        ('fireworks', '🎆', Array ['photo','festival','carnival','congratulations','fireworks','fireworks']),
        ('stars', '🌠', Array ['stars','night','photo','stars','shooting_star']),
        ('firecracker', '🧨', Array ['dynamite','boom','explode','explosion','explosive','firecracker','firecracker']),
        ('sparkles', '✨', Array ['stars','shine','shiny','cool','awesome','good','magic','sparkles','sparkles']),
        ('balloon', '🎈', Array ['party','celebration','birthday','circus','balloon','balloon']),
        ('jack_o_lantern', '🎃', Array ['jack','o','lantern','halloween','light','pumpkin','creepy','fall','jack_o_lantern','jack-o-lantern']),
        ('tada', '🎉', Array ['tada','congratulations','birthday','magic','circus','celebration','tada','party_popper']),
        ('confetti_ball', '🎊', Array ['festival','party','birthday','circus','confetti_ball','confetti_ball']),
        ('clock430', '🕟', Array ['clock430','four','thirty','4:30','430','16:30','1630','time','late','early','schedule','clock430','four-thirty']),
        ('dolls', '🎎', Array ['toy','kimono','dolls','japanese_dolls']),
        ('flags', '🎏', Array ['flags','fish','japanese','koinobori','banner','flags','carp_streamer']),
        ('wind_chime', '🎐', Array ['nature','ding','spring','bell','wind_chime','wind_chime']),
        ('rice_scene', '🎑', Array ['rice','scene','photo','japan','asia','tsukimi','rice_scene','moon_viewing_ceremony']),
        ('red_envelope', '🧧', Array ['gift','red_envelope','red_envelope']),
        ('ribbon', '🎀', Array ['decoration','pink','girl','bowtie','ribbon','ribbon']),
        ('gift', '🎁', Array ['present','birthday','christmas','xmas','gift','wrapped_gift']),
        ('bamboo', '🎍', Array ['bamboo','japanese','plant','nature','vegetable','panda','new','years','bamboo','pine_decoration']),
        ('tanabata_tree', '🎋', Array ['plant','nature','branch','summer','bamboo','wish','star','festival','tanzaku','tanabata_tree','tanabata_tree']),
        ('admission_tickets', '🎟️', Array ['sports','concert','entrance','admission_tickets','admission_tickets']),
        ('ticket', '🎫', Array ['event','concert','pass','ticket','ticket']),
        ('sports_medal', '🏅', Array ['award','winning','sports_medal','sports_medal']),
        ('trophy', '🏆', Array ['win','award','contest','place','ftw','ceremony','trophy','trophy']),
        ('first_place_medal', '🥇', Array ['first','award','winning','first_place_medal','1st_place_medal']),
        ('medal', '🎖️', Array ['award','winning','army','medal','military_medal']),
        ('thermometer', '🌡️', Array ['weather','temperature','hot','cold','thermometer','thermometer']),
        ('soccer', '⚽', Array ['sports','football','soccer','soccer_ball']),
        ('second_place_medal', '🥈', Array ['second','award','second_place_medal','2nd_place_medal']),
        ('softball', '🥎', Array ['sports','balls','softball','softball']),
        ('volleyball', '🏐', Array ['sports','balls','volleyball','volleyball']),
        ('reminder_ribbon', '🎗️', Array ['sports','cause','support','awareness','reminder_ribbon','reminder_ribbon']),
        ('baseball', '⚾', Array ['sports','balls','baseball','baseball']),
        ('rugby_football', '🏉', Array ['sports','team','rugby_football','rugby_football']),
        ('tennis', '🎾', Array ['sports','balls','green','tennis','tennis']),
        ('bowling', '🎳', Array ['sports','fun','play','bowling','bowling']),
        ('football', '🏈', Array ['sports','balls','NFL','football','american_football']),
        ('field_hockey_stick_and_ball', '🏑', Array ['stick','and','ball','sports','field_hockey_stick_and_ball','field_hockey']),
        ('flying_disc', '🥏', Array ['sports','frisbee','ultimate','flying_disc','flying_disc']),
        ('lacrosse', '🥍', Array ['sports','ball','stick','lacrosse','lacrosse']),
        ('badminton_racquet_and_shuttlecock', '🏸', Array ['racquet','and','shuttlecock','sports','badminton_racquet_and_shuttlecock','badminton']),
        ('ice_hockey_stick_and_puck', '🏒', Array ['stick','and','puck','sports','ice_hockey_stick_and_puck','ice_hockey']),
        ('boxing_glove', '🥊', Array ['sports','fighting','boxing_glove','boxing_glove']),
        ('table_tennis_paddle_and_ball', '🏓', Array ['table','tennis','paddle','and','ball','sports','pingpong','table_tennis_paddle_and_ball','ping_pong']),
        ('cricket_bat_and_ball', '🏏', Array ['bat','and','ball','sports','cricket_bat_and_ball','cricket_game']),
        ('goal_net', '🥅', Array ['sports','goal_net','goal_net']),
        ('fishing_pole_and_fish', '🎣', Array ['and','fish','food','hobby','summer','fishing_pole_and_fish','fishing_pole']),
        ('golf', '⛳', Array ['golf','sports','business','summer','golf','flag_in_hole']),
        ('sparkler', '🎇', Array ['stars','night','shine','sparkler','sparkler']),
        ('martial_arts_uniform', '🥋', Array ['judo','karate','taekwondo','martial_arts_uniform','martial_arts_uniform']),
        ('ice_skate', '⛸️', Array ['sports','ice_skate','ice_skate']),
        ('running_shirt_with_sash', '🎽', Array ['with','sash','play','pageant','running_shirt_with_sash','running_shirt']),
        ('ski', '🎿', Array ['ski','sports','winter','cold','snow','ski','skis']),
        ('dart', '🎯', Array ['dart','direct','hit','game','play','bar','target','dart','bullseye']),
        ('yo-yo', '🪀', Array ['yo','toy','yo-yo','yo-yo']),
        ('sled', '🛷', Array ['sleigh','luge','toboggan','sled','sled']),
        ('curling_stone', '🥌', Array ['sports','curling_stone','curling_stone']),
        ('gun', '🔫', Array ['gun','violence','weapon','revolver','gun','pistol']),
        ('kite', '🪁', Array ['wind','fly','kite','kite']),
        ('basketball', '🏀', Array ['sports','balls','NBA','basketball','basketball']),
        ('video_game', '🎮', Array ['play','console','PS4','controller','video_game','video_game']),
        ('crystal_ball', '🔮', Array ['disco','party','magic','circus','fortune','teller','crystal_ball','crystal_ball']),
        ('8ball', '🎱', Array ['8ball','pool','8','ball','hobby','game','luck','magic','8ball','billiards']),
        ('joystick', '🕹️', Array ['game','play','joystick','joystick']),
        ('slot_machine', '🎰', Array ['bet','gamble','vegas','fruit','luck','casino','slot_machine','slot_machine']),
        ('teddy_bear', '🧸', Array ['plush','stuffed','teddy_bear','teddy_bear']),
        ('pinata', '🪅', Array ['mexico','candy','celebration','pinata','pinata']),
        ('magic_wand', '🪄', Array ['supernature','power','magic_wand','magic_wand']),
        ('game_die', '🎲', Array ['dice','random','tabletop','play','luck','game_die','game_die']),
        ('mirror_ball', '🪩', Array ['disco','dance','party','mirror_ball','mirror_ball']),
        ('nesting_dolls', '🪆', Array ['matryoshka','toy','nesting_dolls','nesting_dolls']),
        ('jigsaw', '🧩', Array ['jigsaw','interlocking','jigsaw','puzzle_piece']),
        ('clubs', '♣️', Array ['clubs','poker','cards','magic','suits','clubs','club_suit']),
        ('spades', '♠️', Array ['spades','poker','cards','suits','magic','spades','spade_suit']),
        ('hearts', '♥️', Array ['hearts','poker','cards','magic','suits','hearts','heart_suit']),
        ('chess_pawn', '♟️', Array ['expendable','chess_pawn','chess_pawn']),
        ('black_joker', '🃏', Array ['black','poker','cards','game','play','magic','black_joker','joker']),
        ('mahjong', '🀄', Array ['game','play','chinese','kanji','mahjong','mahjong_red_dragon']),
        ('performing_arts', '🎭', Array ['acting','theater','drama','performing_arts','performing_arts']),
        ('frame_with_picture', '🖼️', Array ['frame','with','photography','frame_with_picture','framed_picture']),
        ('diving_mask', '🤿', Array ['sport','ocean','diving_mask','diving_mask']),
        ('sewing_needle', '🪡', Array ['stitches','sewing_needle','sewing_needle']),
        ('thread', '🧵', Array ['needle','sewing','spool','string','thread','thread']),
        ('yarn', '🧶', Array ['ball','crochet','knit','yarn','yarn']),
        ('flower_playing_cards', '🎴', Array ['game','sunset','red','flower_playing_cards','flower_playing_cards']),
        ('knot', '🪢', Array ['rope','scout','knot','knot']),
        ('dark_sunglasses', '🕶️', Array ['dark','face','cool','accessories','dark_sunglasses','sunglasses']),
        ('eyeglasses', '👓', Array ['eyeglasses','fashion','accessories','eyesight','nerdy','dork','geek','eyeglasses','glasses']),
        ('diamonds', '♦️', Array ['diamonds','poker','cards','magic','suits','diamonds','diamond_suit']),
        ('safety_vest', '🦺', Array ['protection','safety_vest','safety_vest']),
        ('necktie', '👔', Array ['shirt','suitup','formal','fashion','cloth','business','necktie','necktie']),
        ('shirt', '👕', Array ['shirt','tshirt','t','fashion','cloth','casual','tee','shirt','t-shirt']),
        ('goggles', '🥽', Array ['eyes','protection','safety','goggles','goggles']),
        ('jeans', '👖', Array ['fashion','shopping','jeans','jeans']),
        ('lab_coat', '🥼', Array ['doctor','experiment','scientist','chemist','lab_coat','lab_coat']),
        ('scarf', '🧣', Array ['neck','winter','clothes','scarf','scarf']),
        ('socks', '🧦', Array ['stockings','clothes','socks','socks']),
        ('kimono', '👘', Array ['dress','fashion','women','female','japanese','kimono','kimono']),
        ('gloves', '🧤', Array ['hands','winter','clothes','gloves','gloves']),
        ('coat', '🧥', Array ['jacket','coat','coat']),
        ('sari', '🥻', Array ['dress','sari','sari']),
        ('art', '🎨', Array ['art','design','paint','draw','colors','art','artist_palette']),
        ('briefs', '🩲', Array ['clothing','briefs','briefs']),
        ('dress', '👗', Array ['clothes','fashion','shopping','dress','dress']),
        ('bikini', '👙', Array ['swimming','female','woman','girl','fashion','beach','summer','bikini','bikini']),
        ('purse', '👛', Array ['fashion','accessories','money','sales','shopping','purse','purse']),
        ('pouch', '👝', Array ['clutch','bag','accessories','shopping','pouch','pouch']),
        ('shopping_bags', '🛍️', Array ['mall','buy','purchase','shopping_bags','shopping_bags']),
        ('handbag', '👜', Array ['fashion','accessory','accessories','shopping','handbag','handbag']),
        ('one-piece_swimsuit', '🩱', Array ['one','piece','fashion','one-piece_swimsuit','one-piece_swimsuit']),
        ('school_satchel', '🎒', Array ['school','satchel','student','education','bag','school_satchel','backpack']),
        ('mans_shoe', '👞', Array ['man','s','fashion','male','mans_shoe','mans_shoe']),
        ('athletic_shoe', '👟', Array ['athletic','shoes','sports','sneakers','athletic_shoe','running_shoe']),
        ('hiking_boot', '🥾', Array ['backpacking','camping','hiking_boot','hiking_boot']),
        ('thong_sandal', '🩴', Array ['footwear','summer','thong_sandal','thong_sandal']),
        ('high_heel', '👠', Array ['high','heel','heeled','fashion','shoes','female','pumps','stiletto','high_heel','high-heeled_shoe']),
        ('boot', '👢', Array ['boot','woman','s','shoes','fashion','boot','womans_boots']),
        ('ballet_shoes', '🩰', Array ['dance','ballet_shoes','ballet_shoes']),
        ('womans_flat_shoe', '🥿', Array ['womans','ballet','slip','on','slipper','womans_flat_shoe','flat_shoe']),
        ('womans_clothes', '👚', Array ['woman','s','fashion','shopping','bags','female','womans_clothes','womans_clothes']),
        ('tophat', '🎩', Array ['tophat','magic','gentleman','classy','circus','tophat','top_hat']),
        ('womans_hat', '👒', Array ['woman','s','fashion','accessories','female','lady','spring','womans_hat','womans_hat']),
        ('crown', '👑', Array ['king','kod','leader','royalty','lord','crown','crown']),
        ('billed_cap', '🧢', Array ['baseball','billed_cap','billed_cap']),
        ('prayer_beads', '📿', Array ['dhikr','religious','prayer_beads','prayer_beads']),
        ('mortar_board', '🎓', Array ['mortar','board','school','college','degree','university','hat','legal','learn','education','mortar_board','graduation_cap']),
        ('third_place_medal', '🥉', Array ['third','award','third_place_medal','3rd_place_medal']),
        ('military_helmet', '🪖', Array ['army','protection','military_helmet','military_helmet']),
        ('helmet_with_white_cross', '⛑️', Array ['with','white','cross','worker','s','construction','build','helmet_with_white_cross','rescue_worker’s_helmet']),
        ('gem', '💎', Array ['blue','ruby','diamond','jewelry','gem','gem_stone']),
        ('mute', '🔇', Array ['mute','sound','volume','silence','quiet','mute','muted_speaker']),
        ('speaker', '🔈', Array ['low','volume','sound','silence','broadcast','speaker','speaker']),
        ('sound', '🔉', Array ['sound','broadcast','sound','speaker_medium_volume']),
        ('ring', '💍', Array ['wedding','propose','marriage','valentines','diamond','fashion','jewelry','gem','engagement','ring','ring']),
        ('loud_sound', '🔊', Array ['loud','sound','noise','noisy','broadcast','loud_sound','speaker_high_volume']),
        ('loudspeaker', '📢', Array ['volume','sound','loudspeaker','loudspeaker']),
        ('mega', '📣', Array ['mega','sound','speaker','volume','mega','megaphone']),
        ('bell', '🔔', Array ['sound','notification','christmas','xmas','chime','bell','bell']),
        ('musical_score', '🎼', Array ['treble','clef','compose','musical_score','musical_score']),
        ('no_bell', '🔕', Array ['no','sound','volume','mute','quiet','silent','no_bell','bell_with_slash']),
        ('postal_horn', '📯', Array ['instrument','music','postal_horn','postal_horn']),
        ('musical_note', '🎵', Array ['score','tone','sound','musical_note','musical_note']),
        ('studio_microphone', '🎙️', Array ['sing','recording','artist','talkshow','studio_microphone','studio_microphone']),
        ('notes', '🎶', Array ['music','score','notes','musical_notes']),
        ('control_knobs', '🎛️', Array ['dial','control_knobs','control_knobs']),
        ('headphones', '🎧', Array ['headphones','music','score','gadgets','headphones','headphone']),
        ('microphone', '🎤', Array ['sound','music','PA','sing','talkshow','microphone','microphone']),
        ('radio', '📻', Array ['communication','music','podcast','program','radio','radio']),
        ('saxophone', '🎷', Array ['music','instrument','jazz','blues','saxophone','saxophone']),
        ('level_slider', '🎚️', Array ['scale','level_slider','level_slider']),
        ('accordion', '🪗', Array ['music','accordion','accordion']),
        ('guitar', '🎸', Array ['music','instrument','guitar','guitar']),
        ('musical_keyboard', '🎹', Array ['piano','instrument','compose','musical_keyboard','musical_keyboard']),
        ('violin', '🎻', Array ['music','instrument','orchestra','symphony','violin','violin']),
        ('banjo', '🪕', Array ['music','instructment','banjo','banjo']),
        ('drum_with_drumsticks', '🥁', Array ['with','drumsticks','music','instrument','snare','drum_with_drumsticks','drum']),
        ('long_drum', '🪘', Array ['music','long_drum','long_drum']),
        ('iphone', '📱', Array ['iphone','technology','apple','gadgets','dial','iphone','mobile_phone']),
        ('shorts', '🩳', Array ['clothing','shorts','shorts']),
        ('lipstick', '💄', Array ['female','girl','fashion','woman','lipstick','lipstick']),
        ('calling', '📲', Array ['calling','iphone','incoming','calling','mobile_phone_with_arrow']),
        ('telephone_receiver', '📞', Array ['technology','communication','dial','telephone_receiver','telephone_receiver']),
        ('low_battery', '🪫', Array ['drained','dead','low_battery','low_battery']),
        ('fax', '📠', Array ['communication','technology','fax','fax_machine']),
        ('battery', '🔋', Array ['power','energy','sustain','battery','battery']),
        ('pager', '📟', Array ['bbcall','oldschool','90s','pager','pager']),
        ('electric_plug', '🔌', Array ['charger','power','electric_plug','electric_plug']),
        ('desktop_computer', '🖥️', Array ['technology','computing','screen','desktop_computer','desktop_computer']),
        ('computer', '💻', Array ['computer','technology','screen','display','monitor','computer','laptop']),
        ('keyboard', '⌨️', Array ['technology','computer','type','input','text','keyboard','keyboard']),
        ('three_button_mouse', '🖱️', Array ['three','button','click','three_button_mouse','computer_mouse']),
        ('trumpet', '🎺', Array ['music','brass','trumpet','trumpet']),
        ('printer', '🖨️', Array ['paper','ink','printer','printer']),
        ('floppy_disk', '💾', Array ['oldschool','technology','save','90s','80s','floppy_disk','floppy_disk']),
        ('cd', '💿', Array ['cd','disk','technology','dvd','90s','cd','optical_disc']),
        ('minidisc', '💽', Array ['computer','disk','technology','record','data','90s','minidisc','minidisc']),
        ('dvd', '📀', Array ['cd','disk','disc','dvd','dvd']),
        ('abacus', '🧮', Array ['calculation','abacus','abacus']),
        ('movie_camera', '🎥', Array ['film','record','movie_camera','movie_camera']),
        ('tv', '📺', Array ['tv','technology','program','oldschool','show','tv','television']),
        ('film_projector', '📽️', Array ['video','tape','record','movie','film_projector','film_projector']),
        ('clapper', '🎬', Array ['movie','film','record','clapper','clapper_board']),
        ('film_frames', '🎞️', Array ['movie','film_frames','film_frames']),
        ('camera', '📷', Array ['gadgets','photography','camera','camera']),
        ('camera_with_flash', '📸', Array ['photography','gadgets','camera_with_flash','camera_with_flash']),
        ('video_camera', '📹', Array ['film','record','video_camera','video_camera']),
        ('mag_right', '🔎', Array ['mag','search','zoom','find','detective','mag_right','magnifying_glass_tilted_right']),
        ('mag', '🔍', Array ['mag','search','zoom','find','detective','mag','magnifying_glass_tilted_left']),
        ('vhs', '📼', Array ['vhs','record','video','oldschool','90s','80s','vhs','videocassette']),
        ('bulb', '💡', Array ['electricity','idea','bulb','light_bulb']),
        ('flashlight', '🔦', Array ['dark','camping','sight','night','flashlight','flashlight']),
        ('diya_lamp', '🪔', Array ['lighting','diya_lamp','diya_lamp']),
        ('candle', '🕯️', Array ['fire','wax','candle','candle']),
        ('closed_book', '📕', Array ['read','library','knowledge','textbook','learn','closed_book','closed_book']),
        ('izakaya_lantern', '🏮', Array ['red','paper','light','halloween','spooky','izakaya_lantern','izakaya_lantern']),
        ('notebook_with_decorative_cover', '📔', Array ['classroom','notes','record','paper','study','notebook_with_decorative_cover','notebook_with_decorative_cover']),
        ('book', '📖', Array ['read','library','knowledge','literature','learn','study','book','open_book']),
        ('green_book', '📗', Array ['read','library','knowledge','study','green_book','green_book']),
        ('notebook', '📓', Array ['stationery','record','notes','paper','study','notebook','notebook']),
        ('books', '📚', Array ['literature','library','study','books','books']),
        ('blue_book', '📘', Array ['read','library','knowledge','learn','study','blue_book','blue_book']),
        ('orange_book', '📙', Array ['read','library','knowledge','textbook','study','orange_book','orange_book']),
        ('ledger', '📒', Array ['notes','paper','ledger','ledger']),
        ('page_with_curl', '📃', Array ['documents','office','paper','page_with_curl','page_with_curl']),
        ('page_facing_up', '📄', Array ['documents','office','paper','information','page_facing_up','page_facing_up']),
        ('rolled_up_newspaper', '🗞️', Array ['rolled','up','press','headline','rolled_up_newspaper','rolled-up_newspaper']),
        ('bookmark_tabs', '📑', Array ['favorite','save','order','tidy','bookmark_tabs','bookmark_tabs']),
        ('scroll', '📜', Array ['documents','ancient','history','paper','scroll','scroll']),
        ('newspaper', '📰', Array ['press','headline','newspaper','newspaper']),
        ('bookmark', '🔖', Array ['favorite','label','save','bookmark','bookmark']),
        ('label', '🏷️', Array ['sale','tag','label','label']),
        ('trackball', '🖲️', Array ['technology','trackpad','trackball','trackball']),
        ('yen', '💴', Array ['money','sales','japanese','dollar','currency','yen','yen_banknote']),
        ('coin', '🪙', Array ['money','currency','coin','coin']),
        ('dollar', '💵', Array ['money','sales','bill','currency','dollar','dollar_banknote']),
        ('euro', '💶', Array ['money','sales','dollar','currency','euro','euro_banknote']),
        ('money_with_wings', '💸', Array ['dollar','bills','payment','sale','money_with_wings','money_with_wings']),
        ('receipt', '🧾', Array ['accounting','expenses','receipt','receipt']),
        ('pound', '💷', Array ['british','sterling','money','sales','bills','uk','england','currency','pound','pound_banknote']),
        ('chart', '💹', Array ['green','square','graph','presentation','stats','chart','chart_increasing_with_yen']),
        ('credit_card', '💳', Array ['money','sales','dollar','bill','payment','shopping','credit_card','credit_card']),
        ('email', '✉️', Array ['email','letter','postal','inbox','communication','email','envelope']),
        ('incoming_envelope', '📨', Array ['email','inbox','incoming_envelope','incoming_envelope']),
        ('outbox_tray', '📤', Array ['inbox','email','outbox_tray','outbox_tray']),
        ('inbox_tray', '📥', Array ['email','documents','inbox_tray','inbox_tray']),
        ('package', '📦', Array ['mail','gift','cardboard','box','moving','package','package']),
        ('envelope_with_arrow', '📩', Array ['email','communication','envelope_with_arrow','envelope_with_arrow']),
        ('e-mail', '📧', Array ['e','mail','communication','inbox','e-mail','e-mail']),
        ('mailbox', '📫', Array ['email','inbox','communication','mailbox','closed_mailbox_with_raised_flag']),
        ('mailbox_closed', '📪', Array ['email','communication','inbox','mailbox_closed','closed_mailbox_with_lowered_flag']),
        ('moneybag', '💰', Array ['moneybag','dollar','payment','coins','sale','moneybag','money_bag']),
        ('sandal', '👡', Array ['woman','s','shoes','fashion','flip','flops','sandal','womans_sandal']),
        ('mailbox_with_no_mail', '📭', Array ['no','mail','email','inbox','mailbox_with_no_mail','open_mailbox_with_lowered_flag']),
        ('postbox', '📮', Array ['email','letter','envelope','postbox','postbox']),
        ('mailbox_with_mail', '📬', Array ['mail','email','inbox','communication','mailbox_with_mail','open_mailbox_with_raised_flag']),
        ('lower_left_fountain_pen', '🖋️', Array ['lower','left','stationery','writing','write','lower_left_fountain_pen','fountain_pen']),
        ('ballot_box_with_ballot', '🗳️', Array ['election','vote','ballot_box_with_ballot','ballot_box_with_ballot']),
        ('lower_left_paintbrush', '🖌️', Array ['lower','left','drawing','creativity','art','lower_left_paintbrush','paintbrush']),
        ('phone', '☎️', Array ['phone','technology','communication','dial','phone','telephone']),
        ('lower_left_crayon', '🖍️', Array ['lower','left','drawing','creativity','lower_left_crayon','crayon']),
        ('lower_left_ballpoint_pen', '🖊️', Array ['lower','left','ballpoint','stationery','writing','write','lower_left_ballpoint_pen','pen']),
        ('memo', '📝', Array ['pencil','write','documents','stationery','paper','writing','legal','exam','quiz','test','study','compose','memo','memo']),
        ('open_file_folder', '📂', Array ['documents','load','open_file_folder','open_file_folder']),
        ('file_folder', '📁', Array ['documents','business','office','file_folder','file_folder']),
        ('date', '📅', Array ['date','schedule','date','calendar']),
        ('calendar', '📆', Array ['tear','off','schedule','date','planning','calendar','tear-off_calendar']),
        ('black_nib', '✒️', Array ['pen','stationery','writing','write','black_nib','black_nib']),
        ('briefcase', '💼', Array ['business','documents','work','law','legal','job','career','briefcase','briefcase']),
        ('pencil2', '✏️', Array ['pencil2','stationery','write','paper','writing','school','study','pencil2','pencil']),
        ('spiral_calendar_pad', '🗓️', Array ['pad','date','schedule','planning','spiral_calendar_pad','spiral_calendar']),
        ('card_index_dividers', '🗂️', Array ['organizing','business','stationery','card_index_dividers','card_index_dividers']),
        ('chart_with_upwards_trend', '📈', Array ['with','upwards','trend','graph','presentation','stats','recovery','business','economics','money','sales','good','success','chart_with_upwards_trend','chart_increasing']),
        ('clipboard', '📋', Array ['stationery','documents','clipboard','clipboard']),
        ('chart_with_downwards_trend', '📉', Array ['with','downwards','trend','graph','presentation','stats','recession','business','economics','money','sales','bad','failure','chart_with_downwards_trend','chart_decreasing']),
        ('pushpin', '📌', Array ['stationery','mark','here','pushpin','pushpin']),
        ('bar_chart', '📊', Array ['graph','presentation','stats','bar_chart','bar_chart']),
        ('paperclip', '📎', Array ['documents','stationery','paperclip','paperclip']),
        ('linked_paperclips', '🖇️', Array ['documents','stationery','linked_paperclips','linked_paperclips']),
        ('triangular_ruler', '📐', Array ['stationery','math','architect','sketch','triangular_ruler','triangular_ruler']),
        ('scissors', '✂️', Array ['stationery','cut','scissors','scissors']),
        ('card_file_box', '🗃️', Array ['business','stationery','card_file_box','card_file_box']),
        ('straight_ruler', '📏', Array ['stationery','calculate','length','math','school','drawing','architect','sketch','straight_ruler','straight_ruler']),
        ('wastebasket', '🗑️', Array ['bin','trash','rubbish','garbage','toss','wastebasket','wastebasket']),
        ('lock', '🔒', Array ['locked','security','password','padlock','lock','lock']),
        ('file_cabinet', '🗄️', Array ['filing','organizing','file_cabinet','file_cabinet']),
        ('card_index', '📇', Array ['business','stationery','card_index','card_index']),
        ('closed_lock_with_key', '🔐', Array ['closed','lock','security','privacy','closed_lock_with_key','locked_with_key']),
        ('key', '🔑', Array ['lock','door','password','key','key']),
        ('old_key', '🗝️', Array ['lock','door','password','old_key','old_key']),
        ('hammer', '🔨', Array ['tools','build','create','hammer','hammer']),
        ('axe', '🪓', Array ['tool','chop','cut','axe','axe']),
        ('pick', '⛏️', Array ['tools','dig','pick','pick']),
        ('hammer_and_pick', '⚒️', Array ['tools','build','create','hammer_and_pick','hammer_and_pick']),
        ('round_pushpin', '📍', Array ['stationery','location','map','here','round_pushpin','round_pushpin']),
        ('lock_with_ink_pen', '🔏', Array ['lock','ink','security','secret','lock_with_ink_pen','locked_with_pen']),
        ('dagger_knife', '🗡️', Array ['knife','weapon','dagger_knife','dagger']),
        ('spiral_note_pad', '🗒️', Array ['note','pad','memo','stationery','spiral_note_pad','spiral_notepad']),
        ('shield', '🛡️', Array ['protection','security','shield','shield']),
        ('unlock', '🔓', Array ['unlock','privacy','security','unlock','unlocked']),
        ('boomerang', '🪃', Array ['weapon','boomerang','boomerang']),
        ('bomb', '💣', Array ['boom','explode','explosion','terrorism','bomb','bomb']),
        ('carpentry_saw', '🪚', Array ['cut','chop','carpentry_saw','carpentry_saw']),
        ('wrench', '🔧', Array ['tools','diy','ikea','fix','maintainer','wrench','wrench']),
        ('screwdriver', '🪛', Array ['tools','screwdriver','screwdriver']),
        ('compression', '🗜️', Array ['compression','tool','compression','clamp']),
        ('crossed_swords', '⚔️', Array ['weapon','crossed_swords','crossed_swords']),
        ('scales', '⚖️', Array ['scales','law','fairness','weight','scales','balance_scale']),
        ('link', '🔗', Array ['rings','url','link','link']),
        ('gear', '⚙️', Array ['cog','gear','gear']),
        ('bow_and_arrow', '🏹', Array ['sports','bow_and_arrow','bow_and_arrow']),
        ('nut_and_bolt', '🔩', Array ['handy','tools','fix','nut_and_bolt','nut_and_bolt']),
        ('hook', '🪝', Array ['tools','hook','hook']),
        ('magnet', '🧲', Array ['attraction','magnetic','magnet','magnet']),
        ('chains', '⛓️', Array ['lock','arrest','chains','chains']),
        ('probing_cane', '🦯', Array ['probing','accessibility','probing_cane','white_cane']),
        ('alembic', '⚗️', Array ['distilling','science','experiment','chemistry','alembic','alembic']),
        ('toolbox', '🧰', Array ['tools','diy','fix','maintainer','mechanic','toolbox','toolbox']),
        ('test_tube', '🧪', Array ['chemistry','experiment','lab','science','test_tube','test_tube']),
        ('dna', '🧬', Array ['biologist','genetics','life','dna','dna']),
        ('hammer_and_wrench', '🛠️', Array ['tools','build','create','hammer_and_wrench','hammer_and_wrench']),
        ('ladder', '🪜', Array ['tools','ladder','ladder']),
        ('syringe', '💉', Array ['health','hospital','drugs','blood','medicine','needle','doctor','nurse','syringe','syringe']),
        ('telescope', '🔭', Array ['stars','space','zoom','science','astronomy','telescope','telescope']),
        ('satellite_antenna', '📡', Array ['communication','future','radio','space','satellite_antenna','satellite_antenna']),
        ('petri_dish', '🧫', Array ['bacteria','biology','culture','lab','petri_dish','petri_dish']),
        ('drop_of_blood', '🩸', Array ['period','hurt','harm','wound','drop_of_blood','drop_of_blood']),
        ('pill', '💊', Array ['health','medicine','doctor','pharmacy','drug','pill','pill']),
        ('adhesive_bandage', '🩹', Array ['heal','adhesive_bandage','adhesive_bandage']),
        ('x-ray', '🩻', Array ['x','ray','skeleton','medicine','x-ray','x-ray']),
        ('stethoscope', '🩺', Array ['health','stethoscope','stethoscope']),
        ('door', '🚪', Array ['house','entry','exit','door','door']),
        ('mirror', '🪞', Array ['reflection','mirror','mirror']),
        ('window', '🪟', Array ['scenery','window','window']),
        ('bed', '🛏️', Array ['sleep','rest','bed','bed']),
        ('elevator', '🛗', Array ['lift','elevator','elevator']),
        ('microscope', '🔬', Array ['laboratory','experiment','zoomin','science','study','microscope','microscope']),
        ('shower', '🚿', Array ['clean','water','bathroom','shower','shower']),
        ('plunger', '🪠', Array ['toilet','plunger','plunger']),
        ('toilet', '🚽', Array ['restroom','wc','washroom','bathroom','potty','toilet','toilet']),
        ('couch_and_lamp', '🛋️', Array ['read','chill','couch_and_lamp','couch_and_lamp']),
        ('bathtub', '🛁', Array ['clean','shower','bathroom','bathtub','bathtub']),
        ('safety_pin', '🧷', Array ['diaper','safety_pin','safety_pin']),
        ('razor', '🪒', Array ['cut','razor','razor']),
        ('chair', '🪑', Array ['sit','furniture','chair','chair']),
        ('basket', '🧺', Array ['laundry','basket','basket']),
        ('broom', '🧹', Array ['cleaning','sweeping','witch','broom','broom']),
        ('roll_of_paper', '🧻', Array ['roll_of_paper','roll_of_paper']),
        ('bucket', '🪣', Array ['water','container','bucket','bucket']),
        ('soap', '🧼', Array ['bar','bathing','cleaning','lather','soap','soap']),
        ('bubbles', '🫧', Array ['soap','fun','carbonation','sparkling','bubbles','bubbles']),
        ('fire_extinguisher', '🧯', Array ['quench','fire_extinguisher','fire_extinguisher']),
        ('sponge', '🧽', Array ['absorbing','cleaning','porous','sponge','sponge']),
        ('toothbrush', '🪥', Array ['hygiene','dental','toothbrush','toothbrush']),
        ('smoking', '🚬', Array ['smoking','kills','tobacco','joint','smoke','smoking','cigarette']),
        ('lotion_bottle', '🧴', Array ['moisturizer','sunscreen','lotion_bottle','lotion_bottle']),
        ('coffin', '⚰️', Array ['vampire','dead','die','death','rip','graveyard','cemetery','casket','funeral','box','coffin','coffin']),
        ('funeral_urn', '⚱️', Array ['dead','die','death','rip','ashes','funeral_urn','funeral_urn']),
        ('shopping_trolley', '🛒', Array ['trolley','shopping_trolley','shopping_cart']),
        ('nazar_amulet', '🧿', Array ['bead','charm','nazar_amulet','nazar_amulet']),
        ('moyai', '🗿', Array ['moyai','rock','easter','island','moyai','moai']),
        ('headstone', '🪦', Array ['death','rip','grave','headstone','headstone']),
        ('hamsa', '🪬', Array ['religion','protection','hamsa','hamsa']),
        ('identification_card', '🪪', Array ['document','identification_card','identification_card']),
        ('placard', '🪧', Array ['announcement','placard','placard']),
        ('mouse_trap', '🪤', Array ['cheese','mouse_trap','mouse_trap']),
        ('womens', '🚺', Array ['womens','women','s','purple','square','woman','female','toilet','loo','restroom','gender','womens','women’s_room']),
        ('potable_water', '🚰', Array ['blue','square','liquid','restroom','cleaning','faucet','potable_water','potable_water']),
        ('wheelchair', '♿', Array ['blue','square','disabled','accessibility','wheelchair','wheelchair_symbol']),
        ('put_litter_in_its_place', '🚮', Array ['put','its','place','blue','square','human','info','put_litter_in_its_place','litter_in_bin_sign']),
        ('restroom', '🚻', Array ['blue','square','toilet','refresh','wc','gender','restroom','restroom']),
        ('mens', '🚹', Array ['mens','men','s','toilet','restroom','wc','blue','square','gender','male','mens','men’s_room']),
        ('baby_symbol', '🚼', Array ['orange','square','child','baby_symbol','baby_symbol']),
        ('wc', '🚾', Array ['wc','toilet','restroom','blue','square','wc','water_closet']),
        ('crutch', '🩼', Array ['accessibility','assist','crutch','crutch']),
        ('passport_control', '🛂', Array ['custom','blue','square','passport_control','passport_control']),
        ('warning', '⚠️', Array ['exclamation','wip','alert','error','problem','issue','warning','warning']),
        ('baggage_claim', '🛄', Array ['blue','square','airport','transport','baggage_claim','baggage_claim']),
        ('left_luggage', '🛅', Array ['blue','square','travel','left_luggage','left_luggage']),
        ('no_entry', '⛔', Array ['limit','security','privacy','bad','denied','stop','circle','no_entry','no_entry']),
        ('atm', '🏧', Array ['money','sales','cash','blue','square','payment','bank','atm','atm_sign']),
        ('children_crossing', '🚸', Array ['school','warning','danger','sign','driving','yellow','diamond','children_crossing','children_crossing']),
        ('no_pedestrians', '🚷', Array ['rules','crossing','walking','circle','no_pedestrians','no_pedestrians']),
        ('customs', '🛃', Array ['passport','border','blue','square','customs','customs']),
        ('no_smoking', '🚭', Array ['cigarette','blue','square','smell','smoke','no_smoking','no_smoking']),
        ('no_mobile_phones', '📵', Array ['iphone','mute','circle','no_mobile_phones','no_mobile_phones']),
        ('underage', '🔞', Array ['underage','18','drink','pub','night','minor','circle','underage','no_one_under_eighteen']),
        ('arrow_up', '⬆️', Array ['blue','square','continue','top','direction','arrow_up','up_arrow']),
        ('non-potable_water', '🚱', Array ['non','potable','drink','faucet','tap','circle','non-potable_water','non-potable_water']),
        ('radioactive_sign', '☢️', Array ['sign','nuclear','danger','radioactive_sign','radioactive']),
        ('biohazard_sign', '☣️', Array ['sign','danger','biohazard_sign','biohazard']),
        ('do_not_litter', '🚯', Array ['do','not','litter','trash','bin','garbage','circle','do_not_litter','no_littering']),
        ('arrow_lower_left', '↙️', Array ['lower','left','down','blue','square','direction','diagonal','southwest','arrow_lower_left','down-left_arrow']),
        ('arrow_left', '⬅️', Array ['blue','square','previous','back','arrow_left','left_arrow']),
        ('arrow_right', '➡️', Array ['blue','square','next','arrow_right','right_arrow']),
        ('arrow_lower_right', '↘️', Array ['lower','right','down','blue','square','direction','diagonal','southeast','arrow_lower_right','south_east_arrow']),
        ('arrow_down', '⬇️', Array ['blue','square','direction','bottom','arrow_down','down_arrow']),
        ('arrow_upper_left', '↖️', Array ['upper','left','up','blue','square','point','direction','diagonal','northwest','arrow_upper_left','up-left_arrow']),
        ('no_entry_sign', '🚫', Array ['no','entry','sign','forbid','stop','limit','denied','disallow','circle','no_entry_sign','prohibited']),
        ('arrow_right_hook', '↪️', Array ['hook','blue','square','return','rotate','direction','arrow_right_hook','left_arrow_curving_right']),
        ('no_bicycles', '🚳', Array ['cyclist','prohibited','circle','no_bicycles','no_bicycles']),
        ('leftwards_arrow_with_hook', '↩️', Array ['leftwards','with','hook','back','return','blue','square','undo','enter','leftwards_arrow_with_hook','right_arrow_curving_left']),
        ('left_right_arrow', '↔️', Array ['shape','direction','horizontal','sideways','left_right_arrow','left_right_arrow']),
        ('arrow_heading_down', '⤵️', Array ['heading','blue','square','direction','bottom','arrow_heading_down','right_arrow_curving_down']),
        ('arrows_clockwise', '🔃', Array ['sync','cycle','round','repeat','arrows_clockwise','clockwise_vertical_arrows']),
        ('arrows_counterclockwise', '🔄', Array ['blue','square','sync','cycle','arrows_counterclockwise','counterclockwise_arrows_button']),
        ('end', '🔚', Array ['words','end','end_arrow']),
        ('on', '🔛', Array ['on','words','on','on!_arrow']),
        ('arrow_upper_right', '↗️', Array ['upper','right','up','blue','square','point','direction','diagonal','northeast','arrow_upper_right','up-right_arrow']),
        ('soon', '🔜', Array ['words','soon','soon_arrow']),
        ('back', '🔙', Array ['words','return','back','back_arrow']),
        ('arrow_heading_up', '⤴️', Array ['heading','blue','square','direction','top','arrow_heading_up','right_arrow_curving_up']),
        ('top', '🔝', Array ['words','blue','square','top','top_arrow']),
        ('om_symbol', '🕉️', Array ['symbol','hinduism','buddhism','sikhism','jainism','om_symbol','om']),
        ('latin_cross', '✝️', Array ['christianity','latin_cross','latin_cross']),
        ('wheel_of_dharma', '☸️', Array ['hinduism','buddhism','sikhism','jainism','wheel_of_dharma','wheel_of_dharma']),
        ('place_of_worship', '🛐', Array ['religion','church','temple','prayer','place_of_worship','place_of_worship']),
        ('star_of_david', '✡️', Array ['judaism','star_of_david','star_of_david']),
        ('arrow_up_down', '↕️', Array ['blue','square','direction','way','vertical','arrow_up_down','up_down_arrow']),
        ('atom_symbol', '⚛️', Array ['science','physics','chemistry','atom_symbol','atom_symbol']),
        ('star_and_crescent', '☪️', Array ['islam','star_and_crescent','star_and_crescent']),
        ('aries', '♈', Array ['sign','purple','square','zodiac','astrology','aries','aries']),
        ('peace_symbol', '☮️', Array ['hippie','peace_symbol','peace_symbol']),
        ('taurus', '♉', Array ['purple','square','sign','zodiac','astrology','taurus','taurus']),
        ('gemini', '♊', Array ['sign','zodiac','purple','square','astrology','gemini','gemini']),
        ('cancer', '♋', Array ['sign','zodiac','purple','square','astrology','cancer','cancer']),
        ('menorah_with_nine_branches', '🕎', Array ['with','nine','branches','hanukkah','candles','jewish','menorah_with_nine_branches','menorah']),
        ('virgo', '♍', Array ['sign','zodiac','purple','square','astrology','virgo','virgo']),
        ('leo', '♌', Array ['sign','purple','square','zodiac','astrology','leo','leo']),
        ('libra', '♎', Array ['sign','purple','square','zodiac','astrology','libra','libra']),
        ('sagittarius', '♐', Array ['sign','zodiac','purple','square','astrology','sagittarius','sagittarius']),
        ('six_pointed_star', '🔯', Array ['six','pointed','purple','square','religion','jewish','hexagram','six_pointed_star','dotted_six-pointed_star']),
        ('scorpius', '♏', Array ['scorpius','sign','zodiac','purple','square','astrology','scorpius','scorpio']),
        ('aquarius', '♒', Array ['sign','purple','square','zodiac','astrology','aquarius','aquarius']),
        ('pisces', '♓', Array ['purple','square','sign','zodiac','astrology','pisces','pisces']),
        ('ophiuchus', '⛎', Array ['sign','purple','square','constellation','astrology','ophiuchus','ophiuchus']),
        ('repeat', '🔁', Array ['loop','record','repeat','repeat_button']),
        ('repeat_one', '🔂', Array ['one','blue','square','loop','repeat_one','repeat_single_button']),
        ('orthodox_cross', '☦️', Array ['suppedaneum','religion','orthodox_cross','orthodox_cross']),
        ('fast_forward', '⏩', Array ['fast','forward','blue','square','play','speed','continue','fast_forward','fast-forward_button']),
        ('black_right_pointing_double_triangle_with_vertical_bar', '⏭️', Array ['black','right','pointing','double','triangle','with','vertical','bar','forward','blue','square','black_right_pointing_double_triangle_with_vertical_bar','next_track_button']),
        ('black_right_pointing_triangle_with_double_vertical_bar', '⏯️', Array ['black','right','pointing','triangle','with','double','vertical','bar','blue','square','black_right_pointing_triangle_with_double_vertical_bar','play_or_pause_button']),
        ('twisted_rightwards_arrows', '🔀', Array ['twisted','rightwards','arrows','blue','square','music','random','twisted_rightwards_arrows','shuffle_tracks_button']),
        ('rewind', '⏪', Array ['rewind','play','blue','square','rewind','fast_reverse_button']),
        ('arrow_forward', '▶️', Array ['arrow','forward','blue','square','right','direction','arrow_forward','play_button']),
        ('arrow_double_up', '⏫', Array ['arrow','double','blue','square','direction','top','arrow_double_up','fast_up_button']),
        ('capricorn', '♑', Array ['sign','zodiac','purple','square','astrology','capricorn','capricorn']),
        ('black_left_pointing_double_triangle_with_vertical_bar', '⏮️', Array ['black','left','pointing','double','triangle','with','vertical','bar','backward','black_left_pointing_double_triangle_with_vertical_bar','last_track_button']),
        ('arrow_down_small', '🔽', Array ['arrow','down','small','blue','square','direction','bottom','arrow_down_small','downwards_button']),
        ('arrow_up_small', '🔼', Array ['arrow','up','small','blue','square','triangle','direction','point','forward','top','arrow_up_small','upwards_button']),
        ('black_square_for_stop', '⏹️', Array ['black','square','for','blue','black_square_for_stop','stop_button']),
        ('arrow_backward', '◀️', Array ['arrow','backward','blue','square','left','direction','arrow_backward','reverse_button']),
        ('black_circle_for_record', '⏺️', Array ['black','circle','for','blue','square','black_circle_for_record','record_button']),
        ('eject', '⏏️', Array ['blue','square','eject','eject_button']),
        ('cinema', '🎦', Array ['blue','square','record','film','movie','curtain','stage','theater','cinema','cinema']),
        ('high_brightness', '🔆', Array ['high','brightness','sun','light','high_brightness','bright_button']),
        ('signal_strength', '📶', Array ['signal','strength','blue','square','reception','phone','internet','connection','wifi','bluetooth','signal_strength','antenna_bars']),
        ('vibration_mode', '📳', Array ['orange','square','phone','vibration_mode','vibration_mode']),
        ('low_brightness', '🔅', Array ['low','brightness','sun','afternoon','warm','summer','low_brightness','dim_button']),
        ('arrow_double_down', '⏬', Array ['arrow','double','blue','square','direction','bottom','arrow_double_down','fast_down_button']),
        ('male_sign', '♂️', Array ['man','boy','men','male_sign','male_sign']),
        ('female_sign', '♀️', Array ['woman','women','lady','girl','female_sign','female_sign']),
        ('heavy_plus_sign', '➕', Array ['heavy','sign','math','calculation','addition','more','increase','heavy_plus_sign','plus']),
        ('heavy_multiplication_x', '✖️', Array ['heavy','multiplication','x','sign','math','calculation','heavy_multiplication_x','multiply']),
        ('mobile_phone_off', '📴', Array ['mute','orange','square','silence','quiet','mobile_phone_off','mobile_phone_off']),
        ('heavy_division_sign', '➗', Array ['heavy','division','sign','math','calculation','heavy_division_sign','divide']),
        ('transgender_symbol', '⚧️', Array ['lgbtq','transgender_symbol','transgender_symbol']),
        ('infinity', '♾️', Array ['forever','infinity','infinity']),
        ('double_vertical_bar', '⏸️', Array ['double','vertical','bar','blue','square','double_vertical_bar','pause_button']),
        ('heavy_equals_sign', '🟰', Array ['math','heavy_equals_sign','heavy_equals_sign']),
        ('bangbang', '‼️', Array ['bangbang','surprise','bangbang','double_exclamation_mark']),
        ('question', '❓', Array ['doubt','confused','question','red_question_mark']),
        ('grey_question', '❔', Array ['grey','doubts','gray','huh','confused','grey_question','white_question_mark']),
        ('heavy_minus_sign', '➖', Array ['heavy','sign','math','calculation','subtract','less','heavy_minus_sign','minus']),
        ('wavy_dash', '〰️', Array ['draw','line','moustache','mustache','squiggle','scribble','wavy_dash','wavy_dash']),
        ('interrobang', '⁉️', Array ['interrobang','wat','punctuation','surprise','interrobang','exclamation_question_mark']),
        ('heavy_dollar_sign', '💲', Array ['money','sales','payment','currency','buck','heavy_dollar_sign','heavy_dollar_sign']),
        ('exclamation', '❗', Array ['heavy','danger','surprise','punctuation','wow','warning','exclamation','red_exclamation_mark']),
        ('fleur_de_lis', '⚜️', Array ['fleur','de','lis','decorative','scout','fleur_de_lis','fleur-de-lis']),
        ('medical_symbol', '⚕️', Array ['staff','of','aesculapius','health','hospital','medical_symbol','medical_symbol']),
        ('recycle', '♻️', Array ['recycle','arrow','environment','garbage','trash','recycle','recycling_symbol']),
        ('name_badge', '📛', Array ['fire','forbid','name_badge','name_badge']),
        ('currency_exchange', '💱', Array ['money','sales','dollar','travel','currency_exchange','currency_exchange']),
        ('yin_yang', '☯️', Array ['balance','yin_yang','yin_yang']),
        ('beginner', '🔰', Array ['badge','shield','beginner','japanese_symbol_for_beginner']),
        ('ballot_box_with_check', '☑️', Array ['ballot','ok','agree','confirm','black','square','vote','election','yes','tick','ballot_box_with_check','check_box_with_check']),
        ('heavy_check_mark', '✔️', Array ['heavy','ok','nike','answer','yes','tick','heavy_check_mark','check_mark']),
        ('white_check_mark', '✅', Array ['white','green','square','ok','agree','vote','election','answer','tick','white_check_mark','check_mark_button']),
        ('curly_loop', '➰', Array ['scribble','draw','shape','squiggle','curly_loop','curly_loop']),
        ('loop', '➿', Array ['tape','cassette','loop','double_curly_loop']),
        ('part_alternation_mark', '〽️', Array ['graph','presentation','stats','business','economics','bad','part_alternation_mark','part_alternation_mark']),
        ('x', '❌', Array ['x','no','delete','remove','cancel','red','x','cross_mark']),
        ('eight_spoked_asterisk', '✳️', Array ['star','sparkle','green','square','eight_spoked_asterisk','eight_spoked_asterisk']),
        ('eight_pointed_black_star', '✴️', Array ['eight','pointed','black','orange','square','shape','polygon','eight_pointed_black_star','eight-pointed_star']),
        ('copyright', '©️', Array ['ip','license','circle','law','legal','copyright','copyright']),
        ('registered', '®️', Array ['alphabet','circle','registered','registered']),
        ('o', '⭕', Array ['o','round','o','hollow_red_circle']),
        ('tm', '™️', Array ['tm','trademark','brand','law','legal','tm','trade_mark']),
        ('sparkle', '❇️', Array ['stars','green','square','awesome','good','fireworks','sparkle','sparkle']),
        ('hash', '#️⃣', Array ['keycap','','symbol','blue','square','twitter','hash','hash_key']),
        ('zero', '0️⃣', Array ['zero','numbers','blue','square','null','zero','keycap_0']),
        ('two', '2️⃣', Array ['two','numbers','prime','blue','square','two','keycap_2']),
        ('keycap_star', '*️⃣', Array ['keycap','star','','keycap_star','keycap:_*']),
        ('one', '1️⃣', Array ['one','blue','square','numbers','one','keycap_1']),
        ('three', '3️⃣', Array ['three','numbers','prime','blue','square','three','keycap_3']),
        ('four', '4️⃣', Array ['four','numbers','blue','square','four','keycap_4']),
        ('trident', '🔱', Array ['weapon','spear','trident','trident_emblem']),
        ('negative_squared_cross_mark', '❎', Array ['negative','squared','x','green','square','no','deny','negative_squared_cross_mark','cross_mark_button']),
        ('eight', '8️⃣', Array ['eight','blue','square','numbers','eight','keycap_8']),
        ('six', '6️⃣', Array ['six','numbers','blue','square','six','keycap_6']),
        ('keycap_ten', '🔟', Array ['ten','numbers','blue','square','keycap_ten','keycap_10']),
        ('seven', '7️⃣', Array ['seven','numbers','blue','square','prime','seven','keycap_7']),
        ('symbols', '🔣', Array ['blue','square','music','note','ampersand','percent','glyphs','characters','symbols','input_symbols']),
        ('abcd', '🔡', Array ['abcd','blue','square','alphabet','abcd','input_latin_lowercase']),
        ('abc', '🔤', Array ['abc','blue','square','alphabet','abc','input_latin_letters']),
        ('a', '🅰️', Array ['red','square','alphabet','letter','a','a_button_(blood_type)']),
        ('nine', '9️⃣', Array ['nine','blue','square','numbers','nine','keycap_9']),
        ('ab', '🆎', Array ['button','red','square','alphabet','ab','negative_squared_ab']),
        ('cl', '🆑', Array ['alphabet','words','red','square','cl','cl_button']),
        ('free', '🆓', Array ['blue','square','words','free','free_button']),
        ('b', '🅱️', Array ['red','square','alphabet','letter','b','b_button_(blood_type)']),
        ('cool', '🆒', Array ['words','blue','square','cool','cool_button']),
        ('information_source', 'ℹ️', Array ['source','blue','square','alphabet','letter','information_source','information']),
        ('id', '🆔', Array ['purple','square','words','id','id_button']),
        ('ng', '🆖', Array ['blue','square','words','shape','icon','ng','ng_button']),
        ('new', '🆕', Array ['blue','square','words','start','new','new_button']),
        ('m', 'Ⓜ️', Array ['alphabet','blue','circle','letter','m','circled_m']),
        ('ok', '🆗', Array ['good','agree','yes','blue','square','ok','ok_button']),
        ('o2', '🅾️', Array ['o2','alphabet','red','square','letter','o2','o_button_(blood_type)']),
        ('sos', '🆘', Array ['help','red','square','words','emergency','911','sos','sos_button']),
        ('up', '🆙', Array ['up','blue','square','above','high','up','up!_button']),
        ('koko', '🈁', Array ['japanese','here','button','blue','square','destination','koko','squared_katakana_koko']),
        ('sa', '🈂️', Array ['japanese','service','charge','button','blue','square','sa','squared_katakana_sa']),
        ('parking', '🅿️', Array ['parking','cars','blue','square','alphabet','letter','parking','p_button']),
        ('vs', '🆚', Array ['words','orange','square','vs','vs_button']),
        ('u6708', '🈷️', Array ['u6708','monthly','amount','chinese','month','moon','orange','square','kanji','u6708','japanese_“monthly_amount”_button']),
        ('u6709', '🈶', Array ['u6709','japanese','not','free','of','charge','button','orange','square','chinese','have','kanji','u6709','squared_cjk_unified_ideograph-6709']),
        ('u6307', '🈯', Array ['u6307','reserved','chinese','point','green','square','kanji','u6307','japanese_“reserved”_button']),
        ('ideograph_advantage', '🉐', Array ['ideograph','advantage','bargain','chinese','kanji','obtain','get','circle','ideograph_advantage','japanese_“bargain”_button']),
        ('u7121', '🈚', Array ['u7121','free','charge','nothing','chinese','kanji','orange','square','u7121','japanese_“free_of_charge”_button']),
        ('u5272', '🈹', Array ['u5272','discount','cut','divide','chinese','kanji','pink','square','u5272','japanese_“discount”_button']),
        ('u7533', '🈸', Array ['u7533','application','chinese','kanji','orange','square','u7533','japanese_“application”_button']),
        ('u5408', '🈴', Array ['u5408','passing','grade','chinese','join','kanji','red','square','u5408','japanese_“passing_grade”_button']),
        ('u7981', '🈲', Array ['u7981','prohibited','kanji','chinese','forbidden','limit','restricted','red','square','u7981','japanese_“prohibited”_button']),
        ('accept', '🉑', Array ['japanese','acceptable','button','ok','good','chinese','kanji','agree','yes','orange','circle','accept','circled_ideograph_accept']),
        ('u7a7a', '🈳', Array ['u7a7a','vacancy','kanji','chinese','empty','sky','blue','square','u7a7a','japanese_“vacancy”_button']),
        ('secret', '㊙️', Array ['japanese','button','privacy','chinese','sshh','kanji','red','circle','secret','circled_ideograph_secret']),
        ('u55b6', '🈺', Array ['u55b6','japanese','open','for','business','button','opening','hours','orange','square','u55b6','squared_cjk_unified_ideograph-55b6']),
        ('u6e80', '🈵', Array ['u6e80','no','vacancy','full','chinese','red','square','kanji','u6e80','japanese_“no_vacancy”_button']),
        ('large_yellow_circle', '🟡', Array ['large','round','large_yellow_circle','yellow_circle']),
        ('large_orange_circle', '🟠', Array ['large','round','large_orange_circle','orange_circle']),
        ('congratulations', '㊗️', Array ['congratulations','japanese','button','chinese','kanji','red','circle','congratulations','circled_ideograph_congratulation']),
        ('large_blue_circle', '🔵', Array ['large','shape','icon','button','large_blue_circle','blue_circle']),
        ('capital_abcd', '🔠', Array ['capital','abcd','alphabet','words','blue','square','capital_abcd','input_latin_uppercase']),
        ('red_circle', '🔴', Array ['shape','error','danger','red_circle','red_circle']),
        ('large_brown_circle', '🟤', Array ['large','round','large_brown_circle','brown_circle']),
        ('black_circle', '⚫', Array ['shape','button','round','black_circle','black_circle']),
        ('large_purple_circle', '🟣', Array ['large','round','large_purple_circle','purple_circle']),
        ('grey_exclamation', '❕', Array ['grey','surprise','punctuation','gray','wow','warning','grey_exclamation','white_exclamation_mark']),
        ('large_yellow_square', '🟨', Array ['large','large_yellow_square','yellow_square']),
        ('five', '5️⃣', Array ['five','numbers','blue','square','prime','five','keycap_5']),
        ('white_circle', '⚪', Array ['shape','round','white_circle','white_circle']),
        ('large_green_square', '🟩', Array ['large','large_green_square','green_square']),
        ('large_blue_square', '🟦', Array ['large','large_blue_square','blue_square']),
        ('large_purple_square', '🟪', Array ['large','large_purple_square','purple_square']),
        ('black_large_square', '⬛', Array ['shape','icon','button','black_large_square','black_large_square']),
        ('white_large_square', '⬜', Array ['shape','icon','stone','button','white_large_square','white_large_square']),
        ('large_red_square', '🟥', Array ['large','large_red_square','red_square']),
        ('large_orange_square', '🟧', Array ['large','large_orange_square','orange_square']),
        ('black_medium_square', '◼️', Array ['shape','button','icon','black_medium_square','black_medium_square']),
        ('white_medium_small_square', '◽', Array ['shape','stone','icon','button','white_medium_small_square','white_medium_small_square']),
        ('white_medium_square', '◻️', Array ['shape','stone','icon','white_medium_square','white_medium_square']),
        ('white_small_square', '▫️', Array ['shape','icon','white_small_square','white_small_square']),
        ('black_small_square', '▪️', Array ['shape','icon','black_small_square','black_small_square']),
        ('small_orange_diamond', '🔸', Array ['shape','jewel','gem','small_orange_diamond','small_orange_diamond']),
        ('black_medium_small_square', '◾', Array ['icon','shape','button','black_medium_small_square','black_medium_small_square']),
        ('small_red_triangle_down', '🔻', Array ['small','shape','direction','bottom','small_red_triangle_down','red_triangle_pointed_down']),
        ('small_red_triangle', '🔺', Array ['small','shape','direction','top','small_red_triangle','red_triangle_pointed_up']),
        ('large_orange_diamond', '🔶', Array ['shape','jewel','gem','large_orange_diamond','large_orange_diamond']),
        ('large_blue_diamond', '🔷', Array ['shape','jewel','gem','large_blue_diamond','large_blue_diamond']),
        ('large_brown_square', '🟫', Array ['large','large_brown_square','brown_square']),
        ('diamond_shape_with_a_dot_inside', '💠', Array ['shape','inside','jewel','blue','gem','crystal','fancy','diamond_shape_with_a_dot_inside','diamond_with_a_dot']),
        ('white_square_button', '🔳', Array ['shape','input','white_square_button','white_square_button']),
        ('radio_button', '🔘', Array ['input','old','music','circle','radio_button','radio_button']),
        ('triangular_flag_on_post', '🚩', Array ['on','post','mark','milestone','place','triangular_flag_on_post','triangular_flag']),
        ('crossed_flags', '🎌', Array ['japanese','nation','country','border','crossed_flags','crossed_flags']),
        ('waving_black_flag', '🏴', Array ['waving','pirate','waving_black_flag','black_flag']),
        ('small_blue_diamond', '🔹', Array ['shape','jewel','gem','small_blue_diamond','small_blue_diamond']),
        ('waving_white_flag', '🏳️', Array ['waving','losing','loser','lost','surrender','give','up','fail','waving_white_flag','white_flag']),
        ('rainbow-flag', '🏳️‍🌈', Array ['pride','gay','lgbt','glbt','queer','homosexual','lesbian','bisexual','transgender','rainbow-flag','rainbow_flag']),
        ('black_square_button', '🔲', Array ['shape','input','frame','black_square_button','black_square_button']),
        ('flag-ac', '🇦🇨', Array ['ac','flag-ac','ascension_island_flag']),
        ('flag-ae', '🇦🇪', Array ['ae','nation','country','banner','flag-ae','united_arab_emirates_flag']),
        ('flag-ad', '🇦🇩', Array ['ad','nation','country','banner','flag-ad','andorra_flag']),
        ('flag-af', '🇦🇫', Array ['af','nation','country','banner','flag-af','afghanistan_flag']),
        ('pirate_flag', '🏴‍☠️', Array ['skull','crossbones','banner','pirate_flag','pirate_flag']),
        ('flag-ag', '🇦🇬', Array ['ag','nation','country','banner','flag-ag','antigua_&_barbuda_flag']),
        ('flag-al', '🇦🇱', Array ['al','nation','country','banner','flag-al','albania_flag']),
        ('flag-ao', '🇦🇴', Array ['ao','nation','country','banner','flag-ao','angola_flag']),
        ('flag-aq', '🇦🇶', Array ['aq','nation','country','banner','flag-aq','antarctica_flag']),
        ('transgender_flag', '🏳️‍⚧️', Array ['lgbtq','transgender_flag','transgender_flag']),
        ('flag-as', '🇦🇸', Array ['as','ws','nation','country','banner','flag-as','american_samoa_flag']),
        ('flag-ai', '🇦🇮', Array ['ai','nation','country','banner','flag-ai','anguilla_flag']),
        ('flag-ar', '🇦🇷', Array ['ar','nation','country','banner','flag-ar','argentina_flag']),
        ('flag-at', '🇦🇹', Array ['at','nation','country','banner','flag-at','austria_flag']),
        ('flag-au', '🇦🇺', Array ['au','nation','country','banner','flag-au','australia_flag']),
        ('flag-az', '🇦🇿', Array ['az','nation','country','banner','flag-az','azerbaijan_flag']),
        ('flag-ba', '🇧🇦', Array ['ba','nation','country','banner','flag-ba','bosnia_&_herzegovina_flag']),
        ('flag-ax', '🇦🇽', Array ['ax','aland','Aland','nation','country','banner','flag-ax','åland_islands_flag']),
        ('flag-aw', '🇦🇼', Array ['aw','nation','country','banner','flag-aw','aruba_flag']),
        ('flag-bb', '🇧🇧', Array ['bb','nation','country','banner','flag-bb','barbados_flag']),
        ('flag-bd', '🇧🇩', Array ['bd','nation','country','banner','flag-bd','bangladesh_flag']),
        ('flag-bh', '🇧🇭', Array ['bh','nation','country','banner','flag-bh','bahrain_flag']),
        ('flag-bg', '🇧🇬', Array ['bg','nation','country','banner','flag-bg','bulgaria_flag']),
        ('flag-be', '🇧🇪', Array ['be','nation','country','banner','flag-be','belgium_flag']),
        ('checkered_flag', '🏁', Array ['checkered','contest','finishline','race','gokart','checkered_flag','chequered_flag']),
        ('flag-bj', '🇧🇯', Array ['bj','nation','country','banner','flag-bj','benin_flag']),
        ('flag-bi', '🇧🇮', Array ['bi','nation','country','banner','flag-bi','burundi_flag']),
        ('flag-bo', '🇧🇴', Array ['bo','nation','country','banner','flag-bo','bolivia_flag']),
        ('flag-bn', '🇧🇳', Array ['bn','darussalam','nation','country','banner','flag-bn','brunei_flag']),
        ('flag-bm', '🇧🇲', Array ['bm','nation','country','banner','flag-bm','bermuda_flag']),
        ('flag-bl', '🇧🇱', Array ['bl','st','barthelemy','saint','nation','country','banner','flag-bl','st._barthélemy_flag']),
        ('flag-bq', '🇧🇶', Array ['bq','bonaire','nation','country','banner','flag-bq','caribbean_netherlands_flag']),
        ('flag-br', '🇧🇷', Array ['br','nation','country','banner','flag-br','brazil_flag']),
        ('flag-bw', '🇧🇼', Array ['bw','nation','country','banner','flag-bw','botswana_flag']),
        ('flag-by', '🇧🇾', Array ['by','nation','country','banner','flag-by','belarus_flag']),
        ('large_green_circle', '🟢', Array ['large','round','large_green_circle','green_circle']),
        ('flag-bt', '🇧🇹', Array ['bt','nation','country','banner','flag-bt','bhutan_flag']),
        ('flag-bs', '🇧🇸', Array ['bs','nation','country','banner','flag-bs','bahamas_flag']),
        ('flag-bv', '🇧🇻', Array ['bv','norway','flag-bv','bouvet_island_flag']),
        ('flag-ca', '🇨🇦', Array ['ca','nation','country','banner','flag-ca','canada_flag']),
        ('flag-cf', '🇨🇫', Array ['cf','nation','country','banner','flag-cf','central_african_republic_flag']),
        ('flag-cg', '🇨🇬', Array ['cg','nation','country','banner','flag-cg','congo_-_brazzaville_flag']),
        ('flag-am', '🇦🇲', Array ['am','nation','country','banner','flag-am','armenia_flag']),
        ('flag-cd', '🇨🇩', Array ['cd','democratic','republic','nation','country','banner','flag-cd','congo_-_kinshasa_flag']),
        ('flag-ch', '🇨🇭', Array ['ch','nation','country','banner','flag-ch','switzerland_flag']),
        ('flag-cc', '🇨🇨', Array ['cc','keeling','nation','country','banner','flag-cc','cocos_(keeling)_islands_flag']),
        ('flag-cm', '🇨🇲', Array ['cm','nation','country','banner','flag-cm','cameroon_flag']),
        ('cn', '🇨🇳', Array ['cn','chinese','prc','country','nation','banner','cn','china_flag']),
        ('flag-ck', '🇨🇰', Array ['ck','nation','country','banner','flag-ck','cook_islands_flag']),
        ('flag-cl', '🇨🇱', Array ['cl','nation','country','banner','flag-cl','chile_flag']),
        ('flag-cp', '🇨🇵', Array ['cp','flag-cp','clipperton_island_flag']),
        ('flag-co', '🇨🇴', Array ['co','nation','country','banner','flag-co','colombia_flag']),
        ('flag-cw', '🇨🇼', Array ['cw','curacao','nation','country','banner','flag-cw','curaçao_flag']),
        ('flag-cv', '🇨🇻', Array ['cv','cabo','nation','country','banner','flag-cv','cape_verde_flag']),
        ('flag-ci', '🇨🇮', Array ['ci','cote','d','ivoire','ivory','coast','nation','country','banner','flag-ci','côte_d’ivoire_flag']),
        ('flag-cu', '🇨🇺', Array ['cu','nation','country','banner','flag-cu','cuba_flag']),
        ('flag-cx', '🇨🇽', Array ['cx','nation','country','banner','flag-cx','christmas_island_flag']),
        ('flag-cr', '🇨🇷', Array ['cr','nation','country','banner','flag-cr','costa_rica_flag']),
        ('flag-cz', '🇨🇿', Array ['cz','nation','country','banner','flag-cz','czechia_flag']),
        ('flag-bz', '🇧🇿', Array ['bz','nation','country','banner','flag-bz','belize_flag']),
        ('flag-dj', '🇩🇯', Array ['dj','nation','country','banner','flag-dj','djibouti_flag']),
        ('flag-dm', '🇩🇲', Array ['dm','nation','country','banner','flag-dm','dominica_flag']),
        ('de', '🇩🇪', Array ['de','german','nation','country','banner','de','germany_flag']),
        ('flag-dg', '🇩🇬', Array ['dg','flag-dg','diego_garcia_flag']),
        ('flag-dz', '🇩🇿', Array ['dz','nation','country','banner','flag-dz','algeria_flag']),
        ('flag-do', '🇩🇴', Array ['do','nation','country','banner','flag-do','dominican_republic_flag']),
        ('flag-dk', '🇩🇰', Array ['dk','nation','country','banner','flag-dk','denmark_flag']),
        ('flag-eg', '🇪🇬', Array ['eg','nation','country','banner','flag-eg','egypt_flag']),
        ('flag-ee', '🇪🇪', Array ['ee','nation','country','banner','flag-ee','estonia_flag']),
        ('flag-ec', '🇪🇨', Array ['ec','nation','country','banner','flag-ec','ecuador_flag']),
        ('flag-ea', '🇪🇦', Array ['ea','flag-ea','ceuta_&_melilla_flag']),
        ('flag-eh', '🇪🇭', Array ['eh','nation','country','banner','flag-eh','western_sahara_flag']),
        ('es', '🇪🇸', Array ['es','nation','country','banner','es','spain_flag']),
        ('flag-eu', '🇪🇺', Array ['eu','banner','flag-eu','european_union_flag']),
        ('flag-fi', '🇫🇮', Array ['fi','nation','country','banner','flag-fi','finland_flag']),
        ('flag-et', '🇪🇹', Array ['et','nation','country','banner','flag-et','ethiopia_flag']),
        ('flag-fk', '🇫🇰', Array ['fk','malvinas','nation','country','banner','flag-fk','falkland_islands_flag']),
        ('flag-er', '🇪🇷', Array ['er','nation','country','banner','flag-er','eritrea_flag']),
        ('flag-fj', '🇫🇯', Array ['fj','nation','country','banner','flag-fj','fiji_flag']),
        ('flag-ga', '🇬🇦', Array ['ga','nation','country','banner','flag-ga','gabon_flag']),
        ('flag-fm', '🇫🇲', Array ['fm','federated','states','nation','country','banner','flag-fm','micronesia_flag']),
        ('flag-bf', '🇧🇫', Array ['bf','nation','country','banner','flag-bf','burkina_faso_flag']),
        ('flag-fo', '🇫🇴', Array ['fo','nation','country','banner','flag-fo','faroe_islands_flag']),
        ('flag-ge', '🇬🇪', Array ['ge','nation','country','banner','flag-ge','georgia_flag']),
        ('gb', '🇬🇧', Array ['gb','uk','great','britain','northern','ireland','nation','country','banner','british','UK','english','england','union','jack','gb','united_kingdom_flag']),
        ('flag-gf', '🇬🇫', Array ['gf','nation','country','banner','flag-gf','french_guiana_flag']),
        ('flag-gh', '🇬🇭', Array ['gh','nation','country','banner','flag-gh','ghana_flag']),
        ('flag-gl', '🇬🇱', Array ['gl','nation','country','banner','flag-gl','greenland_flag']),
        ('flag-gm', '🇬🇲', Array ['gm','nation','country','banner','flag-gm','gambia_flag']),
        ('fr', '🇫🇷', Array ['fr','banner','nation','french','country','fr','france_flag']),
        ('flag-gg', '🇬🇬', Array ['gg','nation','country','banner','flag-gg','guernsey_flag']),
        ('flag-gn', '🇬🇳', Array ['gn','nation','country','banner','flag-gn','guinea_flag']),
        ('flag-gi', '🇬🇮', Array ['gi','nation','country','banner','flag-gi','gibraltar_flag']),
        ('flag-gd', '🇬🇩', Array ['gd','nation','country','banner','flag-gd','grenada_flag']),
        ('flag-gt', '🇬🇹', Array ['gt','nation','country','banner','flag-gt','guatemala_flag']),
        ('flag-gu', '🇬🇺', Array ['gu','nation','country','banner','flag-gu','guam_flag']),
        ('flag-gw', '🇬🇼', Array ['gw','guinea','bissau','nation','country','banner','flag-gw','guinea-bissau_flag']),
        ('flag-gy', '🇬🇾', Array ['gy','nation','country','banner','flag-gy','guyana_flag']),
        ('flag-gq', '🇬🇶', Array ['gq','gn','nation','country','banner','flag-gq','equatorial_guinea_flag']),
        ('flag-gs', '🇬🇸', Array ['gs','nation','country','banner','flag-gs','south_georgia_&_south_sandwich_islands_flag']),
        ('flag-hk', '🇭🇰', Array ['hk','nation','country','banner','flag-hk','hong_kong_sar_china_flag']),
        ('flag-hm', '🇭🇲', Array ['hm','flag-hm','heard_&_mcdonald_islands_flag']),
        ('flag-hu', '🇭🇺', Array ['hu','nation','country','banner','flag-hu','hungary_flag']),
        ('flag-ht', '🇭🇹', Array ['ht','nation','country','banner','flag-ht','haiti_flag']),
        ('flag-hr', '🇭🇷', Array ['hr','nation','country','banner','flag-hr','croatia_flag']),
        ('flag-hn', '🇭🇳', Array ['hn','nation','country','banner','flag-hn','honduras_flag']),
        ('flag-gr', '🇬🇷', Array ['gr','nation','country','banner','flag-gr','greece_flag']),
        ('flag-id', '🇮🇩', Array ['id','nation','country','banner','flag-id','indonesia_flag']),
        ('flag-in', '🇮🇳', Array ['in','nation','country','banner','flag-in','india_flag']),
        ('flag-im', '🇮🇲', Array ['im','nation','country','banner','flag-im','isle_of_man_flag']),
        ('flag-ie', '🇮🇪', Array ['ie','nation','country','banner','flag-ie','ireland_flag']),
        ('flag-cy', '🇨🇾', Array ['cy','nation','country','banner','flag-cy','cyprus_flag']),
        ('flag-gp', '🇬🇵', Array ['gp','nation','country','banner','flag-gp','guadeloupe_flag']),
        ('flag-iq', '🇮🇶', Array ['iq','nation','country','banner','flag-iq','iraq_flag']),
        ('flag-il', '🇮🇱', Array ['il','nation','country','banner','flag-il','israel_flag']),
        ('flag-je', '🇯🇪', Array ['je','nation','country','banner','flag-je','jersey_flag']),
        ('flag-jm', '🇯🇲', Array ['jm','nation','country','banner','flag-jm','jamaica_flag']),
        ('flag-jo', '🇯🇴', Array ['jo','nation','country','banner','flag-jo','jordan_flag']),
        ('flag-is', '🇮🇸', Array ['is','nation','country','banner','flag-is','iceland_flag']),
        ('flag-ir', '🇮🇷', Array ['ir','islamic','republic','nation','country','banner','flag-ir','iran_flag']),
        ('jp', '🇯🇵', Array ['jp','japanese','nation','country','banner','ja','jp','japan_flag']),
        ('flag-ke', '🇰🇪', Array ['ke','nation','country','banner','flag-ke','kenya_flag']),
        ('flag-km', '🇰🇲', Array ['km','nation','country','banner','flag-km','comoros_flag']),
        ('it', '🇮🇹', Array ['it','nation','country','banner','it','italy_flag']),
        ('flag-kh', '🇰🇭', Array ['kh','nation','country','banner','flag-kh','cambodia_flag']),
        ('flag-kn', '🇰🇳', Array ['kn','st','saint','nation','country','banner','flag-kn','st._kitts_&_nevis_flag']),
        ('flag-ki', '🇰🇮', Array ['ki','nation','country','banner','flag-ki','kiribati_flag']),
        ('flag-kg', '🇰🇬', Array ['kg','nation','country','banner','flag-kg','kyrgyzstan_flag']),
        ('flag-io', '🇮🇴', Array ['io','nation','country','banner','flag-io','british_indian_ocean_territory_flag']),
        ('flag-kw', '🇰🇼', Array ['kw','nation','country','banner','flag-kw','kuwait_flag']),
        ('kr', '🇰🇷', Array ['kr','nation','country','banner','kr','south_korea_flag']),
        ('flag-kz', '🇰🇿', Array ['kz','nation','country','banner','flag-kz','kazakhstan_flag']),
        ('flag-la', '🇱🇦', Array ['la','lao','democratic','republic','nation','country','banner','flag-la','laos_flag']),
        ('flag-lb', '🇱🇧', Array ['lb','nation','country','banner','flag-lb','lebanon_flag']),
        ('flag-lr', '🇱🇷', Array ['lr','nation','country','banner','flag-lr','liberia_flag']),
        ('flag-ky', '🇰🇾', Array ['ky','nation','country','banner','flag-ky','cayman_islands_flag']),
        ('flag-lc', '🇱🇨', Array ['lc','st','saint','nation','country','banner','flag-lc','st._lucia_flag']),
        ('flag-li', '🇱🇮', Array ['li','nation','country','banner','flag-li','liechtenstein_flag']),
        ('flag-ls', '🇱🇸', Array ['ls','nation','country','banner','flag-ls','lesotho_flag']),
        ('flag-lt', '🇱🇹', Array ['lt','nation','country','banner','flag-lt','lithuania_flag']),
        ('flag-lu', '🇱🇺', Array ['lu','nation','country','banner','flag-lu','luxembourg_flag']),
        ('flag-lv', '🇱🇻', Array ['lv','nation','country','banner','flag-lv','latvia_flag']),
        ('flag-ly', '🇱🇾', Array ['ly','nation','country','banner','flag-ly','libya_flag']),
        ('flag-ma', '🇲🇦', Array ['ma','nation','country','banner','flag-ma','morocco_flag']),
        ('flag-me', '🇲🇪', Array ['me','nation','country','banner','flag-me','montenegro_flag']),
        ('flag-ic', '🇮🇨', Array ['ic','nation','country','banner','flag-ic','canary_islands_flag']),
        ('flag-mc', '🇲🇨', Array ['mc','nation','country','banner','flag-mc','monaco_flag']),
        ('flag-md', '🇲🇩', Array ['md','republic','nation','country','banner','flag-md','moldova_flag']),
        ('flag-mf', '🇲🇫', Array ['mf','st','flag-mf','st._martin_flag']),
        ('flag-mg', '🇲🇬', Array ['mg','nation','country','banner','flag-mg','madagascar_flag']),
        ('flag-ml', '🇲🇱', Array ['ml','nation','country','banner','flag-ml','mali_flag']),
        ('flag-mk', '🇲🇰', Array ['mk','nation','country','banner','flag-mk','north_macedonia_flag']),
        ('flag-mn', '🇲🇳', Array ['mn','nation','country','banner','flag-mn','mongolia_flag']),
        ('flag-mm', '🇲🇲', Array ['mm','nation','country','banner','flag-mm','myanmar_(burma)_flag']),
        ('flag-mh', '🇲🇭', Array ['mh','nation','country','banner','flag-mh','marshall_islands_flag']),
        ('flag-mr', '🇲🇷', Array ['mr','nation','country','banner','flag-mr','mauritania_flag']),
        ('flag-mq', '🇲🇶', Array ['mq','nation','country','banner','flag-mq','martinique_flag']),
        ('flag-lk', '🇱🇰', Array ['lk','nation','country','banner','flag-lk','sri_lanka_flag']),
        ('flag-ms', '🇲🇸', Array ['ms','nation','country','banner','flag-ms','montserrat_flag']),
        ('flag-mt', '🇲🇹', Array ['mt','nation','country','banner','flag-mt','malta_flag']),
        ('flag-mw', '🇲🇼', Array ['mw','nation','country','banner','flag-mw','malawi_flag']),
        ('flag-mu', '🇲🇺', Array ['mu','nation','country','banner','flag-mu','mauritius_flag']),
        ('flag-my', '🇲🇾', Array ['my','nation','country','banner','flag-my','malaysia_flag']),
        ('flag-mv', '🇲🇻', Array ['mv','nation','country','banner','flag-mv','maldives_flag']),
        ('flag-na', '🇳🇦', Array ['na','nation','country','banner','flag-na','namibia_flag']),
        ('flag-mx', '🇲🇽', Array ['mx','nation','country','banner','flag-mx','mexico_flag']),
        ('flag-mz', '🇲🇿', Array ['mz','nation','country','banner','flag-mz','mozambique_flag']),
        ('flag-nc', '🇳🇨', Array ['nc','nation','country','banner','flag-nc','new_caledonia_flag']),
        ('flag-kp', '🇰🇵', Array ['kp','nation','country','banner','flag-kp','north_korea_flag']),
        ('flag-nl', '🇳🇱', Array ['nl','nation','country','banner','flag-nl','netherlands_flag']),
        ('flag-ng', '🇳🇬', Array ['ng','nation','country','banner','flag-ng','nigeria_flag']),
        ('flag-ne', '🇳🇪', Array ['ne','nation','country','banner','flag-ne','niger_flag']),
        ('flag-no', '🇳🇴', Array ['no','nation','country','banner','flag-no','norway_flag']),
        ('flag-ni', '🇳🇮', Array ['ni','nation','country','banner','flag-ni','nicaragua_flag']),
        ('flag-nu', '🇳🇺', Array ['nu','nation','country','banner','flag-nu','niue_flag']),
        ('flag-mo', '🇲🇴', Array ['mo','nation','country','banner','flag-mo','macao_sar_china_flag']),
        ('flag-np', '🇳🇵', Array ['np','nation','country','banner','flag-np','nepal_flag']),
        ('flag-om', '🇴🇲', Array ['om','symbol','nation','country','banner','flag-om','oman_flag']),
        ('flag-nr', '🇳🇷', Array ['nr','nation','country','banner','flag-nr','nauru_flag']),
        ('flag-pf', '🇵🇫', Array ['pf','nation','country','banner','flag-pf','french_polynesia_flag']),
        ('flag-nz', '🇳🇿', Array ['nz','nation','country','banner','flag-nz','new_zealand_flag']),
        ('flag-pa', '🇵🇦', Array ['pa','nation','country','banner','flag-pa','panama_flag']),
        ('flag-ph', '🇵🇭', Array ['ph','nation','country','banner','flag-ph','philippines_flag']),
        ('flag-pg', '🇵🇬', Array ['pg','nation','country','banner','flag-pg','papua_new_guinea_flag']),
        ('flag-pk', '🇵🇰', Array ['pk','nation','country','banner','flag-pk','pakistan_flag']),
        ('flag-pl', '🇵🇱', Array ['pl','nation','country','banner','flag-pl','poland_flag']),
        ('flag-pm', '🇵🇲', Array ['pm','st','saint','nation','country','banner','flag-pm','st._pierre_&_miquelon_flag']),
        ('flag-pe', '🇵🇪', Array ['pe','nation','country','banner','flag-pe','peru_flag']),
        ('flag-pt', '🇵🇹', Array ['pt','nation','country','banner','flag-pt','portugal_flag']),
        ('flag-pr', '🇵🇷', Array ['pr','nation','country','banner','flag-pr','puerto_rico_flag']),
        ('flag-pn', '🇵🇳', Array ['pn','nation','country','banner','flag-pn','pitcairn_islands_flag']),
        ('flag-pw', '🇵🇼', Array ['pw','nation','country','banner','flag-pw','palau_flag']),
        ('flag-re', '🇷🇪', Array ['re','reunion','nation','country','banner','flag-re','réunion_flag']),
        ('flag-ps', '🇵🇸', Array ['ps','palestine','nation','country','banner','flag-ps','palestinian_territories_flag']),
        ('flag-py', '🇵🇾', Array ['py','nation','country','banner','flag-py','paraguay_flag']),
        ('flag-qa', '🇶🇦', Array ['qa','nation','country','banner','flag-qa','qatar_flag']),
        ('flag-ro', '🇷🇴', Array ['ro','nation','country','banner','flag-ro','romania_flag']),
        ('ru', '🇷🇺', Array ['ru','russian','federation','nation','country','banner','ru','russia_flag']),
        ('flag-rw', '🇷🇼', Array ['rw','nation','country','banner','flag-rw','rwanda_flag']),
        ('flag-sb', '🇸🇧', Array ['sb','nation','country','banner','flag-sb','solomon_islands_flag']),
        ('flag-mp', '🇲🇵', Array ['mp','nation','country','banner','flag-mp','northern_mariana_islands_flag']),
        ('flag-se', '🇸🇪', Array ['se','nation','country','banner','flag-se','sweden_flag']),
        ('flag-sc', '🇸🇨', Array ['sc','nation','country','banner','flag-sc','seychelles_flag']),
        ('flag-sa', '🇸🇦', Array ['sa','nation','country','banner','flag-sa','saudi_arabia_flag']),
        ('flag-sd', '🇸🇩', Array ['sd','nation','country','banner','flag-sd','sudan_flag']),
        ('flag-sg', '🇸🇬', Array ['sg','nation','country','banner','flag-sg','singapore_flag']),
        ('flag-sl', '🇸🇱', Array ['sl','nation','country','banner','flag-sl','sierra_leone_flag']),
        ('flag-rs', '🇷🇸', Array ['rs','nation','country','banner','flag-rs','serbia_flag']),
        ('flag-sj', '🇸🇯', Array ['sj','flag-sj','svalbard_&_jan_mayen_flag']),
        ('flag-sk', '🇸🇰', Array ['sk','nation','country','banner','flag-sk','slovakia_flag']),
        ('flag-sm', '🇸🇲', Array ['sm','nation','country','banner','flag-sm','san_marino_flag']),
        ('flag-si', '🇸🇮', Array ['si','nation','country','banner','flag-si','slovenia_flag']),
        ('flag-ss', '🇸🇸', Array ['ss','sd','nation','country','banner','flag-ss','south_sudan_flag']),
        ('flag-st', '🇸🇹', Array ['st','sao','tome','principe','nation','country','banner','flag-st','são_tomé_&_príncipe_flag']),
        ('flag-sv', '🇸🇻', Array ['sv','nation','country','banner','flag-sv','el_salvador_flag']),
        ('flag-sr', '🇸🇷', Array ['sr','nation','country','banner','flag-sr','suriname_flag']),
        ('flag-sx', '🇸🇽', Array ['sx','dutch','nation','country','banner','flag-sx','sint_maarten_flag']),
        ('flag-so', '🇸🇴', Array ['so','nation','country','banner','flag-so','somalia_flag']),
        ('flag-sn', '🇸🇳', Array ['sn','nation','country','banner','flag-sn','senegal_flag']),
        ('flag-tc', '🇹🇨', Array ['tc','nation','country','banner','flag-tc','turks_&_caicos_islands_flag']),
        ('flag-ta', '🇹🇦', Array ['ta','flag-ta','tristan_da_cunha_flag']),
        ('flag-sz', '🇸🇿', Array ['sz','nation','country','banner','flag-sz','eswatini_flag']),
        ('flag-tg', '🇹🇬', Array ['tg','nation','country','banner','flag-tg','togo_flag']),
        ('flag-tf', '🇹🇫', Array ['tf','nation','country','banner','flag-tf','french_southern_territories_flag']),
        ('flag-th', '🇹🇭', Array ['th','nation','country','banner','flag-th','thailand_flag']),
        ('flag-tk', '🇹🇰', Array ['tk','nation','country','banner','flag-tk','tokelau_flag']),
        ('flag-nf', '🇳🇫', Array ['nf','nation','country','banner','flag-nf','norfolk_island_flag']),
        ('flag-tl', '🇹🇱', Array ['tl','timor','leste','nation','country','banner','flag-tl','timor-leste_flag']),
        ('flag-tj', '🇹🇯', Array ['tj','nation','country','banner','flag-tj','tajikistan_flag']),
        ('flag-to', '🇹🇴', Array ['to','nation','country','banner','flag-to','tonga_flag']),
        ('flag-tt', '🇹🇹', Array ['tt','nation','country','banner','flag-tt','trinidad_&_tobago_flag']),
        ('flag-tm', '🇹🇲', Array ['tm','nation','country','banner','flag-tm','turkmenistan_flag']),
        ('flag-tv', '🇹🇻', Array ['tv','nation','country','banner','flag-tv','tuvalu_flag']),
        ('flag-sh', '🇸🇭', Array ['sh','st','saint','ascension','tristan','cunha','nation','country','banner','flag-sh','st._helena_flag']),
        ('flag-td', '🇹🇩', Array ['td','nation','country','banner','flag-td','chad_flag']),
        ('flag-tz', '🇹🇿', Array ['tz','united','republic','nation','country','banner','flag-tz','tanzania_flag']),
        ('flag-ua', '🇺🇦', Array ['ua','nation','country','banner','flag-ua','ukraine_flag']),
        ('flag-um', '🇺🇲', Array ['um','u','s','flag-um','u.s._outlying_islands_flag']),
        ('flag-tn', '🇹🇳', Array ['tn','nation','country','banner','flag-tn','tunisia_flag']),
        ('flag-tr', '🇹🇷', Array ['tr','nation','country','banner','flag-tr','turkey_flag']),
        ('flag-tw', '🇹🇼', Array ['tw','nation','country','banner','flag-tw','taiwan_flag']),
        ('flag-sy', '🇸🇾', Array ['sy','syrian','arab','republic','nation','country','banner','flag-sy','syria_flag']),
        ('flag-uz', '🇺🇿', Array ['uz','nation','country','banner','flag-uz','uzbekistan_flag']),
        ('flag-un', '🇺🇳', Array ['un','banner','flag-un','united_nations_flag']),
        ('flag-uy', '🇺🇾', Array ['uy','nation','country','banner','flag-uy','uruguay_flag']),
        ('flag-ve', '🇻🇪', Array ['ve','bolivarian','republic','nation','country','banner','flag-ve','venezuela_flag']),
        ('flag-ug', '🇺🇬', Array ['ug','nation','country','banner','flag-ug','uganda_flag']),
        ('flag-va', '🇻🇦', Array ['va','nation','country','banner','flag-va','vatican_city_flag']),
        ('flag-vn', '🇻🇳', Array ['vn','viet','nam','nation','country','banner','flag-vn','vietnam_flag']),
        ('flag-vi', '🇻🇮', Array ['vi','u','s','us','nation','country','banner','flag-vi','u.s._virgin_islands_flag']),
        ('flag-wf', '🇼🇫', Array ['wf','nation','country','banner','flag-wf','wallis_&_futuna_flag']),
        ('us', '🇺🇸', Array ['us','america','nation','country','banner','us','united_states_flag']),
        ('flag-vc', '🇻🇨', Array ['vc','st','saint','nation','country','banner','flag-vc','st._vincent_&_grenadines_flag']),
        ('flag-xk', '🇽🇰', Array ['xk','nation','country','banner','flag-xk','kosovo_flag']),
        ('flag-ye', '🇾🇪', Array ['ye','nation','country','banner','flag-ye','yemen_flag']),
        ('flag-zw', '🇿🇼', Array ['zw','nation','country','banner','flag-zw','zimbabwe_flag']),
        ('flag-zm', '🇿🇲', Array ['zm','nation','country','banner','flag-zm','zambia_flag']),
        ('flag-za', '🇿🇦', Array ['za','nation','country','banner','flag-za','south_africa_flag']),
        ('flag-ws', '🇼🇸', Array ['ws','nation','country','banner','flag-ws','samoa_flag']),
        ('flag-wales', '🏴󠁧󠁢󠁷󠁬󠁳󠁿', Array ['welsh','flag-wales','wales_flag']),
        ('flag-scotland', '🏴󠁧󠁢󠁳󠁣󠁴󠁿', Array ['scottish','flag-scotland','scotland_flag']),
        ('flag-england', '🏴󠁧󠁢󠁥󠁮󠁧󠁿', Array ['english','flag-england','england_flag']),
        ('flag-vg', '🇻🇬', Array ['vg','bvi','nation','country','banner','flag-vg','british_virgin_islands_flag']),
        ('flag-yt', '🇾🇹', Array ['yt','nation','country','banner','flag-yt','mayotte_flag']),
        ('flag-vu', '🇻🇺', Array ['vu','nation','country','banner','flag-vu','vanuatu_flag']); 
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      -- Delete all existing emojis (this is the first seed for emojis)
      TRUNCATE TABLE "emojis" CASCADE;
    `);
  }
}

export const seed = new DefaultEmojisSeed();
