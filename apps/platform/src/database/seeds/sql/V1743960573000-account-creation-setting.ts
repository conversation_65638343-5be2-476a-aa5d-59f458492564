import { QueryRunner } from "typeorm";
import { Seed } from "../manager";

class AccountCreationSettingSeed implements Seed {
  public id = "*************";
  public name = "AccountCreationSettingSeed";
  public timestamp = *************;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      -- Insert the settings schema
      INSERT INTO "platform_settings_schema" ("setting_key", "setting_type", "default_value", "description")
      VALUES
        ('account_creation_while_contact_creation', 'boolean', 'true', 'Weather the an account should be created while creating a contact');
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      -- Delete the settings schema
      DELETE FROM "platform_settings_schema" WHERE "setting_key" = 'account_creation_while_contact_creation';
    `);
  }
}

export const seed = new AccountCreationSettingSeed();
