import { QueryRunner } from "typeorm";
import { Seed } from "../manager";

class ThenaRestrictedFieldHintAndPlaceholderTextUpdateSeed implements Seed {
  public id = "1748400000000";
  public name = "ThenaRestrictedFieldHintAndPlaceholderTextUpdateSeed";
  public timestamp = 1748400000000;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      UPDATE thena_restricted_field SET
        placeholder_text = 'Select status',
        hint_text = 'Current status of the ticket (Open, In Progress, On Hold, Closed)'
      WHERE name = 'Status';

      UPDATE thena_restricted_field SET
        placeholder_text = 'Select assignee',
        hint_text = 'Choose the team member responsible for resolving this ticket'
      WHERE name = 'Assignee';

      UPDATE thena_restricted_field SET
        placeholder_text = '<EMAIL>',
        hint_text = 'Email address of the person requesting support (required)'
      WHERE name = 'Requester';

      UPDATE thena_restricted_field SET
        placeholder_text = 'Enter a clear, concise title',
        hint_text = 'Summarize the issue or request in one line (required)'
      WHERE name = 'Title';

      UPDATE thena_restricted_field SET
        placeholder_text = 'Provide detailed description of the issue or request',
        hint_text = 'Include steps to reproduce, expected vs actual behavior, and any relevant context'
      WHERE name = 'Description';

      UPDATE thena_restricted_field SET
        placeholder_text = 'Select priority level',
        hint_text = 'Choose priority based on urgency and business impact'
      WHERE name = 'Priority';

      UPDATE thena_restricted_field SET
        placeholder_text = 'Select or enter account name',
        hint_text = 'The customer account or organization this ticket relates to'
      WHERE name = 'Account';

      UPDATE thena_restricted_field SET
        placeholder_text = '<EMAIL>',
        hint_text = 'Email address of the person submitting this ticket'
      WHERE name = 'Submitter';

      UPDATE thena_restricted_field SET
        placeholder_text = 'Select or enter team name',
        hint_text = 'Choose the team responsible for handling this ticket (required)'
      WHERE name = 'Team';

      UPDATE thena_restricted_field SET
        placeholder_text = 'Select ticket type',
        hint_text = 'Choose the category that best describes this ticket'
      WHERE name = 'Type';

      UPDATE thena_restricted_field SET
        placeholder_text = 'Enter ticket number or reference',
        hint_text = 'Provide the original ticket ID or reference number being escalated'
      WHERE name = 'Ticket';

      UPDATE thena_restricted_field SET
        placeholder_text = 'Describe why this ticket needs escalation',
        hint_text = 'Explain the specific reason requiring escalation (e.g., customer urgency, technical complexity, SLA breach)'
      WHERE name = 'Reason for Escalation';

      UPDATE thena_restricted_field SET
        placeholder_text = 'Describe the business impact',
        hint_text = 'Explain how this issue affects business operations, revenue, or customer experience'
      WHERE name = 'Business Impact';
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      UPDATE thena_restricted_field SET
        placeholder_text = null,
        hint_text = null
      WHERE name IN (
        'Status', 'Assignee', 'Requester', 'Title', 'Description',
        'Priority', 'Account', 'Submitter', 'Team', 'Type',
        'Ticket', 'Reason for Escalation', 'Business Impact'
      );
    `);
  }
}

export const seed = new ThenaRestrictedFieldHintAndPlaceholderTextUpdateSeed();
