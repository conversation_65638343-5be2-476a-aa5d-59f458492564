import { QueryRunner } from "typeorm";
import { Seed } from "../manager";

class UserSlackAuthSettingsSeed implements Seed {
  public id = "1741951686097";
  public name = "UserSlackAuthSettingsSeed";
  public timestamp = 1741951686097;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      -- Insert the settings schema
      INSERT INTO "platform_settings_schema" ("setting_key", "setting_type", "default_value", "description")
      VALUES
        ('slack_auth_enabled', 'boolean', 'false', 'Weather the users synced slack messages should use the User auth token or not');
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      -- Delete the settings schema
      DELETE FROM "platform_settings_schema" WHERE "setting_key" = 'slack_auth_enabled';
    `);
  }
}

export const seed = new UserSlackAuthSettingsSeed();
