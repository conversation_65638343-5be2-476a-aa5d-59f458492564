import { QueryRunner } from "typeorm";
import { Seed } from "../manager";

class ThenaRestrictedFieldForEscalatedForms implements Seed {
  public id = "1742893412000";
  public name = "ThenaRestrictedFieldForEscalatedForms";
  public timestamp = 1742893412000;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
     INSERT INTO "public"."thena_restricted_field" (
      "name",
      "field_type",
      "is_active",
      "is_deleted",
      "options",
      "placeholder_text",
      "hint_text",
      "mandatory_on_closure",
      "mandatory_on_creation",
      "visible_to_customer",
      "version",
      "description",
      "default_value",
      "metadata",
      "editable_by_customer",
      "type"
      ) VALUES 
        ('Ticket', 'text', 'true', 'false', 
        null, null, null, 'false', 'true', 'false', null, null, null, null, 'true', 'escalation'),

        ('Reason for Escalation', 'text', 'true', 'false', 
        null, null, null, 'false', 'false', 'false', null, null, null, null, 'false', 'escalation'),

        ('Business Impact', 'text', 'true', 'false', 
        null, null, null, 'false', 'false', 'false', null, null, null, null, 'false', 'escalation');
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DELETE FROM thena_restricted_field WHERE name IN ('Ticket', 'Reason for Escalation', 'Business Impact') AND type = 'escalation';
    `);
  }
}

export const seed = new ThenaRestrictedFieldForEscalatedForms();
