import { QueryRunner } from "typeorm";
import { Seed } from "../manager";

class UpdateThenaRestrictedField implements Seed {
  public id = "1745469858000";
  public name = "UpdateThenaRestrictedField";
  public timestamp = 1745469858000;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      UPDATE "public"."thena_restricted_field"
      SET "name" = 'Requester'
      WHERE "name" = 'Requestor';
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      UPDATE "public"."thena_restricted_field"
      SET "name" = 'Requestor'
      WHERE "name" = 'Requester';
    `);
  }
}

export const seed = new UpdateThenaRestrictedField();
