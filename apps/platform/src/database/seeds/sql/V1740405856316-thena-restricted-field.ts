import { QueryRunner } from "typeorm";
import { Seed } from "../manager";

class ThenaRestrictedFieldSeed implements Seed {
  public id = "1740405856316";
  public name = "ThenaRestrictedFieldSeed";
  public timestamp = 1740405856316;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
     INSERT INTO "public"."thena_restricted_field" (
      "name",
      "field_type",
      "is_active",
      "is_deleted",
      "options",
      "placeholder_text",
      "hint_text",
      "mandatory_on_closure",
      "mandatory_on_creation",
      "visible_to_customer",
      "version",
      "description",
      "default_value",
      "metadata",
      "editable_by_customer"
      ) VALUES 
        ('Status', 'choice', 'true', 'false', 
        '[{"id": "open", "value": "OPEN"}, {"id": "in_progress", "value": "INPROGRESS"}, {"id": "on_hold", "value": "ONHOLD"}, {"id": "closed", "value": "CLOSED"}]', 
        null, null, 'false', 'false', 'false', null, null, null, null, 'false'),

        ('Assignee', 'text', 'true', 'false', 
        null, null, null, 'false', 'false', 'false', null, null, null, null, 'false'),

        ('Requestor', 'specialized', 'true', 'false', 
        null, null, null, 'true', 'true', 'true', null, null, null, null, 'true'),

        ('Title', 'text', 'true', 'false', 
        null, null, null, 'true', 'true', 'true', null, null, null, null, 'true'),

        ('Description', 'text', 'true', 'false', 
        null, null, null, 'false', 'false', 'false', null, null, null, null, 'false'),

        ('Priority', 'choice', 'true', 'false', 
        '[{"id": "high", "value": "High"}, {"id": "medium", "value": "Medium"}, {"id": "low", "value": "Low"}, {"id": "urgent", "value": "Urgent"}]', 
        null, null, 'false', 'false', 'false', null, null, null, null, 'false'),

        ('Account', 'text', 'true', 'false', 
        null, null, null, 'false', 'false', 'false', null, null, null, null, 'false'),

        ('Submitter', 'specialized', 'true', 'false', 
        null, null, null, 'false', 'false', 'false', null, null, null, null, 'false'),

        ('Team', 'text', 'true', 'false', 
        null, null, null, 'true', 'true', 'true', null, null, null, null, 'true'),

        ('Type', 'text', 'true', 'false', 
        null, null, null, 'false', 'false', 'false', null, null, null, null, 'false'); 
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DELETE FROM thena_restricted_field WHERE name IN ('Status', 'Assignee', 'Requestor', 'Title', 'Description', 'Priority', 'Account', 'Submitter', 'Team', 'Type');
    `);
  }
}

export const seed = new ThenaRestrictedFieldSeed();
