import { User, UserStatus, UserType } from "@repo/thena-platform-entities";

export class AuthUserResponseDto {
  id: string;
  email: string;
  name: string;
  userType: UserType;
  status: UserStatus;
  organizationId: string;
  primaryTeamId: string;
  lastLoginAt: Date;
  avatarUrl: string;
  timezone: string;
  isActive: boolean;

  static fromEntity(entity: User): AuthUserResponseDto {
    const dto = new AuthUserResponseDto();

    dto.id = entity.uid;
    dto.email = entity.email;
    dto.name = entity.name;
    dto.userType = entity.userType;
    dto.isActive = entity.isActive;
    dto.status = entity.status;
    dto.lastLoginAt = entity.lastLoginAt;
    dto.avatarUrl = entity.avatarUrl;
    dto.timezone = entity.timezone;

    return dto;
  }
}
