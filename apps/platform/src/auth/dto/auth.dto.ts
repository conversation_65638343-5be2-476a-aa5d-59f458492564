import { UserType } from "@repo/thena-platform-entities";
import { IsEmail, IsEnum, IsOptional, IsString } from "class-validator";

export class SignUpDto {
  @IsString()
  name: string;

  @IsEmail()
  email: string;

  @IsString()
  password: string;

  @IsString()
  organizationId: string;

  @IsEnum(UserType)
  userType: UserType;
}

export class SignInDto {
  @IsEmail()
  email: string;

  @IsOptional()
  @IsString()
  password?: string;
}
