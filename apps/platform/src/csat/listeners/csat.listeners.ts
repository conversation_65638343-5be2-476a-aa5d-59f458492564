import { Inject, Injectable } from "@nestjs/common";
import { OnEvent } from "@nestjs/event-emitter";
import { ILogger } from "@repo/nestjs-commons/logger";
import { TICKET_EVENTS } from "@repo/nestjs-commons/platform-events";

@Injectable()
export class CsatListeners {
  constructor(
    @Inject("CustomLogger")
    private readonly logger: ILogger,
  ) { }

  @OnEvent(TICKET_EVENTS.UPDATED)
  handleTicketClosed(payload: {
    ticketId: string;
    teamId: string;
    orgId: string;
  }): void {
    console.log(
      `Received ticket:deleted event for ticket ${payload.ticketId}`,
    );

    try {
      console.log(
        payload.ticketId,
        payload.teamId,
        payload.orgId,
      );
    } catch (error) {
      this.logger.error(
        `Error processing ticket:deleted event for ticket ${payload.ticketId}: ${error.message}`,
        error.stack,
      );
    }
  }

}
