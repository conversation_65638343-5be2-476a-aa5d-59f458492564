import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Controllers
import { CsatSubmissionController } from './controllers/csat-submission.controller';
import { CsatWebController } from './controllers/csat-web.controller';
import { CsatController } from './controllers/csat.controller';
// Services
import { ScheduleModule } from '@nestjs/schedule';
import { CsatAuditLog, CsatAuditLogRepository, CsatFeedbackConfig, CsatFeedbackConfigRepository, CsatRule, CsatRuleFilter, CsatRuleFilterRepository, CsatRuleRepository, CsatSettings, CsatSettingsRepository, CsatSurvey, CsatSurveyRepository, CsatTriggerConfig, CsatTriggerConfigRepository, EmailConfig, EmailConfigRepository, TransactionService } from '@repo/thena-platform-entities';
import { CommonModule } from '../common/common.module';
import { SharedModule } from '../shared/shared.module';
import { TicketsModule } from '../tickets/tickets.module';
import { AmpEmailService } from './services/amp-email/email-template.service';
import { EncodingDecodingService } from './services/amp-email/encoding-decoding-service';
import { CsatAuditLogService } from './services/csat-audit-logs.service';
import { CsatCronService } from './services/csat-cron/cron.service';
import { CsatFeedbackConfigService } from './services/csat-feedback-config.service';
import { CsatMapperService } from './services/csat-mapper.service';
import { CsatRuleFilterService } from './services/csat-rule-filter.service';
import { CsatRuleService } from './services/csat-rule.service';
import { CsatSettingsService } from './services/csat-settings.service';
import { CsatSurveyService } from './services/csat-survey/csat-survey.service';
import { CsatTriggerConfigService } from './services/csat-trigger-config.service';
@Module({
  imports: [
    SharedModule,
    CommonModule,
    forwardRef(() => TicketsModule),
    TypeOrmModule.forFeature([
      CsatSettings,
      CsatRule,
      CsatRuleFilter,
      CsatTriggerConfig,
      CsatFeedbackConfig,
      CsatAuditLog,
      CsatSurvey,
      // Repositories
      CsatSettingsRepository,
      CsatRuleRepository,
      CsatRuleFilterRepository,
      CsatTriggerConfigRepository,
      CsatFeedbackConfigRepository,
      CsatAuditLogRepository,
      CsatSurveyRepository,
      EmailConfigRepository,
      EmailConfig,
    ]),
    ScheduleModule.forRoot(),
  ],
  controllers: [
    CsatController,
    CsatSubmissionController,
    CsatWebController,
  ],
  providers: [
    // Services
    CsatSettingsService,
    CsatRuleService,
    CsatRuleFilterService,
    CsatTriggerConfigService,
    CsatFeedbackConfigService,
    CsatAuditLogService,
    TransactionService,
    CsatSurveyService,
    CsatMapperService,
    EncodingDecodingService,
    // Repositories
    CsatSettingsRepository,
    CsatRuleRepository,
    CsatRuleFilterRepository,
    CsatTriggerConfigRepository,
    CsatFeedbackConfigRepository,
    CsatAuditLogRepository,
    AmpEmailService,
    CsatCronService,
    CsatSurveyRepository,
    EmailConfigRepository,
  ],
  exports: [
    CsatSettingsService,
    CsatRuleService,
    CsatAuditLogService,
    CsatSurveyService,
  ],
})
export class CsatModule { }
