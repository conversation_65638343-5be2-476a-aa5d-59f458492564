import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { ValidateNested } from 'class-validator';
import { CreateCsatRuleDto } from './create-csat-rule.dto';

export class CsatSettingsWithRuleDto {
  @ApiProperty({
    description: 'Cooldown period in days',
    example: 14,
  })
  cooldownPeriodDays: number;

  @ApiProperty({
    description: 'The initial rule to create with the settings',
    type: CreateCsatRuleDto,
  })
  @ValidateNested()
  @Type(() => CreateCsatRuleDto)
  rule: CreateCsatRuleDto;
}
