import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsInt, IsOptional, Max, Min } from 'class-validator';

export enum TriggerTypeEnum {
  ALWAYS = 'always',
  RANDOM = 'random'
}

export enum TriggerEventEnum {
  TICKET_RESOLVED = 'ticket_resolved',
  TICKET_CLOSED = 'ticket_closed',
  TIME_AFTER_RESPONSE = 'time_after_response'
}

export class TriggerConfigDto {
  @ApiProperty({
    enum: TriggerTypeEnum,
    description: 'Type of trigger: always send or randomly send',
    example: 'always',
  })
  @IsEnum(TriggerTypeEnum)
  triggerType: TriggerTypeEnum;

  @ApiPropertyOptional({
    type: Number,
    description: 'Percentage of tickets to randomly select for CSAT (10% or 20%)',
    enum: [10, 20],
    example: 10,
  })
  @IsOptional()
  @IsInt()
  @Min(10)
  @Max(20)
  randomPercentage?: number;

  @ApiPropertyOptional({
    enum: TriggerEventEnum,
    description: 'Event that triggers the CSAT survey',
    example: 'ticket_resolved',
  })
  @IsOptional()
  @IsEnum(TriggerEventEnum)
  triggerEvent?: TriggerEventEnum;

  @ApiPropertyOptional({
    type: Number,
    description: 'Delay in minutes after the trigger event',
    example: 30,
    minimum: 0,
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  delayMinutes?: number;
}
