import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsBoolean, IsNumber, IsOptional, IsString, ValidateNested } from 'class-validator';
import { FeedbackConfigDto } from './csat-feedback-config.dto';
import { FilterDto } from './csat-rule-filter.dto';
import { TriggerConfigDto } from './csat-trigger-config.dto';

export class CreateCsatRuleDto {
  @ApiProperty({
    type: String,
    description: 'Name of the CSAT rule',
    example: 'High Priority Ticket Survey',
  })
  @IsString()
  name: string;

  @ApiPropertyOptional({
    type: String,
    description: 'Description of the CSAT rule',
    example: 'Sends CSAT surveys to customers with high priority tickets',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    type: Boolean,
    description: 'Whether the rule is active',
    example: true,
  })
  @IsBoolean()
  isActive: boolean;

  @ApiProperty({
    type: FilterDto,
    description: 'Filters where ALL conditions must be met',
  })
  @ValidateNested()
  @Type(() => FilterDto)
  allFilters: FilterDto;

  @ApiProperty({
    type: FilterDto,
    description: 'Filters where ANY condition can be met',
  })
  @ValidateNested()
  @Type(() => FilterDto)
  anyFilters: FilterDto;

  @ApiProperty({
    type: TriggerConfigDto,
    description: 'Configuration for when and how the CSAT survey is triggered',
  })
  @ValidateNested()
  @Type(() => TriggerConfigDto)
  triggerConfig: TriggerConfigDto;

  @ApiProperty({
    type: FeedbackConfigDto,
    description: 'Configuration for the CSAT survey appearance and behavior',
  })
  @ValidateNested()
  @Type(() => FeedbackConfigDto)
  feedbackConfig: FeedbackConfigDto;

  @ApiPropertyOptional({
    type: Number,
    description: 'Priority of the CSAT rule',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  priority?: number;
}
