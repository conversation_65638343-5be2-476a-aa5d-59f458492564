import { ApiProperty } from '@nestjs/swagger';
import { CsatRuleResponseDto } from './csat-rule-response.dto';

export class TeamInfoDto {
  @ApiProperty({
    description: 'The ID of the team',
    example: '12345678910',
    type: 'string',
  })
  id: string;
}

export class OrganizationInfoDto {
  @ApiProperty({
    description: 'The ID of the organization',
    example: '12345678910',
    type: 'string',
  })
  id: string;
}

export class CsatSettingsResponseDto {
  @ApiProperty({
    description: 'The unique identifier for the CSAT settings',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'The team associated with the CSAT settings',
    type: () => TeamInfoDto,
  })
  team: TeamInfoDto;

  @ApiProperty({
    description: 'Cooldown period in days',
    example: 14,
  })
  cooldownPeriodDays: number;

  @ApiProperty({
    description: 'The organization associated with the CSAT settings',
    type: () => OrganizationInfoDto,
  })

  organization: OrganizationInfoDto;

  @ApiProperty({
    description: 'The Email Config Id associated with the CSAT settings',
    type: 'string',
  })
  emailConfigId?: string;

  @ApiProperty({
    description: 'Whether CSAT surveys are enabled for this team',
    example: true,
  })
  isEnabled: boolean;

  @ApiProperty({
    description: 'The rules configured for these CSAT settings',
    type: () => [CsatRuleResponseDto],
  })
  rules: CsatRuleResponseDto[];

  @ApiProperty({
    description: 'When the CSAT settings were created',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: string;

  @ApiProperty({
    description: 'When the CSAT settings were last updated',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: string;
}
