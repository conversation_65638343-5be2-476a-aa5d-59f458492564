import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsOptional, IsString, Matches } from 'class-validator';

export enum FeedbackTypeEnum {
  STAR = 'star',
  THUMBS = 'thumbs'
}

export class FeedbackConfigDto {
  @ApiProperty({
    type: Boolean,
    description: 'Whether this feedback configuration is enabled',
    example: true,
  })
  @IsBoolean()
  enabled: boolean;

  @ApiProperty({
    enum: FeedbackTypeEnum,
    description: 'Type of feedback: star rating or thumbs up/down',
    example: 'star',
  })
  @IsEnum(FeedbackTypeEnum)
  feedbackType: FeedbackTypeEnum;

  @ApiPropertyOptional({
    type: String,
    description: 'Custom title for the CSAT survey',
    example: 'How was your experience?',
  })
  @IsOptional()
  @IsString()
  customTitle?: string;

  @ApiPropertyOptional({
    type: String,
    description: 'Custom message for the CSAT survey',
    example: 'Please rate your satisfaction with our support.',
  })
  @IsOptional()
  @IsString()
  customMessage?: string;

  @ApiPropertyOptional({
    type: String,
    description: 'Custom thank you message displayed after survey completion',
    example: 'Thank you for your feedback!',
  })
  @IsOptional()
  @IsString()
  customThankYouMessage?: string;

  @ApiProperty({
    type: Boolean,
    description: 'Whether to include a comment field in the survey',
    example: true,
  })
  @IsBoolean()
  includeCommentField: boolean;

  @ApiPropertyOptional({
    type: String,
    description: 'Label for the comment field',
    example: 'Additional Comments',
  })
  @IsOptional()
  @IsString()
  commentFieldLabel?: string;

  @ApiPropertyOptional({
    type: String,
    description: 'Placeholder text for the comment field',
    example: 'Please share any additional feedback here...',
  })
  @IsOptional()
  @IsString()
  commentFieldPlaceholder?: string;

  @ApiPropertyOptional({
    type: String,
    description: 'Hex color code for survey branding',
    example: '#3366CC',
  })
  @IsOptional()
  @IsString()
  @Matches(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, {
    message: 'brandingColor must be a valid hex color code',
  })
  brandingColor?: string;
}
