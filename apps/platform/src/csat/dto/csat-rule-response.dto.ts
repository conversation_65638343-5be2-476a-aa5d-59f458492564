import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { FeedbackConfigDto } from './csat-feedback-config.dto';
import { FilterDto } from './csat-rule-filter.dto';
import { TriggerConfigDto } from './csat-trigger-config.dto';

export class CsatRuleResponseDto {
  @ApiProperty({
    type: String,
    description: 'Rule ID',
    example: 'e4d2a5b1-9c3f-4c8d-b1a2-3e4f5a6b7c8d',
  })
  id: string;

  @ApiProperty({
    type: String,
    description: 'Name of the CSAT rule',
    example: 'High Priority Ticket Survey',
  })
  name: string;

  @ApiPropertyOptional({
    type: String,
    description: 'Description of the CSAT rule',
    example: 'Sends CSAT surveys to customers with high priority tickets',
  })
  description?: string;

  @ApiProperty({
    type: Boolean,
    description: 'Whether the rule is active',
    example: true,
  })
  isActive: boolean;

  @ApiProperty({
    type: FilterDto,
    description: 'Filters where ALL conditions must be met',
    example: {
      ticket: {
        standardFields: [
          { field: 'status', operator: '=', value: 'open' },
        ],
        customFields: [
          { field: 'custom_CFLLXQWWY4Z', customFieldId: 'CFLLXQWWY4Z', operator: '=', value: 'Custom value' },
        ],
      },
      account: {
        standardFields: [],
        customFields: [],
      },
      contact: {
        standardFields: [],
        customFields: [],
      },
    },
  })
  allFilters: FilterDto;

  @ApiProperty({
    type: FilterDto,
    description: 'Filters where ANY condition can be met',
    example: {
      ticket: {
        standardFields: [
          { field: 'priority', operator: '=', value: 'high' },
        ],
        customFields: [],
      },
      account: {
        standardFields: [
          { field: 'tier', operator: '=', value: 'premium' },
        ],
        customFields: [],
      },
      contact: {
        standardFields: [],
        customFields: [],
      },
    },
  })
  anyFilters: FilterDto;

  @ApiProperty({
    type: TriggerConfigDto,
    description: 'Configuration for when and how the CSAT survey is triggered',
  })
  triggerConfig: TriggerConfigDto;

  @ApiProperty({
    type: FeedbackConfigDto,
    description: 'Configuration for the CSAT survey appearance and behavior',
  })
  feedbackConfig: FeedbackConfigDto;

  @ApiProperty({
    type: Number,
    description: 'Priority of the rule (higher number means higher priority)',
    example: 10,
  })
  priority: number;

  @ApiProperty({
    type: String,
    description: 'Date when the rule was created',
    example: '2023-08-01T12:00:00Z',
  })
  createdAt: string;

  @ApiProperty({
    type: String,
    description: 'Date when the rule was last updated',
    example: '2023-08-02T14:30:00Z',
  })
  updatedAt: string;
}
