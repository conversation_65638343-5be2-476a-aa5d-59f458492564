import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsEnum, IsOptional, IsString, ValidateNested } from 'class-validator';

export enum CsatFilterOperator {
  EQUALS = "=",
  NOT_EQUALS = "!=",
  GREATER_THAN = ">",
  LESS_THAN = "<",
  GREATER_THAN_EQUAL = ">=",
  LESS_THAN_EQUAL = "<=",
  CONTAINS = "contains",
  NOT_CONTAINS = "not_contains",
  IN = "in",
  NOT_IN = "not_in",
  IS_EMPTY = "is_empty",
  IS_NOT_EMPTY = "is_not_empty"
}

export class StandardFilterDto {
  @ApiPropertyOptional({
    description: 'Field name',
    example: 'status',
  })
  @IsString()
  field: string;

  @ApiPropertyOptional({
    description: 'Operator',
    enum: CsatFilterOperator,
    example: '=',
  })
  @IsEnum(CsatFilterOperator)
  operator: CsatFilterOperator;

  @ApiPropertyOptional({
    description: 'Filter value',
    example: 'open',
  })
  @IsOptional()
  value: string | string[] | boolean | number | Date | null;
}

export class CustomFieldFilterDto {
  @ApiPropertyOptional({
    description: 'Field name',
    example: 'custom_CFLLXQWWY4Z',
  })
  @IsString()
  field: string;

  @ApiPropertyOptional({
    description: 'ID of the custom field',
    example: 'CFLLXQWWY4Z',
  })
  @IsString()
  customFieldId: string;

  @ApiPropertyOptional({
    description: 'Operator',
    enum: CsatFilterOperator,
    example: '=',
  })
  @IsEnum(CsatFilterOperator)
  operator: CsatFilterOperator;

  @ApiPropertyOptional({
    description: 'Filter value',
    example: 'This is a custom field value',
  })
  @IsOptional()
  value: string | string[] | boolean | number | Date | null;

}

export class EntityFilterDto {
  @ApiPropertyOptional({
    type: [StandardFilterDto],
    description: 'Standard field filters',
    example: [{ field: 'status', operator: '=', value: 'open' }],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => StandardFilterDto)
  standardFields: StandardFilterDto[];

  @ApiPropertyOptional({
    type: [CustomFieldFilterDto],
    description: 'Custom field filters',
    example: [{ field: 'custom_CFLLXQWWY4Z', customFieldId: 'CFLLXQWWY4Z', operator: '=', value: 'Value' }],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CustomFieldFilterDto)
  customFields: CustomFieldFilterDto[];
}

export class FilterDto {
  @ApiPropertyOptional({
    type: EntityFilterDto,
    description: 'Ticket filters',
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => EntityFilterDto)
  ticket: EntityFilterDto;

  @ApiPropertyOptional({
    type: EntityFilterDto,
    description: 'Account filters',
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => EntityFilterDto)
  account: EntityFilterDto;

  @ApiPropertyOptional({
    type: EntityFilterDto,
    description: 'Contact filters',
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => EntityFilterDto)
  contact: EntityFilterDto;
}
