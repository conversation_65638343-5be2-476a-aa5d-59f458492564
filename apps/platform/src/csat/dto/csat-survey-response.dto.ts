import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { CsatDeliveryChannel, CsatSurveyStatus } from '@repo/thena-platform-entities';

// Adding basic team and organization info DTOs
class TeamBasicInfoDto {
  @ApiProperty({
    description: 'The ID of the team',
    example: '12345678910',
    type: 'bigint',
  })
  id: string;
}

class OrganizationBasicInfoDto {
  @ApiProperty({
    description: 'The ID of the organization',
    example: '12345678910',
    type: 'bigint',
  })
  id: string;
}

class RuleBasicInfoDto {
  @ApiProperty({
    description: 'The ID of the rule',
    example: 'e4d2a5b1-9c3f-4c8d-b1a2-3e4f5a6b7c8d',
  })
  id: string;

  @ApiProperty({
    description: 'The name of the rule',
    example: 'High Priority Ticket Survey',
  })
  name: string;
}

export class CsatSurveyResponseDto {
  @ApiProperty({
    description: 'The unique identifier for the CSAT survey',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'The team associated with this survey',
    type: TeamBasicInfoDto,
  })
  team: TeamBasicInfoDto;

  @ApiProperty({
    description: 'The organization associated with this survey',
    type: OrganizationBasicInfoDto,
  })
  organization: OrganizationBasicInfoDto;

  @ApiProperty({
    description: 'The rule that triggered this survey',
    type: RuleBasicInfoDto,
  })
  rule: RuleBasicInfoDto;

  @ApiProperty({
    description: 'The ID of the ticket that triggered the survey',
    example: 'TKT12345',
  })
  ticketId: string;

  @ApiPropertyOptional({
    description: 'The ID of the account associated with the ticket',
    example: 'ACC12345',
  })
  accountId?: string;

  @ApiPropertyOptional({
    description: 'The ID of the contact associated with the ticket',
    example: 'CON12345',
  })
  contactId?: string;

  @ApiProperty({
    description: 'The type of feedback requested',
    example: 'star',
  })
  feedbackType: string;

  @ApiProperty({
    description: 'Token used to identify and access the survey',
    example: 'abcdef123456',
  })
  surveyToken: string;

  @ApiProperty({
    description: 'Configuration details for the survey',
    type: Object,
  })
  surveyConfig: {
    includeComment?: boolean;
    titleMessage?: string;
    feedbackMessage?: string;
    thankYouMessage?: string;
    commentFieldLabel?: string;
    commentPlaceholder?: string;
    brandingColor?: string;
  };

  @ApiProperty({
    enum: CsatDeliveryChannel,
    description: 'Channel used to deliver the survey',
    example: 'email',
  })
  deliveryChannel: CsatDeliveryChannel;

  @ApiPropertyOptional({
    description: 'The email of the requestor',
    example: '<EMAIL>',
  })
  requestorEmail?: string;

  @ApiProperty({
    enum: CsatSurveyStatus,
    description: 'Current status of the survey',
    example: 'sent',
  })
  status: CsatSurveyStatus;

  @ApiProperty({
    description: 'Details about the survey delivery',
    type: Object,
  })
  deliveryDetails: {
    recipient?: string;
    messageId?: string;
    errorDetails?: string;
    deliveryAttempts?: number;
  };

  @ApiPropertyOptional({
    description: 'Rating value provided by the respondent',
    example: 5,
  })
  ratingValue?: number;

  @ApiPropertyOptional({
    description: 'Comment text provided by the respondent',
    example: 'Great service, very helpful!',
  })
  commentText?: string;

  @ApiProperty({
    description: 'Additional metadata related to the survey response',
    type: Object,
  })
  responseMetadata: Record<string, any>;

  @ApiProperty({
    description: 'When the survey was created',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: string;

  @ApiPropertyOptional({
    description: 'When the survey was sent',
    example: '2023-01-01T00:10:00.000Z',
  })
  sentAt?: string;

  @ApiPropertyOptional({
    description: 'When the survey was viewed',
    example: '2023-01-01T12:00:00.000Z',
  })
  viewedAt?: string;

  @ApiPropertyOptional({
    description: 'When the survey was completed',
    example: '2023-01-01T12:05:00.000Z',
  })
  completedAt?: string;

  @ApiPropertyOptional({
    description: 'When the survey expires',
    example: '2023-01-08T00:00:00.000Z',
  })
  expiresAt?: string;

  @ApiProperty({
    description: 'When the survey was last updated',
    example: '2023-01-01T12:05:00.000Z',
  })
  updatedAt: string;
}
