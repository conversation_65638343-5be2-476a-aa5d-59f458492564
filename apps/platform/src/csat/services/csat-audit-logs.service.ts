import { Injectable } from '@nestjs/common';
import { CsatAuditAction, CsatAuditLog, CsatAuditLogRepository, TransactionContext } from '@repo/thena-platform-entities';
import { toIdString } from '../utils/helper';

interface LogAuditParams {
  teamId: string | bigint;
  organizationId: string | bigint;
  entityType: string;
  entityId: string;
  action: CsatAuditAction;
  previousState: Record<string, any>;
  newState: Record<string, any>;
  metadata: Record<string, any>;
}

@Injectable()
export class CsatAuditLogService {
  constructor(
    private readonly csatAuditLogRepository: CsatAuditLogRepository,
  ) { }

  /**
   * Logs an audit event
   * @param params - Audit log parameters
   * @param txn - Transaction context
   * @returns Created audit log
   */
  async logAudit(
    params: LogAuditParams,
    txn: TransactionContext
  ): Promise<CsatAuditLog> {
    const auditLog = this.csatAuditLogRepository.create({
      team: { id: toIdString(params.teamId) },
      organization: { id: toIdString(params.organizationId) },
      entityType: params.entityType,
      entityId: params.entityId,
      action: params.action,
      previousState: params.previousState,
      newState: params.newState,
      metadata: params.metadata,
    });

    // Save with transaction
    return await this.csatAuditLogRepository.saveWithTxn(txn, auditLog);
  }

  /**
   * Gets audit logs for a specific entity
   * @param entityType - The type of entity
   * @param entityId - The ID of the entity
   * @returns Array of audit logs
   */
  async getAuditLogsByEntity(entityType: string, entityId: string): Promise<CsatAuditLog[]> {
    return await this.csatAuditLogRepository.findAll({
      where: { entityType, entityId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Gets audit logs for a team
   * @param teamId - The ID of the team
   * @returns Array of audit logs
   */
  async getAuditLogsByTeam(teamId: string | bigint): Promise<CsatAuditLog[]> {
    return await this.csatAuditLogRepository.findAll({
      where: { team: { id: toIdString(teamId) } },
      order: { createdAt: 'DESC' },
    });
  }
}
