import { Injectable } from '@nestjs/common';
import * as crypto from 'crypto';
import { ConfigKeys, ConfigService } from '../../../config/config.service';

@Injectable()
export class EncodingDecodingService {
  private readonly algorithm = 'aes-128-gcm';
  private readonly key: Buffer;

  constructor(private readonly configService: ConfigService) {
    const encryptionKey = this.configService.get(ConfigKeys.ENCRYPTION_KEY);
    this.key = crypto.scryptSync(encryptionKey, 'salt', 16);
  }

  /**
   * Encrypts data using AES-128-GCM for compact output
   * @param data - Data to encrypt (can be any type, will be stringified)
   * @returns Compact encrypted string
   */
  encrypt(data: any): string {
    try {
      const iv = crypto.randomBytes(12);
      const cipher = crypto.createCipheriv(this.algorithm, this.key, iv);

      const stringData = JSON.stringify(data);
      let encrypted = cipher.update(stringData, 'utf8', 'base64url');
      encrypted += cipher.final('base64url');

      const authTag = cipher.getAuthTag();

      // Combine IV, encrypted data and auth tag in URL-safe base64
      return Buffer.concat([iv, Buffer.from(encrypted, 'base64url'), authTag])
        .toString('base64url');
    } catch (error) {
      throw new Error(`Encryption failed: ${error.message}`);
    }
  }

  /**
   * Decrypts AES-128-GCM encrypted data
   * @param encryptedData - Compact encrypted string
   * @returns Decrypted data in original format
   */
  decrypt(encryptedData: string): any {
    try {
      const buffer = Buffer.from(encryptedData, 'base64url');

      const iv = buffer.slice(0, 12);
      const authTag = buffer.slice(-16);
      const encrypted = buffer.slice(12, -16).toString('base64url');

      const decipher = crypto.createDecipheriv(this.algorithm, this.key, iv);
      decipher.setAuthTag(authTag);

      let decrypted = decipher.update(encrypted, 'base64url', 'utf8');
      decrypted += decipher.final('utf8');

      return JSON.parse(decrypted);
    } catch (error) {
      throw new Error(`Decryption failed: ${error.message}`);
    }
  }
}
