import { Injectable } from '@nestjs/common';
import { ConfigKeys, ConfigService } from '../../../config/config.service';
import { CsatRuleResponseDto } from '../../dto/csat-rule-response.dto';
import { EncodingDecodingService } from './encoding-decoding-service';

interface SurveyDetails {
  teamId: bigint | string;
  orgId: bigint | string;
  ticketId: string;
}

@Injectable()
export class AmpEmailService {
  constructor(
    private readonly encodingDecodingService: EncodingDecodingService,
    private readonly configService: ConfigService
  ) { }

  /**
   * Creates a survey email package with all necessary templates and encoded details
   * Ensures we only encode the details once for efficiency
   * 
   * @param config The feedback form configuration
   * @param details The survey details (teamId, orgId, ticketId)
   * @returns Object containing all necessary templates and the encoded details
   */
  createSurveyEmailPackage(config: CsatRuleResponseDto, details: SurveyDetails) {
    // Encode the details once
    const encodedDetails = this.encodingDecodingService.encrypt(details);

    // Generate all templates using the same encoded details
    return {
      encodedDetails,
      ampHtml: this.generateAmpEmailTemplate(config, encodedDetails),
      fallbackHtml: this.generateFallbackHtmlTemplate(config, encodedDetails),
      plainText: this.generatePlainTextVersion(config, encodedDetails),
      hybridHtml: this.generateHybridEmailTemplate(config, encodedDetails),
    };
  }

  /**
   * Generates a hybrid email that combines AMP and standard HTML
   * This approach increases the chances of the email working in different clients
   * 
   * @param config The feedback form configuration
   * @param encodedDetails Pre-encoded survey details
   * @returns HTML string containing the hybrid email template
   */
  generateHybridEmailTemplate(config: CsatRuleResponseDto, encodedDetails: string): string {
    // Get the regular AMP template
    const ampTemplate = this.generateAmpEmailTemplate(config, encodedDetails);

    // Get a fallback HTML template
    const fallbackHtml = this.generateFallbackHtmlTemplate(config, encodedDetails);

    // Create a boundary for multi-part content
    const boundary = `amp-email-boundary-${Date.now().toString(36)}`;

    // Combine them using a multipart approach that some email clients may respect
    return `Content-Type: multipart/alternative; boundary="${boundary}"

--${boundary}
Content-Type: text/html; charset=UTF-8

${fallbackHtml}

--${boundary}
Content-Type: text/x-amp-html; charset=UTF-8

${ampTemplate}

--${boundary}--`;
  }

  /**
   * Generates an AMP email template based on the provided configuration
   * @param config The feedback form configuration
   * @param encodedDetails Pre-encoded survey details
   * @returns HTML string containing the AMP email template
   */
  generateAmpEmailTemplate(config: CsatRuleResponseDto, encodedDetails: string): string {
    // Default values
    const title = config.feedbackConfig.customTitle || 'How was your experience?';
    const message = config.feedbackConfig.customMessage || 'Please let us know how we did with your recent support request.';
    const thankYouMessage = config.feedbackConfig.customThankYouMessage || 'We appreciate your input and will use it to improve our services.';
    const commentLabel = config.feedbackConfig.commentFieldLabel || 'Any additional comments?';
    const commentPlaceholder = config.feedbackConfig.commentFieldPlaceholder || 'Tell us more about your experience...';
    const brandingColor = config.feedbackConfig.brandingColor || '#0284c7';

    // Get API URL from config instead of hardcoding local URL
    const apiUrl = this.configService.get(ConfigKeys.BASE_URL);
    const submitEndpoint = `${apiUrl}/csat/submissions?details=${encodedDetails}`;

    // Rating component - either star or thumbs with email-safe centering
    let ratingComponent = '';

    if (config.feedbackConfig.feedbackType === 'star') {
      // Enhanced star rating component with state binding for progressive star selection
      ratingComponent = `
      <div class="rating-container">
        <!-- Bind state to track selected rating -->
        <amp-state id="ratingState">
          <script type="application/json">
            {
              "selectedRating": 0
            }
          </script>
        </amp-state>
        
        <!-- Star selector with email-safe centering -->
        <amp-selector id="star-selector" class="star-rating" name="rating" 
                      on="select:AMP.setState({
                        ratingState: {
                          selectedRating: event.targetOption
                        }
                      })">
          <div class="star" role="button" tabindex="0" option="1"
               [class]="ratingState.selectedRating >= 1 ? 'star selected' : 'star'">★</div>
          <div class="star" role="button" tabindex="0" option="2"
               [class]="ratingState.selectedRating >= 2 ? 'star selected' : 'star'">★</div>
          <div class="star" role="button" tabindex="0" option="3"
               [class]="ratingState.selectedRating >= 3 ? 'star selected' : 'star'">★</div>
          <div class="star" role="button" tabindex="0" option="4"
               [class]="ratingState.selectedRating >= 4 ? 'star selected' : 'star'">★</div>
          <div class="star" role="button" tabindex="0" option="5"
               [class]="ratingState.selectedRating >= 5 ? 'star selected' : 'star'">★</div>
        </amp-selector>
      </div>
      <input type="hidden" name="ratingType" value="star">
      `;
    } else if (config.feedbackConfig.feedbackType === 'thumbs') {
      ratingComponent = `
      <div class="rating-container">
        <amp-selector id="thumbs-selector" class="thumbs-container" name="rating" on="select:AMP.setState({thumbRating: event.targetOption})">
          <div class="thumb-option">
            <div class="thumb" role="button" tabindex="0" option="up">👍</div>
            <div class="thumb-label">Good</div>
          </div>
          <div class="thumb-option">
            <div class="thumb" role="button" tabindex="0" option="down">👎</div>
            <div class="thumb-label">Needs Improvement</div>
          </div>
        </amp-selector>
      </div>
      <input type="hidden" name="ratingType" value="thumbs">
      `;
    }

    // Comment field component
    let commentComponent = '';
    if (config.feedbackConfig.includeCommentField) {
      commentComponent = `
      <div class="form-group">
        <label for="comment" class="form-label">
          ${commentLabel}
        </label>
        <textarea id="comment" name="comment" class="form-control" placeholder="${commentPlaceholder}"></textarea>
      </div>
      `;
    }

    // Updated custom styles - using .selected class for progressive highlight
    const customColorStyles = `
      .submit-btn {
        background-color: ${brandingColor};
      }
      .star[selected], .star.selected {
        background-color: ${config.feedbackConfig.feedbackType === 'star' ? '#f59e0b' : brandingColor};
      }
      .thumb[selected] {
        background-color: ${brandingColor};
      }
    `;

    // The complete template with email-safe CSS
    return `<!doctype html>
<html ⚡4email data-css-strict>
<head>
  <meta charset="utf-8">
  <script async src="https://cdn.ampproject.org/v0.js"></script>
  <script async custom-element="amp-form" src="https://cdn.ampproject.org/v0/amp-form-0.1.js"></script>
  <script async custom-element="amp-bind" src="https://cdn.ampproject.org/v0/amp-bind-0.1.js"></script>
  <script async custom-template="amp-mustache" src="https://cdn.ampproject.org/v0/amp-mustache-0.2.js"></script>
  <script async custom-element="amp-selector" src="https://cdn.ampproject.org/v0/amp-selector-0.1.js"></script>
  <style amp4email-boilerplate>body{visibility:hidden}</style>
  <style amp-custom>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f9fafb;
      color: #111827;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    .feedback-card {
      background-color: white;
      border-radius: 8px;
      border: 1px solid #e5e7eb;
      padding: 24px;
      margin-bottom: 20px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    }
    .text-center {
      text-align: center;
    }
    .title {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 8px;
      color: #111827;
      text-align: center;
    }
    .message {
      font-size: 14px;
      color: #6b7280;
      margin-bottom: 16px;
      text-align: center;
    }
    /* Email-safe centering for rating container */
    .rating-container {
      text-align: center;
      margin: 24px 0;
    }
    .rating-container-hidden {
      display: none;
    }
    /* Star rating styles with email-safe centering */
    .star-rating {
      display: inline-block;
      text-align: center;
    }
    .star {
      display: inline-block;
      width: 32px;
      height: 32px;
      margin: 0 6px;
      background-color: #d1d5db;
      border-radius: 50%;
      text-align: center;
      line-height: 32px;
      font-size: 18px;
      color: white;
      cursor: pointer;
    }
    .star[selected], .star.selected {
      background-color: #f59e0b;
    }
    /* Thumbs styles with email-safe centering */
    .thumbs-container {
      text-align: center;
    }
    .thumb-option {
      display: inline-block;
      text-align: center;
      margin: 0 20px;
    }
    .thumb {
      width: 32px;
      height: 32px;
      margin: 0 auto;
      cursor: pointer;
      background-color: #d1d5db;
      border-radius: 50%;
      text-align: center;
      line-height: 32px;
      font-size: 16px;
      color: white;
    }
    .thumb-label {
      font-size: 12px;
      margin-top: 4px;
      text-align: center;
    }
    .thumb[selected] {
      background-color: #0284c7;
    }
    /* Form elements */
    .form-group {
      margin-bottom: 16px;
    }
    .form-group-hidden {
      display: none;
    }
    .form-label {
      display: block;
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 8px;
      color: #374151;
    }
    .form-control {
      width: 100%;
      padding: 10px 12px;
      font-size: 14px;
      line-height: 1.5;
      color: #374151;
      background-color: #fff;
      border: 1px solid #d1d5db;
      border-radius: 6px;
      box-sizing: border-box;
    }
    textarea.form-control {
      min-height: 80px;
      resize: vertical;
    }
    .submit-btn {
      display: block;
      width: 100%;
      color: white;
      background-color: #0284c7;
      border: none;
      border-radius: 6px;
      padding: 12px 16px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      text-align: center;
    }
    .submit-btn-disabled {
      opacity: 0.7;
    }
    .error {
      color: #ef4444;
      font-size: 12px;
      margin-top: 4px;
    }
    .thank-you {
      text-align: center;
      padding: 40px 20px;
    }
    .thank-you-title {
      font-size: 20px;
      font-weight: 600;
      margin-bottom: 16px;
    }
    .thank-you-message {
      font-size: 16px;
      color: #4b5563;
    }
    .hidden {
      display: none;
    }
    .visible {
      display: block;
    }
    
    ${customColorStyles}
  </style>
</head>
<body>
  <div class="container">
    <div class="feedback-card">
      <!-- Initial state with the feedback form -->
      <div id="feedback-form-container">
        <div class="text-center">
          <!-- Dynamic title -->
          <h3 class="title">
            ${title}
          </h3>
          
          <p class="message">
            ${message}
          </p>
        </div>
        
        <!-- AMP Form -->
        <form method="post" 
              action-xhr="${submitEndpoint}">
          
          <!-- Dynamic Rating Component (Star or Thumbs) -->
          ${ratingComponent}
          
          <!-- Dynamic Comment Field -->
          ${commentComponent}
          
          <!-- Submit button -->
          <div>
            <button 
              type="submit" 
              class="submit-btn">
              Submit feedback
            </button>
          </div>
          
          <!-- Validation messages will appear here -->
          <div submit-error>
            <template type="amp-mustache">
              <div class="error">
                Failed to submit: {{message}}
              </div>
            </template>
          </div>
          
          <div submit-success>
            <template type="amp-mustache">
              <div class="thank-you">
                <div class="thank-you-title">Thank you for your feedback!</div>
                <p class="thank-you-message">
                  ${thankYouMessage}
                </p>
              </div>
            </template>
          </div>
        </form>
      </div>
    </div>
  </div>
</body>
</html>`;
  }

  /**
   * Generates a fallback HTML version for email clients that don't support AMP
   * This is a non-interactive version with a link to a web form
   * 
   * @param config The feedback form configuration
   * @param encodedDetails Pre-encoded survey details
   * @returns HTML string containing the fallback template
   */
  generateFallbackHtmlTemplate(config: CsatRuleResponseDto, encodedDetails: string): string {
    const title = config.feedbackConfig.customTitle || 'How was your experience?';
    const message = config.feedbackConfig.customMessage || 'Please let us know how we did with your recent support request.';
    const brandingColor = config.feedbackConfig.brandingColor || '#0284c7';
    const webUrl = `${this.configService.get(ConfigKeys.BASE_URL)}/csat-feedback`;

    // Create a simplified rating display based on the feedback type
    let ratingDisplay = '';

    if (config.feedbackConfig.feedbackType === 'star') {
      ratingDisplay = `
      <div style="text-align: center; margin: 24px 0;">
        <div style="display: inline-block; margin: 0 auto;">
          <a href="${webUrl}/${encodedDetails}" style="display: inline-block; width: 32px; height: 32px; line-height: 32px; background-color: #d1d5db; color: white; border-radius: 50%; margin: 0 6px; font-size: 18px; text-align: center; text-decoration: none;">★</a>
          <a href="${webUrl}/${encodedDetails}" style="display: inline-block; width: 32px; height: 32px; line-height: 32px; background-color: #d1d5db; color: white; border-radius: 50%; margin: 0 6px; font-size: 18px; text-align: center; text-decoration: none;">★</a>
          <a href="${webUrl}/${encodedDetails}" style="display: inline-block; width: 32px; height: 32px; line-height: 32px; background-color: #d1d5db; color: white; border-radius: 50%; margin: 0 6px; font-size: 18px; text-align: center; text-decoration: none;">★</a>
          <a href="${webUrl}/${encodedDetails}" style="display: inline-block; width: 32px; height: 32px; line-height: 32px; background-color: #d1d5db; color: white; border-radius: 50%; margin: 0 6px; font-size: 18px; text-align: center; text-decoration: none;">★</a>
          <a href="${webUrl}/${encodedDetails}" style="display: inline-block; width: 32px; height: 32px; line-height: 32px; background-color: #d1d5db; color: white; border-radius: 50%; margin: 0 6px; font-size: 18px; text-align: center; text-decoration: none;">★</a>
        </div>
      </div>
      `;
    } else if (config.feedbackConfig.feedbackType === 'thumbs') {
      ratingDisplay = `
      <div style="text-align: center; margin: 24px 0;">
        <div style="display: inline-block; margin: 0 20px;">
          <a href="${webUrl}/${encodedDetails}" style="display: block; width: 32px; height: 32px; line-height: 32px; background-color: #d1d5db; color: white; border-radius: 50%; margin: 0 auto; font-size: 16px; text-align: center; text-decoration: none;">👍</a>
          <div style="font-size: 12px; margin-top: 4px; text-align: center;">Good</div>
        </div>
        <div style="display: inline-block; margin: 0 20px;">
          <a href="${webUrl}/${encodedDetails}" style="display: block; width: 32px; height: 32px; line-height: 32px; background-color: #d1d5db; color: white; border-radius: 50%; margin: 0 auto; font-size: 16px; text-align: center; text-decoration: none;">👎</a>
          <div style="font-size: 12px; margin-top: 4px; text-align: center;">Needs Improvement</div>
        </div>
      </div>
      `;
    }

    return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>${title}</title>
    </head>
    <body style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; margin: 0; padding: 0; background-color: #f9fafb; color: #111827;">
      <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background-color: white; border-radius: 8px; border: 1px solid #e5e7eb; padding: 24px; margin-bottom: 20px; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);">
          <h3 style="font-size: 18px; font-weight: 600; margin-bottom: 8px; color: #111827; text-align: center;">${title}</h3>
          <p style="font-size: 14px; color: #6b7280; margin-bottom: 16px; text-align: center;">${message}</p>
          
          ${ratingDisplay}
          
          <a href="${webUrl}/${encodedDetails}" style="display: block; width: 200px; margin: 24px auto; padding: 12px 24px; background-color: ${brandingColor}; color: white; text-align: center; text-decoration: none; border-radius: 6px; font-weight: 500;">
            Share Your Feedback
          </a>
          
          <div style="background-color: #f3f4f6; padding: 12px; border-radius: 6px; margin-top: 20px; font-size: 13px; color: #4b5563;">
            <p>This email contains an interactive feedback form that may not be supported by your email client.</p>
            <p>For the best experience, click the button above to share your feedback on our website.</p>
          </div>
        </div>
      </div>
    </body>
    </html>
    `;
  }

  /**
   * Generates a plain text version of the feedback email
   * This is required for email clients that don't support HTML or AMP
   * 
   * @param config The feedback form configuration
   * @param encodedDetails Pre-encoded survey details
   * @returns Plain text string for the email
   */
  generatePlainTextVersion(config: CsatRuleResponseDto, encodedDetails: string): string {
    const title = config.feedbackConfig.customTitle || 'How was your experience?';
    const message = config.feedbackConfig.customMessage || 'Please let us know how we did with your recent support request.';
    const webUrl = this.configService.get(ConfigKeys.BASE_URL);

    return `
${title}

${message}

Please share your feedback by visiting: ${webUrl}/csat-feedback/${encodedDetails}

Note: This email contains an interactive feedback form that requires an email client supporting AMP for Email.
    `;
  }
}
