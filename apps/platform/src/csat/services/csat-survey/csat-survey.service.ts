import { Injectable, Logger } from '@nestjs/common';
import { email } from '@repo/shared-proto';
import { CsatDeliveryStatus, CsatMappingStatus, CsatSurvey, CsatSurveyRepository, Ticket } from '@repo/thena-platform-entities';
import { In } from 'typeorm';
import { EmailGrpcClient } from '../../../common/grpc/email.client.grpc';
import { SharedService } from '../../../shared/shared.service';
import { CsatSettingsService } from '../csat-settings.service';
@Injectable()
export class CsatSurveyService {
  private readonly logger = new Logger(CsatSurveyService.name);
  constructor(
    private readonly csatSurveyRepository: CsatSurveyRepository,
    private readonly csatSettingsService: CsatSettingsService,
    private readonly emailGrpcClient: EmailGrpcClient,
    private readonly sharedService: SharedService
  ) { }

  /**
   * Initializes a CSAT survey for a closed ticket
   * @param ticket The ticket to create the survey for
   * @returns The created CSAT survey
   */
  public async initializeCsatSurvey(ticket: Ticket) {
    try {

      const status = await this.sharedService.findStatusById(ticket.statusId);

      const isClosed = status.name.toLowerCase() === 'closed' || (status.parentStatusId !== null && status.parentStatus.name.toLowerCase() === 'closed');

      if (!isClosed) {
        return;
      }

      let csatSettings;
      try {
        csatSettings = await this.csatSettingsService.getSettingsByTeamId(
          ticket.teamId,
        );
        if (!csatSettings || csatSettings.rules.length === 0) {
          console.warn(`No CSAT settings found for team ${ticket.teamId}`);
          return;
        }
      } catch (error) {
        console.error(
          `Error getting CSAT settings for team ${ticket.teamId}:`,
          error,
        );
        return;
      }

      let existingSurvey;
      try {
        existingSurvey = await this.csatSurveyRepository.findByCondition({
          where: {
            ticketId: ticket.id,
          },
        });

        if (existingSurvey) {
          return;
        }
      } catch (error) {
        console.error(
          `Error checking existing CSAT survey for ticket ${ticket.uid}:`,
          error,
        );
        return;
      }

      const surveyTicketMapping = new CsatSurvey();
      surveyTicketMapping.ticketId = ticket.id;
      surveyTicketMapping.teamId = BigInt(ticket.teamId);
      surveyTicketMapping.organizationId = BigInt(ticket.organizationId);
      surveyTicketMapping.ticketClosedAt = ticket.updatedAt;
      surveyTicketMapping.mappingStatus = CsatMappingStatus.CREATED;
      surveyTicketMapping.ticketMetadata = {};
      surveyTicketMapping.ticketTitle = ticket.title || null;

      const createdSurvey = await this.csatSurveyRepository.save(
        surveyTicketMapping,
      );

      return createdSurvey;
    } catch (error) {
      console.error(
        `Error initializing CSAT survey for ticket ${ticket.uid}: ${error.message}`,
        error?.stack,
      );
    }
  }

  /**
   * Gets all pending CSAT ticket mappings
   * @returns Array of pending CsatTicketMapping entities
   */
  public async getPendingCsatSurveys(): Promise<CsatSurvey[]> {
    try {
      return await this.csatSurveyRepository.findAll({
        where: {
          mappingStatus: In([CsatMappingStatus.CREATED, CsatMappingStatus.FAILED, CsatMappingStatus.QUEUED, CsatMappingStatus.PROCESSING]),
        },
      });
    } catch (error) {
      console.error("Error fetching pending CSAT surveys:", error);
      throw error;
    }
  }

  public async submitSurvey(survey: CsatSurvey) {
    try {
      survey.deliveryStatus = CsatDeliveryStatus.SENT;
      await this.csatSurveyRepository.save(survey);
    } catch (error) {
      console.error("Error submitting CSAT survey:", error);
      throw error;
    }
  }

  public async getSurveyByTicketId(ticketId: string) {
    return await this.csatSurveyRepository.findByCondition({
      where: {
        ticketId: ticketId,
      },
    });
  }

  public async sendAnEmail({
    from,
    to,
    subject,
    htmlBody,
    textBody = "",
    cc = "",
    bcc = "",
  }: {
    from: string;
    to: string;
    subject: string;
    htmlBody: string;
    textBody?: string;
    cc?: string;
    bcc?: string;
  }) {
    try {
      const response = await this.emailGrpcClient.sendEmail({
        from,
        to,
        subject,
        htmlBody,
        textBody: textBody || "",
        cc: cc || "",
        bcc: bcc || "",
      });

      if (response.errorCode !== 0) {
        throw new Error(response.message);
      }

      return response;
    } catch (error) {
      this.logger.error("Error sending email:", error);
      throw error;
    }
  }

  public async getEmailConfigById(
    emailConfig: email.GetEmailConfigByIdRequest,
  ) {
    try {
      return await this.emailGrpcClient.getEmailConfigById(emailConfig);
    } catch (error) {
      console.error("Error getting email config:", error);
      throw error;
    }
  }
}
