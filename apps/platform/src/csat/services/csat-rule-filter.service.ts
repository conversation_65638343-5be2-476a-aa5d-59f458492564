import { Injectable } from '@nestjs/common';
import {
  CsatFilterType,
  CsatRuleFilter,
  CsatRuleFilterRepository,
  TransactionContext,
} from '@repo/thena-platform-entities';
import {
  CsatFilterOperator,
  CustomFieldFilterDto,
  EntityFilterDto,
  FilterDto,
  StandardFilterDto,
} from '../dto/csat-rule-filter.dto';

@Injectable()
export class CsatRuleFilterService {
  constructor(
    private readonly csatRuleFilterRepository: CsatRuleFilterRepository,
  ) { }

  /**
   * Creates rule filters from the provided filter DTOs
   * @param ruleId - Legacy rule ID (for backward compatibility)
   * @param allFiltersDto - Filters that must ALL match (AND logic)
   * @param anyFiltersDto - Filters where ANY can match (OR logic)
   * @param txn - Transaction context
   * @returns Array of created filter entities
   */
  async createFilters(
    ruleId: string,
    allFiltersDto: FilterDto,
    anyFiltersDto: FilterDto,
    txn: TransactionContext
  ): Promise<CsatRuleFilter[]> {
    const filters: CsatRuleFilter[] = [];

    // Process ALL filters (AND logic)
    if (allFiltersDto) {
      // Process ticket filters
      if (allFiltersDto.ticket) {
        const ticketFilters = await this.createEntityTypeFilters(
          ruleId,
          allFiltersDto.ticket,
          CsatFilterType.TICKET,
          true, // isAllFilter
          false, // isAnyFilter
          txn
        );
        filters.push(...ticketFilters);
      }

      // Process account filters
      if (allFiltersDto.account) {
        const accountFilters = await this.createEntityTypeFilters(
          ruleId,
          allFiltersDto.account,
          CsatFilterType.ACCOUNT,
          true, // isAllFilter
          false, // isAnyFilter
          txn
        );
        filters.push(...accountFilters);
      }

      // Process contact filters
      if (allFiltersDto.contact) {
        const contactFilters = await this.createEntityTypeFilters(
          ruleId,
          allFiltersDto.contact,
          CsatFilterType.CONTACT,
          true, // isAllFilter
          false, // isAnyFilter
          txn
        );
        filters.push(...contactFilters);
      }
    }

    // Process ANY filters (OR logic)
    if (anyFiltersDto) {
      // Process ticket filters
      if (anyFiltersDto.ticket) {
        const ticketFilters = await this.createEntityTypeFilters(
          ruleId,
          anyFiltersDto.ticket,
          CsatFilterType.TICKET,
          false, // isAllFilter
          true, // isAnyFilter
          txn
        );
        filters.push(...ticketFilters);
      }

      // Process account filters
      if (anyFiltersDto.account) {
        const accountFilters = await this.createEntityTypeFilters(
          ruleId,
          anyFiltersDto.account,
          CsatFilterType.ACCOUNT,
          false, // isAllFilter
          true, // isAnyFilter
          txn
        );
        filters.push(...accountFilters);
      }

      // Process contact filters
      if (anyFiltersDto.contact) {
        const contactFilters = await this.createEntityTypeFilters(
          ruleId,
          anyFiltersDto.contact,
          CsatFilterType.CONTACT,
          false, // isAllFilter
          true, // isAnyFilter
          txn
        );
        filters.push(...contactFilters);
      }
    }

    return filters;
  }

  /**
   * Creates entity type filters (handling both standard and custom fields)
   * @param ruleId - The ID of the rule
   * @param entityFilter - Entity filter data containing standard and custom fields
   * @param filterType - Type of filter (TICKET, ACCOUNT, CONTACT)
   * @param isAllFilter - Whether this is an ALL filter (AND logic)
   * @param isAnyFilter - Whether this is an ANY filter (OR logic)
   * @param txn - Transaction context
   * @returns Array of created filter entities
   */
  private async createEntityTypeFilters(
    ruleId: string,
    entityFilter: EntityFilterDto,
    filterType: CsatFilterType,
    isAllFilter: boolean,
    isAnyFilter: boolean,
    txn: TransactionContext
  ): Promise<CsatRuleFilter[]> {
    const filters: CsatRuleFilter[] = [];

    // Process standard fields
    if (entityFilter.standardFields?.length) {
      const standardFieldFilters = await this.createStandardFieldFilters(
        ruleId,
        entityFilter.standardFields,
        filterType,
        isAllFilter,
        isAnyFilter,
        txn
      );
      filters.push(...standardFieldFilters);
    }

    // Process custom fields
    if (entityFilter.customFields?.length) {
      const customFieldFilters = await this.createCustomFieldFilters(
        ruleId,
        entityFilter.customFields,
        filterType,
        isAllFilter,
        isAnyFilter,
        txn
      );
      filters.push(...customFieldFilters);
    }

    return filters;
  }

  /**
   * Creates standard field filters
   * @param ruleId - The ID of the rule
   * @param standardFields - Array of standard field filters
   * @param filterType - Type of filter (TICKET, ACCOUNT, CONTACT)
   * @param isAllFilter - Whether this is an ALL filter (AND logic)
   * @param isAnyFilter - Whether this is an ANY filter (OR logic)
   * @param txn - Transaction context
   * @returns Array of created filter entities
   */
  private async createStandardFieldFilters(
    ruleId: string,
    standardFields: StandardFilterDto[],
    filterType: CsatFilterType,
    isAllFilter: boolean,
    isAnyFilter: boolean,
    txn: TransactionContext
  ): Promise<CsatRuleFilter[]> {
    const standardFieldFilters = standardFields.map(filter => {
      const filterEntity = this.csatRuleFilterRepository.create({
        ruleId, // Maintain for backward compatibility
        filterType,
        fieldName: filter.field,
        fieldOperator: filter.operator,
        fieldValue: filter.value,
      });

      // Set the appropriate relationship based on filter type
      if (isAllFilter) {
        filterEntity.allRuleId = ruleId;
      }
      if (isAnyFilter) {
        filterEntity.anyRuleId = ruleId;
      }

      return filterEntity;
    });

    // Save with transaction
    return await this.csatRuleFilterRepository.saveManyWithTxn(txn, standardFieldFilters);
  }

  /**
   * Creates custom field filters
   * @param ruleId - The ID of the rule
   * @param customFields - Array of custom field filters
   * @param filterType - Type of filter (TICKET, ACCOUNT, CONTACT)
   * @param isAllFilter - Whether this is an ALL filter (AND logic)
   * @param isAnyFilter - Whether this is an ANY filter (OR logic)
   * @param txn - Transaction context
   * @returns Array of created filter entities
   */
  private async createCustomFieldFilters(
    ruleId: string,
    customFields: CustomFieldFilterDto[],
    filterType: CsatFilterType,
    isAllFilter: boolean,
    isAnyFilter: boolean,
    txn: TransactionContext
  ): Promise<CsatRuleFilter[]> {
    const customFieldFilters = customFields.map(filter => {
      const filterEntity = this.csatRuleFilterRepository.create({
        ruleId, // Maintain for backward compatibility
        filterType,
        fieldName: filter.field,
        fieldOperator: filter.operator,
        fieldValue: filter.value,
        customFieldUid: filter.customFieldId,
      });

      // Set the appropriate relationship based on filter type
      if (isAllFilter) {
        filterEntity.allRuleId = ruleId;
      }
      if (isAnyFilter) {
        filterEntity.anyRuleId = ruleId;
      }

      return filterEntity;
    });

    // Save with transaction
    return await this.csatRuleFilterRepository.saveManyWithTxn(txn, customFieldFilters);
  }

  /**
   * Updates rule filters
   * @param ruleId - The ID of the rule to update filters for
   * @param allFiltersDto - The new ALL filter configuration
   * @param anyFiltersDto - The new ANY filter configuration
   * @param txn - Transaction context
   * @returns Array of updated filter entities
   */
  async updateFilters(
    ruleId: string,
    allFiltersDto: FilterDto,
    anyFiltersDto: FilterDto,
    txn: TransactionContext
  ): Promise<CsatRuleFilter[]> {
    // Find existing filters (both legacy, all, and any filters)
    const existingFilters = await this.csatRuleFilterRepository.findAll({
      where: [
        { ruleId },
        { allRuleId: ruleId },
        { anyRuleId: ruleId },
      ],
    });

    // Delete existing filters
    await Promise.all(existingFilters.map(filter =>
      this.csatRuleFilterRepository.removeWithTxn(txn, filter)
    ));

    // Create new filters
    return this.createFilters(ruleId, allFiltersDto, anyFiltersDto, txn);
  }

  /**
   * Retrieves all filters for a specific rule
   * @param ruleId - The ID of the rule
   * @returns Array of filter entities
   */
  async getFiltersByRuleId(ruleId: string): Promise<CsatRuleFilter[]> {
    return await this.csatRuleFilterRepository.findAll({
      where: [
        { ruleId },
        { allRuleId: ruleId },
        { anyRuleId: ruleId },
      ],
    });
  }

  /**
   * Gets all ALL filters for a specific rule
   * @param ruleId - The ID of the rule
   * @returns Array of ALL filter entities
   */
  async getAllFiltersByRuleId(ruleId: string): Promise<CsatRuleFilter[]> {
    return await this.csatRuleFilterRepository.findAll({
      where: { allRuleId: ruleId },
    });
  }

  /**
   * Gets all ANY filters for a specific rule
   * @param ruleId - The ID of the rule
   * @returns Array of ANY filter entities
   */
  async getAnyFiltersByRuleId(ruleId: string): Promise<CsatRuleFilter[]> {
    return await this.csatRuleFilterRepository.findAll({
      where: { anyRuleId: ruleId },
    });
  }

  /**
   * Maps database filter entities to DTO format with new structure
   * @param filters - Array of filter entities from database
   * @returns Filter DTO object with entities grouped by type and field type
   */
  mapFiltersToDto(filters: CsatRuleFilter[]): FilterDto {
    // Initialize filter DTO with empty object
    const dto: FilterDto = {
      ticket: { standardFields: [], customFields: [] },
      account: { standardFields: [], customFields: [] },
      contact: { standardFields: [], customFields: [] },
    };

    if (!filters || filters.length === 0) {
      return dto;
    }

    // Group filters by entity type and field type
    filters.forEach(filter => {
      const entityType = filter.filterType.toLowerCase();

      // Skip unsupported entity types
      if (!['ticket', 'account', 'contact'].includes(entityType)) {
        console.warn(`Skipping unsupported filter type: ${entityType}`);
        return;
      }

      // Detect if this is a custom field by checking if customFieldId exists
      const isCustomField = !!filter.customFieldUid;

      if (isCustomField) {
        // This is a custom field
        const customFieldFilter: CustomFieldFilterDto = {
          field: filter.fieldName,
          customFieldId: filter.customFieldUid,
          operator: filter.fieldOperator as CsatFilterOperator,
          value: filter.fieldValue,
        };

        // Add to appropriate array based on entity type
        switch (entityType) {
          case 'ticket':
            dto.ticket.customFields.push(customFieldFilter);
            break;
          case 'account':
            dto.account.customFields.push(customFieldFilter);
            break;
          case 'contact':
            dto.contact.customFields.push(customFieldFilter);
            break;
        }
      } else {
        // This is a standard field
        const standardFieldFilter: StandardFilterDto = {
          field: filter.fieldName,
          operator: filter.fieldOperator as CsatFilterOperator,
          value: filter.fieldValue,
        };

        // Add to appropriate array based on entity type
        switch (entityType) {
          case 'ticket':
            dto.ticket.standardFields.push(standardFieldFilter);
            break;
          case 'account':
            dto.account.standardFields.push(standardFieldFilter);
            break;
          case 'contact':
            dto.contact.standardFields.push(standardFieldFilter);
            break;
        }
      }
    });

    return dto;
  }
}
