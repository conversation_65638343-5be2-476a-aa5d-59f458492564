import { Injectable, NotFoundException } from '@nestjs/common';
import {
  CsatFeedbackConfigRepository,
  CsatRuleFilterRepository,
  CsatRuleRepository,
  CsatTriggerConfigRepository,
  TransactionContext,
} from '@repo/thena-platform-entities';
import { CreateCsatRuleDto } from '../dto/create-csat-rule.dto';
import { CsatRuleResponseDto } from '../dto/csat-rule-response.dto';
import { UpdateCsatRuleDto } from '../dto/update-csat-rule.dto';
import { CsatAuditLogService } from './csat-audit-logs.service';
import { CsatFeedbackConfigService } from './csat-feedback-config.service';
import { CsatRuleFilterService } from './csat-rule-filter.service';
import { CsatTriggerConfigService } from './csat-trigger-config.service';

@Injectable()
export class CsatRuleService {
  constructor(
    private readonly csatRuleRepository: CsatRuleRepository,
    private readonly csatRuleFilterRepository: CsatRuleFilterRepository,
    private readonly csatTriggerConfigRepository: CsatTriggerConfigRepository,
    private readonly csatFeedbackConfigRepository: CsatFeedbackConfigRepository,
    private readonly csatRuleFilterService: CsatRuleFilterService,
    private readonly csatTriggerConfigService: CsatTriggerConfigService,
    private readonly csatFeedbackConfigService: CsatFeedbackConfigService,
    private readonly csatAuditLogService: CsatAuditLogService,
  ) { }

  /**
   * Gets a CSAT rule by ID
   * @param id - The ID of the rule to retrieve
   * @returns Complete rule with all configurations
   */
  async getRule(id: string): Promise<CsatRuleResponseDto> {
    // Get the base rule
    const rule = await this.csatRuleRepository.findOneById(id);

    if (!rule) {
      throw new NotFoundException(`CSAT rule with ID ${id} not found`);
    }

    // Get related configurations
    const allFilters = await this.csatRuleFilterService.getAllFiltersByRuleId(id);
    const anyFilters = await this.csatRuleFilterService.getAnyFiltersByRuleId(id);
    const triggerConfig = await this.csatTriggerConfigService.getTriggerConfigByRuleId(id);
    const feedbackConfig = await this.csatFeedbackConfigService.getFeedbackConfigByRuleId(id);

    if (!triggerConfig || !feedbackConfig) {
      throw new NotFoundException(`Configuration for CSAT rule with ID ${id} is incomplete`);
    }

    // Map to response DTO
    return {
      id: rule.id,
      name: rule.name,
      description: rule.description,
      isActive: rule.isActive,
      allFilters: this.csatRuleFilterService.mapFiltersToDto(allFilters),
      anyFilters: this.csatRuleFilterService.mapFiltersToDto(anyFilters),
      triggerConfig: this.csatTriggerConfigService.mapToDto(triggerConfig),
      feedbackConfig: this.csatFeedbackConfigService.mapToDto(feedbackConfig),
      priority: rule.priority,
      createdAt: rule.createdAt.toISOString(),
      updatedAt: rule.updatedAt.toISOString(),
    };
  }

  /**
   * Creates a CSAT rule with all related configurations
   * @param csatSettingsId - The ID of the CSAT settings
   * @param createDto - The rule creation data
   * @param userId - The ID of the user creating the rule
   * @param txn - Transaction context
   * @returns Created rule with complete configuration
   */
  async createRule(
    csatSettingsId: string,
    createDto: CreateCsatRuleDto,
    userId: string,
    txn: TransactionContext
  ): Promise<CsatRuleResponseDto> {
    try {
      const existingRules = await this.csatRuleRepository.findAll({
        where: { csatSettingsId },
        order: { priority: 'DESC' },
      });

      const priority = existingRules.length === 0 ? 1 : existingRules[0].priority + 1;

      // 1. Create and save the base rule first
      const rule = this.csatRuleRepository.create({
        csatSettingsId,
        name: createDto.name,
        description: createDto.description,
        isActive: createDto.isActive,
        priority,
      });

      // Save rule to get an ID
      const savedRule = await this.csatRuleRepository.saveWithTxn(txn, rule);
      const ruleId = savedRule.id;

      // 2. Create trigger config directly using the service
      const triggerConfig = await this.csatTriggerConfigService.createTriggerConfig(
        ruleId,
        createDto.triggerConfig,
        txn
      );

      // 3. Create feedback config directly using the service
      const feedbackConfig = await this.csatFeedbackConfigService.createFeedbackConfig(
        ruleId,
        createDto.feedbackConfig,
        txn
      );

      // 4. Create filters directly using the service
      await this.csatRuleFilterService.createFilters(
        ruleId,
        createDto.allFilters,
        createDto.anyFilters,
        txn
      );

      // 5. Build the response directly with the data we already have
      return {
        id: ruleId,
        name: savedRule.name,
        description: savedRule.description,
        isActive: savedRule.isActive,
        allFilters: createDto.allFilters,
        anyFilters: createDto.anyFilters,
        triggerConfig: this.csatTriggerConfigService.mapToDto(triggerConfig),
        feedbackConfig: this.csatFeedbackConfigService.mapToDto(feedbackConfig),
        priority: savedRule.priority,
        createdAt: savedRule.createdAt.toISOString(),
        updatedAt: savedRule.updatedAt.toISOString(),
      };
    } catch (error) {
      console.error('Error creating CSAT rule:', error);
      throw error;
    }
  }

  /**
   * Gets all CSAT rules for a specific CSAT settings
   * @param csatSettingsId - The ID of the CSAT settings
   * @returns Array of complete rules with all configurations
   */
  async getRulesByCsatSettingsId(csatSettingsId: string): Promise<CsatRuleResponseDto[]> {
    const rules = await this.csatRuleRepository.findAll({
      where: { csatSettingsId },
      order: { priority: 'DESC' },
    });

    // Get complete data for each rule
    const completeRules = await Promise.all(
      rules.map(async rule => {
        try {
          return await this.getRule(rule.id);
        } catch (error) {
          console.error(`Error getting rule ${rule.id}:`, error);
          return {
            id: rule.id,
            name: rule.name,
            description: rule.description,
            isActive: rule.isActive,
            priority: rule.priority,
            allFilters: null,
            anyFilters: null,
            triggerConfig: null,
            feedbackConfig: null,
            createdAt: rule.createdAt.toISOString(),
            updatedAt: rule.updatedAt.toISOString(),
          };
        }
      })
    );

    return completeRules;
  }

  /**
   * Updates a CSAT rule and its related configurations
   * @param ruleId - The ID of the rule to update
   * @param updateDto - The rule update data
   * @param userId - The ID of the user updating the rule
   * @param txn - Transaction context
   * @returns Updated rule with complete configuration
   */
  async updateRule(
    ruleId: string,
    updateDto: UpdateCsatRuleDto,
    userId: string,
    txn: TransactionContext
  ): Promise<CsatRuleResponseDto> {
    try {
      // 1. Get the existing rule 
      const existingRule = await this.csatRuleRepository.findOneById(ruleId);

      if (!existingRule) {
        throw new NotFoundException(`CSAT rule with ID ${ruleId} not found`);
      }

      // 2. Update the rule base properties
      if (updateDto.name !== undefined) existingRule.name = updateDto.name;
      if (updateDto.description !== undefined) existingRule.description = updateDto.description;
      if (updateDto.isActive !== undefined) existingRule.isActive = updateDto.isActive;

      // Save rule updates first
      const updatedRule = await this.csatRuleRepository.saveWithTxn(txn, existingRule);

      let triggerConfigDto = null;
      let feedbackConfigDto = null;
      let allFiltersDto = null;
      let anyFiltersDto = null;

      // 3. Handle trigger config updates via service
      if (updateDto.triggerConfig) {
        const existingTriggerConfig = await this.csatTriggerConfigService.getTriggerConfigByRuleId(ruleId);

        if (existingTriggerConfig) {
          const updatedTriggerConfig = await this.csatTriggerConfigService.updateTriggerConfig(
            existingTriggerConfig.id,
            updateDto.triggerConfig,
            txn
          );
          triggerConfigDto = this.csatTriggerConfigService.mapToDto(updatedTriggerConfig);
        } else {
          const newTriggerConfig = await this.csatTriggerConfigService.createTriggerConfig(
            ruleId,
            updateDto.triggerConfig,
            txn
          );
          triggerConfigDto = this.csatTriggerConfigService.mapToDto(newTriggerConfig);
        }
      } else {
        // Just fetch existing trigger config
        const existingTriggerConfig = await this.csatTriggerConfigService.getTriggerConfigByRuleId(ruleId);
        if (existingTriggerConfig) {
          triggerConfigDto = this.csatTriggerConfigService.mapToDto(existingTriggerConfig);
        }
      }

      // 4. Handle feedback config updates via service
      if (updateDto.feedbackConfig) {
        const existingFeedbackConfig = await this.csatFeedbackConfigService.getFeedbackConfigByRuleId(ruleId);

        if (existingFeedbackConfig) {
          const updatedFeedbackConfig = await this.csatFeedbackConfigService.updateFeedbackConfig(
            existingFeedbackConfig.id,
            updateDto.feedbackConfig,
            txn
          );
          feedbackConfigDto = this.csatFeedbackConfigService.mapToDto(updatedFeedbackConfig);
        } else {
          const newFeedbackConfig = await this.csatFeedbackConfigService.createFeedbackConfig(
            ruleId,
            updateDto.feedbackConfig,
            txn
          );
          feedbackConfigDto = this.csatFeedbackConfigService.mapToDto(newFeedbackConfig);
        }
      } else {
        // Just fetch existing feedback config
        const existingFeedbackConfig = await this.csatFeedbackConfigService.getFeedbackConfigByRuleId(ruleId);
        if (existingFeedbackConfig) {
          feedbackConfigDto = this.csatFeedbackConfigService.mapToDto(existingFeedbackConfig);
        }
      }

      // 5. Handle filter updates via service
      let updatedFilters = [];

      // Check if we're updating the filters
      if (updateDto.allFilters || updateDto.anyFilters) {
        updatedFilters = await this.csatRuleFilterService.updateFilters(
          ruleId,
          updateDto.allFilters || null,
          updateDto.anyFilters || null,
          txn
        );
      }

      // Get the filters for the response
      if (updatedFilters.length > 0) {
        // If we've just updated the filters, extract them directly
        allFiltersDto = this.csatRuleFilterService.mapFiltersToDto(
          updatedFilters.filter(filter => filter.allRuleId === ruleId)
        );
        anyFiltersDto = this.csatRuleFilterService.mapFiltersToDto(
          updatedFilters.filter(filter => filter.anyRuleId === ruleId)
        );
      } else {
        // Fetch existing filters
        const allFilters = await this.csatRuleFilterService.getAllFiltersByRuleId(ruleId);
        const anyFilters = await this.csatRuleFilterService.getAnyFiltersByRuleId(ruleId);
        allFiltersDto = this.csatRuleFilterService.mapFiltersToDto(allFilters);
        anyFiltersDto = this.csatRuleFilterService.mapFiltersToDto(anyFilters);
      }

      // 6. Build and return response directly from the data we have
      return {
        id: updatedRule.id,
        name: updatedRule.name,
        description: updatedRule.description,
        isActive: updatedRule.isActive,
        allFilters: allFiltersDto,
        anyFilters: anyFiltersDto,
        triggerConfig: triggerConfigDto,
        feedbackConfig: feedbackConfigDto,
        priority: updatedRule.priority,
        createdAt: updatedRule.createdAt.toISOString(),
        updatedAt: updatedRule.updatedAt.toISOString(),
      };
    } catch (error) {
      console.error('Error updating CSAT rule:', error);
      throw error;
    }
  }

  /**
   * Deletes a CSAT rule and all its related configurations
   * @param ruleId - The ID of the rule to delete
   * @param userId - The ID of the user deleting the rule
   * @param txn - Transaction context
   * @returns void
   */
  async deleteRule(
    ruleId: string,
    userId: string,
    txn: TransactionContext
  ): Promise<void> {
    // 1. Get the existing rule
    const existingRule = await this.csatRuleRepository.findOneById(ruleId);

    if (!existingRule) {
      throw new NotFoundException(`CSAT rule with ID ${ruleId} not found`);
    }

    // 2. Remove the rule (cascading delete will handle related entities)
    await this.csatRuleRepository.removeWithTxn(txn, existingRule);
  }

  /**
   * Reorders the priority of CSAT rules for a team
   * @param csatSettingsId - The ID of the CSAT settings
   * @param reorderRules - An array of objects containing ruleId and priority
   * @param txn - Transaction context
   * @returns An array of updated rules with all configurations
   */
  async reorderRules(
    csatSettingsId: string,
    reorderRules: Array<{ ruleId: string; priority: number }>,
    txn: TransactionContext
  ): Promise<CsatRuleResponseDto[]> {
    try {
      // Sort the reorderRules by priority to ensure we're updating in a consistent order
      const sortedReorderRules = [...reorderRules].sort((a, b) => b.priority - a.priority);

      // Get all existing rules
      const existingRules = await this.csatRuleRepository.findAll({
        where: { csatSettingsId },
      });

      // Build a map of rule IDs to entities for quick lookup
      const ruleMap = new Map<string, any>();
      existingRules.forEach(rule => {
        ruleMap.set(rule.id, rule);
      });

      // First pass: Temporarily set all priorities to a large value plus index
      let index = 0;
      for (const rule of existingRules) {
        // Create a temporary priority using array index to ensure uniqueness
        const tempPriority = 10000 + index;
        rule.priority = tempPriority;

        // Save with transaction
        await this.csatRuleRepository.saveWithTxn(txn, rule);
        index++;
      }

      // Second pass: Set the actual target priorities
      for (const ruleUpdate of sortedReorderRules) {
        const rule = ruleMap.get(ruleUpdate.ruleId);
        if (rule) {
          rule.priority = ruleUpdate.priority;

          // Save with transaction
          await this.csatRuleRepository.saveWithTxn(txn, rule);
        }
      }

      // Get the updated rules directly from the repository
      const updatedRules = await this.csatRuleRepository.findAll({
        where: { csatSettingsId },
        order: { priority: 'DESC' },
      });

      // Map to response DTOs
      const responseRules = await Promise.all(
        updatedRules.map(async rule => {
          try {
            // Get both ALL and ANY filters
            const allFilters = await this.csatRuleFilterService.getAllFiltersByRuleId(rule.id);
            const anyFilters = await this.csatRuleFilterService.getAnyFiltersByRuleId(rule.id);
            const triggerConfig = await this.csatTriggerConfigService.getTriggerConfigByRuleId(rule.id);
            const feedbackConfig = await this.csatFeedbackConfigService.getFeedbackConfigByRuleId(rule.id);

            return {
              id: rule.id,
              name: rule.name,
              description: rule.description,
              isActive: rule.isActive,
              allFilters: this.csatRuleFilterService.mapFiltersToDto(allFilters),
              anyFilters: this.csatRuleFilterService.mapFiltersToDto(anyFilters),
              triggerConfig: triggerConfig ? this.csatTriggerConfigService.mapToDto(triggerConfig) : null,
              feedbackConfig: feedbackConfig ? this.csatFeedbackConfigService.mapToDto(feedbackConfig) : null,
              priority: rule.priority,
              createdAt: rule.createdAt.toISOString(),
              updatedAt: rule.updatedAt.toISOString(),
            };
          } catch (error) {
            console.error(`Error getting rule ${rule.id} details:`, error);
            return {
              id: rule.id,
              name: rule.name,
              description: rule.description,
              isActive: rule.isActive,
              priority: rule.priority,
              allFilters: null,
              anyFilters: null,
              triggerConfig: null,
              feedbackConfig: null,
              createdAt: rule.createdAt.toISOString(),
              updatedAt: rule.updatedAt.toISOString(),
            };
          }
        })
      );

      return responseRules;
    } catch (error) {
      console.error('Error reordering CSAT rules:', error);
      throw error;
    }
  }
}
