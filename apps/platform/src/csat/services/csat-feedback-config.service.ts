import { Injectable } from '@nestjs/common';
import { CsatFeedbackConfig, CsatFeedbackConfigRepository, TransactionContext } from '@repo/thena-platform-entities';
import { FeedbackConfigDto } from '../dto/csat-feedback-config.dto';
import { AmpEmailService } from './amp-email/email-template.service';
@Injectable()
export class CsatFeedbackConfigService {
  constructor(
    private readonly csatFeedbackConfigRepository: CsatFeedbackConfigRepository,
    private readonly ampEmailService: AmpEmailService
  ) { }

  /**
   * Creates a feedback configuration
   * @param ruleId - The ID of the rule to create the feedback config for
   * @param configDto - The feedback configuration data
   * @param txn - Transaction context
   * @returns Created feedback config entity
   */
  async createFeedbackConfig(
    ruleId: string,
    configDto: FeedbackConfigDto,
    txn: TransactionContext
  ): Promise<CsatFeedbackConfig> {
    const config = this.csatFeedbackConfigRepository.create({
      ruleId,
      enabled: configDto.enabled,
      feedbackType: configDto.feedbackType,
      brandingColor: configDto.brandingColor,
      includeCommentField: configDto.includeCommentField,
      customTitle: configDto.customTitle,
      customMessage: configDto.customMessage,
      customThankYouMessage: configDto.customThankYouMessage,
      channelConfig: {
        commentFieldLabel: configDto.commentFieldLabel,
        commentFieldPlaceholder: configDto.commentFieldPlaceholder,
      },
    });

    // Save with transaction
    return await this.csatFeedbackConfigRepository.saveWithTxn(txn, config);
  }

  /**
   * Updates a feedback configuration
   * @param configId - The ID of the config to update
   * @param configDto - The new feedback configuration data
   * @param txn - Transaction context
   * @returns Updated feedback config entity
   */
  async updateFeedbackConfig(
    configId: string,
    configDto: FeedbackConfigDto,
    txn: TransactionContext
  ): Promise<CsatFeedbackConfig> {
    const existingConfig = await this.csatFeedbackConfigRepository.findOneById(configId);

    if (!existingConfig) {
      throw new Error(`Feedback config with ID ${configId} not found`);
    }

    // Update fields
    existingConfig.enabled = configDto.enabled;
    existingConfig.feedbackType = configDto.feedbackType;
    existingConfig.brandingColor = configDto.brandingColor;
    existingConfig.includeCommentField = configDto.includeCommentField;
    existingConfig.customTitle = configDto.customTitle;
    existingConfig.customMessage = configDto.customMessage;
    existingConfig.customThankYouMessage = configDto.customThankYouMessage;
    existingConfig.channelConfig = {
      ...existingConfig.channelConfig,
      commentFieldLabel: configDto.commentFieldLabel,
      commentFieldPlaceholder: configDto.commentFieldPlaceholder,
    };

    // Save with transaction
    return await this.csatFeedbackConfigRepository.saveWithTxn(txn, existingConfig);
  }

  /**
   * Gets a feedback configuration by rule ID
   * @param ruleId - The ID of the rule
   * @returns Feedback config entity or null if not found
   */
  async getFeedbackConfigByRuleId(ruleId: string): Promise<CsatFeedbackConfig | null> {
    return await this.csatFeedbackConfigRepository.findByCondition({
      where: { ruleId },
    });
  }

  /**
   * Maps a feedback config entity to DTO format
   * @param config - The feedback config entity
   * @returns Feedback config DTO
   */
  mapToDto(config: CsatFeedbackConfig): FeedbackConfigDto {
    return {
      enabled: config.enabled,
      feedbackType: config.feedbackType as any,
      brandingColor: config.brandingColor,
      includeCommentField: config.includeCommentField,
      customTitle: config.customTitle,
      customMessage: config.customMessage,
      customThankYouMessage: config.customThankYouMessage,
      commentFieldLabel: config.channelConfig?.commentFieldLabel,
      commentFieldPlaceholder: config.channelConfig?.commentFieldPlaceholder,
    };
  }
}
