import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { email } from '@repo/shared-proto';
import {
  CsatDeliveryChannel,
  CsatDeliveryStatus,
  CsatFeedbackType,
  CsatFilterOperator,
  CsatFilterType,
  CsatMappingStatus,
  CsatSamplingStatus,
  CsatSurvey,
} from '@repo/thena-platform-entities';
import { LessThan, Repository } from 'typeorm';
import { SharedService } from '../../../shared/shared.service';
import { FeedbackTypeEnum } from '../../dto/csat-feedback-config.dto';
import { FilterDto } from '../../dto/csat-rule-filter.dto';
import { CsatRuleResponseDto } from '../../dto/csat-rule-response.dto';
import { AmpEmailService } from '../amp-email/email-template.service';
import { CsatSettingsService } from '../csat-settings.service';
import { CsatSurveyService } from '../csat-survey/csat-survey.service';

@Injectable()
export class CsatCronService {
  private readonly logger = new Logger(CsatCronService.name);
  private isProcessing = false;
  private readonly PROCESSING_TIMEOUT_MINUTES = 5;
  private readonly FIELD_PATH_MAPPING = {
    ticket: {
      'status': 'status.name',
      'tags': 'tags.name',
      'priority': 'priority.name',
      'sentiment': 'sentiment.name',
      'type': 'type.name',
      'team': 'team.name',
      'parentTeam': 'parentTeam.name',
      'assignedAgent': 'assignedAgent.name',
      'requestorEmail': 'requestorEmail',
      'submitterEmail': 'submitterEmail',
      'title': 'title',
      'aiGeneratedTitle': 'aiGeneratedTitle',
      'description': 'description',
      'aiGeneratedSummary': 'aiGeneratedSummary',
      'createdAt': 'createdAt',
      'dueDate': 'dueDate',
      'isEscalated': 'isEscalated',
      'updatedAt': 'updatedAt',
      'isPrivate': 'isPrivate',
      'storyPoints': 'storyPoints',
      'isDraft': 'isDraft',
      'source': 'source',
      'id': 'id',
      'uid': 'uid',
      'teamId': 'teamId',
      'teamUid': 'teamUid',
      'organizationId': 'organizationId',
      'organizationUid': 'organizationUid',
      'ticketId': 'ticketId',
      'formId': 'formId',
      'customerContactId': 'customerContactId',
      'accountId': 'accountId',
    },

    account: {
      'status': 'statusAttribute.name',
      'classification': 'classificationAttribute.name',
      'health': 'healthAttribute.name',
      'industry': 'industryAttribute.name',
      'accountOwner': 'accountOwner.name',
      'id': 'id',
      'uid': 'uid',
      'name': 'name',
      'description': 'description',
      'primaryDomain': 'primaryDomain',
      'secondaryDomain': 'secondaryDomain',
      'annualRevenue': 'annualRevenue',
      'employees': 'employees',
      'website': 'website',
      'billingAddress': 'billingAddress',
      'shippingAddress': 'shippingAddress',
      'createdAt': 'createdAt',
      'updatedAt': 'updatedAt',
      'isActive': 'isActive',
      'source': 'source',
      'organizationId': 'organizationId',
      'organizationUid': 'organizationUid',
    },

    contact: {
      'contactType': 'contactTypeAttribute.name',
      'customerUser': 'customerUser.name',
      'id': 'id',
      'uid': 'uid',
      'firstName': 'firstName',
      'lastName': 'lastName',
      'email': 'email',
      'phoneNumber': 'phoneNumber',
      'isMarketingContact': 'isMarketingContact',
      'isActive': 'isActive',
      'createdAt': 'createdAt',
      'updatedAt': 'updatedAt',
      'organizationId': 'organizationId',
      'organizationUid': 'organizationUid',
    },
  };

  constructor(
    @InjectRepository(CsatSurvey)
    private readonly csatSurveyRepository: Repository<CsatSurvey>,
    private readonly csatSettingsService: CsatSettingsService,
    private readonly csatSurveyService: CsatSurveyService,
    private readonly ampEmailService: AmpEmailService,
    private readonly sharedService: SharedService,
  ) { }

  @Cron(CronExpression.EVERY_MINUTE)
  async handleCsatProcessing() {
    if (this.isProcessing) {
      this.logger.warn('Previous CSAT processing still running, skipping this cycle');
      return;
    }

    this.isProcessing = true;

    try {
      await this.cleanupStuckSurveys();

      const pendingSurveys = await this.csatSurveyService.getPendingCsatSurveys();

      if (pendingSurveys.length === 0) {
        this.logger.debug("No pending CSAT surveys found");
        return;
      }

      this.logger.log(`Processing ${pendingSurveys.length} pending CSAT surveys`);

      const teamSurveys = this.groupSurveysByTeam(pendingSurveys);

      for (const [teamId, surveys] of Object.entries(teamSurveys)) {
        await this.processTeamSurveys(teamId, surveys);
      }
    } catch (error) {
      this.logger.error('Error in CSAT processing cron job', error);
    } finally {
      this.isProcessing = false;
    }
  }

  private async cleanupStuckSurveys() {
    try {
      const timeoutDate = new Date();
      timeoutDate.setMinutes(timeoutDate.getMinutes() - this.PROCESSING_TIMEOUT_MINUTES);

      await this.csatSurveyRepository.update(
        {
          mappingStatus: CsatMappingStatus.PROCESSING,
          updatedAt: LessThan(timeoutDate),
        },
        {
          mappingStatus: CsatMappingStatus.QUEUED,
          processingError: 'Reset due to processing timeout',
          updatedAt: new Date(),
        }
      );

      await this.csatSurveyRepository.update(
        {
          deliveryStatus: CsatDeliveryStatus.SENDING,
          updatedAt: LessThan(timeoutDate),
        },
        {
          deliveryStatus: CsatDeliveryStatus.FAILED,
          deliveryDetails: () =>
            `jsonb_set(COALESCE(delivery_details, '{}'::jsonb), '{errorDetails}', '"Delivery timeout - stuck in SENDING state"')`,
          updatedAt: new Date(),
        }
      );

      this.logger.debug('Completed stuck survey cleanup');
    } catch (error) {
      this.logger.error('Error cleaning up stuck surveys', error);
    }
  }

  private groupSurveysByTeam(surveys: CsatSurvey[]): Record<string, CsatSurvey[]> {
    const teamSurveys: Record<string, CsatSurvey[]> = {};

    for (const survey of surveys) {
      const teamId = survey.teamId.toString();
      if (!teamSurveys[teamId]) {
        teamSurveys[teamId] = [];
      }
      teamSurveys[teamId].push(survey);
    }

    return teamSurveys;
  }

  private async processTeamSurveys(teamId: string, surveys: CsatSurvey[]) {
    try {
      const settings = await this.csatSettingsService.getSettingsByTeamId(teamId);

      if (!settings) {
        await this.markSurveysAsSkipped(surveys, 'Team settings not found');
        return;
      }

      const activeRules = settings.rules.filter(rule => rule.isActive);

      if (activeRules.length === 0) {
        await this.markSurveysAsSkipped(surveys, 'No active rules');
        return;
      }

      const ruleToSurveysMap: Record<string, CsatSurvey[]> = {};
      const unmatchedSurveys: CsatSurvey[] = [];
      const failedSurveys: CsatSurvey[] = [];

      for (const survey of surveys) {
        try {
          const cooldownPassed = this.hasCooldownPeriodPassed(survey, settings.cooldownPeriodDays);
          if (!cooldownPassed) {
            survey.samplingStatus = CsatSamplingStatus.EXCLUDED;
            survey.processingError = 'Cooldown period not passed';
            survey.mappingStatus = CsatMappingStatus.PROCESSED;
            survey.processedAt = new Date();
            survey.updatedAt = new Date();
            unmatchedSurveys.push(survey);
            continue;
          }

          survey.mappingStatus = CsatMappingStatus.PROCESSING;
          survey.updatedAt = new Date();
          await this.csatSurveyRepository.save(survey);

          const ticketData = await this.getTicketData(survey.ticketId);
          const matchedRule = await this.findMatchingRule(activeRules, ticketData);

          survey.mappingStatus = CsatMappingStatus.PROCESSED;
          survey.processedAt = new Date();
          survey.updatedAt = new Date();

          if (matchedRule) {
            survey.ruleName = matchedRule.name;
            survey.ruleConfig = this.extractRuleConfig(matchedRule);

            const ruleId = matchedRule.id.toString();
            if (!ruleToSurveysMap[ruleId]) {
              ruleToSurveysMap[ruleId] = [];
            }
            ruleToSurveysMap[ruleId].push(survey);
          } else {
            survey.samplingStatus = CsatSamplingStatus.EXCLUDED;
            survey.ruleName = null;
            survey.ruleConfig = {};
            unmatchedSurveys.push(survey);
          }
        } catch (error) {
          this.logger.error(`Error processing survey ${survey.id}:`, error);
          survey.mappingStatus = CsatMappingStatus.FAILED;
          survey.processingError = error.message;
          survey.updatedAt = new Date();
          failedSurveys.push(survey);
        }
      }

      if (unmatchedSurveys.length > 0) {
        await this.csatSurveyRepository.save(unmatchedSurveys);
      }

      if (failedSurveys.length > 0) {
        await this.csatSurveyRepository.save(failedSurveys);
      }

      for (const [ruleId, ruleSurveys] of Object.entries(ruleToSurveysMap)) {
        const rule = activeRules.find(r => r.id.toString() === ruleId);
        if (rule) {
          const emailConfig = {
            id: settings.emailConfigId,
          }
          await this.applySamplingLogicToGroup(ruleSurveys, rule, emailConfig);
        }
      }
    } catch (error) {
      this.logger.error(`Error processing team ${teamId} surveys:`, error);
      await this.markSurveysAsFailed(surveys, error.message);
    }
  }

  private hasCooldownPeriodPassed(survey: CsatSurvey, cooldownDays: number): boolean {
    if (!cooldownDays) {
      return true;
    }

    const cooldownDate = new Date(survey.ticketClosedAt);
    cooldownDate.setDate(cooldownDate.getDate() + cooldownDays);

    return new Date() >= cooldownDate;
  }

  private async getTicketData(ticketId: string): Promise<Record<string, any>> {
    try {
      const ticket = await this.sharedService.getTicketByPrimaryKey(ticketId);

      if (!ticket) {
        return {};
      }

      const ticketData = {
        ...ticket,
        teamUid: ticket.team?.uid,
        organizationUid: ticket.organization?.uid,
        customFields: this.formatCustomFields(ticket.customFieldValues),
      };

      const accountData = ticket.account ? {
        ...ticket.account,
        organizationUid: ticket.account.organization?.uid,
        customFields: this.formatCustomFields(ticket.account.customFieldValues),
      } : null;

      const contactData = ticket.customerContact ? {
        ...ticket.customerContact,
        organizationUid: ticket.customerContact.organization?.uid,
        customFields: this.formatCustomFields(ticket.customerContact.customFieldValues),
      } : null;

      const result = {
        ticket: ticketData,
        account: accountData,
        contact: contactData,
      };

      return result;
    } catch (error) {
      this.logger.error('Error getting ticket data:', error);
      throw error;
    }
  }

  private hasFilters(filters: FilterDto): boolean {
    if (!filters) return false;

    const hasTicketFilters = (filters.ticket?.standardFields?.length > 0 || filters.ticket?.customFields?.length > 0);
    const hasAccountFilters = (filters.account?.standardFields?.length > 0 || filters.account?.customFields?.length > 0);
    const hasContactFilters = (filters.contact?.standardFields?.length > 0 || filters.contact?.customFields?.length > 0);

    return hasTicketFilters || hasAccountFilters || hasContactFilters;
  }

  private formatCustomFields(customFieldValues: any[]): Record<string, any> {
    if (!customFieldValues || !Array.isArray(customFieldValues)) {
      return {};
    }

    const formattedFields = {};

    customFieldValues.forEach(cfv => {
      if (cfv && cfv.customFieldId) {
        formattedFields[cfv.customFieldId] = cfv.value;
      }
    });

    return formattedFields;
  }

  private async findMatchingRule(
    rules: CsatRuleResponseDto[],
    ticketData: Record<string, any>
  ): Promise<CsatRuleResponseDto | null> {
    this.logger.debug('Starting rule matching process');
    this.logger.debug(`Number of active rules to check: ${rules.length}`);

    const sortedRules = [...rules].sort((a, b) => b.priority - a.priority);

    for (const rule of sortedRules) {
      this.logger.debug(`Checking Rule: "${rule.name}" (Priority: ${rule.priority})`);

      const hasAnyFilters = rule.anyFilters && this.hasFilters(rule.anyFilters);
      const hasAllFilters = rule.allFilters && this.hasFilters(rule.allFilters);

      if (!hasAnyFilters && !hasAllFilters) {
        this.logger.debug('Rule has no filters defined, skipping...');
        continue;
      }

      const doesMatch = await this.doesRuleMatch(rule, ticketData);
      if (doesMatch) {
        this.logger.debug(`RULE MATCHED: "${rule.name}"`);
        return rule;
      } else {
        this.logger.debug(`Rule did not match: "${rule.name}"`);
      }
    }

    this.logger.debug('NO RULES MATCHED');
    return null;
  }

  private async doesRuleMatch(
    rule: CsatRuleResponseDto,
    ticketData: Record<string, any>
  ): Promise<boolean> {
    this.logger.debug('Evaluating Rule Conditions');

    if (rule.anyFilters && this.hasFilters(rule.anyFilters)) {
      this.logger.debug('Checking ANY filters (at least one must match):');
      const anyMatch = await this.checkAnyFilters(rule.anyFilters, ticketData);

      if (!anyMatch) {
        this.logger.debug('ANY filters check failed - no filters matched');
        return false;
      }
      this.logger.debug('ANY filters check passed - at least one filter matched');
    }

    if (rule.allFilters && this.hasFilters(rule.allFilters)) {
      this.logger.debug('Checking ALL filters (all must match):');
      const allMatch = await this.checkAllFilters(rule.allFilters, ticketData);

      if (!allMatch) {
        this.logger.debug('ALL filters check failed - not all filters matched');
        return false;
      }
      this.logger.debug('ALL filters check passed - all filters matched');
    }

    return true;
  }

  private processFilters(
    filterType: CsatFilterType,
    filters: {
      standardFields?: Array<{ field: string; operator: string; value: any }>;
      customFields?: Array<{ field: string; customFieldId: string; operator: string; value: any }>;
    },
    ticketData: Record<string, any>
  ): boolean[] {

    let entityKey: string;
    switch (filterType) {
      case CsatFilterType.TICKET:
        entityKey = 'ticket';
        break;
      case CsatFilterType.ACCOUNT:
        entityKey = 'account';
        break;
      case CsatFilterType.CONTACT:
        entityKey = 'contact';
        break;
      default:
        return [];
    }

    const results: boolean[] = [];
    const entity = ticketData[entityKey];

    if (!entity) {
      this.logger.debug(`Entity "${entityKey}" not found in ticket data`);
      const totalFilters = (filters?.standardFields?.length || 0) + (filters?.customFields?.length || 0);
      return Array(totalFilters).fill(false);
    }

    if (filters?.standardFields?.length > 0) {
      this.logger.debug(`Standard Fields (${filters.standardFields.length}):`);
      for (const filter of filters.standardFields) {
        this.logger.debug(`Checking field: "${filter.field}"`);

        const fieldPath = this.FIELD_PATH_MAPPING[entityKey]?.[filter.field];
        let fieldValue;

        if (fieldPath) {
          this.logger.debug(`Using mapped path: ${fieldPath}`);
          fieldValue = this.getFieldValue(entity, fieldPath);
        } else {
          this.logger.debug(`Using direct field access: ${filter.field}`);
          fieldValue = this.getFieldValue(entity, filter.field);
        }

        const operatorEnum = filter.operator as CsatFilterOperator;
        const result = this.compareValues(fieldValue, filter.value, operatorEnum);

        this.logger.debug('=== FILTER COMPARISON ===');
        this.logger.debug(`Entity: ${entityKey}`);
        this.logger.debug(`Field: ${filter.field}`);
        this.logger.debug(`Operator: ${operatorEnum}`);
        this.logger.debug(`Expected Value: ${JSON.stringify(filter.value)}`);
        this.logger.debug(`Actual Value: ${JSON.stringify(fieldValue)}`);
        this.logger.debug(`Result: ${result ? 'MATCH ✓' : 'NO MATCH ✗'}`);

        results.push(result);
      }
    }

    if (filters?.customFields?.length > 0) {
      this.logger.debug(`Custom Fields (${filters.customFields.length}):`);
      for (const filter of filters.customFields) {
        this.logger.debug(`Checking custom field: "${filter.field}" (ID: ${filter.customFieldId})`);

        const customFieldValue = entity.customFields?.[filter.customFieldId];

        const operatorEnum = filter.operator as CsatFilterOperator;
        const result = this.compareValues(customFieldValue, filter.value, operatorEnum);

        this.logger.debug('=== FILTER COMPARISON ===');
        this.logger.debug(`Entity: ${entityKey}`);
        this.logger.debug(`Field: ${filter.field} (Custom Field ID: ${filter.customFieldId})`);
        this.logger.debug(`Operator: ${operatorEnum}`);
        this.logger.debug(`Expected Value: ${JSON.stringify(filter.value)}`);
        this.logger.debug(`Actual Value: ${JSON.stringify(customFieldValue)}`);
        this.logger.debug(`Result: ${result ? 'MATCH ✓' : 'NO MATCH ✗'}`);

        results.push(result);
      }
    }

    const summary = {
      total: results.length,
      passed: results.filter(r => r === true).length,
      failed: results.filter(r => r === false).length,
    };
    this.logger.debug(`${entityKey} Filter Summary: ${summary.passed}/${summary.total} passed`);

    return results;
  }

  private async checkAnyFilters(
    filters: FilterDto,
    ticketData: Record<string, any>
  ): Promise<boolean> {
    this.logger.debug('Processing ANY filters logic:');

    const ticketResults = await this.processFilters(CsatFilterType.TICKET, filters.ticket || {}, ticketData);
    const accountResults = await this.processFilters(CsatFilterType.ACCOUNT, filters.account || {}, ticketData);
    const contactResults = await this.processFilters(CsatFilterType.CONTACT, filters.contact || {}, ticketData);

    const allResults = [...ticketResults, ...accountResults, ...contactResults];

    if (allResults.length === 0) {
      this.logger.debug('No filters to check, returning true');
      return true;
    }

    const hasMatch = allResults.some(result => result === true);
    this.logger.debug(`ANY filters result: ${hasMatch ? 'At least one matched' : 'None matched'}`);

    return hasMatch;
  }

  private async checkAllFilters(
    filters: FilterDto,
    ticketData: Record<string, any>
  ): Promise<boolean> {
    this.logger.debug('Processing ALL filters logic:');

    const ticketResults = await this.processFilters(CsatFilterType.TICKET, filters.ticket || {}, ticketData);
    const accountResults = await this.processFilters(CsatFilterType.ACCOUNT, filters.account || {}, ticketData);
    const contactResults = await this.processFilters(CsatFilterType.CONTACT, filters.contact || {}, ticketData);

    const allResults = [...ticketResults, ...accountResults, ...contactResults];

    const allMatch = allResults.every(result => result === true);
    this.logger.debug(`ALL filters result: ${allMatch ? 'All matched' : 'Not all matched'} (${allResults.filter(r => r).length}/${allResults.length})`);

    return allMatch;
  }

  private getFieldValue(obj: Record<string, any>, fieldPath: string): any {
    this.logger.debug(`Getting value for path: "${fieldPath}"`);

    if (!obj || !fieldPath) {
      this.logger.debug(`Invalid object or field path`);
      return undefined;
    }

    const parts = fieldPath.split('.');
    let current = obj;

    for (let i = 0; i < parts.length; i++) {
      const part = parts[i];

      if (current === null || current === undefined) {
        this.logger.debug(`Value is null/undefined at part: "${part}"`);
        return undefined;
      }

      if (Array.isArray(current)) {
        this.logger.debug(`Array detected at part: "${part}"`);
        if (i === parts.length - 1 && part === 'name') {
          const result = current.map(item => item?.name).filter(name => name !== undefined);
          this.logger.debug(`Extracted names from array: ${JSON.stringify(result)}`);
          return result;
        } else if (part === 'id' || part === 'uid') {
          const result = current.map(item => item?.[part]).filter(id => id !== undefined);
          this.logger.debug(`Extracted ${part}s from array: ${JSON.stringify(result)}`);
          return result;
        }

        const values = [];
        for (const item of current) {
          if (item && typeof item === 'object') {
            const value = this.getFieldValue(item, parts.slice(i).join('.'));
            if (value !== undefined) {
              values.push(value);
            }
          }
        }
        this.logger.debug(`Processed array values: ${JSON.stringify(values)}`);
        return values.length > 0 ? values : undefined;
      }

      current = current[part];
    }

    this.logger.debug(`Retrieved value: ${JSON.stringify(current)}`);
    return current;
  }

  private compareValues(
    actual: any,
    expected: any,
    operator: CsatFilterOperator
  ): boolean {
    this.logger.debug(`Comparing with operator: ${operator}`);
    this.logger.debug(`Actual type: ${typeof actual}, Is Array: ${Array.isArray(actual)}`);
    this.logger.debug(`Expected type: ${typeof expected}, Is Array: ${Array.isArray(expected)}`);

    if (actual === undefined || actual === null) {
      const result = operator === CsatFilterOperator.IS_EMPTY;
      this.logger.debug(`Null/undefined check: ${result}`);
      return result;
    }

    if (operator === CsatFilterOperator.IS_NOT_EMPTY) {
      const result = actual !== undefined && actual !== null;
      this.logger.debug(`IS_NOT_EMPTY check: ${result}`);
      return result;
    }

    let normalizedActual = actual;
    let normalizedExpected = expected;

    // Handle date strings - check if strings look like ISO dates
    const isoDateRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/;

    if (typeof actual === 'string' && isoDateRegex.test(actual)) {
      normalizedActual = new Date(actual);
      this.logger.debug(`Normalized actual from ISO string to Date: ${normalizedActual}`);
    }

    if (typeof expected === 'string' && isoDateRegex.test(expected)) {
      normalizedExpected = new Date(expected);
      this.logger.debug(`Normalized expected from ISO string to Date: ${normalizedExpected}`);
    }

    if (typeof expected === 'string' && ['true', 'false'].includes(expected.toLowerCase())) {
      normalizedExpected = expected.toLowerCase() === 'true';
      this.logger.debug(`Normalized expected from string to boolean: ${normalizedExpected}`);
    }
    if (typeof actual === 'string' && ['true', 'false'].includes(actual.toLowerCase())) {
      normalizedActual = actual.toLowerCase() === 'true';
      this.logger.debug(`Normalized actual from string to boolean: ${normalizedActual}`);
    }

    if (typeof expected === 'string' && !isNaN(Number(expected)) && !isoDateRegex.test(expected)) {
      normalizedExpected = expected.includes('.') ? parseFloat(expected) : parseInt(expected, 10);
      this.logger.debug(`Normalized expected from string to number: ${normalizedExpected}`);
    }
    if (typeof actual === 'string' && !isNaN(Number(actual)) && !isoDateRegex.test(actual)) {
      normalizedActual = actual.includes('.') ? parseFloat(actual) : parseInt(actual, 10);
      this.logger.debug(`Normalized actual from string to number: ${normalizedActual}`);
    }

    if (Array.isArray(normalizedActual)) {
      this.logger.debug(`Handling array comparison`);

      if (typeof normalizedExpected === 'string' &&
        normalizedExpected.startsWith('[') &&
        normalizedExpected.endsWith(']')) {
        try {
          normalizedExpected = JSON.parse(normalizedExpected);
          this.logger.debug(`Parsed expected value to array: ${JSON.stringify(normalizedExpected)}`);
        } catch (e) {
          this.logger.debug(`Failed to parse expected value as array: ${e.message}`);
        }
      }

      const expectedArray = Array.isArray(normalizedExpected) ?
        normalizedExpected : [normalizedExpected];

      switch (operator) {
        case CsatFilterOperator.EQUALS: {
          const equalsResult = normalizedActual.some(item =>
            this.areValuesEqual(item, normalizedExpected)
          );
          this.logger.debug(`Array EQUALS result: ${equalsResult}`);
          return equalsResult;
        }

        case CsatFilterOperator.NOT_EQUALS: {
          const notEqualsResult = !normalizedActual.some(item =>
            this.areValuesEqual(item, normalizedExpected)
          );
          this.logger.debug(`Array NOT_EQUALS result: ${notEqualsResult}`);
          return notEqualsResult;
        }

        case CsatFilterOperator.CONTAINS: {
          const containsResult = expectedArray.some(expectedItem =>
            normalizedActual.some(actualItem =>
              this.areValuesEqual(actualItem, expectedItem)
            )
          );
          this.logger.debug(`Array CONTAINS result: ${containsResult}`);
          return containsResult;
        }

        case CsatFilterOperator.NOT_CONTAINS: {
          const notContainsResult = !expectedArray.some(expectedItem =>
            normalizedActual.some(actualItem =>
              this.areValuesEqual(actualItem, expectedItem)
            )
          );
          this.logger.debug(`Array NOT_CONTAINS result: ${notContainsResult}`);
          return notContainsResult;
        }

        case CsatFilterOperator.IN: {
          const inResult = normalizedActual.some(actualItem =>
            expectedArray.some(expectedItem =>
              this.areValuesEqual(actualItem, expectedItem)
            )
          );
          this.logger.debug(`Array IN result: ${inResult}`);
          return inResult;
        }

        case CsatFilterOperator.NOT_IN: {
          const notInResult = !normalizedActual.some(actualItem =>
            expectedArray.some(expectedItem =>
              this.areValuesEqual(actualItem, expectedItem)
            )
          );
          this.logger.debug(`Array NOT_IN result: ${notInResult}`);
          return notInResult;
        }

        default: {
          const defaultResult = normalizedActual.some(item =>
            this.compareValues(item, normalizedExpected, operator)
          );
          this.logger.debug(`Array default operator result: ${defaultResult}`);
          return defaultResult;
        }
      }
    }

    if (typeof normalizedActual === 'string' && typeof normalizedExpected === 'string') {
      const actualLower = normalizedActual.toLowerCase().trim();
      const expectedLower = normalizedExpected.toLowerCase().trim();

      switch (operator) {
        case CsatFilterOperator.CONTAINS: {
          const containsResult = actualLower.includes(expectedLower);
          this.logger.debug(`String CONTAINS result: ${containsResult}`);
          return containsResult;
        }

        case CsatFilterOperator.NOT_CONTAINS: {
          const notContainsResult = !actualLower.includes(expectedLower);
          this.logger.debug(`String NOT_CONTAINS result: ${notContainsResult}`);
          return notContainsResult;
        }
      }
    }

    let result: boolean;
    switch (operator) {
      case CsatFilterOperator.EQUALS: {
        result = this.areValuesEqual(normalizedActual, normalizedExpected);
        this.logger.debug(`EQUALS result: ${result}`);
        return result;
      }

      case CsatFilterOperator.NOT_EQUALS: {
        result = !this.areValuesEqual(normalizedActual, normalizedExpected);
        this.logger.debug(`NOT_EQUALS result: ${result}`);
        return result;
      }

      case CsatFilterOperator.GREATER_THAN: {
        if (normalizedActual instanceof Date && normalizedExpected instanceof Date) {
          result = normalizedActual.getTime() > normalizedExpected.getTime();
          this.logger.debug(`GREATER_THAN (Date) result: ${result} (${normalizedActual} > ${normalizedExpected})`);
        } else {
          result = normalizedActual > normalizedExpected;
          this.logger.debug(`GREATER_THAN result: ${result}`);
        }
        return result;
      }

      case CsatFilterOperator.LESS_THAN: {
        if (normalizedActual instanceof Date && normalizedExpected instanceof Date) {
          result = normalizedActual.getTime() < normalizedExpected.getTime();
          this.logger.debug(`LESS_THAN (Date) result: ${result} (${normalizedActual} < ${normalizedExpected})`);
        } else {
          result = normalizedActual < normalizedExpected;
          this.logger.debug(`LESS_THAN result: ${result}`);
        }
        return result;
      }

      case CsatFilterOperator.GREATER_THAN_EQUAL: {
        if (normalizedActual instanceof Date && normalizedExpected instanceof Date) {
          result = normalizedActual.getTime() >= normalizedExpected.getTime();
          this.logger.debug(`GREATER_THAN_EQUAL (Date) result: ${result} (${normalizedActual} >= ${normalizedExpected})`);
        } else {
          result = normalizedActual >= normalizedExpected;
          this.logger.debug(`GREATER_THAN_EQUAL result: ${result}`);
        }
        return result;
      }

      case CsatFilterOperator.LESS_THAN_EQUAL: {
        if (normalizedActual instanceof Date && normalizedExpected instanceof Date) {
          result = normalizedActual.getTime() <= normalizedExpected.getTime();
          this.logger.debug(`LESS_THAN_EQUAL (Date) result: ${result} (${normalizedActual} <= ${normalizedExpected})`);
        } else {
          result = normalizedActual <= normalizedExpected;
          this.logger.debug(`LESS_THAN_EQUAL result: ${result}`);
        }
        return result;
      }

      case CsatFilterOperator.IN: {
        if (typeof normalizedExpected === 'string' &&
          normalizedExpected.startsWith('[') &&
          normalizedExpected.endsWith(']')) {
          try {
            const expectedArray = JSON.parse(normalizedExpected);
            result = Array.isArray(expectedArray) &&
              expectedArray.some(item => this.areValuesEqual(normalizedActual, item));
            this.logger.debug(`IN (parsed) result: ${result}`);
            return result;
          } catch (e) {
            this.logger.debug(`IN parse error: ${e.message}`);
            return false;
          }
        }

        if (Array.isArray(normalizedExpected)) {
          result = normalizedExpected.some(item => this.areValuesEqual(normalizedActual, item));
          this.logger.debug(`IN (array) result: ${result}`);
          return result;
        }

        result = this.areValuesEqual(normalizedActual, normalizedExpected);
        this.logger.debug(`IN (direct) result: ${result}`);
        return result;
      }

      case CsatFilterOperator.NOT_IN: {
        if (typeof normalizedExpected === 'string' &&
          normalizedExpected.startsWith('[') &&
          normalizedExpected.endsWith(']')) {
          try {
            const expectedArray = JSON.parse(normalizedExpected);
            result = !Array.isArray(expectedArray) ||
              !expectedArray.some(item => this.areValuesEqual(normalizedActual, item));
            this.logger.debug(`NOT_IN (parsed) result: ${result}`);
            return result;
          } catch (e) {
            this.logger.debug(`NOT_IN parse error: ${e.message}`);
            return true;
          }
        }

        if (Array.isArray(normalizedExpected)) {
          result = !normalizedExpected.some(item => this.areValuesEqual(normalizedActual, item));
          this.logger.debug(`NOT_IN (array) result: ${result}`);
          return result;
        }

        result = !this.areValuesEqual(normalizedActual, normalizedExpected);
        this.logger.debug(`NOT_IN (direct) result: ${result}`);
        return result;
      }

      default: {
        this.logger.debug(`Unknown operator: ${operator}`);
        return false;
      }
    }
  }

  private areValuesEqual(a: any, b: any): boolean {
    this.logger.debug(`Comparing equality: "${a}" vs "${b}"`);

    // Handle same types
    const typeA = typeof a;
    const typeB = typeof b;

    if (typeA === typeB) {
      switch (typeA) {
        case 'string': {
          const stringResult = a.toLowerCase() === b.toLowerCase();
          this.logger.debug(`String comparison result: ${stringResult}`);
          return stringResult;
        }
        case 'object': {
          if (a instanceof Date && b instanceof Date) {
            const dateResult = a.getTime() === b.getTime();
            this.logger.debug(`Date comparison result: ${dateResult}`);
            return dateResult;
          }
          if (a === null || b === null) {
            const nullResult = a === b;
            this.logger.debug(`Null comparison result: ${nullResult}`);
            return nullResult;
          }
          break;
        }
        default: {
          const defaultResult = a === b;
          this.logger.debug(`Default comparison result: ${defaultResult}`);
          return defaultResult;
        }
      }
    }

    // Handle type conversions using a conversion map
    const conversions = {
      'boolean-string': () => {
        const strValue = typeA === 'string' ? a : b;
        const boolValue = typeA === 'boolean' ? a : b;
        const result = boolValue === (strValue.toLowerCase().trim() === 'true');
        this.logger.debug(`Boolean-string conversion result: ${result}`);
        return result;
      },
      'number-string': () => {
        const strValue = typeA === 'string' ? a : b;
        const numValue = typeA === 'number' ? a : b;
        const result = numValue === Number(strValue);
        this.logger.debug(`Number-string conversion result: ${result}`);
        return result;
      },
    };

    const conversionKey = `${typeA}-${typeB}`;
    const reverseKey = `${typeB}-${typeA}`;
    const converter = conversions[conversionKey] || conversions[reverseKey];

    if (converter) {
      return converter();
    }

    // Fallback to loose equality
    const looseResult = a == b;
    this.logger.debug(`Loose equality result: ${looseResult}`);
    return looseResult;
  }

  private extractRuleConfig(rule: CsatRuleResponseDto): Record<string, any> {
    return {
      id: rule.id,
      name: rule.name,
      description: rule.description,
      priority: rule.priority,
      triggerConfig: rule.triggerConfig,
      feedbackConfig: rule.feedbackConfig,
    };
  }

  private async applySamplingLogicToGroup(
    surveys: CsatSurvey[],
    rule: CsatRuleResponseDto,
    emailConfig: email.GetEmailConfigByIdRequest
  ) {
    const triggerType = rule.triggerConfig?.triggerType || 'always';

    // Set sampling method for all surveys
    surveys.forEach(survey => {
      survey.samplingMethod = triggerType;
      survey.updatedAt = new Date();
    });

    if (triggerType === 'always') {
      // Select all surveys for delivery
      this.logger.log(`Sampling: Selecting all ${surveys.length} surveys (always trigger)`);

      for (const survey of surveys) {
        await this.selectSurveyForDelivery(survey, rule, emailConfig);
      }
      return;
    }

    if (triggerType === 'random') {
      const percentage = rule.triggerConfig?.randomPercentage || 20;
      const samplingRate = percentage / 100;

      this.logger.log(`Sampling: Random selection at ${percentage}% rate for ${surveys.length} surveys`);

      // Calculate how many to select, ensuring at least 1 if there are any surveys
      const countToSelect = surveys.length > 0 ? Math.max(1, Math.round(surveys.length * samplingRate)) : 0;

      // Fisher-Yates shuffle for truly random selection
      const shuffledSurveys = this.fisherYatesShuffle(surveys);

      // Take the first countToSelect surveys as selected
      const selectedSurveys = shuffledSurveys.slice(0, countToSelect);
      const notSelectedSurveys = shuffledSurveys.slice(countToSelect);

      // Set sampling metadata for all surveys
      [...selectedSurveys, ...notSelectedSurveys].forEach(survey => {
        survey.samplingRate = samplingRate;
        survey.samplingValue = Math.random(); // Store random value for audit
        survey.updatedAt = new Date();
      });

      this.logger.log(`Sampling: Selected ${selectedSurveys.length} out of ${surveys.length} surveys (${surveys.length > 0 ? (selectedSurveys.length / surveys.length * 100).toFixed(1) : 0}%)`);

      // Process selected surveys
      for (const survey of selectedSurveys) {
        await this.selectSurveyForDelivery(survey, rule, emailConfig);
      }

      // Mark not selected surveys
      for (const survey of notSelectedSurveys) {
        survey.samplingStatus = CsatSamplingStatus.NOT_SELECTED;
      }

      // Save not selected surveys in bulk
      if (notSelectedSurveys.length > 0) {
        await this.csatSurveyRepository.save(notSelectedSurveys);
      }

      return;
    }

    // Unknown trigger type - exclude all
    this.logger.warn(`Sampling: Unknown trigger type "${triggerType}", excluding all surveys`);

    for (const survey of surveys) {
      survey.samplingStatus = CsatSamplingStatus.EXCLUDED;
      survey.updatedAt = new Date();
    }
    await this.csatSurveyRepository.save(surveys);
  }

  private fisherYatesShuffle<T>(array: T[]): T[] {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }

  private async selectSurveyForDelivery(survey: CsatSurvey, rule: CsatRuleResponseDto, emailConfig: email.GetEmailConfigByIdRequest) {
    survey.samplingStatus = CsatSamplingStatus.SELECTED;
    survey.deliveryStatus = CsatDeliveryStatus.PENDING;
    survey.updatedAt = new Date();
    this.configureSurvey(survey, rule);

    await this.csatSurveyRepository.save(survey);

    const ticketData = await this.getTicketData(survey.ticketId);
    await this.queueSurveyForDelivery(survey, ticketData, emailConfig);
  }

  private configureSurvey(survey: CsatSurvey, rule: CsatRuleResponseDto) {
    const feedbackConfig = rule.feedbackConfig;
    if (!feedbackConfig) {
      return;
    }

    switch (feedbackConfig.feedbackType) {
      case FeedbackTypeEnum.STAR:
        survey.feedbackType = CsatFeedbackType.STAR;
        break;
      case FeedbackTypeEnum.THUMBS:
        survey.feedbackType = CsatFeedbackType.THUMBS;
        break;
      default:
        survey.feedbackType = CsatFeedbackType.STAR;
        break;
    }

    survey.deliveryChannel = CsatDeliveryChannel.EMAIL;

    survey.surveyConfig = {
      includeComment: feedbackConfig.includeCommentField || false,
      titleMessage: feedbackConfig.customTitle || 'How was your experience?',
      feedbackMessage: feedbackConfig.customMessage || 'Please rate your satisfaction with our service',
      thankYouMessage: feedbackConfig.customThankYouMessage || 'Thank you for your feedback!',
      commentFieldLabel: feedbackConfig.commentFieldLabel || 'Additional comments',
      commentPlaceholder: feedbackConfig.commentFieldPlaceholder || 'Tell us more about your experience...',
      brandingColor: feedbackConfig.brandingColor || '#000000',
      ratingScale: survey.feedbackType === CsatFeedbackType.STAR ? 5 : 10,
    };
  }

  private async queueSurveyForDelivery(survey: CsatSurvey, ticketData: any, emailConfig: email.GetEmailConfigByIdRequest) {
    try {
      survey.deliveryStatus = CsatDeliveryStatus.SENDING;
      survey.updatedAt = new Date();
      await this.csatSurveyRepository.save(survey);

      const sent = await this.sendSurveyEmail(survey, ticketData, emailConfig);

      if (sent) {
        survey.deliveryStatus = CsatDeliveryStatus.SENT;
        survey.sentAt = new Date();
      } else {
        survey.deliveryStatus = CsatDeliveryStatus.FAILED;
        survey.deliveryDetails = {
          ...survey.deliveryDetails,
          errorDetails: 'Failed to send email',
          deliveryAttempts: (survey.deliveryDetails?.deliveryAttempts || 0) + 1,
          lastAttemptAt: new Date().toISOString(),
        };
      }

      survey.updatedAt = new Date();
      await this.csatSurveyRepository.save(survey);
    } catch (error) {
      this.logger.error(`Error in queueSurveyForDelivery for survey ${survey.id}:`, error);
      survey.deliveryStatus = CsatDeliveryStatus.FAILED;
      survey.deliveryDetails = {
        ...survey.deliveryDetails,
        errorDetails: error.message,
        deliveryAttempts: (survey.deliveryDetails?.deliveryAttempts || 0) + 1,
        lastAttemptAt: new Date().toISOString(),
      };
      survey.updatedAt = new Date();

      await this.csatSurveyRepository.save(survey);
    }
  }

  private async sendSurveyEmail(survey: CsatSurvey, ticketData: any, emailConfig: email.GetEmailConfigByIdRequest): Promise<boolean> {
    try {
      const surveyDetails = {
        teamId: survey.teamId,
        orgId: survey.organizationId,
        ticketId: survey.ticketId,
      };

      const emailPackage = this.ampEmailService.createSurveyEmailPackage(
        survey.ruleConfig as CsatRuleResponseDto,
        surveyDetails
      );

      const to = ticketData.ticket.requestorEmail;
      const subject = "How was your support experience?";

      const emailConfigInfo = await this.csatSurveyService.getEmailConfigById(emailConfig)
      const from = emailConfigInfo.forwardingEmailAddress

      await this.csatSurveyService.sendAnEmail({
        from,
        to,
        subject,
        htmlBody: emailPackage.fallbackHtml,
        textBody: emailPackage.plainText,
      });

      survey.deliveryDetails = {
        ...survey.deliveryDetails,
        recipient: survey.requestorEmail,
        deliveryAttempts: (survey.deliveryDetails?.deliveryAttempts || 0) + 1,
        lastAttemptAt: new Date().toISOString(),
      };

      return true;
    } catch (error) {
      this.logger.error('Error in sendSurveyEmail:', error);
      return false;
    }
  }

  private async markSurveysAsSkipped(surveys: CsatSurvey[], reason: string) {
    for (const survey of surveys) {
      survey.mappingStatus = CsatMappingStatus.PROCESSED;
      survey.processingError = reason;
      survey.processedAt = new Date();
      survey.samplingStatus = CsatSamplingStatus.EXCLUDED;
      survey.updatedAt = new Date();
    }

    await this.csatSurveyRepository.save(surveys);
  }

  private async markSurveysAsFailed(surveys: CsatSurvey[], errorMessage: string) {
    for (const survey of surveys) {
      survey.mappingStatus = CsatMappingStatus.FAILED;
      survey.processingError = errorMessage;
      survey.processedAt = new Date();
      survey.updatedAt = new Date();
    }

    await this.csatSurveyRepository.save(surveys);
  }
}
