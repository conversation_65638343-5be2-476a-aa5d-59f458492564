import { Injectable } from '@nestjs/common';
import {
  CsatAuditLog,
  CsatRule,
  CsatSettings,
} from '@repo/thena-platform-entities';
import { CsatRuleResponseDto } from '../dto/csat-rule-response.dto';
import { CsatSettingsResponseDto } from '../dto/csat-settings-response.dto';
import { organizationToDto, teamToDto } from '../utils/helper';
import { CsatFeedbackConfigService } from './csat-feedback-config.service';
import { CsatRuleFilterService } from './csat-rule-filter.service';
import { CsatTriggerConfigService } from './csat-trigger-config.service';

@Injectable()
export class CsatMapperService {
  constructor(
    private readonly csatRuleFilterService: CsatRuleFilterService,
    private readonly csatTriggerConfigService: CsatTriggerConfigService,
    private readonly csatFeedbackConfigService: CsatFeedbackConfigService,
  ) { }

  /**
   * Maps a CsatSettings entity to its DTO representation
   */
  mapSettingsToDto(settings: CsatSettings, rules: CsatRuleResponseDto[] = []): CsatSettingsResponseDto {
    return {
      id: settings.id,
      team: teamToDto(settings.team),
      organization: organizationToDto(settings.organization),
      isEnabled: settings.isEnabled,
      rules,
      cooldownPeriodDays: settings.cooldownPeriodDays,
      createdAt: settings.createdAt.toISOString(),
      updatedAt: settings.updatedAt.toISOString(),
    };
  }

  /**
   * Maps a CsatRule entity to its DTO representation
   */
  async mapRuleToDto(rule: CsatRule): Promise<CsatRuleResponseDto> {
    try {
      // Get related data with proper filtering
      const allFilters = await this.csatRuleFilterService.getAllFiltersByRuleId(rule.id);
      const anyFilters = await this.csatRuleFilterService.getAnyFiltersByRuleId(rule.id);
      const triggerConfig = await this.csatTriggerConfigService.getTriggerConfigByRuleId(rule.id);
      const feedbackConfig = await this.csatFeedbackConfigService.getFeedbackConfigByRuleId(rule.id);

      if (!triggerConfig || !feedbackConfig) {
        throw new Error(`Configuration for CSAT rule with ID ${rule.id} is incomplete`);
      }

      return {
        id: rule.id,
        name: rule.name,
        description: rule.description,
        isActive: rule.isActive,
        allFilters: this.csatRuleFilterService.mapFiltersToDto(allFilters),
        anyFilters: this.csatRuleFilterService.mapFiltersToDto(anyFilters),
        triggerConfig: this.csatTriggerConfigService.mapToDto(triggerConfig),
        feedbackConfig: this.csatFeedbackConfigService.mapToDto(feedbackConfig),
        priority: rule.priority,
        createdAt: rule.createdAt.toISOString(),
        updatedAt: rule.updatedAt.toISOString(),
      };
    } catch (error) {
      console.error(`Error mapping rule ${rule.id} to DTO:`, error);

      // Return a partial rule object in case of error
      return {
        id: rule.id,
        name: rule.name,
        description: rule.description,
        isActive: rule.isActive,
        allFilters: null,
        anyFilters: null,
        triggerConfig: null,
        feedbackConfig: null,
        priority: rule.priority,
        createdAt: rule.createdAt.toISOString(),
        updatedAt: rule.updatedAt.toISOString(),
      };
    }
  }

  /**
   * Maps a CsatAuditLog entity to its DTO representation
   */
  mapAuditLogToDto(auditLog: CsatAuditLog): any {
    return {
      id: auditLog.id,
      team: teamToDto(auditLog.team),
      organization: organizationToDto(auditLog.organization),
      entityType: auditLog.entityType,
      entityId: auditLog.entityId,
      action: auditLog.action,
      previousState: auditLog.previousState,
      newState: auditLog.newState,
      metadata: auditLog.metadata,
      createdAt: auditLog.createdAt.toISOString(),
    };
  }
}
