import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Patch,
  Post,
} from '@nestjs/common';
import {
  ApiBody,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import {
  ApiCreateEndpoint,
  ApiDeleteEndpoint,
  ApiGetEndpoint,
  ApiResponseMessage,
  ApiUpdateEndpoint,
} from '@repo/nestjs-commons/decorators';
import { TransactionService } from '@repo/thena-platform-entities';
import { CurrentUser } from '../../common/decorators';
import { SkipAllThrottler } from '../../common/decorators/throttler.decorator';
import { SharedService } from '../../shared/shared.service';
import { CreateCsatRuleDto } from '../dto/create-csat-rule.dto';
import { CsatRuleResponseDto } from '../dto/csat-rule-response.dto';
import { CsatSettingsResponseDto } from '../dto/csat-settings-response.dto';
import { CsatSettingsWithRuleDto } from '../dto/csat-settings-with-rule.dto';
import { UpdateCsatRuleDto } from '../dto/update-csat-rule.dto';
import { CsatRuleService } from '../services/csat-rule.service';
import { CsatSettingsService } from '../services/csat-settings.service';

@ApiTags('CSAT')
@SkipAllThrottler()
@Controller('v1/csat')
export class CsatController {
  constructor(
    private readonly csatSettingsService: CsatSettingsService,
    private readonly csatRuleService: CsatRuleService,
    private readonly sharedService: SharedService,
    private readonly transactionService: TransactionService,
  ) { }

  @Get('team/:teamUid')
  @ApiResponseMessage('CSAT settings fetched successfully')
  @ApiGetEndpoint({
    summary: 'Get CSAT settings by team ID',
    responseType: CsatSettingsResponseDto,
  })
  async getSettingsByTeamId(
    @Param('teamUid') teamUid: string,
    @CurrentUser() user: CurrentUser,
  ): Promise<CsatSettingsResponseDto> {
    const team = await this.sharedService.getTeam(teamUid, user.orgId);
    return await this.csatSettingsService.getSettingsByTeamId(team.id);
  }

  @Get('rules/:id')
  @ApiResponseMessage('CSAT rule fetched successfully')
  @ApiGetEndpoint({
    summary: 'Get a CSAT rule by ID',
    responseType: CsatRuleResponseDto,
  })
  async getRule(
    @Param('id') id: string,
  ): Promise<CsatRuleResponseDto> {
    return await this.csatRuleService.getRule(id);
  }

  @Post('rules/:teamUid')
  @ApiOperation({
    summary: 'Create a CSAT rule for a team',
    description: 'Creates a new rule for a team. If CSAT settings do not exist for the team, they will be created automatically.',
  })
  @ApiBody({ type: CreateCsatRuleDto })
  @ApiResponseMessage('CSAT rule created successfully')
  @ApiCreateEndpoint({
    summary: 'Create a CSAT rule',
    responseType: CsatSettingsWithRuleDto,
  })
  async createRule(
    @Param('teamUid') teamUid: string,
    @Body() createRuleDto: CreateCsatRuleDto,
    @CurrentUser() user: CurrentUser,
  ): Promise<CsatSettingsResponseDto> {
    const team = await this.sharedService.getTeam(teamUid, user.orgId);
    return await this.transactionService.runInTransaction(async (txn) => {
      return await this.csatSettingsService.createOrUpdateRule(
        team.id,
        createRuleDto,
        user.sub,
        user.orgId,
        txn,
      );
    });
  }

  @Patch('team/:teamUid/update')
  @ApiResponseMessage('Csat Setting updated successfully')
  @ApiUpdateEndpoint({
    summary: 'Update the Csat setting for a team',
    responseType: CsatSettingsResponseDto,
  })
  async updateCsatSettings(
    @Param('teamUid') teamUid: string,
    @Body() updateCsatSettingDto: { cooldownPeriodDays: number, emailConfigId: string },
    @CurrentUser() user: CurrentUser,
  ): Promise<CsatSettingsResponseDto> {
    const team = await this.sharedService.getTeam(teamUid, user.orgId);
    return await this.csatSettingsService.updateCsatSetting(team.id, updateCsatSettingDto.cooldownPeriodDays, updateCsatSettingDto.emailConfigId, user.orgId);
  }

  @Patch('rules/:id')
  @ApiResponseMessage('CSAT rule updated successfully')
  @ApiUpdateEndpoint({
    summary: 'Update a CSAT rule',
    responseType: CsatRuleResponseDto,
  })
  async updateRule(
    @Param('id') id: string,
    @Body() updateRuleDto: UpdateCsatRuleDto,
    @CurrentUser() user: CurrentUser,
  ): Promise<CsatRuleResponseDto> {
    return await this.transactionService.runInTransaction(async (txn) => {
      return await this.csatRuleService.updateRule(id, updateRuleDto, user.sub, txn);
    });
  }

  @Patch('team/:teamUid/rules/reorder')
  @ApiResponseMessage('CSAT rules reordered successfully')
  @ApiUpdateEndpoint({
    summary: 'Reorder the priority of CSAT rules for a team',
    responseType: CsatSettingsResponseDto,
  })
  async updateRulePriority(
    @Param('teamUid') teamUid: string,
    @Body() reorderRules: Array<{ ruleId: string; priority: number }>,
    @CurrentUser() user: CurrentUser,
  ): Promise<CsatSettingsResponseDto> {
    const team = await this.sharedService.getTeam(teamUid, user.orgId);
    return await this.transactionService.runInTransaction(async (txn) => {
      return await this.csatSettingsService.reorderRules(team.id, reorderRules, txn);
    });
  }

  @Delete('rules/:id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiResponseMessage('CSAT rule deleted successfully')
  @ApiDeleteEndpoint({
    summary: 'Delete a CSAT rule',
  })
  async deleteRule(
    @Param('id') id: string,
    @CurrentUser() user: CurrentUser,
  ): Promise<void> {
    return await this.transactionService.runInTransaction(async (txn) => {
      return await this.csatRuleService.deleteRule(id, user.sub, txn);
    });
  }
}
