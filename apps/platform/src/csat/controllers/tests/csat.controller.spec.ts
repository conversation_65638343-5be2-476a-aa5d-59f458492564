import { NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { Organization, OrganizationTier, Team, TeamConfiguration, TransactionService, UserType } from '@repo/thena-platform-entities';
import { SharedService } from '../../../shared/shared.service';
import { CreateCsatRuleDto } from '../../dto/create-csat-rule.dto';
import { FeedbackTypeEnum } from '../../dto/csat-feedback-config.dto';
import { CsatRuleResponseDto } from '../../dto/csat-rule-response.dto';
import { CsatSettingsResponseDto } from '../../dto/csat-settings-response.dto';
import { TriggerTypeEnum } from '../../dto/csat-trigger-config.dto';
import { UpdateCsatRuleDto } from '../../dto/update-csat-rule.dto';
import { CsatRuleService } from '../../services/csat-rule.service';
import { CsatSettingsService } from '../../services/csat-settings.service';
import { CsatController } from '../csat.controller';

describe('CsatController', () => {
  let controller: CsatController;
  let csatSettingsService: jest.Mocked<CsatSettingsService>;
  let csatRuleService: jest.Mocked<CsatRuleService>;
  let sharedService: jest.Mocked<SharedService>;
  let transactionService: jest.Mocked<TransactionService>;

  const mockUser = {
    sub: 'user123',
    orgId: 'org123',
    userType: UserType.ORG_ADMIN,
    timezone: 'UTC',
    email: '<EMAIL>',
    uid: 'user123',
    orgUid: 'org123',
  };

  const mockTeamConfiguration = {
    id: 'config123',
    teamId: 'team123',
    organizationId: 'org123',
    timezone: 'UTC',
    routingRespectsTimezone: true,
    routingRespectsUserTimezone: true,
    routingRespectsUserAvailability: true,
    routingRespectsUserCapacity: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  } as TeamConfiguration;

  const mockTeam = {
    id: 'team123',
    uid: 'team-uid-123',
    name: 'Test Team',
    icon: 'default',
    color: '#000000',
    identifier: 'test-team',
    createdAt: new Date(),
    updatedAt: new Date(),
    isDeleted: false,
    deletedAt: null,
    deletedBy: null,
    createdBy: 'test-user',
    updatedBy: 'test-user',
    organizationId: 'org123',
    parentTeam: null,
    configuration: mockTeamConfiguration,
    configurationId: 'config123',
    teamOwner: null,
    teamOwnerId: 'user123',
    tags: [],
    isActive: true,
    isPrivate: false,
    teamMembers: [],
    organization: {
      id: 'org123',
      uid: 'org123',
      logoUrl: '',
      domains: [],
      allowSameDomainJoin: false,
      createdAt: new Date(),
      updatedAt: new Date(),
      name: 'Test Org',
      slug: 'test-org',
      isActive: true,
      isVerified: true,
      metadata: {},
      tier: OrganizationTier.FREE,
    } as Organization,
  } as Team;

  beforeEach(async () => {
    const mockCsatSettingsService = {
      getSettingsByTeamId: jest.fn(),
      createOrUpdateRule: jest.fn(),
      updateCsatSetting: jest.fn(),
      reorderRules: jest.fn(),
    };

    const mockCsatRuleService = {
      getRule: jest.fn(),
      updateRule: jest.fn(),
      deleteRule: jest.fn(),
    };

    const mockSharedService = {
      getTeam: jest.fn(),
    };

    const mockTransactionService = {
      runInTransaction: jest.fn((callback) => callback({})),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [CsatController],
      providers: [
        {
          provide: CsatSettingsService,
          useValue: mockCsatSettingsService,
        },
        {
          provide: CsatRuleService,
          useValue: mockCsatRuleService,
        },
        {
          provide: SharedService,
          useValue: mockSharedService,
        },
        {
          provide: TransactionService,
          useValue: mockTransactionService,
        },
      ],
    }).compile();

    controller = module.get<CsatController>(CsatController);
    csatSettingsService = module.get(CsatSettingsService);
    csatRuleService = module.get(CsatRuleService);
    sharedService = module.get(SharedService);
    transactionService = module.get(TransactionService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getSettingsByTeamId', () => {
    it('should return CSAT settings for a team', async () => {
      const mockSettings: CsatSettingsResponseDto = {
        id: 'settings123',
        team: mockTeam,
        cooldownPeriodDays: 7,
        organization: mockTeam.organization,
        isEnabled: true,
        rules: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      sharedService.getTeam.mockResolvedValue(mockTeam);
      csatSettingsService.getSettingsByTeamId.mockResolvedValue(mockSettings);

      const result = await controller.getSettingsByTeamId('team-uid-123', mockUser);

      expect(sharedService.getTeam).toHaveBeenCalledWith('team-uid-123', mockUser.orgId);
      expect(csatSettingsService.getSettingsByTeamId).toHaveBeenCalledWith(mockTeam.id);
      expect(result).toEqual(mockSettings);
    });
  });

  describe('getRule', () => {
    it('should return a CSAT rule by ID', async () => {
      const mockRule: CsatRuleResponseDto = {
        id: 'rule123',
        name: 'Test Rule',
        isActive: true,
        allFilters: {
          ticket: { standardFields: [], customFields: [] },
          account: { standardFields: [], customFields: [] },
          contact: { standardFields: [], customFields: [] },
        },
        anyFilters: {
          ticket: { standardFields: [], customFields: [] },
          account: { standardFields: [], customFields: [] },
          contact: { standardFields: [], customFields: [] },
        },
        triggerConfig: {
          triggerType: TriggerTypeEnum.ALWAYS,
        },
        feedbackConfig: {
          enabled: true,
          feedbackType: FeedbackTypeEnum.STAR,
          includeCommentField: true,
        },
        priority: 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      csatRuleService.getRule.mockResolvedValue(mockRule);

      const result = await controller.getRule('rule123');

      expect(csatRuleService.getRule).toHaveBeenCalledWith('rule123');
      expect(result).toEqual(mockRule);
    });
  });

  describe('createRule', () => {
    it('should create a new CSAT rule', async () => {
      const createRuleDto: CreateCsatRuleDto = {
        name: 'Test Rule',
        isActive: true,
        allFilters: {
          ticket: { standardFields: [], customFields: [] },
          account: { standardFields: [], customFields: [] },
          contact: { standardFields: [], customFields: [] },
        },
        anyFilters: {
          ticket: { standardFields: [], customFields: [] },
          account: { standardFields: [], customFields: [] },
          contact: { standardFields: [], customFields: [] },
        },
        triggerConfig: {
          triggerType: TriggerTypeEnum.ALWAYS,
        },
        feedbackConfig: {
          enabled: true,
          feedbackType: FeedbackTypeEnum.STAR,
          includeCommentField: true,
        },
      };

      const mockResult: CsatSettingsResponseDto = {
        id: 'new-rule-123',
        team: mockTeam,
        cooldownPeriodDays: 7,
        organization: mockTeam.organization,
        isEnabled: true,
        rules: [{
          id: 'new-rule-123',
          name: 'Test Rule',
          isActive: true,
          allFilters: {
            ticket: { standardFields: [], customFields: [] },
            account: { standardFields: [], customFields: [] },
            contact: { standardFields: [], customFields: [] },
          },
          anyFilters: {
            ticket: { standardFields: [], customFields: [] },
            account: { standardFields: [], customFields: [] },
            contact: { standardFields: [], customFields: [] },
          },
          triggerConfig: {
            triggerType: TriggerTypeEnum.ALWAYS,
          },
          feedbackConfig: {
            enabled: true,
            feedbackType: FeedbackTypeEnum.STAR,
            includeCommentField: true,
          },
          priority: 1,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      sharedService.getTeam.mockResolvedValue(mockTeam);
      csatSettingsService.createOrUpdateRule.mockResolvedValue(mockResult);

      const result = await controller.createRule('team-uid-123', createRuleDto, mockUser);

      expect(sharedService.getTeam).toHaveBeenCalledWith('team-uid-123', mockUser.orgId);
      expect(csatSettingsService.createOrUpdateRule).toHaveBeenCalledWith(
        mockTeam.id,
        createRuleDto,
        mockUser.sub,
        mockUser.orgId,
        expect.anything(),
      );
      expect(result).toEqual(mockResult);
    });
  });

  describe('updateCsatSettings', () => {
    it('should update CSAT settings for a team', async () => {
      const updateDto = {
        cooldownPeriodDays: 7,
        emailConfigId: 'email123',
      };

      const mockResult: CsatSettingsResponseDto = {
        id: 'updated-settings-123',
        team: mockTeam,
        cooldownPeriodDays: 7,
        organization: mockTeam.organization,
        isEnabled: true,
        emailConfigId: 'email123',
        rules: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      sharedService.getTeam.mockResolvedValue(mockTeam);
      csatSettingsService.updateCsatSetting.mockResolvedValue(mockResult);

      const result = await controller.updateCsatSettings('team-uid-123', updateDto, mockUser);

      expect(sharedService.getTeam).toHaveBeenCalledWith('team-uid-123', mockUser.orgId);
      expect(csatSettingsService.updateCsatSetting).toHaveBeenCalledWith(
        mockTeam.id,
        updateDto.cooldownPeriodDays,
        updateDto.emailConfigId,
        mockUser.orgId,
      );
      expect(result).toEqual(mockResult);
    });
  });

  describe('updateRule', () => {
    it('should update a CSAT rule', async () => {
      const updateRuleDto: UpdateCsatRuleDto = {
        name: 'Updated Rule',
      };

      const mockResult: CsatRuleResponseDto = {
        id: 'updated-rule-123',
        name: 'Updated Rule',
        isActive: true,
        allFilters: {
          ticket: { standardFields: [], customFields: [] },
          account: { standardFields: [], customFields: [] },
          contact: { standardFields: [], customFields: [] },
        },
        anyFilters: {
          ticket: { standardFields: [], customFields: [] },
          account: { standardFields: [], customFields: [] },
          contact: { standardFields: [], customFields: [] },
        },
        triggerConfig: {
          triggerType: TriggerTypeEnum.ALWAYS,
        },
        feedbackConfig: {
          enabled: true,
          feedbackType: FeedbackTypeEnum.STAR,
          includeCommentField: true,
        },
        priority: 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      csatRuleService.updateRule.mockResolvedValue(mockResult);

      const result = await controller.updateRule('rule123', updateRuleDto, mockUser);

      expect(csatRuleService.updateRule).toHaveBeenCalledWith(
        'rule123',
        updateRuleDto,
        mockUser.sub,
        expect.anything(),
      );
      expect(result).toEqual(mockResult);
    });
  });

  describe('updateRulePriority', () => {
    it('should reorder CSAT rules', async () => {
      const reorderRules = [
        { ruleId: 'rule1', priority: 1 },
        { ruleId: 'rule2', priority: 2 },
      ];

      const mockResult: CsatSettingsResponseDto = {
        id: 'reordered-rules-123',
        team: mockTeam,
        cooldownPeriodDays: 7,
        organization: mockTeam.organization,
        isEnabled: true,
        rules: [
          {
            id: 'rule1',
            name: 'Rule 1',
            isActive: true,
            allFilters: {
              ticket: { standardFields: [], customFields: [] },
              account: { standardFields: [], customFields: [] },
              contact: { standardFields: [], customFields: [] },
            },
            anyFilters: {
              ticket: { standardFields: [], customFields: [] },
              account: { standardFields: [], customFields: [] },
              contact: { standardFields: [], customFields: [] },
            },
            triggerConfig: {
              triggerType: TriggerTypeEnum.ALWAYS,
            },
            feedbackConfig: {
              enabled: true,
              feedbackType: FeedbackTypeEnum.STAR,
              includeCommentField: true,
            },
            priority: 1,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
          {
            id: 'rule2',
            name: 'Rule 2',
            isActive: true,
            allFilters: {
              ticket: { standardFields: [], customFields: [] },
              account: { standardFields: [], customFields: [] },
              contact: { standardFields: [], customFields: [] },
            },
            anyFilters: {
              ticket: { standardFields: [], customFields: [] },
              account: { standardFields: [], customFields: [] },
              contact: { standardFields: [], customFields: [] },
            },
            triggerConfig: {
              triggerType: TriggerTypeEnum.ALWAYS,
            },
            feedbackConfig: {
              enabled: true,
              feedbackType: FeedbackTypeEnum.STAR,
              includeCommentField: true,
            },
            priority: 2,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
        ],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      sharedService.getTeam.mockResolvedValue(mockTeam);
      csatSettingsService.reorderRules.mockResolvedValue(mockResult);

      const result = await controller.updateRulePriority('team-uid-123', reorderRules, mockUser);

      expect(sharedService.getTeam).toHaveBeenCalledWith('team-uid-123', mockUser.orgId);
      expect(csatSettingsService.reorderRules).toHaveBeenCalledWith(
        mockTeam.id,
        reorderRules,
        expect.anything(),
      );
      expect(result).toEqual(mockResult);
    });
  });

  describe('deleteRule', () => {
    it('should delete a CSAT rule', async () => {
      csatRuleService.deleteRule.mockResolvedValue(undefined);

      await controller.deleteRule('rule123', mockUser);

      expect(csatRuleService.deleteRule).toHaveBeenCalledWith(
        'rule123',
        mockUser.sub,
        expect.anything(),
      );
    });
  });

  describe('Error handling and edge cases', () => {
    it('should handle team not found error', async () => {
      sharedService.getTeam.mockRejectedValue(new NotFoundException('Team not found'));

      await expect(controller.getSettingsByTeamId('non-existent-team', mockUser))
        .rejects.toThrow(NotFoundException);
    });

    it('should handle transaction rollback on rule creation failure', async () => {
      const createRuleDto: CreateCsatRuleDto = {
        name: 'Test Rule',
        isActive: true,
        allFilters: {
          ticket: { standardFields: [], customFields: [] },
          account: { standardFields: [], customFields: [] },
          contact: { standardFields: [], customFields: [] },
        },
        anyFilters: {
          ticket: { standardFields: [], customFields: [] },
          account: { standardFields: [], customFields: [] },
          contact: { standardFields: [], customFields: [] },
        },
        triggerConfig: {
          triggerType: TriggerTypeEnum.ALWAYS,
        },
        feedbackConfig: {
          enabled: true,
          feedbackType: FeedbackTypeEnum.STAR,
          includeCommentField: true,
        },
      };

      sharedService.getTeam.mockResolvedValue(mockTeam);

      // Simulate transaction failure
      transactionService.runInTransaction.mockImplementation((_callback) => {
        throw new Error('Transaction failed');
      });

      await expect(controller.createRule('team-uid-123', createRuleDto, mockUser))
        .rejects.toThrow('Transaction failed');
    });

    it('should handle partial update of CSAT settings', async () => {
      const updateDto = {
        cooldownPeriodDays: 14, // Only updating cooldown, not emailConfigId
        emailConfigId: undefined,
      };

      const mockResult: CsatSettingsResponseDto = {
        id: 'settings123',
        team: mockTeam,
        cooldownPeriodDays: 14,
        organization: mockTeam.organization,
        isEnabled: true,
        rules: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      sharedService.getTeam.mockResolvedValue(mockTeam);
      csatSettingsService.updateCsatSetting.mockResolvedValue(mockResult);

      const result = await controller.updateCsatSettings('team-uid-123', updateDto, mockUser);

      expect(csatSettingsService.updateCsatSetting).toHaveBeenCalledWith(
        mockTeam.id,
        14,
        undefined,
        mockUser.orgId
      );
      expect(result.cooldownPeriodDays).toBe(14);
    });

    it('should handle reordering with duplicate priorities', async () => {
      const reorderRules = [
        { ruleId: 'rule1', priority: 1 },
        { ruleId: 'rule2', priority: 1 }, // Duplicate priority
        { ruleId: 'rule3', priority: 2 },
      ];

      sharedService.getTeam.mockResolvedValue(mockTeam);

      // The service should handle this gracefully
      const mockResult: CsatSettingsResponseDto = {
        id: 'settings123',
        team: mockTeam,
        cooldownPeriodDays: 7,
        organization: mockTeam.organization,
        isEnabled: true,
        rules: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      csatSettingsService.reorderRules.mockResolvedValue(mockResult);

      const _result = await controller.updateRulePriority('team-uid-123', reorderRules, mockUser);

      expect(csatSettingsService.reorderRules).toHaveBeenCalledWith(
        mockTeam.id,
        reorderRules,
        expect.anything()
      );
    });

    it('should handle authorization errors', async () => {
      const unauthorizedUser = {
        ...mockUser,
        orgId: 'different-org',
      };

      sharedService.getTeam.mockRejectedValue(new Error('Unauthorized'));

      await expect(controller.getSettingsByTeamId('team-uid-123', unauthorizedUser))
        .rejects.toThrow('Unauthorized');
    });

    it('should handle concurrent rule updates', async () => {
      const updateRuleDto1: UpdateCsatRuleDto = { name: 'Updated Rule 1' };
      const updateRuleDto2: UpdateCsatRuleDto = { name: 'Updated Rule 2' };

      const mockResult: CsatRuleResponseDto = {
        id: 'rule123',
        name: 'Updated Rule 2', // Last update wins
        isActive: true,
        allFilters: {
          ticket: { standardFields: [], customFields: [] },
          account: { standardFields: [], customFields: [] },
          contact: { standardFields: [], customFields: [] },
        },
        anyFilters: {
          ticket: { standardFields: [], customFields: [] },
          account: { standardFields: [], customFields: [] },
          contact: { standardFields: [], customFields: [] },
        },
        triggerConfig: {
          triggerType: TriggerTypeEnum.ALWAYS,
        },
        feedbackConfig: {
          enabled: true,
          feedbackType: FeedbackTypeEnum.STAR,
          includeCommentField: true,
        },
        priority: 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      csatRuleService.updateRule.mockResolvedValue(mockResult);

      // Simulate concurrent updates
      const [result1, result2] = await Promise.all([
        controller.updateRule('rule123', updateRuleDto1, mockUser),
        controller.updateRule('rule123', updateRuleDto2, mockUser),
      ]);

      expect(csatRuleService.updateRule).toHaveBeenCalledTimes(2);
      // Both should return the same result (last update wins)
      expect(result1.name).toBe('Updated Rule 2');
      expect(result2.name).toBe('Updated Rule 2');
    });
  });
});
