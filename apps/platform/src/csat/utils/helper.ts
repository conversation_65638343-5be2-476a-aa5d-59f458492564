/**
 * Utility functions for handling entity IDs and relations
 */

/**
 * Safely converts a value to bigint for use with TypeORM queries
 * Handles string, number, or bigint inputs
 */
export function toBigInt(value: string | number | bigint | undefined | null): bigint | undefined {
  if (value === undefined || value === null) {
    return undefined;
  }

  if (typeof value === 'bigint') {
    return value;
  }

  return BigInt(value);
}

/**
 * Safely converts a value to string ID format for entity references
 * Handles string, number, or bigint inputs
 * Use this when creating entity references like { id: toIdString(someId) }
 */
export function toIdString(value: string | number | bigint | undefined | null): string | undefined {
  if (value === undefined || value === null) {
    return undefined;
  }

  return value.toString();
}

/**
 * Converts a Team entity to a DTO object with string ID
 */
export function teamToDto(team: any): { id: string; name?: string } {
  if (!team) return null;

  return {
    id: team.id ? team.id.toString() : null,
    ...(team.name ? { name: team.name } : {}),
  };
}

/**
 * Converts an Organization entity to a DTO object with string ID
 */
export function organizationToDto(org: any): { id: string; name?: string } {
  if (!org) return null;

  return {
    id: org.id ? org.id.toString() : null,
    ...(org.name ? { name: org.name } : {}),
  };
}

/**
 * Creates a Team entity reference with a string ID
 */
export function createTeamReference(teamId: string | number | bigint): { id: string } {
  return { id: toIdString(teamId) };
}

/**
 * Creates an Organization entity reference with a string ID
 */
export function createOrgReference(orgId: string | number | bigint): { id: string } {
  return { id: toIdString(orgId) };
}

/**
 * Creates a User entity reference with a string ID
 */
export function createUserReference(userId: string | number | bigint): { id: string } {
  if (!userId) return null;
  return { id: toIdString(userId) };
}
