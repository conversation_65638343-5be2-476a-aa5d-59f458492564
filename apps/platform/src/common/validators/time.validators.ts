import { BusinessSlot } from "@repo/thena-platform-entities";
import {
  registerDecorator,
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from "class-validator";

export const IS_TIMEZONE_OR_EMPTY = "isTimezoneOrEmpty";

@ValidatorConstraint({ name: "hasAllBusinessDays", async: false })
export class HasAllBusinessDaysConstraint
  implements ValidatorConstraintInterface
{
  private readonly requiredDays = [
    "monday",
    "tuesday",
    "wednesday",
    "thursday",
    "friday",
    "saturday",
    "sunday",
  ];

  validate(value: any): boolean {
    if (!value) return true; // Optional check
    if (typeof value !== "object") return false;

    return this.requiredDays.every((day) =>
      Object.hasOwnProperty.call(value, day),
    );
  }

  defaultMessage(args: ValidationArguments): string {
    const missingDays = this.requiredDays.filter(
      (day) => !Object.hasOwnProperty.call(args.value || {}, day),
    );

    return `Business hours must include all days of the week. Missing: ${missingDays.join(
      ", ",
    )}`;
  }
}

@ValidatorConstraint({ name: "isTimeFormat", async: false })
export class IsTimeFormatConstraint implements ValidatorConstraintInterface {
  validate(value: any): Promise<boolean> | boolean {
    if (typeof value !== "string") return false;
    return /^([01]\d|2[0-3]):([0-5]\d)$/.test(value);
  }

  defaultMessage(): string {
    return "Time must be in the format (HH:mm)";
  }
}

@ValidatorConstraint({ name: "isValidTimeSlot", async: false })
export class IsValidTimeSlotConstraint implements ValidatorConstraintInterface {
  validate(slots: BusinessSlot[], args: ValidationArguments): boolean {
    const businessDay = args.object as any;

    // If the day is inactive, allow undefined/null/empty slots
    if (!businessDay.isActive) {
      return true;
    }

    // If the day is active, there must be at least one slot
    if (businessDay.isActive) {
      // If the slots are not an array, return false
      if (!Array.isArray(slots)) return false;

      // If the slots array is empty, return false
      if (slots.length === 0) return false;
    }

    if (!Array.isArray(slots)) return false;
    if (slots.length === 0) return true; // Empty array is valid for inactive days

    return slots.every((slot) => {
      if (!slot || !slot.start || !slot.end) return false;
      const start = this.parseTime(slot.start);
      const end = this.parseTime(slot.end);
      if (start >= end) return false;

      // Check for overlapping slots
      return slots.every((otherSlot) => {
        if (slot === otherSlot) return true;
        const otherStart = this.parseTime(otherSlot.start);
        const otherEnd = this.parseTime(otherSlot.end);
        return start >= otherEnd || end <= otherStart;
      });
    });
  }

  private parseTime(time: string): number {
    if (!/^([01]\d|2[0-3]):([0-5]\d)$/.test(time)) return -1;
    const [hours, minutes] = time.split(":").map(Number);
    return hours * 60 + minutes;
  }

  defaultMessage(): string {
    return "Time slots must be valid and non-overlapping, with start time before end time";
  }
}
@ValidatorConstraint({ name: "validateActiveDay", async: false })
export class ValidateActiveDayConstraint
  implements ValidatorConstraintInterface
{
  validate(slot: any, args: ValidationArguments): boolean {
    const businessDay = args.object as any;

    if (businessDay.isActive) {
      return !!slot; // Must have a slot if active
    }

    return !slot; // Must not have a slot if inactive
  }

  defaultMessage(args: ValidationArguments): string {
    const businessDay = args.object as any;
    if (businessDay.isActive && !args.value) {
      return "Active business days must have a time slot";
    }
    if (!businessDay.isActive && args.value) {
      return "Inactive business days should not have a time slot";
    }
    return "Invalid business day configuration";
  }
}

export function IsAfterDate(
  property: string,
  validationOptions?: ValidationOptions,
) {
  return function (object: Record<string, any>, propertyName: string) {
    registerDecorator({
      name: "isAfterDate",
      target: object.constructor,
      propertyName: propertyName,
      constraints: [property],
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          const [relatedPropertyName] = args.constraints;
          const relatedValue = (args.object as any)[relatedPropertyName];

          if (!value || !relatedValue) return false;

          const compareDate = new Date(value);
          const relatedDate = new Date(relatedValue);

          if (isNaN(compareDate.getTime()) || isNaN(relatedDate.getTime())) {
            return false;
          }

          return compareDate >= relatedDate;
        },
        defaultMessage(args: ValidationArguments) {
          const [relatedPropertyName] = args.constraints;
          return `${propertyName} must not be before ${relatedPropertyName}`;
        },
      },
    });
  };
}

export function IsTimezoneOrEmpty(validationOptions?: ValidationOptions) {
  return function (object: Record<string, any>, propertyName: string) {
    registerDecorator({
      name: IS_TIMEZONE_OR_EMPTY,
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any) {
          // Allow empty string
          if (value === "UNSET") return true;

          try {
            // Test if it's a valid IANA timezone
            Intl.DateTimeFormat(undefined, { timeZone: value });
            return true;
          } catch (_error) {
            return false;
          }
        },
        defaultMessage() {
          return "Value must be either a valid IANA timezone or an empty string";
        },
      },
    });
  };
}
