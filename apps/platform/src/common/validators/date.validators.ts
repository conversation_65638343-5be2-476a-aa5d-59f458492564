import {
  registerDecorator,
  ValidationArguments,
  ValidationOptions,
} from "class-validator";

export const IS_DATE_DDMM = "isDateDDMM";
export const IS_DATE_DDMMYYYY = "isDateDDMMYYYY";
export const IS_DATE_DDMM_OR_DDMMYYYY = "isDateDDMMOrDDMMYYYY";

/**
 * Validates that the value is a valid date in DD-MM format
 * @param validationOptions The validation options
 * @returns A decorator function
 */
export function IsDateDDMM(validationOptions?: ValidationOptions) {
  return function (object: Record<string, any>, propertyName: string) {
    registerDecorator({
      name: IS_DATE_DDMM,
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate<T extends string>(value: T) {
          if (typeof value !== "string") return false;

          // Regular expression for DD-MM format
          const ddmmRegex = /^(0[1-9]|[12]\d|3[01])-(0[1-9]|1[0-2])$/;

          if (!ddmmRegex.test(value)) return false;

          // Extract day and month
          const [day, month] = value.split("-").map(Number);

          // Check for valid days in each month
          const daysInMonth = new Date(2024, month - 1, 0).getDate();
          return day <= daysInMonth;
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} must be a valid date in DD-MM format`;
        },
      },
    });
  };
}

/**
 * Validates that the value is a valid date in DD-MM-YYYY format
 * @param validationOptions The validation options
 * @returns A decorator function
 */
export function IsDateDDMMYYYY(validationOptions?: ValidationOptions) {
  return function (object: Record<string, any>, propertyName: string) {
    registerDecorator({
      name: IS_DATE_DDMMYYYY,
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate<T extends string>(value: T) {
          if (typeof value !== "string") return false;

          // Regular expression for DD-MM-YYYY format
          const ddmmyyyyRegex = /^(0[1-9]|[12]\d|3[01])-(0[1-9]|1[0-2])-\d{4}$/;

          if (!ddmmyyyyRegex.test(value)) return false;

          // Extract day, month, and year
          const [day, month, year] = value.split("-").map(Number);

          // Create Date object and verify it's valid
          const date = new Date(year, month - 1, day);
          return (
            date.getDate() === day &&
            date.getMonth() === month - 1 &&
            date.getFullYear() === year
          );
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} must be a valid date in DD-MM-YYYY format`;
        },
      },
    });
  };
}

/**
 * Validates that the value is a valid date in DD-MM or DD-MM-YYYY format
 * @param validationOptions The validation options
 * @returns A decorator function
 */
export function IsDateDDMMOrDDMMYYYY(validationOptions?: ValidationOptions) {
  return function (object: Record<string, any>, propertyName: string) {
    registerDecorator({
      name: IS_DATE_DDMM_OR_DDMMYYYY,
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate<T extends string>(value: T) {
          if (typeof value !== "string") return false;

          // Regular expressions for both formats
          const ddmmRegex = /^(0[1-9]|[12]\d|3[01])-(0[1-9]|1[0-2])$/;
          const ddmmyyyyRegex = /^(0[1-9]|[12]\d|3[01])-(0[1-9]|1[0-2])-\d{4}$/;

          // Check if the value matches either format
          const isDDMM = ddmmRegex.test(value);
          const isDDMMYYYY = ddmmyyyyRegex.test(value);

          if (!isDDMM && !isDDMMYYYY) return false;

          const parts = value.split("-").map(Number);
          const day = parts[0];
          const month = parts[1];
          const year = parts[2] || new Date().getFullYear(); // Use current year for DD-MM format

          // Validate the date
          const date = new Date(year, month - 1, day);
          return (
            date.getDate() === day &&
            date.getMonth() === month - 1 &&
            (parts.length === 2 || date.getFullYear() === year)
          );
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} must be a valid date in either DD-MM or DD-MM-YYYY format`;
        },
      },
    });
  };
}
