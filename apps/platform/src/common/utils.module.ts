import { Global, Module } from "@nestjs/common";
import { AUTH_GRPC_SERVICE_URL_TOKEN } from "@repo/nestjs-commons/guards";
import { ConfigModule } from "../config/config.module";
import { ConfigKeys, ConfigService } from "../config/config.service";
import * as utils from "./utils";

@Global()
@Module({
  imports: [ConfigModule],
  providers: [
    ...Object.values(utils),
    {
      provide: AUTH_GRPC_SERVICE_URL_TOKEN,
      useFactory: (configService: ConfigService) =>
        configService.get(ConfigKeys.AUTH_GRPC_URL),
      inject: [ConfigService],
    },
  ],
  exports: [...Object.values(utils), AUTH_GRPC_SERVICE_URL_TOKEN],
})
export class UtilsModule {}
