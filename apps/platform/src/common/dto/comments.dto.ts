import { ApiPropertyOptional } from "@nestjs/swagger";
import { CommentType, CommentVisibility } from "@repo/thena-platform-entities";
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsObject,
  IsOptional,
  IsString,
  Max<PERSON>ength,
} from "class-validator";

export class CreateCommentDto {
  @IsString()
  @IsOptional()
  @MaxLength(5000)
  @ApiPropertyOptional({ description: "The content of the comment" })
  content?: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: "The HTML content of the comment" })
  contentHtml?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ description: "The JSON content of the comment" })
  contentJson?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ description: "The name of the comment thread" })
  threadName?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ description: "The parent comment ID" })
  parentCommentId?: string;

  @IsOptional()
  @IsEnum(CommentVisibility)
  @ApiPropertyOptional({ description: "The visibility of the comment" })
  commentVisibility?: CommentVisibility;

  @IsOptional()
  @IsEnum([CommentType.COMMENT, CommentType.NOTE])
  @ApiPropertyOptional({ description: "The type of the comment" })
  commentType?: CommentType;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ description: "The customer email of the comment" })
  customerEmail?: string;

  @IsOptional()
  @IsObject()
  @ApiPropertyOptional({ description: "The metadata of the comment" })
  metadata?: Record<string, any>;

  @IsArray()
  @IsOptional()
  @ApiPropertyOptional({ description: "The attachment IDs" })
  attachmentIds?: string[];

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ description: "The impersonated user email" })
  impersonatedUserEmail?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ description: "The impersonated user name" })
  impersonatedUserName?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ description: "The impersonated user avatar" })
  impersonatedUserAvatar?: string;

  @IsOptional()
  @IsBoolean()
  @ApiPropertyOptional({ description: "Whether to send an email" })
  shouldSendEmail?: boolean;

  // This is only an internal flag to skip SNS event when comment is created during createTicket api
  // Should not be exposed in the swagger
  @IsOptional()
  @IsBoolean()
  skipSnsEvent?: boolean;
}
