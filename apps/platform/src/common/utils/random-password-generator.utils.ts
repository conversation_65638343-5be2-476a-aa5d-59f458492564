export function generateRandomPassword(length: number = 16): string {
  // Define character sets
  const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  const lowercase = "abcdefghijklmnopqrstuvwxyz";
  const numbers = "0123456789";
  const special = "!@#$%^&*()_+-=[]{}|;:,.<>?";

  const allChars = uppercase + lowercase + numbers + special;

  // Generate crypto-safe random values
  const array = new Uint8Array(length);
  crypto.getRandomValues(array);

  // Ensure at least one of each character type
  let password =
    uppercase[array[0] % uppercase.length] +
    lowercase[array[1] % lowercase.length] +
    numbers[array[2] % numbers.length] +
    special[array[3] % special.length];

  // Fill remaining length with random characters
  for (let i = 4; i < length; i++) {
    password += allChars[array[i] % allChars.length];
  }

  // Shuffle the password to avoid predictable character positions
  return password
    .split("")
    .sort(() => crypto.getRandomValues(new Uint8Array(1))[0] - 128)
    .join("");
}
