import { parse } from "tldts";

export const extractEmailDetails = (email: string) => {
  const { domain, ...rest } = parse(email);
  return { domain, ...rest };
};

export const extractNameFromEmail = (
  email: string,
): { firstName: string; lastName: string } => {
  if (!email || typeof email !== "string") {
    return { firstName: "", lastName: "" };
  }

  // Get the local part (before @)
  const localPart = email.split("@")[0];

  if (!localPart) {
    return { firstName: "", lastName: "" };
  }

  // Common separators in email names
  const separators = [".", "_", "-"];

  // Replace common separators with spaces
  let name = localPart;
  separators.forEach((separator) => {
    name = name.replace(new RegExp("\\" + separator, "g"), " ");
  });

  // Capitalize first letter of each word
  name = name
    .toLowerCase()
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");

  // Handle special cases
  name = name
    .replace(/\d+/g, "") // Remove numbers
    .replace(/\s+/g, " ") // Remove extra spaces
    .trim();

  const firstName = name.split(" ")[0];
  const lastName = name.split(" ").slice(1).join(" ");

  return { firstName, lastName };
};

export const extractNameFromDomain = (domain: string): string => {
  if (!domain || typeof domain !== "string") {
    return "";
  }

  // Extract the domain name without TLD
  const domainParts = domain.split(".");
  if (domainParts.length < 2) {
    return "";
  }

  const domainName = domainParts[0];

  // Capitalize the first letter
  return domainName.charAt(0).toUpperCase() + domainName.slice(1);
};
