export function generateIdentifier(name: string, length: number = 3): string {
  if (!name?.trim()) {
    throw new Error("Name cannot be empty or null");
  }

  if (length <= 0) {
    throw new Error("Length must be a positive number");
  }

  // Split the string into words and filter out empty strings
  const words = name
    .normalize("NFKD")
    .replace(/[^\w\s-_]/g, "")
    .split(/[\s-_]+/)
    .filter((word) => word.length > 0)
    .map((word) => word.toUpperCase());

  // If it's a single word, take first {length} characters
  if (words.length === 1) {
    return words[0].slice(0, length);
  }

  // For multiple words
  if (words.length >= length) {
    // Take first letter of first {length} words
    return words
      .slice(0, length)
      .map((word) => word[0])
      .join("");
  }

  // If we have fewer words than desired length
  let identifier = words.map((word) => word[0]).join(""); // First get all first letters

  // Fill remaining length with characters from the last word
  const remaining = length - identifier.length;
  if (remaining > 0) {
    const lastWord = words[words.length - 1];
    const availableChars = lastWord.slice(1);
    identifier += availableChars.padEnd(
      remaining,
      lastWord[lastWord.length - 1],
    );
  }

  return identifier;
}
