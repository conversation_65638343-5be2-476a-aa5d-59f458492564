import { BusinessHoursConfigDto, BusinessSlotDto } from "../dto";

export function constructDailyConfigFromCommonSlots(
  commonSlots: BusinessSlotDto[],
): BusinessHoursConfigDto {
  const dailyConfig: BusinessHoursConfigDto = {
    monday: { isActive: true, slots: commonSlots },
    tuesday: { isActive: true, slots: commonSlots },
    wednesday: { isActive: true, slots: commonSlots },
    thursday: { isActive: true, slots: commonSlots },
    friday: { isActive: true, slots: commonSlots },
    saturday: { isActive: true, slots: commonSlots },
    sunday: { isActive: true, slots: commonSlots },
  };

  return dailyConfig;
}
