import { ApiProperty } from "@nestjs/swagger";
import { Expose } from "class-transformer";

export class ResponseMessage {
  @ApiProperty({ default: true, description: "The status of the response" })
  @Expose()
  status: boolean;

  @ApiProperty({
    default: "Success",
    description: "The message of the response",
  })
  @Expose()
  message: string;

  @ApiProperty({
    default: "2024-01-01T00:00:00.000Z",
    description: "The timestamp of the response",
  })
  @Expose()
  timestamp: Date;
}
