import { randomBytes } from "crypto";

export class IdGeneratorUtils {
  private static readonly DEFAULT_LENGTH = 10;
  private static readonly ALPHABET = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";

  /**
   * Generates a random string of the specified length.
   * @param prefix The prefix to prepend to the generated string.
   * @param length The length of the generated string.
   * @returns A random string of the specified length.
   */
  static generate(
    prefix: string,
    length: number = IdGeneratorUtils.DEFAULT_LENGTH,
  ): string {
    const randomBytesLength = Math.ceil(((length - 1) * 6) / 8);
    const randomBytesBuffer = randomBytes(randomBytesLength);

    let id = prefix;
    for (let i = 0; i < length - 1; i++) {
      const randomIndex =
        randomBytesBuffer.readUInt8(Math.floor((i * 6) / 8)) %
        IdGeneratorUtils.ALPHABET.length;
      id += IdGeneratorUtils.ALPHABET[randomIndex];
    }

    return id;
  }
}
