import { Injectable } from "@nestjs/common";
import { InjectDataSource } from "@nestjs/typeorm";
import { cloneDeep } from "lodash";
import { DataSource } from "typeorm";

@Injectable()
export class FieldMetadataService {
  constructor(
    @InjectDataSource()
    private readonly dataSource: DataSource,
  ) {}

  getFieldMetadata(entity: any) {
    const metadata = this.dataSource.getMetadata(entity);
    const fields: Record<string, any> = {};

    metadata.columns.forEach((column) => {
      const fieldMetadataReflect = cloneDeep(
        Reflect.getMetadata(
          "fieldMetadata",
          entity.prototype,
          column.propertyName,
        ),
      );

      if (fieldMetadataReflect) {
        fieldMetadataReflect.fieldName = column.propertyName;
        fields[fieldMetadataReflect.expression] = fieldMetadataReflect;
      }
    });

    metadata.relations.forEach((relation) => {
      const fieldMetadataReflect = cloneDeep(
        Reflect.getMetadata(
          "fieldMetadata",
          entity.prototype,
          relation.propertyName,
        ),
      );

      if (fieldMetadataReflect) {
        fieldMetadataReflect.fieldName = relation.propertyName;
        fields[fieldMetadataReflect.expression] = fieldMetadataReflect;
      }
    });

    return { fields };
  }
}
