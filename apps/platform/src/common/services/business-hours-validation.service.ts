import { Injectable } from "@nestjs/common";
import { DateTime } from "luxon";
import {
  BusinessDayDto,
  BusinessHoursConfigDto,
  BusinessSlotDto,
} from "../dto";

@Injectable()
export class BusinessHoursValidatorService {
  validateBusinessHours(
    businessHours: BusinessHoursConfigDto,
    timezone: string,
  ): { isValid: boolean; error?: string } {
    try {
      // Validate individual days first (no timezone needed)
      for (const [day, config] of Object.entries(businessHours)) {
        const dayValidation = this.validateBusinessDay(config, day);
        if (!dayValidation.isValid) {
          return dayValidation;
        }
      }

      // Then validate all days together for timezone-based overlaps
      const allSlotsInUTC = this.getAllSlotsInUTC(businessHours, timezone);
      const overlapValidation = this.validateNoOverlapsInUTC(allSlotsInUTC);
      if (!overlapValidation.isValid) {
        return overlapValidation;
      }

      return { isValid: true };
    } catch (error) {
      return {
        isValid: false,
        error: `Invalid business hours configuration: ${error.message}`,
      };
    }
  }

  private validateBusinessDay(
    dayConfig: BusinessDayDto,
    dayName: string,
  ): { isValid: boolean; error?: string } {
    if (!dayConfig) {
      return { isValid: false, error: `Invalid configuration for ${dayName}` };
    }

    if (dayConfig.isActive) {
      if (
        !dayConfig.slots ||
        !Array.isArray(dayConfig.slots) ||
        dayConfig.slots.length === 0
      ) {
        return {
          isValid: false,
          error: `Active day ${dayName} must have at least one time slot`,
        };
      }

      // Validate each slot's time format
      for (const slot of dayConfig.slots) {
        if (
          !this.isValidTimeFormat(slot.start) ||
          !this.isValidTimeFormat(slot.end)
        ) {
          return {
            isValid: false,
            error: `Invalid time format for ${dayName}. Use HH:mm format`,
          };
        }
      }

      // Validate slots don't overlap within the same day
      const slotsValidation = this.validateDaySlots(dayConfig.slots, dayName);
      if (!slotsValidation.isValid) {
        return slotsValidation;
      }
    } else if (dayConfig.slots && dayConfig.slots.length > 0) {
      return {
        isValid: false,
        error: `Inactive day ${dayName} should not have time slots`,
      };
    }

    return { isValid: true };
  }

  private validateDaySlots(
    slots: BusinessSlotDto[],
    dayName: string,
  ): { isValid: boolean; error?: string } {
    // Sort slots by start time
    const sortedSlots = [...slots].sort((a, b) => {
      const startA = this.parseTimeToMinutes(a.start);
      const startB = this.parseTimeToMinutes(b.start);
      return startA - startB;
    });

    // Check each slot's validity and overlaps
    for (let i = 0; i < sortedSlots.length; i++) {
      const currentSlot = sortedSlots[i];
      const startMinutes = this.parseTimeToMinutes(currentSlot.start);
      const endMinutes = this.parseTimeToMinutes(currentSlot.end);

      // Check if slot spans more than 24 hours
      let duration = endMinutes - startMinutes;
      if (endMinutes <= startMinutes) {
        // Crossing midnight
        duration = 24 * 60 - startMinutes + endMinutes;
      }

      if (duration <= 0 || duration > 24 * 60) {
        return {
          isValid: false,
          error: `Invalid duration for slot in ${dayName}: ${currentSlot.start}-${currentSlot.end}`,
        };
      }

      // Check overlap with next slot
      if (i < sortedSlots.length - 1) {
        const nextSlot = sortedSlots[i + 1];
        const nextStartMinutes = this.parseTimeToMinutes(nextSlot.start);

        // For same-day validation, we just need to check if the end time is after the next start time
        if (endMinutes > nextStartMinutes) {
          return {
            isValid: false,
            error: `Overlapping slots in ${dayName}: ${currentSlot.start}-${currentSlot.end} and ${nextSlot.start}-${nextSlot.end}`,
          };
        }
      }
    }

    return { isValid: true };
  }

  private parseTimeToMinutes(time: string): number {
    const [hours, minutes] = time.split(":").map(Number);
    return hours * 60 + minutes;
  }

  private isValidTimeFormat(time: string): boolean {
    return /^([01]\d|2[0-3]):([0-5]\d)$/.test(time);
  }
  private getAllSlotsInUTC(
    businessHours: BusinessHoursConfigDto,
    timezone: string,
  ) {
    const slots: Array<{
      day: string;
      utcStart: DateTime;
      utcEnd: DateTime;
    }> = [];

    const days = [
      "monday",
      "tuesday",
      "wednesday",
      "thursday",
      "friday",
      "saturday",
      "sunday",
    ];

    days.forEach((day, index) => {
      const businessDay = businessHours[day];
      if (businessDay?.isActive && businessDay.slots) {
        const baseDate = DateTime.now()
          .setZone(timezone)
          .startOf("week")
          .plus({ days: index });

        // Add each slot for the day
        businessDay.slots.forEach((slot) => {
          const startDt = this.parseTimeInTimezone(
            slot.start,
            timezone,
            baseDate,
          );
          const endDt = this.parseTimeInTimezone(slot.end, timezone, baseDate);

          // Handle slots crossing midnight
          let adjustedEndDt = endDt;
          if (endDt < startDt) {
            adjustedEndDt = endDt.plus({ days: 1 });
          }

          slots.push({
            day,
            utcStart: startDt.toUTC(),
            utcEnd: adjustedEndDt.toUTC(),
          });
        });
      }
    });

    return slots.sort((a, b) => a.utcStart.toMillis() - b.utcStart.toMillis());
  }

  private validateNoOverlapsInUTC(
    slots: Array<{
      day: string;
      utcStart: DateTime;
      utcEnd: DateTime;
    }>,
  ): { isValid: boolean; error?: string } {
    for (let i = 0; i < slots.length - 1; i++) {
      const current = slots[i];
      const next = slots[i + 1];

      if (current.utcEnd > next.utcStart) {
        return {
          isValid: false,
          error: `Business hours overlap detected between ${current.day} and ${next.day} in UTC`,
        };
      }
    }

    return { isValid: true };
  }

  private parseTimeInMinutes(time: string): number {
    const [hours, minutes] = time.split(":").map(Number);
    return hours * 60 + minutes;
  }

  private parseTimeInTimezone(
    time: string,
    timezone: string,
    baseDate?: DateTime,
  ): DateTime {
    const [hours, minutes] = time.split(":").map(Number);
    const base = baseDate || DateTime.now().setZone(timezone);

    return base.set({
      hour: hours,
      minute: minutes,
      second: 0,
      millisecond: 0,
    });
  }
}
