import { Inject, Injectable, OnModuleD<PERSON>roy, OnModuleInit } from "@nestjs/common";
import { BaseGrpcClient } from "@repo/nestjs-commons/grpc";
import { ILogger } from "@repo/nestjs-commons/logger";
import { email } from "@repo/shared-proto";
import { ConfigKeys, ConfigService } from "../../config/config.service";

@Injectable()
export class EmailGrpcClient extends BaseGrpcClient implements OnModuleInit, OnModuleDestroy {
  constructor(
    @Inject("CustomLogger") logger: ILogger,
    private readonly configService: ConfigService,
  ) {
    super(logger, "email");
  }

  onModuleInit() {
    this.initializeClient("email", email.EMAIL_PROVIDER_SERVICE_NAME);
  }

  protected getServiceUrl(): string {
    return this.configService.get(ConfigKeys.EMAIL_GRPC_URL);
  }

  sendEmail(data: email.SendEmailRequest): Promise<email.SendEmailResponse> {
    return this.makeGrpcRequest<email.SendEmailRequest, email.SendEmailResponse>(
      "SendEmail",
      data,
      { bypass_auth: "true" }
    );
  }

  getEmailConfigById(data: email.GetEmailConfigByIdRequest): Promise<email.EmailConfig> {
    return this.makeGrpcRequest<email.GetEmailConfigByIdRequest, email.EmailConfig>(
      "GetEmailConfigById",
      data,
      { bypass_auth: "true" }
    );
  }
} 
