import {
  Inject,
  Injectable,
  OnModule<PERSON><PERSON>roy,
  OnModuleInit,
} from "@nestjs/common";
import { BaseGrpcClient } from "@repo/nestjs-commons/grpc";
import { ILogger } from "@repo/nestjs-commons/logger";
import { workflows } from "@repo/shared-proto";
import { ConfigKeys, ConfigService } from "../../config/config.service";

@Injectable()
export class WorkflowsGrpcClient
  extends BaseGrpcClient
  implements OnModuleInit, OnModuleDestroy
{
  constructor(
    @Inject("CustomLogger") logger: ILogger,
    private readonly configService: ConfigService,
  ) {
    super(logger, "workflows");
  }

  onModuleInit() {
    this.initializeClient("workflows", workflows.WORKFLOWS_SERVICE_NAME);
  }

  protected getServiceUrl(): string {
    return this.configService.get(ConfigKeys.WORKFLOWS_GRPC_URL);
  }

  registerActivities(data: workflows.RegisterActivitiesRequest) {
    return this.makeGrpcRequest<
      workflows.RegisterActivitiesRequest,
      workflows.RegisterActivitiesResponse
    >("registerActivities", data, { bypass_auth: "true" });
  }

  registerEvents(data: workflows.RegisterEventsRequest) {
    return this.makeGrpcRequest<
      workflows.RegisterEventsRequest,
      workflows.RegisterEventsResponse
    >("registerEvents", data, { bypass_auth: "true" });
  }

  seedWorkflows(data: workflows.SeedWorkflowsRequest) {
    return this.makeGrpcRequest<
      workflows.SeedWorkflowsRequest,
      workflows.SeedWorkflowsResponse
    >("seedWorkflows", data, { bypass_auth: "true" });
  }
}
