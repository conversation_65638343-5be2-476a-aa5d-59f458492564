import { SkipThrottle, Throttle } from "@nestjs/throttler";
import {
  THROTTLER_SPECIAL_TIER,
  THROTTLER_TIER,
  THROTTLER_TIER_1,
  THROTTLER_TIER_2,
  THROTTLER_TIER_3,
  THROTTLER_TIER_4,
} from "../../config/throttler.config";

const ALL_SKIP = {
  [THROTTLER_TIER_1]: true,
  [THROTTLER_TIER_2]: true,
  [THROTTLER_TIER_3]: true,
  [THROTTLER_TIER_4]: true,
  [THROTTLER_SPECIAL_TIER]: true,
};

/**
 * Skip throttler for all tiers
 */
export function SkipAllThrottler() {
  return SkipThrottle(ALL_SKIP);
}

/**
 * Throttle API rate limit for tier 1
 */
export function ThrottleTierOne() {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    // Apply SkipThrottle first
    SkipThrottle({
      [THROTTLER_TIER_2]: true,
      [THROTTLER_TIER_3]: true,
      [THROTTLER_TIER_4]: true,
      [THROTTLER_SPECIAL_TIER]: true,
    })(target, propertyKey, descriptor);

    // Then apply Throttle with tier configuration
    Throttle({
      [THROTTLER_TIER_1]: THROTTLER_TIER.TIER_1,
    })(target, propertyKey, descriptor);

    return descriptor;
  };
}

/**
 * Throttle API rate limit for tier 2
 */
export function ThrottleTierTwo() {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    // Apply SkipThrottle first
    SkipThrottle({
      [THROTTLER_TIER_1]: true,
      [THROTTLER_TIER_3]: true,
      [THROTTLER_TIER_4]: true,
      [THROTTLER_SPECIAL_TIER]: true,
    })(target, propertyKey, descriptor);

    // Then apply Throttle with tier configuration
    Throttle({
      [THROTTLER_TIER_2]: THROTTLER_TIER.TIER_2,
    })(target, propertyKey, descriptor);

    return descriptor;
  };
}

/**
 * Throttle API rate limit for tier 3
 */
export function ThrottleTierThree() {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    // Apply SkipThrottle first
    SkipThrottle({
      [THROTTLER_TIER_1]: true,
      [THROTTLER_TIER_2]: true,
      [THROTTLER_TIER_4]: true,
      [THROTTLER_SPECIAL_TIER]: true,
    })(target, propertyKey, descriptor);

    // Then apply Throttle with tier configuration
    Throttle({
      [THROTTLER_TIER_3]: THROTTLER_TIER.TIER_3,
    })(target, propertyKey, descriptor);

    return descriptor;
  };
}

/**
 * Throttle API rate limit for tier 4
 */
export function ThrottleTierFour() {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    // Apply SkipThrottle first
    SkipThrottle({
      [THROTTLER_TIER_1]: true,
      [THROTTLER_TIER_2]: true,
      [THROTTLER_TIER_3]: true,
      [THROTTLER_SPECIAL_TIER]: true,
    })(target, propertyKey, descriptor);

    // Then apply Throttle with tier configuration
    Throttle({
      [THROTTLER_TIER_4]: THROTTLER_TIER.TIER_4,
    })(target, propertyKey, descriptor);

    return descriptor;
  };
}

/**
 * Throttle API rate limit for special tier
 * @returns Combined decorator function
 */
export function ThrottleSpecialTier() {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    // Apply SkipThrottle first
    SkipThrottle({
      [THROTTLER_TIER_1]: true,
      [THROTTLER_TIER_2]: true,
      [THROTTLER_TIER_3]: true,
      [THROTTLER_TIER_4]: true,
    })(target, propertyKey, descriptor);

    // Then apply Throttle with tier configuration
    Throttle({
      [THROTTLER_SPECIAL_TIER]: THROTTLER_TIER.SPECIAL_TIER,
    })(target, propertyKey, descriptor);

    return descriptor;
  };
}
