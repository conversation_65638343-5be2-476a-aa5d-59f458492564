import {
  createParamDecorator,
  ExecutionContext,
  UnauthorizedException,
} from "@nestjs/common";
import { OrganizationTier, UserType } from "@repo/thena-platform-entities";
import { FastifyRequest } from "fastify";

export interface CurrentUser {
  /**
   * The user's token.
   */
  token?: string;

  /**
   * The user's type
   */
  userType: UserType;

  /**
   * The user's timezone.
   */
  timezone: string;

  /**
   * The user's internal unique identifier.
   */
  sub: string;

  /**
   * The user's email address.
   */
  email: string;

  /**
   * The user's organization ID.
   */
  orgId: string;

  /**
   * The user's external unique identifier.
   */
  uid: string;

  /**
   * The user's organization UID.
   */
  orgUid: string;

  /**
   * The user's organization tier.
   */
  orgTier?: OrganizationTier;
}

export const CurrentUser = createParamDecorator(
  (_data: unknown, context: ExecutionContext) => {
    const request = context.switchToHttp().getRequest<FastifyRequest>();

    // If the user is not authenticated, throw an error
    if (!request.user) {
      throw new UnauthorizedException();
    }

    return request.user as CurrentUser;
  },
);
