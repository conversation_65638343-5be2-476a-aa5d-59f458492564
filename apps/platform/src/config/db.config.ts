import { TypeOrmModuleOptions } from "@nestjs/typeorm";
import { getCertificateFromValut } from "@repo/nestjs-commons/utils";
import {
  Account,
  AccountActivity,
  AccountAttributeValue,
  AccountNote,
  AccountRelationship,
  AccountRelationshipType,
  AccountTask,
  AuditLog,
  BusinessHoursConfig,
  Comment,
  CsatAuditLog,
  CsatFeedbackConfig,
  CsatRule,
  CsatRuleFilter,
  CsatSettings,
  CsatSurvey,
  CsatTriggerConfig,
  CustomerContact,
  CustomField,
  CustomFieldValues,
  CustomObject,
  CustomObjectFields,
  CustomObjectRecords,
  Draft,
  EmojiActions,
  Emojis,
  Form,
  FormFieldEvents,
  Mentions,
  NotificationChannel,
  NotificationSubscription,
  NotificationSubscriptionPreference,
  Organization,
  OrganizationDomains,
  OrganizationInvitations,
  PlatformSettings,
  PlatformSettingsSchema,
  Reactions,
  Storage,
  Tag,
  Team,
  TeamCapacity,
  TeamConfiguration,
  TeamMember,
  TeamRoutingRules,
  ThenaRestrictedField,
  Ticket,
  TicketPriority,
  TicketRelationships,
  TicketSentiment,
  TicketStatus,
  TicketTimeLog,
  TicketType,
  TimeOff,
  User,
  UserNotificationPreference,
  UserNotificationPreferenceProfile,
  UserSkills,
  Views,
  ViewsType,
} from "@repo/thena-platform-entities";
import { ConfigKeys, ConfigService } from "./config.service";

const entities = [
  Account,
  PlatformSettings,
  PlatformSettingsSchema,
  AccountActivity,
  AccountAttributeValue,
  CustomerContact,
  AccountNote,
  AccountRelationship,
  EmojiActions,
  AccountRelationshipType,
  AccountTask,
  AuditLog,
  BusinessHoursConfig,
  Comment,
  CustomField,
  CustomFieldValues,
  CustomObject,
  CustomObjectFields,
  CustomObjectRecords,
  FormFieldEvents,
  Draft,
  Emojis,
  Mentions,
  NotificationChannel,
  NotificationSubscription,
  NotificationSubscriptionPreference,
  Organization,
  OrganizationDomains,
  OrganizationInvitations,
  Reactions,
  Storage,
  Tag,
  Team,
  TeamCapacity,
  TeamConfiguration,
  TeamMember,
  TeamRoutingRules,
  Ticket,
  TicketPriority,
  TicketSentiment,
  TicketRelationships,
  TicketStatus,
  TicketTimeLog,
  TicketType,
  TimeOff,
  User,
  UserNotificationPreferenceProfile,
  UserNotificationPreference,
  UserSkills,
  Views,
  ViewsType,
  ThenaRestrictedField,
  Form,
  CsatSettings,
  CsatRule,
  CsatRuleFilter,
  CsatTriggerConfig,
  CsatFeedbackConfig,
  CsatAuditLog,
  CsatSurvey,
];

const getConnectionUrl = (configService: ConfigService) => {
  const dbConfig = {
    host: configService.get(ConfigKeys.THENA_PLATFORM_DB_HOST),
    port: configService.get(ConfigKeys.THENA_PLATFORM_DB_PORT),
    database: configService.get(ConfigKeys.THENA_PLATFORM_DB_NAME),
    username: configService.get(ConfigKeys.THENA_PLATFORM_DB_USER),
    password: configService.get(ConfigKeys.THENA_PLATFORM_DB_PASSWORD),
  };
  return `postgresql://${dbConfig.username}:${dbConfig.password}@${dbConfig.host}:${dbConfig.port}/${dbConfig.database}`;
};

async function getSSLConfig(
  configService: ConfigService,
  isDevelopment: boolean,
) {
  if (isDevelopment) {
    return false;
  }

  return await getCertificateFromValut({
    url: configService.get(ConfigKeys.VAULT_URL),
    token: configService.get(ConfigKeys.VAULT_TOKEN),
    certPath: configService.get(ConfigKeys.CERT_PATH),
  });
}

export const getPlatformDBConfig = async (
  configService: ConfigService,
): Promise<TypeOrmModuleOptions> => {
  const environment = configService.get(ConfigKeys.NODE_ENV);
  const isDevelopment = environment === "development" || environment === "test";
  const ssl = await getSSLConfig(configService, isDevelopment);

  return {
    type: "postgres",
    url: getConnectionUrl(configService),
    entities,
    ssl,
    synchronize: isDevelopment ? true : false,
    logging: false,
    extra: {
      max: 20, // connection pool max size
      connectionTimeoutMillis: 10000, // 10 seconds
      idleTimeoutMillis: 60000, // 1 minute
    },
  };
};
