import { ThrottlerOptions } from "@nestjs/throttler";

const TTL_IN_MS = 60_000; // 1 minute
const BLOCK_DURATION_IN_MS = 10_000; // 10 seconds

export const THROTTLER_TIER_1 = "tier-1";
export const THROTTLER_TIER_2 = "tier-2";
export const THROTTLER_TIER_3 = "tier-3";
export const THROTTLER_TIER_4 = "tier-4";
export const THROTTLER_SPECIAL_TIER = "special-tier";

export const THROTTLER_TIER = {
  TIER_1: {
    name: THROTTLER_TIER_1,
    ttl: TTL_IN_MS,
    limit: 3,
  },
  TIER_2: {
    name: THROTTLER_TIER_2,
    ttl: TTL_IN_MS,
    limit: 24,
  },
  TIER_3: {
    name: THROTTLER_TIER_3,
    ttl: TTL_IN_MS,
    limit: 52,
  },
  TIER_4: {
    name: THROTTLER_TIER_4,
    ttl: TTL_IN_MS,
    limit: 112,
  },
  SPECIAL_TIER: {
    name: THROTTLER_SPECIAL_TIER,
    ttl: TTL_IN_MS,
    limit: 250,
  },
};

export const THROTTLER_CONFIG: ThrottlerOptions[] = [
  {
    ...THROTTLER_TIER.TIER_1,
    blockDuration: BLOCK_DURATION_IN_MS,
  },
  {
    ...THROTTLER_TIER.TIER_2,
    blockDuration: BLOCK_DURATION_IN_MS,
  },
  {
    ...THROTTLER_TIER.TIER_3,
    blockDuration: BLOCK_DURATION_IN_MS,
  },
  {
    ...THROTTLER_TIER.TIER_4,
    blockDuration: BLOCK_DURATION_IN_MS,
  },
  {
    ...THROTTLER_TIER.SPECIAL_TIER,
    blockDuration: BLOCK_DURATION_IN_MS,
  },
];
