/**
 * Configuration interface for tenant-specific Typesense settings
 * @interface TenantConfig
 */

export interface TenantConfig {
  /** API key for write operations */
  apiKey: string;
  /** Read-only API key for search operations */
  searchKey: string;
  /** List of enabled collection names for this tenant */
  collections: string[];
  /** Optional custom configuration for tenant-specific settings */
  customConfig?: Record<string, any>;
}
