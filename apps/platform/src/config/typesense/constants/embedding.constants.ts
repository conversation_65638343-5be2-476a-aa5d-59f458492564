import { TYPESENSE_CONSTANTS } from "../constants/typesense.constants";

export const EMBEDDING_CONSTANTS = {
  DIMENSIONS: 1536,
  MAX_TEXT_LENGTH: 8000,
  WEIGHTS: {
    TITLE: 0.6,
    DESCRIPTION: 0.4,
  },
  THRESHOLDS: {
    DEFAULT: 0.7,
    MIN: 0.5,
    MAX: 0.95,
  },
  LIMITS: {
    DEFAULT: 5,
    MIN: 1,
    MAX: 100,
  },
} as const;

export const ticketEmbeddingsSchema = {
  name: `${TYPESENSE_CONSTANTS.COLLECTION_PREFIX}ticket_embeddings`,
  fields: [
    { name: "ticket_id", type: "string" },
    {
      name: "title_embedding",
      type: "float[]",
      num_dim: EMBEDDING_CONSTANTS.DIMENSIONS,
    },
    {
      name: "description_embedding",
      type: "float[]",
      num_dim: EMBEDDING_CONSTANTS.DIMENSIONS,
    },
    {
      name: "combined_embedding",
      type: "float[]",
      num_dim: EMBEDDING_CONSTANTS.DIMENSIONS,
    },
    { name: "team_id", type: "string", facet: true },
    { name: "organization_id", type: "string", facet: true },
    { name: "updated_at", type: "int64" },
  ],
  default_sorting_field: "updated_at",
};
