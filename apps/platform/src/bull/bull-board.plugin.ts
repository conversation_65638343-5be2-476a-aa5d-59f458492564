import { createBullBoard } from "@bull-board/api";
import { BullMQAdapter } from "@bull-board/api/bullMQAdapter";
import { FastifyAdapter } from "@bull-board/fastify";
import { Queue } from "bullmq";
import { FastifyInstance } from "fastify";
import fp from "fastify-plugin";

interface BullBoardPluginOptions {
  queues: Queue[];
  basePath?: string;
}

export default fp(
  async (fastify: FastifyInstance, options: BullBoardPluginOptions) => {
    const serverAdapter = new FastifyAdapter();
    serverAdapter.setBasePath(options.basePath || "/admin/queues");

    createBullBoard({
      queues: options.queues.map((queue) => new BullMQAdapter(queue)),
      serverAdapter,
      options: {
        uiConfig: {
          boardTitle: "Thena Platform",
        },
      },
    });

    await fastify.register(serverAdapter.registerPlugin(), {
      prefix: options.basePath || "/admin/queues",
      basePath: options.basePath || "/admin/queues",
      logLevel: "silent",
    });

    // Add basic auth hook
    fastify.addHook("onRequest", async (request, reply) => {
      if (request.url.startsWith(options.basePath || "/admin/queues")) {
        const authHeader = request.headers.authorization;
        if (!authHeader || !authHeader.startsWith("Basic ")) {
          reply.header("WWW-Authenticate", 'Basic realm="Bull Board"');
          await reply.status(401).send();
          return;
        }

        const [username, password] = Buffer.from(
          authHeader.split(" ")[1],
          "base64",
        )
          .toString()
          .split(":");

        const adminUser = process.env.BULL_BOARD_ADMIN_USER;
        const adminPassword = process.env.BULL_BOARD_ADMIN_PASSWORD;

        if (username !== adminUser || password !== adminPassword) {
          reply.header("WWW-Authenticate", 'Basic realm="Bull Board"');
          await reply.status(401).send();
          return;
        }
      }
    });
  },
);
