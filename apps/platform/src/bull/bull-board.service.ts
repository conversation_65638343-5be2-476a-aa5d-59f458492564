import { createBullBoard } from "@bull-board/api";
import { BullMQAdapter } from "@bull-board/api/bullMQAdapter";
import { FastifyAdapter } from "@bull-board/fastify";
import { InjectQueue } from "@nestjs/bullmq";
import { Inject, Injectable } from "@nestjs/common";
import { REDIS_CONFIG_TOKEN } from "@repo/nestjs-commons/cache/redis/redis-cache.constants";
import { Queue } from "bullmq";
import { RedisOptions } from "ioredis";
import { QueueNames } from "../constants/queue.constants";

@Injectable()
export class BullBoardService {
  private serverAdapter: FastifyAdapter;

  constructor(
    @InjectQueue(QueueNames.TICKET_SNS_PUBLISHER)
    public readonly ticketsSNSPublishQueue: Queue,
    @InjectQueue(QueueNames.ACCOUNTS_SNS_PUBLISHER)
    public readonly accountsSNSPublishQueue: Queue,
    @InjectQueue(QueueNames.ORGANIZATION_SNS_PUBLISHER)
    public readonly organizationSNSPublishQueue: Queue,
    @InjectQueue(QueueNames.COMMENT_SNS_PUBLISHER)
    public readonly commentSNSPublishQueue: Queue,
    @InjectQueue(QueueNames.REACTION_SNS_PUBLISHER)
    public readonly reactionsSNSPublishQueue: Queue,
    @InjectQueue(QueueNames.WORKFLOW_SEED_PUBLISHER_QUEUE)
    public readonly workflowSeedPublisherQueue: Queue,
    @Inject(REDIS_CONFIG_TOKEN)
    private readonly redisOptions: RedisOptions,
  ) {
    this.setupBullBoard();
  }

  private setupBullBoard() {
    this.serverAdapter = new FastifyAdapter();
    this.serverAdapter.setBasePath("/admin/queues");

    createBullBoard({
      queues: [
        new BullMQAdapter(this.ticketsSNSPublishQueue),
        new BullMQAdapter(this.accountsSNSPublishQueue),
        new BullMQAdapter(this.organizationSNSPublishQueue),
        new BullMQAdapter(this.commentSNSPublishQueue),
        new BullMQAdapter(this.reactionsSNSPublishQueue),
        new BullMQAdapter(this.workflowSeedPublisherQueue),
        // Add other queues here if necessary
      ],
      serverAdapter: this.serverAdapter,
    });

    return this.serverAdapter;
  }

  getRouter() {
    return this.serverAdapter.registerPlugin();
  }
}
