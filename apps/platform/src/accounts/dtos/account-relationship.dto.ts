import {
  <PERSON><PERSON>ot<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from "class-validator";

import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsString } from "class-validator";

export class CreateAccountRelationshipTypeDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: "The name of the relationship type",
    example: "Parent",
  })
  name: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The inverse relationship type ID",
    example: "A123",
  })
  inverseRelationshipId?: string;

  @IsObject()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The metadata of the relationship type",
    example: {},
  })
  metadata?: Record<string, any>;
}

export class UpdateAccountRelationshipTypeDto {
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The name of the relationship type",
    example: "<PERSON><PERSON>",
  })
  name?: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The inverse relationship type ID",
    example: "A123",
  })
  inverseRelationshipId?: string;

  @IsObject()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The metadata of the relationship type",
    example: {},
  })
  metadata?: Record<string, any>;
}
export class CreateAccountRelationshipDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: "Account ID",
    example: "A123",
  })
  accountId: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: "Related account ID",
    example: "A124",
  })
  relatedAccountId: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: "The identifier of the relationship type attribute",
    example: "RELATIONSHIP_TYPE_PARENT",
  })
  relationshipType: string;

  @IsObject()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The metadata of the relationship",
    example: {},
  })
  metadata?: Record<string, any>;
}

export class UpdateAccountRelationshipDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: "The identifier of the relationship type attribute",
    example: "RELATIONSHIP_TYPE_PARENT",
  })
  relationshipType: string;

  @IsObject()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The metadata of the relationship",
    example: {},
  })
  metadata?: Record<string, any>;
}

export class FindAccountRelationshipDto {
  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "Account ID",
    example: "A123",
  })
  accountId?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The identifier of the relationship type attribute",
    example: "RELATIONSHIP_TYPE_PARENT",
  })
  relationshipTypeId?: string;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  @Min(0)
  @ApiPropertyOptional({ description: "The page number" })
  page?: number;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  @Min(1)
  @Max(100)
  @ApiPropertyOptional({
    description: "The limit number of relationships to fetch",
  })
  limit?: number;
}
