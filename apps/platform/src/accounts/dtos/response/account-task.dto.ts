import { ApiProperty } from "@nestjs/swagger";
import {
  AccountAttributeValueConfiguration,
  AccountTask,
} from "@repo/thena-platform-entities";
import { ExternalStorageResponseDto } from "../../../storage/dto/storage.dto";
import { PaginatedResponseDto } from "./common-response.dto";
export class AccountTaskResponseDto {
  @ApiProperty({ description: "The identifier of the task" })
  id: string;

  @ApiProperty({ description: "The identifier of the account" })
  accountId: string;

  @ApiProperty({ description: "The name of the account" })
  account: string;

  @ApiProperty({ description: "The identifier of the activity" })
  activityId: string;

  @ApiProperty({ description: "The title of the task" })
  title: string;

  @ApiProperty({ description: "The description of the task" })
  description: string;

  @ApiProperty({ description: "The identifier of the assignee" })
  assigneeId: string;

  @ApiProperty({ description: "The type of the task" })
  type: string;

  @ApiProperty({ description: "The identifier of the type" })
  typeId: string;

  @ApiProperty({ description: "The configuration of the type" })
  typeConfiguration: AccountAttributeValueConfiguration;

  @ApiProperty({ description: "The status of the task" })
  status: string;

  @ApiProperty({ description: "The identifier of the status" })
  statusId: string;

  @ApiProperty({ description: "The configuration of the status" })
  statusConfiguration: AccountAttributeValueConfiguration;

  @ApiProperty({ description: "The priority of the task" })
  priority: string;

  @ApiProperty({ description: "The identifier of the priority" })
  priorityId: string;

  @ApiProperty({ description: "The configuration of the priority" })
  priorityConfiguration: AccountAttributeValueConfiguration;

  @ApiProperty({ description: "The attachments of the task" })
  attachments: ExternalStorageResponseDto[];

  @ApiProperty({ description: "Whether the task is active" })
  isActive: boolean;

  @ApiProperty({ description: "The creator of the task" })
  creator: string;

  @ApiProperty({ description: "The identifier of the creator" })
  creatorId: string;

  @ApiProperty({ description: "The email of the creator" })
  creatorEmail: string;

  @ApiProperty({ description: "The creation date of the task" })
  createdAt: string;

  @ApiProperty({ description: "The update date of the task" })
  updatedAt: string;

  @ApiProperty({ description: "The metadata of the task" })
  metadata: Record<string, any>;

  static fromEntity(entity: AccountTask) {
    const dto = new AccountTaskResponseDto();

    dto.id = entity.uid;
    dto.accountId = entity.account.uid;
    dto.account = entity.account.name;
    dto.activityId = entity.activity?.uid; // Account task may not have an activity
    dto.title = entity.title;
    dto.description = entity.description;
    dto.assigneeId = entity.assignee.uid;
    dto.type = entity.typeAttribute?.value;
    dto.typeId = entity.typeAttribute?.uid;
    dto.typeConfiguration = entity.typeAttribute?.configuration;
    dto.status = entity.statusAttribute?.value;
    dto.statusId = entity.statusAttribute?.uid;
    dto.statusConfiguration = entity.statusAttribute?.configuration;
    dto.priority = entity.priorityAttribute?.value;
    dto.priorityId = entity.priorityAttribute?.uid;
    dto.priorityConfiguration = entity.priorityAttribute?.configuration;
    dto.attachments = entity.attachments?.map((attachment) =>
      ExternalStorageResponseDto.fromEntity(attachment),
    );
    dto.isActive = entity.isActive;
    dto.creator = entity.creator.name;
    dto.creatorId = entity.creator.uid;
    dto.creatorEmail = entity.creator.email;
    dto.createdAt =
      typeof entity.createdAt === "string"
        ? entity.createdAt
        : entity.createdAt.toISOString();
    dto.updatedAt =
      typeof entity.updatedAt === "string"
        ? entity.updatedAt
        : entity.updatedAt.toISOString();
    dto.metadata = entity.metadata;

    return dto;
  }
}

export class AccountTaskPaginatedResponseDto extends PaginatedResponseDto<AccountTaskResponseDto> {}
