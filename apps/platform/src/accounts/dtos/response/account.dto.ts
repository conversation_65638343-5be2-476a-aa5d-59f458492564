import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
  Account,
  AccountAttributeValueConfiguration,
} from "@repo/thena-platform-entities";
import { AccountMetadata } from "@repo/thena-shared-interfaces";
import { ExternalCustomFieldValuesDto } from "../../../custom-field/dto/custom-field-values.dto";
import { PaginatedResponseDto } from "./common-response.dto";
export class AccountResponseDto {
  @ApiProperty({ description: "Unique identifier of the account" })
  id: string;

  @ApiProperty({ description: "Name of the account" })
  name: string;

  @ApiPropertyOptional({ description: "The description of the account" })
  description?: string;

  @ApiProperty({
    description: "The source of the account",
    example: "hubspot",
  })
  source: string;

  @ApiPropertyOptional({ description: "The URL of the account logo" })
  logo?: string;

  @ApiProperty({ description: "The identifier of the status of the account" })
  statusId: string;

  @ApiProperty({ description: "The status of the account" })
  status: string;

  @ApiProperty({ description: "The configuration of the status" })
  statusConfiguration: AccountAttributeValueConfiguration;

  @ApiProperty({
    description: "The identifier of the classification of the account",
  })
  classificationId: string;

  @ApiProperty({ description: "The classification of the account" })
  classification: string;

  @ApiProperty({ description: "The configuration of the classification" })
  classificationConfiguration: AccountAttributeValueConfiguration;

  @ApiProperty({ description: "The identifier of the health attribute" })
  healthId: string;

  @ApiProperty({ description: "The health of the account" })
  health: string;

  @ApiProperty({ description: "The configuration of the health" })
  healthConfiguration: AccountAttributeValueConfiguration;

  @ApiProperty({ description: "The identifier of the industry attribute" })
  industryId: string;

  @ApiProperty({ description: "The industry of the account" })
  industry: string;

  @ApiProperty({ description: "The configuration of the industry" })
  industryConfiguration: AccountAttributeValueConfiguration;

  @ApiProperty({ description: "Primary domain of the account" })
  primaryDomain: string;

  @ApiPropertyOptional({ description: "Secondary domain of the account" })
  secondaryDomain?: string;

  @ApiPropertyOptional({ description: "Name of the account owner" })
  accountOwner?: string;

  @ApiPropertyOptional({
    description: "Unique identifier of the account owner",
  })
  accountOwnerId?: string;

  @ApiPropertyOptional({ description: "Email of the account owner" })
  accountOwnerEmail?: string;

  @ApiPropertyOptional({ description: "The avatar URL of the account owner" })
  accountOwnerAvatarUrl?: string;

  @ApiPropertyOptional({ description: "Annual revenue of the account" })
  annualRevenue?: number;

  @ApiPropertyOptional({ description: "Number of employees of the account" })
  employees?: number;

  @ApiPropertyOptional({ description: "Website of the account" })
  website?: string;

  @ApiPropertyOptional({ description: "Billing address of the account" })
  billingAddress?: string;

  @ApiPropertyOptional({ description: "Shipping address of the account" })
  shippingAddress?: string;

  @ApiProperty({
    description: "The custom field values",
  })
  customFieldValues?: ExternalCustomFieldValuesDto[];

  @ApiProperty({ description: "The metadata of the account" })
  metadata?: Record<string, any> | AccountMetadata;

  @ApiProperty({ description: "Creation date of the account" })
  createdAt: string;

  @ApiProperty({ description: "Last update date of the account" })
  updatedAt: string;

  static fromEntity(entity: Account): AccountResponseDto {
    const dto = new AccountResponseDto();

    dto.id = entity.uid;
    dto.name = entity.name;
    dto.description = entity.description;
    dto.source = entity.source;
    dto.logo = entity.logo;
    dto.statusId = entity.statusAttribute?.uid;
    dto.status = entity.statusAttribute?.value;
    dto.statusConfiguration = entity.statusAttribute?.configuration;
    dto.classificationId = entity.classificationAttribute?.uid;
    dto.classification = entity.classificationAttribute?.value;
    dto.classificationConfiguration =
      entity.classificationAttribute?.configuration;
    dto.healthId = entity.healthAttribute?.uid;
    dto.health = entity.healthAttribute?.value;
    dto.healthConfiguration = entity.healthAttribute?.configuration;
    dto.industryId = entity.industryAttribute?.uid;
    dto.industry = entity.industryAttribute?.value;
    dto.industryConfiguration = entity.industryAttribute?.configuration;
    dto.primaryDomain = entity.primaryDomain;
    dto.secondaryDomain = entity.secondaryDomain;
    dto.accountOwner = entity.accountOwner?.name;
    dto.accountOwnerId = entity.accountOwner?.uid;
    dto.accountOwnerEmail = entity.accountOwner?.email;
    dto.accountOwnerAvatarUrl = entity.accountOwner?.avatarUrl;
    dto.annualRevenue = entity.annualRevenue;
    dto.employees = entity.employees;
    dto.website = entity.website;
    dto.billingAddress = entity.billingAddress;
    dto.shippingAddress = entity.shippingAddress;
    dto.customFieldValues = entity?.customFieldValues?.map((cfv) => ({
      customFieldId: cfv.customField.uid,
      data: cfv.data,
      metadata: cfv.metadata,
    }));
    dto.metadata = entity.metadata;
    dto.createdAt =
      typeof entity.createdAt === "string"
        ? entity.createdAt
        : entity.createdAt.toISOString();
    dto.updatedAt =
      typeof entity.updatedAt === "string"
        ? entity.updatedAt
        : entity.updatedAt.toISOString();

    return dto;
  }
}

export class AccountWithCustomerContactsResponseDto extends AccountResponseDto {
  @ApiProperty({
    description: "The customer contacts of the account",
    type: Array<{
      id: string;
      name: string;
      email: string;
      avatarUrl: string;
    }>,
  })
  customerContacts: {
    id: string;
    name: string;
    email: string;
    avatarUrl: string;
  }[];

  static fromEntity(entity: Account): AccountWithCustomerContactsResponseDto {
    const dto = new AccountWithCustomerContactsResponseDto();

    Object.assign(dto, super.fromEntity(entity));
    dto.customerContacts = entity.customerContacts.map((cc) => ({
      id: cc.uid,
      name: cc.firstName + " " + cc.lastName,
      email: cc.email,
      avatarUrl: cc.avatarUrl,
    }));

    return dto;
  }
}

export class AccountPaginatedResponseDto extends PaginatedResponseDto<AccountResponseDto> {}
export class AccountWithCustomerContactsPaginatedResponseDto extends PaginatedResponseDto<AccountWithCustomerContactsResponseDto> {}
