import { ApiProperty } from "@nestjs/swagger";
import {
  AccountAttributeType,
  AccountAttributeValue,
  AccountAttributeValueConfiguration,
} from "@repo/thena-platform-entities";

export class AccountAttributeValueResponseDto {
  @ApiProperty({
    description: "The unique identifier of the attribute value",
  })
  id: string;

  @ApiProperty({
    description: "Attribute type",
    example: "account_status",
    enum: AccountAttributeType,
  })
  attribute: AccountAttributeType;

  @ApiProperty({
    description: "Attribute value",
    example: "PROSPECT",
  })
  value: string;

  @ApiProperty({
    description: "Whether this value is default for the attribute",
  })
  isDefault: boolean;

  @ApiProperty({
    description: "Configuration of the attribute value",
  })
  configuration: AccountAttributeValueConfiguration;

  @ApiProperty({
    description: "The creation date of the attribute value",
  })
  createdAt: string;

  @ApiProperty({
    description: "The update date of the attribute value",
  })
  updatedAt: string;

  @ApiProperty({
    description: "The metadata of the attribute value",
  })
  metadata: Record<string, any>;

  static fromEntity(entity: AccountAttributeValue) {
    const dto = new AccountAttributeValueResponseDto();

    dto.id = entity.uid;
    dto.attribute = entity.attribute;
    dto.value = entity.value;
    dto.isDefault = entity.isDefault;
    dto.configuration = entity.configuration;
    dto.createdAt =
      typeof entity.createdAt === "string"
        ? entity.createdAt
        : entity.createdAt.toISOString();
    dto.updatedAt =
      typeof entity.updatedAt === "string"
        ? entity.updatedAt
        : entity.updatedAt.toISOString();
    dto.metadata = entity.metadata;

    return dto;
  }
}
