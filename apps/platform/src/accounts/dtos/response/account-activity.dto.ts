import { ApiProperty } from "@nestjs/swagger";
import {
  AccountActivity,
  AccountAttributeValueConfiguration,
} from "@repo/thena-platform-entities";
import { ExternalStorageResponseDto } from "../../../storage/dto/storage.dto";
import { PaginatedResponseDto } from "./common-response.dto";

export class AccountActivityResponseDto {
  @ApiProperty({ description: "The identifier of the activity" })
  id: string;

  @ApiProperty({ description: "The identifier of the account" })
  accountId: string;

  @ApiProperty({ description: "The name of the account" })
  account: string;

  @ApiProperty({ description: "The timestamp of the activity" })
  activityTimestamp: string;

  @ApiProperty({ description: "The duration of the activity in minutes" })
  duration: number;

  @ApiProperty({ description: "The location of the activity" })
  location: string;

  @ApiProperty({ description: "The title of the activity" })
  title: string;

  @ApiProperty({ description: "The description of the activity" })
  description: string;

  @ApiProperty({ description: "The type of the activity" })
  type: string;

  @ApiProperty({ description: "The identifier of the type attribute" })
  typeId: string;

  @ApiProperty({ description: "The configuration of the type attribute" })
  typeConfiguration: AccountAttributeValueConfiguration;

  @ApiProperty({ description: "The status of the activity" })
  status: string;

  @ApiProperty({ description: "The identifier of the status attribute" })
  statusId: string;

  @ApiProperty({ description: "The configuration of the status attribute" })
  statusConfiguration: AccountAttributeValueConfiguration;

  @ApiProperty({ description: "The participants of the activity" })
  participants: string[];

  @ApiProperty({ description: "The creator of the activity" })
  creator: string;

  @ApiProperty({ description: "The identifier of the creator" })
  creatorId: string;

  @ApiProperty({ description: "The email of the creator" })
  creatorEmail: string;

  @ApiProperty({ description: "The attachments of the activity" })
  attachments: ExternalStorageResponseDto[];

  @ApiProperty({
    description: "The creation date of the activity",
  })
  createdAt: string;

  @ApiProperty({
    description: "The update date of the activity",
  })
  updatedAt: string;

  @ApiProperty({
    description: "The metadata of the activity",
  })
  metadata: Record<string, any>;

  static fromEntity(entity: AccountActivity) {
    const dto = new AccountActivityResponseDto();

    dto.id = entity.uid;
    dto.accountId = entity.account.uid;
    dto.account = entity.account.name;
    dto.activityTimestamp =
      typeof entity.activityTimestamp === "string"
        ? entity.activityTimestamp
        : entity.activityTimestamp.toISOString();
    dto.duration = entity.duration;
    dto.location = entity.location;
    dto.title = entity.title;
    dto.description = entity.description;
    dto.type = entity.typeAttribute?.value;
    dto.typeId = entity.typeAttribute?.uid;
    dto.typeConfiguration = entity.typeAttribute?.configuration;
    dto.status = entity.statusAttribute?.value;
    dto.statusId = entity.statusAttribute?.uid;
    dto.statusConfiguration = entity.statusAttribute?.configuration;
    dto.participants = entity.participants;
    dto.attachments = entity.attachments?.map((attachment) =>
      ExternalStorageResponseDto.fromEntity(attachment),
    );
    dto.creator = entity.creator.name;
    dto.creatorId = entity.creator.uid;
    dto.creatorEmail = entity.creator.email;
    dto.createdAt =
      typeof entity.createdAt === "string"
        ? entity.createdAt
        : entity.createdAt.toISOString();
    dto.updatedAt =
      typeof entity.updatedAt === "string"
        ? entity.updatedAt
        : entity.updatedAt.toISOString();
    dto.metadata = entity.metadata;

    return dto;
  }
}

export class AccountActivityPaginatedResponseDto extends PaginatedResponseDto<AccountActivityResponseDto> {}
