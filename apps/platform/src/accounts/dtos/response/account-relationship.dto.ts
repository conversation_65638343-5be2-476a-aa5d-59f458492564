import { ApiProperty } from "@nestjs/swagger";
import {
  AccountRelationship,
  AccountRelationshipType,
} from "@repo/thena-platform-entities";
import { PaginatedResponseDto } from "./common-response.dto";
export class AccountRelationshipTypeResponseDto {
  @ApiProperty({
    description: "The identifier of the relationship type",
    example: "ART123",
  })
  id: string;

  @ApiProperty({
    description: "The name of the relationship type",
    example: "Parent",
  })
  name: string;

  @ApiProperty({
    description: "The identifier of the inverse relationship type",
    example: "ART124",
  })
  inverseRelationshipId: string;

  @ApiProperty({
    description: "The name of the inverse relationship type",
    example: "Subsidiary",
  })
  inverseRelationship: string;

  @ApiProperty({
    description: "The creation date of the relationship type",
  })
  createdAt: string;

  @ApiProperty({
    description: "Last update date of the relationship type",
  })
  updatedAt: string;

  @ApiProperty({
    description: "The metadata of the relationship type",
  })
  metadata: Record<string, any>;

  static fromEntity(entity: AccountRelationshipType) {
    const dto = new AccountRelationshipTypeResponseDto();

    dto.id = entity.uid;
    dto.name = entity.name;
    dto.inverseRelationshipId = entity.inverseRelationship?.uid;
    dto.inverseRelationship = entity.inverseRelationship?.name;
    dto.createdAt =
      typeof entity.createdAt === "string"
        ? entity.createdAt
        : entity.createdAt.toISOString();
    dto.updatedAt =
      typeof entity.updatedAt === "string"
        ? entity.updatedAt
        : entity.updatedAt.toISOString();
    dto.metadata = entity.metadata;

    return dto;
  }
}

export class AccountRelationshipResponseDto {
  @ApiProperty({
    description: "The identifier of the relationship",
    example: "AR123",
  })
  id: string;

  @ApiProperty({
    description: "The identifier of the primary account of the relationship",
  })
  accountId: string;

  @ApiProperty({
    description: "The name of the primary account of the relationship",
  })
  account: string;

  @ApiProperty({
    description: "The identifier of the related account of the relationship",
  })
  relatedAccountId: string;

  @ApiProperty({
    description: "The name of the related account of the relationship",
  })
  relatedAccount: string;

  @ApiProperty({
    description: "Relationship type",
  })
  relationshipType: AccountRelationshipTypeResponseDto;

  @ApiProperty({
    description: "The creation date of the relationship",
  })
  createdAt: string;

  @ApiProperty({
    description: "Last update date of the relationship",
  })
  updatedAt: string;

  @ApiProperty({
    description: "The metadata of the relationship",
  })
  metadata: Record<string, any>;

  static fromEntity(entity: AccountRelationship) {
    // Needs population of account, relatedAccount and relationshipType
    const dto = new AccountRelationshipResponseDto();

    dto.id = entity.uid;
    dto.accountId = entity.account.uid;
    dto.account = entity.account.name;
    dto.relatedAccountId = entity.relatedAccount.uid;
    dto.relatedAccount = entity.relatedAccount.name;
    dto.relationshipType = AccountRelationshipTypeResponseDto.fromEntity(
      entity.relationshipType,
    );
    dto.createdAt =
      typeof entity.createdAt === "string"
        ? entity.createdAt
        : entity.createdAt.toISOString();
    dto.updatedAt =
      typeof entity.updatedAt === "string"
        ? entity.updatedAt
        : entity.updatedAt.toISOString();
    dto.metadata = entity.metadata;

    return dto;
  }
}

export class AccountRelationshipPaginatedResponseDto extends PaginatedResponseDto<AccountRelationshipResponseDto> {}
