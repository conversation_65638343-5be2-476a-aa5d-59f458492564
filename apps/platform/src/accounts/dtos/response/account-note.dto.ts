import { ApiProperty } from "@nestjs/swagger";
import {
  AccountAttributeValueConfiguration,
  AccountNote,
} from "@repo/thena-platform-entities";
import { ExternalStorageResponseDto } from "../../../storage/dto/storage.dto";
import { PaginatedResponseDto } from "./common-response.dto";

export class AccountNoteResponseDto {
  @ApiProperty({ description: "The identifier of the activity" })
  id: string;

  @ApiProperty({ description: "The identifier of the account" })
  accountId: string;

  @ApiProperty({ description: "The name of the account" })
  account: string;

  @ApiProperty({ description: "The content of the note" })
  content: string;

  @ApiProperty({ description: "The type of the note" })
  type: string;

  @ApiProperty({ description: "The identifier of the type attribute" })
  typeId: string;

  @ApiProperty({ description: "The configuration of the type" })
  typeConfiguration: AccountAttributeValueConfiguration;

  @ApiProperty({ description: "The visibility of the note" })
  visibility: string;

  @ApiProperty({ description: "The attachments of the note" })
  attachments: ExternalStorageResponseDto[];

  @ApiProperty({ description: "The name of the author" })
  author: string;

  @ApiProperty({ description: "The identifier of the author" })
  authorId: string;

  @ApiProperty({ description: "The email of the author" })
  authorEmail: string;

  @ApiProperty({ description: "The timestamp of the note" })
  createdAt: string;

  @ApiProperty({ description: "The last updated timestamp of the note" })
  updatedAt: string;

  @ApiProperty({ description: "The metadata of the note" })
  metadata: Record<string, any>;

  static fromEntity(entity: AccountNote) {
    const dto = new AccountNoteResponseDto();

    dto.id = entity.uid;
    dto.accountId = entity.account.uid;
    dto.account = entity.account.name;
    dto.content = entity.content;
    dto.type = entity.noteTypeAttribute?.value;
    dto.typeId = entity.noteTypeAttribute?.uid;
    dto.typeConfiguration = entity.noteTypeAttribute?.configuration;
    dto.visibility = entity.visibility;
    dto.attachments = entity.attachments?.map((attachment) =>
      ExternalStorageResponseDto.fromEntity(attachment),
    );
    dto.author = entity.author.name;
    dto.authorId = entity.author.uid;
    dto.authorEmail = entity.author.email;
    dto.createdAt =
      typeof entity.createdAt === "string"
        ? entity.createdAt
        : entity.createdAt.toISOString();
    dto.updatedAt =
      typeof entity.updatedAt === "string"
        ? entity.updatedAt
        : entity.updatedAt.toISOString();
    dto.metadata = entity.metadata;

    return dto;
  }
}

export class AccountNotePaginatedResponseDto extends PaginatedResponseDto<AccountNoteResponseDto> {}
