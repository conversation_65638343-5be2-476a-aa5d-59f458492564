import { ApiProperty } from "@nestjs/swagger";
import { CustomerContact } from "@repo/thena-platform-entities";
import { CustomerContactMetadata } from "@repo/thena-shared-interfaces";
import { ExternalCustomFieldValuesDto } from "../../../custom-field/dto";
import { PaginatedResponseDto } from "./common-response.dto";

export class CustomerContactResponseDto {
  @ApiProperty({
    description: "The identifier of the customer contact",
  })
  id: string;

  @ApiProperty({
    description: "The first name of the customer contact",
  })
  firstName: string;

  @ApiProperty({
    description: "The last name of the customer contact",
  })
  lastName: string;

  @ApiProperty({
    description: "The email of the customer contact",
  })
  email: string;

  @ApiProperty({
    description: "The phone number of the customer contact",
  })
  phoneNumber: string;

  @ApiProperty({
    description: "The avatar URL of the customer contact",
  })
  avatarUrl: string;

  @ApiProperty({ description: "The name of the account" })
  accounts: { id: string; name: string }[];

  @ApiProperty({ description: "The identifier of the contact type" })
  contactTypeId: string;

  @ApiProperty({ description: "The name of the contact type" })
  contactType: string;

  @ApiProperty({
    description: "The custom field values",
  })
  customFieldValues?: ExternalCustomFieldValuesDto[];

  @ApiProperty({ description: "The metadata of the contact" })
  metadata?: Record<string, any> | CustomerContactMetadata;

  @ApiProperty({
    description: "The creation date of the contact",
  })
  createdAt: string;

  @ApiProperty({
    description: "The last update date of the contact",
  })
  updatedAt: string;

  static fromEntity(entity: CustomerContact) {
    const dto = new CustomerContactResponseDto();

    dto.id = entity.uid;
    dto.firstName = entity.firstName;
    dto.lastName = entity.lastName;
    dto.email = entity.email;
    dto.phoneNumber = entity.phoneNumber;
    dto.avatarUrl = entity.avatarUrl;
    dto.accounts =
      entity.accounts?.map((account) => ({
        id: account.uid,
        name: account.name,
      })) ?? [];
    dto.customFieldValues = entity?.customFieldValues?.map((cfv) => ({
      customFieldId: cfv.customField.uid,
      data: cfv.data,
      metadata: cfv.metadata,
    }));
    dto.contactTypeId = entity.contactTypeAttribute?.uid;
    dto.contactType = entity.contactTypeAttribute?.value;
    dto.metadata = entity.metadata;
    dto.createdAt =
      typeof entity.createdAt === "string"
        ? entity.createdAt
        : entity.createdAt.toISOString();
    dto.updatedAt =
      typeof entity.updatedAt === "string"
        ? entity.updatedAt
        : entity.updatedAt.toISOString();

    return dto;
  }
}

export class CustomerContactBulkResponseDto {
  @ApiProperty({ description: "The total number of contacts provided" })
  total: number;

  @ApiProperty({ description: "The number of contacts created" })
  created: number;

  @ApiProperty({
    description:
      "The number of contacts skipped due to existing contacts with the same email.",
  })
  skipped: number;
}

export class CustomerContactPaginatedResponseDto extends PaginatedResponseDto<CustomerContactResponseDto> {}
