import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
  AccountSinks,
  CustomerContactMetadata,
} from "@repo/thena-shared-interfaces";
import { Transform, Type } from "class-transformer";
import {
  IsArray,
  IsEmail,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  Max,
  Min,
  ValidateNested,
} from "class-validator";
import { ExternalCustomFieldValuesDto } from "../../custom-field/dto";

export class CreateCustomerContactDto {
  @ApiProperty({
    description: "First name of the customer contact",
    example: "<PERSON>",
  })
  @IsNotEmpty()
  @IsString()
  firstName: string;

  @ApiPropertyOptional({
    description: "Last name of the customer contact",
    example: "Doe",
  })
  @IsOptional()
  @IsString()
  lastName?: string;

  @ApiProperty({
    description: "Email of the customer contact",
    example: "<EMAIL>",
  })
  @IsEmail()
  @Transform(({ value }) => value?.toLowerCase())
  email: string;

  @ApiPropertyOptional({
    description: "Phone number of the customer contact with country code",
    example: "+**********",
  })
  @IsOptional()
  phoneNumber?: string;

  @ApiPropertyOptional({
    description:
      "Contact type (UID of the contact type attribute value) of the customer contact to create. (Uses default contact type if not provided)",
    example: "CT123",
  })
  @IsOptional()
  @IsString()
  contactType?: string;

  @ApiPropertyOptional({
    description: "Avatar URL of the customer contact",
    example: "https://example.com/avatar.png",
  })
  @IsOptional()
  @IsString()
  avatarUrl?: string;

  @ApiPropertyOptional({
    description: "Account IDs of the customer contact",
    example: ["A123", "A456"],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  accountIds?: string[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ExternalCustomFieldValuesDto)
  @ApiPropertyOptional({
    description: "The custom field values of the customer contact",
    type: [ExternalCustomFieldValuesDto],
  })
  customFieldValues?: ExternalCustomFieldValuesDto[];

  @IsOptional()
  @IsObject()
  @ApiPropertyOptional({
    description: "The metadata of the customer contact",
    type: Object,
  })
  metadata?: CustomerContactMetadata | Record<string, any>;
}

export class UpdateCustomerContactDto {
  @ApiPropertyOptional({
    description: "First name of the customer contact",
    example: "John",
  })
  @IsOptional()
  @IsString()
  firstName?: string;

  @ApiPropertyOptional({
    description: "Last name of the customer contact",
    example: "Doe",
  })
  @IsOptional()
  @IsString()
  lastName?: string;

  @ApiPropertyOptional({
    description: "Avatar URL of the customer contact",
    example: "https://example.com/avatar.png",
  })
  @IsOptional()
  @IsString()
  avatarUrl?: string;

  @ApiPropertyOptional({
    description: "Email of the customer contact",
    example: "<EMAIL>",
  })
  @IsOptional()
  @IsEmail()
  @Transform(({ value }) => value.toLowerCase())
  email?: string;

  @ApiPropertyOptional({
    description: "Phone number of the customer contact with country code",
    example: "+**********",
  })
  @IsOptional()
  phoneNumber?: string;

  @ApiPropertyOptional({
    description: "Contact type of the customer contact",
    example: "CT123",
  })
  @IsOptional()
  @IsString()
  contactType?: string;

  @ApiPropertyOptional({
    description:
      "Account IDs of the customer contact. This will replace the existing account IDs of the customer contact",
    example: ["A123", "A456"],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  accountIds?: string[];

  @IsObject()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The metadata of the customer contact",
    type: Object,
  })
  metadata?: CustomerContactMetadata | Record<string, any>;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ExternalCustomFieldValuesDto)
  @ApiPropertyOptional({
    description: "The custom field values of the customer contact",
    type: [ExternalCustomFieldValuesDto],
  })
  customFieldValues?: ExternalCustomFieldValuesDto[];
}

class BulkCreateCustomerContactDetailsDto {
  @ApiProperty({
    description: "First name of the customer contact",
    example: "John",
  })
  @IsNotEmpty()
  @IsString()
  firstName: string;

  @ApiPropertyOptional({
    description: "Last name of the customer contact",
    example: "Doe",
  })
  @IsOptional()
  @IsString()
  lastName?: string;

  @ApiProperty({
    description: "Email of the customer contact",
    example: "<EMAIL>",
  })
  @IsNotEmpty()
  @IsEmail()
  @Transform(({ value }) => value.toLowerCase())
  email: string;

  @ApiPropertyOptional({
    description: "Phone number of the customer contact with country code",
    example: "+**********",
  })
  @IsOptional()
  phoneNumber?: string;

  @ApiPropertyOptional({
    description: "Avatar URL of the customer contact",
    example: "https://example.com/avatar.png",
  })
  @IsOptional()
  @IsString()
  avatarUrl?: string;
}

export class BulkCreateCustomerContactsDto {
  @ApiProperty({
    description: "Details of customer contacts to create",
  })
  @IsArray()
  @ValidateNested()
  @Type(() => BulkCreateCustomerContactDetailsDto)
  contacts: BulkCreateCustomerContactDetailsDto[];

  @ApiPropertyOptional({
    description: "Account ID of the customer contacts",
    example: "A123",
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  accountIds?: string[];

  @ApiPropertyOptional({
    description: "Contact type of the customer contacts",
    example: "CT123",
  })
  @IsOptional()
  @IsString()
  contactType?: string;

  // Add source
  @ApiPropertyOptional({
    description: "Source of the customer contacts",
    example: "Slack",
  })
  @IsOptional()
  @IsString()
  source?: AccountSinks;
}

export class FindAllCustomerContactsDto {
  @ApiPropertyOptional({
    description: "Account ID of the customer contact",
    example: "A123",
  })
  @IsOptional()
  @IsString()
  accountId?: string;

  @ApiPropertyOptional({
    description:
      "Contact type of the customer contact to find. (Fetches all contacts satisfying other conditions if not provided)",
    example: "CT123",
  })
  @IsOptional()
  @IsString()
  contactType?: string;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  @Min(0)
  @ApiPropertyOptional({ description: "The page number" })
  page?: number;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  @Min(1)
  @Max(100)
  @ApiPropertyOptional({ description: "The limit number of contacts to fetch" })
  limit?: number;
}

export class FilterCustomerContactsByIdsDto {
  @ApiProperty({
    description: "The IDs of the customer contacts to filter",
    example: ["C123", "C456"],
  })
  @IsArray()
  @IsString({ each: true })
  ids: string[];
}

export class SearchCustomerContactsDto {
  @ApiProperty({
    description: "The email of the customer contact to search",
    example: "<EMAIL>",
  })
  @IsOptional()
  email?: string;
}
