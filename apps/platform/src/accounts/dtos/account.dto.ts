import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { AccountMetadata } from "@repo/thena-shared-interfaces";
import { Transform, Type } from "class-transformer";
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  IsUrl,
  Max,
  Min,
  ValidateNested,
} from "class-validator";
import { ExternalCustomFieldValuesDto } from "../../custom-field/dto";

class CommonAccountDto {
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The description of the account",
  })
  description?: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The source of the account",
    example: "hubspot",
  })
  source?: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The user identifier of the account owner",
    example: "USER123",
  })
  accountOwnerId?: string;

  @IsUrl()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The URL of the account logo",
    example: "https://example.com/logo.png",
  })
  logo?: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The identifier / value of the status attribute",
    example: "STATUS123",
  })
  status?: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The identifier / value of the classification attribute",
    example: "CLASSIFICATION123",
  })
  classification?: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The identifier / value of the health attribute",
    example: "HEALTH123",
  })
  health?: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The identifier / value of the industry attribute",
    example: "INDUSTRY123",
  })
  industry?: string;

  @IsOptional()
  @ApiPropertyOptional({
    description: "The secondary domain of the account",
    example: "example.com",
  })
  secondaryDomain?: string;

  @IsNumber()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The annual revenue of the account",
    type: Number,
    example: 1000000,
  })
  annualRevenue?: number;

  @IsNumber()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The number of employees of the account",
    type: Number,
    example: 100,
  })
  employees?: number;

  @IsUrl()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The website of the account",
    example: "https://example.com",
  })
  website?: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The billing address of the account",
    example: "123 Main St, Town, State, USA 12345",
  })
  billingAddress?: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The shipping address of the account",
    example: "123 Main St, Town, State, USA 12345",
  })
  shippingAddress?: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ExternalCustomFieldValuesDto)
  @ApiPropertyOptional({
    description: "The custom field values of the account",
    type: [ExternalCustomFieldValuesDto],
  })
  customFieldValues?: ExternalCustomFieldValuesDto[];

  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => Boolean(value))
  @ApiPropertyOptional({
    description:
      "Whether to add existing users matching the email domain to account contacts (false by default)",
    example: true,
  })
  addExistingUsersToAccountContacts?: boolean;

  @IsObject()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The metadata of the account",
    type: Object,
  })
  metadata?: AccountMetadata | Record<string, any>;
}

export class CreateAccountDto extends CommonAccountDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: "The name of the account",
    example: "Example",
  })
  name: string;

  @IsNotEmpty()
  @ApiProperty({
    description: "The primary domain of the account",
    example: "example.com",
  })
  primaryDomain: string;
}

export class UpdateAccountDto extends CommonAccountDto {
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The name of the account",
    example: "Example",
  })
  name?: string;

  @IsOptional()
  @ApiPropertyOptional({
    description: "The primary domain of the account",
    example: "example.com",
  })
  primaryDomain?: string;
}

export class FindAllAccountsDto {
  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The source of the accounts to find",
    example: "manual",
  })
  source?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The identifier / value of the status attribute",
    example: "STATUS123",
  })
  status?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The identifier / value of the classification attribute",
    example: "CLASSIFICATION123",
  })
  classification?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The identifier / value of the health attribute",
    example: "HEALTH123",
  })
  health?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The identifier / value of the industry attribute",
    example: "INDUSTRY123",
  })
  industry?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The identifier of the account owner",
    example: "USER123",
  })
  accountOwnerId?: string;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  @Min(0)
  @ApiPropertyOptional({ description: "The page number" })
  page?: number;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  @Min(1)
  @Max(100)
  @ApiPropertyOptional({ description: "The limit number of accounts to fetch" })
  limit?: number;
}

export class FilterAccountsByPrimaryDomainsDto {
  @IsArray()
  @ArrayMinSize(1)
  @IsString({ each: true })
  @ApiProperty({ type: [String] })
  primaryDomains: string[];
}

export class FilterAccountsByIdsDto {
  @IsArray()
  @ArrayMinSize(1)
  @IsString({ each: true })
  @ApiProperty({ type: [String] })
  ids: string[];
}
