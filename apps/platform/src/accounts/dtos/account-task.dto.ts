import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import {
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  <PERSON>,
  <PERSON>,
} from "class-validator";

export class FindAccountTaskDto {
  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The identifier of the account to find tasks for",
    example: "A123",
  })
  accountId?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The identifier of the activity to find tasks for",
    example: "A123",
  })
  activityId?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The identifier of the assignee to find tasks for",
    example: "U123",
  })
  assigneeId?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The identifier / value of the type attribute of the task",
    example: "TASK_TYPE_TODO",
  })
  type?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The identifier / value of the status attribute of the task",
    example: "TASK_STATUS_TODO",
  })
  status?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The identifier / value of the priority attribute of the task",
    example: "TASK_PRIORITY_HIGH",
  })
  priority?: string;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  @Min(0)
  @ApiPropertyOptional({ description: "The page number" })
  page?: number;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  @Min(1)
  @Max(100)
  @ApiPropertyOptional({ description: "The limit number of tasks to fetch" })
  limit?: number;
}

export class CreateAccountTaskDto {
  @IsNotEmpty()
  @IsString()
  @ApiProperty({
    description: "The identifier of the account to create the task for",
    example: "A123",
  })
  accountId: string;

  @IsNotEmpty()
  @IsString()
  @ApiProperty({
    description: "The title of the task",
    example: "Task 1",
  })
  title: string;

  @IsNotEmpty()
  @IsString()
  @ApiProperty({
    description: "The identifier of the assignee",
    example: "U123",
  })
  assigneeId: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The identifier of the activity to create the task for",
    example: "A123",
  })
  activityId?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The description of the task",
    example: "This is a task",
  })
  description?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The identifier / value of the type attribute of the task",
    example: "T123",
  })
  type?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The identifier / value of the status attribute of the task	",
    example: "S123",
  })
  status?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The identifier / value of the priority attribute of the task",
    example: "P123",
  })
  priority?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ApiPropertyOptional({
    description: "The URLs of the attachments to attach to the note",
    example: [
      "https://example.com/attachment1.jpg",
      "https://example.com/attachment2.jpg",
    ],
  })
  attachmentUrls?: string[];

  @IsOptional()
  @IsObject()
  @ApiPropertyOptional({
    description: "The metadata of the task",
    example: {},
  })
  metadata?: Record<string, any>;
}

export class UpdateAccountTaskDto {
  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The title of the task",
    example: "Task 1",
  })
  title?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The identifier of the assignee",
    example: "U123",
  })
  assigneeId?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "Change the activity associated with the task",
    example: "A123",
  })
  activityId?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The description of the task",
    example: "This is a task",
  })
  description?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The identifier / value of the type attribute of the task",
    example: "T123",
  })
  type?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The identifier / value of the status attribute of the task	",
    example: "S123",
  })
  status?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The identifier / value of the priority attribute of the task",
    example: "P123",
  })
  priority?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ApiPropertyOptional({
    description: "The URLs of the attachments to attach to the note",
    example: [
      "https://example.com/attachment1.jpg",
      "https://example.com/attachment2.jpg",
    ],
  })
  attachmentUrls?: string[];

  @IsOptional()
  @IsObject()
  @ApiPropertyOptional({
    description: "The metadata of the task",
    example: {},
  })
  metadata?: Record<string, any>;
}
