import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import {
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  <PERSON>,
  <PERSON>,
} from "class-validator";

export class FindAccountActivityDto {
  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The identifier of the account to find activities for",
    example: "A123",
  })
  accountId?: string;

  @IsOptional()
  @ApiPropertyOptional({
    description: "The identifier / value of the type of the activity",
  })
  type?: string;

  @IsOptional()
  @ApiPropertyOptional({
    description: "The identifier / value of the status of the activity",
  })
  status?: string;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  @Min(0)
  @ApiPropertyOptional({ description: "The page number" })
  page?: number;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  @Min(1)
  @Max(100)
  @ApiPropertyOptional({
    description: "The limit number of activities to fetch",
  })
  limit?: number;
}

export class CreateAccountActivityDto {
  @IsNotEmpty()
  @IsString()
  @ApiProperty({
    description: "The identifier of the account to create the activity for",
    example: "A123",
  })
  accountId: string;

  @IsNotEmpty()
  @IsString()
  @ApiProperty({
    description: "The timestamp of the activity",
    example: "2024-01-01T00:00:00Z",
  })
  activityTimestamp: string;

  @IsNotEmpty()
  @IsString()
  @ApiProperty({
    description: "The title of the activity",
    example: "Meeting with John Doe",
  })
  title: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The description of the activity",
    example: "Meeting with John Doe",
  })
  description?: string;

  @IsOptional()
  @IsNumber()
  @ApiPropertyOptional({
    description: "The duration of the activity in minutes",
    example: 60,
  })
  duration?: number;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The location of the activity",
    example: "New York, NY",
  })
  location?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The identifier / value of the Type attribute of the activity",
    example: "T123",
  })
  type?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description:
      "The identifier / value of the Status attribute of the activity",
    example: "S123",
  })
  status?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ApiPropertyOptional({
    description: "The identifiers of the participants of the activity",
    example: ["U123", "U124"],
  })
  participants?: string[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ApiPropertyOptional({
    description: "The URLs of the attachments to attach to the note",
    example: [
      "https://example.com/attachment1.jpg",
      "https://example.com/attachment2.jpg",
    ],
  })
  attachmentUrls?: string[];

  @IsOptional()
  @IsObject()
  @ApiPropertyOptional({
    description: "The metadata of the activity",
    example: {},
  })
  metadata?: Record<string, any>;
}

export class UpdateAccountActivityDto {
  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The title of the activity",
    example: "Meeting with John Doe",
  })
  title?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The description of the activity",
    example: "Meeting with John Doe",
  })
  description?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The timestamp of the activity",
    example: "2024-01-01T00:00:00Z",
  })
  activityTimestamp?: string;

  @IsOptional()
  @IsNumber()
  @ApiPropertyOptional({
    description: "The duration of the activity in minutes",
    example: 60,
  })
  duration?: number;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The location of the activity",
    example: "New York, NY",
  })
  location?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The identifier / value of the Type attribute of the activity",
    example: "T123",
  })
  type?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description:
      "The identifier / value of the Status attribute of the activity",
    example: "S123",
  })
  status?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ApiPropertyOptional({
    description: "The identifiers of the participants of the activity",
    example: ["U123", "U124"],
  })
  participants?: string[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ApiPropertyOptional({
    description: "The URLs of the attachments to attach to the note",
    example: [
      "https://example.com/attachment1.jpg",
      "https://example.com/attachment2.jpg",
    ],
  })
  attachmentUrls?: string[];

  @IsOptional()
  @IsObject()
  @ApiPropertyOptional({
    description: "The metadata of the activity",
    example: {},
  })
  metadata?: Record<string, any>;
}
