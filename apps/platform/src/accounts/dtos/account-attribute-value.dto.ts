import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { AccountAttributeType } from "@repo/thena-platform-entities";
import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
} from "class-validator";

class CommonAttributeConfiguration {
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description: "Hero icon name of the account attribute value",
    example: "camera",
  })
  icon?: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description: "HEX color of the account attribute value",
    example: "#000000",
  })
  color?: string;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({
    description:
      "Whether the account attribute value is closed. Applicable only for task_status attribute type",
    example: false,
  })
  isClosed?: boolean;

  @IsObject()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The metadata of the account attribute value",
    example: {},
  })
  metadata?: Record<string, any>;
}

export class CreateAccountAttributeValueDto extends CommonAttributeConfiguration {
  @IsEnum(AccountAttributeType)
  @IsNotEmpty()
  @ApiProperty({
    description: "Attribute type",
    example: "account_status",
    enum: AccountAttributeType,
  })
  attribute: AccountAttributeType;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: "Attribute value",
    example: "PROSPECT",
  })
  value: string;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({
    description: "Whether the value is default for the attribute",
    example: true,
  })
  isDefault: boolean;
}

export class FindAccountAttributeValueDto {
  @IsEnum(AccountAttributeType)
  @IsNotEmpty()
  @ApiProperty({
    description: "Attribute type",
    example: "account_status",
    enum: AccountAttributeType,
  })
  attribute: AccountAttributeType;
}

export class UpdateAccountAttributeValueDto extends CommonAttributeConfiguration {
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description: "Attribute value",
    example: "PROSPECT",
  })
  value?: string;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({
    description: "Whether the attribute is default",
    example: false,
  })
  isDefault?: boolean;
}
