import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { AccountNoteVisibility } from "@repo/thena-platform-entities";
import { Transform } from "class-transformer";
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  Max,
  <PERSON>,
} from "class-validator";

export class FindAccountNoteDto {
  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The identifier of the account to find notes for",
    example: "A123",
  })
  accountId?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The identifier / value of the type attribute of the note",
    example: "T123",
  })
  type?: string;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  @Min(0)
  @ApiPropertyOptional({ description: "The page number" })
  page?: number;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  @Min(1)
  @Max(100)
  @ApiPropertyOptional({ description: "The limit number of notes to fetch" })
  limit?: number;
}

export class CreateAccountNoteDto {
  @IsNotEmpty()
  @IsString()
  @ApiProperty({
    description: "The identifier of the account to create the note for",
    example: "A123",
  })
  accountId: string;

  @IsNotEmpty()
  @IsString()
  @ApiProperty({
    description: "The content of the note",
    example: "This is a note",
  })
  content: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The identifier / value of the Type attribute of the activity",
    example: "T123",
  })
  type?: string;

  @IsOptional()
  @IsEnum(AccountNoteVisibility)
  @ApiPropertyOptional({
    description: "The visibility of the note",
    example: AccountNoteVisibility.PRIVATE,
  })
  visibility?: AccountNoteVisibility;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ApiPropertyOptional({
    description: "The URLs of the attachments to attach to the note",
    example: [
      "https://example.com/attachment1.jpg",
      "https://example.com/attachment2.jpg",
    ],
  })
  attachmentUrls?: string[];

  @IsOptional()
  @IsObject()
  @ApiPropertyOptional({
    description: "The metadata of the note",
  })
  metadata?: Record<string, any>;
}

export class UpdateAccountNoteDto {
  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The content of the note",
    example: "This is a note",
  })
  content?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The identifier / value of the type attribute of the note",
    example: "T123",
  })
  type?: string;

  @IsOptional()
  @IsEnum(AccountNoteVisibility)
  @ApiPropertyOptional({
    description: "The visibility of the note",
    example: AccountNoteVisibility.PRIVATE,
  })
  visibility?: AccountNoteVisibility;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ApiPropertyOptional({
    description: "The URLs of the attachments to attach to the note",
    example: [
      "https://example.com/attachment1.jpg",
      "https://example.com/attachment2.jpg",
    ],
  })
  attachmentUrls?: string[];

  @IsOptional()
  @IsObject()
  @ApiPropertyOptional({
    description: "The metadata of the note",
    example: {},
  })
  metadata?: Record<string, any>;
}
