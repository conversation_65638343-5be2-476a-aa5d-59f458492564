import { Processor, WorkerHost } from "@nestjs/bullmq";
import { Inject, Injectable } from "@nestjs/common";
import { ILogger } from "@repo/nestjs-commons/logger";
import { SNSPublisherService } from "@repo/thena-eventbridge";
import { Job } from "bullmq";
import * as rTracer from "cls-rtracer";
import { v4 as uuidv4 } from "uuid";
import { ConfigKeys, ConfigService } from "../../config/config.service";
import { QueueNames } from "../../constants/queue.constants";

@Injectable()
@Processor(QueueNames.ACCOUNTS_SNS_PUBLISHER)
export class AccountsSNSPublisher extends WorkerHost {
  constructor(
    @Inject("ACCOUNTS_SNS_PUBLISHER")
    private readonly snsPublisherService: SNSPublisherService,
    @Inject("CustomLogger")
    private readonly logger: ILogger,
    private readonly configService: ConfigService,
  ) {
    super();
  }

  async process(job: Job) {
    const { event, eventData, user, reqId } = job.data;
    await rTracer.runWithId(async () => {
      this.logger.log(
        `BullMQ Queue: ${QueueNames.ACCOUNTS_SNS_PUBLISHER} - Processing SNS message for job ${job.id}`,
      );

      await this.snsPublisherService.publishSNSMessage({
        subject: event,
        message: JSON.stringify(eventData),
        topicArn: this.configService.get(ConfigKeys.AWS_SNS_ACCOUNTS_TOPIC_ARN),
        messageAttributes: {
          event_name: event,
          event_id: (reqId as { reqId: string })?.reqId ?? uuidv4(),
          event_timestamp: Math.floor(Date.now() / 1000).toString(),
          context_user_id: user.uid,
          context_user_type: user.userType,
          context_organization_id: user.orgUid,
        },
        messageGroupId: user.orgUid,
      });
    }, reqId);
  }
}
