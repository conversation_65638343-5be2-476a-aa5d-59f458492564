import { <PERSON>ada<PERSON> } from "@grpc/grpc-js";
import { Controller, UseGuards } from "@nestjs/common";
import { GrpcMethod } from "@nestjs/microservices";
import { handleRpcError } from "@repo/nestjs-commons/errors";
import { GrpcAuthGuard } from "@repo/nestjs-commons/guards";
import {
  extractUserMetadata,
  extractWorkflowContextFromMetadata,
} from "@repo/nestjs-commons/utils";
import { accounts } from "@repo/shared-proto";
import {
  CreateAccountDto,
  FindAllAccountsDto,
  UpdateAccountDto,
} from "../../dtos/account.dto";
import { AccountResponseDto } from "../../dtos/response/account.dto";
import { AccountsService } from "../../services/accounts.service";

@Controller()
@UseGuards(GrpcAuthGuard)
export class AccountsGrpcController {
  constructor(private readonly accountsService: AccountsService) {}

  private toGrpcAccount(dto: AccountResponseDto): accounts.Account {
    return {
      ...dto,
      customFieldValues: dto.customFieldValues?.map((cfv) => ({
        customFieldId: cfv.customFieldId,
        data: cfv.data,
        metadata: cfv.metadata ? JSON.stringify(cfv.metadata) : undefined,
      })),
      createdAt: dto.createdAt,
      updatedAt: dto.updatedAt,
    };
  }

  @GrpcMethod(accounts.ACCOUNTS_SERVICE_NAME, "GetAccounts")
  async getAccounts(
    request: accounts.GetAccountsRequest,
    metadata: Metadata,
  ): Promise<accounts.GetAccountsResponse> {
    try {
      const { orgId } = extractUserMetadata(metadata);

      const query: FindAllAccountsDto = {
        source: request.source,
        status: request.status,
        classification: request.classification,
        health: request.health,
        industry: request.industry,
        accountOwnerId: request.accountOwnerId,
        page: request.page,
        limit: request.limit,
      };

      const accounts = await this.accountsService.findAllAccounts(orgId, query);
      const accountDtos = accounts.results.map((account) =>
        this.toGrpcAccount(AccountResponseDto.fromEntity(account)),
      );

      return {
        data: accountDtos,
      };
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(accounts.ACCOUNTS_SERVICE_NAME, "GetAccountDetails")
  async getAccountDetails(
    request: accounts.GetAccountDetailsRequest,
    metadata: Metadata,
  ): Promise<accounts.Account> {
    try {
      const user = extractUserMetadata(metadata);

      const account = await this.accountsService.findAccountDetails(
        user,
        request.id,
      );
      return this.toGrpcAccount(AccountResponseDto.fromEntity(account));
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(accounts.ACCOUNTS_SERVICE_NAME, "CreateAccount")
  async createAccount(
    request: accounts.CreateAccountRequest,
    metadata: Metadata,
  ): Promise<accounts.Account> {
    try {
      const user = extractUserMetadata(metadata);

      const workflowContext = extractWorkflowContextFromMetadata(metadata);

      const createDto: CreateAccountDto = {
        name: request.name,
        description: request.description,
        source: request.source,
        logo: request.logo,
        status: request.status,
        classification: request.classification,
        health: request.health,
        industry: request.industry,
        primaryDomain: request.primaryDomain,
        secondaryDomain: request.secondaryDomain,
        accountOwnerId: request.accountOwnerId,
        annualRevenue: request.annualRevenue,
        employees: request.employees,
        website: request.website,
        billingAddress: request.billingAddress,
        shippingAddress: request.shippingAddress,
        customFieldValues: request.customFieldValues?.map((cfv) => ({
          customFieldId: cfv.customFieldId,
          data: cfv.data,
          metadata: cfv.metadata ? JSON.parse(cfv.metadata) : {},
        })),
        addExistingUsersToAccountContacts:
          request.addExistingUsersToAccountContacts,
        ...(workflowContext.workflowId
          ? {
              metadata: {
                workflowExecutions: [workflowContext],
              },
            }
          : {}),
      };

      const account = await this.accountsService.createAccount(user, createDto);
      return this.toGrpcAccount(AccountResponseDto.fromEntity(account));
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(accounts.ACCOUNTS_SERVICE_NAME, "UpdateAccount")
  async updateAccount(
    request: accounts.UpdateAccountRequest,
    metadata: Metadata,
  ): Promise<accounts.Account> {
    try {
      const user = extractUserMetadata(metadata);

      const workflowContext = extractWorkflowContextFromMetadata(metadata);

      const updateDto: UpdateAccountDto = {
        name: request.name,
        description: request.description,
        source: request.source,
        logo: request.logo,
        status: request.status,
        classification: request.classification,
        health: request.health,
        industry: request.industry,
        primaryDomain: request.primaryDomain,
        secondaryDomain: request.secondaryDomain,
        accountOwnerId: request.accountOwnerId,
        annualRevenue: request.annualRevenue,
        employees: request.employees,
        website: request.website,
        billingAddress: request.billingAddress,
        shippingAddress: request.shippingAddress,
        customFieldValues: request.customFieldValues?.map((cfv) => ({
          customFieldId: cfv.customFieldId,
          data: cfv.data,
          metadata: cfv.metadata ? JSON.parse(cfv.metadata) : {},
        })),
        addExistingUsersToAccountContacts:
          request.addExistingUsersToAccountContacts,
        ...(workflowContext.workflowId
          ? {
              metadata: {
                workflowExecutions: [workflowContext],
              },
            }
          : {}),
      };

      const account = await this.accountsService.updateAccount(
        request.id,
        user,
        updateDto,
      );
      return this.toGrpcAccount(AccountResponseDto.fromEntity(account));
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(accounts.ACCOUNTS_SERVICE_NAME, "DeleteAccount")
  async deleteAccount(
    request: accounts.DeleteAccountRequest,
    metadata: Metadata,
  ): Promise<void> {
    try {
      const user = extractUserMetadata(metadata);

      const workflowContext = extractWorkflowContextFromMetadata(metadata);

      await this.accountsService.deleteAccount(
        request.id,
        user,
        workflowContext.workflowId
          ? { workflowExecutions: [workflowContext] }
          : undefined,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(accounts.ACCOUNTS_SERVICE_NAME, "FilterAccountsByPrimaryDomains")
  async filterAccountsByPrimaryDomains(
    request: accounts.FilterAccountsByPrimaryDomainsRequest,
    metadata: Metadata,
  ): Promise<accounts.FilterAccountsByPrimaryDomainsResponse> {
    try {
      const user = extractUserMetadata(metadata);

      const accounts =
        await this.accountsService.findAllAccountsByPrimaryDomains(
          request.primaryDomains,
          user.orgId,
        );

      return {
        data: accounts.map((account) =>
          this.toGrpcAccount(AccountResponseDto.fromEntity(account)),
        ),
      };
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(accounts.ACCOUNTS_SERVICE_NAME, "FilterAccountsByIds")
  async filterAccountsByIds(
    request: accounts.FilterAccountsByIdsRequest,
    metadata: Metadata,
  ): Promise<accounts.FilterAccountsByIdsResponse> {
    try {
      const user = extractUserMetadata(metadata);

      const accounts = await this.accountsService.findAccountsByPublicIds(
        request.ids,
        user.orgId,
      );

      return {
        data: accounts.map((account) =>
          this.toGrpcAccount(AccountResponseDto.fromEntity(account)),
        ),
      };
    } catch (error) {
      handleRpcError(error);
    }
  }
}
