import { <PERSON>ada<PERSON> } from "@grpc/grpc-js";
import { Controller, UseGuards } from "@nestjs/common";
import { GrpcMethod } from "@nestjs/microservices";
import { handleRpcError } from "@repo/nestjs-commons/errors";
import { GrpcAuthGuard } from "@repo/nestjs-commons/guards";
import {
  extractUserMetadata,
  extractWorkflowContextFromMetadata,
} from "@repo/nestjs-commons/utils";
import { accounts } from "@repo/shared-proto";
import {
  BulkCreateCustomerContactsDto,
  CreateCustomerContactDto,
  FindAllCustomerContactsDto,
  UpdateCustomerContactDto,
} from "../../dtos/customer-contact.dto";
import { CustomerContactResponseDto } from "../../dtos/response/customer-contact.dto";
import { CustomerContactActionService } from "../../services/customer-contact.action.service";

/**
 * Helper function to convert CustomerContactResponseDto to proto CustomerContact
 */
function convertToProtoCustomerContact(
  dto: CustomerContactResponseDto,
): accounts.CustomerContact {
  // Create a new object with the structure expected by the proto interface
  const protoContact: any = {
    ...dto,
    // Convert custom field values to the format expected by proto
    customFieldValues:
      dto.customFieldValues?.map((cf) => ({
        customFieldId: cf.customFieldId,
        data: cf.data,
        // Convert metadata object to string if needed
        metadata: cf.metadata ? JSON.stringify(cf.metadata) : undefined,
      })) || [],
    // Convert metadata object to map<string, string> if needed
    metadata: dto.metadata || {},
  };

  return protoContact as accounts.CustomerContact;
}

@Controller()
@UseGuards(GrpcAuthGuard)
export class CustomerContactsGrpcController {
  constructor(
    private readonly customerContactActionService: CustomerContactActionService,
  ) {}

  @GrpcMethod(accounts.CUSTOMER_CONTACTS_SERVICE_NAME, "GetCustomerContacts")
  async getCustomerContacts(
    request: accounts.GetCustomerContactsRequest,
    metadata: Metadata,
  ): Promise<accounts.GetCustomerContactsResponse> {
    try {
      const user = extractUserMetadata(metadata);

      const query: FindAllCustomerContactsDto = {
        accountId: request.accountId,
        contactType: request.contactType,
        page: request.page,
        limit: request.limit,
      };

      const contacts =
        await this.customerContactActionService.findCustomerContacts(
          user,
          query,
        );
      const contactDtos = contacts.results.map((contact) =>
        convertToProtoCustomerContact(
          CustomerContactResponseDto.fromEntity(contact),
        ),
      );

      return {
        data: contactDtos,
      };
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(accounts.CUSTOMER_CONTACTS_SERVICE_NAME, "CreateCustomerContact")
  async createCustomerContact(
    request: accounts.CreateCustomerContactRequest,
    metadata: Metadata,
  ): Promise<accounts.CustomerContact> {
    try {
      const user = extractUserMetadata(metadata);

      const workflowContext = extractWorkflowContextFromMetadata(metadata);

      // Using type assertion to include avatarUrl while maintaining compatibility
      const createDto: CreateCustomerContactDto = {
        firstName: request.firstName,
        lastName: request.lastName,
        email: request.email,
        phoneNumber: request.phoneNumber,
        contactType: request.contactType,
        accountIds: request.accountIds,
        avatarUrl: (request as any).avatarUrl,
        ...(workflowContext.workflowId
          ? {
              metadata: {
                workflowExecutions: [workflowContext],
              },
            }
          : {}),
      };

      const contact =
        await this.customerContactActionService.createCustomerContact(
          user,
          createDto,
        );
      return convertToProtoCustomerContact(
        CustomerContactResponseDto.fromEntity(contact),
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(accounts.CUSTOMER_CONTACTS_SERVICE_NAME, "UpdateCustomerContact")
  async updateCustomerContact(
    request: accounts.UpdateCustomerContactRequest,
    metadata: Metadata,
  ): Promise<accounts.CustomerContact> {
    try {
      const user = extractUserMetadata(metadata);

      const workflowContext = extractWorkflowContextFromMetadata(metadata);

      const updateDto: UpdateCustomerContactDto = {
        firstName: request.firstName,
        lastName: request.lastName,
        email: request.email,
        phoneNumber: request.phoneNumber,
        contactType: request.contactType,
        accountIds: request.accountIds,
        avatarUrl: request.avatarUrl,
        ...(workflowContext.workflowId
          ? {
              metadata: {
                workflowExecutions: [workflowContext],
              },
            }
          : {}),
      };

      const contact =
        await this.customerContactActionService.updateCustomerContact(
          user,
          request.contactId,
          updateDto,
        );
      return convertToProtoCustomerContact(
        CustomerContactResponseDto.fromEntity(contact),
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(accounts.CUSTOMER_CONTACTS_SERVICE_NAME, "DeleteCustomerContact")
  async deleteCustomerContact(
    request: accounts.DeleteCustomerContactRequest,
    metadata: Metadata,
  ): Promise<void> {
    try {
      const user = extractUserMetadata(metadata);

      const workflowContext = extractWorkflowContextFromMetadata(metadata);

      await this.customerContactActionService.deleteCustomerContact(
        user,
        request.contactId,
        workflowContext.workflowId
          ? { workflowExecutions: [workflowContext] }
          : undefined,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    accounts.CUSTOMER_CONTACTS_SERVICE_NAME,
    "BulkCreateCustomerContacts",
  )
  async bulkCreateCustomerContacts(
    request: accounts.BulkCreateCustomerContactsRequest,
    metadata: Metadata,
  ): Promise<accounts.BulkCreateCustomerContactsResponse> {
    try {
      const user = extractUserMetadata(metadata);

      const bulkCreateDto: BulkCreateCustomerContactsDto = {
        contacts: request.contacts.map((contact) => ({
          firstName: contact.firstName,
          lastName: contact.lastName,
          email: contact.email,
          phoneNumber: contact.phoneNumber,
        })),
        accountIds: request.accountIds,
        contactType: request.contactType,
      };

      const result =
        await this.customerContactActionService.bulkCreateCustomerContacts(
          user,
          bulkCreateDto,
        );
      return {
        total: result.total,
        created: result.created,
        skipped: result.skipped,
      };
    } catch (error) {
      handleRpcError(error);
    }
  }
}
