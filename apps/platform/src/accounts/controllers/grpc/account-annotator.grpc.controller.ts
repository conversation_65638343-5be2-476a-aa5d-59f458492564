import { Metadata } from "@grpc/grpc-js";
import { Controller, UseGuards } from "@nestjs/common";
import { GrpcMethod } from "@nestjs/microservices";
import { handleRpcError } from "@repo/nestjs-commons/errors";
import { GrpcAuthGuard } from "@repo/nestjs-commons/guards";
import { extractUserMetadata } from "@repo/nestjs-commons/utils";
import { accounts } from "@repo/shared-proto";
import { AccountAttributeType } from "@repo/thena-platform-entities";
import { AccountAnnotatorService } from "../../services/account-annotator.service";

@Controller()
@UseGuards(GrpcAuthGuard)
export class AccountAnnotatorGrpcController {
  constructor(
    private readonly accountAnnotatorService: AccountAnnotatorService,
  ) {}

  @GrpcMethod(
    accounts.ACCOUNT_ANNOTATOR_SERVICE_NAME,
    "GetAccountFieldMetadata",
  )
  getAccountFieldMetadata(): accounts.GetAccountFieldMetadataResponse {
    try {
      return this.accountAnnotatorService.getAccountFieldMetadata();
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    accounts.ACCOUNT_ANNOTATOR_SERVICE_NAME,
    "GetAccountAttributeValueFieldMetadata",
  )
  async getAccountAttributeValueFieldMetadata(
    data: accounts.GetAccountAttributeValueFieldMetadataRequest,
    metadata: Metadata,
  ): Promise<accounts.GetAccountAttributeValueFieldMetadataResponse> {
    try {
      const { orgId } = extractUserMetadata(metadata);
      return await this.accountAnnotatorService.getAccountAttributeValueFieldMetadata(
        data.attributeType as AccountAttributeType,
        orgId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(accounts.ACCOUNT_ANNOTATOR_SERVICE_NAME, "GetAccountData")
  async getAccountData(
    request: accounts.GetAccountDataRequest,
    metadata: Metadata,
  ): Promise<accounts.GetAccountDataResponse> {
    try {
      const { orgId } = extractUserMetadata(metadata);
      return await this.accountAnnotatorService.getAccountData(
        request.accountId,
        request.relations,
        orgId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    accounts.ACCOUNT_ANNOTATOR_SERVICE_NAME,
    "GetAccountAttributeValueData",
  )
  async getAccountAttributeValueData(
    request: accounts.GetAccountAttributeValueDataRequest,
    metadata: Metadata,
  ): Promise<accounts.GetAccountAttributeValueDataResponse> {
    try {
      const { orgId } = extractUserMetadata(metadata);
      return await this.accountAnnotatorService.getAccountAttributeValueData(
        request.attributeValueId,
        request.relations,
        orgId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    accounts.ACCOUNT_ANNOTATOR_SERVICE_NAME,
    "GetCustomerContactFieldMetadata",
  )
  getCustomerContactFieldMetadata(): accounts.GetCustomerContactFieldMetadataResponse {
    try {
      return this.accountAnnotatorService.getCustomerContactFieldMetadata();
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(accounts.ACCOUNT_ANNOTATOR_SERVICE_NAME, "GetCustomerContactData")
  async getCustomerContactData(
    request: accounts.GetCustomerContactDataRequest,
    metadata: Metadata,
  ): Promise<accounts.GetCustomerContactDataResponse> {
    try {
      const { orgId } = extractUserMetadata(metadata);
      return await this.accountAnnotatorService.getCustomerContactData(
        request.customerContactId,
        request.relations,
        orgId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }
}
