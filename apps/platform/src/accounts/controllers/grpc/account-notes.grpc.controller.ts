import { Metadata } from "@grpc/grpc-js";
import { Controller, UseGuards } from "@nestjs/common";
import { GrpcMethod } from "@nestjs/microservices";
import { handleRpcError } from "@repo/nestjs-commons/errors";
import { GrpcAuthGuard } from "@repo/nestjs-commons/guards";
import {
  extractUserMetadata,
  extractWorkflowContextFromMetadata,
} from "@repo/nestjs-commons/utils";
import { accounts } from "@repo/shared-proto";
import { AccountNoteVisibility } from "@repo/thena-platform-entities";
import {
  CreateAccountNoteDto,
  FindAccountNoteDto,
  UpdateAccountNoteDto,
} from "../../dtos/account-note.dto";
import { AccountNoteResponseDto } from "../../dtos/response/account-note.dto";
import { AccountNoteActionService } from "../../services/account-note.action.service";

@Controller()
@UseGuards(GrpcAuthGuard)
export class AccountNotesGrpcController {
  constructor(
    private readonly accountNoteActionService: AccountNoteActionService,
  ) {}

  @GrpcMethod(accounts.ACCOUNT_NOTES_SERVICE_NAME, "GetAccountNotes")
  async getAccountNotes(
    request: accounts.GetAccountNotesRequest,
    metadata: Metadata,
  ): Promise<accounts.GetAccountNotesResponse> {
    try {
      const user = extractUserMetadata(metadata);

      const query: FindAccountNoteDto = {
        accountId: request.accountId,
        type: request.type,
        page: request.page,
        limit: request.limit,
      };

      const notes = await this.accountNoteActionService.findAllAccountNotes(
        user,
        query,
      );

      const notesResponse = notes.results.map((note) => {
        const dto = AccountNoteResponseDto.fromEntity(note);
        return this.convertToProtoAccountNote(dto);
      });

      return {
        data: notesResponse,
      };
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(accounts.ACCOUNT_NOTES_SERVICE_NAME, "CreateAccountNote")
  async createAccountNote(
    request: accounts.CreateAccountNoteRequest,
    metadata: Metadata,
  ): Promise<accounts.AccountNote> {
    try {
      const user = extractUserMetadata(metadata);

      const workflowContext = extractWorkflowContextFromMetadata(metadata);

      const createDto: CreateAccountNoteDto = {
        accountId: request.accountId,
        content: request.content,
        type: request.type,
        visibility: request.visibility as AccountNoteVisibility,
        attachmentUrls: request.attachmentUrls,
        ...(workflowContext.workflowId
          ? {
              metadata: {
                workflowExecutions: [workflowContext],
              },
            }
          : {}),
      };

      const note = await this.accountNoteActionService.createAccountNote(
        user,
        createDto,
      );

      const noteResponse = AccountNoteResponseDto.fromEntity(note);
      return this.convertToProtoAccountNote(noteResponse);
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(accounts.ACCOUNT_NOTES_SERVICE_NAME, "UpdateAccountNote")
  async updateAccountNote(
    request: accounts.UpdateAccountNoteRequest,
    metadata: Metadata,
  ): Promise<accounts.AccountNote> {
    try {
      const user = extractUserMetadata(metadata);

      const workflowContext = extractWorkflowContextFromMetadata(metadata);

      const updateDto: UpdateAccountNoteDto = {
        content: request.content,
        type: request.type,
        visibility: request.visibility as AccountNoteVisibility,
        attachmentUrls: request.attachmentUrls,
        ...(workflowContext.workflowId
          ? {
              metadata: {
                workflowExecutions: [workflowContext],
              },
            }
          : {}),
      };

      const note = await this.accountNoteActionService.updateAccountNote(
        user,
        request.noteId,
        updateDto,
      );

      const noteResponse = AccountNoteResponseDto.fromEntity(note);
      return this.convertToProtoAccountNote(noteResponse);
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(accounts.ACCOUNT_NOTES_SERVICE_NAME, "DeleteAccountNote")
  async deleteAccountNote(
    request: accounts.DeleteAccountNoteRequest,
    metadata: Metadata,
  ): Promise<void> {
    try {
      const user = extractUserMetadata(metadata);

      const workflowContext = extractWorkflowContextFromMetadata(metadata);

      await this.accountNoteActionService.deleteAccountNote(
        user,
        request.noteId,
        workflowContext.workflowId
          ? { workflowExecutions: [workflowContext] }
          : undefined,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  // Helper method to properly convert between DTO and proto type
  private convertToProtoAccountNote(
    dto: AccountNoteResponseDto,
  ): accounts.AccountNote {
    return {
      id: dto.id,
      accountId: dto.accountId,
      account: dto.account,
      content: dto.content,
      type: dto.type || "",
      typeId: dto.typeId || "",
      typeConfiguration: dto.typeConfiguration as Record<string, string>,
      visibility: dto.visibility,
      attachments:
        dto.attachments?.map((att) => ({
          id: att.id,
          url: att.url,
          name: att.name || "",
          size: att.size || 0,
          contentType: att.contentType || "",
          createdAt: att.createdAt || "",
        })) || [],
      author: dto.author || "",
      authorId: dto.authorId || "",
      authorEmail: dto.authorEmail || "",
      createdAt: dto.createdAt,
      updatedAt: dto.updatedAt,
    };
  }
}
