import { Metadata } from "@grpc/grpc-js";
import { Controller, UseGuards } from "@nestjs/common";
import { GrpcMethod } from "@nestjs/microservices";
import { handleRpcError } from "@repo/nestjs-commons/errors";
import { GrpcAuthGuard } from "@repo/nestjs-commons/guards";
import {
  extractUserMetadata,
  extractWorkflowContextFromMetadata,
} from "@repo/nestjs-commons/utils";
import { accounts } from "@repo/shared-proto";
import {
  CreateAccountRelationshipDto,
  CreateAccountRelationshipTypeDto,
  FindAccountRelationshipDto,
  UpdateAccountRelationshipDto,
  UpdateAccountRelationshipTypeDto,
} from "../../dtos/account-relationship.dto";
import {
  AccountRelationshipResponseDto,
  AccountRelationshipTypeResponseDto,
} from "../../dtos/response/account-relationship.dto";
import { AccountRelationshipActionService } from "../../services/account-relationship.action.service";
@Controller()
@UseGuards(GrpcAuthGuard)
export class AccountRelationshipsGrpcController {
  constructor(
    private readonly accountRelationshipActionService: AccountRelationshipActionService,
  ) {}

  @GrpcMethod(
    accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME,
    "GetAccountRelationshipTypes",
  )
  async getAccountRelationshipTypes(
    metadata: Metadata,
  ): Promise<accounts.GetAccountRelationshipTypesResponse> {
    try {
      const user = extractUserMetadata(metadata);

      const types =
        await this.accountRelationshipActionService.findAllAccountRelationshipTypes(
          user.orgId,
        );
      const typeDtos = types.map((type) =>
        AccountRelationshipTypeResponseDto.fromEntity(type),
      ) as accounts.AccountRelationshipType[];

      return {
        data: typeDtos,
      };
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME,
    "CreateAccountRelationshipType",
  )
  async createAccountRelationshipType(
    request: accounts.CreateAccountRelationshipTypeRequest,
    metadata: Metadata,
  ): Promise<accounts.AccountRelationshipType> {
    try {
      const user = extractUserMetadata(metadata);

      const workflowContext = extractWorkflowContextFromMetadata(metadata);

      const createDto: CreateAccountRelationshipTypeDto = {
        name: request.name,
        inverseRelationshipId: request.inverseRelationshipId,
        ...(workflowContext.workflowId
          ? {
              metadata: {
                workflowExecutions: [workflowContext],
              },
            }
          : {}),
      };

      const type =
        await this.accountRelationshipActionService.createAccountRelationshipType(
          user,
          createDto,
        );
      return AccountRelationshipTypeResponseDto.fromEntity(type);
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME,
    "UpdateAccountRelationshipType",
  )
  async updateAccountRelationshipType(
    request: accounts.UpdateAccountRelationshipTypeRequest,
    metadata: Metadata,
  ): Promise<accounts.AccountRelationshipType> {
    try {
      const user = extractUserMetadata(metadata);

      const workflowContext = extractWorkflowContextFromMetadata(metadata);

      const updateDto: UpdateAccountRelationshipTypeDto = {
        name: request.name,
        inverseRelationshipId: request.inverseRelationshipId,
        ...(workflowContext.workflowId
          ? {
              metadata: {
                workflowExecutions: [workflowContext],
              },
            }
          : {}),
      };

      const type =
        await this.accountRelationshipActionService.updateAccountRelationshipType(
          user,
          request.id,
          updateDto,
        );
      return AccountRelationshipTypeResponseDto.fromEntity(type);
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME,
    "DeleteAccountRelationshipType",
  )
  async deleteAccountRelationshipType(
    request: accounts.DeleteAccountRelationshipTypeRequest,
    metadata: Metadata,
  ): Promise<void> {
    try {
      const user = extractUserMetadata(metadata);

      const workflowContext = extractWorkflowContextFromMetadata(metadata);

      await this.accountRelationshipActionService.deleteAccountRelationshipType(
        user,
        request.id,
        workflowContext.workflowId
          ? { workflowExecutions: [workflowContext] }
          : undefined,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME,
    "GetAccountRelationships",
  )
  async getAccountRelationships(
    request: accounts.GetAccountRelationshipsRequest,
    metadata: Metadata,
  ): Promise<accounts.GetAccountRelationshipsResponse> {
    try {
      const user = extractUserMetadata(metadata);

      const query: FindAccountRelationshipDto = {
        accountId: request.accountId,
        relationshipTypeId: request.relationshipType,
        page: request.page,
        limit: request.limit,
      };

      const relationships =
        await this.accountRelationshipActionService.findAllAccountRelationships(
          user,
          query,
        );
      const relationshipDtos = relationships.results.map((relationship) =>
        AccountRelationshipResponseDto.fromEntity(relationship),
      ) as accounts.AccountRelationship[];

      return {
        data: relationshipDtos,
      };
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME,
    "CreateAccountRelationship",
  )
  async createAccountRelationship(
    request: accounts.CreateAccountRelationshipRequest,
    metadata: Metadata,
  ): Promise<accounts.AccountRelationship> {
    try {
      const user = extractUserMetadata(metadata);

      const workflowContext = extractWorkflowContextFromMetadata(metadata);

      const createDto: CreateAccountRelationshipDto = {
        accountId: request.accountId,
        relatedAccountId: request.relatedAccountId,
        relationshipType: request.relationshipType,
        ...(workflowContext.workflowId
          ? {
              metadata: {
                workflowExecutions: [workflowContext],
              },
            }
          : {}),
      };

      const relationship =
        await this.accountRelationshipActionService.createAccountRelationship(
          user,
          createDto,
        );
      return AccountRelationshipResponseDto.fromEntity(relationship);
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME,
    "UpdateAccountRelationship",
  )
  async updateAccountRelationship(
    request: accounts.UpdateAccountRelationshipRequest,
    metadata: Metadata,
  ): Promise<accounts.AccountRelationship> {
    try {
      const user = extractUserMetadata(metadata);

      const workflowContext = extractWorkflowContextFromMetadata(metadata);

      const updateDto: UpdateAccountRelationshipDto = {
        relationshipType: request.relationshipType,
        ...(workflowContext.workflowId
          ? {
              metadata: {
                workflowExecutions: [workflowContext],
              },
            }
          : {}),
      };

      const relationship =
        await this.accountRelationshipActionService.updateAccountRelationship(
          user,
          request.relationshipId,
          updateDto,
        );
      return AccountRelationshipResponseDto.fromEntity(relationship);
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME,
    "DeleteAccountRelationship",
  )
  async deleteAccountRelationship(
    request: accounts.DeleteAccountRelationshipRequest,
    metadata: Metadata,
  ): Promise<void> {
    try {
      const user = extractUserMetadata(metadata);

      const workflowContext = extractWorkflowContextFromMetadata(metadata);

      await this.accountRelationshipActionService.deleteAccountRelationship(
        user,
        request.relationshipId,
        workflowContext.workflowId
          ? { workflowExecutions: [workflowContext] }
          : undefined,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }
}
