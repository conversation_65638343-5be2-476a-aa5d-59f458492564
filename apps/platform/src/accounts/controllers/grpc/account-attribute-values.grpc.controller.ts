import { Metadata } from "@grpc/grpc-js";
import { Controller, UseGuards } from "@nestjs/common";
import { GrpcMethod } from "@nestjs/microservices";
import { handleRpcError } from "@repo/nestjs-commons/errors";
import { GrpcAuthGuard } from "@repo/nestjs-commons/guards";
import {
  extractUserMetadata,
  extractWorkflowContextFromMetadata,
} from "@repo/nestjs-commons/utils";
import { accounts } from "@repo/shared-proto";
import { AccountAttributeType } from "@repo/thena-platform-entities";
import {
  CreateAccountAttributeValueDto,
  FindAccountAttributeValueDto,
  UpdateAccountAttributeValueDto,
} from "../../dtos/account-attribute-value.dto";
import { AccountAttributeValueResponseDto } from "../../dtos/response/account-attribute-value.dto";
import { AccountAttributeValueActionService } from "../../services/account-attribute-value.action.service";

@Controller()
@UseGuards(GrpcAuthGuard)
export class AccountAttributeValuesGrpcController {
  constructor(
    private readonly accountAttributeValueActionService: AccountAttributeValueActionService,
  ) {}

  @GrpcMethod(
    accounts.ACCOUNT_ATTRIBUTE_VALUES_SERVICE_NAME,
    "GetAccountAttributeValues",
  )
  async getAccountAttributeValues(
    request: accounts.GetAccountAttributeValuesRequest,
    metadata: Metadata,
  ): Promise<accounts.GetAccountAttributeValuesResponse> {
    try {
      const user = extractUserMetadata(metadata);

      const query: FindAccountAttributeValueDto = {
        attribute: request.attribute as AccountAttributeType,
      };

      const attributeValues =
        await this.accountAttributeValueActionService.findAccountAttributeValues(
          user,
          query.attribute,
        );
      const attributeValueDtos = attributeValues.map((value) =>
        this.convertToProtoAccountAttributeValue(
          AccountAttributeValueResponseDto.fromEntity(value),
        ),
      );

      return {
        data: attributeValueDtos,
      };
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    accounts.ACCOUNT_ATTRIBUTE_VALUES_SERVICE_NAME,
    "CreateAccountAttributeValue",
  )
  async createAccountAttributeValue(
    request: accounts.CreateAccountAttributeValueRequest,
    metadata: Metadata,
  ): Promise<accounts.AccountAttributeValue> {
    try {
      const user = extractUserMetadata(metadata);

      const workflowContext = extractWorkflowContextFromMetadata(metadata);

      const createDto: CreateAccountAttributeValueDto = {
        attribute: request.attribute as AccountAttributeType,
        value: request.value,
        isDefault: request.isDefault,
        ...(workflowContext.workflowId
          ? {
              metadata: {
                workflowExecutions: [workflowContext],
              },
            }
          : {}),
      };

      const attributeValue =
        await this.accountAttributeValueActionService.createAccountAttributeValue(
          user,
          createDto,
        );
      return this.convertToProtoAccountAttributeValue(
        AccountAttributeValueResponseDto.fromEntity(attributeValue),
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    accounts.ACCOUNT_ATTRIBUTE_VALUES_SERVICE_NAME,
    "UpdateAccountAttributeValue",
  )
  async updateAccountAttributeValue(
    request: accounts.UpdateAccountAttributeValueRequest,
    metadata: Metadata,
  ): Promise<accounts.AccountAttributeValue> {
    try {
      const user = extractUserMetadata(metadata);

      const workflowContext = extractWorkflowContextFromMetadata(metadata);

      const updateDto: UpdateAccountAttributeValueDto = {
        value: request.value,
        isDefault: request.isDefault,
        ...(workflowContext.workflowId
          ? {
              metadata: {
                workflowExecutions: [workflowContext],
              },
            }
          : {}),
      };

      const attributeValue =
        await this.accountAttributeValueActionService.updateAccountAttributeValue(
          user,
          request.id,
          updateDto,
        );
      return this.convertToProtoAccountAttributeValue(
        AccountAttributeValueResponseDto.fromEntity(attributeValue),
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    accounts.ACCOUNT_ATTRIBUTE_VALUES_SERVICE_NAME,
    "DeleteAccountAttributeValue",
  )
  async deleteAccountAttributeValue(
    request: accounts.DeleteAccountAttributeValueRequest,
    metadata: Metadata,
  ): Promise<void> {
    try {
      const user = extractUserMetadata(metadata);
      const workflowContext = extractWorkflowContextFromMetadata(metadata);

      await this.accountAttributeValueActionService.deleteAccountAttributeValue(
        user,
        request.id,
        request.forceDelete,
        workflowContext.workflowId
          ? { workflowExecutions: [workflowContext] }
          : undefined,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  // Helper method to properly convert between DTO and proto type
  private convertToProtoAccountAttributeValue(
    dto: AccountAttributeValueResponseDto,
  ): accounts.AccountAttributeValue {
    return {
      id: dto.id,
      attribute: dto.attribute,
      value: dto.value,
      isDefault: dto.isDefault,
      configuration: dto.configuration as Record<string, string>,
      createdAt: dto.createdAt,
      updatedAt: dto.updatedAt,
    };
  }
}
