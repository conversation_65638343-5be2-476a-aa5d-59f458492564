import { Metadata } from "@grpc/grpc-js";
import { Controller, UseGuards } from "@nestjs/common";
import { GrpcMethod } from "@nestjs/microservices";
import { handleRpcError } from "@repo/nestjs-commons/errors";
import { GrpcAuthGuard } from "@repo/nestjs-commons/guards";
import {
  extractUserMetadata,
  extractWorkflowContextFromMetadata,
} from "@repo/nestjs-commons/utils";
import { accounts } from "@repo/shared-proto";
import {
  CreateAccountActivityDto,
  FindAccountActivityDto,
  UpdateAccountActivityDto,
} from "../../dtos/account-activity.dto";
import { AccountActivityResponseDto } from "../../dtos/response/account-activity.dto";
import { AccountActivityActionService } from "../../services/account-activity.action.service";

@Controller()
@UseGuards(GrpcAuthGuard)
export class AccountActivitiesGrpcController {
  constructor(
    private readonly accountActivityActionService: AccountActivityActionService,
  ) {}

  @GrpcMethod(accounts.ACCOUNT_ACTIVITIES_SERVICE_NAME, "GetAccountActivities")
  async getAccountActivities(
    request: accounts.GetAccountActivitiesRequest,
    metadata: Metadata,
  ): Promise<accounts.GetAccountActivitiesResponse> {
    try {
      const user = extractUserMetadata(metadata);

      const query: FindAccountActivityDto = {
        accountId: request.accountId,
        type: request.type,
        status: request.status,
        page: request.page,
        limit: request.limit,
      };

      const activities =
        await this.accountActivityActionService.findAccountActivities(
          user,
          query,
        );

      const activityDtos = activities.results.map((activity) => {
        const dto = AccountActivityResponseDto.fromEntity(activity);
        return this.convertToProtoAccountActivity(dto);
      });

      return {
        data: activityDtos,
      };
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(accounts.ACCOUNT_ACTIVITIES_SERVICE_NAME, "CreateAccountActivity")
  async createAccountActivity(
    request: accounts.CreateAccountActivityRequest,
    metadata: Metadata,
  ): Promise<accounts.AccountActivity> {
    try {
      const user = extractUserMetadata(metadata);

      const workflowContext = extractWorkflowContextFromMetadata(metadata);

      const createDto: CreateAccountActivityDto = {
        accountId: request.accountId,
        activityTimestamp: request.activityTimestamp,
        duration: request.duration,
        location: request.location,
        type: request.type,
        status: request.status,
        participants: request.participants,
        attachmentUrls: request.attachmentUrls,
        title: request.title,
        ...(workflowContext.workflowId
          ? {
              metadata: {
                workflowExecutions: [workflowContext],
              },
            }
          : {}),
      };

      const activity =
        await this.accountActivityActionService.createAccountActivity(
          user,
          createDto,
        );

      const dto = AccountActivityResponseDto.fromEntity(activity);
      return this.convertToProtoAccountActivity(dto);
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(accounts.ACCOUNT_ACTIVITIES_SERVICE_NAME, "UpdateAccountActivity")
  async updateAccountActivity(
    request: accounts.UpdateAccountActivityRequest,
    metadata: Metadata,
  ): Promise<accounts.AccountActivity> {
    try {
      const user = extractUserMetadata(metadata);

      const workflowContext = extractWorkflowContextFromMetadata(metadata);

      const updateDto: UpdateAccountActivityDto = {
        activityTimestamp: request.activityTimestamp,
        duration: request.duration,
        location: request.location,
        type: request.type,
        status: request.status,
        participants: request.participants,
        attachmentUrls: request.attachmentUrls,
        ...(workflowContext.workflowId
          ? {
              metadata: {
                workflowExecutions: [workflowContext],
              },
            }
          : {}),
      };

      const activity =
        await this.accountActivityActionService.updateAccountActivity(
          user,
          request.activityId,
          updateDto,
        );

      const dto = AccountActivityResponseDto.fromEntity(activity);
      return this.convertToProtoAccountActivity(dto);
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(accounts.ACCOUNT_ACTIVITIES_SERVICE_NAME, "DeleteAccountActivity")
  async deleteAccountActivity(
    request: accounts.DeleteAccountActivityRequest,
    metadata: Metadata,
  ): Promise<void> {
    try {
      const user = extractUserMetadata(metadata);

      const workflowContext = extractWorkflowContextFromMetadata(metadata);

      await this.accountActivityActionService.deleteAccountActivity(
        user,
        request.activityId,
        workflowContext.workflowId
          ? { workflowExecutions: [workflowContext] }
          : undefined,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    accounts.ACCOUNT_ACTIVITIES_SERVICE_NAME,
    "RemoveActivityAttachment",
  )
  async removeActivityAttachment(
    request: accounts.RemoveActivityAttachmentRequest,
    metadata: Metadata,
  ): Promise<void> {
    try {
      const user = extractUserMetadata(metadata);
      await this.accountActivityActionService.removeActivityAttachment(
        user,
        request.activityId,
        request.attachmentId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  private convertToProtoAccountActivity(
    dto: AccountActivityResponseDto,
  ): accounts.AccountActivity {
    return {
      id: dto.id,
      accountId: dto.accountId,
      account: dto.account,
      title: dto.title || "",
      description: dto.description || "",
      activityTimestamp: dto.activityTimestamp,
      duration: dto.duration || 0,
      location: dto.location || "",
      type: dto.type || "",
      typeId: dto.typeId || "",
      typeConfiguration: dto.typeConfiguration as Record<string, string>,
      status: dto.status || "",
      statusId: dto.statusId || "",
      statusConfiguration: dto.statusConfiguration as Record<string, string>,
      participants: dto.participants || [],
      creator: dto.creator || "",
      creatorId: dto.creatorId || "",
      creatorEmail: dto.creatorEmail || "",
      attachments:
        dto.attachments?.map((att) => ({
          id: att.id,
          url: att.url,
          name: att.name || "",
          size: att.size || 0,
          contentType: att.contentType || "",
          createdAt: att.createdAt || "",
        })) || [],
      createdAt: dto.createdAt,
      updatedAt: dto.updatedAt,
    };
  }
}
