import { Metadata } from "@grpc/grpc-js";
import { Controller, UseGuards } from "@nestjs/common";
import { GrpcMethod } from "@nestjs/microservices";
import { handleRpcError } from "@repo/nestjs-commons/errors";
import { GrpcAuthGuard } from "@repo/nestjs-commons/guards";
import {
  extractUserMetadata,
  extractWorkflowContextFromMetadata,
} from "@repo/nestjs-commons/utils";
import { accounts } from "@repo/shared-proto";
import {
  CreateAccountTaskDto,
  FindAccountTaskDto,
  UpdateAccountTaskDto,
} from "../../dtos/account-task.dto";
import { AccountTaskResponseDto } from "../../dtos/response/account-task.dto";
import { AccountTaskActionService } from "../../services/account-task.action.service";

@Controller()
@UseGuards(GrpcAuthGuard)
export class AccountTasksGrpcController {
  constructor(private readonly accountTasksService: AccountTaskActionService) {}

  @GrpcMethod(accounts.ACCOUNT_TASKS_SERVICE_NAME, "GetAccountTasks")
  async getAccountTasks(
    request: accounts.GetAccountTasksRequest,
    metadata: Metadata,
  ): Promise<accounts.GetAccountTasksResponse> {
    try {
      const user = extractUserMetadata(metadata);

      const query: FindAccountTaskDto = {
        accountId: request.accountId,
        activityId: request.activityId,
        assigneeId: request.assigneeId,
        type: request.type,
        status: request.status,
        priority: request.priority,
        page: request.page,
        limit: request.limit,
      };

      const tasks = await this.accountTasksService.findAllAccountTasks(
        user,
        query,
      );

      // Convert to proper proto type instead of unsafe casting
      const taskDtos = tasks.results.map((task) => {
        const dto = AccountTaskResponseDto.fromEntity(task);
        return this.convertToProtoAccountTask(dto);
      });

      return {
        data: taskDtos,
      };
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(accounts.ACCOUNT_TASKS_SERVICE_NAME, "CreateAccountTask")
  async createAccountTask(
    request: accounts.CreateAccountTaskRequest,
    metadata: Metadata,
  ): Promise<accounts.AccountTask> {
    try {
      const user = extractUserMetadata(metadata);

      const workflowContext = extractWorkflowContextFromMetadata(metadata);

      const createDto: CreateAccountTaskDto = {
        accountId: request.accountId,
        title: request.title,
        assigneeId: request.assigneeId,
        activityId: request.activityId,
        description: request.description,
        type: request.type,
        status: request.status,
        priority: request.priority,
        attachmentUrls: request.attachmentUrls,
        ...(workflowContext.workflowId
          ? {
              metadata: {
                workflowExecutions: [workflowContext],
              },
            }
          : {}),
      };

      const task = await this.accountTasksService.createAccountTask(
        user,
        createDto,
      );

      // Use proper conversion instead of unsafe type cast
      const dto = AccountTaskResponseDto.fromEntity(task);
      return this.convertToProtoAccountTask(dto);
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(accounts.ACCOUNT_TASKS_SERVICE_NAME, "UpdateAccountTask")
  async updateAccountTask(
    request: accounts.UpdateAccountTaskRequest,
    metadata: Metadata,
  ): Promise<accounts.AccountTask> {
    try {
      const user = extractUserMetadata(metadata);

      const workflowContext = extractWorkflowContextFromMetadata(metadata);

      const updateDto: UpdateAccountTaskDto = {
        title: request.title,
        assigneeId: request.assigneeId,
        activityId: request.activityId,
        description: request.description,
        type: request.type,
        status: request.status,
        priority: request.priority,
        attachmentUrls: request.attachmentUrls,
        ...(workflowContext.workflowId
          ? {
              metadata: {
                workflowExecutions: [workflowContext],
              },
            }
          : {}),
      };

      const task = await this.accountTasksService.updateAccountTask(
        user,
        request.taskId,
        updateDto,
      );

      // Use proper conversion instead of unsafe type cast
      const dto = AccountTaskResponseDto.fromEntity(task);
      return this.convertToProtoAccountTask(dto);
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(accounts.ACCOUNT_TASKS_SERVICE_NAME, "DeleteAccountTask")
  async deleteAccountTask(
    request: accounts.DeleteAccountTaskRequest,
    metadata: Metadata,
  ): Promise<void> {
    try {
      const user = extractUserMetadata(metadata);

      const workflowContext = extractWorkflowContextFromMetadata(metadata);

      await this.accountTasksService.deleteAccountTask(
        user,
        request.taskId,
        workflowContext.workflowId
          ? { workflowExecutions: [workflowContext] }
          : undefined,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  // Helper method to properly convert between DTO and proto type
  private convertToProtoAccountTask(
    dto: AccountTaskResponseDto,
  ): accounts.AccountTask {
    return {
      id: dto.id,
      accountId: dto.accountId,
      account: dto.account,
      activityId: dto.activityId || "",
      title: dto.title,
      description: dto.description || "",
      assigneeId: dto.assigneeId || "",
      creator: dto.creator || "",
      creatorId: dto.creatorId || "",
      creatorEmail: dto.creatorEmail || "",
      type: dto.type || "",
      typeId: dto.typeId || "",
      typeConfiguration: dto.typeConfiguration as Record<string, string>,
      status: dto.status || "",
      statusId: dto.statusId || "",
      statusConfiguration: dto.statusConfiguration as Record<string, string>,
      priority: dto.priority || "",
      priorityId: dto.priorityId || "",
      priorityConfiguration: dto.priorityConfiguration as Record<
        string,
        string
      >,
      attachments:
        dto.attachments?.map((att) => ({
          id: att.id,
          url: att.url,
          name: att.name || "",
          size: att.size || 0,
          contentType: att.contentType || "",
          createdAt: att.createdAt || "",
        })) || [],
      createdAt: dto.createdAt,
      updatedAt: dto.updatedAt,
    };
  }
}
