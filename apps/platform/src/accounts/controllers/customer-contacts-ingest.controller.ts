import {
  Body,
  Controller,
  Inject,
  Post,
  UseInterceptors,
} from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import {
  ApiCreateEndpoint,
  ApiResponseMessage,
  ResponseTransformInterceptor,
} from "@repo/nestjs-commons/decorators";
import { CUSTOM_LOGGER_TOKEN, ILogger } from "@repo/nestjs-commons/logger";
import { CurrentUser } from "@repo/nestjs-commons/utils";
import { IngestCustomerContactDTO } from "@repo/thena-shared-interfaces";
import { SkipAllThrottler } from "../../common/decorators/throttler.decorator";
import { CustomerContactsIngestService } from "../services/customer-contacts-ingest.service";

@ApiTags("Accounts")
@Controller("v1/accounts/contacts")
@UseInterceptors(ResponseTransformInterceptor)
export class CustomerContactsIngestController {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN)
    private readonly logger: ILogger,
    private readonly customerContactsIngestService: CustomerContactsIngestService,
  ) {}

  @Post("/ingest")
  @ApiResponseMessage("Users ingested successfully!")
  @ApiCreateEndpoint({
    summary: "Ingest users",
  })
  @SkipAllThrottler()
  ingestCustomers(
    @CurrentUser() currentUser: CurrentUser,
    @Body() data: IngestCustomerContactDTO,
  ) {
    // Ingest customers
    const ingestedCustomers =
      this.customerContactsIngestService.ingestCustomerContacts(
        currentUser,
        data,
      );

    return ingestedCustomers;
  }
}
