import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  Inject,
  InternalServerErrorException,
  Param,
  Post,
  Put,
  Query,
  UseInterceptors,
} from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import {
  ApiCreateEndpoint,
  ApiDeleteEndpoint,
  ApiGetEndpoint,
  ApiResponseMessage,
  ApiUpdateEndpoint,
  ResponseTransformInterceptor,
} from "@repo/nestjs-commons/decorators";
import { SentryService } from "@repo/nestjs-commons/filters";
import { ILogger } from "@repo/nestjs-commons/logger";
import { SkipAllThrottler } from "../../common/decorators/throttler.decorator";
import { CurrentUser } from "../../common/decorators/user.decorator";
import {
  BulkCreateCustomerContactsDto,
  CreateCustomerContactDto,
  FilterCustomerContactsByIdsDto,
  FindAllCustomerContactsDto,
  SearchCustomerContactsDto,
  UpdateCustomerContactDto,
} from "../dtos/customer-contact.dto";
import {
  CustomerContactBulkResponseDto,
  CustomerContactPaginatedResponseDto,
  CustomerContactResponseDto,
} from "../dtos/response/customer-contact.dto";
import { CustomerContactActionService } from "../services/customer-contact.action.service";

@ApiTags("Accounts")
@Controller("v1/accounts/contacts")
@SkipAllThrottler()
@UseInterceptors(ResponseTransformInterceptor)
export class CustomerContactActionController {
  private readonly logSpanId = "[CustomerContactActionController]";

  constructor(
    private readonly customerContactActionService: CustomerContactActionService,
    @Inject("Sentry") private readonly sentryService: SentryService,
    @Inject("CustomLogger") private readonly logger: ILogger,
  ) {}

  @Post()
  @ApiResponseMessage("Customer contact created successfully!")
  @ApiCreateEndpoint({
    summary: "Create a customer contact",
    responseType: CustomerContactResponseDto,
  })
  async createCustomerContact(
    @CurrentUser() user: CurrentUser,
    @Body() createDto: CreateCustomerContactDto,
  ): Promise<CustomerContactResponseDto> {
    try {
      const contact =
        await this.customerContactActionService.createCustomerContact(
          user,
          createDto,
        );

      return CustomerContactResponseDto.fromEntity(contact);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `${this.logSpanId} Error encountered while creating customer contact. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "CUSTOMER_CONTACT_CONTROLLER",
        fn: "createCustomerContact",
        organizationId: user.orgUid,
        body: createDto,
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Post("/bulk")
  @ApiResponseMessage("Customer contacts created successfully!")
  @ApiCreateEndpoint({
    summary: "Bulk create customer contacts",
    responseType: CustomerContactBulkResponseDto,
  })
  async bulkCreateCustomerContacts(
    @CurrentUser() user: CurrentUser,
    @Body()
    bulkCreateDto: BulkCreateCustomerContactsDto,
  ): Promise<CustomerContactBulkResponseDto> {
    try {
      const result =
        await this.customerContactActionService.bulkCreateCustomerContacts(
          user,
          bulkCreateDto,
        );

      return result;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `${this.logSpanId} Error encountered while bulk creating customer contacts. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "CUSTOMER_CONTACT_CONTROLLER",
        fn: "bulkCreateCustomerContacts",
        organizationId: user.orgUid,
        body: bulkCreateDto,
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Get()
  @ApiResponseMessage("Customer contacts fetched successfully!")
  @ApiGetEndpoint({
    summary: "Get all customer contacts",
    responseType: CustomerContactPaginatedResponseDto,
  })
  async findAllCustomerContacts(
    @CurrentUser() user: CurrentUser,
    @Query() query: FindAllCustomerContactsDto,
  ): Promise<CustomerContactPaginatedResponseDto> {
    try {
      const contacts =
        await this.customerContactActionService.findCustomerContacts(
          user,
          query,
        );

      return {
        results: contacts.results.map(CustomerContactResponseDto.fromEntity),
        meta: contacts.meta,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `${this.logSpanId} Error encountered while fetching customer contacts. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "CUSTOMER_CONTACT_CONTROLLER",
        fn: "findAllCustomerContacts",
        organizationId: user.orgUid,
        query,
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Post("/filter/ids")
  @ApiResponseMessage("Customer contacts filtered successfully!")
  @ApiCreateEndpoint({
    summary: "Filter customer contacts by ids",
    responseType: [CustomerContactResponseDto],
  })
  async filterCustomerContactsByIds(
    @CurrentUser() user: CurrentUser,
    @Body()
    filterDto: FilterCustomerContactsByIdsDto,
  ): Promise<CustomerContactResponseDto[]> {
    try {
      const result =
        await this.customerContactActionService.filterCustomerContactsByIds(
          user,
          filterDto.ids,
        );

      return result.map(CustomerContactResponseDto.fromEntity);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `${this.logSpanId} Error encountered while filtering customer contacts by ids. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "CUSTOMER_CONTACT_CONTROLLER",
        fn: "filterCustomerContactsByIds",
        organizationId: user.orgUid,
        body: filterDto,
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Put(":contactId")
  @ApiResponseMessage("Customer contact updated successfully!")
  @ApiUpdateEndpoint({
    summary: "Update a customer contact",
    responseType: CustomerContactResponseDto,
  })
  async updateCustomerContact(
    @CurrentUser() user: CurrentUser,
    @Param("contactId") contactId: string,
    @Body() updateDto: UpdateCustomerContactDto,
  ): Promise<CustomerContactResponseDto> {
    if (!contactId) throw new BadRequestException("Contact ID is required!");

    try {
      const contact =
        await this.customerContactActionService.updateCustomerContact(
          user,
          contactId,
          updateDto,
        );

      return CustomerContactResponseDto.fromEntity(contact);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `${this.logSpanId} Error encountered while updating customer contact. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "CUSTOMER_CONTACT_CONTROLLER",
        fn: "updateCustomerContact",
        organizationId: user.orgUid,
        body: updateDto,
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Delete(":contactId")
  @ApiResponseMessage("Customer contact deleted successfully!")
  @ApiDeleteEndpoint({
    summary: "Delete a customer contact",
  })
  async deleteCustomerContact(
    @CurrentUser() user: CurrentUser,
    @Param("contactId") contactId: string,
  ): Promise<void> {
    if (!contactId) throw new BadRequestException("Contact ID is required!");

    try {
      await this.customerContactActionService.deleteCustomerContact(
        user,
        contactId,
      );
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `${this.logSpanId} Error encountered while deleting customer contact. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "CUSTOMER_CONTACT_CONTROLLER",
        fn: "deleteCustomerContact",
        organizationId: user.orgUid,
        params: { contactId },
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Get("search")
  @ApiResponseMessage("Customer contacts searched successfully!")
  @ApiGetEndpoint({
    summary: "Search customer contacts",
    responseType: [CustomerContactResponseDto],
  })
  async searchCustomerContacts(
    @CurrentUser() user: CurrentUser,
    @Query() query: SearchCustomerContactsDto,
  ): Promise<CustomerContactResponseDto[]> {
    try {
      const contacts =
        await this.customerContactActionService.searchCustomerContacts(
          user,
          query,
        );

      return contacts.map(CustomerContactResponseDto.fromEntity);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `${this.logSpanId} Error encountered while searching customer contacts. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "CUSTOMER_CONTACT_CONTROLLER",
        fn: "searchCustomerContacts",
        organizationId: user.orgUid,
        query,
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }
}
