import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  Inject,
  InternalServerErrorException,
  Param,
  Post,
  Put,
  Query,
  UseInterceptors,
} from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import {
  ApiCreateEndpoint,
  ApiDeleteEndpoint,
  ApiGetEndpoint,
  ApiResponseMessage,
  ApiUpdateEndpoint,
  ResponseTransformInterceptor,
} from "@repo/nestjs-commons/decorators";
import { SentryService } from "@repo/nestjs-commons/filters";
import { ILogger } from "@repo/nestjs-commons/logger";
import { SkipAllThrottler } from "../../common/decorators/throttler.decorator";
import { CurrentUser } from "../../common/decorators/user.decorator";
import {
  CreateAccountAttributeValueDto,
  FindAccountAttributeValueDto,
  UpdateAccountAttributeValueDto,
} from "../dtos/account-attribute-value.dto";
import { AccountAttributeValueResponseDto } from "../dtos/response/account-attribute-value.dto";
import { AccountAttributeValueActionService } from "../services/account-attribute-value.action.service";

@ApiTags("Accounts")
@Controller("v1/accounts/attributes")
@SkipAllThrottler()
@UseInterceptors(ResponseTransformInterceptor)
export class AccountAttributeValueActionController {
  private readonly logSpanId = "[AccountAttributeValueActionController]";

  constructor(
    private readonly accountAttributeValueActionService: AccountAttributeValueActionService,
    @Inject("Sentry") private readonly sentryService: SentryService,
    @Inject("CustomLogger") private readonly logger: ILogger,
  ) {}

  @Post()
  @ApiResponseMessage("Account attribute value created successfully!")
  @ApiCreateEndpoint({
    summary: "Creates an account attribute value",
    responseType: AccountAttributeValueResponseDto,
  })
  async createAccountAttributeValue(
    @CurrentUser() user: CurrentUser,
    @Body() createAccountAttributeValueDto: CreateAccountAttributeValueDto,
  ): Promise<AccountAttributeValueResponseDto> {
    try {
      const createdAttributeValue =
        await this.accountAttributeValueActionService.createAccountAttributeValue(
          user,
          createAccountAttributeValueDto,
        );

      return AccountAttributeValueResponseDto.fromEntity(createdAttributeValue);
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Error encountered while creating account attribute value. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "ACCOUNTS_ATTRIBUTE_VALUE_CONTROLLER",
        fn: "createAccountAttributeValue",
        organizationId: user.orgUid,
        body: createAccountAttributeValueDto,
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Get()
  @ApiResponseMessage("Account attribute values fetched successfully!")
  @ApiGetEndpoint({
    summary: "Gets all account attribute values",
    responseType: AccountAttributeValueResponseDto,
  })
  async findAttributeValuesByAttribute(
    @CurrentUser() user: CurrentUser,
    @Query()
    findAccountAttributeValueDto: FindAccountAttributeValueDto,
  ): Promise<AccountAttributeValueResponseDto[]> {
    try {
      const attributeValues =
        await this.accountAttributeValueActionService.findAccountAttributeValues(
          user,
          findAccountAttributeValueDto.attribute,
        );

      return attributeValues.map(AccountAttributeValueResponseDto.fromEntity);
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Error encountered while fetching account attribute values. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "ACCOUNTS_ATTRIBUTE_VALUE_CONTROLLER",
        fn: "findAttributeValuesByAttribute",
        organizationId: user.orgUid,
        body: findAccountAttributeValueDto,
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Put(":id")
  @ApiResponseMessage("Account attribute value updated successfully!")
  @ApiUpdateEndpoint({
    summary: "Updates an account attribute value",
    responseType: AccountAttributeValueResponseDto,
  })
  async updateAccountAttributeValue(
    @CurrentUser() user: CurrentUser,
    @Param("id") id: string,
    @Body() updateAccountAttributeValueDto: UpdateAccountAttributeValueDto,
  ): Promise<AccountAttributeValueResponseDto> {
    if (!id)
      throw new BadRequestException("Account attribute value ID is required!");

    try {
      const updatedAttributeValue =
        await this.accountAttributeValueActionService.updateAccountAttributeValue(
          user,
          id,
          updateAccountAttributeValueDto,
        );

      return AccountAttributeValueResponseDto.fromEntity(updatedAttributeValue);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `${this.logSpanId} Error encountered while updating account attribute value. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "ACCOUNTS_ATTRIBUTE_VALUE_CONTROLLER",
        fn: "updateAccountAttributeValue",
        organizationId: user.orgUid,
        params: { id },
        body: updateAccountAttributeValueDto,
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Delete(":id")
  @ApiResponseMessage("Account attribute value deleted successfully!")
  @ApiDeleteEndpoint({
    summary: "Deletes an account attribute value",
  })
  async deleteAccountAttributeValue(
    @CurrentUser() user: CurrentUser,
    @Param("id") id: string,
    @Query("forceDelete") forceDelete: boolean,
  ): Promise<void> {
    try {
      await this.accountAttributeValueActionService.deleteAccountAttributeValue(
        user,
        id,
        forceDelete,
      );
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `${this.logSpanId} Error encountered while deleting account attribute value. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "ACCOUNTS_ATTRIBUTE_VALUE_CONTROLLER",
        fn: "deleteAccountAttributeValue",
        organizationId: user.orgUid,
        params: { id, forceDelete },
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }
}
