import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  Inject,
  InternalServerErrorException,
  Param,
  Post,
  Put,
  Query,
  UseInterceptors,
} from "@nestjs/common";
import { ApiOperation, ApiTags } from "@nestjs/swagger";
import { SkipThrottle, Throttle } from "@nestjs/throttler";
import {
  ApiCreateEndpoint,
  ApiDeleteEndpoint,
  ApiGetEndpoint,
  ApiResponseMessage,
  ApiUpdateEndpoint,
  ResponseTransformInterceptor,
} from "@repo/nestjs-commons/decorators";
import { SentryService } from "@repo/nestjs-commons/filters";
import { RequiredTier } from "@repo/nestjs-commons/guards/organization-tier/index";
import { ILogger } from "@repo/nestjs-commons/logger";
import { OrganizationTier } from "@repo/thena-platform-entities";
import { CurrentUser } from "../../common/decorators/user.decorator";
import {
  THROTTLER_SPECIAL_TIER,
  THROTTLER_TIER,
  THROTTLER_TIER_1,
  THROTTLER_TIER_2,
  THROTTLER_TIER_3,
  THROTTLER_TIER_4,
} from "../../config/throttler.config";
import { CustomFieldResponseDto } from "../../custom-field/dto";
import {
  CreateAccountDto,
  FilterAccountsByIdsDto,
  FilterAccountsByPrimaryDomainsDto,
  FindAllAccountsDto,
  UpdateAccountDto,
} from "../dtos/account.dto";
import {
  AccountResponseDto,
  AccountWithCustomerContactsPaginatedResponseDto,
  AccountWithCustomerContactsResponseDto,
} from "../dtos/response/account.dto";
import { AccountsService } from "../services/accounts.service";

@ApiTags("Accounts")
@Controller("v1/accounts")
@SkipThrottle({ [THROTTLER_TIER_1]: true })
@SkipThrottle({ [THROTTLER_TIER_2]: true })
@SkipThrottle({ [THROTTLER_TIER_4]: true })
@SkipThrottle({ [THROTTLER_SPECIAL_TIER]: true })
@Throttle({ [THROTTLER_TIER_3]: THROTTLER_TIER.TIER_3 })
@UseInterceptors(ResponseTransformInterceptor)
export class AccountsController {
  private readonly logSpanId = "[AccountsController]";

  constructor(
    private readonly accountsService: AccountsService,
    @Inject("Sentry") private readonly sentryService: SentryService,
    @Inject("CustomLogger") private readonly logger: ILogger,
  ) {}

  @Get("")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description:
      "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Accounts fetched successfully!")
  @ApiGetEndpoint({
    summary: "Get all accounts",
    responseType: AccountWithCustomerContactsPaginatedResponseDto,
  })
  async getAccounts(
    @CurrentUser() user: CurrentUser,
    @Query() query: FindAllAccountsDto,
  ): Promise<AccountWithCustomerContactsPaginatedResponseDto> {
    try {
      const accounts = await this.accountsService.findAllAccounts(
        user.orgId,
        query,
      );

      return {
        results: accounts.results.map(
          AccountWithCustomerContactsResponseDto.fromEntity,
        ),
        meta: accounts.meta,
      };
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Error encountered while fetching accounts. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "ACCOUNTS_CONTROLLER",
        fn: "getAccounts",
        organizationId: user.orgUid,
        query,
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Post("/filter/primary-domains")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description:
      "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Accounts fetched successfully!")
  @ApiCreateEndpoint({
    summary: "Get accounts by primary domains",
    responseType: AccountResponseDto,
  })
  async filterAccountsByPrimaryDomains(
    @CurrentUser() user: CurrentUser,
    @Body() body: FilterAccountsByPrimaryDomainsDto,
  ): Promise<AccountResponseDto[]> {
    try {
      const accounts =
        await this.accountsService.findAllAccountsByPrimaryDomains(
          body.primaryDomains,
          user.orgId,
        );

      return accounts.map((account) => AccountResponseDto.fromEntity(account));
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Error encountered while fetching accounts by primary domains. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "ACCOUNTS_CONTROLLER",
        fn: "filterAccountsByPrimaryDomains",
        organizationId: user.orgUid,
        body,
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Post("/filter/ids")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description:
      "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Accounts fetched successfully!")
  @ApiCreateEndpoint({
    summary: "Get accounts by IDs",
    responseType: AccountResponseDto,
  })
  async filterAccountsByIds(
    @CurrentUser() user: CurrentUser,
    @Body() body: FilterAccountsByIdsDto,
  ): Promise<AccountResponseDto[]> {
    try {
      const accounts = await this.accountsService.findAccountsByPublicIds(
        body.ids,
        user.orgId,
      );

      return accounts.map((account) => AccountResponseDto.fromEntity(account));
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Error encountered while fetching accounts by IDs. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "ACCOUNTS_CONTROLLER",
        fn: "filterAccountsByIds",
        organizationId: user.orgUid,
        body,
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Get(":id")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description:
      "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Account details fetched successfully!")
  @ApiGetEndpoint({
    summary: "Get account details",
    responseType: AccountResponseDto,
  })
  async getAccountDetails(
    @CurrentUser() user: CurrentUser,
    @Param("id") id: string,
  ): Promise<AccountResponseDto> {
    if (!id) throw new BadRequestException("Account ID is required!");

    try {
      const account = await this.accountsService.findAccountDetails(user, id);

      return AccountResponseDto.fromEntity(account);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `${this.logSpanId} Error encountered while fetching account details. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "ACCOUNTS_CONTROLLER",
        fn: "getAccountDetails",
        organizationId: user.orgUid,
        params: {
          uid: id,
        },
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Post()
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description:
      "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Account created successfully!")
  @ApiCreateEndpoint({
    summary: "Create an account",
    responseType: AccountResponseDto,
  })
  async createAccount(
    @CurrentUser() user: CurrentUser,
    @Body() createAccountDto: CreateAccountDto,
  ): Promise<AccountResponseDto> {
    try {
      const createdAccount = await this.accountsService.createAccount(
        user,
        createAccountDto,
      );

      return AccountResponseDto.fromEntity(createdAccount);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `${this.logSpanId} Error encountered while creating account. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "ACCOUNTS_CONTROLLER",
        fn: "createAccount",
        organizationId: user.orgUid,
        body: createAccountDto,
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Post("/bulk")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description:
      "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Accounts created successfully!")
  @ApiCreateEndpoint({
    summary: "Create accounts in bulk",
    responseType: [AccountResponseDto],
  })
  async bulkCreateAccounts(
    @CurrentUser() user: CurrentUser,
    @Body() bulkCreateAccountDto: CreateAccountDto[],
  ): Promise<AccountResponseDto[]> {
    try {
      const createdAccounts = await this.accountsService.bulkCreateAccounts(
        user,
        bulkCreateAccountDto,
      );

      return createdAccounts.map((account) =>
        AccountResponseDto.fromEntity(account),
      );
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `${this.logSpanId} Error encountered while creating accounts in bulk. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "ACCOUNTS_CONTROLLER",
        fn: "bulkCreateAccounts",
        organizationId: user.orgUid,
        body: bulkCreateAccountDto,
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Put(":id")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description:
      "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Account updated successfully!")
  @ApiUpdateEndpoint({
    summary: "Update an account",
    responseType: AccountResponseDto,
  })
  async updateAccount(
    @CurrentUser() user: CurrentUser,
    @Param("id") id: string,
    @Body() updateAccountDto: UpdateAccountDto,
  ): Promise<AccountResponseDto> {
    if (!id) throw new BadRequestException("Account ID is required!");

    try {
      const updatedAccount = await this.accountsService.updateAccount(
        id,
        user,
        updateAccountDto,
      );

      return AccountResponseDto.fromEntity(updatedAccount);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `${this.logSpanId} Error encountered while updating account. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "ACCOUNTS_CONTROLLER",
        fn: "updateAccount",
        organizationId: user.orgUid,
        params: { id },
        body: updateAccountDto,
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Delete(":id")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description:
      "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Account deleted successfully!")
  @ApiDeleteEndpoint({
    summary: "Delete an account",
  })
  async deleteAccount(
    @CurrentUser() user: CurrentUser,
    @Param("id") id: string,
  ): Promise<void> {
    if (!id) throw new BadRequestException("Account ID is required!");

    try {
      await this.accountsService.deleteAccount(id, user);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `${this.logSpanId} Error encountered while deleting account. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "ACCOUNTS_CONTROLLER",
        fn: "deleteAccount",
        organizationId: user.orgUid,
        params: {
          uid: id,
        },
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Get("/account-custom-fields")
  @ApiResponseMessage("Custom fields fetched successfully!")
  @ApiGetEndpoint({
    summary: "Get custom fields for an account",
    responseType: CustomFieldResponseDto,
  })
  async getCustomFields(
    @CurrentUser() user: CurrentUser,
  ): Promise<CustomFieldResponseDto> {
    return await this.accountsService.getCustomFields(user);
  }
}
