import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  Inject,
  InternalServerErrorException,
  Param,
  Post,
  Put,
  Query,
  UseInterceptors,
} from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import {
  ApiCreateEndpoint,
  ApiDeleteEndpoint,
  ApiGetEndpoint,
  ApiResponseMessage,
  ApiUpdateEndpoint,
  ResponseTransformInterceptor,
} from "@repo/nestjs-commons/decorators";
import { SentryService } from "@repo/nestjs-commons/filters";
import { ILogger } from "@repo/nestjs-commons/logger";
import { SkipAllThrottler } from "../../common/decorators/throttler.decorator";
import { CurrentUser } from "../../common/decorators/user.decorator";
import {
  CreateAccountActivityDto,
  FindAccountActivityDto,
  UpdateAccountActivityDto,
} from "../dtos/account-activity.dto";
import {
  AccountActivityPaginatedResponseDto,
  AccountActivityResponseDto,
} from "../dtos/response/account-activity.dto";
import { AccountActivityActionService } from "../services/account-activity.action.service";

@ApiTags("Accounts")
@Controller("v1/accounts/activities")
@SkipAllThrottler()
@UseInterceptors(ResponseTransformInterceptor)
export class AccountActivityActionController {
  private readonly logSpanId = "[AccountActivityActionController]";

  constructor(
    private readonly accountActivityActionService: AccountActivityActionService,
    @Inject("Sentry") private readonly sentryService: SentryService,
    @Inject("CustomLogger") private readonly logger: ILogger,
  ) {}

  @Get()
  @ApiResponseMessage("Account activities fetched successfully!")
  @ApiGetEndpoint({
    summary: "Fetches all account activities",
    responseType: AccountActivityPaginatedResponseDto,
  })
  async getAccountActivities(
    @CurrentUser() user: CurrentUser,
    @Query() findAccountActivityDto: FindAccountActivityDto,
  ): Promise<AccountActivityPaginatedResponseDto> {
    try {
      const activities =
        await this.accountActivityActionService.findAccountActivities(
          user,
          findAccountActivityDto,
        );

      return {
        results: activities.results.map(AccountActivityResponseDto.fromEntity),
        meta: activities.meta,
      };
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Error encountered while fetching account activities. > Error message: ${error.message}`,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: "ACCOUNTS_ACTIVITY_CONTROLLER",
        fn: "getAccountActivities",
        organizationId: user.orgUid,
        query: findAccountActivityDto,
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Post()
  @ApiResponseMessage("Account activity created successfully!")
  @ApiCreateEndpoint({
    summary: "Creates an account activity",
    responseType: AccountActivityResponseDto,
  })
  async createAccountActivity(
    @CurrentUser() user: CurrentUser,
    @Body() createAccountActivityDto: CreateAccountActivityDto,
  ): Promise<AccountActivityResponseDto> {
    try {
      const createdActivity =
        await this.accountActivityActionService.createAccountActivity(
          user,
          createAccountActivityDto,
        );

      return AccountActivityResponseDto.fromEntity(createdActivity);
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Error encountered while creating account activity. > Error message: ${error.message}`,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: "ACCOUNTS_ACTIVITY_CONTROLLER",
        fn: "createAccountActivity",
        organizationId: user.orgUid,
        body: createAccountActivityDto,
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Put(":activityId")
  @ApiResponseMessage("Account activity updated successfully!")
  @ApiUpdateEndpoint({
    summary: "Updates an account activity",
    responseType: AccountActivityResponseDto,
  })
  async updateAccountActivity(
    @CurrentUser() user: CurrentUser,
    @Param("activityId") activityId: string,
    @Body() updateAccountActivityDto: UpdateAccountActivityDto,
  ): Promise<AccountActivityResponseDto> {
    if (!activityId) {
      throw new BadRequestException("Activity ID is required!");
    }

    try {
      const updatedActivity =
        await this.accountActivityActionService.updateAccountActivity(
          user,
          activityId,
          updateAccountActivityDto,
        );

      return AccountActivityResponseDto.fromEntity(updatedActivity);
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Error encountered while updating account activity. > Error message: ${error.message}`,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: "ACCOUNTS_ACTIVITY_CONTROLLER",
        fn: "updateAccountActivity",
        organizationId: user.orgUid,
        params: { activityId },
        body: updateAccountActivityDto,
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Delete(":activityId")
  @ApiResponseMessage("Account activity deleted successfully!")
  @ApiDeleteEndpoint({
    summary: "Deletes an account activity",
  })
  async deleteAccountActivity(
    @CurrentUser() user: CurrentUser,
    @Param("activityId") activityId: string,
  ): Promise<void> {
    if (!activityId) {
      throw new BadRequestException("Activity ID is required!");
    }

    try {
      await this.accountActivityActionService.deleteAccountActivity(
        user,
        activityId,
      );
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Error encountered while deleting account activity. > Error message: ${error.message}`,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: "ACCOUNTS_ACTIVITY_CONTROLLER",
        fn: "deleteAccountActivity",
        organizationId: user.orgUid,
        params: { activityId },
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Delete(":activityId/attachments/:attachmentId")
  @ApiResponseMessage("Attachment removed successfully!")
  @ApiDeleteEndpoint({
    summary: "Removes an attachment from an account activity",
  })
  async removeActivityAttachment(
    @CurrentUser() user: CurrentUser,
    @Param("activityId") activityId: string,
    @Param("attachmentId") attachmentId: string,
  ): Promise<void> {
    if (!activityId) {
      throw new BadRequestException("Activity ID is required!");
    }

    if (!attachmentId) {
      throw new BadRequestException("Attachment ID is required!");
    }

    try {
      await this.accountActivityActionService.removeActivityAttachment(
        user,
        activityId,
        attachmentId,
      );
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Error encountered while removing activity attachment. > Error message: ${error.message}`,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: "ACCOUNTS_ACTIVITY_CONTROLLER",
        fn: "removeActivityAttachment",
        organizationId: user.orgUid,
        params: { activityId, attachmentId },
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }
}
