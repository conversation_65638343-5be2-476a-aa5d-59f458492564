import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  Inject,
  InternalServerErrorException,
  Param,
  Post,
  Put,
  Query,
  UseInterceptors,
} from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import {
  ApiCreateEndpoint,
  ApiDeleteEndpoint,
  ApiGetEndpoint,
  ApiResponseMessage,
  ApiUpdateEndpoint,
  ResponseTransformInterceptor,
} from "@repo/nestjs-commons/decorators";
import { SentryService } from "@repo/nestjs-commons/filters";
import { ILogger } from "@repo/nestjs-commons/logger";
import { SkipAllThrottler } from "../../common/decorators/throttler.decorator";
import { CurrentUser } from "../../common/decorators/user.decorator";
import {
  CreateAccountTaskDto,
  FindAccountTaskDto,
  UpdateAccountTaskDto,
} from "../dtos/account-task.dto";
import {
  AccountTaskPaginatedResponseDto,
  AccountTaskResponseDto,
} from "../dtos/response/account-task.dto";
import { AccountTaskActionService } from "../services/account-task.action.service";

@ApiTags("Accounts")
@Controller("v1/accounts/tasks")
@SkipAllThrottler()
@UseInterceptors(ResponseTransformInterceptor)
export class AccountTaskActionController {
  private readonly logSpanId = "[AccountTaskActionController]";

  constructor(
    private readonly accountTaskActionService: AccountTaskActionService,
    @Inject("Sentry") private readonly sentryService: SentryService,
    @Inject("CustomLogger") private readonly logger: ILogger,
  ) {}

  @Get()
  @ApiResponseMessage("Account tasks fetched successfully!")
  @ApiGetEndpoint({
    summary: "Fetches all account tasks by account ID or by task ID",
    responseType: AccountTaskPaginatedResponseDto,
  })
  async getAccountTasks(
    @CurrentUser() user: CurrentUser,
    @Query() findAccountTaskDto: FindAccountTaskDto,
  ): Promise<AccountTaskPaginatedResponseDto> {
    try {
      const tasks = await this.accountTaskActionService.findAllAccountTasks(
        user,
        findAccountTaskDto,
      );

      return {
        results: tasks.results.map(AccountTaskResponseDto.fromEntity),
        meta: tasks.meta,
      };
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Error encountered while fetching account tasks. > Error message: ${error.message}`,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: "ACCOUNTS_TASK_CONTROLLER",
        fn: "getAccountTasks",
        organizationId: user.orgUid,
        query: findAccountTaskDto,
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Post()
  @ApiResponseMessage("Account task created successfully!")
  @ApiCreateEndpoint({
    summary: "Creates an account task",
    responseType: AccountTaskResponseDto,
  })
  async createAccountTask(
    @CurrentUser() user: CurrentUser,
    @Body() createAccountTaskDto: CreateAccountTaskDto,
  ): Promise<AccountTaskResponseDto> {
    try {
      const createdTask = await this.accountTaskActionService.createAccountTask(
        user,
        createAccountTaskDto,
      );

      return AccountTaskResponseDto.fromEntity(createdTask);
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Error encountered while creating account task. > Error message: ${error.message}`,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: "ACCOUNTS_TASK_CONTROLLER",
        fn: "createAccountTask",
        organizationId: user.orgUid,
        body: createAccountTaskDto,
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Put(":taskId")
  @ApiResponseMessage("Account task updated successfully!")
  @ApiUpdateEndpoint({
    summary: "Updates an account task",
    responseType: AccountTaskResponseDto,
  })
  async updateAccountTask(
    @CurrentUser() user: CurrentUser,
    @Param("taskId") taskId: string,
    @Body() updateAccountTaskDto: UpdateAccountTaskDto,
  ): Promise<AccountTaskResponseDto> {
    if (!taskId) {
      throw new BadRequestException("Task ID is required!");
    }

    try {
      const updatedTask = await this.accountTaskActionService.updateAccountTask(
        user,
        taskId,
        updateAccountTaskDto,
      );

      return AccountTaskResponseDto.fromEntity(updatedTask);
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Error encountered while updating account task. > Error message: ${error.message}`,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: "ACCOUNTS_TASK_CONTROLLER",
        fn: "updateAccountTask",
        organizationId: user.orgUid,
        params: { taskId },
        body: updateAccountTaskDto,
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Delete(":taskId")
  @ApiResponseMessage("Account task deleted successfully!")
  @ApiDeleteEndpoint({
    summary: "Deletes an account task",
  })
  async deleteAccountTask(
    @CurrentUser() user: CurrentUser,
    @Param("taskId") taskId: string,
  ): Promise<void> {
    if (!taskId) {
      throw new BadRequestException("Task ID is required!");
    }

    try {
      await this.accountTaskActionService.deleteAccountTask(user, taskId);
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Error encountered while deleting account task. > Error message: ${error.message}`,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: "ACCOUNTS_TASK_CONTROLLER",
        fn: "deleteAccountTask",
        organizationId: user.orgUid,
        params: { taskId },
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Delete(":taskId/attachments/:attachmentId")
  @ApiResponseMessage("Attachment removed successfully!")
  @ApiDeleteEndpoint({
    summary: "Removes an attachment from an account task",
  })
  async removeTaskAttachment(
    @CurrentUser() user: CurrentUser,
    @Param("taskId") taskId: string,
    @Param("attachmentId") attachmentId: string,
  ): Promise<void> {
    if (!taskId) {
      throw new BadRequestException("Task ID is required!");
    }

    if (!attachmentId) {
      throw new BadRequestException("Attachment ID is required!");
    }

    try {
      await this.accountTaskActionService.removeTaskAttachment(
        user,
        taskId,
        attachmentId,
      );
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Error encountered while removing task attachment. > Error message: ${error.message}`,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: "ACCOUNTS_TASK_CONTROLLER",
        fn: "removeTaskAttachment",
        organizationId: user.orgUid,
        params: { taskId, attachmentId },
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }
}
