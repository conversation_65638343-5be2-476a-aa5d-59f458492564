import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  Inject,
  InternalServerErrorException,
  Param,
  Post,
  Put,
  Query,
  UseInterceptors,
} from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import {
  ApiCreateEndpoint,
  ApiDeleteEndpoint,
  ApiGetEndpoint,
  ApiResponseMessage,
  ApiUpdateEndpoint,
  ResponseTransformInterceptor,
} from "@repo/nestjs-commons/decorators";
import { SentryService } from "@repo/nestjs-commons/filters";
import { ILogger } from "@repo/nestjs-commons/logger";
import { SkipAllThrottler } from "../../common/decorators/throttler.decorator";
import { CurrentUser } from "../../common/decorators/user.decorator";
import {
  CreateAccountRelationshipDto,
  CreateAccountRelationshipTypeDto,
  FindAccountRelationshipDto,
  UpdateAccountRelationshipDto,
  UpdateAccountRelationshipTypeDto,
} from "../dtos/account-relationship.dto";
import {
  AccountRelationshipPaginatedResponseDto,
  AccountRelationshipResponseDto,
  AccountRelationshipTypeResponseDto,
} from "../dtos/response/account-relationship.dto";
import { AccountRelationshipActionService } from "../services/account-relationship.action.service";

@ApiTags("Accounts")
@Controller("v1/accounts/relationships")
@SkipAllThrottler()
@UseInterceptors(ResponseTransformInterceptor)
export class AccountRelationshipActionController {
  private readonly logSpanId = "[AccountRelationshipActionController]";

  constructor(
    private readonly accountRelationshipActionService: AccountRelationshipActionService,
    @Inject("Sentry") private readonly sentryService: SentryService,
    @Inject("CustomLogger") private readonly logger: ILogger,
  ) {}

  @Post("/types")
  @ApiResponseMessage("Account relationship type created successfully!")
  @ApiCreateEndpoint({
    summary: "Create an account relationship type",
    responseType: AccountRelationshipTypeResponseDto,
  })
  async createAccountRelationshipType(
    @CurrentUser() user: CurrentUser,
    @Body() createDto: CreateAccountRelationshipTypeDto,
  ): Promise<AccountRelationshipTypeResponseDto> {
    try {
      const relationshipType =
        await this.accountRelationshipActionService.createAccountRelationshipType(
          user,
          createDto,
        );

      return AccountRelationshipTypeResponseDto.fromEntity(relationshipType);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `${this.logSpanId} Error encountered while creating relationship type. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "ACCOUNT_RELATIONSHIP_CONTROLLER",
        fn: "createAccountRelationshipType",
        organizationId: user.orgUid,
        body: createDto,
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Get("/types")
  @ApiResponseMessage("Account relationship types fetched successfully!")
  @ApiGetEndpoint({
    summary: "Get all account relationship types",
    responseType: [AccountRelationshipTypeResponseDto],
  })
  async findAllAccountRelationshipTypes(
    @CurrentUser() user: CurrentUser,
  ): Promise<AccountRelationshipTypeResponseDto[]> {
    try {
      const relationshipTypes =
        await this.accountRelationshipActionService.findAllAccountRelationshipTypes(
          user.orgId,
        );

      return relationshipTypes.map((type) =>
        AccountRelationshipTypeResponseDto.fromEntity(type),
      );
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Error encountered while fetching relationship types. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "ACCOUNT_RELATIONSHIP_CONTROLLER",
        fn: "findAllAccountRelationshipTypes",
        organizationId: user.orgUid,
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Put("/types/:id")
  @ApiResponseMessage("Account relationship type updated successfully!")
  @ApiUpdateEndpoint({
    summary: "Update an account relationship type",
    responseType: AccountRelationshipTypeResponseDto,
  })
  async updateAccountRelationshipType(
    @CurrentUser() user: CurrentUser,
    @Param("id") id: string,
    @Body() updateDto: UpdateAccountRelationshipTypeDto,
  ): Promise<AccountRelationshipTypeResponseDto> {
    if (!id)
      throw new BadRequestException(
        "Account relationship type ID is required!",
      );

    try {
      const relationshipType =
        await this.accountRelationshipActionService.updateAccountRelationshipType(
          user,
          id,
          updateDto,
        );

      return AccountRelationshipTypeResponseDto.fromEntity(relationshipType);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `${this.logSpanId} Error encountered while updating relationship type. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "ACCOUNT_RELATIONSHIP_CONTROLLER",
        fn: "updateAccountRelationshipType",
        organizationId: user.orgUid,
        params: { uid: id },
        body: updateDto,
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Delete("/types/:id")
  @ApiResponseMessage("Account relationship type deleted successfully!")
  @ApiDeleteEndpoint({
    summary: "Delete an account relationship type",
  })
  async deleteAccountRelationshipType(
    @CurrentUser() user: CurrentUser,
    @Param("id") id: string,
  ): Promise<void> {
    if (!id)
      throw new BadRequestException(
        "Account relationship type ID is required!",
      );

    try {
      await this.accountRelationshipActionService.deleteAccountRelationshipType(
        user,
        id,
      );
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `${this.logSpanId} Error encountered while deleting relationship type. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "ACCOUNT_RELATIONSHIP_CONTROLLER",
        fn: "deleteAccountRelationshipType",
        organizationId: user.orgUid,
        params: { uid: id },
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Post("")
  @ApiResponseMessage("Account relationship created successfully!")
  @ApiCreateEndpoint({
    summary: "Create an account relationship",
    responseType: AccountRelationshipResponseDto,
  })
  async createAccountRelationship(
    @CurrentUser() user: CurrentUser,
    @Body() createDto: CreateAccountRelationshipDto,
  ): Promise<AccountRelationshipResponseDto> {
    try {
      const relationship =
        await this.accountRelationshipActionService.createAccountRelationship(
          user,
          createDto,
        );

      return AccountRelationshipResponseDto.fromEntity(relationship);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `${this.logSpanId} Error encountered while creating relationship. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "ACCOUNT_RELATIONSHIP_CONTROLLER",
        fn: "createAccountRelationship",
        organizationId: user.orgUid,
        body: createDto,
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Get("")
  @ApiResponseMessage("Account relationships fetched successfully!")
  @ApiGetEndpoint({
    summary: "Get all account relationships",
    responseType: AccountRelationshipPaginatedResponseDto,
  })
  async findAllAccountRelationships(
    @CurrentUser() user: CurrentUser,
    @Query() findAllRelationshipsDto: FindAccountRelationshipDto,
  ): Promise<AccountRelationshipPaginatedResponseDto> {
    try {
      const relationships =
        await this.accountRelationshipActionService.findAllAccountRelationships(
          user,
          findAllRelationshipsDto,
        );

      return {
        results: relationships.results.map(
          AccountRelationshipResponseDto.fromEntity,
        ),
        meta: relationships.meta,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `${this.logSpanId} Error encountered while fetching relationships. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "ACCOUNT_RELATIONSHIP_CONTROLLER",
        fn: "findAllAccountRelationships",
        organizationId: user.orgUid,
        query: findAllRelationshipsDto,
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Put(":id")
  @ApiResponseMessage("Account relationship updated successfully!")
  @ApiUpdateEndpoint({
    summary: "Update an account relationship",
    responseType: AccountRelationshipResponseDto,
  })
  async updateAccountRelationship(
    @CurrentUser() user: CurrentUser,
    @Param("id") id: string,
    @Body() updateDto: UpdateAccountRelationshipDto,
  ): Promise<AccountRelationshipResponseDto> {
    if (!id)
      throw new BadRequestException("Account relationship ID is required!");

    try {
      const relationship =
        await this.accountRelationshipActionService.updateAccountRelationship(
          user,
          id,
          updateDto,
        );

      return AccountRelationshipResponseDto.fromEntity(relationship);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `${this.logSpanId} Error encountered while updating relationship. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "ACCOUNT_RELATIONSHIP_CONTROLLER",
        fn: "updateAccountRelationship",
        organizationId: user.orgUid,
        params: { uid: id },
        body: updateDto,
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Delete(":id")
  @ApiResponseMessage("Account relationship deleted successfully!")
  @ApiDeleteEndpoint({
    summary: "Delete an account relationship",
  })
  async deleteAccountRelationship(
    @CurrentUser() user: CurrentUser,
    @Param("id") id: string,
  ): Promise<void> {
    if (!id)
      throw new BadRequestException("Account relationship ID is required!");

    try {
      await this.accountRelationshipActionService.deleteAccountRelationship(
        user,
        id,
      );
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `${this.logSpanId} Error encountered while deleting relationship. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "ACCOUNT_RELATIONSHIP_CONTROLLER",
        fn: "deleteAccountRelationship",
        organizationId: user.orgUid,
        params: { uid: id },
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }
}
