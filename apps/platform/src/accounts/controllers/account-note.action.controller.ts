import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  Inject,
  InternalServerErrorException,
  Param,
  Post,
  Put,
  Query,
  UseInterceptors,
} from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import {
  ApiCreateEndpoint,
  ApiDeleteEndpoint,
  ApiGetEndpoint,
  ApiResponseMessage,
  ApiUpdateEndpoint,
  ResponseTransformInterceptor,
} from "@repo/nestjs-commons/decorators";
import { SentryService } from "@repo/nestjs-commons/filters";
import { ILogger } from "@repo/nestjs-commons/logger";
import { SkipAllThrottler } from "../../common/decorators/throttler.decorator";
import { CurrentUser } from "../../common/decorators/user.decorator";
import {
  CreateAccountNoteDto,
  FindAccountNoteDto,
  UpdateAccountNoteDto,
} from "../dtos/account-note.dto";
import {
  AccountNotePaginatedResponseDto,
  AccountNoteResponseDto,
} from "../dtos/response/account-note.dto";
import { AccountNoteActionService } from "../services/account-note.action.service";

@ApiTags("Accounts")
@Controller("v1/accounts/notes")
@SkipAllThrottler()
@UseInterceptors(ResponseTransformInterceptor)
export class AccountNoteActionController {
  private readonly logSpanId = "[AccountNoteActionController]";

  constructor(
    private readonly accountNoteActionService: AccountNoteActionService,
    @Inject("Sentry") private readonly sentryService: SentryService,
    @Inject("CustomLogger") private readonly logger: ILogger,
  ) {}

  @Get()
  @ApiResponseMessage("Account notes fetched successfully!")
  @ApiGetEndpoint({
    summary: "Fetches all account notes by account ID or by note ID",
    responseType: AccountNotePaginatedResponseDto,
  })
  async getAccountNotes(
    @CurrentUser() user: CurrentUser,
    @Query() findAccountNoteDto: FindAccountNoteDto,
  ): Promise<AccountNotePaginatedResponseDto> {
    try {
      const notes = await this.accountNoteActionService.findAllAccountNotes(
        user,
        findAccountNoteDto,
      );

      return {
        results: notes.results.map(AccountNoteResponseDto.fromEntity),
        meta: notes.meta,
      };
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Error encountered while fetching account notes. > Error message: ${error.message}`,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: "ACCOUNTS_NOTE_CONTROLLER",
        fn: "getAccountNotes",
        organizationId: user.orgUid,
        query: findAccountNoteDto,
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Post()
  @ApiResponseMessage("Account note created successfully!")
  @ApiCreateEndpoint({
    summary: "Creates an account note",
    responseType: AccountNoteResponseDto,
  })
  async createAccountNote(
    @CurrentUser() user: CurrentUser,
    @Body() createAccountNoteDto: CreateAccountNoteDto,
  ): Promise<AccountNoteResponseDto> {
    try {
      const createdNote = await this.accountNoteActionService.createAccountNote(
        user,
        createAccountNoteDto,
      );

      return AccountNoteResponseDto.fromEntity(createdNote);
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Error encountered while creating account note. > Error message: ${error.message}`,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: "ACCOUNTS_NOTE_CONTROLLER",
        fn: "createAccountNote",
        organizationId: user.orgUid,
        body: createAccountNoteDto,
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Put(":noteId")
  @ApiResponseMessage("Account note updated successfully!")
  @ApiUpdateEndpoint({
    summary: "Updates an account note",
    responseType: AccountNoteResponseDto,
  })
  async updateAccountNote(
    @CurrentUser() user: CurrentUser,
    @Param("noteId") noteId: string,
    @Body() updateAccountNoteDto: UpdateAccountNoteDto,
  ): Promise<AccountNoteResponseDto> {
    if (!noteId) {
      throw new BadRequestException("Note ID is required!");
    }

    try {
      const updatedNote = await this.accountNoteActionService.updateAccountNote(
        user,
        noteId,
        updateAccountNoteDto,
      );

      return AccountNoteResponseDto.fromEntity(updatedNote);
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Error encountered while updating account note. > Error message: ${error.message}`,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: "ACCOUNTS_NOTE_CONTROLLER",
        fn: "updateAccountNote",
        organizationId: user.orgUid,
        params: { noteId },
        body: updateAccountNoteDto,
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Delete(":noteId")
  @ApiResponseMessage("Account note deleted successfully!")
  @ApiDeleteEndpoint({
    summary: "Deletes an account note",
  })
  async deleteAccountNote(
    @CurrentUser() user: CurrentUser,
    @Param("noteId") noteId: string,
  ): Promise<void> {
    if (!noteId) {
      throw new BadRequestException("Note ID is required!");
    }

    try {
      await this.accountNoteActionService.deleteAccountNote(user, noteId);
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Error encountered while deleting account note. > Error message: ${error.message}`,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: "ACCOUNTS_NOTE_CONTROLLER",
        fn: "deleteAccountNote",
        organizationId: user.orgUid,
        params: { noteId },
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Delete(":noteId/attachments/:attachmentId")
  @ApiResponseMessage("Attachment removed successfully!")
  @ApiDeleteEndpoint({
    summary: "Removes an attachment from an account note",
  })
  async removeNoteAttachment(
    @CurrentUser() user: CurrentUser,
    @Param("noteId") noteId: string,
    @Param("attachmentId") attachmentId: string,
  ): Promise<void> {
    if (!noteId) {
      throw new BadRequestException("Note ID is required!");
    }

    if (!attachmentId) {
      throw new BadRequestException("Attachment ID is required!");
    }

    try {
      await this.accountNoteActionService.removeNoteAttachment(
        user,
        noteId,
        attachmentId,
      );
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Error encountered while removing note attachment. > Error message: ${error.message}`,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: "ACCOUNTS_NOTE_CONTROLLER",
        fn: "removeNoteAttachment",
        organizationId: user.orgUid,
        params: { noteId, attachmentId },
        user,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }
}
