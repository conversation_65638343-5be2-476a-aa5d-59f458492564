import { Inject, Injectable } from "@nestjs/common";
import { OnEvent } from "@nestjs/event-emitter";
import { SentryService } from "@repo/nestjs-commons/filters/sentry-alerts.filter";
import { ILogger } from "@repo/nestjs-commons/logger";
import {
  AccountAttributeType,
  DEFAULT_ACCOUNT_ATTRIBUTE_VALUE_SEEDS,
  DEFAULT_ACCOUNT_RELATIONSHIP_TYPES,
  UserType,
} from "@repo/thena-platform-entities";
import { CurrentUser } from "../../common/decorators/user.decorator";
import {
  EmittableOrganizationEvents,
  OrganizationCreatedEvent,
} from "../../organization/events/organization.events";
import {
  AccountAttributeValueDeletedEvent,
  EmittableAccountEvents,
  LinkContactsToAccountByEmailDomain,
} from "../events/accounts.events";
import { AccountActivityActionService } from "../services/account-activity.action.service";
import { AccountAttributeValueActionService } from "../services/account-attribute-value.action.service";
import { AccountCommonService } from "../services/account-commons.service";
import { AccountNoteActionService } from "../services/account-note.action.service";
import { AccountRelationshipActionService } from "../services/account-relationship.action.service";
import { AccountTaskActionService } from "../services/account-task.action.service";
import { AccountsService } from "../services/accounts.service";
import { CustomerContactActionService } from "../services/customer-contact.action.service";

@Injectable()
export class AccountsListeners {
  private readonly logSpanId = "[AccountsListeners]";

  constructor(
    @Inject("CustomLogger") private readonly logger: ILogger,
    @Inject("Sentry") private readonly sentryService: SentryService,

    private readonly accountsService: AccountsService,
    private readonly accountCommonService: AccountCommonService,
    private readonly accountAttributeValueActionService: AccountAttributeValueActionService,
    private readonly accountRelationshipActionService: AccountRelationshipActionService,
    private readonly customerContactActionService: CustomerContactActionService,
    private readonly accountActivityActionService: AccountActivityActionService,
    private readonly accountNoteActionService: AccountNoteActionService,
    private readonly accountTaskActionService: AccountTaskActionService,
  ) {}

  @OnEvent(EmittableAccountEvents.ACCOUNT_ATTRIBUTE_VALUE_DELETED)
  async handleAccountAttributeValueDeletedEvent(
    event: AccountAttributeValueDeletedEvent,
  ) {
    const { organizationId, attribute, attributeValueId } = event;

    this.logger.log(
      `${this.logSpanId} Account attribute value deleted event received. OrgId: ${organizationId}, Attribute: ${attribute}, AttributeValueId: ${attributeValueId}`,
    );

    const defaultAttributeValue =
      await this.accountCommonService.findDefaultAttributeValue(
        organizationId,
        attribute,
      );

    try {
      switch (attribute) {
        case AccountAttributeType.ACCOUNT_CLASSIFICATION:
        case AccountAttributeType.ACCOUNT_STATUS:
        case AccountAttributeType.ACCOUNT_HEALTH:
        case AccountAttributeType.ACCOUNT_INDUSTRY: {
          await this.accountsService.updateAccountAttributeValue(
            attributeValueId,
            defaultAttributeValue.id,
            attribute,
          );
          break;
        }
        case AccountAttributeType.CONTACT_TYPE: {
          await this.customerContactActionService.updateContactTypeAttribute(
            attributeValueId,
            defaultAttributeValue.id,
          );
          break;
        }
        case AccountAttributeType.ACTIVITY_STATUS: {
          await this.accountActivityActionService.updateActivityStatusAttribute(
            attributeValueId,
            defaultAttributeValue.id,
          );
          break;
        }
        case AccountAttributeType.ACTIVITY_TYPE: {
          await this.accountActivityActionService.updateActivityTypeAttribute(
            attributeValueId,
            defaultAttributeValue.id,
          );
          break;
        }
        case AccountAttributeType.NOTE_TYPE: {
          await this.accountNoteActionService.updateNoteTypeAttribute(
            attributeValueId,
            defaultAttributeValue.id,
          );
          break;
        }
        case AccountAttributeType.TASK_TYPE:
        case AccountAttributeType.TASK_STATUS:
        case AccountAttributeType.TASK_PRIORITY: {
          await this.accountTaskActionService.updateTaskAttributeValue(
            attributeValueId,
            defaultAttributeValue.id,
            attribute,
          );
          break;
        }
        default: {
          this.logger.error(
            `${this.logSpanId} Unsupported attribute type: ${attribute}`,
          );
          break;
        }
      }
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Failed to update account attribute value for organization ${organizationId}`,
        error,
      );
      this.sentryService.captureException(error, {
        tag: "ACCOUNTS_LISTENERS",
        fn: "handleAccountAttributeValueDeletedEvent",
        organizationId,
        body: event,
      });
    }
  }

  @OnEvent(EmittableOrganizationEvents.ORGANIZATION_CREATED)
  async handleOrganizationCreatedEvent(event: OrganizationCreatedEvent) {
    const { organizationId } = event;

    this.logger.log(
      `${this.logSpanId} Organization created event received. OrgId: ${organizationId}`,
    );

    // Create initial AccountAttributeValue seeds for each attribute type
    for (const [attribute, values] of Object.entries(
      DEFAULT_ACCOUNT_ATTRIBUTE_VALUE_SEEDS,
    )) {
      for (const attributeValue of values) {
        try {
          await this.accountAttributeValueActionService.createAccountAttributeValue(
            {
              orgId: organizationId,
              userType: UserType.BOT_USER,
            } as CurrentUser,
            {
              attribute: attribute as AccountAttributeType,
              value: attributeValue.value,
              icon: attributeValue.configuration.icon,
              color: attributeValue.configuration.color,
              isClosed:
                attribute === AccountAttributeType.TASK_STATUS
                  ? (attributeValue.configuration as { isClosed: boolean })
                      .isClosed
                  : false,
              isDefault: attributeValue.isDefault,
            },
          );
        } catch (error) {
          this.logger.error(
            `${this.logSpanId} Failed to create account attribute value for organization ${organizationId}`,
            error,
          );
          this.sentryService.captureException(error, {
            tag: "ACCOUNTS_LISTENERS",
            fn: "handleOrganizationCreatedEvent",
            organizationId,
            body: event,
          });
        }
      }
    }

    // Create initial AccountRelationshipType seeds
    const createdRelationshipTypes = new Map<string, string>(); // Map to store name -> uid mapping

    for (const relationshipType of DEFAULT_ACCOUNT_RELATIONSHIP_TYPES) {
      try {
        const createdType =
          await this.accountRelationshipActionService.createAccountRelationshipType(
            {
              orgId: organizationId,
              userType: UserType.BOT_USER,
            } as CurrentUser,
            {
              name: relationshipType.name,
              inverseRelationshipId: relationshipType.inverseRelationship
                ? createdRelationshipTypes.get(
                    relationshipType.inverseRelationship,
                  )
                : null,
            },
          );

        // Store the created type's uid for potential inverse relationships
        createdRelationshipTypes.set(relationshipType.name, createdType.uid);
      } catch (error) {
        this.logger.error(
          `${this.logSpanId} Failed to create account relationship type for organization ${organizationId}`,
          error,
        );
        this.sentryService.captureException(error, {
          tag: "ACCOUNTS_LISTENERS",
          fn: "handleOrganizationCreatedEvent",
          organizationId,
          body: event,
        });
      }
    }
  }

  @OnEvent(EmittableAccountEvents.LINK_CONTACTS_TO_ACCOUNT_BY_EMAIL_DOMAIN)
  async handleLinkContactsToAccountByEmailDomainEvent(
    event: LinkContactsToAccountByEmailDomain,
  ) {
    const { organizationId, accountId, domain } = event;

    this.logger.log(
      `${this.logSpanId} Link contacts to account by email domain event received. OrgId: ${organizationId}, AccountId: ${accountId}, Domain: ${domain}`,
    );

    try {
      await this.customerContactActionService.addContactsMatchingDomainToAccount(
        organizationId,
        domain,
        accountId,
      );
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Failed to add contacts matching domain to account for organization ${organizationId}`,
        error,
      );
      this.sentryService.captureException(error, {
        tag: "ACCOUNTS_LISTENERS",
        fn: "handleLinkContactsToAccountByEmailDomainEvent",
        organizationId,
        body: event,
      });
    }
  }
}
