import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import {
  AccountAttributeType,
  AccountAttributeValue,
  AccountNote,
  AccountNoteRepository,
  AccountNoteVisibility,
  AuditLogEntityType,
  AuditLogOp,
  AuditLogVisibility,
  CachedAccountNoteRepository,
  TransactionService,
} from "@repo/thena-platform-entities";
import { AccountEvents } from "@repo/thena-shared-interfaces";
import { cloneDeep, isArray, mergeWith } from "lodash";
import { FindOptionsWhere } from "typeorm";
import { ActivitiesService } from "../../activities/services/activities.service";
import { CurrentUser } from "../../common/decorators/user.decorator";
import { StorageService } from "../../storage/services/storage-service";
import { UsersService } from "../../users/services/users.service";
import {
  CreateAccountNoteDto,
  FindAccountNoteDto,
  UpdateAccountNoteDto,
} from "../dtos/account-note.dto";
import { PaginatedResponseDto } from "../dtos/response/common-response.dto";
import { AccountsSNSEventsFactory } from "../events/accounts-sns-events.factory";
import { AccountCommonService } from "./account-commons.service";

@Injectable()
export class AccountNoteActionService {
  constructor(
    // Inject repositories
    private readonly accountNoteRepository: AccountNoteRepository,
    private readonly cachedAccountNoteRepository: CachedAccountNoteRepository,

    // Accounts services
    private readonly accountCommonService: AccountCommonService,

    // Transaction service
    private readonly transactionService: TransactionService,

    // Activities service
    private readonly activitiesService: ActivitiesService,

    // Users service
    private readonly usersService: UsersService,

    // Storage service
    private readonly storageService: StorageService,

    // Event emitter
    private readonly accountsSNSEventsFactory: AccountsSNSEventsFactory,
  ) {}

  /**
   * Validate and get a note type
   *
   * @param orgId The Organization ID
   * @param noteType The Note Type UID
   * @returns The note type
   */
  private async validateAndGetNoteType(
    orgId: string,
    noteType: string,
  ): Promise<AccountAttributeValue> {
    const noteTypeAttribute = await this.accountCommonService.getAttributeValue(
      noteType,
      AccountAttributeType.NOTE_TYPE,
      orgId,
    );
    if (
      !noteTypeAttribute ||
      noteTypeAttribute.attribute !== AccountAttributeType.NOTE_TYPE
    ) {
      throw new NotFoundException("Note type not found");
    }
    return noteTypeAttribute;
  }

  /**
   * Checks if a note type attribute is in use.
   *
   * @param noteTypeAttributeId The ID of the note type attribute.
   * @returns Whether the note type attribute is in use.
   */
  async isNoteTypeAttributeInUse(
    noteTypeAttributeId: string,
  ): Promise<boolean> {
    const count = await this.accountNoteRepository.count({
      where: { noteType: noteTypeAttributeId, isActive: true },
    });
    return count > 0;
  }

  async updateNoteTypeAttribute(
    prevNoteTypeAttributeId: string,
    newNoteTypeAttributeId: string,
  ): Promise<void> {
    await this.transactionService.runInTransaction(async (txnContext) => {
      // Find account activities using the previous activity status attribute
      const accountNotes = await this.accountNoteRepository.findAll({
        where: { noteType: prevNoteTypeAttributeId },
      });

      if (accountNotes.length === 0) {
        return;
      }

      // Update the activity status attribute for each account activity
      for (const accountNote of accountNotes) {
        accountNote.noteType = newNoteTypeAttributeId;
      }

      // Save the account activities
      await this.accountNoteRepository.saveManyWithTxn(
        txnContext,
        accountNotes,
      );

      // Invalidate the cache for each account
      this.cachedAccountNoteRepository.invalidateAccountNoteCache({
        uids: accountNotes.map((a) => a.uid),
      });
    });
  }

  /**
   * Find an existing account note
   *
   * @param noteId The Note ID
   * @param authorId The Author ID
   * @returns The account note
   */
  async findAccountNote(
    noteId: string,
    authorId: string,
  ): Promise<AccountNote> {
    const accountNote = await this.cachedAccountNoteRepository.findByCondition({
      where: { uid: noteId, isActive: true },
      relations: ["author", "account", "noteTypeAttribute", "attachments"],
    });

    if (
      accountNote &&
      accountNote.visibility === AccountNoteVisibility.PRIVATE &&
      accountNote.authorId !== authorId
    ) {
      // Not Found Error to be handled by the caller
      return null;
    }

    return accountNote;
  }

  /**
   * Find all account notes
   *
   * @param user The current user
   * @param findAccountNoteDto {@link FindAccountNoteDto} The DTO containing the search criteria
   * @returns The list of account notes
   */
  async findAllAccountNotes(
    user: CurrentUser,
    findAccountNoteDto: FindAccountNoteDto,
  ): Promise<PaginatedResponseDto<AccountNote>> {
    const whereClause: FindOptionsWhere<AccountNote> = {
      account: {
        organizationId: user.orgId,
      },
      isActive: true,
    };

    if (findAccountNoteDto.accountId) {
      const account = await this.accountCommonService.validateAndGetAccount(
        findAccountNoteDto.accountId,
        user.orgId,
      );

      whereClause.accountId = account.id;
    }

    if (findAccountNoteDto.type) {
      const noteType = await this.validateAndGetNoteType(
        user.orgId,
        findAccountNoteDto.type,
      );

      whereClause.noteType = noteType.id;
    }

    const results = await this.accountNoteRepository.fetchPaginatedResults(
      {
        page: findAccountNoteDto.page > 0 ? findAccountNoteDto.page - 1 : 0,
        limit: Math.min(findAccountNoteDto.limit ?? 10, 100),
      },
      {
        where: [
          {
            ...whereClause,
            visibility: AccountNoteVisibility.ORGANIZATION,
          },
          {
            ...whereClause,
            visibility: AccountNoteVisibility.PRIVATE,
            authorId: user.sub,
          },
        ],
        relations: ["author", "account", "noteTypeAttribute", "attachments"],
      },
    );

    return {
      results: results.results,
      meta: {
        totalCount: results.total,
        totalPages: Math.ceil(results.total / results.results.length),
        currentPage: findAccountNoteDto.page > 0 ? findAccountNoteDto.page : 1,
        currentPageCount: results.results.length,
      },
    };
  }

  /**
   * Create an account note
   *
   * @param user The current user
   * @param createAccountNoteDto {@link CreateAccountNoteDto} The DTO containing the note details
   * @returns The created account note
   */
  async createAccountNote(
    user: CurrentUser,
    createAccountNoteDto: CreateAccountNoteDto,
  ): Promise<AccountNote> {
    // Find account
    const account = await this.accountCommonService.validateAndGetAccount(
      createAccountNoteDto.accountId,
      user.orgId,
    );

    // get user for author
    const author = await this.usersService.findOneByPublicId(user.uid);

    let noteType: AccountAttributeValue;

    if (!createAccountNoteDto.type) {
      // Use default note type if not provided
      const defaultNoteType =
        await this.accountCommonService.findDefaultAttributeValue(
          user.orgId,
          AccountAttributeType.NOTE_TYPE,
        );

      noteType = defaultNoteType;
    } else {
      // Use provided note type
      noteType = await this.validateAndGetNoteType(
        user.orgId,
        createAccountNoteDto.type,
      );
    }

    const note = this.accountNoteRepository.create({
      account,
      noteTypeAttribute: noteType,
      author,
      visibility:
        createAccountNoteDto.visibility ?? AccountNoteVisibility.ORGANIZATION,
      content: createAccountNoteDto.content ?? "",
      ...(createAccountNoteDto.metadata && {
        metadata: createAccountNoteDto.metadata,
      }),
    });

    const noteId = await this.transactionService.runInTransaction(
      async (txnContext) => {
        // Save in db
        const savedNote = await this.accountNoteRepository.saveWithTxn(
          txnContext,
          note,
        );

        // Save attachments
        if (createAccountNoteDto.attachmentUrls) {
          savedNote.attachments = await this.storageService.attachFilesToEntity(
            createAccountNoteDto.attachmentUrls,
            user.orgId,
          );

          await this.accountNoteRepository.saveWithTxn(txnContext, savedNote);
        }

        // Record audit log
        await this.activitiesService.recordAuditLog(
          {
            organization: { id: user.orgId },
            activityPerformedBy: { id: user.sub },
            entityId: savedNote.id,
            entityType: AuditLogEntityType.ACCOUNT_NOTE,
            op: AuditLogOp.CREATED,
            visibility: AuditLogVisibility.ORGANIZATION,
            activity: `Account note ${savedNote.id} for account ${account.id} was created!`,
            description: `Account note ${savedNote.id} for account ${account.id} was created by ${user.email}!`,
          },
          txnContext,
        );

        // Invalidate cache
        await this.cachedAccountNoteRepository.invalidateAccountNoteCache({
          uids: [savedNote.uid],
        });

        // Publish account note created event to SNS
        const accountNoteCreatedEvent =
          this.accountsSNSEventsFactory.createAccountNoteCreatedSNSEvent(
            user,
            savedNote,
          );

        this.accountCommonService.publishEventToSNSQueue(
          AccountEvents.ACCOUNT_NOTE_CREATED,
          accountNoteCreatedEvent,
          user,
        );

        return savedNote.uid;
      },
    );

    return this.findAccountNote(noteId, user.sub);
  }

  /**
   * Update an account note
   *
   * @param user The current user
   * @param noteId The Note ID
   * @param updateAccountNoteDto {@link UpdateAccountNoteDto} The DTO containing the note details
   * @returns The updated account note
   */
  async updateAccountNote(
    user: CurrentUser,
    noteId: string,
    updateAccountNoteDto: UpdateAccountNoteDto,
  ): Promise<AccountNote> {
    // Find account note
    const note = await this.findAccountNote(noteId, user.sub);

    if (!note) {
      throw new NotFoundException("Account note not found");
    }

    const previousNote = cloneDeep(note);

    if (updateAccountNoteDto.type) {
      const noteType = await this.validateAndGetNoteType(
        user.orgId,
        updateAccountNoteDto.type,
      );

      note.noteTypeAttribute = noteType;
    }

    note.content = updateAccountNoteDto.content ?? previousNote.content;

    if (updateAccountNoteDto.visibility) {
      if (
        updateAccountNoteDto.visibility === AccountNoteVisibility.PRIVATE &&
        previousNote.authorId !== user.sub
      ) {
        throw new BadRequestException(
          "Only the author can make a note private",
        );
      }
      note.visibility = updateAccountNoteDto.visibility;
    }

    if (updateAccountNoteDto.metadata) {
      note.metadata = mergeWith(
        note.metadata || {},
        updateAccountNoteDto.metadata,
        (a, b) => {
          if (isArray(a)) {
            return a.concat(b);
          }
        },
      );
    }

    if (updateAccountNoteDto.attachmentUrls) {
      const newAttachments = await this.storageService.attachFilesToEntity(
        updateAccountNoteDto.attachmentUrls,
        user.orgId,
      );

      note.attachments = [...(note.attachments ?? []), ...newAttachments];
    }

    await this.transactionService.runInTransaction(async (txnContext) => {
      // Save in db
      await this.accountNoteRepository.saveWithTxn(txnContext, note);

      // Record audit log
      await this.activitiesService.recordAuditLog(
        {
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityId: note.id,
          entityType: AuditLogEntityType.ACCOUNT_NOTE,
          op: AuditLogOp.UPDATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: `Account note ${note.id} for account ${note.accountId} was updated!`,
          description: `Account note ${note.id} for account ${note.accountId} was updated by ${user.email}!`,
          metadata: {
            updatedFields: Object.keys(updateAccountNoteDto).map((key) => ({
              field: key,
              previousValue: previousNote[key],
              updatedToValue: note[key],
            })),
          },
        },
        txnContext,
      );

      // Invalidate cache
      await this.cachedAccountNoteRepository.invalidateAccountNoteCache({
        uids: [note.uid],
      });

      // Publish account note updated event to SNS
      const accountNoteUpdatedEvent =
        this.accountsSNSEventsFactory.createAccountNoteUpdatedSNSEvent(
          user,
          previousNote,
          note,
        );

      this.accountCommonService.publishEventToSNSQueue(
        AccountEvents.ACCOUNT_NOTE_UPDATED,
        accountNoteUpdatedEvent,
        user,
      );
    });

    return this.findAccountNote(noteId, user.sub);
  }

  /**
   * Delete an account note
   *
   * @param user The current user
   * @param noteId The Note ID
   * @returns The deleted account note
   */
  async deleteAccountNote(
    user: CurrentUser,
    noteId: string,
    metadata?: Record<string, any>,
  ): Promise<void> {
    const note = await this.findAccountNote(noteId, user.sub);

    if (!note) {
      throw new NotFoundException("Account note not found");
    }

    if (metadata) {
      note.metadata = mergeWith(note.metadata || {}, metadata, (a, b) => {
        if (isArray(a)) {
          return a.concat(b);
        }
      });
    }

    await this.transactionService.runInTransaction(async (txnContext) => {
      note.isActive = false;

      // Save in db
      await this.accountNoteRepository.saveWithTxn(txnContext, note);

      // Record audit log
      await this.activitiesService.recordAuditLog(
        {
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityId: note.id,
          entityType: AuditLogEntityType.ACCOUNT_NOTE,
          op: AuditLogOp.DELETED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: `Account note ${note.id} for account ${note.accountId} was deleted!`,
          description: `Account note ${note.id} for account ${note.accountId} was deleted by ${user.email}!`,
          metadata: {
            updatedFields: [
              {
                field: "isActive",
                previousValue: "true",
                updatedToValue: "false",
              },
            ],
          },
        },
        txnContext,
      );

      // Invalidate cache
      await this.cachedAccountNoteRepository.invalidateAccountNoteCache({
        uids: [note.uid],
      });

      // Publish account note deleted event to SNS
      const accountNoteDeletedEvent =
        this.accountsSNSEventsFactory.createAccountNoteDeletedSNSEvent(
          user,
          note,
        );

      this.accountCommonService.publishEventToSNSQueue(
        AccountEvents.ACCOUNT_NOTE_DELETED,
        accountNoteDeletedEvent,
        user,
      );
    });
  }

  /**
   * Remove an attachment from an account note
   *
   * @param user Current user
   * @param noteId The Note ID
   * @param attachmentId The Attachment ID
   */
  async removeNoteAttachment(
    user: CurrentUser,
    noteId: string,
    attachmentId: string,
  ): Promise<void> {
    const note = await this.findAccountNote(noteId, user.sub);
    if (!note) {
      throw new NotFoundException("Note not found!");
    }
    const previousNote = cloneDeep(note);
    const existingAttachmentIds = note.attachments?.map((a) => a.uid);

    const attachment = note.attachments?.find((a) => a.uid === attachmentId);
    if (!attachment) {
      throw new NotFoundException("Attachment not found!");
    }

    await this.transactionService.runInTransaction(async (txnContext) => {
      note.attachments = note.attachments?.filter(
        (a) => a.uid !== attachmentId,
      );

      // Save in db
      await this.accountNoteRepository.saveWithTxn(txnContext, note);

      // Record audit log
      await this.activitiesService.recordAuditLog(
        {
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityId: note.id,
          entityUid: note.uid,
          entityType: AuditLogEntityType.ACCOUNT_NOTE,
          op: AuditLogOp.UPDATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: `Attachment ${attachmentId} was removed from account note ${note.id}!`,
          description: `Attachment ${attachmentId} was removed from account note ${note.id} by ${user.email}!`,
          metadata: {
            updatedFields: [
              {
                field: "attachments",
                previousValue: existingAttachmentIds?.join(","),
                updatedToValue: note.attachments?.map((a) => a.uid).join(","),
              },
            ],
          },
        },
        txnContext,
      );

      // Publish account note updated event to SNS
      const accountNoteUpdatedEvent =
        this.accountsSNSEventsFactory.createAccountNoteUpdatedSNSEvent(
          user,
          previousNote,
          note,
        );

      this.accountCommonService.publishEventToSNSQueue(
        AccountEvents.ACCOUNT_NOTE_UPDATED,
        accountNoteUpdatedEvent,
        user,
      );
    });
  }
}
