import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import {
  AccountActivity,
  AccountActivityRepository,
  AccountAttributeType,
  AccountAttributeValue,
  AuditLogEntityType,
  AuditLogOp,
  AuditLogVisibility,
  CachedAccountActivityRepository,
  TransactionService,
  User,
} from "@repo/thena-platform-entities";
import { AccountEvents } from "@repo/thena-shared-interfaces";
import { cloneDeep, isArray, mergeWith } from "lodash";
import { FindOptionsWhere } from "typeorm";
import { ActivitiesService } from "../../activities/services/activities.service";
import { CurrentUser } from "../../common/decorators/user.decorator";
import { StorageService } from "../../storage/services/storage-service";
import { UsersService } from "../../users/services/users.service";
import {
  CreateAccountActivityDto,
  FindAccountActivityDto,
  UpdateAccountActivityDto,
} from "../dtos/account-activity.dto";
import { PaginatedResponseDto } from "../dtos/response/common-response.dto";
import { AccountsEventsFactory } from "../events/accounts-events.factory";
import { AccountsSNSEventsFactory } from "../events/accounts-sns-events.factory";
import { AccountCommonService } from "./account-commons.service";

@Injectable()
export class AccountActivityActionService {
  constructor(
    // Injected repositories
    private readonly accountActivityRepository: AccountActivityRepository,
    private readonly cachedAccountActivityRepository: CachedAccountActivityRepository,

    // Accounts services
    private readonly accountCommonService: AccountCommonService,

    // Transaction service
    private readonly transactionService: TransactionService,

    // Activities service
    private readonly activitiesService: ActivitiesService,

    // Users service
    private readonly usersService: UsersService,

    // Storage service
    private readonly storageService: StorageService,

    // Event emitter
    private readonly accountsEventsFactory: AccountsEventsFactory,
    private readonly accountsSNSEventsFactory: AccountsSNSEventsFactory,
  ) {}

  /**
   * Find all account activities
   *
   * @param findAccountActivityDto {@link FindAccountActivityDto} The DTO containing the search criteria
   * @returns The list of account activities
   */
  async findAccountActivities(
    user: CurrentUser,
    findAccountActivityDto: FindAccountActivityDto,
  ): Promise<PaginatedResponseDto<AccountActivity>> {
    const whereClause: FindOptionsWhere<AccountActivity> = {
      account: {
        organizationId: user.orgId,
      },
      isActive: true,
    };

    if (findAccountActivityDto.accountId) {
      const account = await this.accountCommonService.validateAndGetAccount(
        findAccountActivityDto.accountId,
        user.orgId,
      );

      whereClause.accountId = account.id;
    }

    if (findAccountActivityDto.type) {
      const activityType = await this.validateAndGetActivityType(
        user.orgId,
        findAccountActivityDto.type,
      );

      whereClause.typeAttribute = activityType;
    }

    if (findAccountActivityDto.status) {
      const activityStatus = await this.validateAndGetActivityStatus(
        user.orgId,
        findAccountActivityDto.status,
      );

      whereClause.statusAttribute = activityStatus;
    }

    const results = await this.accountActivityRepository.fetchPaginatedResults(
      {
        page:
          findAccountActivityDto.page > 0 ? findAccountActivityDto.page - 1 : 0,
        limit: Math.min(findAccountActivityDto.limit ?? 10, 100),
      },
      {
        where: whereClause,
        relations: [
          "account",
          "creator",
          "typeAttribute",
          "statusAttribute",
          "attachments",
        ],
      },
    );

    return {
      results: results.results,
      meta: {
        totalCount: results.total,
        totalPages: Math.ceil(results.total / results.results.length),
        currentPage:
          findAccountActivityDto.page > 0 ? findAccountActivityDto.page : 1,
        currentPageCount: results.results.length,
      },
    };
  }

  /**
   * Find an existing account activity
   *
   * @param activityId The Activity ID
   * @returns The account activity
   */
  async findAccountActivity(activityId: string): Promise<AccountActivity> {
    const activity = await this.cachedAccountActivityRepository.findByCondition(
      {
        where: { uid: activityId, isActive: true },
        relations: [
          "account",
          "creator",
          "typeAttribute",
          "statusAttribute",
          "attachments",
        ],
      },
    );

    return activity;
  }

  /**
   * Validate and get an activity type
   *
   * @param orgId The Organization ID
   * @param activityType The Activity Type UID / value
   * @returns The activity type
   */
  private async validateAndGetActivityType(
    orgId: string,
    activityType: string,
  ): Promise<AccountAttributeValue> {
    const activityTypeAttribute =
      await this.accountCommonService.getAttributeValue(
        activityType,
        AccountAttributeType.ACTIVITY_TYPE,
        orgId,
      );
    if (
      !activityTypeAttribute ||
      activityTypeAttribute.attribute !== AccountAttributeType.ACTIVITY_TYPE
    ) {
      throw new NotFoundException("Activity type not found");
    }
    return activityTypeAttribute;
  }

  /**
   * Validate and get an activity status
   *
   * @param orgId The Organization ID
   * @param activityStatusUID The Activity Status UID
   * @returns The activity status
   */
  private async validateAndGetActivityStatus(
    orgId: string,
    activityStatusUID: string,
  ): Promise<AccountAttributeValue> {
    const activityStatus = await this.accountCommonService.getAttributeValue(
      activityStatusUID,
      AccountAttributeType.ACTIVITY_STATUS,
      orgId,
    );
    if (
      !activityStatus ||
      activityStatus.attribute !== AccountAttributeType.ACTIVITY_STATUS
    ) {
      throw new NotFoundException("Activity status not found");
    }
    return activityStatus;
  }

  /**
   * Checks if an activity type attribute is in use.
   *
   * @param activityTypeAttributeId The ID of the activity type attribute.
   * @returns Whether the activity type attribute is in use.
   */
  async isActivityTypeAttributeInUse(
    activityTypeAttributeId: string,
  ): Promise<boolean> {
    const count = await this.accountActivityRepository.count({
      where: { type: activityTypeAttributeId, isActive: true },
    });
    return count > 0;
  }

  /**
   * Checks if an activity status attribute is in use.
   *
   * @param activityStatusAttributeId The ID of the activity status attribute.
   * @returns Whether the activity status attribute is in use.
   */
  async isActivityStatusAttributeInUse(
    activityStatusAttributeId: string,
  ): Promise<boolean> {
    const count = await this.accountActivityRepository.count({
      where: { status: activityStatusAttributeId, isActive: true },
    });
    return count > 0;
  }

  async updateActivityTypeAttribute(
    prevActivityTypeAttributeId: string,
    newActivityTypeAttributeId: string,
  ): Promise<void> {
    await this.transactionService.runInTransaction(async (txnContext) => {
      // Find account activities using the previous activity status attribute
      const accountActivities = await this.accountActivityRepository.findAll({
        where: { type: prevActivityTypeAttributeId },
      });

      if (accountActivities.length === 0) {
        return;
      }

      // Update the activity status attribute for each account activity
      for (const accountActivity of accountActivities) {
        accountActivity.type = newActivityTypeAttributeId;
      }

      // Save the account activities
      await this.accountActivityRepository.saveManyWithTxn(
        txnContext,
        accountActivities,
      );

      // Invalidate the cache for each account
      this.cachedAccountActivityRepository.invalidateAccountActivityCache({
        uids: accountActivities.map((a) => a.uid),
      });
    });
  }

  async updateActivityStatusAttribute(
    prevActivityStatusAttributeId: string,
    newActivityStatusAttributeId: string,
  ): Promise<void> {
    await this.transactionService.runInTransaction(async (txnContext) => {
      // Find account activities using the previous activity status attribute
      const accountActivities = await this.accountActivityRepository.findAll({
        where: { status: prevActivityStatusAttributeId },
      });

      if (accountActivities.length === 0) {
        return;
      }

      // Update the activity status attribute for each account activity
      for (const accountActivity of accountActivities) {
        accountActivity.status = newActivityStatusAttributeId;
      }

      // Save the account activities
      await this.accountActivityRepository.saveManyWithTxn(
        txnContext,
        accountActivities,
      );

      // Invalidate the cache for each account
      this.cachedAccountActivityRepository.invalidateAccountActivityCache({
        uids: accountActivities.map((a) => a.uid),
      });
    });
  }

  /**
   * Create a new account activity
   *
   * @param user Current user
   * @param createAccountActivityDto {@link CreateAccountActivityDto} The DTO containing the account activity details
   * @returns The created account activity
   */
  async createAccountActivity(
    user: CurrentUser,
    createAccountActivityDto: CreateAccountActivityDto,
  ): Promise<AccountActivity> {
    // Find account
    const account = await this.accountCommonService.validateAndGetAccount(
      createAccountActivityDto.accountId,
      user.orgId,
    );

    // get user for creator
    const creator = await this.usersService.findOneByPublicId(user.uid);

    // verify if all participants exist
    let participants: User[] = [];

    if (createAccountActivityDto.participants) {
      participants = await this.usersService.findManyByPublicIds(
        createAccountActivityDto.participants,
      );
      if (
        participants.length !== createAccountActivityDto.participants.length
      ) {
        throw new BadRequestException(
          `${
            createAccountActivityDto.participants.length - participants.length
          } participants not found!`,
        );
      }
    }

    let activityType: AccountAttributeValue;
    let activityStatus: AccountAttributeValue;

    if (!createAccountActivityDto.type) {
      // Use default activity type if not provided
      const defaultActivityType =
        await this.accountCommonService.findDefaultAttributeValue(
          user.orgId,
          AccountAttributeType.ACTIVITY_TYPE,
        );

      activityType = defaultActivityType;
    } else {
      // Use provided activity type
      activityType = await this.validateAndGetActivityType(
        user.orgId,
        createAccountActivityDto.type,
      );
    }

    if (!createAccountActivityDto.status) {
      // Use default activity status if not provided
      const defaultActivityStatus =
        await this.accountCommonService.findDefaultAttributeValue(
          user.orgId,
          AccountAttributeType.ACTIVITY_STATUS,
        );

      activityStatus = defaultActivityStatus;
    } else {
      // Use provided activity status
      activityStatus = await this.validateAndGetActivityStatus(
        user.orgId,
        createAccountActivityDto.status,
      );
    }

    const activity = this.accountActivityRepository.create({
      account,
      typeAttribute: activityType,
      statusAttribute: activityStatus,
      title: createAccountActivityDto.title,
      ...(createAccountActivityDto.description && {
        description: createAccountActivityDto.description,
      }),
      ...(createAccountActivityDto.duration && {
        duration: createAccountActivityDto.duration,
      }),
      ...(createAccountActivityDto.location && {
        location: createAccountActivityDto.location,
      }),
      ...(createAccountActivityDto.metadata && {
        metadata: createAccountActivityDto.metadata,
      }),
      activityTimestamp: createAccountActivityDto.activityTimestamp,
      creator,
      participants: participants.map((p) => p.uid),
    });

    const activityId = await this.transactionService.runInTransaction(
      async (txnContext) => {
        // Save in db
        const savedActivity = await this.accountActivityRepository.saveWithTxn(
          txnContext,
          activity,
        );

        // Save attachments
        if (createAccountActivityDto.attachmentUrls) {
          savedActivity.attachments =
            await this.storageService.attachFilesToEntity(
              createAccountActivityDto.attachmentUrls,
              user.orgId,
            );

          await this.accountActivityRepository.saveWithTxn(
            txnContext,
            savedActivity,
          );
        }

        // Record audit log
        await this.activitiesService.recordAuditLog(
          {
            organization: { id: user.orgId },
            activityPerformedBy: { id: user.sub },
            entityId: savedActivity.id,
            entityType: AuditLogEntityType.ACCOUNT_ACTIVITY,
            op: AuditLogOp.CREATED,
            visibility: AuditLogVisibility.ORGANIZATION,
            activity: `Account activity ${savedActivity.id} for account ${account.id} was created!`,
            description: `Account activity ${savedActivity.id} for account ${account.id} was created by ${user.email}!`,
          },
          txnContext,
        );

        // Invalidate cache
        await this.cachedAccountActivityRepository.invalidateAccountActivityCache(
          {
            uids: [savedActivity.uid],
          },
        );

        // Publish account activity created event to SNS
        const accountActivityCreatedEvent =
          this.accountsSNSEventsFactory.createAccountActivityCreatedSNSEvent(
            user,
            savedActivity,
          );

        this.accountCommonService.publishEventToSNSQueue(
          AccountEvents.ACCOUNT_ACTIVITY_CREATED,
          accountActivityCreatedEvent,
          user,
        );

        return savedActivity.uid;
      },
    );

    return this.findAccountActivity(activityId);
  }

  /**
   * Update an existing account activity
   *
   * @param user Current user
   * @param activityId The Activity ID
   * @param updateAccountActivityDto {@link UpdateAccountActivityDto} The DTO containing the account activity details
   * @returns The updated account activity
   */
  async updateAccountActivity(
    user: CurrentUser,
    activityId: string,
    updateAccountActivityDto: UpdateAccountActivityDto,
  ): Promise<AccountActivity> {
    const activity = await this.findAccountActivity(activityId);
    if (!activity) {
      throw new NotFoundException("Activity not found!");
    }

    const prevActivity = cloneDeep(activity);

    if (updateAccountActivityDto.type) {
      const activityType = await this.validateAndGetActivityType(
        user.orgId,
        updateAccountActivityDto.type,
      );

      activity.typeAttribute = activityType;
    }

    if (updateAccountActivityDto.status) {
      const activityStatus = await this.validateAndGetActivityStatus(
        user.orgId,
        updateAccountActivityDto.status,
      );

      activity.statusAttribute = activityStatus;
    }

    if (updateAccountActivityDto.participants) {
      const participants = await this.usersService.findManyByPublicIds(
        updateAccountActivityDto.participants,
      );

      if (
        participants.length !== updateAccountActivityDto.participants.length
      ) {
        throw new BadRequestException(
          `${
            updateAccountActivityDto.participants.length - participants.length
          } participants not found!`,
        );
      }

      activity.participants = updateAccountActivityDto.participants;
    }

    if (updateAccountActivityDto.attachmentUrls) {
      const newAttachments = await this.storageService.attachFilesToEntity(
        updateAccountActivityDto.attachmentUrls,
        user.orgId,
      );

      activity.attachments = [
        ...(activity.attachments ?? []),
        ...newAttachments,
      ];
    }

    if (updateAccountActivityDto.duration !== undefined) {
      activity.duration = updateAccountActivityDto.duration;
    }

    if (updateAccountActivityDto.location !== undefined) {
      activity.location = updateAccountActivityDto.location;
    }

    if (updateAccountActivityDto.metadata) {
      activity.metadata = mergeWith(
        activity.metadata || {},
        updateAccountActivityDto.metadata,
        (a, b) => {
          if (isArray(a)) {
            return a.concat(b);
          }
        },
      );
    }

    activity.title = updateAccountActivityDto.title ?? activity.title;
    activity.description =
      updateAccountActivityDto.description ?? activity.description;
    activity.activityTimestamp = updateAccountActivityDto.activityTimestamp
      ? new Date(updateAccountActivityDto.activityTimestamp)
      : activity.activityTimestamp;

    await this.transactionService.runInTransaction(async (txnContext) => {
      // Save in db
      const savedActivity = await this.accountActivityRepository.saveWithTxn(
        txnContext,
        activity,
      );

      // Record audit log
      await this.activitiesService.recordAuditLog(
        {
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityId: activity.id,
          entityUid: activity.uid,
          entityType: AuditLogEntityType.ACCOUNT_ACTIVITY,
          op: AuditLogOp.UPDATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: `Account activity ${activity.id} was updated!`,
          description: `Account activity ${activity.id} was updated by ${user.email}!`,
          metadata: {
            updatedFields: Object.keys(updateAccountActivityDto).map((key) => ({
              field: key,
              previousValue: prevActivity[key],
              updatedToValue: savedActivity[key],
            })),
          },
        },
        txnContext,
      );

      // Invalidate cache
      await this.cachedAccountActivityRepository.invalidateAccountActivityCache(
        {
          uids: [activity.uid],
        },
      );

      // Publish account activity updated event to SNS
      const accountActivityUpdatedEvent =
        this.accountsSNSEventsFactory.createAccountActivityUpdatedSNSEvent(
          user,
          prevActivity,
          activity,
        );

      this.accountCommonService.publishEventToSNSQueue(
        AccountEvents.ACCOUNT_ACTIVITY_UPDATED,
        accountActivityUpdatedEvent,
        user,
      );
    });

    return this.findAccountActivity(activityId);
  }

  /**
   * Delete an existing account activity
   *
   * @param user Current user
   * @param activityId The Activity ID
   */
  async deleteAccountActivity(
    user: CurrentUser,
    activityId: string,
    metadata?: Record<string, any>,
  ): Promise<void> {
    const activity = await this.findAccountActivity(activityId);
    if (!activity) {
      throw new NotFoundException("Activity not found!");
    }

    activity.isActive = false;
    if (metadata) {
      activity.metadata = mergeWith(
        activity.metadata || {},
        metadata,
        (a, b) => {
          if (isArray(a)) {
            return a.concat(b);
          }
        },
      );
    }

    await this.transactionService.runInTransaction(async (txnContext) => {
      await this.accountActivityRepository.saveWithTxn(txnContext, activity);

      // Record account deleted activity
      await this.activitiesService.recordAuditLog(
        {
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityId: activity.id,
          entityUid: activity.uid,
          entityType: AuditLogEntityType.ACCOUNT_ACTIVITY,
          op: AuditLogOp.DELETED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: `Account activity ${activity.id} was deleted!`,
          description: `Account activity ${activity.id} was deleted by ${user.email}!`,
          metadata: {
            updatedFields: [
              {
                field: "isActive",
                previousValue: "true",
                updatedToValue: "false",
              },
            ],
          },
        },
        txnContext,
      );

      // Invalidate cache
      await this.cachedAccountActivityRepository.invalidateAccountActivityCache(
        {
          uids: [activity.uid],
        },
      );

      // Publish account activity deleted event to SNS
      const accountActivityDeletedEvent =
        this.accountsSNSEventsFactory.createAccountActivityDeletedSNSEvent(
          user,
          activity,
        );

      this.accountCommonService.publishEventToSNSQueue(
        AccountEvents.ACCOUNT_ACTIVITY_DELETED,
        accountActivityDeletedEvent,
        user,
      );
    });
  }

  /**
   * Remove an attachment from an activity
   *
   * @param user Current user
   * @param activityId The Activity ID
   * @param attachmentId The Attachment ID
   */
  async removeActivityAttachment(
    user: CurrentUser,
    activityId: string,
    attachmentId: string,
  ): Promise<void> {
    const activity = await this.findAccountActivity(activityId);
    if (!activity) {
      throw new NotFoundException("Activity not found!");
    }
    const existingAttachmentIds = activity.attachments?.map((a) => a.uid);

    const attachment = activity.attachments?.find(
      (a) => a.uid === attachmentId,
    );
    if (!attachment) {
      throw new NotFoundException("Attachment not found!");
    }

    await this.transactionService.runInTransaction(async (txnContext) => {
      activity.attachments = activity.attachments?.filter(
        (a) => a.uid !== attachmentId,
      );

      // Save in db
      await this.accountActivityRepository.saveWithTxn(txnContext, activity);

      // Record audit log
      await this.activitiesService.recordAuditLog(
        {
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityId: activity.id,
          entityUid: activity.uid,
          entityType: AuditLogEntityType.ACCOUNT_ACTIVITY,
          op: AuditLogOp.UPDATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: `Attachment ${attachmentId} was removed from account activity ${activity.id}!`,
          description: `Attachment ${attachmentId} was removed from account activity ${activity.id} by ${user.email}!`,
          metadata: {
            updatedFields: [
              {
                field: "attachments",
                previousValue: existingAttachmentIds?.join(","),
                updatedToValue: activity.attachments
                  ?.map((a) => a.uid)
                  .join(","),
              },
            ],
          },
        },
        txnContext,
      );

      // Invalidate cache
      await this.cachedAccountActivityRepository.invalidateAccountActivityCache(
        {
          uids: [activity.uid],
        },
      );
    });
  }
}
