import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import {
  AccountRelationship,
  AccountRelationshipRepository,
  AccountRelationshipType,
  AccountRelationshipTypeRepository,
  AuditLogEntityType,
  AuditLogOp,
  AuditLogVisibility,
  CachedAccountRelationshipRepository,
  CachedAccountRelationshipTypeRepository,
  TransactionService,
  UserType,
} from "@repo/thena-platform-entities";
import { AccountEvents } from "@repo/thena-shared-interfaces";
import { cloneDeep, isArray, mergeWith } from "lodash";
import { DeepPartial, FindOptionsWhere } from "typeorm";
import { ActivitiesService } from "../../activities/services/activities.service";
import { CurrentUser } from "../../common/decorators";
import {
  CreateAccountRelationshipDto,
  CreateAccountRelationshipTypeDto,
  FindAccountRelationshipDto,
  UpdateAccountRelationshipDto,
  UpdateAccountRelationshipTypeDto,
} from "../dtos/account-relationship.dto";
import { PaginatedResponseDto } from "../dtos/response/common-response.dto";
import { AccountsSNSEventsFactory } from "../events/accounts-sns-events.factory";
import { AccountCommonService } from "./account-commons.service";

@Injectable()
export class AccountRelationshipActionService {
  constructor(
    // Injected repositories
    private readonly accountRelationshipTypeRepository: AccountRelationshipTypeRepository,
    private readonly cachedAccountRelationshipTypeRepository: CachedAccountRelationshipTypeRepository,
    private readonly accountRelationshipRepository: AccountRelationshipRepository,
    private readonly cachedAccountRelationshipRepository: CachedAccountRelationshipRepository,

    // Accounts services
    private readonly accountCommonService: AccountCommonService,

    // Transaction service
    private readonly transactionService: TransactionService,

    // Activities service
    private readonly activitiesService: ActivitiesService,

    // Event emitter
    private readonly accountsSNSEventsFactory: AccountsSNSEventsFactory,
  ) {}

  private async hasCyclicRelationship(
    accountId: string,
    relatedAccountId: string,
    relationshipTypeId: string,
  ): Promise<boolean> {
    // Set to keep track of visited accounts
    const visited = new Set<string>();
    // Queue to process accounts in BFS manner
    const queue: Array<{ id: string; depth: number }> = [];
    const MAX_DEPTH = 10; // Prevent infinite loops in deep relationships

    // Start with the account we're trying to relate to (relatedAccountId)
    queue.push({ id: relatedAccountId, depth: 0 });

    while (queue.length > 0) {
      const { id: currentAccountId, depth } = queue.shift()!;

      // Skip if we've reached max depth or already visited this account
      if (depth >= MAX_DEPTH || visited.has(currentAccountId)) {
        continue;
      }

      visited.add(currentAccountId);

      // Get all relationships where current account is the primary account
      const relationships = await this.accountRelationshipRepository.findAll({
        where: {
          accountId: currentAccountId,
          relationship: relationshipTypeId, // Only check relationships of same type
          isActive: true,
        },
      });

      for (const relationship of relationships) {
        // If we find the original account in the chain, we have a cycle
        if (relationship.relatedAccountId === accountId) {
          return true;
        }

        // Add related account to queue for processing
        if (!visited.has(relationship.relatedAccountId)) {
          queue.push({
            id: relationship.relatedAccountId,
            depth: depth + 1,
          });
        }
      }
    }

    return false;
  }

  /**
   * Finds all active account relationship types for an organization
   *
   * @param organizationId - The ID of the organization
   * @returns A promise that resolves to an array of account relationship types
   */
  findAllAccountRelationshipTypes(
    organizationId: string,
  ): Promise<AccountRelationshipType[]> {
    return this.cachedAccountRelationshipTypeRepository.findAll({
      where: {
        organizationId,
        isActive: true,
      },
      relations: ["inverseRelationship"],
    });
  }

  private findAccountRelationshipTypeByUID(
    uid: string,
    organizationId: string,
  ): Promise<AccountRelationshipType> {
    return this.cachedAccountRelationshipTypeRepository.findByCondition({
      where: {
        uid,
        organizationId,
        isActive: true,
      },
      relations: ["inverseRelationship"],
    });
  }

  /**
   * Creates a new account relationship type
   *
   * - If inverse relationship specified, links them
   * - If inverse relationship for specified relationship type already exists, throws BadRequestException
   *
   * @param user - The current user
   * @param createDto - The create DTO
   * @returns The created account relationship type
   */
  async createAccountRelationshipType(
    user: CurrentUser,
    createDto: CreateAccountRelationshipTypeDto,
  ): Promise<AccountRelationshipType> {
    const organizationId = user.orgId;

    const savedType = await this.transactionService.runInTransaction(
      async (txnContext) => {
        const uidsToInvalidate: string[] = [];

        // Create the relationship type
        const relationshipType = this.accountRelationshipTypeRepository.create({
          organizationId,
          name: createDto.name,
          isActive: true,
          ...(createDto.metadata && {
            metadata: createDto.metadata,
          }),
        });

        const savedRelationshipType =
          await this.accountRelationshipTypeRepository.saveWithTxn(
            txnContext,
            relationshipType,
          );

        uidsToInvalidate.push(savedRelationshipType.uid);

        // If inverse relationship specified, link them
        if (createDto.inverseRelationshipId) {
          const inverseType =
            await this.accountRelationshipTypeRepository.findByCondition({
              where: {
                uid: createDto.inverseRelationshipId,
                organizationId,
                isActive: true,
              },
            });

          if (!inverseType) {
            throw new NotFoundException("Inverse relationship type not found");
          }

          // If inverse relationship for specified inverse relationship type already exists, throw BadRequestException
          if (inverseType.inverseRelationshipId) {
            throw new BadRequestException(
              `Relationship type ${inverseType.name} already has an inverse relationship`,
            );
          }

          // Update both types with inverse relationship
          savedRelationshipType.inverseRelationship = inverseType;
          inverseType.inverseRelationship = savedRelationshipType;

          uidsToInvalidate.push(inverseType.uid);

          await this.accountRelationshipTypeRepository.saveWithTxn(
            txnContext,
            inverseType,
          );
          await this.accountRelationshipTypeRepository.saveWithTxn(
            txnContext,
            savedRelationshipType,
          );

          // Record audit log
          await this.activitiesService.recordAuditLog(
            {
              organization: { id: organizationId },
              activityPerformedBy:
                user.userType === UserType.BOT_USER ? null : { id: user.sub },
              isAutomated: user.userType === UserType.BOT_USER,
              entityId: inverseType.id,
              entityUid: inverseType.uid,
              entityType: AuditLogEntityType.ACCOUNT_RELATIONSHIP_TYPE,
              op: AuditLogOp.UPDATED,
              visibility: AuditLogVisibility.ORGANIZATION,
              activity: `Inverse relationship type for ${inverseType.name} was added!`,
              description: `Inverse relationship type for ${inverseType.name} was added by ${user.email}!`,
              metadata: {
                updatedFields: [
                  {
                    field: "inverseRelationshipId",
                    previousValue: null,
                    updatedToValue: savedRelationshipType.uid,
                  },
                ],
              },
            },
            txnContext,
          );
        }

        // Invalidate cache
        await this.cachedAccountRelationshipTypeRepository.invalidateAccountRelationshipTypeCache(
          {
            organizationId,
            uids: uidsToInvalidate,
          },
        );

        await this.activitiesService.recordAuditLog(
          {
            organization: { id: organizationId },
            activityPerformedBy: { id: user.sub },
            entityId: savedRelationshipType.id,
            entityUid: savedRelationshipType.uid,
            entityType: AuditLogEntityType.ACCOUNT_RELATIONSHIP_TYPE,
            op: AuditLogOp.CREATED,
            visibility: AuditLogVisibility.ORGANIZATION,
            activity: `Account relationship type ${savedRelationshipType.name} was created!`,
            description: `Account relationship type ${savedRelationshipType.name} was created by ${user.email}!`,
          },
          txnContext,
        );

        return savedRelationshipType;
      },
    );

    // Return from cache
    return this.findAccountRelationshipTypeByUID(savedType.uid, organizationId);
  }

  /**
   * Updates an existing account relationship type
   *
   * - If inverse relationship is specified as null
   * 	 - Removes existing inverse relationship
   *   - Marks inverse relationship as null for the current inverse relationship type
   * - If inverse relationship is specified
   *   - Attaches a new inverse relationship to the current relationship type
   *   - Marks current relationship type as inverse relationship for new inverse relationship type
   *   - Marks inverse relationship as null for the current inverse relationship type
   *
   * @param user - The current user
   * @param relationshipTypeUID - The UID of the relationship type
   * @param updateDto - The update DTO
   * @returns The updated account relationship type
   */
  async updateAccountRelationshipType(
    user: CurrentUser,
    relationshipTypeUID: string,
    updateDto: UpdateAccountRelationshipTypeDto,
  ): Promise<AccountRelationshipType> {
    const organizationId = user.orgId;

    // Find existing relationship type from cache
    const existingType =
      await this.cachedAccountRelationshipTypeRepository.findByCondition({
        where: { uid: relationshipTypeUID, organizationId, isActive: true },
      });

    if (!existingType) {
      throw new NotFoundException("Relationship type not found");
    }

    const savedType = await this.transactionService.runInTransaction(
      async (txnContext) => {
        const uidsToInvalidate: string[] = [];

        uidsToInvalidate.push(existingType.uid);

        const updates: DeepPartial<AccountRelationshipType> = {
          ...existingType,
          name: updateDto.name ?? existingType.name,
          ...(updateDto.metadata && {
            metadata: mergeWith(
              existingType.metadata || {},
              updateDto.metadata,
              (a, b) => {
                if (isArray(a)) {
                  return a.concat(b);
                }
              },
            ),
          }),
        };

        // Handle inverse relationship changes
        if (updateDto.inverseRelationshipId !== undefined) {
          if (updateDto.inverseRelationshipId === null) {
            // Remove existing inverse relationship
            updates.inverseRelationship = null;
          } else {
            // Attach a new inverse relationship
            const newInverseType =
              await this.accountRelationshipTypeRepository.findByCondition({
                where: {
                  uid: updateDto.inverseRelationshipId,
                  organizationId,
                  isActive: true,
                },
              });

            if (!newInverseType) {
              throw new NotFoundException(
                "Inverse relationship type not found",
              );
            }

            // If inverse relationship for specified inverse relationship type already exists, throw BadRequestException
            if (newInverseType.inverseRelationshipId) {
              throw new BadRequestException(
                `Relationship type ${newInverseType.name} already has an inverse relationship`,
              );
            }

            updates.inverseRelationship = newInverseType;
            newInverseType.inverseRelationship = existingType;

            if (updateDto.metadata) {
              newInverseType.metadata = mergeWith(
                newInverseType.metadata || {},
                updateDto.metadata,
                (a, b) => {
                  if (isArray(a)) {
                    return a.concat(b);
                  }
                },
              );
            }
            // Save new inverse relationship type
            await this.accountRelationshipTypeRepository.saveWithTxn(
              txnContext,
              newInverseType,
            );

            uidsToInvalidate.push(newInverseType.uid);

            // Record audit log
            await this.activitiesService.recordAuditLog(
              {
                organization: { id: organizationId },
                activityPerformedBy: { id: user.sub },
                entityId: newInverseType.id,
                entityUid: newInverseType.uid,
                entityType: AuditLogEntityType.ACCOUNT_RELATIONSHIP_TYPE,
                op: AuditLogOp.UPDATED,
                visibility: AuditLogVisibility.ORGANIZATION,
                activity: `Inverse relationship type for ${newInverseType.name} was added!`,
                description: `Inverse relationship type for ${newInverseType.name} was added by ${user.email}!`,
                metadata: {
                  updatedFields: [
                    {
                      field: "inverseRelationshipId",
                      previousValue: null,
                      updatedToValue: existingType.id,
                    },
                  ],
                },
              },
              txnContext,
            );
          }

          // Remove inverse relationship from the current inverse relationship type
          const prevInverseType =
            await this.accountRelationshipTypeRepository.findByCondition({
              where: {
                id: existingType.inverseRelationshipId,
                organizationId,
                isActive: true,
              },
            });

          prevInverseType.inverseRelationship = null;

          if (updateDto.metadata) {
            prevInverseType.metadata = mergeWith(
              prevInverseType.metadata || {},
              updateDto.metadata,
              (a, b) => {
                if (isArray(a)) {
                  return a.concat(b);
                }
              },
            );
          }

          // Save current inverse relationship type
          await this.accountRelationshipTypeRepository.saveWithTxn(
            txnContext,
            prevInverseType,
          );

          uidsToInvalidate.push(prevInverseType.uid);

          // Record audit log
          await this.activitiesService.recordAuditLog(
            {
              organization: { id: organizationId },
              activityPerformedBy: { id: user.sub },
              entityId: prevInverseType.id,
              entityUid: prevInverseType.uid,
              entityType: AuditLogEntityType.ACCOUNT_RELATIONSHIP_TYPE,
              op: AuditLogOp.UPDATED,
              visibility: AuditLogVisibility.ORGANIZATION,
              activity: `Inverse relationship type for ${prevInverseType.name} was added!`,
              description: `Inverse relationship type for ${prevInverseType.name} was added by ${user.email}!`,
              metadata: {
                updatedFields: [
                  {
                    field: "inverseRelationshipId",
                    previousValue: existingType.id,
                    updatedToValue: null,
                  },
                ],
              },
            },
            txnContext,
          );
        }

        // Save updated relationship type
        const updatedType =
          await this.accountRelationshipTypeRepository.saveWithTxn(
            txnContext,
            updates,
          );

        await this.activitiesService.recordAuditLog(
          {
            organization: { id: organizationId },
            activityPerformedBy: { id: user.sub },
            entityId: updatedType.id,
            entityUid: updatedType.uid,
            entityType: AuditLogEntityType.ACCOUNT_RELATIONSHIP_TYPE,
            op: AuditLogOp.UPDATED,
            visibility: AuditLogVisibility.ORGANIZATION,
            activity: `Account relationship type ${updatedType.name} was updated!`,
            description: `Account relationship type ${updatedType.name} was updated by ${user.email}!`,
          },
          txnContext,
        );

        // Invalidate cache
        await this.cachedAccountRelationshipTypeRepository.invalidateAccountRelationshipTypeCache(
          {
            organizationId,
            uids: uidsToInvalidate,
          },
        );

        return updatedType;
      },
    );

    // Return from cache
    return this.findAccountRelationshipTypeByUID(savedType.uid, organizationId);
  }

  /**
   * Deletes an existing account relationship type
   *
   * - If relationship type has active relationships, throws BadRequestException
   * - Marks the relationship type as inactive
   * - Removes inverse relationship for the current relationship type if exists
   * - Removes inverse relationship for the inverse relationship type if exists
   *
   * @param user - The current user
   * @param relationshipTypeUID - The UID of the relationship type
   */
  async deleteAccountRelationshipType(
    user: CurrentUser,
    relationshipTypeUID: string,
    metadata?: Record<string, any>,
  ): Promise<void> {
    const organizationId = user.orgId;

    // Find existing relationship type from cache
    const existingType =
      await this.cachedAccountRelationshipTypeRepository.findByCondition({
        where: { uid: relationshipTypeUID, organizationId, isActive: true },
      });

    if (!existingType) {
      throw new NotFoundException("Relationship type not found");
    }

    // If relationship type has active relationships, throw BadRequestException
    const relationships = await this.accountRelationshipRepository.findAll({
      where: { relationship: existingType.id, isActive: true },
    });

    if (relationships.length > 0) {
      throw new BadRequestException(
        "Cannot delete relationship type with active relationships",
      );
    }

    await this.transactionService.runInTransaction(async (txnContext) => {
      const uidsToInvalidate: string[] = [];

      const inverseRelationshipId = existingType.inverseRelationshipId;

      // Remove inverse relationship for the inverse relationship type if exists
      if (inverseRelationshipId) {
        const inverseType =
          await this.accountRelationshipTypeRepository.findByCondition({
            where: { id: existingType.inverseRelationshipId },
          });

        if (inverseType) {
          // If relationship type has active relationships, throw BadRequestException
          const relationships =
            await this.accountRelationshipRepository.findAll({
              where: { relationship: inverseType.id, isActive: true },
            });

          if (relationships.length > 0) {
            throw new BadRequestException(
              "Cannot delete relationship type as inverse relationship type has active relationships",
            );
          }

          inverseType.inverseRelationship = null;

          // Save in db
          await this.accountRelationshipTypeRepository.saveWithTxn(
            txnContext,
            inverseType,
          );

          uidsToInvalidate.push(inverseType.uid);

          // Record audit log
          await this.activitiesService.recordAuditLog(
            {
              organization: { id: organizationId },
              activityPerformedBy: { id: user.sub },
              entityId: inverseType.id,
              entityUid: inverseType.uid,
              entityType: AuditLogEntityType.ACCOUNT_RELATIONSHIP_TYPE,
              op: AuditLogOp.UPDATED,
              visibility: AuditLogVisibility.ORGANIZATION,
              activity: `Inverse relationship type for ${inverseType.name} was removed!`,
              description: `Inverse relationship type for ${inverseType.name} was removed by ${user.email}!`,
            },
            txnContext,
          );
        }
      }

      // Mark the relationship type as inactive
      existingType.inverseRelationship = null;
      existingType.isActive = false;
      existingType.deletedAt = new Date();

      if (metadata) {
        existingType.metadata = mergeWith(
          existingType.metadata || {},
          metadata,
          (a, b) => {
            if (isArray(a)) {
              return a.concat(b);
            }
          },
        );
      }

      uidsToInvalidate.push(existingType.uid);

      // Save in db
      await this.accountRelationshipTypeRepository.saveWithTxn(
        txnContext,
        existingType,
      );

      // Invalidate cache
      await this.cachedAccountRelationshipTypeRepository.invalidateAccountRelationshipTypeCache(
        {
          organizationId,
          uids: uidsToInvalidate,
        },
      );

      // Record audit log
      await this.activitiesService.recordAuditLog(
        {
          organization: { id: organizationId },
          activityPerformedBy: { id: user.sub },
          entityId: existingType.id,
          entityUid: existingType.uid,
          entityType: AuditLogEntityType.ACCOUNT_RELATIONSHIP_TYPE,
          op: AuditLogOp.DELETED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: `Account relationship type ${existingType.name} was deleted!`,
          description: `Account relationship type ${existingType.name} was deleted by ${user.email}!`,
          metadata: {
            updatedFields: [
              {
                field: "isActive",
                previousValue: "true",
                updatedToValue: "false",
              },
              {
                field: "inverseRelationshipId",
                previousValue: inverseRelationshipId,
                updatedToValue: null,
              },
            ],
          },
        },
        txnContext,
      );
    });
  }

  /**
   * Find all active account relationships for an account
   * @param user - The current user
   * @param accountId - The UID of the account
   * @returns A promise that resolves to an array of account relationships
   */
  async findAllAccountRelationships(
    user: CurrentUser,
    findAllRelationshipsDto: FindAccountRelationshipDto,
  ): Promise<PaginatedResponseDto<AccountRelationship>> {
    const whereClause: FindOptionsWhere<AccountRelationship> = {
      account: {
        organizationId: user.orgId,
      },
      isActive: true,
    };

    if (findAllRelationshipsDto.accountId) {
      const account = await this.accountCommonService.validateAndGetAccount(
        findAllRelationshipsDto.accountId,
        user.orgId,
      );

      whereClause.accountId = account.id;
    }

    if (findAllRelationshipsDto.relationshipTypeId) {
      const relationshipType = await this.findAccountRelationshipTypeByUID(
        findAllRelationshipsDto.relationshipTypeId,
        user.orgId,
      );

      if (!relationshipType) {
        throw new NotFoundException("Relationship type not found");
      }

      whereClause.relationship = relationshipType.id;
    }

    const results =
      await this.accountRelationshipRepository.fetchPaginatedResults(
        {
          page:
            findAllRelationshipsDto.page > 0
              ? findAllRelationshipsDto.page - 1
              : 0,
          limit: Math.min(findAllRelationshipsDto.limit ?? 10, 100),
        },
        {
          where: whereClause,
          relations: ["account", "relatedAccount", "relationshipType"],
        },
      );

    return {
      results: results.results,
      meta: {
        totalCount: results.total,
        totalPages: Math.ceil(results.total / results.results.length),
        currentPage:
          findAllRelationshipsDto.page > 0 ? findAllRelationshipsDto.page : 1,
        currentPageCount: results.results.length,
      },
    };
  }

  private findAccountRelationship(
    accountId: string,
    relatedAccountId: string,
  ): Promise<AccountRelationship> {
    return this.cachedAccountRelationshipRepository.findByCondition({
      where: { accountId, relatedAccountId, isActive: true },
      relations: ["account", "relatedAccount", "relationshipType"],
    });
  }

  private findAccountRelationshipByUID(
    uid: string,
  ): Promise<AccountRelationship> {
    return this.cachedAccountRelationshipRepository.findByCondition({
      where: { uid, isActive: true },
      relations: ["account", "relatedAccount", "relationshipType"],
    });
  }

  /**
   * Create a new account relationship
   *
   * - If cyclic relationship detected, throws BadRequestException
   * - If inverse relationship exists for the relationship type, creates inverse relationship
   *
   * @param user - The current user
   * @param createDto - The create DTO
   * @returns The created account relationship
   */
  async createAccountRelationship(
    user: CurrentUser,
    createDto: CreateAccountRelationshipDto,
  ): Promise<AccountRelationship> {
    const organizationId = user.orgId;

    const account = await this.accountCommonService.validateAndGetAccount(
      createDto.accountId,
      organizationId,
    );

    const relatedAccount =
      await this.accountCommonService.validateAndGetAccount(
        createDto.relatedAccountId,
        organizationId,
      );

    const relationshipType =
      await this.accountRelationshipTypeRepository.findByCondition({
        where: {
          uid: createDto.relationshipType,
          organizationId,
          isActive: true,
        },
        relations: ["inverseRelationship"],
      });

    if (!relationshipType) {
      throw new NotFoundException("Relationship type not found");
    }

    const existingRelationship = await this.findAccountRelationship(
      account.id,
      relatedAccount.id,
    );

    if (existingRelationship) {
      throw new BadRequestException("Relationship already exists");
    }

    if (relationshipType.inverseRelationshipId) {
      const hasCyclicRelationship = await this.hasCyclicRelationship(
        account.id,
        relatedAccount.id,
        relationshipType.id,
      );

      if (hasCyclicRelationship) {
        throw new BadRequestException("Cyclic relationship detected");
      }
    }

    const savedRelationship = await this.transactionService.runInTransaction(
      async (txnContext) => {
        const relationship = this.accountRelationshipRepository.create({
          account,
          relatedAccount,
          relationshipType,
          isActive: true,
          ...(createDto.metadata && {
            metadata: createDto.metadata,
          }),
        });

        const savedRelationship =
          await this.accountRelationshipRepository.saveWithTxn(
            txnContext,
            relationship,
          );

        // Invalidate cache
        await this.cachedAccountRelationshipRepository.invalidateAccountRelationshipCache(
          {
            accountId: account.id,
            relatedAccountId: relatedAccount.id,
            uid: savedRelationship.uid,
          },
        );

        // Record audit log
        await this.activitiesService.recordAuditLog({
          organization: { id: organizationId },
          activityPerformedBy: { id: user.sub },
          entityId: savedRelationship.id,
          entityUid: savedRelationship.uid,
          entityType: AuditLogEntityType.ACCOUNT_RELATIONSHIP,
          op: AuditLogOp.CREATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: `Account relationship ${relationshipType.id} between ${account.id} and ${relatedAccount.id} was created!`,
          description: `Account relationship ${relationshipType.id} between ${account.id} and ${relatedAccount.id} was created by ${user.email}!`,
        });

        if (relationshipType.inverseRelationshipId) {
          const inverseRelationship = this.accountRelationshipRepository.create(
            {
              account: relatedAccount,
              relatedAccount: account,
              relationshipType: relationshipType.inverseRelationship,
              isActive: true,
              ...(createDto.metadata && {
                metadata: createDto.metadata,
              }),
            },
          );

          const savedInverseRelationship =
            await this.accountRelationshipRepository.saveWithTxn(
              txnContext,
              inverseRelationship,
            );

          await this.cachedAccountRelationshipRepository.invalidateAccountRelationshipCache(
            {
              accountId: relatedAccount.id,
              relatedAccountId: account.id,
              uid: savedInverseRelationship.uid,
            },
          );

          // Record audit log
          await this.activitiesService.recordAuditLog({
            organization: { id: organizationId },
            activityPerformedBy: { id: user.sub },
            entityId: savedInverseRelationship.id,
            entityUid: savedInverseRelationship.uid,
            entityType: AuditLogEntityType.ACCOUNT_RELATIONSHIP,
            op: AuditLogOp.CREATED,
            visibility: AuditLogVisibility.ORGANIZATION,
            activity: `Account relationship ${relationshipType.inverseRelationshipId} between ${relatedAccount.id} and ${account.id} was created!`,
            description: `Account relationship ${relationshipType.inverseRelationshipId} between ${relatedAccount.id} and ${account.id} was created by ${user.email}!`,
          });

          const accountInverseRelationshipCreatedEvent =
            this.accountsSNSEventsFactory.createAccountRelationshipCreatedSNSEvent(
              user,
              savedInverseRelationship,
            );

          this.accountCommonService.publishEventToSNSQueue(
            AccountEvents.ACCOUNT_RELATIONSHIP_CREATED,
            accountInverseRelationshipCreatedEvent,
            user,
          );
        }

        // Note: To be done at the end as we can't roll back event published to SNS if something fails while creating inverse relationship
        // Publish account relationship created event to SNS
        const accountRelationshipCreatedEvent =
          this.accountsSNSEventsFactory.createAccountRelationshipCreatedSNSEvent(
            user,
            savedRelationship,
          );

        this.accountCommonService.publishEventToSNSQueue(
          AccountEvents.ACCOUNT_RELATIONSHIP_CREATED,
          accountRelationshipCreatedEvent,
          user,
        );

        return savedRelationship;
      },
    );

    return this.findAccountRelationshipByUID(savedRelationship.uid);
  }

  /**
   * Updates an existing account relationship (updates only relationship type between two accounts)
   *
   * - If existing relationship type and new relationship type both have inverse relationship, updates inverse relationship to new relationship type's inverse relationship
   * - If existing relationship type does not have inverse relationship, creates inverse relationship with new relationship type's inverse relationship
   * - If new relationship type does not have inverse relationship, deactivates the existing inverse relationship
   *
   * @param user - The current user
   * @param relationshipUID - The UID of the relationship=
   * @param updateDto - The update DTO
   * @returns The updated account relationship
   */
  async updateAccountRelationship(
    user: CurrentUser,
    relationshipUID: string,
    updateDto: UpdateAccountRelationshipDto,
  ): Promise<AccountRelationship> {
    const currentRelationship = await this.findAccountRelationshipByUID(
      relationshipUID,
    );

    const prevRelationship = cloneDeep(currentRelationship);

    if (!currentRelationship) {
      throw new NotFoundException("Relationship not found");
    }

    const prevRelationshipType = currentRelationship.relationshipType;

    const newRelationshipType = await this.findAccountRelationshipTypeByUID(
      updateDto.relationshipType,
      user.orgId,
    );

    if (!newRelationshipType) {
      throw new NotFoundException("Relationship type not found");
    }

    const savedRelation = await this.transactionService.runInTransaction(
      async (txnContext) => {
        const relationship =
          await this.accountRelationshipRepository.saveWithTxn(txnContext, {
            ...currentRelationship,
            relationshipType: newRelationshipType,
            ...(updateDto.metadata && {
              metadata: mergeWith(
                currentRelationship.metadata,
                updateDto.metadata,
                (a, b) => {
                  if (isArray(a)) {
                    return a.concat(b);
                  }
                },
              ),
            }),
          });

        if (
          prevRelationshipType.inverseRelationshipId &&
          newRelationshipType.inverseRelationshipId
        ) {
          // Update inverse relationship with new relationship type's inverse relationship
          const existingInverseRelationship =
            await this.findAccountRelationship(
              currentRelationship.relatedAccountId,
              currentRelationship.accountId,
            );

          const prevInverseRelationship = cloneDeep(
            existingInverseRelationship,
          );

          existingInverseRelationship.relationshipType =
            newRelationshipType.inverseRelationship;

          existingInverseRelationship.metadata = mergeWith(
            existingInverseRelationship.metadata,
            updateDto.metadata,
            (a, b) => {
              if (isArray(a)) {
                return a.concat(b);
              }
            },
          );

          const inverseRelationship =
            await this.accountRelationshipRepository.saveWithTxn(
              txnContext,
              existingInverseRelationship,
            );

          // Invalidate cache
          await this.cachedAccountRelationshipRepository.invalidateAccountRelationshipCache(
            {
              accountId: inverseRelationship.accountId,
              relatedAccountId: inverseRelationship.relatedAccountId,
              uid: inverseRelationship.uid,
            },
          );

          // Record audit log
          await this.activitiesService.recordAuditLog({
            organization: { id: user.orgId },
            activityPerformedBy: { id: user.sub },
            entityId: inverseRelationship.id,
            entityUid: inverseRelationship.uid,
            entityType: AuditLogEntityType.ACCOUNT_RELATIONSHIP,
            op: AuditLogOp.UPDATED,
            visibility: AuditLogVisibility.ORGANIZATION,
            activity: `Account relationship between ${currentRelationship.relatedAccountId} and ${currentRelationship.accountId} was updated to ${newRelationshipType.inverseRelationshipId}!`,
            description: `Account relationship between ${currentRelationship.relatedAccountId} and ${currentRelationship.accountId} was updated to ${newRelationshipType.inverseRelationshipId} by ${user.email}!`,
            metadata: {
              updatedFields: [
                {
                  field: "relationship",
                  previousValue: prevRelationshipType.inverseRelationshipId,
                  updatedToValue: newRelationshipType.inverseRelationshipId,
                },
              ],
            },
          });

          // Publish account relationship type updated event to SNS
          const accountRelationshipTypeUpdatedEvent =
            this.accountsSNSEventsFactory.createAccountRelationshipUpdatedSNSEvent(
              user,
              prevInverseRelationship,
              inverseRelationship,
            );

          this.accountCommonService.publishEventToSNSQueue(
            AccountEvents.ACCOUNT_RELATIONSHIP_UPDATED,
            accountRelationshipTypeUpdatedEvent,
            user,
          );
        } else if (prevRelationshipType.inverseRelationshipId) {
          // Mark inverse relationship inactive
          const existingInverseRelationship =
            await this.findAccountRelationship(
              currentRelationship.relatedAccountId,
              currentRelationship.accountId,
            );

          existingInverseRelationship.isActive = false;
          existingInverseRelationship.deletedAt = new Date();

          existingInverseRelationship.metadata = mergeWith(
            existingInverseRelationship.metadata,
            updateDto.metadata,
            (a, b) => {
              if (isArray(a)) {
                return a.concat(b);
              }
            },
          );

          const inverseRelationship =
            await this.accountRelationshipRepository.saveWithTxn(
              txnContext,
              existingInverseRelationship,
            );

          // Invalidate cache
          await this.cachedAccountRelationshipRepository.invalidateAccountRelationshipCache(
            {
              accountId: inverseRelationship.accountId,
              relatedAccountId: inverseRelationship.relatedAccountId,
              uid: inverseRelationship.uid,
            },
          );

          // Record audit log
          await this.activitiesService.recordAuditLog({
            organization: { id: user.orgId },
            activityPerformedBy: { id: user.sub },
            entityId: inverseRelationship.id,
            entityUid: inverseRelationship.uid,
            entityType: AuditLogEntityType.ACCOUNT_RELATIONSHIP,
            op: AuditLogOp.DELETED,
            visibility: AuditLogVisibility.ORGANIZATION,
            activity: `Account relationship between ${currentRelationship.relatedAccountId} and ${currentRelationship.accountId} was removed!`,
            description: `Account relationship between ${currentRelationship.relatedAccountId} and ${currentRelationship.accountId} was removed by ${user.email}!`,
            metadata: {
              updatedFields: [
                {
                  field: "isActive",
                  previousValue: "true",
                  updatedToValue: "false",
                },
                {
                  field: "deletedAt",
                  previousValue: null,
                  updatedToValue: new Date().toISOString(),
                },
              ],
            },
          });

          // Publish account relationship deleted event to SNS
          const accountInverseRelationshipDeletedEvent =
            this.accountsSNSEventsFactory.createAccountRelationshipDeletedSNSEvent(
              user,
              inverseRelationship,
            );

          this.accountCommonService.publishEventToSNSQueue(
            AccountEvents.ACCOUNT_RELATIONSHIP_DELETED,
            accountInverseRelationshipDeletedEvent,
            user,
          );
        } else if (newRelationshipType.inverseRelationshipId) {
          const inverseRelationship = this.accountRelationshipRepository.create(
            {
              account: currentRelationship.relatedAccount,
              relatedAccount: currentRelationship.account,
              relationshipType: newRelationshipType.inverseRelationship,
              isActive: true,
              ...(updateDto.metadata && {
                metadata: updateDto.metadata,
              }),
            },
          );

          const savedInverseRelationship =
            await this.accountRelationshipRepository.saveWithTxn(
              txnContext,
              inverseRelationship,
            );

          // Record audit log
          await this.activitiesService.recordAuditLog({
            organization: { id: user.orgId },
            activityPerformedBy: { id: user.sub },
            entityId: savedInverseRelationship.id,
            entityUid: savedInverseRelationship.uid,
            entityType: AuditLogEntityType.ACCOUNT_RELATIONSHIP,
            op: AuditLogOp.CREATED,
            visibility: AuditLogVisibility.ORGANIZATION,
            activity: `Account relationship ${newRelationshipType.inverseRelationshipId} between ${currentRelationship.relatedAccountId} and ${currentRelationship.accountId} was created!`,
            description: `Account relationship ${newRelationshipType.inverseRelationshipId} between ${currentRelationship.relatedAccountId} and ${currentRelationship.accountId} was created by ${user.email}!`,
          });

          // Publish account relationship created event to SNS
          const accountInverseRelationshipCreatedEvent =
            this.accountsSNSEventsFactory.createAccountRelationshipCreatedSNSEvent(
              user,
              savedInverseRelationship,
            );

          this.accountCommonService.publishEventToSNSQueue(
            AccountEvents.ACCOUNT_RELATIONSHIP_CREATED,
            accountInverseRelationshipCreatedEvent,
            user,
          );
        }

        // Invalidate cache
        await this.cachedAccountRelationshipRepository.invalidateAccountRelationshipCache(
          {
            accountId: relationship.accountId,
            relatedAccountId: relationship.relatedAccountId,
            uid: relationship.uid,
          },
        );

        // Record audit log
        await this.activitiesService.recordAuditLog({
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityId: relationship.id,
          entityUid: relationship.uid,
          entityType: AuditLogEntityType.ACCOUNT_RELATIONSHIP,
          op: AuditLogOp.UPDATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: `Account relationship between ${relationship.accountId} and ${relationship.relatedAccountId} was updated to ${newRelationshipType.id}!`,
          description: `Account relationship between ${relationship.accountId} and ${relationship.relatedAccountId} was updated to ${newRelationshipType.id} by ${user.email}!`,
          metadata: {
            updatedFields: [
              {
                field: "relationship",
                previousValue: prevRelationshipType.id,
                updatedToValue: newRelationshipType.id,
              },
            ],
          },
        });

        // Publish account relationship updated event to SNS
        const accountRelationshipTypeUpdatedEvent =
          this.accountsSNSEventsFactory.createAccountRelationshipUpdatedSNSEvent(
            user,
            prevRelationship,
            relationship,
          );

        this.accountCommonService.publishEventToSNSQueue(
          AccountEvents.ACCOUNT_RELATIONSHIP_UPDATED,
          accountRelationshipTypeUpdatedEvent,
          user,
        );

        return relationship;
      },
    );

    return this.findAccountRelationshipByUID(savedRelation.uid);
  }

  /**
   * Deletes an existing account relationship
   *
   * - If inverse relationship exists, deactivates the inverse relationship
   *
   * @param user - The current user
   * @param relationshipUID - The UID of the relationship
   */
  async deleteAccountRelationship(
    user: CurrentUser,
    relationshipUID: string,
    metadata?: Record<string, any>,
  ): Promise<void> {
    const currentRelationship = await this.findAccountRelationshipByUID(
      relationshipUID,
    );

    if (!currentRelationship) {
      throw new NotFoundException("Relationship not found");
    }

    const existingRelationshipType = currentRelationship.relationshipType;

    return await this.transactionService.runInTransaction(
      async (txnContext) => {
        if (existingRelationshipType.inverseRelationshipId) {
          const inverseRelationship = this.accountRelationshipRepository.create(
            {
              account: currentRelationship.relatedAccount,
              relatedAccount: currentRelationship.account,
              relationshipType: existingRelationshipType.inverseRelationship,
              isActive: false,
              deletedAt: new Date(),
            },
          );

          const savedInverseRelationship =
            await this.accountRelationshipRepository.saveWithTxn(
              txnContext,
              inverseRelationship,
            );

          // Invalidate cache
          await this.cachedAccountRelationshipRepository.invalidateAccountRelationshipCache(
            {
              accountId: savedInverseRelationship.accountId,
              relatedAccountId: savedInverseRelationship.relatedAccountId,
              uid: savedInverseRelationship.uid,
            },
          );

          // Record audit log
          await this.activitiesService.recordAuditLog({
            organization: { id: user.orgId },
            activityPerformedBy: { id: user.sub },
            entityId: savedInverseRelationship.id,
            entityUid: savedInverseRelationship.uid,
            entityType: AuditLogEntityType.ACCOUNT_RELATIONSHIP,
            op: AuditLogOp.UPDATED,
            visibility: AuditLogVisibility.ORGANIZATION,
            activity: `Account relationship between ${savedInverseRelationship.accountId} and ${savedInverseRelationship.relatedAccountId} was removed!`,
            description: `Account relationship between ${savedInverseRelationship.accountId} and ${savedInverseRelationship.relatedAccountId} was removed by ${user.email}!`,
            metadata: {
              updatedFields: [
                {
                  field: "isActive",
                  previousValue: "true",
                  updatedToValue: "false",
                },
                {
                  field: "deletedAt",
                  previousValue: null,
                  updatedToValue: new Date().toISOString(),
                },
              ],
            },
          });

          // Publish account relationship deleted event to SNS
          const accountInverseRelationshipDeletedEvent =
            this.accountsSNSEventsFactory.createAccountRelationshipDeletedSNSEvent(
              user,
              savedInverseRelationship,
            );

          this.accountCommonService.publishEventToSNSQueue(
            AccountEvents.ACCOUNT_RELATIONSHIP_DELETED,
            accountInverseRelationshipDeletedEvent,
            user,
          );
        }

        currentRelationship.isActive = false;
        currentRelationship.deletedAt = new Date();
        currentRelationship.metadata = mergeWith(
          currentRelationship.metadata,
          metadata,
          (a, b) => {
            if (isArray(a)) {
              return a.concat(b);
            }
          },
        );

        // Invalidate cache
        await this.cachedAccountRelationshipRepository.invalidateAccountRelationshipCache(
          {
            accountId: currentRelationship.accountId,
            relatedAccountId: currentRelationship.relatedAccountId,
            uid: currentRelationship.uid,
          },
        );

        // Record audit log
        await this.activitiesService.recordAuditLog({
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityId: currentRelationship.id,
          entityUid: currentRelationship.uid,
          entityType: AuditLogEntityType.ACCOUNT_RELATIONSHIP,
          op: AuditLogOp.UPDATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: `Account relationship between ${currentRelationship.accountId} and ${currentRelationship.relatedAccountId} was removed!`,
          description: `Account relationship between ${currentRelationship.accountId} and ${currentRelationship.relatedAccountId} was removed by ${user.email}!`,
          metadata: {
            updatedFields: [
              {
                field: "isActive",
                previousValue: "true",
                updatedToValue: "false",
              },
              {
                field: "deletedAt",
                previousValue: null,
                updatedToValue: new Date().toISOString(),
              },
            ],
          },
        });

        // Publish account relationship deleted event to SNS
        const accountRelationshipDeletedEvent =
          this.accountsSNSEventsFactory.createAccountRelationshipDeletedSNSEvent(
            user,
            currentRelationship,
          );

        this.accountCommonService.publishEventToSNSQueue(
          AccountEvents.ACCOUNT_RELATIONSHIP_DELETED,
          accountRelationshipDeletedEvent,
          user,
        );

        return;
      },
    );
  }
}
