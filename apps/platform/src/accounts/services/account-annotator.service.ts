import { Injectable, NotFoundException } from "@nestjs/common";
import { accounts } from "@repo/shared-proto";
import {
  Account,
  AccountAttributeType,
  AccountAttributeValue,
  AccountAttributeValueRepository,
  AccountRepository,
  CustomerContact,
  CustomerContactRepository,
} from "@repo/thena-platform-entities";
import { FieldMetadataService } from "../../common/services/field-metadata.service";
import { AccountAttributeValueActionService } from "./account-attribute-value.action.service";

@Injectable()
export class AccountAnnotatorService {
  constructor(
    private readonly fieldMetadataService: FieldMetadataService,
    private readonly accountRepository: AccountRepository,
    private readonly accountAttributeValueRepository: AccountAttributeValueRepository,
    private readonly customerContactRepository: CustomerContactRepository,

    private readonly accountAttributeValueActionService: AccountAttributeValueActionService,
  ) {}

  /**
   * Get field metadata for accounts with dynamic choices
   */
  getAccountFieldMetadata(): accounts.GetAccountFieldMetadataResponse {
    return this.fieldMetadataService.getFieldMetadata(Account);
  }

  /**
   * Get field metadata for account attribute values
   */
  async getAccountAttributeValueFieldMetadata(
    attributeType: AccountAttributeType,
    organizationId: string,
  ): Promise<accounts.GetAccountAttributeValueFieldMetadataResponse> {
    const baseMetadata = this.fieldMetadataService.getFieldMetadata(
      AccountAttributeValue,
    );

    if (!attributeType) {
      return baseMetadata;
    }

    const enhancedFields = { ...baseMetadata.fields };

    // Get all attribute values for this type
    const attributeValues =
      await this.accountAttributeValueActionService.findAccountAttributeValues(
        {
          orgId: organizationId,
        },
        attributeType,
      );

    // Enhance status field
    enhancedFields.id.constraints = enhancedFields.id.constraints || {};
    enhancedFields.id.constraints.dynamicChoices = attributeValues.map(
      (attrValue) => ({
        label: attrValue.uid,
        value: attrValue.uid,
      }),
    );

    enhancedFields.value.constraints = enhancedFields.value.constraints || {};
    enhancedFields.value.constraints.dynamicChoices = attributeValues.map(
      (attrValue) => ({
        label: attrValue.value,
        value: attrValue.value,
      }),
    );

    return {
      fields: enhancedFields,
    };
  }

  /**
   * Get field metadata for account attribute values
   */
  getCustomerContactFieldMetadata(): accounts.GetCustomerContactFieldMetadataResponse {
    return this.fieldMetadataService.getFieldMetadata(CustomerContact);
  }

  /**
   * Get account data with relations
   */
  async getAccountData(
    accountId: string,
    relations: string[],
    organizationId: string,
  ): Promise<accounts.GetAccountDataResponse> {
    const account = await this.accountRepository.findByCondition({
      where: { uid: accountId, organizationId },
      relations,
    });

    if (!account) {
      throw new NotFoundException("Account not found");
    }

    return {
      data: JSON.stringify(account),
    };
  }

  /**
   * Get account attribute value data with relations
   */
  async getAccountAttributeValueData(
    attributeValueId: string,
    relations: string[],
    organizationId: string,
  ): Promise<accounts.GetAccountAttributeValueDataResponse> {
    const attributeValue =
      await this.accountAttributeValueRepository.findByCondition({
        where: {
          uid: attributeValueId,
          organizationId,
        },
        relations: [...relations],
      });

    if (!attributeValue) {
      throw new NotFoundException("Account attribute value not found");
    }

    return {
      data: JSON.stringify(attributeValue),
    };
  }

  /**
   * Get account attribute value data with relations
   */
  async getCustomerContactData(
    customerContactId: string,
    relations: string[],
    organizationId: string,
  ): Promise<accounts.GetCustomerContactDataResponse> {
    const customerContact =
      await this.customerContactRepository.findByCondition({
        where: {
          uid: customerContactId,
          organizationId,
        },
        relations: [...relations],
      });

    if (!customerContact) {
      throw new NotFoundException("Customer contact not found");
    }

    return {
      data: JSON.stringify(customerContact),
    };
  }
}
