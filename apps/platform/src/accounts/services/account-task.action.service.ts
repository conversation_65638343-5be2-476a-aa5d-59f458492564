import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import {
  AccountAttributeType,
  AccountAttributeValue,
  AccountTask,
  AccountTaskRepository,
  AuditLogEntityType,
  AuditLogOp,
  AuditLogVisibility,
  CachedAccountTaskRepository,
  TransactionService,
} from "@repo/thena-platform-entities";
import { AccountEvents } from "@repo/thena-shared-interfaces";
import { cloneDeep, isArray, mergeWith } from "lodash";
import { FindOptionsWhere } from "typeorm";
import { ActivitiesService } from "../../activities/services/activities.service";
import { CurrentUser } from "../../common/decorators/user.decorator";
import { StorageService } from "../../storage/services/storage-service";
import { UsersService } from "../../users/services/users.service";
import {
  CreateAccountTaskDto,
  FindAccountTaskDto,
  UpdateAccountTaskDto,
} from "../dtos/account-task.dto";
import { PaginatedResponseDto } from "../dtos/response/common-response.dto";
import { AccountsSNSEventsFactory } from "../events/accounts-sns-events.factory";
import { AccountCommonService } from "./account-commons.service";

@Injectable()
export class AccountTaskActionService {
  constructor(
    // Inject repositories
    private readonly accountTaskRepository: AccountTaskRepository,
    private readonly cachedAccountTaskRepository: CachedAccountTaskRepository,

    // Accounts services
    private readonly accountCommonService: AccountCommonService,

    // Transaction service
    private readonly transactionService: TransactionService,

    // Activities service
    private readonly activitiesService: ActivitiesService,

    // Users service
    private readonly usersService: UsersService,

    // Storage service
    private readonly storageService: StorageService,

    // Event emitter
    private readonly accountsSNSEventsFactory: AccountsSNSEventsFactory,
  ) {}

  /**
   * Validates and gets an account task attribute value. (One of the task type, task status, or task priority)
   *
   * @param attributeValue The UID of the attribute value
   * @param organizationId The ID of the organization
   * @param attributeType The attribute type
   * @returns The account attribute value
   */
  private async validateAndGetAttributeValue(
    attributeValue: string,
    organizationId: string,
    attributeType: AccountAttributeType,
  ): Promise<AccountAttributeValue> {
    const attributeValueEntity =
      await this.accountCommonService.getAttributeValue(
        attributeValue,
        attributeType,
        organizationId,
      );

    if (
      !attributeValueEntity ||
      attributeValueEntity.attribute !== attributeType
    ) {
      throw new NotFoundException(
        `Provided value for ${attributeType} not found`,
      );
    }

    return attributeValueEntity;
  }

  /**
   * Checks if an attribute value is in use.
   *
   * @param attributeValueId The ID of the attribute value
   * @param attributeType The attribute type
   * @returns Whether the attribute value is in use
   */
  async isTaskAttributeValueInUse(
    attributeValueId: string,
    attributeType: AccountAttributeType,
  ): Promise<boolean> {
    switch (attributeType) {
      case AccountAttributeType.TASK_TYPE: {
        const count = await this.accountTaskRepository.count({
          where: { type: attributeValueId, isActive: true },
        });
        return count > 0;
      }
      case AccountAttributeType.TASK_STATUS: {
        const count = await this.accountTaskRepository.count({
          where: { status: attributeValueId, isActive: true },
        });
        return count > 0;
      }
      case AccountAttributeType.TASK_PRIORITY: {
        const count = await this.accountTaskRepository.count({
          where: { priority: attributeValueId, isActive: true },
        });
        return count > 0;
      }
      default: {
        return false;
      }
    }
  }

  async updateTaskAttributeValue(
    attributeValueId: string,
    defaultAttributeValueId: string,
    attributeType: AccountAttributeType,
  ): Promise<void> {
    const attributeTypeColMapping = {
      [AccountAttributeType.TASK_TYPE]: "type",
      [AccountAttributeType.TASK_STATUS]: "status",
      [AccountAttributeType.TASK_PRIORITY]: "priority",
    };

    await this.transactionService.runInTransaction(async (txnContext) => {
      // Find account activities using the previous activity status attribute
      const accountTasks = await this.accountTaskRepository.findAll({
        where: { [attributeTypeColMapping[attributeType]]: attributeValueId },
      });

      if (accountTasks.length === 0) {
        return;
      }

      // Update the activity status attribute for each account activity
      for (const accountTask of accountTasks) {
        accountTask[attributeTypeColMapping[attributeType]] =
          defaultAttributeValueId;
      }

      // Save the account activities
      await this.accountTaskRepository.saveManyWithTxn(
        txnContext,
        accountTasks,
      );

      // Invalidate the cache for each account
      this.cachedAccountTaskRepository.invalidateAccountTaskCache({
        uids: accountTasks.map((a) => a.uid),
      });
    });
  }

  /**
   * Finds an account task.
   *
   * @param taskId The ID of the task
   * @returns The found account task
   */
  findAccountTask(taskId: string): Promise<AccountTask> {
    return this.cachedAccountTaskRepository.findByCondition({
      where: { uid: taskId, isActive: true },
      relations: [
        "account",
        "activity",
        "assignee",
        "typeAttribute",
        "statusAttribute",
        "priorityAttribute",
        "creator",
        "attachments",
      ],
    });
  }

  /**
   * Finds all account tasks.
   *
   * @param user The current user
   * @param findAccountTasksDto The find account tasks DTO
   * @returns The found account tasks
   */
  async findAllAccountTasks(
    user: CurrentUser,
    findAccountTasksDto: FindAccountTaskDto,
  ): Promise<PaginatedResponseDto<AccountTask>> {
    const where: FindOptionsWhere<AccountTask> = {
      account: {
        organizationId: user.orgId,
      },
      isActive: true,
    };

    if (findAccountTasksDto.accountId) {
      // Validate and get account
      const account = await this.accountCommonService.validateAndGetAccount(
        findAccountTasksDto.accountId,
        user.orgId,
      );

      where.accountId = account.id;
    }

    if (findAccountTasksDto.activityId) {
      // Validate and get activity
      const activity = await this.accountCommonService.validateAndGetActivity(
        findAccountTasksDto.activityId,
        user.orgId,
      );

      if (where.accountId && activity.accountId !== where.accountId) {
        throw new BadRequestException(
          "Activity does not belong to the same account",
        );
      }

      where.activityId = activity.id;
    }

    if (findAccountTasksDto.assigneeId) {
      const assignee = await this.usersService.findOneByPublicId(
        findAccountTasksDto.assigneeId,
      );

      if (!assignee) {
        throw new NotFoundException("Assignee not found");
      }

      where.assigneeId = assignee.id;
    }

    if (findAccountTasksDto.type) {
      const taskType = await this.validateAndGetAttributeValue(
        findAccountTasksDto.type,
        user.orgId,
        AccountAttributeType.TASK_TYPE,
      );

      where.type = taskType.id;
    }

    if (findAccountTasksDto.status) {
      const taskStatus = await this.validateAndGetAttributeValue(
        findAccountTasksDto.status,
        user.orgId,
        AccountAttributeType.TASK_STATUS,
      );

      where.status = taskStatus.id;
    }

    if (findAccountTasksDto.priority) {
      const taskPriority = await this.validateAndGetAttributeValue(
        findAccountTasksDto.priority,
        user.orgId,
        AccountAttributeType.TASK_PRIORITY,
      );

      where.priority = taskPriority.id;
    }

    const results = await this.accountTaskRepository.fetchPaginatedResults(
      {
        page: findAccountTasksDto.page > 0 ? findAccountTasksDto.page - 1 : 0,
        limit: Math.min(findAccountTasksDto.limit ?? 10, 100),
      },
      {
        where,
        relations: [
          "account",
          "activity",
          "assignee",
          "typeAttribute",
          "statusAttribute",
          "priorityAttribute",
          "creator",
          "attachments",
        ],
      },
    );

    return {
      results: results.results,
      meta: {
        totalCount: results.total,
        totalPages: Math.ceil(results.total / results.results.length),
        currentPage:
          findAccountTasksDto.page > 0 ? findAccountTasksDto.page : 1,
        currentPageCount: results.results.length,
      },
    };
  }

  /**
   * Creates an account task.
   *
   * @param user The current user
   * @param createAccountTaskDto The create account task DTO
   * @returns The created account task
   */
  async createAccountTask(
    user: CurrentUser,
    createAccountTaskDto: CreateAccountTaskDto,
  ): Promise<AccountTask> {
    // validate and get account
    const account = await this.accountCommonService.validateAndGetAccount(
      createAccountTaskDto.accountId,
      user.orgId,
    );

    if (!account) {
      throw new NotFoundException("Account not found");
    }

    // get user for assignee
    const assignee = await this.usersService.findOneByPublicId(
      createAccountTaskDto.assigneeId,
    );

    if (!assignee) {
      throw new NotFoundException("Assignee not found");
    }

    // get user for creator
    const creator = await this.usersService.findOneByPublicId(user.uid);

    let taskType: AccountAttributeValue;
    let taskStatus: AccountAttributeValue;
    let taskPriority: AccountAttributeValue;

    if (!createAccountTaskDto.type) {
      // Use default task type if not provided
      const defaultTaskType =
        await this.accountCommonService.findDefaultAttributeValue(
          user.orgId,
          AccountAttributeType.TASK_TYPE,
        );

      taskType = defaultTaskType;
    } else {
      // Use provided task type
      taskType = await this.validateAndGetAttributeValue(
        createAccountTaskDto.type,
        user.orgId,
        AccountAttributeType.TASK_TYPE,
      );
    }

    if (!createAccountTaskDto.status) {
      // Use default task status if not provided
      const defaultTaskStatus =
        await this.accountCommonService.findDefaultAttributeValue(
          user.orgId,
          AccountAttributeType.TASK_STATUS,
        );

      taskStatus = defaultTaskStatus;
    } else {
      // Use provided task status
      taskStatus = await this.validateAndGetAttributeValue(
        createAccountTaskDto.status,
        user.orgId,
        AccountAttributeType.TASK_STATUS,
      );
    }

    if (!createAccountTaskDto.priority) {
      // Use default task priority if not provided
      const defaultTaskPriority =
        await this.accountCommonService.findDefaultAttributeValue(
          user.orgId,
          AccountAttributeType.TASK_PRIORITY,
        );

      taskPriority = defaultTaskPriority;
    } else {
      // Use provided task priority
      taskPriority = await this.validateAndGetAttributeValue(
        createAccountTaskDto.priority,
        user.orgId,
        AccountAttributeType.TASK_PRIORITY,
      );
    }

    const task = this.accountTaskRepository.create({
      account,
      assignee,
      title: createAccountTaskDto.title,
      description: createAccountTaskDto.description ?? "",
      typeAttribute: taskType,
      statusAttribute: taskStatus,
      priorityAttribute: taskPriority,
      creator,
      ...(createAccountTaskDto.metadata && {
        metadata: createAccountTaskDto.metadata,
      }),
    });

    if (createAccountTaskDto.activityId) {
      // validate and get activity
      const activity = await this.accountCommonService.validateAndGetActivity(
        createAccountTaskDto.activityId,
        user.orgId,
      );

      if (activity.accountId !== account.id) {
        throw new BadRequestException(
          "Activity does not belong to the same account",
        );
      }

      task.activity = activity;
    }

    const taskId = await this.transactionService.runInTransaction(
      async (txnContext) => {
        // Save in db
        const savedTask = await this.accountTaskRepository.saveWithTxn(
          txnContext,
          task,
        );

        // Save attachments
        if (createAccountTaskDto.attachmentUrls) {
          savedTask.attachments = await this.storageService.attachFilesToEntity(
            createAccountTaskDto.attachmentUrls,
            user.orgId,
          );

          await this.accountTaskRepository.saveWithTxn(txnContext, savedTask);
        }

        // Record audit log
        await this.activitiesService.recordAuditLog({
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityId: savedTask.id,
          entityType: AuditLogEntityType.ACCOUNT_TASK,
          op: AuditLogOp.CREATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: `Account task ${savedTask.id} for account ${account.id} was created!`,
          description: `Account task ${savedTask.id} for account ${account.id} was created by ${user.email}!`,
        });

        // Invalidate cache
        await this.cachedAccountTaskRepository.invalidateAccountTaskCache({
          uids: [savedTask.uid],
        });

        // Publish account task created event to SNS
        const accountTaskCreatedEvent =
          this.accountsSNSEventsFactory.createAccountTaskCreatedSNSEvent(
            user,
            savedTask,
          );

        this.accountCommonService.publishEventToSNSQueue(
          AccountEvents.ACCOUNT_TASK_CREATED,
          accountTaskCreatedEvent,
          user,
        );

        return savedTask.uid;
      },
    );

    return this.findAccountTask(taskId);
  }

  /**
   * Updates an account task.
   *
   * @param user The current user
   * @param taskId The ID of the task
   * @param updateAccountTaskDto The update account task DTO
   * @returns The updated account task
   */
  async updateAccountTask(
    user: CurrentUser,
    taskId: string,
    updateAccountTaskDto: UpdateAccountTaskDto,
  ): Promise<AccountTask> {
    // validate and get task
    const task = await this.findAccountTask(taskId);

    if (!task) {
      throw new NotFoundException("Task not found");
    }

    const previousTask = cloneDeep(task);

    task.title = updateAccountTaskDto.title ?? previousTask.title;
    task.description =
      updateAccountTaskDto.description ?? previousTask.description;

    // Update activity
    if (updateAccountTaskDto.activityId) {
      const activity = await this.accountCommonService.validateAndGetActivity(
        updateAccountTaskDto.activityId,
        user.orgId,
      );

      if (activity.accountId !== task.accountId) {
        throw new BadRequestException(
          "Activity does not belong to the same account",
        );
      }

      task.activity = activity;
    }

    // Update assignee
    if (updateAccountTaskDto.assigneeId) {
      const assignee = await this.usersService.findOneByPublicId(
        updateAccountTaskDto.assigneeId,
      );

      if (!assignee) {
        throw new NotFoundException("Assignee not found");
      }

      task.assignee = assignee;
    }

    // Update task type
    if (updateAccountTaskDto.type) {
      const taskType = await this.validateAndGetAttributeValue(
        updateAccountTaskDto.type,
        user.orgId,
        AccountAttributeType.TASK_TYPE,
      );

      task.typeAttribute = taskType;
    }

    // Update task status
    if (updateAccountTaskDto.status) {
      const taskStatus = await this.validateAndGetAttributeValue(
        updateAccountTaskDto.status,
        user.orgId,
        AccountAttributeType.TASK_STATUS,
      );

      task.statusAttribute = taskStatus;
    }

    // Update task priority
    if (updateAccountTaskDto.priority) {
      const taskPriority = await this.validateAndGetAttributeValue(
        updateAccountTaskDto.priority,
        user.orgId,
        AccountAttributeType.TASK_PRIORITY,
      );

      task.priorityAttribute = taskPriority;
    }

    if (updateAccountTaskDto.metadata) {
      task.metadata = mergeWith(
        task.metadata || {},
        updateAccountTaskDto.metadata,
        (a, b) => {
          if (isArray(a)) {
            return a.concat(b);
          }
        },
      );
    }

    if (updateAccountTaskDto.attachmentUrls) {
      const newAttachments = await this.storageService.attachFilesToEntity(
        updateAccountTaskDto.attachmentUrls,
        user.orgId,
      );

      task.attachments = [...(task.attachments ?? []), ...newAttachments];
    }

    await this.transactionService.runInTransaction(async (txnContext) => {
      // Save in db
      await this.accountTaskRepository.saveWithTxn(txnContext, task);

      // Record audit log
      await this.activitiesService.recordAuditLog({
        organization: { id: user.orgId },
        activityPerformedBy: { id: user.sub },
        entityId: task.id,
        entityType: AuditLogEntityType.ACCOUNT_TASK,
        op: AuditLogOp.UPDATED,
        visibility: AuditLogVisibility.ORGANIZATION,
        activity: `Account task ${task.id} for account ${task.accountId} was updated!`,
        description: `Account task ${task.id} for account ${task.accountId} was updated by ${user.email}!`,
        metadata: {
          updatedFields: Object.keys(updateAccountTaskDto).map((key) => ({
            field: key,
            previousValue: previousTask[key],
            newValue: task[key],
          })),
        },
      });

      // Invalidate cache
      await this.cachedAccountTaskRepository.invalidateAccountTaskCache({
        uids: [taskId],
      });

      // Publish account task updated event to SNS
      const accountTaskUpdatedEvent =
        this.accountsSNSEventsFactory.createAccountTaskUpdatedSNSEvent(
          user,
          previousTask,
          task,
        );

      this.accountCommonService.publishEventToSNSQueue(
        AccountEvents.ACCOUNT_TASK_UPDATED,
        accountTaskUpdatedEvent,
        user,
      );
    });

    return this.findAccountTask(taskId);
  }

  /**
   * Deletes an account task.
   *
   * @param user The current user
   * @param taskId The ID of the task
   * @returns The deleted account task
   */
  async deleteAccountTask(
    user: CurrentUser,
    taskId: string,
    metadata?: Record<string, any>,
  ): Promise<void> {
    const task = await this.findAccountTask(taskId);

    if (!task) {
      throw new NotFoundException("Task not found");
    }

    task.isActive = false;

    if (metadata) {
      task.metadata = mergeWith(task.metadata || {}, metadata, (a, b) => {
        if (isArray(a)) {
          return a.concat(b);
        }
      });
    }

    await this.transactionService.runInTransaction(async (txnContext) => {
      // Save in db
      await this.accountTaskRepository.saveWithTxn(txnContext, task);

      // Record audit log
      await this.activitiesService.recordAuditLog({
        organization: { id: user.orgId },
        activityPerformedBy: { id: user.sub },
        entityId: task.id,
        entityType: AuditLogEntityType.ACCOUNT_TASK,
        op: AuditLogOp.DELETED,
        visibility: AuditLogVisibility.ORGANIZATION,
        activity: `Account task ${task.id} for account ${task.accountId} was deleted!`,
        description: `Account task ${task.id} for account ${task.accountId} was deleted by ${user.email}!`,
        metadata: {
          updatedFields: [
            {
              field: "isActive",
              previousValue: "true",
              updatedToValue: "false",
            },
          ],
        },
      });

      // Invalidate cache
      await this.cachedAccountTaskRepository.invalidateAccountTaskCache({
        uids: [taskId],
      });

      // Publish account task deleted event to SNS
      const accountTaskDeletedEvent =
        this.accountsSNSEventsFactory.createAccountTaskDeletedSNSEvent(
          user,
          task,
        );

      this.accountCommonService.publishEventToSNSQueue(
        AccountEvents.ACCOUNT_TASK_DELETED,
        accountTaskDeletedEvent,
        user,
      );
    });
  }

  /**
   * Remove an attachment from an account task
   *
   * @param user Current user
   * @param taskId The Task ID
   * @param attachmentId The Attachment ID
   */
  async removeTaskAttachment(
    user: CurrentUser,
    taskId: string,
    attachmentId: string,
  ): Promise<void> {
    const task = await this.findAccountTask(taskId);
    if (!task) {
      throw new NotFoundException("Task not found!");
    }
    const previousTask = cloneDeep(task);
    const existingAttachmentIds = task.attachments?.map((a) => a.uid);

    const attachment = task.attachments?.find((a) => a.uid === attachmentId);
    if (!attachment) {
      throw new NotFoundException("Attachment not found!");
    }

    await this.transactionService.runInTransaction(async (txnContext) => {
      task.attachments = task.attachments?.filter(
        (a) => a.uid !== attachmentId,
      );

      // Save in db
      await this.accountTaskRepository.saveWithTxn(txnContext, task);

      // Record audit log
      await this.activitiesService.recordAuditLog(
        {
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityId: task.id,
          entityUid: task.uid,
          entityType: AuditLogEntityType.ACCOUNT_TASK,
          op: AuditLogOp.UPDATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: `Attachment ${attachmentId} was removed from account task ${task.id}!`,
          description: `Attachment ${attachmentId} was removed from account task ${task.id} by ${user.email}!`,
          metadata: {
            updatedFields: [
              {
                field: "attachments",
                previousValue: existingAttachmentIds?.join(","),
                updatedToValue: task.attachments?.map((a) => a.uid).join(","),
              },
            ],
          },
        },
        txnContext,
      );

      // Publish account task deleted event to SNS
      const accountTaskUpdatedEvent =
        this.accountsSNSEventsFactory.createAccountTaskUpdatedSNSEvent(
          user,
          previousTask,
          task,
        );

      this.accountCommonService.publishEventToSNSQueue(
        AccountEvents.ACCOUNT_TASK_UPDATED,
        accountTaskUpdatedEvent,
        user,
      );

      // Invalidate cache
      await this.cachedAccountTaskRepository.invalidateAccountTaskCache({
        uids: [task.uid],
      });
    });
  }
}
