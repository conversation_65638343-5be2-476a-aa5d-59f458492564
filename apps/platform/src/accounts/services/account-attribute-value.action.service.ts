import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";
import {
  AccountAttributeType,
  AccountAttributeValue,
  AccountAttributeValueRepository,
  AuditLogEntityType,
  AuditLogOp,
  AuditLogVisibility,
  CachedAccountAttributeValueRepository,
  TransactionService,
  UserType,
} from "@repo/thena-platform-entities";
import { cloneDeep, isArray, mergeWith } from "lodash";
import { ActivitiesService } from "../../activities/services/activities.service";
import { CurrentUser } from "../../common/decorators/user.decorator";
import {
  CreateAccountAttributeValueDto,
  UpdateAccountAttributeValueDto,
} from "../dtos/account-attribute-value.dto";
import { AccountsEventsFactory } from "../events/accounts-events.factory";
import { EmittableAccountEvents } from "../events/accounts.events";
import { AccountActivityActionService } from "./account-activity.action.service";
import { AccountCommonService } from "./account-commons.service";
import { AccountNoteActionService } from "./account-note.action.service";
import { AccountTaskActionService } from "./account-task.action.service";
import { AccountsService } from "./accounts.service";
import { CustomerContactActionService } from "./customer-contact.action.service";

@Injectable()
export class AccountAttributeValueActionService {
  constructor(
    // Injected repositories
    private accountAttributeValueRepository: AccountAttributeValueRepository,
    private cachedAccountAttributeValueRepository: CachedAccountAttributeValueRepository,

    // Accounts services
    private readonly accountCommonService: AccountCommonService,
    private readonly accountsService: AccountsService,
    private readonly customerContactActionService: CustomerContactActionService,
    private readonly accountActivityActionService: AccountActivityActionService,
    private readonly accountNoteActionService: AccountNoteActionService,
    private readonly accountTaskActionService: AccountTaskActionService,

    // Transaction service
    private readonly transactionService: TransactionService,

    // Activities service
    private readonly activitiesService: ActivitiesService,

    // Event emitter
    private readonly accountsEventsFactory: AccountsEventsFactory,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * Checks if an attribute value is in use.
   *
   * @param attributeValue The attribute value to check.
   * @returns Whether the attribute value is in use.
   */
  private async isAttributeInUse(
    attributeValue: AccountAttributeValue,
  ): Promise<boolean> {
    switch (attributeValue.attribute) {
      case AccountAttributeType.ACCOUNT_STATUS:
      case AccountAttributeType.ACCOUNT_CLASSIFICATION:
      case AccountAttributeType.ACCOUNT_HEALTH:
      case AccountAttributeType.ACCOUNT_INDUSTRY:
        return await this.accountsService.isAccountAttributeValueInUse(
          attributeValue.id,
          attributeValue.attribute,
        );
      case AccountAttributeType.CONTACT_TYPE:
        return await this.customerContactActionService.isContactTypeAttributeInUse(
          attributeValue.id,
        );
      case AccountAttributeType.ACTIVITY_STATUS:
        return await this.accountActivityActionService.isActivityStatusAttributeInUse(
          attributeValue.id,
        );
      case AccountAttributeType.ACTIVITY_TYPE:
        return await this.accountActivityActionService.isActivityTypeAttributeInUse(
          attributeValue.id,
        );
      case AccountAttributeType.NOTE_TYPE:
        return await this.accountNoteActionService.isNoteTypeAttributeInUse(
          attributeValue.id,
        );
      case AccountAttributeType.TASK_TYPE:
      case AccountAttributeType.TASK_STATUS:
      case AccountAttributeType.TASK_PRIORITY:
        return await this.accountTaskActionService.isTaskAttributeValueInUse(
          attributeValue.id,
          attributeValue.attribute,
        );
      default:
        return false;
    }
  }

  /**
   * Creates a new account attribute value.
   *
   * If the attribute value is set as default,
   * - we update the previous default attribute value to not be default.
   * - we invalidate the cache for the previous default attribute value.
   *
   * @param organizationId The ID of the organization.
   * @param createAccountAttributeValueDto The DTO containing the attribute value details.
   * @returns The created account attribute value.
   */
  async createAccountAttributeValue(
    user: CurrentUser,
    createAccountAttributeValueDto: CreateAccountAttributeValueDto,
  ): Promise<AccountAttributeValue> {
    const organizationId = user.orgId;

    const attributeValue = this.accountAttributeValueRepository.create({
      organizationId,
      attribute: createAccountAttributeValueDto.attribute,
      value: createAccountAttributeValueDto.value,
      isDefault: createAccountAttributeValueDto.isDefault,
      configuration: {
        ...(createAccountAttributeValueDto.icon && {
          icon: createAccountAttributeValueDto.icon,
        }),
        ...(createAccountAttributeValueDto.color && {
          color: createAccountAttributeValueDto.color,
        }),
        ...(createAccountAttributeValueDto.attribute ===
          AccountAttributeType.TASK_STATUS && {
          isClosed: createAccountAttributeValueDto.isClosed ?? false,
        }),
      },
      ...(createAccountAttributeValueDto.metadata && {
        metadata: createAccountAttributeValueDto.metadata,
      }),
    });

    await this.transactionService.runInTransaction(async (txnContext) => {
      if (createAccountAttributeValueDto.isDefault) {
        // Find default attribute value and set it to not default
        const prevDefaultAttributeValue =
          await this.accountCommonService.findDefaultAttributeValue(
            organizationId,
            createAccountAttributeValueDto.attribute,
          );

        if (prevDefaultAttributeValue) {
          prevDefaultAttributeValue.isDefault = false;
          await this.accountAttributeValueRepository.saveWithTxn(
            txnContext,
            prevDefaultAttributeValue,
          );

          // Invalidate cache for the all attribute values and old default attribute value
          await this.cachedAccountAttributeValueRepository.invalidateAccountAttributeValueCache(
            {
              organizationId,
              attribute: createAccountAttributeValueDto.attribute,
              isDefault: true,
              attributeValueUID: prevDefaultAttributeValue.uid,
            },
          );
        }
      }

      // Invalidate cache for the all attribute values
      await this.cachedAccountAttributeValueRepository.invalidateAccountAttributeValueCache(
        {
          organizationId,
          attribute: createAccountAttributeValueDto.attribute,
        },
      );

      // Save the attribute value
      await this.accountAttributeValueRepository.saveWithTxn(
        txnContext,
        attributeValue,
      );

      // Record audit log
      await this.activitiesService.recordAuditLog(
        {
          organization: { id: organizationId },
          activityPerformedBy:
            user.userType === UserType.BOT_USER ? null : { id: user.sub },
          isAutomated: user.userType === UserType.BOT_USER,
          entityId: attributeValue.id,
          entityUid: attributeValue.uid,
          entityType: AuditLogEntityType.ACCOUNT_ATTRIBUTE_VALUE,
          op: AuditLogOp.CREATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: `Account attribute value ${attributeValue.id} was created!`,
          description: `Account attribute value ${attributeValue.id} was created by ${user.email}!`,
        },
        txnContext,
      );
    });

    return await this.cachedAccountAttributeValueRepository.findByCondition({
      where: {
        uid: attributeValue.uid,
        organizationId,
        isActive: true,
      },
    });
  }

  /**
   * Finds all active account attribute values for an organization.
   *
   * @param organizationId The ID of the organization.
   * @returns The active account attribute values.
   */
  findAccountAttributeValues(
    user: { orgId: string },
    attribute: AccountAttributeType,
  ): Promise<AccountAttributeValue[]> {
    const organizationId = user.orgId;

    return this.cachedAccountAttributeValueRepository.findAll({
      where: { organizationId, isActive: true, attribute },
    });
  }

  /**
   * Finds an account attribute value by its UID.
   *
   * @param organizationId The ID of the organization.
   * @param attributeValueUID The UID of the account attribute value.
   * @returns The account attribute value.
   */
  findAccountAttributeValueByUID(
    organizationId: string,
    attributeValueUID: string,
  ): Promise<AccountAttributeValue> {
    return this.cachedAccountAttributeValueRepository.findByCondition({
      where: { uid: attributeValueUID, organizationId, isActive: true },
    });
  }

  /**
   * Updates an account attribute value.
   *
   * If the attribute value is set as default,
   * - we update the previous default attribute value to not be default.
   * - we invalidate the cache for the previous default attribute value.
   *
   * @param organizationId The ID of the organization.
   * @param accountAttributeValueUID The UID of the account attribute value.
   * @param updateAccountAttributeValueDto The DTO containing the updated attribute value details.
   * @returns The updated account attribute value.
   */
  async updateAccountAttributeValue(
    user: CurrentUser,
    accountAttributeValueUID: string,
    updateAccountAttributeValueDto: UpdateAccountAttributeValueDto,
  ): Promise<AccountAttributeValue> {
    const organizationId = user.orgId;

    const attributeValue =
      await this.cachedAccountAttributeValueRepository.findByCondition({
        where: {
          uid: accountAttributeValueUID,
          isActive: true,
          organizationId,
        },
      });

    if (!attributeValue) {
      throw new NotFoundException("Account attribute value not found");
    }

    const existingAttributeValue = cloneDeep(attributeValue);

    attributeValue.value =
      updateAccountAttributeValueDto.value ?? attributeValue.value;

    if (updateAccountAttributeValueDto.metadata) {
      attributeValue.metadata = mergeWith(
        attributeValue.metadata || {},
        updateAccountAttributeValueDto.metadata,
        (a, b) => {
          if (isArray(a)) {
            return a.concat(b);
          }
        },
      );
    }

    // Add attribute value configuration
    attributeValue.configuration = {
      ...attributeValue.configuration,
      ...(updateAccountAttributeValueDto.icon && {
        icon: updateAccountAttributeValueDto.icon,
      }),
      ...(updateAccountAttributeValueDto.color && {
        color: updateAccountAttributeValueDto.color,
      }),
      ...(attributeValue.attribute === AccountAttributeType.TASK_STATUS &&
        updateAccountAttributeValueDto.isClosed !== undefined && {
          isClosed: updateAccountAttributeValueDto.isClosed,
        }),
    };

    await this.transactionService.runInTransaction(async (txnContext) => {
      if (updateAccountAttributeValueDto.isDefault) {
        // Find default attribute value and set it to not default
        const prevDefaultAttributeValue =
          await this.accountCommonService.findDefaultAttributeValue(
            organizationId,
            attributeValue.attribute,
          );

        if (prevDefaultAttributeValue) {
          prevDefaultAttributeValue.isDefault = false;
          await this.accountAttributeValueRepository.saveWithTxn(
            txnContext,
            prevDefaultAttributeValue,
          );

          attributeValue.isDefault = true;

          // Invalidate cache for the all attribute values and old default attribute value
          await this.cachedAccountAttributeValueRepository.invalidateAccountAttributeValueCache(
            {
              organizationId,
              attribute: attributeValue.attribute,
              attributeValueUID: prevDefaultAttributeValue.uid,
            },
          );
        }
      } else if (
        updateAccountAttributeValueDto.isDefault === false &&
        attributeValue.isDefault
      ) {
        throw new BadRequestException(
          "Cannot set attribute value as not default. Assign another default value first.",
        );
      }

      // Invalidate cache for the all attribute values and current attribute value
      await this.cachedAccountAttributeValueRepository.invalidateAccountAttributeValueCache(
        {
          organizationId,
          attribute: attributeValue.attribute,
          isDefault: true,
          attributeValueUID: attributeValue.uid,
        },
      );

      // Record audit log
      await this.activitiesService.recordAuditLog(
        {
          organization: { id: organizationId },
          activityPerformedBy: { id: user.sub },
          entityId: attributeValue.id,
          entityUid: attributeValue.uid,
          entityType: AuditLogEntityType.ACCOUNT_ATTRIBUTE_VALUE,
          op: AuditLogOp.UPDATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: `Account attribute value ${attributeValue.id} was updated!`,
          description: `Account attribute value ${attributeValue.id} was updated by ${user.email}!`,
          metadata: {
            updatedFields: [
              existingAttributeValue.value !== attributeValue.value && {
                field: "value",
                previousValue: JSON.stringify(existingAttributeValue.value),
                updatedToValue: JSON.stringify(attributeValue.value),
              },
              existingAttributeValue.isDefault !== attributeValue.isDefault && {
                field: "isDefault",
                previousValue: existingAttributeValue.isDefault.toString(),
                updatedToValue: attributeValue.isDefault.toString(),
              },
            ].filter(Boolean),
          },
        },
        txnContext,
      );

      // Save attribute value
      await this.accountAttributeValueRepository.saveWithTxn(
        txnContext,
        attributeValue,
      );
    });

    return await this.cachedAccountAttributeValueRepository.findByCondition({
      where: {
        uid: attributeValue.uid,
        organizationId,
        isActive: true,
      },
    });
  }

  /**
   * Deletes an account attribute value.
   *
   * @param organizationId The ID of the organization.
   * @param attributeValueUID The UID of the account attribute value.
   * @returns The deleted account attribute value.
   */
  async deleteAccountAttributeValue(
    user: CurrentUser,
    attributeValueUID: string,
    forceDelete: boolean = false,
    metadata?: Record<string, any>,
  ): Promise<AccountAttributeValue> {
    const organizationId = user.orgId;

    const attributeValue =
      await this.cachedAccountAttributeValueRepository.findByCondition({
        where: {
          uid: attributeValueUID,
          isActive: true,
          organizationId,
        },
      });

    if (!attributeValue) {
      throw new NotFoundException("Account attribute value not found");
    }

    if (attributeValue.isDefault) {
      throw new BadRequestException(
        "Cannot delete default attribute value. Assign another default value first.",
      );
    }

    if (!forceDelete && (await this.isAttributeInUse(attributeValue))) {
      throw new BadRequestException(
        "Cannot delete attribute value. It is in use.",
      );
    }

    attributeValue.isActive = false;
    attributeValue.deletedAt = new Date();
    if (metadata) {
      attributeValue.metadata = mergeWith(
        attributeValue.metadata || {},
        metadata,
        (a, b) => {
          if (isArray(a)) {
            return a.concat(b);
          }
        },
      );
    }

    return this.transactionService.runInTransaction(async (txnContext) => {
      // Save attribute value
      const savedAttributeValue =
        await this.accountAttributeValueRepository.saveWithTxn(
          txnContext,
          attributeValue,
        );

      // Record audit log
      await this.activitiesService.recordAuditLog(
        {
          organization: { id: organizationId },
          activityPerformedBy: { id: user.sub },
          entityId: attributeValue.id,
          entityUid: attributeValue.uid,
          entityType: AuditLogEntityType.ACCOUNT_ATTRIBUTE_VALUE,
          op: AuditLogOp.DELETED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: `Account attribute value ${attributeValue.id} was deleted!`,
          description: `Account attribute value ${attributeValue.id} was deleted by ${user.email}!`,
          metadata: {
            updatedFields: [
              {
                field: "isActive",
                previousValue: "true",
                updatedToValue: "false",
              },
            ],
          },
        },
        txnContext,
      );

      // Invalidate cache for the all attribute values and the deleted attribute value
      await this.cachedAccountAttributeValueRepository.invalidateAccountAttributeValueCache(
        {
          organizationId,
          attribute: attributeValue.attribute,
          attributeValueUID: attributeValue.uid,
        },
      );

      if (forceDelete) {
        // Emit deleted event
        const accountAttributeValueDeletedEvent =
          this.accountsEventsFactory.createAccountAttributeValueForceDeletedEvent(
            savedAttributeValue.organizationId,
            savedAttributeValue.attribute,
            savedAttributeValue.id,
          );

        this.eventEmitter.emit(
          EmittableAccountEvents.ACCOUNT_ATTRIBUTE_VALUE_DELETED,
          accountAttributeValueDeletedEvent,
        );
      }

      return savedAttributeValue;
    });
  }
}
