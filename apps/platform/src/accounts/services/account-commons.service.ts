import { InjectQueue } from "@nestjs/bullmq";
import { Inject, Injectable, NotFoundException } from "@nestjs/common";
import { SentryService } from "@repo/nestjs-commons/filters";
import { ILogger } from "@repo/nestjs-commons/logger";
import {
  Account,
  AccountActivity,
  AccountAttributeType,
  AccountAttributeValue,
  CachedAccountActivityRepository,
  CachedAccountAttributeValueRepository,
  CachedAccountRepository,
} from "@repo/thena-platform-entities";
import { Queue } from "bullmq";
import * as rTracer from "cls-rtracer";
import { CurrentUser } from "../../common/decorators";
import { ConfigService } from "../../config/config.service";
import { QueueNames } from "../../constants/queue.constants";
import { SNSEvent } from "../interfaces/sns-events.interface";

/**
 * This service is created to avoid circular dependencies.
 * This service should not be injecting any other account services. It can directly inject repositories.
 * Every account service should be able to inject this service.
 * This service should not be used for any business logic. It should only be used for validation and getting entities.
 * And to be used if and only if there is a risk of circular dependency.
 *
 * @export
 * @class AccountCommonService
 */
@Injectable()
export class AccountCommonService {
  constructor(
    @Inject("Sentry") private readonly sentryService: SentryService,
    @Inject("CustomLogger") private readonly logger: ILogger,

    // Injected repositories
    private readonly cachedAccountRepository: CachedAccountRepository,

    private readonly cachedAccountAttributeValueRepository: CachedAccountAttributeValueRepository,

    private readonly cachedAccountActivityRepository: CachedAccountActivityRepository,

    // Config service
    private readonly configService: ConfigService,

    // SNS Publisher Queue
    @InjectQueue(QueueNames.ACCOUNTS_SNS_PUBLISHER)
    private readonly accountsSNSPublisherQueue: Queue,
  ) {}

  /**
   * Validates and gets an account.
   *
   * @param accountUID The UID of the account
   * @param orgId The ID of the organization
   * @returns The account
   * @throws NotFoundException if the account is not found
   */
  async validateAndGetAccount(
    accountUID: string,
    orgId: string,
  ): Promise<Account> {
    const account = await this.cachedAccountRepository.findByCondition({
      where: { uid: accountUID, organizationId: orgId, isActive: true },
      relations: [
        "accountOwner",
        "statusAttribute",
        "classificationAttribute",
        "healthAttribute",
      ],
    });
    if (!account) {
      throw new NotFoundException("Account not found");
    }
    return account;
  }

  /**
   * Gets an account attribute value.
   *
   * @param attributeValue The UID / value of the attribute value
   * @param organizationId The ID of the organization
   * @returns The account attribute value
   */
  getAttributeValue(
    attributeValue: string,
    attribute: AccountAttributeType,
    organizationId: string,
  ): Promise<AccountAttributeValue> {
    return this.cachedAccountAttributeValueRepository.findByCondition({
      where: [
        {
          uid: attributeValue,
          attribute,
          organizationId,
          isActive: true,
        },
        {
          value: attributeValue,
          attribute,
          organizationId,
          isActive: true,
        },
      ],
    });
  }

  /**
   * Finds the default attribute value for an organization and attribute.
   *
   * @param organizationId The ID of the organization.
   * @param attribute The attribute type.
   * @returns The default attribute value.
   */
  async findDefaultAttributeValue(
    organizationId: string,
    attribute: AccountAttributeType,
  ): Promise<AccountAttributeValue> {
    const defaultAttributeValue =
      await this.cachedAccountAttributeValueRepository.findByCondition({
        where: { organizationId, isDefault: true, attribute },
      });

    return defaultAttributeValue;
  }

  /**
   * Validates and gets an account activity.
   *
   * @param activityUID The UID of the activity
   * @param organizationId The ID of the organization
   * @returns The account activity
   * @throws NotFoundException if the activity is not found
   */
  async validateAndGetActivity(
    activityUID: string,
    organizationId: string,
  ): Promise<AccountActivity> {
    const activity = await this.cachedAccountActivityRepository.findByCondition(
      {
        where: { uid: activityUID, isActive: true },
        relations: ["account"],
      },
    );

    if (!activity || activity.account.organizationId !== organizationId) {
      throw new NotFoundException("Activity not found");
    }
    return activity;
  }

  /**
   * Publishes an account event to SNS.
   * @param event The event to publish.
   * @param eventData The data to publish.
   * @param user The user associated with the event.
   */
  async publishEventToSNSQueue<Q, T extends SNSEvent<Q>>(
    event: T["eventType"],
    eventData: T,
    user: CurrentUser,
  ) {
    try {
      await this.accountsSNSPublisherQueue.add(
        QueueNames.ACCOUNTS_SNS_PUBLISHER,
        {
          event,
          eventData,
          user,
          reqId: rTracer.id(),
        },
        {
          attempts: 3,
          backoff: {
            type: "exponential",
            delay: 1000, // 1 second
          },
        },
      );
    } catch (error) {
      this.logger.error(
        `Error encountered while publishing account event - ${event} for organization ${eventData.orgId} to SNS: ${error?.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "ACCOUNTS_EVENTS",
        fn: "publishEventToSNSQueue",
        organizationId: eventData.orgId,
        user,
        event,
        eventData,
      });

      throw error;
    }
  }
}
