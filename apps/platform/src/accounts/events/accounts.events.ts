// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { AccountAttributeType } from "@repo/thena-platform-entities";

/**
 * The local events are emitted to the nest js event emitter by the account service
 * The SNS events are emitted to the SNS topic by the account service
 */
export enum EmittableAccountEvents {
  // Local events
  ACCOUNT_ATTRIBUTE_VALUE_DELETED = "account.attribute.value.deleted",
  LINK_CONTACTS_TO_ACCOUNT_BY_EMAIL_DOMAIN = "account.contacts.link.by.email.domain",
}

export class BaseAccountServiceEvent {
  organizationId: string;
}

// Local events
export class AccountAttributeValueDeletedEvent extends BaseAccountServiceEvent {
  organizationId: string;
  attribute: AccountAttributeType;
  attributeValueId: string;
}

export class LinkContactsToAccountByEmailDomain extends BaseAccountServiceEvent {
  accountId: string;
  domain: string;
}
