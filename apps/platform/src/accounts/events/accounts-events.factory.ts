import { Injectable } from "@nestjs/common";
import { AccountAttributeType } from "@repo/thena-platform-entities";
import * as AccountServiceEvents from "./accounts.events";

@Injectable()
export class AccountsEventsFactory {
  private createBaseAccountEvent<
    T extends AccountServiceEvents.BaseAccountServiceEvent,
  >(
    EventClass: new (...args: unknown[]) => T,
    organizationId: string,
    args: { [key: string]: unknown },
  ): T {
    if (!organizationId) {
      throw new Error("Missing required fields for account event creation!");
    }

    const event = new EventClass();
    event.organizationId = organizationId;

    for (const [key, value] of Object.entries(args)) {
      event[key] = value;
    }

    return event;
  }

  createAccountAttributeValueForceDeletedEvent(
    organizationId: string,
    attribute: AccountAttributeType,
    attributeValueId: string,
  ): AccountServiceEvents.AccountAttributeValueDeletedEvent {
    return this.createBaseAccountEvent(
      AccountServiceEvents.AccountAttributeValueDeletedEvent,
      organizationId,
      { attribute, attributeValueId },
    );
  }

  createTriggerAccountContactsCreationEvent(
    organizationId: string,
    accountId: string,
    domain: string,
  ): AccountServiceEvents.LinkContactsToAccountByEmailDomain {
    return this.createBaseAccountEvent(
      AccountServiceEvents.LinkContactsToAccountByEmailDomain,
      organizationId,
      { accountId, domain },
    );
  }
}
