import { AccountEvents } from "@repo/thena-shared-interfaces";
import {
  Account,
  AccountActivity,
  AccountNote,
  AccountRelationship,
  AccountTask,
  CustomerContact,
} from "@repo/thena-platform-entities";
import { v4 as uuidv4 } from "uuid";
import { CurrentUser } from "../../common/decorators/user.decorator";
import { AccountActivityResponseDto } from "../dtos/response/account-activity.dto";
import { AccountNoteResponseDto } from "../dtos/response/account-note.dto";
import { AccountRelationshipResponseDto } from "../dtos/response/account-relationship.dto";
import { AccountTaskResponseDto } from "../dtos/response/account-task.dto";
import { AccountResponseDto } from "../dtos/response/account.dto";
import { CustomerContactResponseDto } from "../dtos/response/customer-contact.dto";
import {
  AccountActivityPayload,
  AccountNotePayload,
  AccountPayload,
  AccountRelationshipPayload,
  AccountTaskPayload,
  CustomerContactPayload,
  SNSEvent,
} from "../interfaces/sns-events.interface";

export class AccountsSNSEventsFactory {
  private baseAccountsSNSEvent<T>(
    user: CurrentUser,
    payload: T,
    eventType: AccountEvents,
  ): SNSEvent<T> {
    return {
      eventId: uuidv4(),
      eventType,
      timestamp: new Date().toISOString(),
      orgId: user.orgUid,
      actor: {
        id: user.uid,
        email: user.email,
        type: user.userType,
      },
      payload,
    };
  }

  createAccountCreatedSNSEvent(
    user: CurrentUser,
    account: Account,
  ): SNSEvent<AccountPayload> {
    const accountPayload = {
      account: AccountResponseDto.fromEntity(account),
    } as AccountPayload;

    return this.baseAccountsSNSEvent(
      user,
      accountPayload,
      AccountEvents.ACCOUNT_CREATED,
    );
  }

  createAccountUpdatedSNSEvent(
    user: CurrentUser,
    previousAccount: Account,
    account: Account,
  ): SNSEvent<AccountPayload> {
    const accountPayload = {
      account: AccountResponseDto.fromEntity(account),
      previousAccount: AccountResponseDto.fromEntity(previousAccount),
    } as AccountPayload;

    return this.baseAccountsSNSEvent(
      user,
      accountPayload,
      AccountEvents.ACCOUNT_UPDATED,
    );
  }

  createAccountDeletedSNSEvent(
    user: CurrentUser,
    account: Account,
  ): SNSEvent<AccountPayload> {
    const accountPayload = {
      previousAccount: AccountResponseDto.fromEntity(account),
    } as AccountPayload;

    return this.baseAccountsSNSEvent(
      user,
      accountPayload,
      AccountEvents.ACCOUNT_DELETED,
    );
  }

  createAccountRelationshipCreatedSNSEvent(
    user: CurrentUser,
    relationship: AccountRelationship,
  ): SNSEvent<AccountRelationshipPayload> {
    const relationshipPayload = {
      relationship: AccountRelationshipResponseDto.fromEntity(relationship),
    } as AccountRelationshipPayload;

    return this.baseAccountsSNSEvent(
      user,
      relationshipPayload,
      AccountEvents.ACCOUNT_RELATIONSHIP_CREATED,
    );
  }

  createAccountRelationshipUpdatedSNSEvent(
    user: CurrentUser,
    previousRelationship: AccountRelationship,
    relationship: AccountRelationship,
  ): SNSEvent<AccountRelationshipPayload> {
    const relationshipPayload = {
      relationship: AccountRelationshipResponseDto.fromEntity(relationship),
      previousRelationship:
        AccountRelationshipResponseDto.fromEntity(previousRelationship),
    } as AccountRelationshipPayload;

    return this.baseAccountsSNSEvent(
      user,
      relationshipPayload,
      AccountEvents.ACCOUNT_RELATIONSHIP_UPDATED,
    );
  }

  createAccountRelationshipDeletedSNSEvent(
    user: CurrentUser,
    relationship: AccountRelationship,
  ): SNSEvent<AccountRelationshipPayload> {
    const relationshipPayload = {
      previousRelationship:
        AccountRelationshipResponseDto.fromEntity(relationship),
    } as AccountRelationshipPayload;

    return this.baseAccountsSNSEvent(
      user,
      relationshipPayload,
      AccountEvents.ACCOUNT_RELATIONSHIP_DELETED,
    );
  }

  createAccountActivityCreatedSNSEvent(
    user: CurrentUser,
    activity: AccountActivity,
  ): SNSEvent<AccountActivityPayload> {
    const activityPayload = {
      activity: AccountActivityResponseDto.fromEntity(activity),
    } as AccountActivityPayload;

    return this.baseAccountsSNSEvent(
      user,
      activityPayload,
      AccountEvents.ACCOUNT_ACTIVITY_CREATED,
    );
  }

  createAccountActivityUpdatedSNSEvent(
    user: CurrentUser,
    previousActivity: AccountActivity,
    activity: AccountActivity,
  ): SNSEvent<AccountActivityPayload> {
    const activityPayload = {
      activity: AccountActivityResponseDto.fromEntity(activity),
      previousActivity: AccountActivityResponseDto.fromEntity(previousActivity),
    } as AccountActivityPayload;

    return this.baseAccountsSNSEvent(
      user,
      activityPayload,
      AccountEvents.ACCOUNT_ACTIVITY_UPDATED,
    );
  }

  createAccountActivityDeletedSNSEvent(
    user: CurrentUser,
    activity: AccountActivity,
  ): SNSEvent<AccountActivityPayload> {
    const activityPayload = {
      previousActivity: AccountActivityResponseDto.fromEntity(activity),
    } as AccountActivityPayload;

    return this.baseAccountsSNSEvent(
      user,
      activityPayload,
      AccountEvents.ACCOUNT_ACTIVITY_DELETED,
    );
  }

  createAccountNoteCreatedSNSEvent(
    user: CurrentUser,
    note: AccountNote,
  ): SNSEvent<AccountNotePayload> {
    const notePayload = {
      note: AccountNoteResponseDto.fromEntity(note),
    } as AccountNotePayload;

    return this.baseAccountsSNSEvent(
      user,
      notePayload,
      AccountEvents.ACCOUNT_NOTE_CREATED,
    );
  }

  createAccountNoteUpdatedSNSEvent(
    user: CurrentUser,
    previousNote: AccountNote,
    note: AccountNote,
  ): SNSEvent<AccountNotePayload> {
    const notePayload = {
      note: AccountNoteResponseDto.fromEntity(note),
      previousNote: AccountNoteResponseDto.fromEntity(previousNote),
    } as AccountNotePayload;

    return this.baseAccountsSNSEvent(
      user,
      notePayload,
      AccountEvents.ACCOUNT_NOTE_UPDATED,
    );
  }

  createAccountNoteDeletedSNSEvent(
    user: CurrentUser,
    note: AccountNote,
  ): SNSEvent<AccountNotePayload> {
    const notePayload = {
      previousNote: AccountNoteResponseDto.fromEntity(note),
    } as AccountNotePayload;

    return this.baseAccountsSNSEvent(
      user,
      notePayload,
      AccountEvents.ACCOUNT_NOTE_DELETED,
    );
  }

  createAccountTaskCreatedSNSEvent(
    user: CurrentUser,
    task: AccountTask,
  ): SNSEvent<AccountTaskPayload> {
    const taskPayload = {
      task: AccountTaskResponseDto.fromEntity(task),
    } as AccountTaskPayload;

    return this.baseAccountsSNSEvent(
      user,
      taskPayload,
      AccountEvents.ACCOUNT_TASK_CREATED,
    );
  }

  createAccountTaskUpdatedSNSEvent(
    user: CurrentUser,
    previousTask: AccountTask,
    task: AccountTask,
  ): SNSEvent<AccountTaskPayload> {
    const taskPayload = {
      task: AccountTaskResponseDto.fromEntity(task),
      previousTask: AccountTaskResponseDto.fromEntity(previousTask),
    } as AccountTaskPayload;

    return this.baseAccountsSNSEvent(
      user,
      taskPayload,
      AccountEvents.ACCOUNT_TASK_UPDATED,
    );
  }

  createAccountTaskDeletedSNSEvent(
    user: CurrentUser,
    task: AccountTask,
  ): SNSEvent<AccountTaskPayload> {
    const taskPayload = {
      previousTask: AccountTaskResponseDto.fromEntity(task),
    } as AccountTaskPayload;

    return this.baseAccountsSNSEvent(
      user,
      taskPayload,
      AccountEvents.ACCOUNT_TASK_DELETED,
    );
  }

  createCustomerContactCreatedSNSEvent(
    user: CurrentUser,
    customerContact: CustomerContact,
  ): SNSEvent<CustomerContactPayload> {
    const customerContactPayload = {
      customerContact: CustomerContactResponseDto.fromEntity(customerContact),
    } as CustomerContactPayload;

    return this.baseAccountsSNSEvent(
      user,
      customerContactPayload,
      AccountEvents.CUSTOMER_CONTACT_CREATED,
    );
  }

  createCustomerContactUpdatedSNSEvent(
    user: CurrentUser,
    previousCustomerContact: CustomerContact,
    customerContact: CustomerContact,
  ): SNSEvent<CustomerContactPayload> {
    const customerContactPayload = {
      customerContact: CustomerContactResponseDto.fromEntity(customerContact),
      previousCustomerContact: CustomerContactResponseDto.fromEntity(
        previousCustomerContact,
      ),
    } as CustomerContactPayload;

    return this.baseAccountsSNSEvent(
      user,
      customerContactPayload,
      AccountEvents.CUSTOMER_CONTACT_UPDATED,
    );
  }

  createCustomerContactDeletedSNSEvent(
    user: CurrentUser,
    customerContact: CustomerContact,
  ): SNSEvent<CustomerContactPayload> {
    const customerContactPayload = {
      previousCustomerContact:
        CustomerContactResponseDto.fromEntity(customerContact),
    } as CustomerContactPayload;

    return this.baseAccountsSNSEvent(
      user,
      customerContactPayload,
      AccountEvents.CUSTOMER_CONTACT_DELETED,
    );
  }
}
