import { faker } from "@faker-js/faker";
import {
  HttpStatus,
  UnprocessableEntityException,
  ValidationPipe,
} from "@nestjs/common";
import {
  FastifyAdapter,
  NestFastifyApplication,
} from "@nestjs/platform-fastify";
import { Test } from "@nestjs/testing";
import {
  AccountAttributeType,
  AuditLogEntityType,
  AuditLogOp,
} from "@repo/thena-platform-entities";
import { sleep, t__loginIntoAuthService } from "@repo/thena-shared-libs";
import { DataSource } from "typeorm";
import { AppModule } from "../../app.module";
import { injectWithOrgId } from "../../utils";
import { AccountAttributeValueResponseDto } from "../dtos/response/account-attribute-value.dto";
import { AccountResponseDto } from "../dtos/response/account.dto";
import {
  CustomerContactBulkResponseDto,
  CustomerContactResponseDto,
} from "../dtos/response/customer-contact.dto";

describe("Customer Contacts", () => {
  let app: NestFastifyApplication;
  let connection: DataSource;
  let userAuthToken: string;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = module.createNestApplication<NestFastifyApplication>(
      new FastifyAdapter(),
    );

    app.useGlobalPipes(
      new ValidationPipe({
        transform: true,
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        exceptionFactory: (errors) => {
          return new UnprocessableEntityException(
            errors.map((error) => ({
              property: error.property,
              constraints: error.constraints,
            })),
          );
        },
      }),
    );

    await app.init();
    await app.getHttpAdapter().getInstance().ready();

    connection = app.get(DataSource);

    // Log the user in to get the auth token
    userAuthToken = await t__loginIntoAuthService(
      global.testUser.email,
      global.testUser.password,
    );
  });

  afterAll(async () => {
    await app.close();
  });

  describe("Create Customer Contact", () => {
    it("should be able to create customer contact when all values are provided", async () => {
      const createContactResponse = await injectWithOrgId(app, {
        method: "POST",
        url: `/v1/accounts/contacts`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          firstName: "Customer 1",
          lastName: "Test",
          email: "<EMAIL>",
          phoneNumber: "+**********",
          contactType: global.testContactType.uid,
          accountIds: [global.testAccount.uid],
        },
      });

      expect(createContactResponse.statusCode).toBe(HttpStatus.CREATED);
      const result = createContactResponse.json()
        .data as CustomerContactResponseDto;

      expect(result).toHaveProperty("id");
      expect(result.firstName).toBe("Customer 1");
      expect(result.lastName).toBe("Test");
      expect(result.email).toBe("<EMAIL>");
      expect(result.phoneNumber).toBe("+**********");
      expect(result.contactTypeId).toBe(global.testContactType.uid);
      expect(result.accounts.length).toBe(1);
      expect(result.accounts[0].id).toBe(global.testAccount.uid);
    });

    it("should be able to create customer contact with default contact type", async () => {
      // create default contact type
      const createDefaultContactTypeResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "DEFAULT CONTACT TYPE",
          attribute: AccountAttributeType.CONTACT_TYPE,
          isDefault: true,
        },
      });

      const defaultContactType = createDefaultContactTypeResponse.json()
        .data as AccountAttributeValueResponseDto;

      const createContactResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/contacts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          firstName: "Customer 2",
          lastName: "Test",
          email: "<EMAIL>",
          phoneNumber: "+**********",
        },
      });

      expect(createContactResponse.statusCode).toBe(HttpStatus.CREATED);
      const result = createContactResponse.json()
        .data as CustomerContactResponseDto;

      expect(result).toHaveProperty("id");
      expect(result.firstName).toBe("Customer 2");
      expect(result.lastName).toBe("Test");
      expect(result.email).toBe("<EMAIL>");
      expect(result.phoneNumber).toBe("+**********");
      expect(result.contactTypeId).toBe(defaultContactType.id);
      expect(result.accounts.length).toBe(0);
    });

    it("should record an activity when contact is created", async () => {
      await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/contacts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          firstName: "Customer 3",
          lastName: "Test",
          email: "<EMAIL>",
          phoneNumber: "+**********",
          contactType: global.testContactType.uid,
          accountIds: [global.testAccount.uid],
        },
      });

      const contactData = await connection.query(
        `SELECT * FROM "public"."customer_contacts" WHERE email = '<EMAIL>' AND is_active = true AND organization_id = '${global.testOrganization.id}';`,
      );

      // Check activities
      const activitiesResponse = await connection.query(
        `SELECT * FROM "public"."audit_logs" WHERE organization_id = '${global.testOrganization.id}' AND entity_type = '${AuditLogEntityType.CUSTOMER_CONTACT}' AND entity_id = '${contactData[0].id}' AND operation = '${AuditLogOp.CREATED}';`,
      );

      expect(activitiesResponse.length).toBe(1);
    });

    it("should throw error when account does not exist", async () => {
      const createContactResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/contacts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          firstName: "Customer 5",
          lastName: "Test",
          email: "<EMAIL>",
          phoneNumber: "+**********",
          contactType: global.testContactType.uid,
          accountIds: ["non-existent-account"],
        },
      });

      expect(createContactResponse.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(createContactResponse.json().message).toBe("Account not found");
    });

    it("should throw error when contact type does not exist", async () => {
      const createContactResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/contacts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          firstName: "Customer 6",
          lastName: "Test",
          email: "<EMAIL>",
          phoneNumber: "+**********",
          contactType: "non-existent-type",
          accountIds: [global.testAccount.uid],
        },
      });

      expect(createContactResponse.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(createContactResponse.json().message).toBe(
        "Contact type not found",
      );
    });

    it("should throw error if the provided contact already exists for the account", async () => {
      const createContactResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/contacts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          firstName: "Customer 7",
          lastName: "Test",
          email: "<EMAIL>",
          phoneNumber: "+**********",
          contactType: global.testContactType.uid,
          accountIds: [global.testAccount.uid],
        },
      });

      expect(createContactResponse.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(createContactResponse.json().message).toBe(
        "Contact with this email already exists",
      );
    });
  });

  describe("Bulk Create Customer Contacts", () => {
    it("should be able to bulk create customer contacts", async () => {
      const createContactResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/contacts/bulk",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          contacts: [
            {
              firstName: "Customer 7",
              lastName: "Test",
              email: "<EMAIL>",
              phoneNumber: "+**********",
            },
            {
              firstName: "Customer 8",
              lastName: "Test",
              email: "<EMAIL>",
              phoneNumber: "+**********",
            },
          ],
          contactType: global.testContactType.uid,
          accountIds: [global.testAccount.uid],
        },
      });

      expect(createContactResponse.statusCode).toBe(HttpStatus.CREATED);

      const contacts = await connection.query(`
				SELECT * FROM "public"."customer_contacts" WHERE email IN ('<EMAIL>', '<EMAIL>') AND is_active = true AND organization_id = '${global.testOrganization.id}';
			`);
      expect(contacts.length).toBe(2);

      const results = createContactResponse.json()
        .data as CustomerContactBulkResponseDto;

      expect(results.total).toBe(2);
      expect(results.created).toBe(2);
      expect(results.skipped).toBe(0);
    });

    it("should skip existing contacts in bulk create", async () => {
      const createContactResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/contacts/bulk",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          contacts: [
            {
              firstName: "Customer 9",
              lastName: "Test",
              email: "<EMAIL>",
              phoneNumber: "+**********",
            },
            {
              firstName: "Customer 8",
              lastName: "Test",
              email: "<EMAIL>",
              phoneNumber: "+**********",
            },
          ],
          contactType: global.testContactType.uid,
          accountIds: [global.testAccount.uid],
        },
      });

      expect(createContactResponse.statusCode).toBe(HttpStatus.CREATED);

      const contacts = await connection.query(`
				SELECT * FROM "public"."customer_contacts" WHERE email IN ('<EMAIL>', '<EMAIL>') AND is_active = true AND organization_id = '${global.testOrganization.id}';
			`);
      expect(contacts.length).toBe(2);

      const results = createContactResponse.json()
        .data as CustomerContactBulkResponseDto;

      expect(results.total).toBe(2);
      expect(results.created).toBe(1);
      expect(results.skipped).toBe(1);
    });

    it("should throw error when account is not valid", async () => {
      const createContactResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/contacts/bulk",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          contacts: [
            {
              firstName: "Customer 10",
              lastName: "Test",
              email: "<EMAIL>",
              phoneNumber: "+**********",
            },
            {
              firstName: "Customer 11",
              lastName: "Test",
              email: "<EMAIL>",
              phoneNumber: "+**********",
            },
          ],
          accountIds: ["non-existent-account"],
          contactType: global.testContactType.uid,
        },
      });

      expect(createContactResponse.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(createContactResponse.json().message).toBe("Account not found");
    });

    it("should throw error when contact type is not valid", async () => {
      const createContactResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/contacts/bulk",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          contacts: [
            {
              firstName: "Customer 12",
              lastName: "Test",
              email: "<EMAIL>",
              phoneNumber: "+**********",
            },
          ],
          accountIds: [global.testAccount.uid],
          contactType: "non-existent-type",
        },
      });

      expect(createContactResponse.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(createContactResponse.json().message).toBe(
        "Contact type not found",
      );
    });
  });

  describe("Update Customer Contact", () => {
    it("should be able to update contact type", async () => {
      // create a new account contact
      const createContactResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/contacts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          firstName: "Customer 13",
          lastName: "Test",
          email: "<EMAIL>",
          phoneNumber: "+**********",
          accountIds: [global.testAccount.uid],
        },
      });

      expect(createContactResponse.statusCode).toBe(HttpStatus.CREATED);
      const contact = createContactResponse.json()
        .data as CustomerContactResponseDto;

      // Create new account
      const createAccountResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          name: "Test account for customer contact",
          primaryDomain: faker.internet.domainName(),
          accountOwnerId: global.testUser.uid,
          source: "manual",
        },
      });

      expect(createAccountResponse.statusCode).toBe(HttpStatus.CREATED);
      const newAccount = createAccountResponse.json()
        .data as AccountResponseDto;

      // Create a new contact type first
      const createTypeResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "NEW_TYPE",
          attribute: AccountAttributeType.CONTACT_TYPE,
          isDefault: false,
        },
      });

      const newContactType = createTypeResponse.json().data;

      const updateResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/contacts/${contact.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          firstName: "Customer 13 edited",
          lastName: "Test edited",
          email: "<EMAIL>",
          phoneNumber: "+**********",
          contactType: newContactType.id,
          accountIds: [newAccount.id],
        },
      });

      expect(updateResponse.statusCode).toBe(HttpStatus.OK);
      const result = updateResponse.json().data as CustomerContactResponseDto;

      expect(result.firstName).toBe("Customer 13 edited");
      expect(result.lastName).toBe("Test edited");
      expect(result.email).toBe("<EMAIL>");
      expect(result.phoneNumber).toBe("+**********");
      expect(result.accounts.length).toBe(1);
      expect(result.accounts[0].id).toBe(newAccount.id);
      expect(result.contactTypeId).toBe(newContactType.id);
    });

    it("should record an activity when contact is updated", async () => {
      // create a new account contact
      const createContactResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/contacts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          firstName: "Customer 14",
          lastName: "Test",
          email: "<EMAIL>",
          phoneNumber: "+**********",
          accountIds: [global.testAccount.uid],
        },
      });

      expect(createContactResponse.statusCode).toBe(HttpStatus.CREATED);
      const contact = createContactResponse.json()
        .data as CustomerContactResponseDto;

      // Create new account
      const createAccountResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          name: "Test account for customer contact",
          primaryDomain: faker.internet.domainName(),
          accountOwnerId: global.testUser.uid,
          source: "manual",
        },
      });

      expect(createAccountResponse.statusCode).toBe(HttpStatus.CREATED);
      const newAccount = createAccountResponse.json()
        .data as AccountResponseDto;

      // Create a new contact type first
      const createTypeResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "NEW_TYPE",
          attribute: AccountAttributeType.CONTACT_TYPE,
          isDefault: false,
        },
      });

      const newContactType = createTypeResponse.json().data;

      const updateResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/contacts/${contact.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          firstName: "Customer 14 edited",
          lastName: "Test edited",
          email: "<EMAIL>",
          phoneNumber: "+**********",
          contactType: newContactType.id,
          accountIds: [newAccount.id],
        },
      });

      expect(updateResponse.statusCode).toBe(HttpStatus.OK);
      const customerContact = updateResponse.json()
        .data as CustomerContactResponseDto;

      // Check activities
      const activitiesResponse = await connection.query(
        `SELECT * FROM "public"."audit_logs" WHERE organization_id = '${global.testOrganization.id}' AND entity_type = '${AuditLogEntityType.CUSTOMER_CONTACT}' AND entity_uid = '${customerContact.id}' AND operation = '${AuditLogOp.UPDATED}' ORDER BY id DESC;`,
      );

      expect(activitiesResponse.length).toBeGreaterThanOrEqual(1);
      expect(activitiesResponse[0].metadata.updatedFields.length).toBe(6);
    });

    it("should throw error if contact does not exist", async () => {
      const updateResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/contacts/non-existent-contact`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          firstName: "Customer 15 edited",
        },
      });

      expect(updateResponse.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(updateResponse.json().message).toBe("Contact not found");
    });

    it("should throw error if account does not exist", async () => {
      // create a new account contact
      const createContactResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/contacts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          firstName: "Customer 15",
          lastName: "Test",
          email: "<EMAIL>",
          phoneNumber: "+**********",
          accountIds: [global.testAccount.uid],
        },
      });

      expect(createContactResponse.statusCode).toBe(HttpStatus.CREATED);
      const contact = createContactResponse.json()
        .data as CustomerContactResponseDto;

      const updateResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/contacts/${contact.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountIds: ["non-existent-account"],
        },
      });

      expect(updateResponse.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(updateResponse.json().message).toBe("Account not found");
    });

    it("should throw error when contact type is not valid", async () => {
      // create a new account contact
      const createContactResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/contacts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          firstName: "Customer 16",
          lastName: "Test",
          email: "<EMAIL>",
          phoneNumber: "+**********",
          accountIds: [global.testAccount.uid],
        },
      });

      expect(createContactResponse.statusCode).toBe(HttpStatus.CREATED);
      const contact = createContactResponse.json()
        .data as CustomerContactResponseDto;

      const updateResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/contacts/${contact.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          contactType: "non-existent-type",
        },
      });

      expect(updateResponse.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(updateResponse.json().message).toBe("Contact type not found");
    });
  });

  describe("Delete Customer Contact", () => {
    it("should be able to delete contact", async () => {
      // create a new account contact
      const createContactResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/contacts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          firstName: "Customer 17",
          lastName: "Test",
          email: "<EMAIL>",
          phoneNumber: "+**********",
          accountIds: [global.testAccount.uid],
        },
      });

      expect(createContactResponse.statusCode).toBe(HttpStatus.CREATED);
      const contact = createContactResponse.json()
        .data as CustomerContactResponseDto;

      const deleteResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/contacts/${contact.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(deleteResponse.statusCode).toBe(HttpStatus.NO_CONTENT);

      // Verify contact is marked as inactive
      const contactData = await connection.query(
        `SELECT * FROM "public"."customer_contacts" WHERE uid = '${contact.id}' AND is_active = false;`,
      );

      expect(contactData.length).toBe(1);

      // Check activities
      const activitiesResponse = await connection.query(
        `SELECT * FROM "public"."audit_logs" WHERE organization_id = '${global.testOrganization.id}' AND entity_type = '${AuditLogEntityType.CUSTOMER_CONTACT}' AND entity_id = '${contactData[0].id}' AND operation = '${AuditLogOp.DELETED}';`,
      );

      expect(activitiesResponse.length).toBeGreaterThanOrEqual(1);
    });

    it("should throw error if contact does not exist", async () => {
      const deleteResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: "/v1/accounts/contacts/non-existent-contact",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(deleteResponse.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(deleteResponse.json().message).toBe("Contact not found");
    });
  });

  describe("Get Customer Contacts", () => {
    it("should be able to get all contacts for an account", async () => {
      const getResponse = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/accounts/contacts?accountId=${global.testAccount.uid}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(getResponse.statusCode).toBe(HttpStatus.OK);
      const results = getResponse.json().data.results as CustomerContactResponseDto[];

      expect(Array.isArray(results)).toBe(true);
    });

    it("should be able to filter contacts by contact type", async () => {
      const getResponse = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/accounts/contacts?accountId=${global.testAccount.uid}&contactType=${global.testContactType.uid}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(getResponse.statusCode).toBe(HttpStatus.OK);
      const results = getResponse.json().data.results as CustomerContactResponseDto[];

      expect(Array.isArray(results)).toBe(true);
      results.forEach((contact) => {
        expect(contact.contactTypeId).toBe(global.testContactType.uid);
      });
    });
  });

  describe("Delete account contact attributes", () => {
    it("should be able to delete unused attribute value", async () => {
      // create new note type
      const createContactTypeResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "TO BE DELETED WITHOUT FORCE DELETE",
          attribute: AccountAttributeType.CONTACT_TYPE,
          isDefault: false,
        },
      });

      const contactType = createContactTypeResponse.json()
        .data as AccountAttributeValueResponseDto;

      // delete note type
      const contactTypeDeleteResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/attributes/${contactType.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(contactTypeDeleteResponse.statusCode).toBe(HttpStatus.NO_CONTENT);
    });

    it("should throw an error when trying to delete attribute value that is in use", async () => {
      // create new contact type
      const createContactTypeResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "TO BE DELETED WITHOUT FORCE DELETE",
          attribute: AccountAttributeType.CONTACT_TYPE,
          isDefault: false,
        },
      });

      const contactType = createContactTypeResponse.json()
        .data as AccountAttributeValueResponseDto;

      // create contact with contact type
      const createContactResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/contacts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          firstName: "Customer 18",
          lastName: "Test",
          email: "<EMAIL>",
          phoneNumber: "+**********",
          accountIds: [global.testAccount.uid],
          contactType: contactType.id,
        },
      });
      expect(createContactResponse.statusCode).toBe(HttpStatus.CREATED);
      const contact = createContactResponse.json()
        .data as CustomerContactResponseDto;

      // Delete contact type
      const contactTypeDeleteResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/attributes/${contactType.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(contactTypeDeleteResponse.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(contactTypeDeleteResponse.json().message).toBe(
        "Cannot delete attribute value. It is in use.",
      );

      // check if account details are still correct
      const latestContact = await connection.query(
        `SELECT * FROM "public"."customer_contacts" WHERE uid = '${contact.id}';`,
      );
      const latestContactType = await connection.query(
        `SELECT * FROM "public"."account_attribute_values" WHERE uid = '${contactType.id}';`,
      );
      expect(latestContact.length).toBe(1);
      expect(latestContact[0].contact_type).toBe(latestContactType[0].id);
    });

    it("should be able to delete attribute value with force delete flag", async () => {
      // create new contact type
      const createContactTypeResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "TO BE DELETED WITHOUT FORCE DELETE",
          attribute: AccountAttributeType.CONTACT_TYPE,
          isDefault: false,
        },
      });

      const contactType = createContactTypeResponse.json()
        .data as AccountAttributeValueResponseDto;

      // create default contact type
      const createDefaultContactTypeResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "DEFAULT NOTE TYPE",
          attribute: AccountAttributeType.CONTACT_TYPE,
          isDefault: true,
        },
      });

      const defaultContactType = createDefaultContactTypeResponse.json()
        .data as AccountAttributeValueResponseDto;

      // create contact with contact type
      const createContactResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/contacts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          firstName: "Customer 19",
          lastName: "Test",
          email: "<EMAIL>",
          phoneNumber: "+**********",
          accountIds: [global.testAccount.uid],
          contactType: contactType.id,
        },
      });
      expect(createContactResponse.statusCode).toBe(HttpStatus.CREATED);
      const contact = createContactResponse.json()
        .data as CustomerContactResponseDto;

      // Delete contact type
      const contactTypeDeleteResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/attributes/${contactType.id}?forceDelete=true`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });
      expect(contactTypeDeleteResponse.statusCode).toBe(HttpStatus.NO_CONTENT);

      await sleep(500);

      // check if account details are still correct
      const latestContact = await connection.query(
        `SELECT * FROM "public"."customer_contacts" WHERE uid = '${contact.id}';`,
      );
      const latestContactType = await connection.query(
        `SELECT * FROM "public"."account_attribute_values" WHERE uid = '${defaultContactType.id}';`,
      );
      expect(latestContact.length).toBe(1);
      expect(latestContact[0].contact_type).toBe(latestContactType[0].id);
    });
  });
});
