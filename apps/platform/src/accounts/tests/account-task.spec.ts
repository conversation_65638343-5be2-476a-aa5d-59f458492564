import { faker } from "@faker-js/faker";
import {
  HttpStatus,
  UnprocessableEntityException,
  ValidationPipe,
} from "@nestjs/common";
import {
  FastifyAdapter,
  NestFastifyApplication,
} from "@nestjs/platform-fastify";
import { Test } from "@nestjs/testing";
import {
  AccountAttributeType,
  AuditLogEntityType,
  AuditLogOp,
  AuditLogUpdatedField,
} from "@repo/thena-platform-entities";
import { sleep, t__loginIntoAuthService } from "@repo/thena-shared-libs";
import { DataSource } from "typeorm";
import { AppModule } from "../../app.module";
import { injectWithOrgId } from "../../utils";
import { AccountActivityResponseDto } from "../dtos/response/account-activity.dto";
import { AccountAttributeValueResponseDto } from "../dtos/response/account-attribute-value.dto";
import { AccountTaskResponseDto } from "../dtos/response/account-task.dto";
import { AccountResponseDto } from "../dtos/response/account.dto";

describe("Account Tasks", () => {
  let app: NestFastifyApplication;
  let connection: DataSource;
  let userAuthToken: string;
  let taskTypeAttribute: AccountAttributeValueResponseDto;
  let taskStatusAttribute: AccountAttributeValueResponseDto;
  let taskPriorityAttribute: AccountAttributeValueResponseDto;
  let testAccount: AccountResponseDto;
  let testActivity: AccountActivityResponseDto;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = module.createNestApplication<NestFastifyApplication>(
      new FastifyAdapter(),
    );

    app.useGlobalPipes(
      new ValidationPipe({
        transform: true,
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        exceptionFactory: (errors) => {
          return new UnprocessableEntityException(
            errors.map((error) => ({
              property: error.property,
              constraints: error.constraints,
            })),
          );
        },
      }),
    );

    await app.init();
    await app.getHttpAdapter().getInstance().ready();

    connection = app.get(DataSource);

    // Log in user to get auth token
    userAuthToken = await t__loginIntoAuthService(
      global.testUser.email,
      global.testUser.password,
    );

    // Create test account
    const createAccountResponse = await injectWithOrgId(app, {
      method: "POST",
      url: "/v1/accounts",
      headers: {
        Authorization: `Bearer ${userAuthToken}`,
      },
      payload: {
        name: "Test Account for account task tests",
        primaryDomain: faker.internet.domainName(),
        accountOwnerId: global.testUser.uid,
        source: "manual",
      },
    });
    testAccount = createAccountResponse.json().data;

    // Create default activity type attribute
    await injectWithOrgId(app, {
      method: "POST",
      url: "/v1/accounts/attributes",
      headers: {
        Authorization: `Bearer ${userAuthToken}`,
      },
      payload: {
        value: "DEFAULT_MEETING",
        attribute: AccountAttributeType.ACTIVITY_TYPE,
        isDefault: true,
      },
    });

    // Create default activity status attribute
    await injectWithOrgId(app, {
      method: "POST",
      url: "/v1/accounts/attributes",
      headers: {
        Authorization: `Bearer ${userAuthToken}`,
      },
      payload: {
        value: "DEFAULT_STATUS",
        attribute: AccountAttributeType.ACTIVITY_STATUS,
        isDefault: true,
      },
    });

    // Create test activity
    const createActivityResponse = await injectWithOrgId(app, {
      method: "POST",
      url: "/v1/accounts/activities",
      headers: {
        Authorization: `Bearer ${userAuthToken}`,
      },
      payload: {
        accountId: testAccount.id,
        activityTimestamp: "2024-01-01T00:00:00.000Z",
        duration: 60,
        location: "New York, NY",
        participants: [global.testUser1.uid],
      },
    });
    testActivity = createActivityResponse.json().data;

    // Create task type attribute
    const createTypeAttributeResponse = await injectWithOrgId(app, {
      method: "POST",
      url: "/v1/accounts/attributes",
      headers: {
        Authorization: `Bearer ${userAuthToken}`,
      },
      payload: {
        value: "FOLLOW_UP",
        attribute: AccountAttributeType.TASK_TYPE,
        isDefault: false,
      },
    });
    taskTypeAttribute = createTypeAttributeResponse.json().data;

    // Create default task type attribute
    await injectWithOrgId(app, {
      method: "POST",
      url: "/v1/accounts/attributes",
      headers: {
        Authorization: `Bearer ${userAuthToken}`,
      },
      payload: {
        value: "GENERAL",
        attribute: AccountAttributeType.TASK_TYPE,
        isDefault: true,
      },
    });

    // Create task status attribute
    const createStatusAttributeResponse = await injectWithOrgId(app, {
      method: "POST",
      url: "/v1/accounts/attributes",
      headers: {
        Authorization: `Bearer ${userAuthToken}`,
      },
      payload: {
        value: "IN_PROGRESS",
        attribute: AccountAttributeType.TASK_STATUS,
        isDefault: false,
      },
    });
    taskStatusAttribute = createStatusAttributeResponse.json().data;

    // Create default task status attribute
    await injectWithOrgId(app, {
      method: "POST",
      url: "/v1/accounts/attributes",
      headers: {
        Authorization: `Bearer ${userAuthToken}`,
      },
      payload: {
        value: "PENDING",
        attribute: AccountAttributeType.TASK_STATUS,
        isDefault: true,
      },
    });

    // Create task priority attribute
    const createPriorityAttributeResponse = await injectWithOrgId(app, {
      method: "POST",
      url: "/v1/accounts/attributes",
      headers: {
        Authorization: `Bearer ${userAuthToken}`,
      },
      payload: {
        value: "HIGH",
        attribute: AccountAttributeType.TASK_PRIORITY,
        isDefault: false,
      },
    });
    taskPriorityAttribute = createPriorityAttributeResponse.json().data;

    // Create default task priority attribute
    await injectWithOrgId(app, {
      method: "POST",
      url: "/v1/accounts/attributes",
      headers: {
        Authorization: `Bearer ${userAuthToken}`,
      },
      payload: {
        value: "LOW",
        attribute: AccountAttributeType.TASK_PRIORITY,
        isDefault: true,
      },
    });
  });

  afterAll(async () => {
    await app.close();
  });

  describe("Create Account Task", () => {
    it("should be able to create account task when all details are provided", async () => {
      const createAccountTaskResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/tasks",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          activityId: testActivity.id,
          title: "Test task",
          description: "Test task description",
          assigneeId: global.testUser1.uid,
          type: taskTypeAttribute.id,
          status: taskStatusAttribute.id,
          priority: taskPriorityAttribute.id,
        },
      });

      expect(createAccountTaskResponse.statusCode).toBe(HttpStatus.CREATED);
      const result = createAccountTaskResponse.json().data;

      expect(result).toHaveProperty("id");
      expect(result.accountId).toBe(testAccount.id);
      expect(result.title).toBe("Test task");
      expect(result.description).toBe("Test task description");
      expect(result.assigneeId).toBe(global.testUser1.uid);
      expect(result.type).toBe(taskTypeAttribute.value);
      expect(result.typeId).toBe(taskTypeAttribute.id);
      expect(result.status).toBe(taskStatusAttribute.value);
      expect(result.statusId).toBe(taskStatusAttribute.id);
      expect(result.priority).toBe(taskPriorityAttribute.value);
      expect(result.priorityId).toBe(taskPriorityAttribute.id);
      expect(result.creatorId).toBe(global.testUser.uid);
    });

    it("should be able to use default attributes if not provided", async () => {
      const createAccountTaskResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/tasks",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          title: "Test task",
          assigneeId: global.testUser1.uid,
        },
      });

      expect(createAccountTaskResponse.statusCode).toBe(HttpStatus.CREATED);
      const result = createAccountTaskResponse.json().data;

      expect(result.type).toBeDefined();
      expect(result.typeId).toBeDefined();
      expect(result.status).toBeDefined();
      expect(result.statusId).toBeDefined();
      expect(result.priority).toBeDefined();
      expect(result.priorityId).toBeDefined();
    });

    it("should throw error when account id is invalid", async () => {
      const createAccountTaskResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/tasks",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: "non-existent-account",
          title: "Test task",
          assigneeId: global.testUser1.uid,
        },
      });

      expect(createAccountTaskResponse.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(createAccountTaskResponse.json().message).toBe(
        "Account not found",
      );
    });

    it("should throw error when assignee id is invalid", async () => {
      const createAccountTaskResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/tasks",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          title: "Test task",
          assigneeId: "non-existent-user",
        },
      });

      expect(createAccountTaskResponse.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(createAccountTaskResponse.json().message).toBe(
        "Assignee not found",
      );
    });

    it("should record audit log post successful creation", async () => {
      const createAccountTaskResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/tasks",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          title: "Test task",
          assigneeId: global.testUser1.uid,
        },
      });

      const task = createAccountTaskResponse.json()
        .data as AccountTaskResponseDto;

      // Get organization ID from database
      const orgData = await connection.query(
        `SELECT id FROM "public"."organization" WHERE id = '${global.testUser1.organization_id}';`,
      );

      const taskData = await connection.query(
        `SELECT * FROM "public"."account_tasks" WHERE uid = '${task.id}';`,
      );

      // Check audit logs
      const auditLogs = await connection.query(
        `SELECT * FROM "public"."audit_logs" WHERE organization_id = '${orgData[0].id}' AND entity_type = '${AuditLogEntityType.ACCOUNT_TASK}' AND entity_id = '${taskData[0].id}' AND operation = '${AuditLogOp.CREATED}';`,
      );

      expect(auditLogs.length).toBe(1);
    });

    it("should throw error when activity provided is not of same account", async () => {
      // Create another account
      const createAnotherAccountResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          name: "Another Test Account",
          primaryDomain: faker.internet.domainName(),
          accountOwnerId: global.testUser.uid,
          source: "manual",
        },
      });

      expect(createAnotherAccountResponse.statusCode).toBe(HttpStatus.CREATED);
      const anotherAccount = createAnotherAccountResponse.json().data;

      // Create an activity for another account
      const createActivityResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/activities",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: anotherAccount.id,
          activityTimestamp: "2024-01-01T00:00:00.000Z",
          duration: 60,
          location: "New York, NY",
        },
      });

      expect(createActivityResponse.statusCode).toBe(HttpStatus.CREATED);
      const activity = createActivityResponse.json().data;

      // Try to create task with activity from different account
      const createTaskResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/tasks",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          activityId: activity.id,
          title: "Test task",
          description: "Test task description",
          assigneeId: global.testUser1.uid,
        },
      });

      expect(createTaskResponse.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(createTaskResponse.json().message).toBe(
        "Activity does not belong to the same account",
      );
    });

    it("should throw error when attribute values are invalid", async () => {
      const invalidTypeTaskResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/tasks",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          title: "Test task",
          assigneeId: global.testUser1.uid,
          type: "non-existent-type",
        },
      });

      expect(invalidTypeTaskResponse.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(invalidTypeTaskResponse.json().message).toBe(
        "Provided value for task_type not found",
      );

      const invalidStatusTaskResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/tasks",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          title: "Test task",
          assigneeId: global.testUser1.uid,
          status: "non-existent-status",
        },
      });

      expect(invalidStatusTaskResponse.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(invalidStatusTaskResponse.json().message).toBe(
        "Provided value for task_status not found",
      );

      const invalidPriorityTaskResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/tasks",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          title: "Test task",
          assigneeId: global.testUser1.uid,
          priority: "non-existent-priority",
        },
      });

      expect(invalidPriorityTaskResponse.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(invalidPriorityTaskResponse.json().message).toBe(
        "Provided value for task_priority not found",
      );
    });

    it("should be able to attach files to a task", async () => {
      const createAccountTaskResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/tasks",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          title: "Test task",
          assigneeId: global.testUser1.uid,
          attachmentUrls: [
            "https://thena-new-backend-test.s3.us-east-1.amazonaws.com/uploads/image-screenshots.png",
          ],
        },
      });
      expect(createAccountTaskResponse.statusCode).toBe(HttpStatus.CREATED);

      const task = createAccountTaskResponse.json()
        .data as AccountTaskResponseDto;

      expect(task.attachments.length).toBe(1);
    });
  });

  describe("Update Account Task", () => {
    it("should be able to update all task fields", async () => {
      // Create a task first
      const createAccountTaskResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/tasks",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          title: "Original task",
          assigneeId: global.testUser1.uid,
        },
      });

      const task = createAccountTaskResponse.json()
        .data as AccountTaskResponseDto;

      // Create new attributes for update
      const createNewTypeResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "UPDATED_TYPE",
          attribute: AccountAttributeType.TASK_TYPE,
          isDefault: false,
        },
      });

      const newTaskType = createNewTypeResponse.json().data;

      const createNewStatusResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "UPDATED_STATUS",
          attribute: AccountAttributeType.TASK_STATUS,
          isDefault: false,
        },
      });

      const newTaskStatus = createNewStatusResponse.json().data;

      const createNewPriorityResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "UPDATED_PRIORITY",
          attribute: AccountAttributeType.TASK_PRIORITY,
          isDefault: false,
        },
      });

      const newTaskPriority = createNewPriorityResponse.json().data;

      // Update the task
      const updateAccountTaskResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/tasks/${task.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          title: "Updated task",
          description: "Updated description",
          assigneeId: global.testUser2.uid,
          type: newTaskType.id,
          status: newTaskStatus.id,
          priority: newTaskPriority.id,
        },
      });

      expect(updateAccountTaskResponse.statusCode).toBe(HttpStatus.OK);
      const result = updateAccountTaskResponse.json()
        .data as AccountTaskResponseDto;

      expect(result.title).toBe("Updated task");
      expect(result.description).toBe("Updated description");
      expect(result.assigneeId).toBe(global.testUser2.uid);
      expect(result.type).toBe(newTaskType.value);
      expect(result.typeId).toBe(newTaskType.id);
      expect(result.status).toBe(newTaskStatus.value);
      expect(result.statusId).toBe(newTaskStatus.id);
      expect(result.priority).toBe(newTaskPriority.value);
      expect(result.priorityId).toBe(newTaskPriority.id);
    });

    it("should throw error when task is invalid", async () => {
      const updateAccountTaskResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: "/v1/accounts/tasks/non-existent-task",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          title: "Updated task",
        },
      });

      expect(updateAccountTaskResponse.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(updateAccountTaskResponse.json().message).toBe("Task not found");
    });

    it("should throw error when assignee is invalid", async () => {
      // Create a task first
      const createAccountTaskResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/tasks",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          title: "Test task",
          assigneeId: global.testUser1.uid,
        },
      });

      const task = createAccountTaskResponse.json()
        .data as AccountTaskResponseDto;

      const updateAccountTaskResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/tasks/${task.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          assigneeId: "non-existent-user",
        },
      });

      expect(updateAccountTaskResponse.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(updateAccountTaskResponse.json().message).toBe(
        "Assignee not found",
      );
    });

    it("should record audit log with updated fields", async () => {
      // Create a task first
      const createAccountTaskResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/tasks",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          title: "Original task",
          assigneeId: global.testUser1.uid,
        },
      });

      const task = createAccountTaskResponse.json()
        .data as AccountTaskResponseDto;

      // Update the task
      const updateAccountTaskResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/tasks/${task.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          title: "Updated task",
          description: "Updated description",
        },
      });

      expect(updateAccountTaskResponse.statusCode).toBe(HttpStatus.OK);

      // Get organization ID from database
      const orgData = await connection.query(
        `SELECT id FROM "public"."organization" WHERE id = '${global.testUser1.organization_id}';`,
      );

      const taskData = await connection.query(
        `SELECT * FROM "public"."account_tasks" WHERE uid = '${task.id}';`,
      );

      // Check audit logs
      const auditLogs = await connection.query(
        `SELECT * FROM "public"."audit_logs" WHERE organization_id = '${orgData[0].id}' AND entity_type = '${AuditLogEntityType.ACCOUNT_TASK}' AND entity_id = '${taskData[0].id}' AND operation = '${AuditLogOp.UPDATED}';`,
      );

      expect(auditLogs.length).toBe(1);
      expect(
        auditLogs[0].metadata.updatedFields.some(
          (m: AuditLogUpdatedField) => m.field === "title",
        ),
      ).toBe(true);
      expect(
        auditLogs[0].metadata.updatedFields.some(
          (m: AuditLogUpdatedField) => m.field === "description",
        ),
      ).toBe(true);
    });

    it("should throw error when activity provided is not of same account", async () => {
      // create task first
      const createTaskResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/tasks",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          title: "Test task",
          assigneeId: global.testUser3.uid,
        },
      });
      const task = createTaskResponse.json().data;

      // Create another account
      const createAnotherAccountResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          name: "Another Test Account",
          primaryDomain: faker.internet.domainName(),
          accountOwnerId: global.testUser.uid,
          source: "manual",
        },
      });

      expect(createAnotherAccountResponse.statusCode).toBe(HttpStatus.CREATED);
      const anotherAccount = createAnotherAccountResponse.json().data;

      // Create an activity for another account
      const createActivityResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/activities",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: anotherAccount.id,
          activityTimestamp: "2024-01-01T00:00:00.000Z",
          duration: 60,
          location: "New York, NY",
        },
      });

      expect(createActivityResponse.statusCode).toBe(HttpStatus.CREATED);
      const activity = createActivityResponse.json().data;

      // Try to update task with activity from different account
      const updateTaskResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/tasks/${task.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          activityId: activity.id,
        },
      });

      expect(updateTaskResponse.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(updateTaskResponse.json().message).toBe(
        "Activity does not belong to the same account",
      );
    });

    it("should throw error when attribute values are invalid", async () => {
      // create task first
      const createTaskResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/tasks",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          title: "Test task",
          assigneeId: global.testUser3.uid,
        },
      });
      const task = createTaskResponse.json().data;

      const invalidTypeTaskResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/tasks/${task.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          type: "non-existent-type",
        },
      });

      expect(invalidTypeTaskResponse.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(invalidTypeTaskResponse.json().message).toBe(
        "Provided value for task_type not found",
      );

      const invalidStatusTaskResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/tasks/${task.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          status: "non-existent-status",
        },
      });

      expect(invalidStatusTaskResponse.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(invalidStatusTaskResponse.json().message).toBe(
        "Provided value for task_status not found",
      );

      const invalidPriorityTaskResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/tasks/${task.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          priority: "non-existent-priority",
        },
      });

      expect(invalidPriorityTaskResponse.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(invalidPriorityTaskResponse.json().message).toBe(
        "Provided value for task_priority not found",
      );
    });

    it("should be able to attach files to a task", async () => {
      const createAccountTaskWithoutAttachmentsResponse = await injectWithOrgId(
        app,
        {
          method: "POST",
          url: "/v1/accounts/tasks",
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            accountId: testAccount.id,
            title: "Test task for account task attachment update 1",
            assigneeId: global.testUser1.uid,
          },
        },
      );
      expect(createAccountTaskWithoutAttachmentsResponse.statusCode).toBe(
        HttpStatus.CREATED,
      );

      const taskWithoutAttachments =
        createAccountTaskWithoutAttachmentsResponse.json()
          .data as AccountTaskResponseDto;

      const createAccountTaskResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/tasks",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          title: "Test task for account task attachment update 2",
          assigneeId: global.testUser1.uid,
          attachmentUrls: [
            "https://thena-new-backend-test.s3.us-east-1.amazonaws.com/uploads/image-screenshots.png",
          ],
        },
      });
      expect(createAccountTaskResponse.statusCode).toBe(HttpStatus.CREATED);

      const task = createAccountTaskResponse.json()
        .data as AccountTaskResponseDto;

      expect(task.attachments.length).toBe(1);

      const updateAccountTaskWithoutAttachmentsResponse = await injectWithOrgId(
        app,
        {
          method: "PUT",
          url: `/v1/accounts/tasks/${taskWithoutAttachments.id}`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            attachmentUrls: [
              "https://thena-new-backend-test.s3.us-east-1.amazonaws.com/uploads/checking/Screenshot+2025-01-13+at+11.01.34%E2%80%AFPM.png",
            ],
          },
        },
      );

      const updatedTaskWithoutAttachments =
        updateAccountTaskWithoutAttachmentsResponse.json()
          .data as AccountTaskResponseDto;

      expect(updatedTaskWithoutAttachments.attachments.length).toBe(1);

      const updateAccountTaskResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/tasks/${task.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          attachmentUrls: [
            "https://thena-new-backend-test.s3.us-east-1.amazonaws.com/uploads/checking/Screenshot+2025-01-13+at+11.01.34%E2%80%AFPM.png",
          ],
        },
      });

      const updatedTask = updateAccountTaskResponse.json()
        .data as AccountTaskResponseDto;

      expect(updatedTask.attachments.length).toBe(2);
    });
  });

  describe("Delete Account Task", () => {
    it("should be able to delete an existing task", async () => {
      // Create a task first
      const createAccountTaskResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/tasks",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          title: "Task to be deleted",
          assigneeId: global.testUser1.uid,
        },
      });

      const task = createAccountTaskResponse.json()
        .data as AccountTaskResponseDto;

      // Delete the task
      const deleteAccountTaskResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/tasks/${task.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(deleteAccountTaskResponse.statusCode).toBe(HttpStatus.NO_CONTENT);

      // Verify task is marked as inactive
      const taskData = await connection.query(
        `SELECT * FROM "public"."account_tasks" WHERE uid = '${task.id}' AND is_active = false;`,
      );

      expect(taskData.length).toBe(1);
    });

    it("should throw error when task is non existent or already deleted", async () => {
      // Try deleting non-existent task
      const deleteNonExistentResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: "/v1/accounts/tasks/non-existent-task",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(deleteNonExistentResponse.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(deleteNonExistentResponse.json().message).toBe("Task not found");

      // Create and delete a task
      const createAccountTaskResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/tasks",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          title: "Task to be deleted",
          assigneeId: global.testUser1.uid,
        },
      });

      const task = createAccountTaskResponse.json()
        .data as AccountTaskResponseDto;

      // Delete the task
      await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/tasks/${task.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      // Try deleting again
      const deleteAgainResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/tasks/${task.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(deleteAgainResponse.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(deleteAgainResponse.json().message).toBe("Task not found");
    });

    it("should record audit log on successful deletion", async () => {
      // Create a task first
      const createAccountTaskResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/tasks",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          title: "Task to be deleted",
          assigneeId: global.testUser1.uid,
        },
      });

      const task = createAccountTaskResponse.json()
        .data as AccountTaskResponseDto;

      // Delete the task
      await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/tasks/${task.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      // Get organization ID from database
      const orgData = await connection.query(
        `SELECT id FROM "public"."organization" WHERE id = '${global.testUser1.organization_id}';`,
      );

      const taskData = await connection.query(
        `SELECT * FROM "public"."account_tasks" WHERE uid = '${task.id}';`,
      );

      // Check audit logs
      const auditLogs = await connection.query(
        `SELECT * FROM "public"."audit_logs" WHERE organization_id = '${orgData[0].id}' AND entity_type = '${AuditLogEntityType.ACCOUNT_TASK}' AND entity_id = '${taskData[0].id}' AND operation = '${AuditLogOp.DELETED}';`,
      );

      expect(auditLogs.length).toBe(1);
    });
  });

  describe("Remove Attachment from Account Task", () => {
    it("should be able to remove an attachment from a task", async () => {
      const createAccountTaskResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/tasks",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          title: "Test task for account task attachment removal",
          assigneeId: global.testUser1.uid,
          attachmentUrls: [
            "https://thena-new-backend-test.s3.us-east-1.amazonaws.com/uploads/image-screenshots.png",
            "https://thena-new-backend-test.s3.us-east-1.amazonaws.com/uploads/checking/Screenshot+2025-01-13+at+11.01.34%E2%80%AFPM.png",
          ],
        },
      });

      const task = createAccountTaskResponse.json()
        .data as AccountTaskResponseDto;

      expect(task.attachments.length).toBe(2);

      const removeAttachmentResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/tasks/${task.id}/attachments/${task.attachments[0].id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(removeAttachmentResponse.statusCode).toBe(HttpStatus.NO_CONTENT);

      const updatedTaskResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/tasks/${task.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          title: "Updated task title",
        },
      });

      const updatedTask = updatedTaskResponse.json().data;
      expect(updatedTask.attachments.length).toBe(1);
    });
  });

  describe("Delete account activity attributes", () => {
    it("should be able to delete unused attribute value", async () => {
      // create new type
      const createTaskTypeResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "TO BE DELETED",
          attribute: AccountAttributeType.TASK_TYPE,
          isDefault: false,
        },
      });

      const taskType = createTaskTypeResponse.json()
        .data as AccountAttributeValueResponseDto;

      // create new status
      const createTaskStatusResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "TO BE DELETED",
          attribute: AccountAttributeType.TASK_STATUS,
          isDefault: false,
        },
      });

      const taskStatus = createTaskStatusResponse.json()
        .data as AccountAttributeValueResponseDto;

      // create new priority
      const createTaskPriorityResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "TO BE DELETED",
          attribute: AccountAttributeType.TASK_PRIORITY,
          isDefault: false,
        },
      });

      const taskPriority = createTaskPriorityResponse.json()
        .data as AccountAttributeValueResponseDto;

      // delete type
      const taskTypeDeleteResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/attributes/${taskType.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(taskTypeDeleteResponse.statusCode).toBe(HttpStatus.NO_CONTENT);

      // delete status
      const taskStatusDeleteResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/attributes/${taskStatus.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(taskStatusDeleteResponse.statusCode).toBe(HttpStatus.NO_CONTENT);

      // delete priority
      const taskPriorityDeleteResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/attributes/${taskPriority.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(taskPriorityDeleteResponse.statusCode).toBe(HttpStatus.NO_CONTENT);
    });

    it("should throw an error when trying to delete attribute value that is in use", async () => {
      // create new type
      const createTaskTypeResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "TO BE DELETED",
          attribute: AccountAttributeType.TASK_TYPE,
          isDefault: false,
        },
      });

      const taskType = createTaskTypeResponse.json()
        .data as AccountAttributeValueResponseDto;

      // create new status
      const createTaskStatusResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "TO BE DELETED",
          attribute: AccountAttributeType.TASK_STATUS,
          isDefault: false,
        },
      });

      const taskStatus = createTaskStatusResponse.json()
        .data as AccountAttributeValueResponseDto;

      // create new priority
      const createTaskPriorityResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "TO BE DELETED",
          attribute: AccountAttributeType.TASK_PRIORITY,
          isDefault: false,
        },
      });

      const taskPriority = createTaskPriorityResponse.json()
        .data as AccountAttributeValueResponseDto;

      // create task with type and status
      const createTaskResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/tasks",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          title: "Test task for account task attribute deletion",
          assigneeId: global.testUser1.uid,
          type: taskType.id,
          status: taskStatus.id,
          priority: taskPriority.id,
        },
      });
      expect(createTaskResponse.statusCode).toBe(HttpStatus.CREATED);
      const task = createTaskResponse.json().data as AccountTaskResponseDto;

      // delete type
      const taskTypeDeleteResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/attributes/${taskType.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(taskTypeDeleteResponse.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(taskTypeDeleteResponse.json().message).toBe(
        "Cannot delete attribute value. It is in use.",
      );

      // delete status
      const taskStatusDeleteResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/attributes/${taskStatus.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(taskStatusDeleteResponse.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(taskStatusDeleteResponse.json().message).toBe(
        "Cannot delete attribute value. It is in use.",
      );

      // delete priority
      const taskPriorityDeleteResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/attributes/${taskPriority.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(taskPriorityDeleteResponse.statusCode).toBe(
        HttpStatus.BAD_REQUEST,
      );
      expect(taskPriorityDeleteResponse.json().message).toBe(
        "Cannot delete attribute value. It is in use.",
      );

      // check if activity details are still correct
      const updatedTaskResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/tasks/${task.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          title: "Updated task title",
        },
      });

      const updatedTask = updatedTaskResponse.json().data;
      expect(updatedTask.typeId).toBe(taskType.id);
      expect(updatedTask.statusId).toBe(taskStatus.id);
      expect(updatedTask.priorityId).toBe(taskPriority.id);
    });

    it("should be able to delete attribute value with force delete flag", async () => {
      // create new type
      const createTaskTypeResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "TO BE DELETED",
          attribute: AccountAttributeType.TASK_TYPE,
          isDefault: false,
        },
      });

      const taskType = createTaskTypeResponse.json()
        .data as AccountAttributeValueResponseDto;

      // create new status
      const createTaskStatusResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "TO BE DELETED",
          attribute: AccountAttributeType.TASK_STATUS,
          isDefault: false,
        },
      });

      const taskStatus = createTaskStatusResponse.json()
        .data as AccountAttributeValueResponseDto;

      // create new priority
      const createTaskPriorityResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "TO BE DELETED",
          attribute: AccountAttributeType.TASK_PRIORITY,
          isDefault: false,
        },
      });

      const taskPriority = createTaskPriorityResponse.json()
        .data as AccountAttributeValueResponseDto;

      // create default type
      const createDefaultTaskTypeResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "DEFAULT TYPE",
          attribute: AccountAttributeType.TASK_TYPE,
          isDefault: true,
        },
      });

      const defaultTaskType = createDefaultTaskTypeResponse.json()
        .data as AccountAttributeValueResponseDto;

      // create default status
      const createDefaultTaskStatusResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "DEFAULT STATUS",
          attribute: AccountAttributeType.TASK_STATUS,
          isDefault: true,
        },
      });

      const defaultTaskStatus = createDefaultTaskStatusResponse.json()
        .data as AccountAttributeValueResponseDto;

      // create default priority
      const createDefaultTaskPriorityResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "DEFAULT PRIORITY",
          attribute: AccountAttributeType.TASK_PRIORITY,
          isDefault: true,
        },
      });

      const defaultTaskPriority = createDefaultTaskPriorityResponse.json()
        .data as AccountAttributeValueResponseDto;

      // create task with type and status
      const createTaskResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/tasks",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          title: "Test task for account task attribute deletion",
          assigneeId: global.testUser1.uid,
          type: taskType.id,
          status: taskStatus.id,
          priority: taskPriority.id,
        },
      });
      expect(createTaskResponse.statusCode).toBe(HttpStatus.CREATED);
      const task = createTaskResponse.json().data as AccountTaskResponseDto;

      // delete type
      const taskTypeDeleteResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/attributes/${taskType.id}?forceDelete=true`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(taskTypeDeleteResponse.statusCode).toBe(HttpStatus.NO_CONTENT);

      // delete status
      const taskStatusDeleteResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/attributes/${taskStatus.id}?forceDelete=true`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(taskStatusDeleteResponse.statusCode).toBe(HttpStatus.NO_CONTENT);

      // delete priority
      const taskPriorityDeleteResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/attributes/${taskPriority.id}?forceDelete=true`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(taskPriorityDeleteResponse.statusCode).toBe(HttpStatus.NO_CONTENT);

      await sleep(500);

      // check if activity details are still correct
      const updatedTaskResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/tasks/${task.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          title: "Updated task title",
        },
      });

      const updatedTask = updatedTaskResponse.json().data;
      expect(updatedTask.typeId).toBe(defaultTaskType.id);
      expect(updatedTask.statusId).toBe(defaultTaskStatus.id);
      expect(updatedTask.priorityId).toBe(defaultTaskPriority.id);
    });
  });

  describe("Find All Account Tasks", () => {
    let tasks: AccountTaskResponseDto[] = [];

    beforeAll(async () => {
      // Create multiple tasks with different attributes for testing
      const createTaskPromises = Array(5)
        .fill(null)
        .map(async (_, index) => {
          const response = await injectWithOrgId(app, {
            method: "POST",
            url: "/v1/accounts/tasks",
            headers: {
              Authorization: `Bearer ${userAuthToken}`,
            },
            payload: {
              accountId: testAccount.id,
              title: `Test Task ${index + 1}`,
              assigneeId: global.testUser.uid,
              type: taskTypeAttribute.id,
              status: taskStatusAttribute.id,
              priority: taskPriorityAttribute.id,
              description: `Description for task ${index + 1}`,
            },
          });
          return response.json().data;
        });

      tasks = await Promise.all(createTaskPromises);
    });

    it("should respect pagination limit", async () => {
      const limit = 2;
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/accounts/tasks",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        query: {
          limit: limit.toString(),
        },
      });

      const data = response.json().data.results;
      expect(response.statusCode).toBe(200);
      expect(data.length).toBe(limit);
    });

    it("should respect pagination offset", async () => {
      const limit = 2;
      const page = 1;
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/accounts/tasks",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        query: {
          limit: limit.toString(),
          page: page.toString(),
        },
      });

      const data = response.json().data;
      expect(response.statusCode).toBe(200);
      expect(data.length).toBe(limit);
      expect(data[0].id).not.toBe(tasks[0].id); // Should be different from first page
    });

    it("should filter by accountId", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/accounts/tasks",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        query: {
          accountId: testAccount.id,
        },
      });

      const data = response.json().data.results;
      expect(response.statusCode).toBe(200);
      data.forEach((task) => {
        expect(task.accountId).toBe(testAccount.id);
      });
    });

    it("should filter by assigneeId", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/accounts/tasks",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        query: {
          assigneeId: global.testUser.uid,
        },
      });

      const data = response.json().data.results;
      expect(response.statusCode).toBe(200);
      data.forEach((task) => {
        expect(task.assigneeId).toBe(global.testUser.uid);
      });
    });

    it("should filter by type", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/accounts/tasks",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        query: {
          type: taskTypeAttribute.value,
        },
      });

      const data = response.json().data.results;
      expect(response.statusCode).toBe(200);
      data.forEach((task) => {
        expect(task.typeId).toBe(taskTypeAttribute.id);
      });
    });

    it("should filter by status", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/accounts/tasks",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        query: {
          status: taskStatusAttribute.value,
        },
      });

      const data = response.json().data.results;
      expect(response.statusCode).toBe(200);
      data.forEach((task) => {
        expect(task.statusId).toBe(taskStatusAttribute.id);
      });
    });

    it("should filter by priority", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/accounts/tasks",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        query: {
          priority: taskPriorityAttribute.value,
        },
      });

      const data = response.json().data.results;
      expect(response.statusCode).toBe(200);
      data.forEach((task) => {
        expect(task.priorityId).toBe(taskPriorityAttribute.id);
      });
    });

    it("should combine multiple filters", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/accounts/tasks",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        query: {
          accountId: testAccount.id,
          assigneeId: global.testUser.uid,
          type: taskTypeAttribute.value,
          status: taskStatusAttribute.value,
          priority: taskPriorityAttribute.value,
          limit: "10",
        },
      });

      const data = response.json().data.results;
      expect(response.statusCode).toBe(200);
      data.forEach((task) => {
        expect(task.accountId).toBe(testAccount.id);
        expect(task.assigneeId).toBe(global.testUser.uid);
        expect(task.typeId).toBe(taskTypeAttribute.id);
        expect(task.statusId).toBe(taskStatusAttribute.id);
        expect(task.priorityId).toBe(taskPriorityAttribute.id);
      });
    });

    afterAll(async () => {
      // Clean up created tasks
      for (const task of tasks) {
        await injectWithOrgId(app, {
          method: "DELETE",
          url: `/v1/accounts/tasks/${task.id}`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
        });
      }
    });
  });
});
