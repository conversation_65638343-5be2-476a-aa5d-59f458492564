import { faker } from "@faker-js/faker";
import {
  HttpStatus,
  UnprocessableEntityException,
  ValidationPipe,
} from "@nestjs/common";
import {
  FastifyAdapter,
  NestFastifyApplication,
} from "@nestjs/platform-fastify";
import { Test } from "@nestjs/testing";
import {
  AccountAttributeType,
  DEFAULT_ACCOUNT_ATTRIBUTE_VALUE_SEEDS,
} from "@repo/thena-platform-entities";
import { sleep, t__loginIntoAuthService } from "@repo/thena-shared-libs";
import { DataSource } from "typeorm";
import { AppModule } from "../../app.module";
import { injectWithOrgId } from "../../utils";
import { AccountAttributeValueResponseDto } from "../dtos/response/account-attribute-value.dto";

describe("Account Attribute Values", () => {
  let app: NestFastifyApplication;
  let connection: DataSource;
  let userAuthToken: string;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = module.createNestApplication<NestFastifyApplication>(
      new FastifyAdapter(),
    );

    app.useGlobalPipes(
      new ValidationPipe({
        transform: true,
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        exceptionFactory: (errors) => {
          return new UnprocessableEntityException(
            errors.map((error) => ({
              property: error.property,
              constraints: error.constraints,
            })),
          );
        },
      }),
    );

    await app.init();
    await app.getHttpAdapter().getInstance().ready();

    connection = app.get(DataSource);

    // Log the user in to get the auth token
    userAuthToken = await t__loginIntoAuthService(
      global.testUser.email,
      global.testUser.password,
    );
  });

  afterAll(async () => {
    await app.close();
  });

  describe("Create Account Attribute Value", () => {
    it("should be able to create account attribute value when all details are provided", async () => {
      const createAttributeValueResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "PROSPECT",
          attribute: AccountAttributeType.ACCOUNT_STATUS,
          isDefault: false,
        },
      });

      expect(createAttributeValueResponse.statusCode).toBe(HttpStatus.CREATED);
      const result = createAttributeValueResponse.json().data;

      expect(result).toHaveProperty("id");
      expect(result.value).toBe("PROSPECT");
      expect(result.attribute).toBe(AccountAttributeType.ACCOUNT_STATUS);
      expect(result.isDefault).toBe(false);
    });

    it("should be able to swap the isDefault flag from old default value when isDefault is provided true", async () => {
      // First default value is created from global setup

      // Create second default value
      const createDefaultAttributeValueResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "TRIAL",
          attribute: AccountAttributeType.ACCOUNT_STATUS,
          isDefault: true,
        },
      });

      const result = createDefaultAttributeValueResponse.json().data;
      expect(result.isDefault).toBe(true);

      // Check first value is no longer default
      const attributeValuesResponse = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/accounts/attributes`,
        query: {
          attribute: AccountAttributeType.ACCOUNT_STATUS,
        },
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      const attributeValues = attributeValuesResponse.json().data;
      expect(
        attributeValues.filter(
          (v: AccountAttributeValueResponseDto) => v.isDefault,
        ).length,
      ).toBe(1);
    });

    it("should be able to create default seeds on the account attribute value table when organization is created", async () => {
      const createOrganizationResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/organizations",
        payload: {
          name: "Test Organization for account attribute value seeds",
          email: `${faker.internet.email()}`,
          password: "test-password",
        },
      });

      const organization = createOrganizationResponse.json().data;

      await sleep(1000);

      const attributeValues = await connection.query(
        `SELECT * FROM "public"."account_attribute_values" WHERE organization_id = '${organization.id}' AND is_active = true;`,
      );

      // Verify that default seeds were created for each attribute type
      for (const attributeType of Object.values(AccountAttributeType)) {
        const seedValues = DEFAULT_ACCOUNT_ATTRIBUTE_VALUE_SEEDS[attributeType];
        const attributeValuesForType = attributeValues.filter(
          (av: any) => av.attribute === attributeType,
        );

        // Check that all seed values were created
        expect(attributeValuesForType.length).toBeGreaterThanOrEqual(
          seedValues.length,
        );

        // Verify each seed value exists
        for (const seedValue of seedValues) {
          expect(
            attributeValuesForType.some(
              (av: any) => av.value.toUpperCase() === seedValue,
            ),
          ).toBe(true);
        }

        // Verify exactly one default value exists per type
        const defaultValuesForType = attributeValuesForType.filter(
          (av: any) => av.is_default,
        );
        expect(defaultValuesForType.length).toBe(1);
        expect(seedValues).toContain(
          defaultValuesForType[0].value.toUpperCase(),
        );
      }
    });
  });

  describe("Update Account Attribute Value", () => {
    it("should be able to update isDefault flag and remove isDefault from existing default attribute value", async () => {
      // There is a default one created from global setup

      // Create a new attribute value
      const createAttributeValueResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "PROSPECT",
          attribute: AccountAttributeType.ACCOUNT_STATUS,
          isDefault: false,
        },
      });

      expect(createAttributeValueResponse.statusCode).toBe(HttpStatus.CREATED);
      const result = createAttributeValueResponse.json()
        .data as AccountAttributeValueResponseDto;

      // Update test value to be default
      const updateResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/attributes/${result.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          isDefault: true,
        },
      });

      expect(updateResponse.statusCode).toBe(HttpStatus.OK);
      const updatedValue = updateResponse.json()
        .data as AccountAttributeValueResponseDto;
      expect(updatedValue.isDefault).toBe(true);

      // Check first value is no longer default
      const attributeValuesResponse = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/accounts/attributes`,
        query: {
          attribute: AccountAttributeType.ACCOUNT_STATUS,
        },
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      const attributeValues = attributeValuesResponse.json().data;
      expect(
        attributeValues.filter(
          (v: AccountAttributeValueResponseDto) => v.isDefault,
        ).length,
      ).toBe(1);
    });

    it("should be able to update the value of existing account attribute value entry", async () => {
      const updateResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/attributes/${global.testAccountStatus.uid}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "Updated ACTIVE",
        },
      });

      expect(updateResponse.statusCode).toBe(HttpStatus.OK);
      const result = updateResponse.json()
        .data as AccountAttributeValueResponseDto;
      expect(result.value).toBe("Updated ACTIVE");

      // Check first value is no longer default
      const attributeValuesResponse = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/accounts/attributes`,
        query: {
          attribute: AccountAttributeType.ACCOUNT_STATUS,
        },
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      const attributeValues = attributeValuesResponse.json().data;
      expect(
        attributeValues.find(
          (v: AccountAttributeValueResponseDto) =>
            v.id === global.testAccountStatus.uid,
        ).value,
      ).toBe("Updated ACTIVE");
    });

    it("should throw error when trying to set attribute value as not default when it is already default", async () => {
      // Create a new attribute value
      const createAttributeValueResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "DEFAULT_VALUE",
          attribute: AccountAttributeType.ACTIVITY_TYPE,
          isDefault: true,
        },
      });

      expect(createAttributeValueResponse.statusCode).toBe(HttpStatus.CREATED);
      const result = createAttributeValueResponse.json()
        .data as AccountAttributeValueResponseDto;

      // Update test value to be default
      const updateResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/attributes/${result.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          isDefault: false,
        },
      });

      expect(updateResponse.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(updateResponse.json().message).toBe(
        "Cannot set attribute value as not default. Assign another default value first.",
      );
    });

    it("should throw error when attribute value provided does not exist", async () => {
      const updateResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: "/v1/accounts/attributes/non-existent-id",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "Updated Value",
        },
      });

      expect(updateResponse.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(updateResponse.json().message).toBe(
        "Account attribute value not found",
      );
    });
  });

  describe("Find Account Attribute Values", () => {
    it("should be able to return all active account attribute values by attribute", async () => {
      await Promise.all([
        injectWithOrgId(app, {
          method: "POST",
          url: "/v1/accounts/attributes",
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            value: "Value 1",
            attribute: AccountAttributeType.ACCOUNT_CLASSIFICATION,
            isDefault: false,
          },
        }),
        injectWithOrgId(app, {
          method: "POST",
          url: "/v1/accounts/attributes",
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            value: "Value 2",
            attribute: AccountAttributeType.ACCOUNT_CLASSIFICATION,
            isDefault: false,
          },
        }),
      ]);

      await sleep(1000);

      const findResponse = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/accounts/attributes`,
        query: {
          attribute: AccountAttributeType.ACCOUNT_CLASSIFICATION,
        },
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(findResponse.statusCode).toBe(HttpStatus.OK);
      const values = findResponse.json().data;
      expect(values.length).toBeGreaterThanOrEqual(2); // There is a default one created from global setup
      expect(values.find((v: any) => v.value === "Value 1")).toBeDefined();
      expect(values.find((v: any) => v.value === "Value 2")).toBeDefined();
    });

    it("should not return the deleted account attribute values", async () => {
      const createAttributeValueResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "TO BE DELETED",
          attribute: AccountAttributeType.ACCOUNT_CLASSIFICATION,
          isDefault: false,
        },
      });

      const result = createAttributeValueResponse.json()
        .data as AccountAttributeValueResponseDto;

      // Delete the value
      await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/attributes/${result.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      // Get all values
      const findResponse = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/accounts/attributes`,
        query: {
          attribute: AccountAttributeType.ACCOUNT_CLASSIFICATION,
        },
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      const values = findResponse.json().data;
      expect(
        values.filter(
          (v: AccountAttributeValueResponseDto) => v.id === result.id,
        ).length,
      ).toBe(0);
    });
  });

  describe("Delete Account Attribute Value", () => {
    it("should be able to delete account attribute value", async () => {
      // Create a new attribute value to delete
      const createAttributeValueResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "TO BE DELETED",
          attribute: AccountAttributeType.ACCOUNT_CLASSIFICATION,
          isDefault: false,
        },
      });

      const result = createAttributeValueResponse.json()
        .data as AccountAttributeValueResponseDto;

      // Delete the value
      await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/attributes/${result.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      // Get all values
      const findResponse = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/accounts/attributes`,
        query: {
          attribute: AccountAttributeType.ACCOUNT_CLASSIFICATION,
        },
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      const values = findResponse.json().data;
      expect(
        values.filter(
          (v: AccountAttributeValueResponseDto) => v.id === result.id,
        ).length,
      ).toBe(0);
    });
  });
});
