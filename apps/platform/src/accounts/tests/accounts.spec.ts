import { faker } from "@faker-js/faker";
import {
  HttpStatus,
  UnprocessableEntityException,
  ValidationPipe,
} from "@nestjs/common";
import {
  FastifyAdapter,
  NestFastifyApplication,
} from "@nestjs/platform-fastify";
import { Test } from "@nestjs/testing";
import {
  AccountAttributeType,
  AuditLogEntityType,
  AuditLogOp,
  AuditLogUpdatedField,
  CustomFieldSource,
  CustomFieldType,
} from "@repo/thena-platform-entities";
import { sleep, t__loginIntoAuthService } from "@repo/thena-shared-libs";
import { DataSource } from "typeorm";
import { AppModule } from "../../app.module";
import { ExternalCustomFieldValuesDto } from "../../custom-field/dto/custom-field-values.dto";
import {
  CreateCustomFieldDto,
  CustomFieldResponseDto,
} from "../../custom-field/dto/custom-field.dto";
import { injectWithOrgId } from "../../utils";
import { CreateAccountDto } from "../dtos/account.dto";
import { AccountAttributeValueResponseDto } from "../dtos/response/account-attribute-value.dto";
import { AccountResponseDto } from "../dtos/response/account.dto";

describe("Accounts", () => {
  let app: NestFastifyApplication;
  let connection: DataSource;
  let userAuthToken: string;
  let textCustomField: CustomFieldResponseDto;
  let singleChoiceCustomField: CustomFieldResponseDto;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = module.createNestApplication<NestFastifyApplication>(
      new FastifyAdapter(),
    );

    app.useGlobalPipes(
      new ValidationPipe({
        transform: true,
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        exceptionFactory: (errors) => {
          return new UnprocessableEntityException(
            errors.map((error) => ({
              property: error.property,
              constraints: error.constraints,
            })),
          );
        },
      }),
    );

    await app.init();
    await app.getHttpAdapter().getInstance().ready();

    connection = app.get(DataSource);

    // Log the user in to get the auth token
    userAuthToken = await t__loginIntoAuthService(
      global.testUser.email,
      global.testUser.password,
    );

    // Create custom fields
    const textCustomFieldResponse = await injectWithOrgId(app, {
      method: "POST",
      url: "/v1/custom-field",
      headers: { Authorization: `Bearer ${userAuthToken}` },
      payload: {
        name: "Test Text Field",
        source: CustomFieldSource.ACCOUNT,
        fieldType: CustomFieldType.SINGLE_LINE,
        placeholderText: "Enter text here",
        hintText: "This is a hint",
        mandatoryOnCreation: false,
        mandatoryOnClose: false,
        visibleToCustomer: true,
        editableByCustomer: true,
        autoAddToAllForms: false,
        defaultValue: null,
      } as CreateCustomFieldDto,
    });

    textCustomField = textCustomFieldResponse.json();

    const singleChoiceCustomFieldWithOptionsResponse = await injectWithOrgId(
      app,
      {
        method: "POST",
        url: "/v1/custom-field",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          name: "Test Text Field with options",
          source: CustomFieldSource.ACCOUNT,
          fieldType: CustomFieldType.SINGLE_CHOICE,
          options: [
            {
              value: "High",
              is_disabled: false,
              order: 0,
              platformField: false,
            },
            {
              value: "Medium",
              is_disabled: false,
              order: 0,
              platformField: false,
            },
            {
              value: "Low",
              is_disabled: false,
              order: 0,
              platformField: false,
            },
          ],
          placeholderText: "Enter text here",
          hintText: "This is a hint",
          mandatoryOnCreation: false,
          mandatoryOnClose: false,
          visibleToCustomer: true,
          editableByCustomer: true,
          autoAddToAllForms: false,
          defaultValue: null,
        } as CreateCustomFieldDto,
      },
    );

    singleChoiceCustomField = singleChoiceCustomFieldWithOptionsResponse.json();
  });

  afterAll(async () => {
    await app.close();
  });

  describe("Create Account", () => {
    it("should be able to create account when all values are provided", async () => {
      const primaryDomain = faker.internet.domainName();
      const secondaryDomain = faker.internet.domainName();
      const website = faker.internet.url();
      const description = faker.lorem.paragraph();
      const billingAddress = faker.location.secondaryAddress();
      const shippingAddress = faker.location.secondaryAddress();
      const logo = faker.image.url();

      const createAccountResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          name: "Test Account 1",
          primaryDomain,
          secondaryDomain,
          accountOwnerId: global.testUser.uid,
          status: global.testAccountStatus.uid,
          classification: global.testAccountClassification.uid,
          health: global.testAccountHealth.uid,
          industry: global.testAccountIndustry.uid,
          annualRevenue: 1000000,
          employees: 100,
          website,
          description,
          billingAddress,
          shippingAddress,
          source: "manual",
          logo,
        },
      });

      expect(createAccountResponse.statusCode).toBe(HttpStatus.CREATED);
      const result = createAccountResponse.json().data as AccountResponseDto;

      expect(result).toHaveProperty("id");
      expect(result.name).toBe("Test Account 1");
      expect(result.statusId).toBe(global.testAccountStatus.uid);
      expect(result.classificationId).toBe(
        global.testAccountClassification.uid,
      );
      expect(result.healthId).toBe(global.testAccountHealth.uid);
      expect(result.industryId).toBe(global.testAccountIndustry.uid);
      expect(result.primaryDomain).toBe(primaryDomain);
      expect(result.secondaryDomain).toBe(secondaryDomain);
      expect(result.accountOwnerId).toBe(global.testUser.uid);
      expect(result.accountOwner).toBe(global.testUser.name);
      expect(result.annualRevenue).toBe(1000000);
      expect(result.employees).toBe(100);
      expect(result.website).toBe(website);
      expect(result.description).toBe(description);
      expect(result.billingAddress).toBe(billingAddress);
      expect(result.shippingAddress).toBe(shippingAddress);
      expect(result.logo).toBe(logo);
      expect(result.source).toBe("manual");
    });

    it("should be able to record an activity that account is created on success", async () => {
      const accountName = "Activity Test Account";
      const createAccountResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          name: accountName,
          primaryDomain: faker.internet.domainName(),
          accountOwnerId: global.testUser.uid,
          status: global.testAccountStatus.uid,
          classification: global.testAccountClassification.uid,
          health: global.testAccountHealth.uid,
          source: "manual",
        },
      });

      const account = createAccountResponse.json().data as AccountResponseDto;

      const accountData = await connection.query(
        `SELECT * FROM "public"."accounts" WHERE uid = '${account.id}';`,
      );

      // Check activities
      const activitiesResponse = await connection.query(
        `SELECT * FROM "public"."audit_logs" WHERE organization_id = '${global.testOrganization.id}' AND entity_type = '${AuditLogEntityType.ACCOUNT}' AND entity_id = '${accountData[0].id}' AND operation = '${AuditLogOp.CREATED}';`,
      );

      expect(activitiesResponse.length).toBe(1);
    });

    it("should throw error when tried with duplicate primary domain", async () => {
      // Try creating second account with same domain
      const duplicateResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          name: "Duplicate primary domain Account",
          primaryDomain: global.testAccount.primaryDomain,
          source: "manual",
        },
      });

      expect(duplicateResponse.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(duplicateResponse.json().message).toBe(
        "Primary domain is already in use by another account!",
      );
    });

    it("should throw error when status / classification / health attribute value is not a valid attribute value id", async () => {
      const createAccountResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          name: "Invalid Status Account",
          primaryDomain: faker.internet.domainName(),
          accountOwnerId: global.testUser.uid,
          status: "invalid-status-id",
          source: "manual",
        },
      });

      expect(createAccountResponse.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(createAccountResponse.json().message).toBe(
        "Provided value for account_status is not valid!",
      );
    });

    it("should throw error when account owner id is not a valid user id", async () => {
      const createAccountResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          name: "Invalid Owner Account",
          primaryDomain: faker.internet.domainName(),
          accountOwnerId: "invalid-user-id",
          source: "manual",
        },
      });

      expect(createAccountResponse.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(createAccountResponse.json().message).toBe(
        "Account owner not found!",
      );
    });

    it("should be able to link all the existing users matching primary domain to the account contacts", async () => {
      const primaryDomain = faker.internet.domainName();

      const organization = await connection.query(
        `SELECT * FROM "public"."organization" WHERE is_active = true`,
      );

      // create contact type
      const createContactTypeResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "Test Contact Type",
          attribute: AccountAttributeType.CONTACT_TYPE,
          isDefault: true,
        },
      });

      const contactTypeResponse = createContactTypeResponse.json()
        .data as AccountAttributeValueResponseDto;

      // get contact type from db
      const contactType = await connection.query(
        `SELECT * FROM "public"."account_attribute_values" WHERE uid = '${contactTypeResponse.id}';`,
      );

      // Create a new user
      await connection.query(
        `INSERT INTO "public"."customer_contacts" (uid, first_name, last_name, email, organization_id, contact_type) VALUES ('${faker.string.alpha(
          {
            length: 8,
            casing: "upper",
          },
        )}', 'Test user', 'for account creation', 'test-user-account-creation@${primaryDomain}', '${
          organization[0].id
        }', '${contactType[0].id}');`,
      );

      const createAccountResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          name: "Test Account for user linking",
          primaryDomain,
          accountOwnerId: global.testUser.uid,
          addExistingUsersToAccountContacts: true,
          source: "manual",
        },
      });

      const createdAccount = createAccountResponse.json()
        .data as AccountResponseDto;

      await sleep(100);

      const accountContacts = await connection.query(
        `SELECT * FROM "public"."customer_contact_accounts" 
				INNER JOIN "public"."accounts" ON "public"."customer_contact_accounts"."account_id" = "public"."accounts"."id" 
				WHERE "public"."accounts"."uid" = '${createdAccount.id}';`,
      );

      expect(accountContacts.length).toBe(1);
    });

    it("should be able to attach custom fields and return them in account details", async () => {
      const highOptionId = singleChoiceCustomField.data.options.find(
        (option) => option.value === "High",
      )?.id;

      const createAccountResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          name: "Test Account for custom fields",
          primaryDomain: faker.internet.domainName(),
          accountOwnerId: global.testUser.uid,
          source: "manual",
          customFieldValues: [
            {
              customFieldId: textCustomField.data.uid,
              data: [{ value: "Test value" }, { value: "Test value" }],
              metadata: {},
            } as ExternalCustomFieldValuesDto,
            {
              customFieldId: singleChoiceCustomField.data.uid,
              data: [{ value: highOptionId }, { value: highOptionId }],
              metadata: {},
            } as ExternalCustomFieldValuesDto,
          ],
        } as CreateAccountDto,
      });
      expect(createAccountResponse.statusCode).toBe(HttpStatus.CREATED);

      const account = createAccountResponse.json().data as AccountResponseDto;
      expect(account.name).toBe("Test Account for custom fields");
      expect(account.customFieldValues).toBeDefined();
      expect(account.customFieldValues?.length).toBe(2);
      expect(account.customFieldValues).toEqual([
        {
          customFieldId: textCustomField.data.uid,
          data: [{ value: "Test value" }],
          metadata: {},
        },
        {
          customFieldId: singleChoiceCustomField.data.uid,
          data: [{ value: highOptionId }],
          metadata: {},
        },
      ]);
    });
  });

  describe("Update Account", () => {
    it("should be able to update account when all values are provided and account exists", async () => {
      // Create a new test account
      const createAccountResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          name: "Test Account for update",
          primaryDomain: faker.internet.domainName(),
          accountOwnerId: global.testUser.uid,
          source: "manual",
        },
      });

      const createdAccount = createAccountResponse.json()
        .data as AccountResponseDto;

      // Create a new attribute value
      const createStatusAttributeValueResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "PROSPECT",
          attribute: AccountAttributeType.ACCOUNT_STATUS,
          isDefault: false,
        },
      });

      const statusAttributeValue = createStatusAttributeValueResponse.json()
        .data as AccountAttributeValueResponseDto;

      // Create a new attribute value
      const createClassificationAttributeValueResponse = await injectWithOrgId(
        app,
        {
          method: "POST",
          url: "/v1/accounts/attributes",
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            value: "ENTERPRISE",
            attribute: AccountAttributeType.ACCOUNT_CLASSIFICATION,
            isDefault: false,
          },
        },
      );

      const classificationAttributeValue =
        createClassificationAttributeValueResponse.json()
          .data as AccountAttributeValueResponseDto;

      // Create a new attribute value
      const createHealthAttributeValueResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "UNHEALTHY",
          attribute: AccountAttributeType.ACCOUNT_HEALTH,
          isDefault: false,
        },
      });

      const healthAttributeValue = createHealthAttributeValueResponse.json()
        .data as AccountAttributeValueResponseDto;

      // Create a new attribute value
      const createIndustryAttributeValueResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "TECHNOLOGY",
          attribute: AccountAttributeType.ACCOUNT_INDUSTRY,
          isDefault: false,
        },
      });

      const industryAttributeValue = createIndustryAttributeValueResponse.json()
        .data as AccountAttributeValueResponseDto;

      const primaryDomain = faker.internet.domainName();
      const secondaryDomain = faker.internet.domainName();
      const website = faker.internet.url();
      const description = faker.lorem.paragraph();
      const billingAddress = faker.location.secondaryAddress();
      const shippingAddress = faker.location.secondaryAddress();
      const logo = faker.image.url();

      // update account
      const updateResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/${createdAccount.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          name: "Updated Account Name",
          primaryDomain,
          secondaryDomain,
          website,
          description,
          billingAddress,
          shippingAddress,
          logo,
          annualRevenue: 2000000,
          employees: 200,
          accountOwnerId: global.testUser1.uid,
          status: statusAttributeValue.id,
          classification: classificationAttributeValue.id,
          health: healthAttributeValue.id,
          industry: industryAttributeValue.id,
        },
      });

      expect(updateResponse.statusCode).toBe(HttpStatus.OK);
      const result = updateResponse.json().data as AccountResponseDto;
      expect(result.id).toBe(createdAccount.id);
      expect(result.name).toBe("Updated Account Name");
      expect(result.status).toBe(statusAttributeValue.value);
      expect(result.classification).toBe(classificationAttributeValue.value);
      expect(result.health).toBe(healthAttributeValue.value);
      expect(result.industry).toBe(industryAttributeValue.value);
      expect(result.primaryDomain).toBe(primaryDomain);
      expect(result.secondaryDomain).toBe(secondaryDomain);
      expect(result.accountOwnerId).toBe(global.testUser1.uid);
      expect(result.accountOwner).toBe(global.testUser1.name);
      expect(result.accountOwnerEmail).toBe(global.testUser1.email);
      expect(result.annualRevenue).toBe(2000000);
      expect(result.employees).toBe(200);
      expect(result.website).toBe(website);
      expect(result.description).toBe(description);
      expect(result.billingAddress).toBe(billingAddress);
      expect(result.shippingAddress).toBe(shippingAddress);
      expect(result.logo).toBe(logo);
    });

    it("should be able to record an activity that account is created on success", async () => {
      // Create a new test account
      const createAccountResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          name: "Test Account for activity check",
          primaryDomain: faker.internet.domainName(),
          accountOwnerId: global.testUser.uid,
          source: "manual",
        },
      });

      const createdAccount = createAccountResponse.json()
        .data as AccountResponseDto;

      // Create a new attribute value
      const createAttributeValueResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "TRIAL",
          attribute: AccountAttributeType.ACCOUNT_STATUS,
          isDefault: false,
        },
      });

      const statusAttributeValue = createAttributeValueResponse.json()
        .data as AccountAttributeValueResponseDto;

      const newDomain = faker.internet.domainName();

      // update account
      const updateResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/${createdAccount.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          primaryDomain: newDomain,
          accountOwnerId: global.testUser1.uid,
          status: statusAttributeValue.id,
        },
      });

      expect(updateResponse.statusCode).toBe(HttpStatus.OK);

      const accountData = await connection.query(
        `SELECT * FROM "public"."accounts" WHERE uid = '${createdAccount.id}';`,
      );

      // Check activities
      const activitiesResponse = await connection.query(
        `SELECT * FROM "public"."audit_logs" WHERE organization_id = '${global.testOrganization.id}' AND entity_type = '${AuditLogEntityType.ACCOUNT}' AND entity_id = '${accountData[0].id}' AND operation = '${AuditLogOp.UPDATED}';`,
      );

      expect(activitiesResponse.length).toBe(1);
      expect(
        activitiesResponse[0].metadata.updatedFields.some(
          (m: AuditLogUpdatedField) => m.field === "primaryDomain",
        ),
      ).toBe(true);
      expect(
        activitiesResponse[0].metadata.updatedFields.some(
          (m: AuditLogUpdatedField) => m.field === "accountOwnerId",
        ),
      ).toBe(true);
      expect(
        activitiesResponse[0].metadata.updatedFields.some(
          (m: AuditLogUpdatedField) => m.field === "status",
        ),
      ).toBe(true);
    });

    it("should throw error when account does not exist", async () => {
      const updateResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: "/v1/accounts/non-existent-id",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          name: "Updated Account Name",
        },
      });

      expect(updateResponse.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(updateResponse.json().message).toBe("Account not found!");
    });

    it("should throw error when tried with duplicate primary domain", async () => {
      // Create account
      const createAccountResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          name: "Other Account",
          primaryDomain: faker.internet.domainName(),
          accountOwnerId: global.testUser.uid,
          source: "manual",
        },
      });

      const result = createAccountResponse.json().data as AccountResponseDto;

      // Create second account
      const createSecondAccountResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          name: "Other Account",
          primaryDomain: faker.internet.domainName(),
          accountOwnerId: global.testUser.uid,
          source: "manual",
        },
      });

      const secondResult = createSecondAccountResponse.json()
        .data as AccountResponseDto;

      // Try updating this new account with global test account's domain
      const updateResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/${result.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          primaryDomain: secondResult.primaryDomain,
        },
      });

      expect(updateResponse.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(updateResponse.json().message).toBe(
        "Primary domain is already in use by another account!",
      );
    });

    it("should throw error when status / classification attribute value is not a valid attribute value id", async () => {
      const updateAccountResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/${global.testAccount.uid}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          status: "invalid-status-id",
        },
      });

      expect(updateAccountResponse.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(updateAccountResponse.json().message).toBe(
        "Provided value for account_status is not valid!",
      );
    });

    it("should throw error when account owner id is not a valid user id", async () => {
      const updateAccountResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/${global.testAccount.uid}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountOwnerId: "invalid-user-id",
        },
      });

      expect(updateAccountResponse.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(updateAccountResponse.json().message).toBe(
        "Account owner not found!",
      );
    });

    it("should be able to update custom fields and return them in account details", async () => {
      const highOptionId = singleChoiceCustomField.data.options.find(
        (option) => option.value === "High",
      )?.id;

      const createAccountResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          name: "Test Account for custom field during update",
          primaryDomain: faker.internet.domainName(),
          accountOwnerId: global.testUser.uid,
          source: "manual",
          customFieldValues: [
            {
              customFieldId: textCustomField.data.uid,
              data: [{ value: "Test value" }],
              metadata: {},
            } as ExternalCustomFieldValuesDto,
            {
              customFieldId: singleChoiceCustomField.data.uid,
              data: [{ value: highOptionId }],
              metadata: {},
            } as ExternalCustomFieldValuesDto,
          ],
        } as CreateAccountDto,
      });
      expect(createAccountResponse.statusCode).toBe(HttpStatus.CREATED);

      const account = createAccountResponse.json().data as AccountResponseDto;
      expect(account.name).toBe("Test Account for custom field during update");
      expect(account.customFieldValues).toBeDefined();
      expect(account.customFieldValues?.length).toBe(2);
      expect(account.customFieldValues).toEqual([
        {
          customFieldId: textCustomField.data.uid,
          data: [{ value: "Test value" }],
          metadata: {},
        },
        {
          customFieldId: singleChoiceCustomField.data.uid,
          data: [{ value: highOptionId }],
          metadata: {},
        },
      ]);

      const updateResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/${account.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          customFieldValues: [
            {
              customFieldId: textCustomField.data.uid,
              data: [{ value: "Updated value" }],
              metadata: {},
            },
          ],
        },
      });

      expect(updateResponse.statusCode).toBe(HttpStatus.OK);
      const updatedAccount = updateResponse.json().data as AccountResponseDto;
      expect(updatedAccount.customFieldValues).toEqual([
        {
          customFieldId: singleChoiceCustomField.data.uid,
          data: [{ value: highOptionId }],
          metadata: {},
        },
        {
          customFieldId: textCustomField.data.uid,
          data: [{ value: "Updated value" }],
          metadata: {},
        },
      ]);
    });
  });

  describe("Delete Account", () => {
    it("should be able to delete account and record activity", async () => {
      // Create a new test account
      const createAccountResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          name: "Test Account for update",
          primaryDomain: faker.internet.domainName(),
          accountOwnerId: global.testUser.uid,
          source: "manual",
        },
      });

      const createdAccount = createAccountResponse.json()
        .data as AccountResponseDto;

      await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/${createdAccount.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      const deletedAccount = await connection.query(
        `SELECT * FROM "public"."accounts" WHERE uid = '${createdAccount.id}' AND is_active = false;`,
      );

      expect(deletedAccount.length).toBe(1);

      const deletedActivityResponse = await connection.query(
        `SELECT * FROM "public"."audit_logs" WHERE organization_id = '${global.testOrganization.id}' AND entity_type = '${AuditLogEntityType.ACCOUNT}' AND entity_id = '${deletedAccount[0].id}' AND operation = '${AuditLogOp.DELETED}';`,
      );

      expect(deletedActivityResponse.length).toBeGreaterThanOrEqual(1);
    });

    it("should throw error when account does not exist", async () => {
      const deleteResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/non-existent-id`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(deleteResponse.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(deleteResponse.json().message).toBe("Account not found!");
    });
  });

  describe("Delete account attributes", () => {
    it("should be able to delete unused attribute value", async () => {
      const createAccountClassificationResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "TO BE DELETED",
          attribute: AccountAttributeType.ACCOUNT_CLASSIFICATION,
          isDefault: false,
        },
      });

      const accountClassification = createAccountClassificationResponse.json()
        .data as AccountAttributeValueResponseDto;

      const createAccountStatusResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "TO BE DELETED",
          attribute: AccountAttributeType.ACCOUNT_STATUS,
          isDefault: false,
        },
      });

      const accountStatus = createAccountStatusResponse.json()
        .data as AccountAttributeValueResponseDto;

      const accountClassificationDeleteResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/attributes/${accountClassification.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(accountClassificationDeleteResponse.statusCode).toBe(
        HttpStatus.NO_CONTENT,
      );

      // Delete the value
      const accountStatusDeleteResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/attributes/${accountStatus.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(accountStatusDeleteResponse.statusCode).toBe(
        HttpStatus.NO_CONTENT,
      );
    });

    it("should throw an error when trying to delete attribute value that is in use", async () => {
      // create new classification
      const createAccountClassificationResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "TO BE DELETED WITHOUT FORCE DELETE",
          attribute: AccountAttributeType.ACCOUNT_CLASSIFICATION,
          isDefault: false,
        },
      });

      const accountClassification = createAccountClassificationResponse.json()
        .data as AccountAttributeValueResponseDto;

      // create new status
      const createAccountStatusResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "TO BE DELETED WITHOUT FORCE DELETE",
          attribute: AccountAttributeType.ACCOUNT_STATUS,
          isDefault: false,
        },
      });

      const accountStatus = createAccountStatusResponse.json()
        .data as AccountAttributeValueResponseDto;

      // create new health
      const createAccountHealthResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "TO BE DELETED WITHOUT FORCE DELETE",
          attribute: AccountAttributeType.ACCOUNT_HEALTH,
          isDefault: false,
        },
      });

      const accountHealth = createAccountHealthResponse.json()
        .data as AccountAttributeValueResponseDto;

      // create account with classification and status
      const createAccountResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          name: "Test Account for delete attribute value",
          primaryDomain: faker.internet.domainName(),
          accountOwnerId: global.testUser.uid,
          source: "manual",
          classification: accountClassification.id,
          status: accountStatus.id,
          health: accountHealth.id,
        },
      });
      expect(createAccountResponse.statusCode).toBe(HttpStatus.CREATED);
      const account = createAccountResponse.json().data as AccountResponseDto;

      // Delete classification
      const accountClassificationDeleteResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/attributes/${accountClassification.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(accountClassificationDeleteResponse.statusCode).toBe(
        HttpStatus.BAD_REQUEST,
      );
      expect(accountClassificationDeleteResponse.json().message).toBe(
        "Cannot delete attribute value. It is in use.",
      );

      // Delete status
      const accountStatusDeleteResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/attributes/${accountStatus.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(accountStatusDeleteResponse.statusCode).toBe(
        HttpStatus.BAD_REQUEST,
      );
      expect(accountStatusDeleteResponse.json().message).toBe(
        "Cannot delete attribute value. It is in use.",
      );

      // Delete health
      const accountHealthDeleteResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/attributes/${accountHealth.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(accountHealthDeleteResponse.statusCode).toBe(
        HttpStatus.BAD_REQUEST,
      );
      expect(accountHealthDeleteResponse.json().message).toBe(
        "Cannot delete attribute value. It is in use.",
      );

      // check if account details are still correct
      const accountResponse = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/accounts/${account.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(accountResponse.statusCode).toBe(HttpStatus.OK);
      const accountDetails = accountResponse.json().data as AccountResponseDto;
      expect(accountDetails.classificationId).toBe(accountClassification.id);
      expect(accountDetails.statusId).toBe(accountStatus.id);
    });

    it("should be able to delete attribute value with force delete flag", async () => {
      // create new classification
      const createAccountClassificationResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "TO BE DELETED WITH FORCE DELETE",
          attribute: AccountAttributeType.ACCOUNT_CLASSIFICATION,
          isDefault: false,
        },
      });

      const accountClassification = createAccountClassificationResponse.json()
        .data as AccountAttributeValueResponseDto;

      // create new status
      const createAccountStatusResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "TO BE DELETED WITH FORCE DELETE",
          attribute: AccountAttributeType.ACCOUNT_STATUS,
          isDefault: false,
        },
      });

      const accountStatus = createAccountStatusResponse.json()
        .data as AccountAttributeValueResponseDto;

      // create new health
      const createAccountHealthResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "TO BE DELETED WITH FORCE DELETE",
          attribute: AccountAttributeType.ACCOUNT_HEALTH,
          isDefault: false,
        },
      });

      const accountHealth = createAccountHealthResponse.json()
        .data as AccountAttributeValueResponseDto;

      // create default classification
      const createDefaultAccountClassificationResponse = await injectWithOrgId(
        app,
        {
          method: "POST",
          url: "/v1/accounts/attributes",
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            value: "DEFAULT CLASSIFICATION",
            attribute: AccountAttributeType.ACCOUNT_CLASSIFICATION,
            isDefault: true,
          },
        },
      );

      const defaultAccountClassification =
        createDefaultAccountClassificationResponse.json()
          .data as AccountAttributeValueResponseDto;

      // create default status
      const createDefaultAccountStatusResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "DEFAULT STATUS",
          attribute: AccountAttributeType.ACCOUNT_STATUS,
          isDefault: true,
        },
      });

      const defaultAccountStatus = createDefaultAccountStatusResponse.json()
        .data as AccountAttributeValueResponseDto;

      // create default health
      const createDefaultAccountHealthResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "DEFAULT HEALTH",
          attribute: AccountAttributeType.ACCOUNT_HEALTH,
          isDefault: true,
        },
      });

      const defaultAccountHealth = createDefaultAccountHealthResponse.json()
        .data as AccountAttributeValueResponseDto;

      // create account with classification and status
      const createAccountResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          name: "Test Account for delete attribute value",
          primaryDomain: faker.internet.domainName(),
          source: "manual",
          accountOwnerId: global.testUser.uid,
          classification: accountClassification.id,
          status: accountStatus.id,
          health: accountHealth.id,
        },
      });
      expect(createAccountResponse.statusCode).toBe(HttpStatus.CREATED);
      const account = createAccountResponse.json().data as AccountResponseDto;

      // Delete classification
      const accountClassificationDeleteResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/attributes/${accountClassification.id}?forceDelete=true`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(accountClassificationDeleteResponse.statusCode).toBe(
        HttpStatus.NO_CONTENT,
      );

      // Delete status
      const accountStatusDeleteResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/attributes/${accountStatus.id}?forceDelete=true`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(accountStatusDeleteResponse.statusCode).toBe(
        HttpStatus.NO_CONTENT,
      );

      // Delete health
      const accountHealthDeleteResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/attributes/${accountHealth.id}?forceDelete=true`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(accountHealthDeleteResponse.statusCode).toBe(
        HttpStatus.NO_CONTENT,
      );

      await sleep(500);

      // check if account details are still correct
      const accountResponse = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/accounts/${account.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(accountResponse.statusCode).toBe(HttpStatus.OK);
      const accountDetails = accountResponse.json().data as AccountResponseDto;
      expect(accountDetails.classificationId).toBe(
        defaultAccountClassification.id,
      );
      expect(accountDetails.statusId).toBe(defaultAccountStatus.id);
      expect(accountDetails.healthId).toBe(defaultAccountHealth.id);
    });
  });

  describe("Find All Accounts", () => {
    let accounts: AccountResponseDto[] = [];

    beforeAll(async () => {
      // Create multiple accounts with different attributes for testing
      const createAccountPromises = Array(5)
        .fill(null)
        .map(async (_, index) => {
          const response = await injectWithOrgId(app, {
            method: "POST",
            url: "/v1/accounts",
            headers: {
              Authorization: `Bearer ${userAuthToken}`,
            },
            payload: {
              name: `Test Account ${index + 1}`,
              primaryDomain: faker.internet.domainName(),
              accountOwnerId: global.testUser.uid,
              status: global.testAccountStatus.uid,
              classification: global.testAccountClassification.uid,
              health: global.testAccountHealth.uid,
              source: "manual",
            },
          });
          return response.json().data;
        });

      accounts = await Promise.all(createAccountPromises);
    });

    it("should respect pagination limit", async () => {
      const limit = 2;
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/accounts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        query: {
          limit: limit.toString(),
        },
      });

      const data = response.json().data;
      expect(response.statusCode).toBe(200);
      expect(data.length).toBe(limit);
    });

    it("should respect pagination offset", async () => {
      const limit = 2;
      const page = 1;
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/accounts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        query: {
          limit: limit.toString(),
          page: page.toString(),
        },
      });

      const data = response.json().data.results;
      expect(response.statusCode).toBe(200);
      expect(data.length).toBe(limit);
      expect(data[0].id).not.toBe(accounts[0].id); // Should be different from first page
    });

    it("should filter by source", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/accounts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        query: {
          source: "manual",
        },
      });

      const data = response.json().data.results;
      expect(response.statusCode).toBe(200);
      data.forEach((account) => {
        expect(account.source).toBe("manual");
      });
    });

    it("should filter by accountOwnerId", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/accounts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        query: {
          accountOwnerId: global.testUser.uid,
        },
      });

      const data = response.json().data.results;
      expect(response.statusCode).toBe(200);
      data.forEach((account) => {
        expect(account.accountOwnerId).toBe(global.testUser.uid);
      });
    });

    it("should filter by status", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/accounts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        query: {
          status: global.testAccountStatus.uid,
        },
      });

      const data = response.json().data.results;
      expect(response.statusCode).toBe(200);
      data.forEach((account) => {
        expect(account.statusId).toBe(global.testAccountStatus.uid);
      });
    });

    it("should filter by classification", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/accounts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        query: {
          classification: global.testAccountClassification.uid,
        },
      });

      const data = response.json().data.results;
      expect(response.statusCode).toBe(200);
      data.forEach((account) => {
        expect(account.classificationId).toBe(
          global.testAccountClassification.uid,
        );
      });
    });

    it("should filter by health", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/accounts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        query: {
          health: global.testAccountHealth.value,
        },
      });

      const data = response.json().data.results;
      expect(response.statusCode).toBe(200);
      data.forEach((account) => {
        expect(account.healthId).toBe(global.testAccountHealth.uid);
      });
    });

    it("should combine multiple filters", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/accounts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        query: {
          accountOwnerId: global.testUser.uid,
          status: global.testAccountStatus.uid,
          classification: global.testAccountClassification.uid,
          health: global.testAccountHealth.uid,
          limit: "10",
        },
      });

      const data = response.json().data.results;
      expect(response.statusCode).toBe(200);
      data.forEach((account) => {
        expect(account.accountOwnerId).toBe(global.testUser.uid);
        expect(account.statusId).toBe(global.testAccountStatus.uid);
        expect(account.classificationId).toBe(
          global.testAccountClassification.uid,
        );
        expect(account.healthId).toBe(global.testAccountHealth.uid);
      });
    });

    afterAll(async () => {
      // Clean up created accounts
      for (const account of accounts) {
        await injectWithOrgId(app, {
          method: "DELETE",
          url: `/v1/accounts/${account.id}`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
        });
      }
    });
  });
});
