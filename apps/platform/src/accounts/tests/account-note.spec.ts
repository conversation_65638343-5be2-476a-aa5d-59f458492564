import { faker } from "@faker-js/faker";
import {
  HttpStatus,
  UnprocessableEntityException,
  ValidationPipe,
} from "@nestjs/common";
import {
  FastifyAdapter,
  NestFastifyApplication,
} from "@nestjs/platform-fastify";
import { Test } from "@nestjs/testing";
import {
  AccountAttributeType,
  AccountNoteVisibility,
  AuditLogEntityType,
  AuditLogOp,
  AuditLogUpdatedField,
} from "@repo/thena-platform-entities";
import { sleep, t__loginIntoAuthService } from "@repo/thena-shared-libs";
import { DataSource } from "typeorm";
import { AppModule } from "../../app.module";
import { injectWithOrgId } from "../../utils";
import { AccountAttributeValueResponseDto } from "../dtos/response/account-attribute-value.dto";
import { AccountNoteResponseDto } from "../dtos/response/account-note.dto";
import { AccountResponseDto } from "../dtos/response/account.dto";

describe("Account Notes", () => {
  let app: NestFastifyApplication;
  let connection: DataSource;
  let userAuthToken: string;
  let newUserToken: string;
  let noteTypeAttribute: AccountAttributeValueResponseDto;
  let testAccount: AccountResponseDto;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = module.createNestApplication<NestFastifyApplication>(
      new FastifyAdapter(),
    );

    app.useGlobalPipes(
      new ValidationPipe({
        transform: true,
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        exceptionFactory: (errors) => {
          return new UnprocessableEntityException(
            errors.map((error) => ({
              property: error.property,
              constraints: error.constraints,
            })),
          );
        },
      }),
    );

    await app.init();
    await app.getHttpAdapter().getInstance().ready();

    connection = app.get(DataSource);

    // Log in user to get auth token
    userAuthToken = await t__loginIntoAuthService(
      global.testUser.email,
      global.testUser.password,
    );

    newUserToken = await t__loginIntoAuthService(
      global.testUser4.email,
      global.testUser4.password,
    );

    // Create test account
    const createAccountResponse = await injectWithOrgId(app, {
      method: "POST",
      url: "/v1/accounts",
      headers: {
        Authorization: `Bearer ${userAuthToken}`,
      },
      payload: {
        name: "Test Account for account note tests",
        primaryDomain: faker.internet.domainName(),
        accountOwnerId: global.testUser.uid,
        source: "manual",
      },
    });
    testAccount = createAccountResponse.json().data;

    // Create note type attribute
    const createNoteTypeResponse = await injectWithOrgId(app, {
      method: "POST",
      url: "/v1/accounts/attributes",
      headers: {
        Authorization: `Bearer ${userAuthToken}`,
      },
      payload: {
        value: "GENERAL",
        attribute: AccountAttributeType.NOTE_TYPE,
        isDefault: false,
      },
    });
    noteTypeAttribute = createNoteTypeResponse.json().data;
  });

  afterAll(async () => {
    await app.close();
  });

  describe("Create Account Note", () => {
    it("should be able to create account note when all details are provided", async () => {
      const createAccountNoteResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/notes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          content: "Test note content",
          type: noteTypeAttribute.id,
          visibility: AccountNoteVisibility.ORGANIZATION,
        },
      });

      expect(createAccountNoteResponse.statusCode).toBe(HttpStatus.CREATED);
      const result = createAccountNoteResponse.json()
        .data as AccountNoteResponseDto;

      expect(result).toHaveProperty("id");
      expect(result.accountId).toBe(testAccount.id);
      expect(result.content).toBe("Test note content");
      expect(result.type).toBe(noteTypeAttribute.value);
      expect(result.typeId).toBe(noteTypeAttribute.id);
      expect(result.visibility).toBe(AccountNoteVisibility.ORGANIZATION);
      expect(result.authorId).toBe(global.testUser.uid);
    });

    it("should be able to use default note type if not provided", async () => {
      // Create default note type
      const createDefaultNoteTypeResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "DEFAULT_NOTE",
          attribute: AccountAttributeType.NOTE_TYPE,
          isDefault: true,
        },
      });

      const defaultNoteType = createDefaultNoteTypeResponse.json().data;

      const createAccountNoteResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/notes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          content: "Test note content",
        },
      });

      expect(createAccountNoteResponse.statusCode).toBe(HttpStatus.CREATED);
      const result = createAccountNoteResponse.json()
        .data as AccountNoteResponseDto;

      expect(result.type).toBe(defaultNoteType.value);
      expect(result.typeId).toBe(defaultNoteType.id);
      expect(result.visibility).toBe(AccountNoteVisibility.ORGANIZATION); // Default visibility
    });

    it("should throw error when account id is invalid", async () => {
      const createAccountNoteResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/notes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: "non-existent-account",
          content: "Test note content",
        },
      });

      expect(createAccountNoteResponse.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(createAccountNoteResponse.json().message).toBe(
        "Account not found",
      );
    });

    it("should record audit log post successful creation", async () => {
      const createAccountNoteResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/notes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          content: "Test note content",
        },
      });

      const note = createAccountNoteResponse.json()
        .data as AccountNoteResponseDto;

      // Get organization ID from database
      const orgData = await connection.query(
        `SELECT id FROM "public"."organization" WHERE id = '${global.testUser1.organization_id}';`,
      );

      const noteData = await connection.query(
        `SELECT * FROM "public"."account_notes" WHERE uid = '${note.id}';`,
      );

      // Check audit logs
      const auditLogs = await connection.query(
        `SELECT * FROM "public"."audit_logs" WHERE organization_id = '${orgData[0].id}' AND entity_type = '${AuditLogEntityType.ACCOUNT_NOTE}' AND entity_id = '${noteData[0].id}' AND operation = '${AuditLogOp.CREATED}';`,
      );

      expect(auditLogs.length).toBe(1);
    });

    it("should throw error when note type attribute is invalid", async () => {
      const createNoteResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/notes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          content: "Test note",
          type: "non-existent-type",
        },
      });

      expect(createNoteResponse.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(createNoteResponse.json().message).toBe("Note type not found");
    });

    it("should be able to attach files to a note", async () => {
      const createAccountNoteResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/notes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          content: "Test note content",
          attachmentUrls: [
            "https://thena-new-backend-test.s3.us-east-1.amazonaws.com/uploads/image-screenshots.png",
          ],
        },
      });
      expect(createAccountNoteResponse.statusCode).toBe(HttpStatus.CREATED);

      const note = createAccountNoteResponse.json()
        .data as AccountNoteResponseDto;

      expect(note.attachments.length).toBe(1);
    });
  });

  describe("Update Account Note", () => {
    it("should be able to update content, type, and visibility", async () => {
      // Create a note first
      const createAccountNoteResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/notes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          content: "Original content",
        },
      });

      const note = createAccountNoteResponse.json()
        .data as AccountNoteResponseDto;

      // Create new note type for update
      const createNewNoteTypeResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "UPDATED_NOTE_TYPE",
          attribute: AccountAttributeType.NOTE_TYPE,
          isDefault: false,
        },
      });

      const newNoteType = createNewNoteTypeResponse.json().data;

      // Update the note
      const updateAccountNoteResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/notes/${note.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          content: "Updated content",
          type: newNoteType.id,
          visibility: AccountNoteVisibility.PRIVATE,
        },
      });

      expect(updateAccountNoteResponse.statusCode).toBe(HttpStatus.OK);
      const result = updateAccountNoteResponse.json()
        .data as AccountNoteResponseDto;

      expect(result.content).toBe("Updated content");
      expect(result.type).toBe(newNoteType.value);
      expect(result.typeId).toBe(newNoteType.id);
      expect(result.visibility).toBe(AccountNoteVisibility.PRIVATE);
    });

    it("should throw error when note is invalid", async () => {
      const updateAccountNoteResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: "/v1/accounts/notes/non-existent-note",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          content: "Updated content",
        },
      });

      expect(updateAccountNoteResponse.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(updateAccountNoteResponse.json().message).toBe(
        "Account note not found",
      );
    });

    it("should throw error when note type is invalid", async () => {
      // Create a note first
      const createAccountNoteResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/notes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          content: "Test content",
        },
      });

      const note = createAccountNoteResponse.json()
        .data as AccountNoteResponseDto;

      const updateAccountNoteResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/notes/${note.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          type: "non-existent-type",
        },
      });

      expect(updateAccountNoteResponse.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(updateAccountNoteResponse.json().message).toBe(
        "Note type not found",
      );
    });

    it("should record audit log with updated fields", async () => {
      // Create a note first
      const createAccountNoteResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/notes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          content: "Original content",
        },
      });

      const note = createAccountNoteResponse.json()
        .data as AccountNoteResponseDto;

      // Update the note
      const updateAccountNoteResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/notes/${note.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          content: "Updated content",
          visibility: AccountNoteVisibility.PRIVATE,
        },
      });

      expect(updateAccountNoteResponse.statusCode).toBe(HttpStatus.OK);

      // Get organization ID from database
      const orgData = await connection.query(
        `SELECT id FROM "public"."organization" WHERE id = '${global.testUser1.organization_id}';`,
      );

      const noteData = await connection.query(
        `SELECT * FROM "public"."account_notes" WHERE uid = '${note.id}';`,
      );

      // Check audit logs
      const auditLogs = await connection.query(
        `SELECT * FROM "public"."audit_logs" WHERE organization_id = '${orgData[0].id}' AND entity_type = '${AuditLogEntityType.ACCOUNT_NOTE}' AND entity_id = '${noteData[0].id}' AND operation = '${AuditLogOp.UPDATED}';`,
      );

      expect(auditLogs.length).toBe(1);
      expect(
        auditLogs[0].metadata.updatedFields.some(
          (m: AuditLogUpdatedField) => m.field === "content",
        ),
      ).toBe(true);
      expect(
        auditLogs[0].metadata.updatedFields.some(
          (m: AuditLogUpdatedField) => m.field === "visibility",
        ),
      ).toBe(true);
    });

    it("should throw error when non author is trying to update a private note", async () => {
      // Create a note with private visibility
      const createNoteResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/notes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          content: "Private note",
          visibility: AccountNoteVisibility.PRIVATE,
        },
      });

      expect(createNoteResponse.statusCode).toBe(HttpStatus.CREATED);
      const note = createNoteResponse.json().data;

      // Update note to private visibility
      const updateNoteResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/notes/${note.id}`,
        headers: {
          Authorization: `Bearer ${newUserToken}`,
        },
        payload: {
          content: "Updated content",
        },
      });

      expect(updateNoteResponse.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(updateNoteResponse.json().message).toBe("Account note not found");
    });

    it("should throw error when non author is trying to update a public note to private", async () => {
      // Create a note with organization visibility
      const createNoteResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/notes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          content: "Original note",
          visibility: AccountNoteVisibility.ORGANIZATION,
        },
      });

      expect(createNoteResponse.statusCode).toBe(HttpStatus.CREATED);
      const note = createNoteResponse.json().data;

      // Try to update note to private visibility
      const updateNoteResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/notes/${note.id}`,
        headers: {
          Authorization: `Bearer ${newUserToken}`,
        },
        payload: {
          visibility: AccountNoteVisibility.PRIVATE,
        },
      });

      expect(updateNoteResponse.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(updateNoteResponse.json().message).toBe(
        "Only the author can make a note private",
      );
    });

    it("should throw error when updating with invalid note type attribute", async () => {
      // Create a note first
      const createNoteResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/notes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          content: "Test note",
        },
      });

      const note = createNoteResponse.json().data;

      // Try to update with invalid type
      const updateNoteResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/notes/${note.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          type: "non-existent-type",
        },
      });

      expect(updateNoteResponse.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(updateNoteResponse.json().message).toBe("Note type not found");
    });

    it("should be able to attach files to a note", async () => {
      const createAccountNoteWithoutAttachmentsResponse = await injectWithOrgId(
        app,
        {
          method: "POST",
          url: "/v1/accounts/notes",
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            accountId: testAccount.id,
            content: "Test note content",
          },
        },
      );
      expect(createAccountNoteWithoutAttachmentsResponse.statusCode).toBe(
        HttpStatus.CREATED,
      );

      const noteWithoutAttachments =
        createAccountNoteWithoutAttachmentsResponse.json()
          .data as AccountNoteResponseDto;

      const createAccountNoteResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/notes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          content: "Test note content",
          attachmentUrls: [
            "https://thena-new-backend-test.s3.us-east-1.amazonaws.com/uploads/image-screenshots.png",
          ],
        },
      });
      expect(createAccountNoteResponse.statusCode).toBe(HttpStatus.CREATED);

      const note = createAccountNoteResponse.json()
        .data as AccountNoteResponseDto;

      expect(note.attachments.length).toBe(1);

      const updateAccountNoteWithoutAttachmentsResponse = await injectWithOrgId(
        app,
        {
          method: "PUT",
          url: `/v1/accounts/notes/${noteWithoutAttachments.id}`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            attachmentUrls: [
              "https://thena-new-backend-test.s3.us-east-1.amazonaws.com/uploads/checking/Screenshot+2025-01-13+at+11.01.34%E2%80%AFPM.png",
            ],
          },
        },
      );

      const updatedNoteWithoutAttachments =
        updateAccountNoteWithoutAttachmentsResponse.json()
          .data as AccountNoteResponseDto;

      expect(updatedNoteWithoutAttachments.attachments.length).toBe(1);

      const updateAccountNoteResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/notes/${note.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          attachmentUrls: [
            "https://thena-new-backend-test.s3.us-east-1.amazonaws.com/uploads/checking/Screenshot+2025-01-13+at+11.01.34%E2%80%AFPM.png",
          ],
        },
      });

      const updatedNote = updateAccountNoteResponse.json()
        .data as AccountNoteResponseDto;

      expect(updatedNote.attachments.length).toBe(2);
    });
  });

  describe("Delete Account Note", () => {
    it("should be able to delete an existing note", async () => {
      // Create a note first
      const createAccountNoteResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/notes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          content: "Note to be deleted",
        },
      });

      const note = createAccountNoteResponse.json()
        .data as AccountNoteResponseDto;

      // Delete the note
      const deleteAccountNoteResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/notes/${note.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(deleteAccountNoteResponse.statusCode).toBe(HttpStatus.NO_CONTENT);

      // Verify note is marked as inactive
      const noteData = await connection.query(
        `SELECT * FROM "public"."account_notes" WHERE uid = '${note.id}' AND is_active = false;`,
      );

      expect(noteData.length).toBe(1);
    });

    it("should throw error when note is non existent or already deleted", async () => {
      // Try deleting non-existent note
      const deleteNonExistentResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: "/v1/accounts/notes/non-existent-note",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(deleteNonExistentResponse.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(deleteNonExistentResponse.json().message).toBe(
        "Account note not found",
      );

      // Create and delete a note
      const createAccountNoteResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/notes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          content: "Note to be deleted",
        },
      });

      const note = createAccountNoteResponse.json()
        .data as AccountNoteResponseDto;

      // Delete the note
      await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/notes/${note.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      // Try deleting again
      const deleteAgainResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/notes/${note.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(deleteAgainResponse.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(deleteAgainResponse.json().message).toBe("Account note not found");
    });

    it("should record audit log on successful deletion", async () => {
      // Create a note first
      const createAccountNoteResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/notes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          content: "Note to be deleted",
        },
      });

      const note = createAccountNoteResponse.json()
        .data as AccountNoteResponseDto;

      // Delete the note
      await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/notes/${note.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      // Get organization ID from database
      const orgData = await connection.query(
        `SELECT id FROM "public"."organization" WHERE id = '${global.testUser1.organization_id}';`,
      );

      const noteData = await connection.query(
        `SELECT * FROM "public"."account_notes" WHERE uid = '${note.id}';`,
      );

      // Check audit logs
      const auditLogs = await connection.query(
        `SELECT * FROM "public"."audit_logs" WHERE organization_id = '${orgData[0].id}' AND entity_type = '${AuditLogEntityType.ACCOUNT_NOTE}' AND entity_id = '${noteData[0].id}' AND operation = '${AuditLogOp.DELETED}';`,
      );

      expect(auditLogs.length).toBe(1);
    });
  });

  describe("Remove Attachment from Account Note", () => {
    it("should be able to remove an attachment from a note", async () => {
      const createAccountNoteResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/notes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          content: "Test note content",
          attachmentUrls: [
            "https://thena-new-backend-test.s3.us-east-1.amazonaws.com/uploads/image-screenshots.png",
            "https://thena-new-backend-test.s3.us-east-1.amazonaws.com/uploads/checking/Screenshot+2025-01-13+at+11.01.34%E2%80%AFPM.png",
          ],
        },
      });

      const note = createAccountNoteResponse.json()
        .data as AccountNoteResponseDto;

      expect(note.attachments.length).toBe(2);

      const removeAttachmentResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/notes/${note.id}/attachments/${note.attachments[0].id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(removeAttachmentResponse.statusCode).toBe(HttpStatus.NO_CONTENT);

      const updatedNoteResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/notes/${note.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          content: "Updated note content",
        },
      });

      const updatedNote = updatedNoteResponse.json().data;
      expect(updatedNote.attachments.length).toBe(1);
    });
  });

  describe("Delete account note attributes", () => {
    it("should be able to delete unused attribute value", async () => {
      // create new note type
      const createNoteTypeResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "TO BE DELETED WITHOUT FORCE DELETE",
          attribute: AccountAttributeType.NOTE_TYPE,
          isDefault: false,
        },
      });

      const noteType = createNoteTypeResponse.json()
        .data as AccountAttributeValueResponseDto;

      // delete note type
      const noteTypeDeleteResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/attributes/${noteType.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(noteTypeDeleteResponse.statusCode).toBe(HttpStatus.NO_CONTENT);
    });

    it("should throw an error when trying to delete attribute value that is in use", async () => {
      // create new note type
      const createNoteTypeResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "TO BE DELETED WITHOUT FORCE DELETE",
          attribute: AccountAttributeType.NOTE_TYPE,
          isDefault: false,
        },
      });

      const noteType = createNoteTypeResponse.json()
        .data as AccountAttributeValueResponseDto;

      // create note with note type
      const createNoteResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/notes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          content: "Test note content",
          type: noteType.id,
        },
      });
      expect(createNoteResponse.statusCode).toBe(HttpStatus.CREATED);
      const note = createNoteResponse.json().data as AccountNoteResponseDto;

      // Delete note type
      const noteTypeDeleteResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/attributes/${noteType.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(noteTypeDeleteResponse.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(noteTypeDeleteResponse.json().message).toBe(
        "Cannot delete attribute value. It is in use.",
      );

      // check if account details are still correct
      const updatedNoteResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/notes/${note.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          content: "Updated note content",
        },
      });

      const updatedNote = updatedNoteResponse.json().data;
      expect(updatedNote.typeId).toBe(noteType.id);
    });

    it("should be able to delete attribute value with force delete flag", async () => {
      // create new note type
      const createNoteTypeResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "TO BE DELETED WITHOUT FORCE DELETE",
          attribute: AccountAttributeType.NOTE_TYPE,
          isDefault: false,
        },
      });

      const noteType = createNoteTypeResponse.json()
        .data as AccountAttributeValueResponseDto;

      // create default note type
      const createDefaultNoteTypeResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "DEFAULT NOTE TYPE",
          attribute: AccountAttributeType.NOTE_TYPE,
          isDefault: true,
        },
      });

      const defaultNoteType = createDefaultNoteTypeResponse.json()
        .data as AccountAttributeValueResponseDto;

      // create note with note type
      const createNoteResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/notes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          content: "Test note content",
          type: noteType.id,
        },
      });
      expect(createNoteResponse.statusCode).toBe(HttpStatus.CREATED);
      const note = createNoteResponse.json().data as AccountNoteResponseDto;

      // Delete note type
      const noteTypeDeleteResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/attributes/${noteType.id}?forceDelete=true`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });
      expect(noteTypeDeleteResponse.statusCode).toBe(HttpStatus.NO_CONTENT);

      await sleep(500);

      // check if account details are still correct
      const updatedNoteResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/notes/${note.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          content: "Updated note content",
        },
      });

      const updatedNote = updatedNoteResponse.json().data;
      expect(updatedNote.typeId).toBe(defaultNoteType.id);
    });
  });

  describe("Find All Account Notes", () => {
    let notes: AccountNoteResponseDto[] = [];

    beforeAll(async () => {
      // Create multiple notes with different attributes for testing
      const createNotePromises = Array(5)
        .fill(null)
        .map(async () => {
          const response = await injectWithOrgId(app, {
            method: "POST",
            url: "/v1/accounts/notes",
            headers: {
              Authorization: `Bearer ${userAuthToken}`,
            },
            payload: {
              accountId: testAccount.id,
              content: "Test note content",
              type: noteTypeAttribute.id,
              visibility: AccountNoteVisibility.ORGANIZATION,
            },
          });
          return response.json().data.results;
        });

      notes = await Promise.all(createNotePromises);
    });

    it("should respect pagination limit", async () => {
      const limit = 2;
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/accounts/notes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        query: {
          limit: limit.toString(),
        },
      });

      const data = response.json().data.results;
      expect(response.statusCode).toBe(200);
      expect(data.length).toBe(limit);
    });

    it("should respect pagination offset", async () => {
      const limit = 2;
      const page = 1;
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/accounts/notes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        query: {
          limit: limit.toString(),
          page: page.toString(),
        },
      });

      const data = response.json().data.results;
      expect(response.statusCode).toBe(200);
      expect(data.length).toBe(limit);
      expect(data[0].id).not.toBe(notes[0].id); // Should be different from first page
    });

    it("should filter by accountId", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/accounts/notes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        query: {
          accountId: testAccount.id,
        },
      });

      const data = response.json().data.results;
      expect(response.statusCode).toBe(200);
      data.forEach((note) => {
        expect(note.accountId).toBe(testAccount.id);
      });
    });

    it("should filter by noteType", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/accounts/notes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        query: {
          type: noteTypeAttribute.id,
        },
      });

      const data = response.json().data.results;
      expect(response.statusCode).toBe(200);
      data.forEach((note) => {
        expect(note.typeId).toBe(noteTypeAttribute.id);
      });
    });

    it("should combine multiple filters", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/accounts/notes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        query: {
          accountId: testAccount.id,
          type: noteTypeAttribute.id,
          limit: "10",
        },
      });

      const data = response.json().data.results;
      expect(response.statusCode).toBe(200);
      data.forEach((note) => {
        expect(note.accountId).toBe(testAccount.id);
        expect(note.typeId).toBe(noteTypeAttribute.id);
      });
    });

    afterAll(async () => {
      // Clean up created notes
      for (const note of notes) {
        await injectWithOrgId(app, {
          method: "DELETE",
          url: `/v1/accounts/notes/${note.id}`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
        });
      }
    });
  });
});
