import { faker } from "@faker-js/faker";
import {
  HttpStatus,
  UnprocessableEntityException,
  ValidationPipe,
} from "@nestjs/common";
import {
  FastifyAdapter,
  NestFastifyApplication,
} from "@nestjs/platform-fastify";
import { Test } from "@nestjs/testing";
import { AuditLogEntityType, AuditLogOp } from "@repo/thena-platform-entities";
import { sleep, t__loginIntoAuthService } from "@repo/thena-shared-libs";
import { DataSource } from "typeorm";
import { AppModule } from "../../app.module";
import { injectWithOrgId } from "../../utils";
import { AccountRelationshipResponseDto } from "../dtos/response/account-relationship.dto";
import { AccountResponseDto } from "../dtos/response/account.dto";

describe("Account Relationships", () => {
  let app: NestFastifyApplication;
  let connection: DataSource;
  let userAuthToken: string;
  let testAccount: AccountResponseDto;
  let relationshipType: AccountRelationshipResponseDto;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = module.createNestApplication<NestFastifyApplication>(
      new FastifyAdapter(),
    );

    app.useGlobalPipes(
      new ValidationPipe({
        transform: true,
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        exceptionFactory: (errors) => {
          return new UnprocessableEntityException(
            errors.map((error) => ({
              property: error.property,
              constraints: error.constraints,
            })),
          );
        },
      }),
    );

    await app.init();
    await app.getHttpAdapter().getInstance().ready();

    connection = app.get(DataSource);

    // Log the user in to get the auth token
    userAuthToken = await t__loginIntoAuthService(
      global.testUser.email,
      global.testUser.password,
    );

    // Create test account
    const createAccountResponse = await injectWithOrgId(app, {
      method: "POST",
      url: "/v1/accounts",
      headers: {
        Authorization: `Bearer ${userAuthToken}`,
      },
      payload: {
        name: "Test Account for relationship tests",
        primaryDomain: faker.internet.domainName(),
        accountOwnerId: global.testUser.uid,
        source: "manual",
      },
    });
    testAccount = createAccountResponse.json().data;

    // Create relationship type
    const createRelationshipTypeResponse = await injectWithOrgId(app, {
      method: "POST",
      url: "/v1/accounts/relationships/types",
      headers: {
        Authorization: `Bearer ${userAuthToken}`,
      },
      payload: {
        name: "Test Relationship Type",
      },
    });
    relationshipType = createRelationshipTypeResponse.json().data;
  });

  afterAll(async () => {
    await app.close();
  });

  describe("Relationship Types", () => {
    describe("Create Relationship Type", () => {
      it("should create a relationship type", async () => {
        const createResponse = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships/types`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            name: "Parent",
          },
        });

        expect(createResponse.statusCode).toBe(HttpStatus.CREATED);
        const result = createResponse.json().data;
        expect(result.name).toBe("Parent");
      });

      it("should create relationship type with inverse relationship", async () => {
        // Create first type
        const parentResponse = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships/types`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            name: "Parent",
          },
        });

        const parentType = parentResponse.json().data;

        // Create second type with inverse
        const childResponse = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships/types`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            name: "Child",
            inverseRelationshipId: parentType.id,
          },
        });

        expect(childResponse.statusCode).toBe(HttpStatus.CREATED);
        const childType = childResponse.json().data;
        expect(childType.inverseRelationshipId).toBe(parentType.id);
      });

      it("should record audit log on creation", async () => {
        const createResponse = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships/types`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            name: "Audit Test Type",
          },
        });

        const typeResponse = createResponse.json().data;

        const type = await connection.query(
          `SELECT * FROM "public"."account_relationship_types" WHERE uid = '${typeResponse.id}'`,
        );

        const auditLogs = await connection.query(
          `SELECT * FROM "public"."audit_logs" WHERE entity_type = '${AuditLogEntityType.ACCOUNT_RELATIONSHIP_TYPE}' AND entity_id = '${type[0].id}' AND operation = '${AuditLogOp.CREATED}';`,
        );

        expect(auditLogs.length).toBe(1);
      });

      it("should not allow creating inverse relationship if target type already has inverse", async () => {
        // Create first pair of inverse relationships
        const type1Response = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships/types`,
          headers: { Authorization: `Bearer ${userAuthToken}` },
          payload: { name: "Type 1" },
        });
        const type1 = type1Response.json().data;

        await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships/types`,
          headers: { Authorization: `Bearer ${userAuthToken}` },
          payload: {
            name: "Type 2",
            inverseRelationshipId: type1.id,
          },
        });

        // Try to create another type with type1 as inverse
        const type3Response = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships/types`,
          headers: { Authorization: `Bearer ${userAuthToken}` },
          payload: {
            name: "Type 3",
            inverseRelationshipId: type1.id,
          },
        });

        expect(type3Response.statusCode).toBe(HttpStatus.BAD_REQUEST);
        expect(type3Response.json().message).toBe(
          `Relationship type ${type1.name} already has an inverse relationship`,
        );
      });

      it("should be able to create default seeds on the account relationship type table when organization is created", async () => {
        const createOrganizationResponse = await injectWithOrgId(app, {
          method: "POST",
          url: "/v1/organizations",
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            name: "Test Organization for account relationship type seeds",
            email: `${faker.internet.email()}`,
            password: "test-password",
          },
        });

        const organization = createOrganizationResponse.json().data;

        const organizationData = await connection.query(
          `SELECT * FROM "public"."organization" WHERE id = '${organization.id}';`,
        );

        await sleep(1000);

        const relationshipTypes = await connection.query(
          `SELECT * FROM "public"."account_relationship_types" WHERE organization_id = '${organizationData[0].id}' AND is_active = true;`,
        );

        expect(relationshipTypes.length).toBe(2);
      });
    });

    describe("Update Relationship Type", () => {
      it("should update relationship type name", async () => {
        // Create type
        const createResponse = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships/types`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            name: "Original Name",
          },
        });

        const type = createResponse.json().data;

        // Update type
        const updateResponse = await injectWithOrgId(app, {
          method: "PUT",
          url: `/v1/accounts/relationships/types/${type.id}`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            name: "Updated Name",
          },
        });

        expect(updateResponse.statusCode).toBe(HttpStatus.OK);
        const result = updateResponse.json().data;
        expect(result.id).toBe(type.id);
        expect(result.name).toBe("Updated Name");
      });

      it("should update inverse relationship", async () => {
        // Create two types
        const firstResponse = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships/types`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            name: "First Type",
          },
        });

        const secondResponse = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships/types`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            name: "Second Type",
          },
        });

        const firstType = firstResponse.json().data;
        const secondType = secondResponse.json().data;

        // Update with inverse relationship
        const updateResponse = await injectWithOrgId(app, {
          method: "PUT",
          url: `/v1/accounts/relationships/types/${firstType.id}`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            inverseRelationshipId: secondType.id,
          },
        });

        expect(updateResponse.statusCode).toBe(HttpStatus.OK);
        const result = updateResponse.json().data;
        expect(result.inverseRelationshipId).toBe(secondType.id);
      });

      it("should handle removal of inverse relationship", async () => {
        // Create two types with inverse relationship
        const type1Response = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships/types`,
          headers: { Authorization: `Bearer ${userAuthToken}` },
          payload: { name: "Type 1" },
        });
        const type1 = type1Response.json().data;

        const type2Response = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships/types`,
          headers: { Authorization: `Bearer ${userAuthToken}` },
          payload: {
            name: "Type 2",
            inverseRelationshipId: type1.id,
          },
        });
        const type2 = type2Response.json().data;

        // Remove inverse relationship
        const updateResponse = await injectWithOrgId(app, {
          method: "PUT",
          url: `/v1/accounts/relationships/types/${type1.id}`,
          headers: { Authorization: `Bearer ${userAuthToken}` },
          payload: {
            inverseRelationshipId: null,
          },
        });

        expect(updateResponse.statusCode).toBe(HttpStatus.OK);
        const result = updateResponse.json().data;
        expect(result.inverseRelationship).toBeFalsy();

        // Verify type2's inverse is also removed using SQL query
        const type2AfterUpdate = await connection.query(
          `SELECT * FROM "public"."account_relationship_types" WHERE uid = $1 AND is_active = true`,
          [type2.id],
        );
        expect(type2AfterUpdate[0].inverse_relationship_id).toBeNull();
      });
    });

    describe("Delete Relationship Type", () => {
      it("should delete relationship type", async () => {
        // Create type
        const createResponse = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships/types`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            name: "Type To Delete",
          },
        });

        const type = createResponse.json().data;

        // Delete type
        const deleteResponse = await injectWithOrgId(app, {
          method: "DELETE",
          url: `/v1/accounts/relationships/types/${type.id}`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
        });

        expect(deleteResponse.statusCode).toBe(HttpStatus.NO_CONTENT);

        // Verify deletion
        const findResponse = await connection.query(
          `SELECT * FROM "public"."account_relationship_types" WHERE uid = '${type.id}' AND is_active = true`,
        );

        expect(findResponse.length).toBe(0);
      });

      it("should not delete type with active relationships", async () => {
        // Create type and relationship
        const typeResponse = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships/types`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            name: "Active Type",
          },
        });

        const type = typeResponse.json().data;

        await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            accountId: global.testAccount.uid,
            relatedAccountId: global.testAccount2.uid,
            relationshipType: type.id,
          },
        });

        // Try to delete type
        const deleteResponse = await injectWithOrgId(app, {
          method: "DELETE",
          url: `/v1/accounts/relationships/types/${type.id}`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
        });

        expect(deleteResponse.statusCode).toBe(HttpStatus.BAD_REQUEST);
        expect(deleteResponse.json().message).toBe(
          "Cannot delete relationship type with active relationships",
        );
      });
    });
  });

  describe("Relationships", () => {
    describe("Create Relationship", () => {
      it("should create a relationship between accounts", async () => {
        // create accounts
        const accountResponse = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            name: "Test Account for create test",
            primaryDomain: "test-account-for-create-test.com",
            accountOwnerId: global.testUser.uid,
            source: "manual",
          },
        });

        const account = accountResponse.json().data;

        // Create relationship type first
        const typeResponse = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships/types`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            name: "Parent",
          },
        });

        const type = typeResponse.json().data;

        // Create relationship
        const createResponse = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            accountId: global.testAccount.uid,
            relatedAccountId: account.id,
            relationshipType: type.id,
          },
        });

        expect(createResponse.statusCode).toBe(HttpStatus.CREATED);
        const result = createResponse.json().data;
        expect(result.accountId).toBe(global.testAccount.uid);
        expect(result.relatedAccountId).toBe(account.id);
        expect(result.relationshipType.id).toBe(type.id);
      });

      it("should create inverse relationship automatically", async () => {
        // create accounts
        const accountResponse = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            name: "Test Account for inverse test",
            primaryDomain: "test-account-for-inverse-test.com",
            accountOwnerId: global.testUser.uid,
            source: "manual",
          },
        });

        const account = accountResponse.json().data;

        // Create parent type
        const parentResponse = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships/types`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            name: "Parent Company",
          },
        });
        const parentType = parentResponse.json().data;

        // Create child type with inverse relationship
        const childResponse = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships/types`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            name: "Subsidiary",
            inverseRelationshipId: parentType.id,
          },
        });

        const childType = childResponse.json().data;

        // Create relationship
        await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            accountId: global.testAccount.uid,
            relatedAccountId: account.id,
            relationshipType: parentType.id,
          },
        });

        const accountData = await connection.query(
          `SELECT * FROM "public"."accounts" WHERE uid = '${account.id}'`,
        );

        // Check inverse relationship
        const inverseRelationships = await connection.query(
          `SELECT art.name as relationship_type_name FROM "public"."account_relationships" ar INNER JOIN "public"."account_relationship_types" art ON ar.relationship = art.id WHERE ar.account_id = '${accountData[0].id}' AND ar.related_account_id = '${global.testAccount.id}'`,
        );

        expect(inverseRelationships.length).toBe(1);
        expect(inverseRelationships[0].relationship_type_name).toBe(
          childType.name,
        );
      });

      it("should prevent cyclic relationships", async () => {
        // create accounts
        const account1Response = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            name: "Test Account for cyclic test 1",
            primaryDomain: "test-account-for-cyclic-test-1.com",
            accountOwnerId: global.testUser.uid,
            source: "manual",
          },
        });

        const account1 = account1Response.json().data;

        const account2Response = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            name: "Test Account for cyclic test 2",
            primaryDomain: "test-account-for-cyclic-test-2.com",
            accountOwnerId: global.testUser.uid,
            source: "manual",
          },
        });

        const account2 = account2Response.json().data;

        const account3Response = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            name: "Test Account for cyclic test 3",
            primaryDomain: "test-account-for-cyclic-test-3.com",
            accountOwnerId: global.testUser.uid,
            source: "manual",
          },
        });

        const account3 = account3Response.json().data;

        // Create relationship type
        const typeResponse = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships/types`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            name: "Parent",
          },
        });
        const type = typeResponse.json().data;

        await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships/types`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            name: "Child",
            inverseRelationshipId: type.id,
          },
        });

        // Get the actual account IDs from database for accurate relationship check
        const account1Data = await connection.query(
          `SELECT id FROM "public"."accounts" WHERE uid = $1`,
          [account1.id],
        );
        const account2Data = await connection.query(
          `SELECT id FROM "public"."accounts" WHERE uid = $1`,
          [account2.id],
        );
        const account3Data = await connection.query(
          `SELECT id FROM "public"."accounts" WHERE uid = $1`,
          [account3.id],
        );

        // Create first relationship: A -> B
        const firstRelationResponse = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships`,
          headers: { Authorization: `Bearer ${userAuthToken}` },
          payload: {
            accountId: account1.id,
            relatedAccountId: account2.id,
            relationshipType: type.id,
          },
        });
        expect(firstRelationResponse.statusCode).toBe(HttpStatus.CREATED);

        // Create second relationship: B -> C
        const secondRelationResponse = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships`,
          headers: { Authorization: `Bearer ${userAuthToken}` },
          payload: {
            accountId: account2.id,
            relatedAccountId: account3.id,
            relationshipType: type.id,
          },
        });
        expect(secondRelationResponse.statusCode).toBe(HttpStatus.CREATED);

        // Try to create cyclic relationship: C -> A
        const cyclicResponse = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships`,
          headers: { Authorization: `Bearer ${userAuthToken}` },
          payload: {
            accountId: account3.id,
            relatedAccountId: account1.id,
            relationshipType: type.id,
          },
        });

        expect(cyclicResponse.statusCode).toBe(HttpStatus.BAD_REQUEST);
        expect(cyclicResponse.json().message).toBe(
          "Cyclic relationship detected",
        );

        // Verify only two relationships exist
        const relationships = await connection.query(
          `SELECT ar.* 
           FROM "public"."account_relationships" ar
           WHERE ar.is_active = true 
           AND ar.account_id IN ($1, $2, $3)
           AND ar.related_account_id IN ($1, $2, $3)`,
          [account1Data[0].id, account2Data[0].id, account3Data[0].id],
        );
        expect(relationships.length).toBe(4); // A -> B, B -> A, B -> C, C -> B
      });

      it("should allow creating two way relationships even if its not of inverse type", async () => {
        // create accounts
        const account1Response = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            name: "Test Account for cyclic test without inverse 1",
            primaryDomain: "test-account-for-cyclic-test-without-inverse-1.com",
            accountOwnerId: global.testUser.uid,
            source: "manual",
          },
        });

        const account1 = account1Response.json().data;

        const account2Response = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            name: "Test Account for cyclic test without inverse 2",
            primaryDomain: "test-account-for-cyclic-test-without-inverse-2.com",
            accountOwnerId: global.testUser.uid,
            source: "manual",
          },
        });

        const account2 = account2Response.json().data;

        // Create relationship type
        const typeResponse = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships/types`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            name: "Partner",
          },
        });
        const type = typeResponse.json().data;

        // Create first relationship: A -> B
        const firstRelationResponse = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships`,
          headers: { Authorization: `Bearer ${userAuthToken}` },
          payload: {
            accountId: account1.id,
            relatedAccountId: account2.id,
            relationshipType: type.id,
          },
        });
        expect(firstRelationResponse.statusCode).toBe(HttpStatus.CREATED);

        // Create second relationship: B -> A
        const secondRelationResponse = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships`,
          headers: { Authorization: `Bearer ${userAuthToken}` },
          payload: {
            accountId: account2.id,
            relatedAccountId: account1.id,
            relationshipType: type.id,
          },
        });
        expect(secondRelationResponse.statusCode).toBe(HttpStatus.CREATED);
      });

      it("should prevent duplicate relationships", async () => {
        // Create relationship type
        const typeResponse = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships/types`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            name: "Partner",
          },
        });
        const type = typeResponse.json().data;

        // Create first relationship
        await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            accountId: global.testAccount.uid,
            relatedAccountId: global.testAccount2.uid,
            relationshipType: type.id,
          },
        });

        // Try to create duplicate relationship
        const duplicateResponse = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            accountId: global.testAccount.uid,
            relatedAccountId: global.testAccount2.uid,
            relationshipType: type.id,
          },
        });

        expect(duplicateResponse.statusCode).toBe(HttpStatus.BAD_REQUEST);
        expect(duplicateResponse.json().message).toBe(
          "Relationship already exists",
        );
      });
    });

    describe("Update Relationship", () => {
      it("should update relationship type", async () => {
        // create new account
        const accountResponse = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            name: "Test Account for update test",
            primaryDomain: "test-account-for-update-test.com",
            accountOwnerId: global.testUser.uid,
            source: "manual",
          },
        });

        const account = accountResponse.json().data;

        // Create two relationship types
        const type1Response = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships/types`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            name: "Partner",
          },
        });
        const type1 = type1Response.json().data;

        const type2Response = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships/types`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            name: "Competitor",
          },
        });
        const type2 = type2Response.json().data;

        // Create relationship
        const createResponse = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            accountId: global.testAccount.uid,
            relatedAccountId: account.id,
            relationshipType: type1.id,
          },
        });
        const relationship = createResponse.json().data;

        // Update relationship type
        const updateResponse = await injectWithOrgId(app, {
          method: "PUT",
          url: `/v1/accounts/relationships/${relationship.id}`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            relationshipType: type2.id,
          },
        });

        expect(updateResponse.statusCode).toBe(HttpStatus.OK);
        const result = updateResponse.json().data;
        expect(result.relationshipType.id).toBe(type2.id);
      });

      it("should handle updating to relationship type with no inverse", async () => {
        // Create accounts and relationship types
        const account1Response = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts`,
          headers: { Authorization: `Bearer ${userAuthToken}` },
          payload: {
            name: "Test Account 1",
            primaryDomain: "test-account-1.com",
            accountOwnerId: global.testUser.uid,
            source: "manual",
          },
        });
        const account1 = account1Response.json().data;

        // Create relationship types with inverse
        const type1Response = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships/types`,
          headers: { Authorization: `Bearer ${userAuthToken}` },
          payload: { name: "Type 1" },
        });
        const type1 = type1Response.json().data;

        await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships/types`,
          headers: { Authorization: `Bearer ${userAuthToken}` },
          payload: {
            name: "Type 2",
            inverseRelationshipId: type1.id,
          },
        });

        // Create type without inverse
        const type3Response = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships/types`,
          headers: { Authorization: `Bearer ${userAuthToken}` },
          payload: { name: "Type 3" },
        });
        const type3 = type3Response.json().data;

        // Create initial relationship
        const createResponse = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships`,
          headers: { Authorization: `Bearer ${userAuthToken}` },
          payload: {
            accountId: global.testAccount.uid,
            relatedAccountId: account1.id,
            relationshipType: type1.id,
          },
        });
        const relationship = createResponse.json().data;

        // Update to type with no inverse
        const updateResponse = await injectWithOrgId(app, {
          method: "PUT",
          url: `/v1/accounts/relationships/${relationship.id}`,
          headers: { Authorization: `Bearer ${userAuthToken}` },
          payload: {
            relationshipType: type3.id,
          },
        });

        expect(updateResponse.statusCode).toBe(HttpStatus.OK);

        // Get actual account IDs
        const account1Data = await connection.query(
          `SELECT id FROM "public"."accounts" WHERE uid = $1`,
          [account1.id],
        );
        const testAccountData = await connection.query(
          `SELECT id FROM "public"."accounts" WHERE uid = $1`,
          [global.testAccount.uid],
        );

        // Get the relationship type IDs
        await connection.query(
          `SELECT id FROM "public"."account_relationship_types" WHERE uid = $1`,
          [type1.id],
        );
        const type3Data = await connection.query(
          `SELECT id FROM "public"."account_relationship_types" WHERE uid = $1`,
          [type3.id],
        );

        // Verify relationships after update - be more specific with the query
        const allRelationships = await connection.query(
          `SELECT ar.* 
           FROM "public"."account_relationships" ar
           WHERE (
             (ar.account_id = $1 AND ar.related_account_id = $2)
             OR 
             (ar.account_id = $2 AND ar.related_account_id = $1)
           )
           ORDER BY ar.is_active DESC, ar.created_at DESC`,
          [testAccountData[0].id, account1Data[0].id],
        );

        // Should have two relationships total (one active with new type, one inactive with old type)
        expect(allRelationships.length).toBe(2);

        // Verify only one active relationship with the new type
        const activeRelationships = allRelationships.filter((r) => r.is_active);
        expect(activeRelationships.length).toBe(1);
        expect(activeRelationships[0].relationship).toBe(type3Data[0].id);

        // Verify one inactive relationship with the old type
        const inactiveRelationships = allRelationships.filter(
          (r) => !r.is_active,
        );
        expect(inactiveRelationships.length).toBe(1);
      });
    });

    describe("Delete Relationship", () => {
      it("should delete relationship and its inverse", async () => {
        // Create parent-child relationship types
        const parentResponse = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships/types`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            name: "Parent",
          },
        });
        const parentType = parentResponse.json().data;

        await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships/types`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            name: "Child",
            inverseRelationshipId: parentType.id,
          },
        });

        // Create relationship
        const createResponse = await injectWithOrgId(app, {
          method: "POST",
          url: `/v1/accounts/relationships`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            accountId: global.testAccount.uid,
            relatedAccountId: global.testAdminAccount.uid,
            relationshipType: parentType.id,
          },
        });
        const relationship = createResponse.json().data;

        // Delete relationship
        const deleteResponse = await injectWithOrgId(app, {
          method: "DELETE",
          url: `/v1/accounts/relationships/${relationship.id}`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
        });

        expect(deleteResponse.statusCode).toBe(HttpStatus.NO_CONTENT);

        // Verify relationship is deleted
        const relationships = await connection.query(
          `SELECT * FROM "public"."account_relationships" WHERE account_id = '${global.testAccount2.id}' AND related_account_id = '${global.testAccount.id}' AND is_active = true`,
        );
        expect(relationships.length).toBe(0);

        // Verify inverse relationship is deleted
        const inverseRelationships = await connection.query(
          `SELECT * FROM "public"."account_relationships" WHERE account_id = '${global.testAccount.id}' AND related_account_id = '${global.testAccount2.id}' AND is_active = false`,
        );
        expect(inverseRelationships.length).toBe(0);
      });
    });
  });

  describe("Find All Account Relationships", () => {
    let relationships: AccountRelationshipResponseDto[] = [];
    let relatedAccount: AccountResponseDto;

    beforeAll(async () => {
      // Create a related account
      const createRelatedAccountResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          name: "Related Test Account",
          primaryDomain: faker.internet.domainName(),
          accountOwnerId: global.testUser.uid,
          source: "manual",
        },
      });
      relatedAccount = createRelatedAccountResponse.json().data;

      // Create multiple relationships with different attributes for testing
      const createRelationshipPromises = Array(5)
        .fill(null)
        .map(async () => {
          const response = await injectWithOrgId(app, {
            method: "POST",
            url: "/v1/accounts/relationships",
            headers: {
              Authorization: `Bearer ${userAuthToken}`,
            },
            payload: {
              accountId: testAccount.id,
              relatedAccountId: relatedAccount.id,
              relationshipType: relationshipType.id,
            },
          });
          return response.json().data;
        });

      relationships = await Promise.all(createRelationshipPromises);
    });

    it("should respect pagination limit", async () => {
      const limit = 2;
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/accounts/relationships",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        query: {
          limit: limit.toString(),
        },
      });

      const data = response.json().data.results;
      expect(response.statusCode).toBe(200);
      expect(data.length).toBe(limit);
    });

    it("should respect pagination offset", async () => {
      const limit = 2;
      const page = 1;
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/accounts/relationships",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        query: {
          limit: limit.toString(),
          page: page.toString(),
        },
      });

      const data = response.json().data.results;
      expect(response.statusCode).toBe(200);
      expect(data.length).toBe(limit);
      expect(data[0].id).not.toBe(relationships[0].id); // Should be different from first page
    });

    it("should filter by accountId", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/accounts/relationships",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        query: {
          accountId: testAccount.id,
        },
      });

      const data = response.json().data.results;
      expect(response.statusCode).toBe(200);
      data.forEach((relationship) => {
        expect(relationship.accountId).toBe(testAccount.id);
      });
    });

    it("should filter by relationshipTypeId", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/accounts/relationships",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        query: {
          relationshipTypeId: relationshipType.id,
        },
      });

      const data = response.json().data.results;
      expect(response.statusCode).toBe(200);
      data.forEach((relationship) => {
        expect(relationship.relationshipType.id).toBe(relationshipType.id);
      });
    });

    it("should combine multiple filters", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/accounts/relationships",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        query: {
          accountId: testAccount.id,
          relationshipTypeId: relationshipType.id,
          limit: "10",
        },
      });

      const data = response.json().data.results;
      expect(response.statusCode).toBe(200);
      data.forEach((relationship) => {
        expect(relationship.accountId).toBe(testAccount.id);
        expect(relationship.relationshipType.id).toBe(relationshipType.id);
      });
    });
  });
});
