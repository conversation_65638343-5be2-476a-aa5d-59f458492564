import { faker } from "@faker-js/faker";
import {
  HttpStatus,
  UnprocessableEntityException,
  ValidationPipe,
} from "@nestjs/common";
import {
  FastifyAdapter,
  NestFastifyApplication,
} from "@nestjs/platform-fastify";
import { Test } from "@nestjs/testing";
import {
  AccountAttributeType,
  AuditLogEntityType,
  AuditLogOp,
  AuditLogUpdatedField,
} from "@repo/thena-platform-entities";
import { sleep, t__loginIntoAuthService } from "@repo/thena-shared-libs";
import { DataSource } from "typeorm";
import { AppModule } from "../../app.module";
import { injectWithOrgId } from "../../utils";
import { AccountActivityResponseDto } from "../dtos/response/account-activity.dto";
import { AccountAttributeValueResponseDto } from "../dtos/response/account-attribute-value.dto";
import { AccountResponseDto } from "../dtos/response/account.dto";

describe("Account Activities", () => {
  let app: NestFastifyApplication;
  let connection: DataSource;
  let userAuthToken: string;
  let activityTypeAttribute: AccountAttributeValueResponseDto;
  let activityStatusAttribute: AccountAttributeValueResponseDto;
  let testAccount: AccountResponseDto;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = module.createNestApplication<NestFastifyApplication>(
      new FastifyAdapter(),
    );

    app.useGlobalPipes(
      new ValidationPipe({
        transform: true,
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        exceptionFactory: (errors) => {
          return new UnprocessableEntityException(
            errors.map((error) => ({
              property: error.property,
              constraints: error.constraints,
            })),
          );
        },
      }),
    );

    await app.init();
    await app.getHttpAdapter().getInstance().ready();

    connection = app.get(DataSource);

    // Log in users to get auth tokens
    userAuthToken = await t__loginIntoAuthService(
      global.testUser.email,
      global.testUser.password,
    );

    // Create test account
    const createAccountResponse = await injectWithOrgId(app, {
      method: "POST",
      url: "/v1/accounts",
      headers: {
        Authorization: `Bearer ${userAuthToken}`,
      },
      payload: {
        name: "Test Account for account activity tests",
        primaryDomain: faker.internet.domainName(),
        accountOwnerId: global.testUser.uid,
        source: "manual",
      },
    });
    testAccount = createAccountResponse.json().data;

    // Create activity type attribute
    const createTypeAttributeResponse = await injectWithOrgId(app, {
      method: "POST",
      url: "/v1/accounts/attributes",
      headers: {
        Authorization: `Bearer ${userAuthToken}`,
      },
      payload: {
        value: "MEETING",
        attribute: AccountAttributeType.ACTIVITY_TYPE,
        isDefault: false,
      },
    });
    activityTypeAttribute = createTypeAttributeResponse.json().data;

    // Create activity status attribute
    const createStatusAttributeResponse = await injectWithOrgId(app, {
      method: "POST",
      url: "/v1/accounts/attributes",
      headers: {
        Authorization: `Bearer ${userAuthToken}`,
      },
      payload: {
        value: "COMPLETED",
        attribute: AccountAttributeType.ACTIVITY_STATUS,
        isDefault: false,
      },
    });
    activityStatusAttribute = createStatusAttributeResponse.json().data;
  });

  afterAll(async () => {
    await app.close();
  });

  describe("Create Account Activity", () => {
    it("should be able to create account activity when all details are provided", async () => {
      const createAccountActivityResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/activities",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          activityTimestamp: "2024-01-01T00:00:00.000Z",
          duration: 60,
          location: "New York, NY",
          participants: [global.testUser1.uid],
          type: activityTypeAttribute.id,
          status: activityStatusAttribute.id,
        },
      });

      expect(createAccountActivityResponse.statusCode).toBe(HttpStatus.CREATED);
      const result = createAccountActivityResponse.json()
        .data as AccountActivityResponseDto;

      expect(result).toHaveProperty("id");
      expect(result.accountId).toBe(testAccount.id);
      expect(result.activityTimestamp).toBe("2024-01-01T00:00:00.000Z");
      expect(result.duration).toBe(60);
      expect(result.location).toBe("New York, NY");
      expect(result.type).toBe(activityTypeAttribute.value);
      expect(result.typeId).toBe(activityTypeAttribute.id);
      expect(result.status).toBe(activityStatusAttribute.value);
      expect(result.statusId).toBe(activityStatusAttribute.id);
      expect(result.participants).toContain(global.testUser1.uid);
      expect(result.creatorId).toBe(global.testUser.uid);
    });

    it("should be able to use default type and status attributes if not provided", async () => {
      // Create default type attribute
      const createDefaultTypeAttributeResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "DEFAULT_MEETING",
          attribute: AccountAttributeType.ACTIVITY_TYPE,
          isDefault: true,
        },
      });

      const defaultTypeAttribute =
        createDefaultTypeAttributeResponse.json().data;

      // Create default status attribute
      const createDefaultStatusAttributeResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "DEFAULT_STATUS",
          attribute: AccountAttributeType.ACTIVITY_STATUS,
          isDefault: true,
        },
      });

      const defaultStatusAttribute =
        createDefaultStatusAttributeResponse.json().data;

      const createAccountActivityResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/activities",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          activityTimestamp: "2024-01-01T00:00:00.000Z",
          duration: 60,
          location: "New York, NY",
        },
      });

      expect(createAccountActivityResponse.statusCode).toBe(HttpStatus.CREATED);
      const result = createAccountActivityResponse.json()
        .data as AccountActivityResponseDto;

      expect(result.type).toBe(defaultTypeAttribute.value);
      expect(result.typeId).toBe(defaultTypeAttribute.id);
      expect(result.status).toBe(defaultStatusAttribute.value);
      expect(result.statusId).toBe(defaultStatusAttribute.id);
    });

    it("should throw error when account id is invalid", async () => {
      const createAccountActivityResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/activities",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: "non-existent-account",
          activityTimestamp: "2024-01-01T00:00:00.000Z",
          duration: 60,
          location: "New York, NY",
        },
      });

      expect(createAccountActivityResponse.statusCode).toBe(
        HttpStatus.NOT_FOUND,
      );
      expect(createAccountActivityResponse.json().message).toBe(
        "Account not found",
      );
    });

    it("should throw error when a user in the list of participants does not exist", async () => {
      const createAccountActivityResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/activities",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          activityTimestamp: "2024-01-01T00:00:00.000Z",
          duration: 60,
          location: "New York, NY",
          participants: ["non-existent-user"],
        },
      });

      expect(createAccountActivityResponse.statusCode).toBe(
        HttpStatus.BAD_REQUEST,
      );
      expect(createAccountActivityResponse.json().message).toBe(
        "1 participants not found!",
      );
    });

    it("should record audit log post successful creation", async () => {
      const createAccountActivityResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/activities",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          activityTimestamp: "2024-01-01T00:00:00.000Z",
          duration: 60,
          location: "New York, NY",
        },
      });

      const activity = createAccountActivityResponse.json()
        .data as AccountActivityResponseDto;

      // Get organization ID from database
      const orgData = await connection.query(
        `SELECT id FROM "public"."organization" WHERE id = '${global.testUser1.organization_id}';`,
      );

      const activityData = await connection.query(
        `SELECT * FROM "public"."account_activities" WHERE uid = '${activity.id}';`,
      );

      // Check audit logs
      const auditLogs = await connection.query(
        `SELECT * FROM "public"."audit_logs" WHERE organization_id = '${orgData[0].id}' AND entity_type = '${AuditLogEntityType.ACCOUNT_ACTIVITY}' AND entity_id = '${activityData[0].id}' AND operation = '${AuditLogOp.CREATED}';`,
      );

      expect(auditLogs.length).toBe(1);
    });

    it("should throw error when attribute values are invalid", async () => {
      const invalidTypeActivityResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/activities",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          activityTimestamp: "2024-01-01T00:00:00.000Z",
          duration: 60,
          location: "New York, NY",
          type: "non-existent-type",
        },
      });

      expect(invalidTypeActivityResponse.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(invalidTypeActivityResponse.json().message).toBe(
        "Activity type not found",
      );

      const invalidStatusActivityResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/activities",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          activityTimestamp: "2024-01-01T00:00:00.000Z",
          duration: 60,
          location: "New York, NY",
          status: "non-existent-status",
        },
      });

      expect(invalidStatusActivityResponse.statusCode).toBe(
        HttpStatus.NOT_FOUND,
      );
      expect(invalidStatusActivityResponse.json().message).toBe(
        "Activity status not found",
      );
    });

    it("should be able to attach files to an activity", async () => {
      const createAccountActivityResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/activities",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          activityTimestamp: "2024-01-01T00:00:00.000Z",
          duration: 60,
          location: "New York, NY",
          attachmentUrls: [
            "https://thena-new-backend-test.s3.us-east-1.amazonaws.com/uploads/image-screenshots.png",
            "https://thena-new-backend-test.s3.us-east-1.amazonaws.com/uploads/image-screenshots.png",
          ],
        },
      });
      expect(createAccountActivityResponse.statusCode).toBe(HttpStatus.CREATED);

      const activity = createAccountActivityResponse.json()
        .data as AccountActivityResponseDto;

      expect(activity.attachments.length).toBe(1);
    });
  });

  describe("Update Account Activity", () => {
    it("should be able to update type, status, participants, duration, location, activity timestamp", async () => {
      // Create an activity first
      const createAccountActivityResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/activities",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          activityTimestamp: "2024-01-01T00:00:00.000Z",
          duration: 60,
          location: "New York, NY",
          participants: [global.testUser1.uid],
          type: activityTypeAttribute.id,
          status: activityStatusAttribute.id,
        },
      });

      const activity = createAccountActivityResponse.json()
        .data as AccountActivityResponseDto;

      // Create new type and status attributes for update
      const createNewTypeAttributeResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "UPDATED_MEETING",
          attribute: AccountAttributeType.ACTIVITY_TYPE,
          isDefault: false,
        },
      });

      const newTypeAttribute = createNewTypeAttributeResponse.json().data;

      const createNewStatusAttributeResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "UPDATED_STATUS",
          attribute: AccountAttributeType.ACTIVITY_STATUS,
          isDefault: false,
        },
      });

      const newStatusAttribute = createNewStatusAttributeResponse.json().data;

      // Update the activity
      const updateAccountActivityResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/activities/${activity.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          type: newTypeAttribute.id,
          status: newStatusAttribute.id,
          participants: [global.testUser2.uid, global.testUser1.uid],
          duration: 120,
          location: "Los Angeles, CA",
          activityTimestamp: "2024-01-02T00:00:00.000Z",
        },
      });

      expect(updateAccountActivityResponse.statusCode).toBe(HttpStatus.OK);
      const result = updateAccountActivityResponse.json()
        .data as AccountActivityResponseDto;

      expect(result.type).toBe(newTypeAttribute.value);
      expect(result.typeId).toBe(newTypeAttribute.id);
      expect(result.status).toBe(newStatusAttribute.value);
      expect(result.statusId).toBe(newStatusAttribute.id);
      expect(result.participants).toContain(global.testUser2.uid);
      expect(result.participants).toContain(global.testUser1.uid);
      expect(result.duration).toBe(120);
      expect(result.location).toBe("Los Angeles, CA");
      expect(result.activityTimestamp).toBe("2024-01-02T00:00:00.000Z");
    });

    it("should throw error when activity is invalid", async () => {
      const updateAccountActivityResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: "/v1/accounts/activities/non-existent-activity",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          duration: 120,
        },
      });

      expect(updateAccountActivityResponse.statusCode).toBe(
        HttpStatus.NOT_FOUND,
      );
      expect(updateAccountActivityResponse.json().message).toBe(
        "Activity not found!",
      );
    });

    it("should throw error when type attribute is invalid", async () => {
      // Create an activity first
      const createAccountActivityResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/activities",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          activityTimestamp: "2024-01-01T00:00:00.000Z",
          duration: 60,
          location: "New York, NY",
        },
      });

      const activity = createAccountActivityResponse.json()
        .data as AccountActivityResponseDto;

      const updateAccountActivityResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/activities/${activity.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          type: "non-existent-type",
        },
      });

      expect(updateAccountActivityResponse.statusCode).toBe(
        HttpStatus.NOT_FOUND,
      );
      expect(updateAccountActivityResponse.json().message).toBe(
        "Activity type not found",
      );
    });

    it("should record audit log with updated fields", async () => {
      // Create an activity first
      const createAccountActivityResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/activities",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          activityTimestamp: "2024-01-01T00:00:00.000Z",
          duration: 60,
          location: "New York, NY",
        },
      });

      const activity = createAccountActivityResponse.json()
        .data as AccountActivityResponseDto;

      // Update the activity
      const updateAccountActivityResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/activities/${activity.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          duration: 120,
          location: "Los Angeles, CA",
        },
      });

      expect(updateAccountActivityResponse.statusCode).toBe(HttpStatus.OK);

      const activityData = await connection.query(
        `SELECT * FROM "public"."account_activities" WHERE uid = '${activity.id}';`,
      );

      // Get organization ID from database
      const orgData = await connection.query(
        `SELECT id FROM "public"."organization" WHERE id = '${global.testUser1.organization_id}';`,
      );

      // Check audit logs
      const auditLogs = await connection.query(
        `SELECT * FROM "public"."audit_logs" WHERE organization_id = '${orgData[0].id}' AND entity_type = '${AuditLogEntityType.ACCOUNT_ACTIVITY}' AND entity_id = '${activityData[0].id}' AND operation = '${AuditLogOp.UPDATED}';`,
      );

      expect(auditLogs.length).toBe(1);
      expect(
        auditLogs[0].metadata.updatedFields.some(
          (m: AuditLogUpdatedField) => m.field === "duration",
        ),
      ).toBe(true);
      expect(
        auditLogs[0].metadata.updatedFields.some(
          (m: AuditLogUpdatedField) => m.field === "location",
        ),
      ).toBe(true);
    });

    it("should throw error when attribute values are invalid", async () => {
      // create task first
      const createActivityResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/activities",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          activityTimestamp: "2024-01-01T00:00:00.000Z",
          duration: 60,
          location: "New York, NY",
        },
      });
      const activity = createActivityResponse.json().data;

      const invalidTypeActivityResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/activities/${activity.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          type: "non-existent-type",
        },
      });

      expect(invalidTypeActivityResponse.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(invalidTypeActivityResponse.json().message).toBe(
        "Activity type not found",
      );

      const invalidStatusActivityResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/activities/${activity.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          status: "non-existent-status",
        },
      });

      expect(invalidStatusActivityResponse.statusCode).toBe(
        HttpStatus.NOT_FOUND,
      );
      expect(invalidStatusActivityResponse.json().message).toBe(
        "Activity status not found",
      );
    });

    it("should be able to attach files to an activity", async () => {
      const createAccountActivityWithoutAttachmentsResponse =
        await injectWithOrgId(app, {
          method: "POST",
          url: "/v1/accounts/activities",
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            accountId: testAccount.id,
            activityTimestamp: "2024-01-01T00:00:00.000Z",
            duration: 60,
            location: "New York, NY",
          },
        });
      expect(createAccountActivityWithoutAttachmentsResponse.statusCode).toBe(
        HttpStatus.CREATED,
      );

      const activityWithoutAttachments =
        createAccountActivityWithoutAttachmentsResponse.json()
          .data as AccountActivityResponseDto;

      const createAccountActivityResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/activities",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          activityTimestamp: "2024-01-01T00:00:00.000Z",
          duration: 60,
          location: "New York, NY",
          attachmentUrls: [
            "https://thena-new-backend-test.s3.us-east-1.amazonaws.com/uploads/image-screenshots.png",
          ],
        },
      });
      expect(createAccountActivityResponse.statusCode).toBe(HttpStatus.CREATED);

      const activity = createAccountActivityResponse.json()
        .data as AccountActivityResponseDto;

      expect(activity.attachments.length).toBe(1);

      const updateAccountActivityResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/activities/${activity.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          attachmentUrls: [
            "https://thena-new-backend-test.s3.us-east-1.amazonaws.com/uploads/image-screenshots.png",
            "https://thena-new-backend-test.s3.us-east-1.amazonaws.com/uploads/checking/Screenshot+2025-01-13+at+11.01.34%E2%80%AFPM.png",
          ],
        },
      });

      const updatedActivity = updateAccountActivityResponse.json()
        .data as AccountActivityResponseDto;

      expect(updatedActivity.attachments.length).toBe(2);

      const updateAccountActivityWithoutAttachmentsResponse =
        await injectWithOrgId(app, {
          method: "PUT",
          url: `/v1/accounts/activities/${activityWithoutAttachments.id}`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
          payload: {
            attachmentUrls: [
              "https://thena-new-backend-test.s3.us-east-1.amazonaws.com/uploads/checking/Screenshot+2025-01-13+at+11.01.34%E2%80%AFPM.png",
            ],
          },
        });

      const updatedActivityWithoutAttachments =
        updateAccountActivityWithoutAttachmentsResponse.json()
          .data as AccountActivityResponseDto;

      expect(updatedActivityWithoutAttachments.attachments.length).toBe(1);
    });
  });

  describe("Delete Account Activity", () => {
    it("should be able to delete an existing activity", async () => {
      // Create an activity first
      const createAccountActivityResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/activities",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          activityTimestamp: "2024-01-01T00:00:00.000Z",
          duration: 60,
          location: "New York, NY",
        },
      });

      const activity = createAccountActivityResponse.json()
        .data as AccountActivityResponseDto;

      // Delete the activity
      const deleteAccountActivityResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/activities/${activity.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(deleteAccountActivityResponse.statusCode).toBe(
        HttpStatus.NO_CONTENT,
      );

      // Verify activity is marked as inactive
      const activityData = await connection.query(
        `SELECT * FROM "public"."account_activities" WHERE uid = '${activity.id}' AND is_active = false;`,
      );

      expect(activityData.length).toBe(1);
    });

    it("should throw error when activity is non existent or already deleted", async () => {
      // Try deleting non-existent activity
      const deleteNonExistentResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: "/v1/accounts/activities/non-existent-activity",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(deleteNonExistentResponse.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(deleteNonExistentResponse.json().message).toBe(
        "Activity not found!",
      );

      // Create and delete an activity
      const createAccountActivityResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/activities",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          activityTimestamp: "2024-01-01T00:00:00.000Z",
          duration: 60,
          location: "New York, NY",
        },
      });

      const activity = createAccountActivityResponse.json()
        .data as AccountActivityResponseDto;

      // Delete the activity
      await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/activities/${activity.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      // Try deleting again
      const deleteAgainResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/activities/${activity.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(deleteAgainResponse.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(deleteAgainResponse.json().message).toBe("Activity not found!");
    });

    it("should record audit log on successful deletion", async () => {
      // Create an activity first
      const createAccountActivityResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/activities",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          activityTimestamp: "2024-01-01T00:00:00.000Z",
          duration: 60,
          location: "New York, NY",
        },
      });

      const activity = createAccountActivityResponse.json()
        .data as AccountActivityResponseDto;

      // Delete the activity
      await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/activities/${activity.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      const activityData = await connection.query(
        `SELECT * FROM "public"."account_activities" WHERE uid = '${activity.id}';`,
      );

      // Get organization ID from database
      const orgData = await connection.query(
        `SELECT id FROM "public"."organization" WHERE id = '${global.testUser1.organization_id}';`,
      );

      // Check audit logs
      const auditLogs = await connection.query(
        `SELECT * FROM "public"."audit_logs" WHERE organization_id = '${orgData[0].id}' AND entity_type = '${AuditLogEntityType.ACCOUNT_ACTIVITY}' AND entity_id = '${activityData[0].id}' AND operation = '${AuditLogOp.DELETED}';`,
      );

      expect(auditLogs.length).toBe(1);
    });
  });

  describe("Remove Attachment from Account Activity", () => {
    it("should be able to remove an attachment from an activity", async () => {
      const createAccountActivityResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/activities",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          activityTimestamp: "2024-01-01T00:00:00.000Z",
          duration: 60,
          location: "New York, NY",
          attachmentUrls: [
            "https://thena-new-backend-test.s3.us-east-1.amazonaws.com/uploads/image-screenshots.png",
            "https://thena-new-backend-test.s3.us-east-1.amazonaws.com/uploads/checking/Screenshot+2025-01-13+at+11.01.34%E2%80%AFPM.png",
          ],
        },
      });

      const activity = createAccountActivityResponse.json()
        .data as AccountActivityResponseDto;

      expect(activity.attachments.length).toBe(2);

      const removeAttachmentResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/activities/${activity.id}/attachments/${activity.attachments[0].id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(removeAttachmentResponse.statusCode).toBe(HttpStatus.NO_CONTENT);

      const updatedActivityResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/activities/${activity.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          duration: 120,
        },
      });

      const updatedActivity = updatedActivityResponse.json().data;
      expect(updatedActivity.attachments.length).toBe(1);
    });
  });

  describe("Delete account activity attributes", () => {
    it("should be able to delete unused attribute value", async () => {
      // create new type
      const createActivityTypeResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "TO BE DELETED",
          attribute: AccountAttributeType.ACTIVITY_TYPE,
          isDefault: false,
        },
      });

      const activityType = createActivityTypeResponse.json()
        .data as AccountAttributeValueResponseDto;

      // create new status
      const createActivityStatusResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "TO BE DELETED",
          attribute: AccountAttributeType.ACTIVITY_STATUS,
          isDefault: false,
        },
      });

      const activityStatus = createActivityStatusResponse.json()
        .data as AccountAttributeValueResponseDto;

      // delete type
      const activityTypeDeleteResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/attributes/${activityType.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(activityTypeDeleteResponse.statusCode).toBe(HttpStatus.NO_CONTENT);

      // delete status
      const activityStatusDeleteResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/attributes/${activityStatus.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(activityStatusDeleteResponse.statusCode).toBe(
        HttpStatus.NO_CONTENT,
      );
    });

    it("should throw an error when trying to delete attribute value that is in use", async () => {
      // create new type
      const createActivityTypeResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "TO BE DELETED WITHOUT FORCE DELETE",
          attribute: AccountAttributeType.ACTIVITY_TYPE,
          isDefault: false,
        },
      });

      const activityType = createActivityTypeResponse.json()
        .data as AccountAttributeValueResponseDto;

      // create new status
      const createActivityStatusResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "TO BE DELETED WITHOUT FORCE DELETE",
          attribute: AccountAttributeType.ACTIVITY_STATUS,
          isDefault: false,
        },
      });

      const activityStatus = createActivityStatusResponse.json()
        .data as AccountAttributeValueResponseDto;

      // create activity with type and status
      const createActivityResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/activities",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          activityTimestamp: "2024-01-01T00:00:00.000Z",
          duration: 60,
          location: "New York, NY",
          type: activityType.id,
          status: activityStatus.id,
        },
      });
      expect(createActivityResponse.statusCode).toBe(HttpStatus.CREATED);
      const activity = createActivityResponse.json()
        .data as AccountActivityResponseDto;

      // Delete type
      const activityTypeDeleteResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/attributes/${activityType.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(activityTypeDeleteResponse.statusCode).toBe(
        HttpStatus.BAD_REQUEST,
      );
      expect(activityTypeDeleteResponse.json().message).toBe(
        "Cannot delete attribute value. It is in use.",
      );

      // delete status
      const activityStatusDeleteResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/attributes/${activityStatus.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(activityStatusDeleteResponse.statusCode).toBe(
        HttpStatus.BAD_REQUEST,
      );
      expect(activityStatusDeleteResponse.json().message).toBe(
        "Cannot delete attribute value. It is in use.",
      );

      // check if activity details are still correct
      const updatedActivityResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/activities/${activity.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          duration: 120,
        },
      });

      const updatedActivity = updatedActivityResponse.json().data;
      expect(updatedActivity.typeId).toBe(activityType.id);
      expect(updatedActivity.statusId).toBe(activityStatus.id);
    });

    it("should be able to delete attribute value with force delete flag", async () => {
      // create new type
      const createActivityTypeResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "TO BE DELETED",
          attribute: AccountAttributeType.ACTIVITY_TYPE,
          isDefault: false,
        },
      });

      const activityType = createActivityTypeResponse.json()
        .data as AccountAttributeValueResponseDto;

      // create new status
      const createActivityStatusResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "TO BE DELETED",
          attribute: AccountAttributeType.ACTIVITY_STATUS,
          isDefault: false,
        },
      });

      const activityStatus = createActivityStatusResponse.json()
        .data as AccountAttributeValueResponseDto;

      // create default type
      const createDefaultActivityTypeResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "DEFAULT TYPE",
          attribute: AccountAttributeType.ACTIVITY_TYPE,
          isDefault: true,
        },
      });

      const defaultActivityType = createDefaultActivityTypeResponse.json()
        .data as AccountAttributeValueResponseDto;

      // create default status
      const createDefaultActivityStatusResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/attributes",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          value: "DEFAULT STATUS",
          attribute: AccountAttributeType.ACTIVITY_STATUS,
          isDefault: true,
        },
      });

      const defaultActivityStatus = createDefaultActivityStatusResponse.json()
        .data as AccountAttributeValueResponseDto;

      // create activity with type and status
      const createActivityResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/accounts/activities",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          accountId: testAccount.id,
          activityTimestamp: "2024-01-01T00:00:00.000Z",
          duration: 60,
          location: "New York, NY",
          type: activityType.id,
          status: activityStatus.id,
        },
      });
      expect(createActivityResponse.statusCode).toBe(HttpStatus.CREATED);
      const activity = createActivityResponse.json()
        .data as AccountActivityResponseDto;

      // Delete type
      const activityTypeDeleteResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/attributes/${activityType.id}?forceDelete=true`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(activityTypeDeleteResponse.statusCode).toBe(HttpStatus.NO_CONTENT);

      // Delete status
      const activityStatusDeleteResponse = await injectWithOrgId(app, {
        method: "DELETE",
        url: `/v1/accounts/attributes/${activityStatus.id}?forceDelete=true`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
      });

      expect(activityStatusDeleteResponse.statusCode).toBe(
        HttpStatus.NO_CONTENT,
      );

      await sleep(500);

      // check if activity details are still correct
      const updatedActivityResponse = await injectWithOrgId(app, {
        method: "PUT",
        url: `/v1/accounts/activities/${activity.id}`,
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        payload: {
          duration: 120,
        },
      });

      const updatedActivity = updatedActivityResponse.json().data;
      expect(updatedActivity.typeId).toBe(defaultActivityType.id);
      expect(updatedActivity.statusId).toBe(defaultActivityStatus.id);
    });
  });

  describe("Find All Account Activities", () => {
    let activities: AccountActivityResponseDto[] = [];
    const activityTimestamp = new Date().toISOString();

    beforeAll(async () => {
      // Create multiple activities with different attributes for testing
      const createActivityPromises = Array(5)
        .fill(null)
        .map(async (_, index) => {
          const response = await injectWithOrgId(app, {
            method: "POST",
            url: "/v1/accounts/activities",
            headers: {
              Authorization: `Bearer ${userAuthToken}`,
            },
            payload: {
              accountId: testAccount.id,
              activityTimestamp,
              duration: 60,
              location: `Location ${index + 1}`,
              type: activityTypeAttribute.id,
              status: activityStatusAttribute.id,
            },
          });
          return response.json().data;
        });

      activities = await Promise.all(createActivityPromises);
    });

    it("should respect pagination limit", async () => {
      const limit = 2;
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/accounts/activities",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        query: {
          limit: limit.toString(),
        },
      });

      const data = response.json().data.results;
      expect(response.statusCode).toBe(200);
      expect(data.length).toBe(limit);
    });

    it("should respect pagination offset", async () => {
      const limit = 2;
      const page = 1;
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/accounts/activities",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        query: {
          limit: limit.toString(),
          page: page.toString(),
        },
      });

      const data = response.json().data.results;
      expect(response.statusCode).toBe(200);
      expect(data.length).toBe(limit);
      expect(data[0].id).not.toBe(activities[0].id); // Should be different from first page
    });

    it("should filter by accountId", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/accounts/activities",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        query: {
          accountId: testAccount.id,
        },
      });

      const data = response.json().data.results;
      expect(response.statusCode).toBe(200);
      data.forEach((activity) => {
        expect(activity.accountId).toBe(testAccount.id);
      });
    });

    it("should filter by type", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/accounts/activities",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        query: {
          type: activityTypeAttribute.value,
        },
      });

      const data = response.json().data.results;
      expect(response.statusCode).toBe(200);
      data.forEach((activity) => {
        expect(activity.type.id).toBe(activityTypeAttribute.id);
      });
    });

    it("should filter by status", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/accounts/activities",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        query: {
          status: activityStatusAttribute.value,
        },
      });

      const data = response.json().data.results;
      expect(response.statusCode).toBe(200);
      data.forEach((activity) => {
        expect(activity.status.id).toBe(activityStatusAttribute.id);
      });
    });

    it("should combine multiple filters", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/accounts/activities",
        headers: {
          Authorization: `Bearer ${userAuthToken}`,
        },
        query: {
          accountId: testAccount.id,
          type: activityTypeAttribute.value,
          status: activityStatusAttribute.value,
          limit: "10",
        },
      });

      const data = response.json().data.results;
      expect(response.statusCode).toBe(200);
      data.forEach((activity) => {
        expect(activity.accountId).toBe(testAccount.id);
        expect(activity.type.id).toBe(activityTypeAttribute.id);
        expect(activity.status.id).toBe(activityStatusAttribute.id);
      });
    });

    afterAll(async () => {
      // Clean up created activities
      for (const activity of activities) {
        await injectWithOrgId(app, {
          method: "DELETE",
          url: `/v1/accounts/activities/${activity.id}`,
          headers: {
            Authorization: `Bearer ${userAuthToken}`,
          },
        });
      }
    });
  });
});
