import { AccountEvents } from "@repo/thena-shared-interfaces";
import { AccountActivityResponseDto } from "../dtos/response/account-activity.dto";
import { AccountNoteResponseDto } from "../dtos/response/account-note.dto";
import { AccountRelationshipResponseDto } from "../dtos/response/account-relationship.dto";
import { AccountTaskResponseDto } from "../dtos/response/account-task.dto";
import { AccountResponseDto } from "../dtos/response/account.dto";
import { CustomerContactResponseDto } from "../dtos/response/customer-contact.dto";

interface Actor {
  id: string;
  type: string;
  email: string;
}

export interface AccountPayload {
  account?: AccountResponseDto;
  previousAccount?: AccountResponseDto;
}

export interface AccountRelationshipPayload {
  relationship?: AccountRelationshipResponseDto;
  previousRelationship?: AccountRelationshipResponseDto;
}

export interface AccountActivityPayload {
  activity?: AccountActivityResponseDto;
  previousActivity?: AccountActivityResponseDto;
}

export interface AccountNotePayload {
  note?: AccountNoteResponseDto;
  previousNote?: AccountNoteResponseDto;
}

interface AccountTaskData extends AccountTaskResponseDto {}

export interface AccountTaskPayload {
  task?: AccountTaskData;
  previousTask?: AccountTaskData;
}

export interface CustomerContactPayload {
  customerContact?: CustomerContactResponseDto;
  previousCustomerContact?: CustomerContactResponseDto;
}

export interface SNSEvent<T> {
  eventId: string;
  eventType: AccountEvents;
  timestamp: string;
  orgId: string;
  actor: Actor;
  payload: T;
}
