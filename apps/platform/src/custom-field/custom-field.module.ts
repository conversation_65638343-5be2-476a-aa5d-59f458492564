import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import {
  CustomField,
  CustomFieldRepository,
  CustomFieldValues,
  CustomFieldValuesRepository,
  ThenaRestrictedField,
  ThenaRestrictedFieldRepository,
  TransactionService,
} from "@repo/thena-platform-entities";
import { AuthModule } from "../auth/auth.module";
import { CommonModule } from "../common/common.module";
import { OrganizationModule } from "../organization/organization.module";
import { SharedModule } from "../shared/shared.module";
import { SharedService } from "../shared/shared.service";
import { TeamsModule } from "../teams/teams.module";
import { CustomFieldController } from "./controllers/custom-field.controller";
import { CustomFieldsGrpcController } from "./controllers/custom-field.grpc.controller";
import { CustomFieldValuesService } from "./services/custom-field-values.service";
import { CustomFieldService } from "./services/custom-field.service";
import { ThenaRestrictedFieldService } from "./services/thena-restricted-field.service";
import { CustomFieldvalidatorService } from "./validators/custom-field.validator";

@Module({
  imports: [
    CommonModule,
    AuthModule,
    TypeOrmModule.forFeature([
      CustomField,
      CustomFieldRepository,
      CustomFieldValues,
      CustomFieldValuesRepository,
      ThenaRestrictedField,
      ThenaRestrictedFieldRepository,
    ]),
    OrganizationModule,
    TeamsModule,
    SharedModule,
  ],
  controllers: [CustomFieldController, CustomFieldsGrpcController],
  providers: [
    CustomFieldService,
    CustomFieldRepository,
    CustomFieldValuesService,
    CustomFieldValuesRepository,
    ThenaRestrictedFieldRepository,
    CustomFieldvalidatorService,
    ThenaRestrictedFieldService,
    SharedService,
    TransactionService,
  ],
  exports: [
    CustomFieldService,
    CustomFieldValuesService,
    CustomFieldvalidatorService,
    ThenaRestrictedFieldService,
  ],
})
export class CustomFieldModule {}
