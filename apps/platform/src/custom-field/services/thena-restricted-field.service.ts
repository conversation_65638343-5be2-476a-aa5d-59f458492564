import { Injectable } from "@nestjs/common";
import {
  ThenaRestrictedFieldRepository,
  ThenaRestrictedFieldType,
} from "@repo/thena-platform-entities";

@Injectable()
export class ThenaRestrictedFieldService {
  constructor(
    private thenaRestrictedFieldRepository: ThenaRestrictedFieldRepository,
  ) {}

  async getAllFields(type?: ThenaRestrictedFieldType) {
    let thenaRestrictedFieldType = null;
    if (type) {
      thenaRestrictedFieldType = type;
    }
    const fields = await this.thenaRestrictedFieldRepository.findAll({
      where: { isDeleted: false, type: thenaRestrictedFieldType },
    });
    return fields;
  }
}
