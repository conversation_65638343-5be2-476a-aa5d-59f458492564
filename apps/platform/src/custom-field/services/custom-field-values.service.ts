import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import {
  CustomFieldValues,
  CustomFieldValuesRepository,
} from "@repo/thena-platform-entities";
import * as _ from "lodash";
import {
  CreateCustomFieldValuesDto,
  UpdateCustomFieldValuesDto,
} from "../dto/custom-field-values.dto";
import { CustomFieldService } from "./custom-field.service";

@Injectable()
export class CustomFieldValuesService {
  constructor(
    private customFieldValuesRepository: CustomFieldValuesRepository,
    private readonly customFieldService: CustomFieldService,
  ) {}

  /**
   * Helper method to remove duplicates from data array based on value and id
   * @param data Array of CreateCustomFieldValuesDto["data"] objects
   * @returns Deduplicated array
   */
  private removeDuplicates(
    data: CreateCustomFieldValuesDto["data"],
  ): CreateCustomFieldValuesDto["data"] {
    // Create a map to track unique values and ids
    const valueMap = new Map<string, boolean>();
    const idMap = new Map<string, boolean>();

    return data.filter((item) => {
      // For items with value
      if (item.value) {
        if (valueMap.has(item.value)) {
          return false; // Skip duplicate value
        }
        valueMap.set(item.value, true);
      }

      // For items with id
      if (item.id) {
        if (idMap.has(item.id)) {
          return false; // Skip duplicate id
        }
        idMap.set(item.id, true);
      }

      return true;
    });
  }

  /**
   * Finds all custom fields.
   * @returns All custom fields.
   */
  findAll(): Promise<CustomFieldValues[]> {
    return this.customFieldValuesRepository.findAll();
  }

  /**
   * Finds a custom field by ID.
   * @param id The ID of the custom field to find.
   * @returns The custom field.
   */
  async findOne(id: string): Promise<CustomFieldValues> {
    const customField = await this.customFieldValuesRepository.findOneById(id);
    if (!customField) {
      throw new NotFoundException(
        "Custom field with provided id was not found!",
      );
    }

    return customField;
  }

  /**
   * Creates a new custom field.
   * @param createCustomFieldValuesDto The custom field data to create.
   * @returns The created custom field.
   */
  async create(
    orgId: string,
    createCustomFieldValuesDto: CreateCustomFieldValuesDto,
  ): Promise<CustomFieldValues> {
    try {
      const customFields = await this.customFieldService.findByIds(orgId, [
        createCustomFieldValuesDto.customFieldId,
      ]);
      const customField = customFields.items[0];
      if (!customField) {
        throw new NotFoundException(
          "Custom field with provided id was not found!",
        );
      }

      // Remove duplicates from data array
      const deduplicatedData = this.removeDuplicates(
        createCustomFieldValuesDto.data,
      );

      const customFieldValues = this.customFieldValuesRepository.create({
        organization: {
          id: createCustomFieldValuesDto.organizationId,
        },
        customField: customField,
        data: deduplicatedData,
        metadata: createCustomFieldValuesDto.metadata,
      });

      return this.customFieldValuesRepository.save(customFieldValues);
    } catch (err) {
      console.log(err);
    }
  }

  /**
   * Updates an existing custom field.
   * @param id The ID of the custom field to update.
   * @param updateCustomFieldDto The custom field data to update.
   * @returns The updated custom field.
   */
  async update(
    id: string,
    updateCustomFieldValuesDto: UpdateCustomFieldValuesDto,
  ): Promise<CustomFieldValues> {
    // Check if the custom field exists
    const customField = await this.customFieldValuesRepository.findOneById(id);
    if (_.isEmpty(customField)) {
      throw new NotFoundException(
        "Custom field with provided id was not found!",
      );
    }

    // Update the custom field name if provided
    if (updateCustomFieldValuesDto.data) {
      // Remove duplicates before updating
      const deduplicatedData = this.removeDuplicates(
        updateCustomFieldValuesDto.data,
      );
      customField.data = deduplicatedData.map((valueData) => ({
        value: valueData.value,
        id: valueData.id,
      }));
    }

    if (updateCustomFieldValuesDto.metadata) {
      customField.metadata = updateCustomFieldValuesDto.metadata;
    }

    return this.customFieldValuesRepository.save(customField);
  }

  /**
   * Removes a custom field by ID.
   * @param id The ID of the custom field to remove.
   */
  async remove(id: string): Promise<void> {
    const customField = await this.customFieldValuesRepository.findOneById(id);
    if (_.isEmpty(customField)) {
      throw new NotFoundException(
        "Custom field with provided id was not found!",
      );
    }

    await this.customFieldValuesRepository.remove(customField);
  }

  /**
   * Creates custom field values for a ticket
   * @param customFieldValues The custom field values to create
   * @param organizationId The organization ID
   * @returns Array of created custom field values
   */
  async createCustomFieldValues(
    customFieldValues: Array<{
      customFieldId: string;
      data: CreateCustomFieldValuesDto["data"];
      metadata?: Record<string, any>;
    }>,
    organizationId: string,
  ): Promise<CustomFieldValues[]> {
    const newCustomFieldValues: CustomFieldValues[] = [];

    const customFieldValuesMap = new Map<string, boolean>();
    for (const customFieldValue of customFieldValues) {
      if (customFieldValuesMap.has(customFieldValue.customFieldId)) {
        throw new BadRequestException("Duplicate custom field values found!");
      }
      customFieldValuesMap.set(customFieldValue.customFieldId, true);
    }

    for (const customFieldValue of customFieldValues) {
      const customFieldValuesDto = new CreateCustomFieldValuesDto();
      customFieldValuesDto.customFieldId = customFieldValue.customFieldId;
      // Remove duplicates from data array
      customFieldValuesDto.data = this.removeDuplicates(customFieldValue.data);
      customFieldValuesDto.metadata = customFieldValue.metadata;
      customFieldValuesDto.organizationId = organizationId;

      const customField = await this.create(
        organizationId,
        customFieldValuesDto,
      );
      newCustomFieldValues.push(customField);
    }

    return newCustomFieldValues;
  }
}
