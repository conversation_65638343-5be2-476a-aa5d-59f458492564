import { BadRequestException, Inject, Injectable } from "@nestjs/common";
import { ILogger } from "@repo/nestjs-commons/logger";
import {
  CustomField,
  CustomFieldRepository,
  CustomFieldSource,
  CustomFieldTypeToNameMapping,
  CustomValidationError,
  FormFieldEventTypes,
  ThenaRestrictedFieldNames,
  ThenaRestrictedFieldRepository,
  ThenaRestrictedFieldType,
} from "@repo/thena-platform-entities";
import { FastifyRequest } from "fastify";
import * as _ from "lodash";
import { ILike, In, IsNull } from "typeorm";
import { IdGeneratorUtils } from "../../common/utils";
import { SharedService } from "../../shared/shared.service";
import { GetFieldRelations } from "../constants/constants";
import {
  CreateCustomFieldDto,
  DeleteCustomFieldDto,
  UpdateCustomFieldDto,
} from "../dto/custom-field.dto";

@Injectable()
export class CustomFieldService {
  constructor(
    @Inject("CustomLogger")
    private readonly logger: ILogger,
    private customFieldRepository: CustomFieldRepository,
    private thenaRestrictedFieldRepository: ThenaRestrictedFieldRepository,
    private sharedService: SharedService,
  ) {}

  /**
   * Finds all custom fields.
   * @returns All custom fields.
   */
  async fetchPaginatedResults(
    organizationId: string,
    limit: number,
    page: number,
    teamId?: string,
    onlyTeamFields?: boolean,
    source?: CustomFieldSource,
  ) {
    const query = [];
    if (teamId) {
      query.push({
        organizationId,
        isDeleted: false,
        teamId,
        ...(source && { source }),
      });
    }

    if (!teamId || !onlyTeamFields) {
      query.push({
        organizationId,
        isDeleted: false,
        teamId: IsNull(),
        ...(source && { source }),
      });
    }

    if (query.length === 0) {
      throw new BadRequestException("No fields to fetch");
    }

    const customFields = await this.customFieldRepository.fetchPaginatedResults(
      { limit: Math.min(limit ?? 10, 100), page: page ?? 0 },
      {
        where: query,
        relations: GetFieldRelations,
      },
    );

    return {
      ...customFields,
      page: (customFields.results || []).length > 0 ? ++page : undefined,
    };
  }

  async findByIds(orgId: string, ids: string[]) {
    const customFields = await this.customFieldRepository.findAll({
      where: {
        organizationId: orgId,
        uid: In(ids),
        isDeleted: false,
      },
      relations: GetFieldRelations,
    });

    return {
      items: customFields,
    };
  }

  /**
   * Finds custom fields by ids.
   * @param request The FastifyRequest object containing user info
   * @param ids Array of custom field IDs to fetch
   * @returns Custom fields matching the provided IDs
   */
  async findByIdsWithTeamCheck(
    orgId: string,
    ids: string[],
    teamIds?: string[],
  ) {
    teamIds = teamIds.filter(Boolean);
    const query = [
      {
        organizationId: orgId,
        uid: In(ids),
        isDeleted: false,
        teamId: IsNull(),
      },
    ];

    if (teamIds?.length > 0) {
      query.push({
        organizationId: orgId,
        uid: In(ids),
        isDeleted: false,
        teamId: In(teamIds),
      });
    }
    const customFields = await this.customFieldRepository.findAll({
      where: query,
      relations: GetFieldRelations,
    });
    if (customFields.length !== ids.length) {
      const missingFields = ids.filter(
        (id) => !customFields.some((field) => field.uid === id),
      );
      throw new BadRequestException(
        `Some fields not found, either they are deleted or not found in your teams, missing fields: ${missingFields.join(
          ", ",
        )}`,
      );
    }
    return {
      items: customFields,
    };
  }

  /**
   * Search custom fields by name.
   * @param request The FastifyRequest object containing user info
   * @param term term to search
   * @returns Custom fields matching the provided term
   */
  async search(
    request: FastifyRequest,
    term: string,
    teamId?: string,
    onlyTeamFields?: boolean,
  ) {
    if (_.isEmpty(term?.trim())) {
      throw new BadRequestException("No search term provided");
    }
    const query = [];
    if (teamId) {
      query.push({
        name: ILike(`%${term.toLocaleLowerCase()}%`),
        organizationId: request.user.orgId,
        isDeleted: false,
        teamId: teamId,
      });
    }

    if (!teamId || !onlyTeamFields) {
      query.push({
        name: ILike(`%${term.toLocaleLowerCase()}%`),
        organizationId: request.user.orgId,
        isDeleted: false,
        teamId: IsNull(),
      });
    }
    const customFields = await this.customFieldRepository.findAll({
      where: query,
      relations: GetFieldRelations,
    });
    return {
      items: customFields,
    };
  }

  /**
   * Creates a new custom field.
   * @param createCustomFieldDto The custom field data to create.
   * @returns The created custom field.
   */
  async create(
    orgId: string,
    userId: string,
    createCustomFieldDto: CreateCustomFieldDto,
  ): Promise<CustomField> {
    const uid = IdGeneratorUtils.generate("CF");
    // eslint-disable-next-line max-len
    const customField = this.customFieldRepository.create({
      organization: {
        id: orgId,
      },
      organizationId: orgId,
      name: createCustomFieldDto.name,
      description: createCustomFieldDto.description,
      source: createCustomFieldDto.source,
      fieldType: createCustomFieldDto.fieldType,
      uid,
      options: createCustomFieldDto.options,
      metadata: createCustomFieldDto.metadata,
      placeholderText: createCustomFieldDto.placeholderText,
      hintText: createCustomFieldDto.hintText,
      mandatoryOnClose: createCustomFieldDto.mandatoryOnClose,
      mandatoryOnCreation: createCustomFieldDto.mandatoryOnCreation,
      visibleToCustomer: createCustomFieldDto.visibleToCustomer,
      editableByCustomer: createCustomFieldDto.editableByCustomer,
      version: 1,
      autoAddToAllForms: createCustomFieldDto.autoAddToAllForms,
      defaultValue: createCustomFieldDto.defaultValue,
      team: createCustomFieldDto.teamId
        ? {
            id: createCustomFieldDto.teamId,
          }
        : undefined,
      teamId: createCustomFieldDto.teamId,
      lookup: createCustomFieldDto.lookup,
      // validationRules: createCustomFieldDto.validationRules,
    });

    let res;
    try {
      res = await this.customFieldRepository.save(customField);
    } catch (error) {
      if (error instanceof CustomValidationError) {
        throw new BadRequestException(error.message);
      }
      throw error;
    }

    try {
      const formFieldEvents = [];
      formFieldEvents.push({
        organization: {
          id: orgId,
        },
        organizationId: orgId,
        entityId: res.uid,
        type: FormFieldEventTypes.FIELD_CREATED,
        data: res,
        user: {
          id: userId,
        },
        userId: userId,
        version: 1,
        createdAt: new Date(),
      });
      await this.sharedService.saveFormFieldEvents(formFieldEvents);
    } catch (error) {
      this.logger.error(
        `Error while trying to log custom field creation event: ${error.message}, stack: ${error.stack}`,
      );
      throw error;
    }
    return res;
  }

  /**
   * Updates existing custom fields in batch.
   * @param updates Array of custom field update data.
   * @returns The updated custom fields.
   */
  async update(
    request: FastifyRequest,
    organizationId: string,
    updates: UpdateCustomFieldDto,
  ) {
    if (!updates.fields || updates.fields.length === 0) {
      throw new BadRequestException("No fields to update");
    }
    const fieldEvents = [];

    const promises = updates.fields.map(async (update) => {
      const res = await this.customFieldRepository.update(
        { organizationId, uid: update.fieldId, version: update.version },
        { ...update.updates, version: update.version + 1 },
      );
      if (res.affected === 1) {
        try {
          const field = await this.customFieldRepository.findByCondition({
            where: { uid: update.fieldId },
          });
          fieldEvents.push({
            organization: {
              id: organizationId,
            },
            organizationId: organizationId,
            entityId: update.fieldId,
            type: FormFieldEventTypes.FIELD_UPDATED,
            data: field,
            user: {
              id: request.user.sub,
            },
            userId: request.user.sub,
            version: update.version + 1,
            createdAt: new Date(),
          });
        } catch (err) {
          this.logger.error(
            `Error while trying to add form field event in batch: ${err.message}, stack: ${err.stack}`,
          );
        }
      } else {
        throw new BadRequestException(
          `Failed to update field ${update.fieldId} due to version mismatch or concurrent modification`,
        );
      }
      return res;
    });

    const results = await Promise.allSettled(promises);

    if (fieldEvents.length > 0) {
      try {
        await this.sharedService.saveFormFieldEvents(fieldEvents);
      } catch (err) {
        this.logger.error(
          `Error while trying to log custom field deletion event: ${err.message}, stack: ${err.stack}`,
        );
      }
    }

    // Collect IDs of forms that failed to update
    const failedUpdates = results
      .map((result, index) =>
        result.status === "rejected" ? updates.fields[index].fieldId : null,
      )
      .filter((id) => id !== null);

    if (failedUpdates.length > 0) {
      throw new BadRequestException(
        `Failed to update the following IDs: ${failedUpdates.join(", ")}`,
      );
    }
  }

  /**
   * Delete fields by custom ids.
   * @param deleteCustomFieldDto The custom field list to delete.
   * @returns The custom field.
   */
  async delete(
    request: FastifyRequest,
    orgId: string,
    fields: DeleteCustomFieldDto,
  ) {
    if (_.isEmpty(orgId) || _.isEmpty(fields)) {
      throw new Error(`Params are missing: org: ${orgId}`);
    }

    const fieldEvents = [];

    const promises = fields.fields.map(async (op) => {
      const res = await this.customFieldRepository.update(
        { organizationId: orgId, uid: op.fieldId, version: op.version },
        { isDeleted: true, version: op.version + 1 },
      );

      if (res.affected === 1) {
        try {
          const field = await this.customFieldRepository.findByCondition({
            where: { uid: op.fieldId },
          });
          fieldEvents.push({
            organization: {
              id: orgId,
            },
            organizationId: orgId,
            entityId: op.fieldId,
            type: FormFieldEventTypes.FIELD_DELETED,
            data: field,
            user: {
              id: request.user.sub,
            },
            userId: request.user.sub,
            version: op.version + 1,
            createdAt: new Date(),
          });
        } catch (err) {
          this.logger.error(
            `Error while trying to add form field event in batch: ${err.message}, stack: ${err.stack}`,
          );
        }
      } else {
        throw new BadRequestException(
          `Failed to delete field ${op.fieldId} due to version mismatch or concurrent modification`,
        );
      }
    });

    const results = await Promise.allSettled(promises);

    try {
      await this.sharedService.saveFormFieldEvents(fieldEvents);
    } catch (err) {
      this.logger.error(
        `Error while trying to log custom field deletion event: ${err.message}, stack: ${err.stack}`,
      );
    }

    // Collect IDs of fields that failed to update
    const failedUpdates = results
      .map((result, index) =>
        result.status === "rejected" ? fields.fields[index].fieldId : null,
      )
      .filter((id) => id !== null);

    if (failedUpdates.length > 0) {
      throw new BadRequestException(
        `Some fields could not be deleted due to version mismatch or concurrent modification, failed fields: ${failedUpdates.join(
          ", ",
        )}`,
      );
    }

    return fields.fields;
  }

  /**
   * Returns all available field types and their corresponding subtypes
   * @returns A record of field types mapped to their possible subtypes
   */
  findAllTypes() {
    return Promise.resolve(CustomFieldTypeToNameMapping);
  }

  async getDefaultFields(orgId: string, teamId?: string) {
    const query = [
      {
        isDeleted: false,
        organizationId: orgId,
        source: CustomFieldSource.TICKET,
        autoAddToAllForms: true,
        teamId: IsNull(),
      },
    ];
    if (teamId) {
      query.push({
        isDeleted: false,
        organizationId: orgId,
        autoAddToAllForms: true,
        source: CustomFieldSource.TICKET,
        teamId: In([teamId]),
      });
    }
    const fields = await this.customFieldRepository.findAll({
      where: query,
    });
    return fields;
  }

  async findAllThenaRestrictedFields(
    orgId: string,
    teamId?: string,
    types?: string[],
  ) {
    const promises = [];
    const fieldTypes = [];
    if (
      !types ||
      types.length === 0 ||
      types.includes(ThenaRestrictedFieldType.TICKET_CREATION)
    ) {
      fieldTypes.push(ThenaRestrictedFieldType.TICKET_CREATION);
    }
    if (types?.includes(ThenaRestrictedFieldType.ESCALATION)) {
      fieldTypes.push(ThenaRestrictedFieldType.ESCALATION);
    }

    const fields = await this.thenaRestrictedFieldRepository.findAll({
      where: {
        isDeleted: false,
        type: In(fieldTypes),
      },
    });

    if (
      teamId &&
      fieldTypes.includes(ThenaRestrictedFieldType.TICKET_CREATION)
    ) {
      promises.push(
        this.sharedService.findAllTicketTypesForTeam(orgId, teamId),
      );
      promises.push(
        this.sharedService.findAllTicketStatusesForTeam(orgId, teamId),
      );
      promises.push(
        this.sharedService.findAllTicketPrioritiesForTeam(orgId, teamId),
      );

      const [ticketTypes, ticketStatuses, ticketPriorities] = await Promise.all(
        promises,
      );

      fields.forEach((field) => {
        if (field.name === ThenaRestrictedFieldNames.Type) {
          field.options = ticketTypes.map((type) => ({
            id: type.uid,
            value: type.name,
          }));
        }
        if (field.name === ThenaRestrictedFieldNames.Status) {
          field.options = ticketStatuses.map((status) => ({
            id: status.uid,
            value: status.name,
          }));
        }
        if (field.name === ThenaRestrictedFieldNames.Priority) {
          field.options = ticketPriorities.map((priority) => ({
            id: priority.uid,
            value: priority.name,
          }));
        }
      });
    }

    return fields;
  }
}
