import { BadRequestException } from "@nestjs/common";
import {
  FastifyAdapter,
  NestFastifyApplication,
} from "@nestjs/platform-fastify";
import { Test } from "@nestjs/testing";
import {
  CustomField,
  CustomFieldSource,
  CustomFieldType,
  Organization,
  User,
} from "@repo/thena-platform-entities";
import { AppModule } from "../../app.module";
import { CustomFieldvalidatorService } from "../validators/custom-field.validator";

const field: CustomField = {
  name: "Test Field",
  uid: "1",
  source: CustomFieldSource.TICKET,
  organization: new Organization(),
  organizationId: "",
  autoAddToAllForms: false,
  createdBy: new User(),
  updatedBy: new User(),
  id: "",
  fieldType: CustomFieldType.SINGLE_LINE,
  createdAt: undefined,
  updatedAt: undefined,
  isActive: false,
  isDeleted: false,
  options: [],
  placeholderText: "",
  hintText: "",
  mandatoryOnClose: false,
  mandatoryOnCreation: false,
  visibleToCustomer: false,
  editableByCustomer: false,
  version: 0,
  description: "",
  defaultValue: "",
  metadata: undefined,
  validateTypeAndSubtype: function (): Promise<void> {
    throw new Error("Function not implemented.");
  },
};

describe("CustomFieldvalidatorService - validateFieldValue", () => {
  let app: NestFastifyApplication;
  let service: CustomFieldvalidatorService;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = module.createNestApplication<NestFastifyApplication>(
      new FastifyAdapter(),
    );
    await app.init();
    service = app.get<CustomFieldvalidatorService>(CustomFieldvalidatorService);
  });

  afterAll(async () => {
    await app.close();
  });

  it("Single line field tests", () => {
    field.fieldType = CustomFieldType.SINGLE_LINE;
    expect(() => service.validateFieldValue(field, undefined)).not.toThrow();
    expect(() => service.validateFieldValue(field, "string")).toThrow(
      BadRequestException,
    );
    expect(() =>
      service.validateFieldValue(field, "string".repeat(100)),
    ).toThrow(BadRequestException);
    expect(() => service.validateFieldValue(field, ["string"])).not.toThrow(
      BadRequestException,
    );
  });

  it("Multi line field tests", () => {
    field.fieldType = CustomFieldType.MULTI_LINE;
    expect(() =>
      service.validateFieldValue(field, ["valid string"]),
    ).not.toThrow();
    expect(() => service.validateFieldValue(field, [""])).toThrow(
      BadRequestException,
    );
  });

  it("Rich text field tests", () => {
    field.fieldType = CustomFieldType.RICH_TEXT;
    expect(() =>
      service.validateFieldValue(field, ["valid rich text"]),
    ).not.toThrow();
    expect(() => service.validateFieldValue(field, [""])).toThrow(
      BadRequestException,
    );
  });

  it("Email field tests", () => {
    field.fieldType = CustomFieldType.EMAIL;
    expect(() =>
      service.validateFieldValue(field, ["<EMAIL>"]),
    ).not.toThrow();
    expect(() => service.validateFieldValue(field, ["invalid-email"])).toThrow(
      BadRequestException,
    );
  });

  it("Phone number field tests", () => {
    field.fieldType = CustomFieldType.PHONE_NUMBER;
    expect(() => service.validateFieldValue(field, [1234567890])).not.toThrow();
    expect(() => service.validateFieldValue(field, [123456789])).toThrow(
      BadRequestException,
    );
  });

  it("URL field tests", () => {
    field.fieldType = CustomFieldType.URL;
    expect(() =>
      service.validateFieldValue(field, ["https://example.com"]),
    ).not.toThrow();
    expect(() => service.validateFieldValue(field, ["invalid-url"])).toThrow(
      BadRequestException,
    );
  });

  it("Integer field tests", () => {
    field.fieldType = CustomFieldType.INTEGER;
    expect(() => service.validateFieldValue(field, ["123"])).not.toThrow();
    expect(() =>
      service.validateFieldValue(field, ["invalid-integer"]),
    ).toThrow(BadRequestException);
  });

  it("Decimal field tests", () => {
    field.fieldType = CustomFieldType.DECIMAL;
    expect(() => service.validateFieldValue(field, [123.45])).not.toThrow();
    expect(() =>
      service.validateFieldValue(field, ["invalid-decimal"]),
    ).toThrow(BadRequestException);
  });

  it("Currency field tests", () => {
    field.fieldType = CustomFieldType.CURRENCY;
    expect(() => service.validateFieldValue(field, [123.45])).not.toThrow();
    expect(() =>
      service.validateFieldValue(field, ["invalid-currency"]),
    ).toThrow(BadRequestException);
  });

  it("Date field tests", () => {
    field.fieldType = CustomFieldType.DATE;
    expect(() =>
      service.validateFieldValue(field, ["2023-01-01"]),
    ).not.toThrow();
    expect(() => service.validateFieldValue(field, ["invalid-date"])).toThrow(
      BadRequestException,
    );
  });

  it("Toggle field tests", () => {
    field.fieldType = CustomFieldType.BOOLEAN;
    expect(() => service.validateFieldValue(field, ["true"])).not.toThrow();
    expect(() => service.validateFieldValue(field, ["invalid-toggle"])).toThrow(
      BadRequestException,
    );
  });

  it("File upload field tests", () => {
    field.fieldType = CustomFieldType.FILE_UPLOAD; // Set fieldType to FILE
    expect(() => service.validateFieldValue(field, ["file.txt"])).not.toThrow();
  });

  it("Calculated field tests", () => {
    field.fieldType = CustomFieldType.CALCULATED; // Set fieldType to CALCULATED
    expect(() => service.validateFieldValue(field, [42])).not.toThrow();
  });

  it("Lookup field tests", () => {
    field.fieldType = CustomFieldType.LOOKUP; // Set fieldType to LOOKUP
    expect(() =>
      service.validateFieldValue(field, ["valid lookup value"]),
    ).not.toThrow();
  });

  it("Address field tests", () => {
    field.fieldType = CustomFieldType.ADDRESS; // Set fieldType to GEOGRAPHIC
    expect(() =>
      service.validateFieldValue(field, ["123 Main St, City, Country"]),
    ).not.toThrow();
  });

  it("Coordinates field tests", () => {
    field.fieldType = CustomFieldType.COORDINATES; // Set fieldType to GEOGRAPHIC
    expect(() =>
      service.validateFieldValue(field, ["40.7128,-74.0060"]),
    ).not.toThrow();
  });

  it("Rating field tests", () => {
    field.fieldType = CustomFieldType.RATING; // Set fieldType to RATING
    expect(() => service.validateFieldValue(field, [5])).not.toThrow();
  });
});
