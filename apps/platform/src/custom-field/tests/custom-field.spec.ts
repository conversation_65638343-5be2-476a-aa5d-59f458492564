import {
  HttpStatus,
  UnprocessableEntityException,
  ValidationPipe,
} from "@nestjs/common";
import {
  FastifyAdapter,
  NestFastifyApplication,
} from "@nestjs/platform-fastify";
import { Test } from "@nestjs/testing";
import {
  CustomFieldSource,
  CustomFieldType,
} from "@repo/thena-platform-entities";
import { t__loginIntoAuthService } from "@repo/thena-shared-libs";
import { DataSource } from "typeorm";
import { AppModule } from "../../app.module";
import { injectWithOrgId } from "../../utils/test-utils";
import {
  CreateCustomFieldDto,
  UpdateCustomFieldDto,
} from "../dto/custom-field.dto";

describe("Custom Fields", () => {
  let app: NestFastifyApplication;
  let userAuthToken: string;
  let userAuthTokenOfUser2: string;
  let conn: DataSource;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = module.createNestApplication<NestFastifyApplication>(
      new FastifyAdapter(),
    );

    app.useGlobalPipes(
      new ValidationPipe({
        transform: true,
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        exceptionFactory: (errors) => {
          return new UnprocessableEntityException(
            errors.map((error) => ({
              property: error.property,
              constraints: error.constraints,
            })),
          );
        },
      }),
    );

    await app.init();
    await app.getHttpAdapter().getInstance().ready();

    conn = app.get(DataSource);
    await conn.query(`
			INSERT INTO "thena_restricted_field" ("name", "field_type", "uid") VALUES ('Requestor', 'rich_text', 'the-1')`);
    await conn.query(`
				INSERT INTO "custom_field" ("name", "field_type", "source", "organization_id", uid, "auto_add_to_all_forms", "version") VALUES ('field-4', 'rich_text', 'ticket', ${global.testUser.organization_id}, 'cf-1', true, 1)`);

    // Log the admin user in
    userAuthToken = await t__loginIntoAuthService(
      global.testUser.email,
      global.testUser.password,
    );

    userAuthTokenOfUser2 = await t__loginIntoAuthService(
      global.testUser2.email,
      global.testUser2.password,
    );
  });

  afterAll(async () => {
    await conn.query(
      `DELETE FROM "thena_restricted_field" WHERE "uid" = 'the-1'`,
    );
    await conn.query(`DELETE FROM "custom_field" WHERE "uid" = 'cf-1'`);
    await app.close();
  });

  let totalFields = 1;
  let createdCustomField;
  let teamSpecificCustomField;

  describe("POST /v1/custom-field", () => {
    it("should create a new text custom field", async () => {
      const createCustomFieldDto: CreateCustomFieldDto = {
        name: "Test Text Field",
        source: CustomFieldSource.TICKET,
        fieldType: CustomFieldType.SINGLE_LINE,
        placeholderText: "Enter text here",
        hintText: "This is a hint",
        mandatoryOnCreation: true,
        mandatoryOnClose: false,
        visibleToCustomer: true,
        editableByCustomer: true,
        autoAddToAllForms: false,
        defaultValue: null,
      };

      const response = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/custom-field",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: createCustomFieldDto,
      });

      expect(response.statusCode).toBe(HttpStatus.CREATED);

      const result = response.json();
      expect(result.data.name).toBe(createCustomFieldDto.name);
      expect(result.data.fieldType).toBe(CustomFieldType.SINGLE_LINE);
      expect(result.data.id).toBeDefined();
      expect(result.data.id).toMatch(/^CF/);

      totalFields += 1;
      createdCustomField = result.data;
    });

    it("should not create a custom field with duplicate name", async () => {
      const duplicateFieldDto: CreateCustomFieldDto = {
        name: "Test Text Field",
        source: CustomFieldSource.TICKET,
        fieldType: CustomFieldType.SINGLE_LINE,
        placeholderText: "",
        hintText: "",
        mandatoryOnClose: false,
        mandatoryOnCreation: false,
        visibleToCustomer: false,
        editableByCustomer: false,
        autoAddToAllForms: false,
        defaultValue: "",
      };

      const response = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/custom-field",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: duplicateFieldDto,
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toContain("already exists");
    });

    it("should not allow mandatory on creation when visible to customer is false", async () => {
      const invalidFieldDto: CreateCustomFieldDto = {
        name: "Invalid Field",
        source: CustomFieldSource.TICKET,
        fieldType: CustomFieldType.SINGLE_LINE,
        mandatoryOnCreation: true,
        visibleToCustomer: false,
        placeholderText: "",
        hintText: "",
        mandatoryOnClose: false,
        editableByCustomer: false,
        autoAddToAllForms: false,
        defaultValue: "",
      };

      const response = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/custom-field",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: invalidFieldDto,
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toBe(
        "Mandatory on creation cannot be true when visible to customer is false",
      );
    });

    it("should create a select field with options", async () => {
      const selectFieldDto: CreateCustomFieldDto = {
        name: "Priority Level",
        source: CustomFieldSource.TICKET,
        fieldType: CustomFieldType.SINGLE_CHOICE,
        options: [
          {
            id: "High",
            value: "high",
            is_disabled: true,
            order: 0,
            platformField: false,
          },
          {
            id: "Medium",
            value: "medium",
            is_disabled: false,
            order: 0,
            platformField: false,
          },
          {
            id: "Low",
            value: "low",
            is_disabled: false,
            order: 0,
            platformField: false,
          },
        ],
        placeholderText: "",
        hintText: "",
        mandatoryOnClose: false,
        mandatoryOnCreation: false,
        visibleToCustomer: false,
        editableByCustomer: false,
        autoAddToAllForms: false,
        defaultValue: "",
      };

      const response = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/custom-field",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: selectFieldDto,
      });

      expect(response.statusCode).toBe(HttpStatus.CREATED);
      expect(response.json().data.options).toHaveLength(3);
      expect(response.json().data.options[0].id).toMatch(/^OP/);

      totalFields += 1;
    });

    it("should not create a custom field with a thena restricted field name", async () => {
      const restrictedFieldDto: CreateCustomFieldDto = {
        name: "Requestor",
        source: CustomFieldSource.TICKET,
        fieldType: CustomFieldType.SINGLE_LINE,
        placeholderText: "",
        hintText: "",
        mandatoryOnClose: false,
        mandatoryOnCreation: false,
        visibleToCustomer: false,
        editableByCustomer: false,
        autoAddToAllForms: false,
        defaultValue: "",
      };

      const response = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/custom-field",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: restrictedFieldDto,
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toBe(
        `${restrictedFieldDto.name} is thena restricted field name, cannot be used to create custom field`,
      );
    });

    it("should create a custom field for a specific team", async () => {
      const createCustomFieldDto: CreateCustomFieldDto = {
        name: "Team Specific Field",
        source: CustomFieldSource.TICKET,
        fieldType: CustomFieldType.SINGLE_LINE,
        placeholderText: "Enter team-specific text here",
        hintText: "This is a hint for the team",
        mandatoryOnCreation: true,
        mandatoryOnClose: false,
        visibleToCustomer: true,
        editableByCustomer: true,
        autoAddToAllForms: false,
        defaultValue: null,
        teamId: global.testTeam.uid,
      };

      const response = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/custom-field",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: createCustomFieldDto,
      });

      expect(response.statusCode).toBe(HttpStatus.CREATED);

      const result = response.json();
      teamSpecificCustomField = result.data;
      expect(result.data.name).toBe(createCustomFieldDto.name);
      expect(result.data.fieldType).toBe(CustomFieldType.SINGLE_LINE);
      expect(result.data.id).toBeDefined();
      expect(result.data.teamId).toBeDefined();

      totalFields += 1;
    });

    it("should not create a custom field for a team that the user does not belong to", async () => {
      const createCustomFieldDto: CreateCustomFieldDto = {
        name: "Team Specific Field",
        source: CustomFieldSource.TICKET,
        fieldType: CustomFieldType.SINGLE_LINE,
        placeholderText: "Enter team-specific text here",
        hintText: "This is a hint for the team",
        mandatoryOnCreation: true,
        mandatoryOnClose: false,
        visibleToCustomer: true,
        editableByCustomer: true,
        autoAddToAllForms: false,
        defaultValue: null,
        teamId: global.testTeam.uid,
      };

      const response = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/custom-field",
        headers: { Authorization: `Bearer ${userAuthTokenOfUser2}` },
        payload: createCustomFieldDto,
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toContain(
        "User is not a member of this team!",
      );
    });
  });

  describe("GET /v1/custom-field", () => {
    it("should fetch paginated custom fields", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/custom-field?limit=10&offset=0",
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(response.statusCode).toBe(HttpStatus.OK);
      const result = response.json();

      // Check pagination structure
      expect(result.data).toHaveProperty("results");
      expect(result.data).toHaveProperty("total");
      expect(Array.isArray(result.data.results)).toBe(true);
      expect(typeof result.data.total).toBe("number");
      expect(result.data.total).toBeGreaterThan(0);

      // If there are more results, offset should be present
      if (result.data.results.length === 10) {
        expect(result.offset).toBe(1);
      }
    });

    it("should respect custom pagination parameters", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/custom-field?limit=5&offset=0",
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(response.statusCode).toBe(HttpStatus.OK);
      const result = response.json();

      expect(Array.isArray(result.data.results)).toBe(true);
      expect(result.data.results.length).toBeLessThanOrEqual(5);
    });

    it("should fetch all custom fields", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/custom-field",
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(response.statusCode).toBe(HttpStatus.OK);
      expect(Array.isArray(response.json().data.results)).toBe(true);
      expect(response.json().data.results.length).toBeGreaterThan(0);
    });

    it("should fetch all custom fields for a team", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/custom-field?teamId=${global.testTeam.uid}&onlyTeamFields=true`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(response.statusCode).toBe(HttpStatus.OK);
      expect(Array.isArray(response.json().data.results)).toBe(true);
      expect(response.json().data.results.length).toBe(1);
    });

    it("should fetch all custom fields which either belongs to no team or belongs to the user provided team", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/custom-field?teamId=${global.testTeam.uid}`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(response.statusCode).toBe(HttpStatus.OK);
      expect(Array.isArray(response.json().data.results)).toBe(true);
      expect(response.json().data.results.length).toBe(totalFields);
    });
  });

  describe("GET /v1/custom-field/fetchByIds", () => {
    it("should fetch multiple custom fields by IDs", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/custom-field/fetchByIds?ids=${createdCustomField.id}`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(response.statusCode).toBe(HttpStatus.OK);
      const result = response.json();
      expect(Array.isArray(result.data)).toBe(true);
    });

    it("should return 404 for non-existent custom fields in batch", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/custom-field/fetchByIds?ids=CF_NON_EXISTENT,CF_ANOTHER_NON_EXISTENT", // Non-existent IDs
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toContain(
        "Some fields not found, either they are deleted or not found in your teams, missing fields:",
      );
    });

    it("should throw an error when a user tries to access custom fields of another team", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/custom-field/fetchByIds?ids=${teamSpecificCustomField.uid}`,
        headers: { Authorization: `Bearer ${userAuthTokenOfUser2}` },
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toContain(
        "Some fields not found, either they are deleted or not found in your teams, missing fields:",
      );
    });

    it("should allow a user to access custom fields of their own team", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/custom-field/fetchByIds?ids=${teamSpecificCustomField.id}`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(response.statusCode).toBe(HttpStatus.OK);
      const result = response.json();
      expect(Array.isArray(result.data)).toBe(true);
      expect(result.data.length).toBeGreaterThan(0);
    });
  });

  describe("PATCH /v1/custom-field", () => {
    it("should throw an error when no fields are provided for update", async () => {
      const response = await injectWithOrgId(app, {
        method: "PATCH",
        url: "/v1/custom-field",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {},
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toContain("No fields to update");
    });

    it("should throw an error when trying to update a field with a version mismatch", async () => {
      const updateDto: UpdateCustomFieldDto = {
        fields: [
          {
            fieldId: createdCustomField.id,
            version: 999,
            updates: {
              name: "Updated Name",
            },
          },
        ],
      };

      const response = await injectWithOrgId(app, {
        method: "PATCH",
        url: "/v1/custom-field",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: updateDto,
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toContain(
        "Some fields have been modified, Please try updating the latest version.",
      );
    });

    it("should throw an error when updating a name that is thena restricted", async () => {
      const updateDto: UpdateCustomFieldDto = {
        fields: [
          {
            fieldId: createdCustomField.id,
            version: createdCustomField.version,
            updates: {
              name: "Requestor",
            },
          },
        ],
      };

      const response = await injectWithOrgId(app, {
        method: "PATCH",
        url: "/v1/custom-field",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: updateDto,
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toContain(
        "Field name is reserved for thena restricted fields",
      );
    });

    it("should throw an error when there are duplicate names in the fields", async () => {
      const createCustomFieldDto: CreateCustomFieldDto = {
        name: "Test Text Field 2",
        source: CustomFieldSource.TICKET,
        fieldType: CustomFieldType.SINGLE_LINE,
        placeholderText: "Enter text here",
        hintText: "This is a hint",
        mandatoryOnCreation: true,
        mandatoryOnClose: false,
        visibleToCustomer: true,
        editableByCustomer: true,
        autoAddToAllForms: false,
        defaultValue: null,
      };

      const res = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/custom-field",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: createCustomFieldDto,
      });
      const field = res.json().data;

      const updateDto: UpdateCustomFieldDto = {
        fields: [
          {
            fieldId: createdCustomField.id,
            version: createdCustomField.version,
            updates: {
              name: "Duplicate Name",
            },
          },
          {
            fieldId: field.id,
            version: field.version,
            updates: {
              name: "Duplicate Name",
            },
          },
        ],
      };

      const response = await injectWithOrgId(app, {
        method: "PATCH",
        url: "/v1/custom-field",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: updateDto,
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toContain(
        "Duplicate field names are not allowed",
      );
    });

    it("should throw an error when mandatory on creation is true and visible in portal is false", async () => {
      const updateDto: UpdateCustomFieldDto = {
        fields: [
          {
            fieldId: createdCustomField.id,
            version: createdCustomField.version,
            updates: {
              mandatoryOnCreation: true,
              visibleToCustomer: false,
            },
          },
        ],
      };

      const response = await injectWithOrgId(app, {
        method: "PATCH",
        url: "/v1/custom-field",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: updateDto,
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toContain(
        "If mandatory on creation is true then visible to customer cannot be false.",
      );
    });

    it("should throw an error when visible to customer is false and editable by customer is true", async () => {
      const updateDto: UpdateCustomFieldDto = {
        fields: [
          {
            fieldId: createdCustomField.id,
            version: createdCustomField.version,
            updates: {
              visibleToCustomer: false,
              editableByCustomer: true,
            },
          },
        ],
      };

      const response = await injectWithOrgId(app, {
        method: "PATCH",
        url: "/v1/custom-field",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: updateDto,
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toContain(
        "If visible to customer is false then editable by customer cannot be true.",
      );
    });

    it("should successfully update a custom field", async () => {
      const updateDto: UpdateCustomFieldDto = {
        fields: [
          {
            fieldId: createdCustomField.id,
            version: createdCustomField.version,
            updates: {
              name: "Updated Custom Field Name",
            },
          },
        ],
      };

      const response = await injectWithOrgId(app, {
        method: "PATCH",
        url: "/v1/custom-field",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: updateDto,
      });

      expect(response.statusCode).toBe(HttpStatus.OK);
      createdCustomField.version += 1;
      const result = response.json();
      expect(result.data[0].name).toBe("Updated Custom Field Name");
    });

    it("should successfully update a custom field for the user's team", async () => {
      const updateDto: UpdateCustomFieldDto = {
        fields: [
          {
            fieldId: teamSpecificCustomField.id,
            version: teamSpecificCustomField.version,
            updates: {
              name: "Updated Team Specific Field Name",
              visibleToCustomer: true,
            },
          },
        ],
      };

      const response = await injectWithOrgId(app, {
        method: "PATCH",
        url: "/v1/custom-field",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: updateDto,
      });

      expect(response.statusCode).toBe(HttpStatus.OK);
      teamSpecificCustomField.version += 1;
      const result = response.json();
      expect(result.data[0].name).toBe("Updated Team Specific Field Name");
    });

    it("should throw an error when trying to update a custom field not belonging to the user's team", async () => {
      const updateDto: UpdateCustomFieldDto = {
        fields: [
          {
            fieldId: teamSpecificCustomField.id,
            version: teamSpecificCustomField.version,
            updates: {
              name: "Unauthorized Update Attempt",
            },
          },
        ],
      };

      const response = await injectWithOrgId(app, {
        method: "PATCH",
        url: "/v1/custom-field",
        headers: { Authorization: `Bearer ${userAuthTokenOfUser2}` },
        payload: updateDto,
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toContain(
        "You are trying to update fields of a team that you are not a part of, fieldId:",
      );
    });
  });

  describe("GET /v1/custom-field/types", () => {
    it("should return all custom field types and subtypes", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/custom-field/types",
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(response.statusCode).toBe(HttpStatus.OK);
      expect(response.json().data).toHaveProperty(CustomFieldType.SINGLE_LINE);
      expect(response.json().data).toHaveProperty(
        CustomFieldType.SINGLE_CHOICE,
      );
    });
  });

  describe("GET /v1/custom-field/search", () => {
    it("should return custom fields matching the search term", async () => {
      const searchTerm = createdCustomField.name;
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/custom-field/search?term=${searchTerm}`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(response.statusCode).toBe(HttpStatus.OK);
      const result = response.json();
      expect(Array.isArray(result.data)).toBe(true);
      expect(result.data.length).toBeGreaterThan(0);
    });

    it("should return an empty array for non-existent search term", async () => {
      const searchTerm = "non-existent-term";
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/custom-field/search?term=${searchTerm}`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(response.statusCode).toBe(HttpStatus.OK);
      const result = response.json();
      expect(result.data).toHaveLength(0);
    });

    it("should return an error if no search term is provided", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: "/v1/custom-field/search",
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toContain("No search term provided");
    });

    it("should return only team-specific fields when teamId is provided", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/custom-field/search?term=field&teamId=${global.testTeam.uid}&onlyTeamFields=true`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(response.statusCode).toBe(HttpStatus.OK);
      const result = response.json();
      expect(Array.isArray(result.data)).toBe(true);
      expect(result.data.length).toBe(1);
    });

    it("should return both organization and team fields when no teamId is provided", async () => {
      const response = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/custom-field/search?term=field&teamId=${global.testTeam.uid}`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(response.statusCode).toBe(HttpStatus.OK);
      const result = response.json();
      expect(Array.isArray(result.data)).toBe(true);
      expect(result.data.length).toBe(totalFields);
    });
  });

  describe("POST /v1/custom-field/delete", () => {
    it("should throw an error when trying to delete a field with a version mismatch", async () => {
      const response = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/custom-field/delete",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          fields: [
            {
              fieldId: createdCustomField.id,
              version: 999,
            },
          ],
        },
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toContain(
        "Some fields have been modified, Please try updating the latest version. Modified fields:",
      );
    });

    it("should not allow deletion of a thena restricted field", async () => {
      const response = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/custom-field/delete",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          fields: [
            {
              fieldId: "the-1",
              version: 1,
            },
          ],
        },
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toContain(
        "Cannot delete thena restricted field",
      );
    });

    it("should throw an error when trying to delete an unknown field", async () => {
      const response = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/custom-field/delete",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          fields: [
            {
              fieldId: "unknown-field",
              version: 999,
            },
          ],
        },
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toContain("Fields not found:");
    });

    it("should throw an error when payload contains duplicate fieldIds", async () => {
      const response = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/custom-field/delete",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          fields: [
            {
              fieldId: createdCustomField.id,
              version: createdCustomField.version,
            },
            {
              fieldId: createdCustomField.id,
              version: createdCustomField.version,
            },
          ],
        },
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toContain(
        "Duplicate field IDs are not allowed. Please provide unique field IDs.",
      );
    });

    it("should throw an error when trying to delete a field for which autoAddToAllForms is true", async () => {
      const response = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/custom-field/delete",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          fields: [
            {
              fieldId: createdCustomField.id,
              version: createdCustomField.version,
            },
            {
              fieldId: "cf-1",
              version: 1,
            },
          ],
        },
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toContain(
        "cannot be deleted as they are configured to be automatically added to all forms",
      );
    });

    it("should delete a custom field", async () => {
      const response = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/custom-field/delete",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          fields: [
            {
              fieldId: createdCustomField.id,
              version: createdCustomField.version,
            },
          ],
        },
      });

      expect(response.statusCode).toBe(HttpStatus.NO_CONTENT);
      createdCustomField.version += 1;

      const getResponse = await injectWithOrgId(app, {
        method: "GET",
        url: `/v1/custom-field/${createdCustomField.id}`,
        headers: { Authorization: `Bearer ${userAuthToken}` },
      });

      expect(getResponse.statusCode).toBe(HttpStatus.NOT_FOUND);
    });

    it("should not allow deletion of a custom field if the user is not from the same team", async () => {
      const response = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/custom-field/delete",
        headers: { Authorization: `Bearer ${userAuthTokenOfUser2}` },
        payload: {
          fields: [
            {
              fieldId: teamSpecificCustomField.id,
              version: teamSpecificCustomField.version,
            },
          ],
        },
      });

      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.json().message).toContain(
        "You are trying to delete fields of a team that you are not a part of, fieldId:",
      );
    });

    it("should allow deletion of a custom field if the user is from the same team", async () => {
      const response = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/custom-field/delete",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          fields: [
            {
              fieldId: teamSpecificCustomField.id,
              version: teamSpecificCustomField.version,
            },
          ],
        },
      });

      expect(response.statusCode).toBe(HttpStatus.NO_CONTENT);
    });
  });
});
