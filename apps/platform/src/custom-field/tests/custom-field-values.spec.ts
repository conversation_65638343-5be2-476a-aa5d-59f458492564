import {
  FastifyAdapter,
  NestFastifyApplication,
} from "@nestjs/platform-fastify";
import { Test } from "@nestjs/testing";
import { CustomValidationError } from "@repo/thena-platform-entities/common/base-error/custom-validation-error";
import { t__loginIntoAuthService } from "@repo/thena-shared-libs";
import { AppModule } from "../../app.module";
import { injectWithOrgId } from "../../utils/test-utils";
import { CustomFieldValuesService } from "../services/custom-field-values.service";

describe("CustomFieldValuesService", () => {
  let app: NestFastifyApplication;
  let service: CustomFieldValuesService;
  let userAuthToken: string;
  let customField: any;
  let customFieldEmail: any;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    service = module.get<CustomFieldValuesService>(CustomFieldValuesService);
    app = module.createNestApplication<NestFastifyApplication>(
      new FastifyAdapter(),
    );

    await app.init();
    await app.getHttpAdapter().getInstance().ready();

    // Get auth token
    userAuthToken = await t__loginIntoAuthService(
      global.testUser.email,
      global.testUser.password,
    );

    // Create custom field text single_line
    const createCustomFieldResponse = await injectWithOrgId(app, {
      method: "POST",
      url: "/v1/custom-field",
      headers: {
        Authorization: `Bearer ${userAuthToken}`,
      },
      payload: {
        source: "ticket",
        fieldType: "text",
        subFieldType: "single_line",
        name: "custom_text_4",
      },
    });

    customField = createCustomFieldResponse.json();

    // Create custom field specialized email
    const createCustomFieldEmailResponse = await injectWithOrgId(app, {
      method: "POST",
      url: "/v1/custom-field",
      headers: {
        Authorization: `Bearer ${userAuthToken}`,
      },
      payload: {
        source: "ticket",
        fieldType: "specialized",
        subFieldType: "email",
        name: "email+1",
      },
    });

    customFieldEmail = createCustomFieldEmailResponse.json();
  });

  afterAll(async () => {
    await app.close();
  });

  it("create custom field value for single_line", async () => {
    const customFieldValue = await service.create(
      global.testUser.organization_id,
      {
        customFieldId: customField.uid,
        organizationId: global.testUser.organization_id,
        data: [
          {
            value: "test",
          },
        ],
      },
    );

    const expectedResult = {
      createdAt: expect.any(Date),
      customField: {
        createdAt: expect.any(Date),
        fieldType: "text",
        id: expect.any(String),
        isActive: true,
        metadata: null,
        name: "custom_text_4",
        options: null,
        organizationId: expect.any(String),
        source: "ticket",
        subFieldType: "single_line",
        uid: expect.any(String),
        updatedAt: expect.any(Date),
      },
      data: [
        {
          value: "test",
        },
      ],
      id: expect.any(String),
      metadata: null,
      organization: {
        id: expect.any(String),
      },
      organizationId: expect.any(String),
      updatedAt: expect.any(Date),
    };

    expect(customFieldValue).toEqual(expectedResult);
  });

  it("creation of  custom field value for customFieldEmail", async () => {
    try {
      const customFieldValue = await service.create(
        global.testUser.organization_id,
        {
          customFieldId: customFieldEmail.uid,
          organizationId: global.testUser.organization_id,
          data: [
            {
              value: "<EMAIL>",
            },
          ],
        },
      );

      const expectedResult = {
        createdAt: expect.any(Date),
        customField: {
          createdAt: expect.any(Date),
          fieldType: "specialized",
          id: expect.any(String),
          isActive: true,
          metadata: null,
          name: "email+1",
          options: null,
          organizationId: expect.any(String),
          source: "ticket",
          subFieldType: "email",
          uid: expect.any(String),
          updatedAt: expect.any(Date),
        },
        data: [
          {
            value: "<EMAIL>",
          },
        ],
        id: expect.any(String),
        metadata: null,
        organization: {
          id: expect.any(String),
        },
        organizationId: expect.any(String),
        updatedAt: expect.any(Date),
      };

      expect(customFieldValue).toEqual(expectedResult);
    } catch (error) {
      expect(error).toBeInstanceOf(CustomValidationError);
      expect(error.message).toBe("Invalid email format for value: abs");
    }
  });

  it("creation of  custom field value for customFieldEmail failed", async () => {
    try {
      await service.create(global.testUser.organization_id, {
        customFieldId: customFieldEmail.uid,
        organizationId: global.testUser.organization_id,
        data: [
          {
            value: "abs",
          },
        ],
      });
    } catch (error) {
      expect(error).toBeInstanceOf(CustomValidationError);
      expect(error.message).toBe("Invalid email format for value: abs");
    }
  });

  describe("Date and Time validations", () => {
    let dateCustomField: any;
    let timeCustomField: any;

    beforeAll(async () => {
      // Create date custom field
      const createDateFieldResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/custom-field",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          source: "ticket",
          fieldType: "specialized",
          subFieldType: "date",
          name: "due_date",
        },
      });
      dateCustomField = createDateFieldResponse.json();

      // Create time custom field
      const createTimeFieldResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/custom-field",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          source: "ticket",
          fieldType: "specialized",
          subFieldType: "time",
          name: "meeting_time",
        },
      });
      timeCustomField = createTimeFieldResponse.json();
    });

    it("should validate correct date format", async () => {
      const result = await service.create(global.testUser.organization_id, {
        customFieldId: dateCustomField.uid,
        organizationId: global.testUser.organization_id,
        data: [{ value: "2024-03-20" }],
      });
      expect(result.data[0].value).toBeDefined();
    });

    it("should reject invalid date format", async () => {
      await expect(
        service.create(global.testUser.organization_id, {
          customFieldId: dateCustomField.uid,
          organizationId: global.testUser.organization_id,
          data: [{ value: "invalid-date" }],
        }),
      ).rejects.toThrow(CustomValidationError);
    });

    it("should validate correct time format", async () => {
      const result = await service.create(global.testUser.organization_id, {
        customFieldId: timeCustomField.uid,
        organizationId: global.testUser.organization_id,
        data: [{ value: "14:30" }],
      });
      expect(result.data[0].value).toBeDefined();
    });

    it("should reject invalid time format", async () => {
      await expect(
        service.create(global.testUser.organization_id, {
          customFieldId: timeCustomField.uid,
          organizationId: global.testUser.organization_id,
          data: [{ value: "25:70" }],
        }),
      ).rejects.toThrow(Error);
    });
  });

  describe("Number validations", () => {
    let integerField: any;
    let decimalField: any;

    beforeAll(async () => {
      // Create integer custom field
      const createIntegerFieldResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/custom-field",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          source: "ticket",
          fieldType: "specialized",
          subFieldType: "integer",
          name: "quantity",
        },
      });
      integerField = createIntegerFieldResponse.json();

      // Create decimal custom field
      const createDecimalFieldResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/custom-field",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          source: "ticket",
          fieldType: "specialized",
          subFieldType: "decimal",
          name: "price",
        },
      });
      decimalField = createDecimalFieldResponse.json();
    });

    it("should validate integer values", async () => {
      const result = await service.create(global.testUser.organization_id, {
        customFieldId: integerField.uid,
        organizationId: global.testUser.organization_id,
        data: [{ value: "42" }],
      });
      expect(result.data[0].value).toBe("42");
    });

    it("should reject non-integer values", async () => {
      await expect(
        service.create(global.testUser.organization_id, {
          customFieldId: integerField.uid,
          organizationId: global.testUser.organization_id,
          data: [{ value: "42.5" }],
        }),
      ).rejects.toThrow(Error);
    });

    it("should validate decimal values", async () => {
      const result = await service.create(global.testUser.organization_id, {
        customFieldId: decimalField.uid,
        organizationId: global.testUser.organization_id,
        data: [{ value: "42.50" }],
      });
      expect(result.data[0].value).toBeDefined();
    });
  });

  describe("Text validations", () => {
    it("should reject single line text exceeding 100 words", async () => {
      const longText = Array(101).fill("word").join(" ");
      await expect(
        service.create(global.testUser.organization_id, {
          customFieldId: customField.uid,
          organizationId: global.testUser.organization_id,
          data: [{ value: longText }],
        }),
      ).rejects.toThrow(CustomValidationError);
    });
  });

  describe("URL and IP Address validations", () => {
    let urlField: any;
    let ipField: any;

    beforeAll(async () => {
      // Create URL custom field
      const createUrlFieldResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/custom-field",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          source: "ticket",
          fieldType: "specialized",
          subFieldType: "url",
          name: "website",
        },
      });
      urlField = createUrlFieldResponse.json();

      // Create IP address custom field
      const createIpFieldResponse = await injectWithOrgId(app, {
        method: "POST",
        url: "/v1/custom-field",
        headers: { Authorization: `Bearer ${userAuthToken}` },
        payload: {
          source: "ticket",
          fieldType: "specialized",
          subFieldType: "ip_address",
          name: "server_ip",
        },
      });
      ipField = createIpFieldResponse.json();
    });

    it("should validate correct URL format", async () => {
      const result = await service.create(global.testUser.organization_id, {
        customFieldId: urlField.uid,
        organizationId: global.testUser.organization_id,
        data: [{ value: "https://example.com" }],
      });
      expect(result.data[0].value).toBe("https://example.com");
    });

    it("should validate correct IP address format", async () => {
      const result = await service.create(global.testUser.organization_id, {
        customFieldId: ipField.uid,
        organizationId: global.testUser.organization_id,
        data: [{ value: "***********" }],
      });
      expect(result.data[0].value).toBe("***********");
    });
  });
});
