import { Logger } from "@nestjs/common";
import {
  IsDateString,
  IsDecimal,
  IsEmail,
  IsIP,
  IsNumber,
  IsUrl,
} from "class-validator";

export class DateValidation {
  @IsDateString()
  date: string;
}

export class EmailValidation {
  @IsEmail()
  email: string;
}

export class UrlValidation {
  @IsUrl()
  url: string;
}

export class IpAddressValidation {
  @IsIP()
  ipAddress: string;
}

export class NumberValidation {
  @IsNumber()
  number: number;
}

export class DecimalValidation {
  @IsDecimal()
  decimal: number;
}

export const isValidString = (value: string, trim = true) => {
  if (!value || typeof value !== "string") return false;

  if (trim) value = value.trim();

  return value.length > 0;
};

export const isValidEmail = (value: string) => {
  if (!isValidString(value)) return false;

  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
};

export const isValidUrl = (string: string): boolean => {
  try {
    // eslint-disable-next-line no-new
    new URL(string);
    return true;
  } catch (err) {
    Logger.error(err);
    return false;
  }
};

export const isValidDate = (value: any): boolean => {
  const date = new Date(value);
  return (
    !Number.isNaN(date.getTime()) &&
    date.getHours() === 0 &&
    date.getMinutes() === 0 &&
    date.getSeconds() === 0 &&
    date.getMilliseconds() === 0
  );
};

export const isValidDateTime = (value: any): boolean => {
  const dateTime = new Date(value);
  return !Number.isNaN(dateTime.getTime());
};

export const isValidNumber = (value: any): boolean => {
  return !Number.isNaN(parseFloat(value)) && Number.isFinite(value);
};

export const isValidPassword = (value: string): boolean => {
  const passwordRegex =
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
  return passwordRegex.test(value);
};

export const isValidPhoneNumber = (value: string): boolean => {
  // Phone number validation rules
  const phoneRegex = /^(\+\d{1,2}\s?)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$/;
  return phoneRegex.test(value);
};

export const isValidRegex = (value: string): boolean => {
  try {
    // eslint-disable-next-line no-new
    new RegExp(value);
    return true;
  } catch (e) {
    Logger.error(e);
    return false;
  }
};
