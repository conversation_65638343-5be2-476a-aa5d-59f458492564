import {
  BadRequestException,
  Body,
  Controller,
  DefaultValuePipe,
  Get,
  Inject,
  NotFoundException,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Req,
  UseInterceptors,
} from "@nestjs/common";

import { ApiBody, ApiOperation, ApiTags } from "@nestjs/swagger";
import {
  ApiCreateEndpoint,
  ApiDeleteEndpoint,
  ApiGetEndpoint,
  ApiResponseMessage,
  ApiUpdateEndpoint,
  ResponseTransformInterceptor,
} from "@repo/nestjs-commons/decorators";
import { RequiredTier } from "@repo/nestjs-commons/guards/organization-tier/index";
import { ILogger } from "@repo/nestjs-commons/logger";
import { CustomFieldSource, OrganizationTier } from "@repo/thena-platform-entities";
import { FastifyRequest } from "fastify";
import { SkipAllThrottler } from "../../common/decorators/throttler.decorator";
import { TeamsService } from "../../teams/services/teams.service";
import {
  BatchCustomFieldResponseDto,
  CreateCustomFieldDto,
  CustomFieldResponseDto,
  DeleteCustomFieldDto,
  GetAllCustomFieldsResponse,
  GetAllCustomFieldTypesResponse,
  ThenaRestrictedFieldData,
  UpdateCustomFieldDto,
} from "../dto/custom-field.dto";
import { CustomFieldService } from "../services/custom-field.service";
import { CustomFieldvalidatorService } from "../validators/custom-field.validator";

@ApiTags("Custom fields")
@UseInterceptors(ResponseTransformInterceptor)
@Controller("v1/custom-field")
@SkipAllThrottler()
export class CustomFieldController {
  constructor(
    @Inject("CustomLogger")
    private readonly logger: ILogger,

    private readonly customFieldService: CustomFieldService,
    private readonly customFieldValidatorService: CustomFieldvalidatorService,
    private readonly teamsService: TeamsService,
  ) {}

  @Post()
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiBody({ type: CreateCustomFieldDto })
  @ApiResponseMessage("Custom field created successfully!")
  @ApiCreateEndpoint({
    summary: "Create a custom field",
    responseType: CustomFieldResponseDto,
  })
  async create(
    @Body() createCustomFieldDto: CreateCustomFieldDto,
    @Req() request: FastifyRequest,
  ) {
    try {
      if (createCustomFieldDto.teamId) {
        const team = await this.validateAndFetchTeam(
          createCustomFieldDto.teamId,
          request.user.orgId,
        );
        createCustomFieldDto.teamId = team?.id;

        await this.checkIfUserBelongsToTeam(
          request.user.sub,
          team.id,
          request.user.orgId,
        );
      }

      await this.customFieldValidatorService.validateCreatePayload(
        request.user.orgId,
        createCustomFieldDto,
      );
      const customField = await this.customFieldService.create(
        request.user.orgId,
        request.user.sub,
        createCustomFieldDto,
      );

      const returnableCustomField = await this.customFieldService.findByIds(
        request.user.orgId,
        [customField.uid],
      );

      return CustomFieldResponseDto.fromEntity(returnableCustomField.items[0]);
    } catch (err) {
      this.logger.error(
        `Error while trying to create custom field: ${err.message}, stack: ${err.stack}`,
      );
      throw err;
    }
  }

  @Get()
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Custom fields fetched successfully!")
  @ApiGetEndpoint({
    summary: "Get all custom fields",
    responseType: GetAllCustomFieldsResponse,
  })
  async findAll(
    @Req() request: FastifyRequest,
    @Query("limit", new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query("page", new DefaultValuePipe(0), ParseIntPipe) page: number,
    @Query("teamId") teamId?: string,
    @Query("source") source?: CustomFieldSource,
    @Query("onlyTeamFields") onlyTeamFields?: boolean,
  ) {
    try {
      let team;
      if (teamId) {
        team = await this.validateAndFetchTeam(teamId, request.user.orgId);
        await this.checkIfUserBelongsToTeam(
          request.user.sub,
          team?.id,
          request.user.orgId,
        );
      }

      const customFields = await this.customFieldService.fetchPaginatedResults(
        request.user.orgId,
        limit,
        page,
        team?.id,
        onlyTeamFields,
        source,
      );
      const responseItems = (customFields.results || []).map((item) =>
        CustomFieldResponseDto.fromEntity(item),
      );
      return { ...customFields, results: responseItems };
    } catch (err) {
      this.logger.error(
        `Error while trying to fetch custom fields: ${err.message}, stack: ${err.stack}`,
      );
      throw err;
    }
  }

  @Get("/fetchByIds")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Custom fields fetched successfully!")
  @ApiGetEndpoint({
    summary: "Get custom fields by IDs",
    responseType: GetAllCustomFieldsResponse,
  })
  async findByIds(
    @Req() request: FastifyRequest,
    @Query("ids") ids: string | string[],
  ) {
    try {
      if (!ids) {
        throw new BadRequestException("No IDs provided");
      }

      const idArray = typeof ids === "string" ? [ids] : ids;

      if (idArray.length === 0) {
        throw new BadRequestException("No IDs provided");
      }
      const teams = await this.teamsService.getTeamsByUser(request.user);
      const teamIds = teams.map((team) => team.teamId);
      const customFields = await this.customFieldService.findByIdsWithTeamCheck(
        request.user.orgId,
        idArray,
        teamIds,
      );
      return (customFields.items || []).map(CustomFieldResponseDto.fromEntity);
    } catch (err) {
      this.logger.error(
        `Error while trying to fetch custom fields by IDs: ${err.message}, stack: ${err.stack}`,
      );
      throw err;
    }
  }

  @Get("/search")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiGetEndpoint({
    summary: "Search custom field using name",
    responseType: GetAllCustomFieldsResponse,
  })
  async search(
    @Req() request: FastifyRequest,
    @Query("term") term: string,
    @Query("teamId") teamId?: string,
    @Query("onlyTeamFields") onlyTeamFields?: boolean,
  ) {
    try {
      let team;
      if (teamId) {
        team = await this.validateAndFetchTeam(teamId, request.user.orgId);
        await this.checkIfUserBelongsToTeam(
          request.user.sub,
          team?.id,
          request.user.orgId,
        );
      }
      const customFields = await this.customFieldService.search(
        request,
        term,
        team?.id,
        onlyTeamFields,
      );
      return (customFields.items || []).map(CustomFieldResponseDto.fromEntity);
    } catch (err) {
      this.logger.error(
        `Error while trying to search custom fields: ${err.message}, stack: ${err.stack}`,
      );
      throw err;
    }
  }

  @Patch()
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Custom fields updated successfully!")
  @ApiUpdateEndpoint({
    summary: "Update custom fields",
    responseType: BatchCustomFieldResponseDto,
  })
  async update(
    @Req() request: FastifyRequest,
    @Body() updates: UpdateCustomFieldDto,
  ) {
    try {
      const teams = await this.teamsService.getTeamsByUser(request.user);
      const teamIds = teams.map((team) => team.teamId);
      const organizationId = request.user.orgId;
      await this.customFieldValidatorService.validateBatchUpdatePayload(
        organizationId,
        updates,
        teamIds,
      );
      await this.customFieldService.update(request, organizationId, updates);
      const customFields = await this.customFieldService.findByIdsWithTeamCheck(
        request.user.orgId,
        updates.fields.map((field) => field.fieldId),
        teamIds,
      );
      return (customFields.items || []).map(CustomFieldResponseDto.fromEntity);
    } catch (err) {
      this.logger.error(
        `Error while trying to update custom fields: ${err.message}, stack: ${err.stack}`,
      );
      throw err;
    }
  }

  @Post("/delete")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Custom fields deleted successfully!")
  @ApiDeleteEndpoint({
    summary: "Delete custom fields",
  })
  async delete(
    @Req() request: FastifyRequest,
    @Body() fields: DeleteCustomFieldDto,
  ) {
    try {
      const teams = await this.teamsService.getTeamsByUser(request.user);
      const teamIds = teams.map((team) => team.teamId);
      const organizationId = request.user.orgId;
      await this.customFieldValidatorService.validateBatchDeletePayload(
        organizationId,
        fields,
        teamIds,
      );
      await this.customFieldService.delete(request, organizationId, fields);
    } catch (err) {
      this.logger.error(
        `Error while trying to delete custom fields: ${err.message}, stack: ${err.stack}`,
      );
      throw err;
    }
  }

  @Get("/types")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Custom field types fetched successfully!")
  @ApiGetEndpoint({
    summary: "Get all custom field types",
    responseType: GetAllCustomFieldTypesResponse,
  })
  findAllTypes() {
    return this.customFieldService.findAllTypes();
  }

  @Get("/thena-restricted-fields")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Thena restricted fields fetched successfully!")
  @ApiGetEndpoint({
    summary: "Get all thena restricted fields",
  })
  async findAllThenaRestrictedFields(
    @Req() request: FastifyRequest,
    @Query("teamId") teamId?: string,
    @Query("types") types?: string[],
  ) {
    const team = await this.validateAndFetchTeam(teamId, request.user.orgId);
    const fields = await this.customFieldService.findAllThenaRestrictedFields(
      request.user.orgId,
      team?.id,
      types,
    );
    return fields.map(ThenaRestrictedFieldData.fromEntity);
  }
  /**
   * Validates and fetches a team by its ID.
   * @param teamId The ID of the team to fetch.
   * @returns The team.
   */
  private async validateAndFetchTeam(teamId: string, organizationId: string) {
    if (!teamId) {
      return null;
    }
    const team = await this.teamsService.findOneByTeamId(
      teamId,
      organizationId,
    );

    if (!team) {
      throw new NotFoundException("Team not found!");
    }

    return team;
  }

  private async checkIfUserBelongsToTeam(
    userId: string,
    teamId: string,
    organizationId: string,
  ) {
    const teamMember = await this.teamsService.userBelongsToTeam(
      userId,
      teamId,
      organizationId,
    );

    if (!teamMember) {
      throw new BadRequestException("User is not a member of this team!");
    }
  }
}
