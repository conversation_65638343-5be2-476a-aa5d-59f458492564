import { <PERSON>ada<PERSON> } from "@grpc/grpc-js";
import {
  BadRequestException,
  Controller,
  Inject,
  NotFoundException,
  UseGuards,
} from "@nestjs/common";
import { GrpcMethod } from "@nestjs/microservices";
import { handleRpcError } from "@repo/nestjs-commons/errors";
import { GrpcAuthGuard } from "@repo/nestjs-commons/guards";
import { ILogger } from "@repo/nestjs-commons/logger";
import { extractUserMetadata } from "@repo/nestjs-commons/utils";
import { customfields } from "@repo/shared-proto";
import {
  CustomField,
  CustomFieldSource,
  Team,
} from "@repo/thena-platform-entities";
import { TeamsService } from "../../teams/services/teams.service";
import { CustomFieldService } from "../services/custom-field.service";
import { CustomFieldvalidatorService } from "../validators/custom-field.validator";

@Controller("v1/custom-field")
@UseGuards(GrpcAuthGuard)
export class CustomFieldsGrpcController {
  constructor(
    @Inject("CustomLogger")
    private readonly logger: ILogger,

    private readonly customFieldService: CustomFieldService,
    private readonly customFieldValidatorService: CustomFieldvalidatorService,
    private readonly teamsService: TeamsService,
  ) {}

  private async validateAndFetchTeam(teamId: string, organizationId: string) {
    const team = await this.teamsService.findOneByTeamId(
      teamId,
      organizationId,
    );

    if (!team) {
      throw new NotFoundException("Team not found!");
    }

    return team;
  }

  private async checkIfUserBelongsToTeam(
    userId: string,
    teamId: string,
    organizationId: string,
  ) {
    const teamMember = await this.teamsService.userBelongsToTeam(
      userId,
      teamId,
      organizationId,
    );

    if (!teamMember) {
      throw new BadRequestException("User is not a member of this team!");
    }
  }

  @GrpcMethod(customfields.CUSTOM_FIELDS_SERVICE_NAME, "FindAllCustomFields")
  async getCustomFields(
    request: customfields.FindAllCustomFieldsRequest,
    metadata: Metadata,
  ): Promise<customfields.FindAllCustomFieldsResponse> {
    try {
      const user = extractUserMetadata(metadata);

      let team: Team;
      if (request.teamId) {
        team = await this.validateAndFetchTeam(request.teamId, user.orgId);
        await this.checkIfUserBelongsToTeam(user.sub, team?.id, user.orgId);
      }

      const customFields = await this.customFieldService.fetchPaginatedResults(
        user.orgId,
        request.limit,
        request.offset,
        team?.id,
        request.onlyTeamFields,
        request.source as CustomFieldSource,
      );

      const customFieldsGRPCResponse = customFields.results?.map(
        this.customFieldToGRPCResponse,
      );

      return {
        results: customFieldsGRPCResponse,
        total: customFields.total,
        offset: customFields.page,
      };
    } catch (err) {
      this.logger.error(
        `Error while trying to fetch custom fields: ${err.message}, stack: ${err.stack}`,
      );
      handleRpcError(err);
    }
  }

  private customFieldToGRPCResponse(
    customField: CustomField,
  ): customfields.CustomField {
    return {
      id: customField.uid,
      name: customField.name,
      description: customField.description,
      fieldType: customField.fieldType,

      options: customField.options?.map((option) => ({
        id: option.id,
        value: option.value,
        isDisabled: option.is_disabled,
        order: option.order,
      })),
      placeholderText: customField.placeholderText,
      hintText: customField.hintText,
      defaultValue: customField.defaultValue,
      lookup: customField.lookup,

      mandatoryOnClose: customField.mandatoryOnClose,
      mandatoryOnCreation: customField.mandatoryOnCreation,
      visibleToCustomer: customField.visibleToCustomer,
      editableByCustomer: customField.editableByCustomer,

      metadata: JSON.stringify(customField.metadata ?? {}),
      validationRules: JSON.stringify(customField.validationRules ?? {}),
    };
  }
}
