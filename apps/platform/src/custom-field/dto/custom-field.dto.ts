import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
  CustomField,
  CustomFieldOption,
  CustomFieldSource,
  CustomFieldType,
  ThenaRestrictedField,
  ValidationType,
} from "@repo/thena-platform-entities";
import { Type } from "class-transformer";
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsEnum,
  IsInt,
  IsObject,
  IsOptional,
  IsString,
  Matches,
  MaxLength,
  <PERSON><PERSON>ength,
  ValidateNested,
} from "class-validator";

export const CustomFieldValidationTypesAllowedForFieldType = {
  ALL: [ValidationType.REQUIRED],
  [CustomFieldType.SINGLE_LINE]: [
    ValidationType.MIN_LENGTH,
    ValidationType.MAX_LENGTH,
  ],
  [CustomFieldType.MULTI_LINE]: [
    ValidationType.MIN_LENGTH,
    ValidationType.MAX_LENGTH,
  ],
};

class CommonCustomFieldDto {
  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ description: "Whether the custom field is active" })
  isActive?: boolean;
}

// function IsValidValidationRule(validationOptions?: ValidationOptions) {
//   return function (object: object, propertyName: string) {
//     registerDecorator({
//       name: "isValidValidationRule",
//       target: object.constructor,
//       propertyName: propertyName,
//       options: validationOptions,
//       validator: {
//         validate(value: any, args: ValidationArguments) {
//           for (const [validationType, rule] of Object.entries(value)) {
//             switch (validationType) {
//               case ValidationType.MIN_LENGTH:
//                 break;
//               case ValidationType.MAX_LENGTH:
//                 break;
//             }
//           }

//           return true;
//         },
//         defaultMessage(args: ValidationArguments) {
//           return "Each validation rule must have a type and value as strings, or boolean/number based on the type. Min length cannot be greater than max length.";
//         },
//       },
//     });
//   };
// }

export class CreateCustomFieldDto extends CommonCustomFieldDto {
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: "The description of the custom field" })
  description?: string;

  @IsString()
  @MinLength(1)
  @MaxLength(100)
  @Matches(/^[\w\s\-\p{Emoji}]+$/u, {
    message:
      "name can only contain alphanumeric characters, spaces, hyphens, underscores, and emojis",
  })
  @ApiProperty({ description: "The name of the custom field" })
  name: string;

  @IsEnum(CustomFieldSource)
  @ApiProperty({ description: "The source of the custom field" })
  source: CustomFieldSource;

  @IsEnum(CustomFieldType)
  @ApiProperty({ description: "The type of the custom field" })
  fieldType: CustomFieldType;

  @IsArray()
  @IsOptional()
  @ApiPropertyOptional({ description: "The options of the custom field" })
  options?: CustomFieldOption[];

  @IsObject()
  @IsOptional()
  @ApiPropertyOptional({ description: "The metadata of the custom field" })
  metadata?: {
    [key: string]: string | number | boolean | null;
  };

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The placeholder text of the custom field",
  })
  placeholderText: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: "The hint text of the custom field" })
  hintText: string;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({
    description: "Whether the custom field is mandatory on close",
  })
  mandatoryOnClose: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({
    description: "Whether the custom field is mandatory on creation",
  })
  mandatoryOnCreation: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({
    description: "Whether the custom field is visible to customer",
  })
  visibleToCustomer: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({
    description: "Whether the custom field is editable by customer",
  })
  editableByCustomer: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({
    description: "Whether the custom field is auto added to all forms",
  })
  autoAddToAllForms: boolean;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: "The default value of the custom field" })
  defaultValue: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: "The team id of the custom field" })
  teamId?: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The lookup object id or thena entity name",
  })
  lookup?: string;

  // @IsArray()
  // @IsOptional()
  // @IsValidValidationRule({ message: "Invalid validation rules format." })
  // @ApiPropertyOptional({
  //   description: "The validation rules of the custom field",
  // })
  // validationRules?: Record<ValidationType, any>;
}

class CustomFieldOptionDto {
  @IsString()
  @IsOptional()
  id: string;

  @ApiProperty()
  @IsString()
  value: string;
}

class UpdateFieldsDto {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @ApiPropertyOptional()
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CustomFieldOptionDto)
  options?: CustomFieldOptionDto[];

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  placeholderText?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  hintText?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  defaultValue?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  regexForValidation?: string;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  mandatoryOnCreation?: boolean;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  mandatoryOnClose?: boolean;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  visibleToCustomer?: boolean;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  editableByCustomer?: boolean;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  autoAddToAllForms?: boolean;
}

class CustomFieldUpdateData {
  @ApiProperty()
  @IsString()
  @MinLength(1, { message: "fieldId must be a non-empty string" })
  fieldId: string;

  @ApiProperty()
  @IsInt({ message: "version must be an integer" })
  version: number;

  // @ApiPropertyOptional()
  // @IsBoolean()
  // @IsOptional()
  // deleteFromExistingForms?: boolean;

  @ApiPropertyOptional()
  @ValidateNested()
  @Type(() => UpdateFieldsDto)
  updates: UpdateFieldsDto;
}

export class UpdateCustomFieldDto {
  @ApiProperty({ type: [CustomFieldUpdateData] })
  @ValidateNested()
  @Type(() => CustomFieldUpdateData)
  fields: CustomFieldUpdateData[];
}

class CustomFieldData {
  @ApiProperty()
  name: string;

  @ApiProperty()
  uid: string;

  @ApiProperty()
  organizationId: string;

  @ApiProperty({ enum: CustomFieldSource })
  source: CustomFieldSource;

  @ApiProperty({ enum: CustomFieldType })
  fieldType: CustomFieldType;

  @ApiProperty({ nullable: true })
  options: CustomFieldOption[] | null;

  @ApiProperty({ nullable: true })
  metadata: Record<string, unknown> | null;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty()
  isActive: boolean;

  @ApiProperty()
  teamId: string;

  @ApiProperty()
  autoAddToAllForms: boolean;

  @ApiProperty()
  hintText: string;

  @ApiProperty()
  placeholderText: string;

  @ApiProperty()
  mandatoryOnCreation: boolean;

  @ApiProperty()
  mandatoryOnClose: boolean;

  @ApiProperty()
  visibleToCustomer: boolean;

  @ApiProperty()
  editableByCustomer: boolean;

  @ApiProperty()
  defaultValue: string;
}

export class BatchCustomFieldResponseDto {
  @ApiProperty({ type: [CustomFieldData] })
  data: CustomFieldData[];

  @ApiProperty()
  status: boolean;

  @ApiProperty()
  message: string;

  @ApiProperty()
  timestamp: Date;
}

export class ThenaRestrictedFieldData {
  @ApiProperty()
  data: CustomFieldData[];

  @ApiProperty()
  status: boolean;

  @ApiProperty()
  message: string;

  @ApiProperty()
  timestamp: Date;

  static fromEntity(entity: ThenaRestrictedField) {
    if (!entity) return null;
    return {
      id: entity.uid,
      name: entity.name,
      description: entity.description ? entity.description : undefined,
      version: entity.version,
      isActive: entity.isActive,
      fieldType: entity.fieldType,
      options: entity.options ?? undefined,
      metadata: entity.metadata ?? undefined,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      hintText: entity.hintText ?? undefined,
      placeholderText: entity.placeholderText ?? undefined,
      mandatoryOnCreation: entity.mandatoryOnCreation,
      mandatoryOnClose: entity.mandatoryOnClose,
      visibleToCustomer: entity.visibleToCustomer,
      editableByCustomer: entity.editableByCustomer,
      defaultValue: entity.defaultValue ?? undefined,
      accessibleInTicketCreationForm: entity.accessibleInTicketCreationForm,
      apiForOptions: entity.apiForOptions,
      type: entity.type,
    };
  }
}

export class CustomFieldResponseDto {
  @ApiProperty()
  data: CustomFieldData;

  @ApiProperty()
  status: boolean;

  @ApiProperty()
  message: string;

  @ApiProperty()
  timestamp: Date;

  static fromEntity(entity: CustomField) {
    if (!entity) return null;
    return {
      id: entity.uid,
      name: entity.name,
      description: entity.description ? entity.description : undefined,
      organizationId: entity.organization?.uid,
      teamId: entity.team ? entity.team.uid : undefined,
      version: entity.version,
      isActive: entity.isActive,
      source: entity.source,
      fieldType: entity.fieldType,
      options: entity.options ?? undefined,
      metadata: entity.metadata ?? undefined,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      lookup: entity.lookup ?? undefined,
      autoAddToAllForms: entity.autoAddToAllForms,
      hintText: entity.hintText ?? undefined,
      placeholderText: entity.placeholderText ?? undefined,
      mandatoryOnCreation: entity.mandatoryOnCreation,
      mandatoryOnClose: entity.mandatoryOnClose,
      visibleToCustomer: entity.visibleToCustomer,
      editableByCustomer: entity.editableByCustomer,
      defaultValue: entity.defaultValue ?? undefined,
    };
  }
}

export class GetAllCustomFieldsResponse {
  @ApiProperty({ type: [CustomFieldData] })
  data: {
    items: CustomFieldData[];
  };

  @ApiProperty()
  status: boolean;

  @ApiProperty()
  message: string;

  @ApiProperty()
  timestamp: Date;
}

export class CustomFieldTypesData {
  @ApiProperty({ type: [String] })
  text: string[];

  @ApiProperty({ type: [String] })
  numeric: string[];

  @ApiProperty({ type: [String] })
  choice: string[];

  @ApiProperty({ type: [String] })
  date: string[];

  @ApiProperty({ type: [String] })
  user: string[];

  @ApiProperty({ type: [String] })
  specialized: string[];

  @ApiProperty({ type: [String] })
  file: string[];

  @ApiProperty({ type: [String] })
  calculated: string[];

  @ApiProperty({ type: [String] })
  lookup: string[];

  @ApiProperty({ type: [String] })
  geographic: string[];

  @ApiProperty({ type: [String] })
  rating: string[];

  @ApiProperty({ type: [String] })
  toggle: string[];
}

export class GetAllCustomFieldTypesResponse {
  @ApiProperty({ type: CustomFieldTypesData })
  data: CustomFieldTypesData;

  @ApiProperty()
  status: boolean;

  @ApiProperty()
  message: string;

  @ApiProperty()
  timestamp: Date;
}

export class DeleteFieldItemDto {
  @ApiProperty()
  @IsString()
  @MinLength(1, { message: "fieldId must be a non-empty string" })
  fieldId: string;

  @ApiProperty()
  @IsInt({ message: "version must be an integer" })
  version: number;
}

export class DeleteCustomFieldDto {
  @IsArray({ message: "Fields must be an array" })
  @ArrayMinSize(1, { message: "At least one field is required" })
  @ValidateNested({ each: true })
  @Type(() => DeleteFieldItemDto) // Required for nested validation
  @ApiProperty({ type: [DeleteFieldItemDto] })
  fields: DeleteFieldItemDto[];
}
