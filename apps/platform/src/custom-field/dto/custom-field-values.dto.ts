import { Type } from "class-transformer";
import {
  Is<PERSON>rray,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
  Max<PERSON>ength,
  <PERSON><PERSON>ength,
  ValidateNested,
} from "class-validator";

class ValueData {
  @IsString()
  value?: string;

  @IsString()
  @IsOptional()
  id?: string;
}

class CommonCustomFieldValuesDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ValueData)
  data: ValueData[];

  @IsObject()
  @IsOptional()
  metadata?: Record<string, unknown>;
}

export class ExternalCustomFieldValuesDto extends CommonCustomFieldValuesDto {
  @IsString()
  @IsNotEmpty()
  customFieldId: string;
}

export class CreateCustomFieldValuesDto extends CommonCustomFieldValuesDto {
  @IsString()
  @IsNotEmpty()
  customFieldId: string;

  @IsString()
  @IsOptional()
  @MinLength(1)
  @MaxLength(100)
  name?: string;

  @IsString()
  @IsNotEmpty()
  organizationId: string;
}

export class UpdateCustomFieldValuesDto extends CommonCustomFieldValuesDto {
  @IsString()
  @IsNotEmpty()
  customFieldId?: string;

  @IsString()
  @IsOptional()
  @MinLength(1)
  @MaxLength(100)
  name?: string;
}
