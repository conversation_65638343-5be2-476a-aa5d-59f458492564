import { BadRequestException, Inject, Injectable } from "@nestjs/common";
import { ILogger } from "@repo/nestjs-commons/logger";
import {
  CustomField,
  CustomFieldRepository,
  CustomFieldSource,
  CustomFieldType,
  ThenaLookupEntities,
  ThenaRestrictedField,
  ThenaRestrictedFieldRepository,
} from "@repo/thena-platform-entities";
import { validateSync } from "class-validator";
import { ILike, In } from "typeorm";
import { IdGeneratorUtils } from "../../common/utils/id-generator.utils";
import { SharedService } from "../../shared/shared.service";
import {
  CreateCustomFieldDto,
  DeleteCustomFieldDto,
  ExternalCustomFieldValuesDto,
  UpdateCustomFieldDto,
} from "../dto";
import {
  DateValidation,
  DecimalValidation,
  EmailValidation,
  IpAddressValidation,
  isValidPassword,
  isValidPhoneNumber,
  isValidRegex,
  isValidString,
  UrlValidation,
} from "../utils/helper";

@Injectable()
export class CustomFieldvalidatorService {
  constructor(
    @Inject("CustomLogger")
    private readonly logger: ILogger,
    private customFieldRepository: CustomFieldRepository,
    private thenaRestrictedFieldRepository: ThenaRestrictedFieldRepository,
    private sharedService: SharedService,
  ) {}

  async validateCreatePayload(
    orgId: string,
    createCustomFieldDto: CreateCustomFieldDto,
  ) {
    const teamId = createCustomFieldDto.teamId;

    if (
      createCustomFieldDto.mandatoryOnCreation === true &&
      createCustomFieldDto.visibleToCustomer === false
    ) {
      throw new BadRequestException(
        "Mandatory on creation cannot be true when visible to customer is false",
      );
    }

    if (
      createCustomFieldDto.visibleToCustomer === false &&
      createCustomFieldDto.editableByCustomer === true
    ) {
      throw new BadRequestException(
        "Editable by customer cannot be true when visible to customer is false",
      );
    }

    if (
      createCustomFieldDto.fieldType === CustomFieldType.LOOKUP &&
      createCustomFieldDto.source !== CustomFieldSource.CUSTOM_OBJECT
    ) {
      throw new BadRequestException(
        "Lookup field type is only allowed for custom object source",
      );
    }

    if (
      createCustomFieldDto.fieldType === CustomFieldType.LOOKUP &&
      !createCustomFieldDto.lookup
    ) {
      throw new BadRequestException(
        "Lookup object is required for lookup field type",
      );
    }

    if (
      createCustomFieldDto.fieldType !== CustomFieldType.LOOKUP &&
      createCustomFieldDto.lookup
    ) {
      throw new BadRequestException(
        "Lookup object is not allowed for non-lookup field type",
      );
    }

    if (createCustomFieldDto.fieldType === CustomFieldType.LOOKUP) {
      const isValid = await this.sharedService.isValidCustomObjectOrThenaEntity(
        createCustomFieldDto.lookup,
        teamId,
      );
      if (!isValid) {
        throw new BadRequestException(
          "Invalid lookup object, either it does not exists or does not exist in the same team as the custom field",
        );
      }
    }

    if (teamId) {
      const existingFieldsInTeam = await this.customFieldRepository.findAll({
        where: {
          organizationId: orgId,
          name: createCustomFieldDto.name,
          isDeleted: false,
          teamId: teamId,
        },
      });

      if (existingFieldsInTeam?.length > 0) {
        throw new BadRequestException(
          `Custom field with the name "${createCustomFieldDto.name}" already exists in the team.`,
        );
      }
    }

    const [existingCustomFieldResult, thenaRestrictedFieldResult] =
      await Promise.allSettled([
        this.customFieldRepository.findByCondition({
          where: {
            organizationId: orgId,
            name: ILike(createCustomFieldDto.name.toLocaleLowerCase()),
            source: createCustomFieldDto.source,
            isDeleted: false,
          },
        }),
        this.thenaRestrictedFieldRepository.findByCondition({
          where: {
            name: ILike(createCustomFieldDto.name.toLocaleLowerCase()),
          },
        }),
      ]);

    // Handle potential failures in the database queries
    if (existingCustomFieldResult.status === "rejected") {
      throw new BadRequestException("Error checking existing custom field");
    }
    if (thenaRestrictedFieldResult.status === "rejected") {
      // console.log(thenaRestrictedFieldResult.reason)
      throw new BadRequestException(
        "Error checking thena restricted field names",
      );
    }

    const existingCustomField = existingCustomFieldResult.value;
    const thenaRestrictedField = thenaRestrictedFieldResult.value;

    if (existingCustomField) {
      throw new BadRequestException(
        `Custom field with name '${createCustomFieldDto.name}' already exists in your organization`,
      );
    }

    if (thenaRestrictedField) {
      throw new BadRequestException(
        `${createCustomFieldDto.name} is thena restricted field name, cannot be used to create custom field`,
      );
    }

    if (createCustomFieldDto.options) {
      createCustomFieldDto.options.forEach((option) => {
        option.id = IdGeneratorUtils.generate("OP");
      });
    }

    // Validate default value if provided
    if (createCustomFieldDto.defaultValue) {
      const tempField = {
        fieldType: createCustomFieldDto.fieldType,
        mandatoryOnCreation: createCustomFieldDto.mandatoryOnCreation,
        name: createCustomFieldDto.name,
        options: createCustomFieldDto.options,
      } as CustomField;

      if (createCustomFieldDto.options) {
        for (const option of createCustomFieldDto.options) {
          if (option.value === createCustomFieldDto.defaultValue) {
            createCustomFieldDto.defaultValue = option.id;
          }
        }
      }

      await this.validateFieldValue(tempField, [
        createCustomFieldDto.defaultValue,
      ]);
    }
  }

  /**
   * Validate batch delete custom field payload
   * @returns validated object
   */
  async validateBatchDeletePayload(
    orgId: string,
    fields: DeleteCustomFieldDto,
    teamIds: string[],
  ) {
    const fieldIds = fields.fields.map((field) => field.fieldId);
    const fieldIdsSet = new Set(fieldIds);
    if (fieldIdsSet.size !== fieldIds.length) {
      throw new BadRequestException(
        `Duplicate field IDs are not allowed. Please provide unique field IDs.`,
      );
    }

    const promises = [];
    promises.push(
      this.customFieldRepository.findAll({
        where: {
          organizationId: orgId,
          uid: In(fieldIds),
          isDeleted: false,
        },
      }),
    );

    promises.push(
      this.thenaRestrictedFieldRepository.findAll({
        where: {
          uid: In(fieldIds),
          isDeleted: false,
        },
      }),
    );

    const response = await Promise.all(promises);

    const existingFields = response[0];
    const thenaRestrictedFields = response[1];

    if (thenaRestrictedFields.length > 0) {
      const restrictedFieldIds = thenaRestrictedFields.map(
        (field) => field.uid,
      );
      throw new BadRequestException(
        `Cannot delete thena restricted fields: ${restrictedFieldIds.join(
          ", ",
        )}`,
      );
    }

    if (existingFields.length !== fieldIds.length) {
      const foundFieldIds = new Set(existingFields.map((f) => f.uid));
      const missingFields = fieldIds.filter((id) => !foundFieldIds.has(id));
      throw new BadRequestException(
        `Fields not found: ${missingFields.join(", ")}`,
      );
    }

    for (const field of existingFields) {
      if (field.teamId && !teamIds.includes(field.teamId)) {
        throw new BadRequestException(
          `You are trying to delete fields of a team that you are not a part of, fieldId: ${field.uid}`,
        );
      }
    }

    const modifieldFields = [];
    const autoAddFields = [];
    for (const field of fields.fields) {
      const existingField = existingFields.find(
        (ef) => ef.uid === field.fieldId,
      );

      if (existingField.version !== field.version) {
        modifieldFields.push(field.fieldId);
      }

      if (existingField.autoAddToAllForms) {
        autoAddFields.push(field.fieldId);
      }
    }

    if (modifieldFields.length > 0) {
      throw new BadRequestException(
        `Some fields have been modified, Please try updating the latest version. Modified fields: ${modifieldFields.join(
          ", ",
        )}`,
      );
    }

    if (autoAddFields.length > 0) {
      throw new BadRequestException(
        `Fields with IDs: ${autoAddFields.join(
          ", ",
        )} cannot be deleted as they are configured to be automatically added to all forms.`,
      );
    }

    const forms = await this.sharedService.findFormsByFieldIds(
      fields.fields.map((field) => field.fieldId),
    );
    if (forms.length) {
      throw new BadRequestException(
        `Cannot delete field ${fields.fields
          .map((field) => field.fieldId)
          .join(", ")} as it is used in the following forms: ${forms
          .map((form) => form.name)
          .join(", ")}`,
      );
    }

    return {
      ...fields,
    };
  }

  async validateBatchUpdatePayload(
    orgId: string,
    updates: UpdateCustomFieldDto,
    teamIds: string[],
  ) {
    if (!updates?.fields || updates.fields.length === 0) {
      throw new BadRequestException("No fields to update");
    }

    const fieldIds = updates.fields.map((field) => field.fieldId);
    const promises = [];
    promises.push(
      this.customFieldRepository.findAll({
        where: {
          organizationId: orgId,
          uid: In(fieldIds),
          isDeleted: false,
        },
      }),
    );

    promises.push(
      this.thenaRestrictedFieldRepository.findAll({
        where: {
          uid: In(fieldIds),
          isDeleted: false,
        },
      }),
    );

    // TODO: fetch all the forms in which current fieldIds are present

    const response = await Promise.all(promises);

    const existingFields = response[0];
    const thenaRestrictedFields = response[1];

    if (thenaRestrictedFields.length > 0) {
      const restrictedFieldIds = thenaRestrictedFields.map(
        (field) => field.uid,
      );
      throw new BadRequestException(
        `Cannot update thena restricted fields: ${restrictedFieldIds.join(
          ", ",
        )}`,
      );
    }

    if (existingFields.length !== fieldIds.length) {
      const foundFieldIds = new Set(existingFields.map((f) => f.uid));
      const missingFields = fieldIds.filter((id) => !foundFieldIds.has(id));
      throw new BadRequestException(
        `Fields not found: ${missingFields.join(", ")}`,
      );
    }

    for (const field of existingFields) {
      if (field.teamId && !teamIds.includes(field.teamId)) {
        throw new BadRequestException(
          `You are trying to update fields of a team that you are not a part of, fieldId: ${field.uid}`,
        );
      }
    }

    const modifieldFields = [];
    const autoAddFields = [];
    for (const field of updates.fields) {
      const existingField = existingFields.find(
        (ef) => ef.uid === field.fieldId,
      );

      if (existingField.version !== field.version) {
        modifieldFields.push(field.fieldId);
      }

      if (existingField.autoAddToAllForms) {
        autoAddFields.push(field.fieldId);
      }

      if (field.updates.options) {
        const optionValueToIdMap = existingField.options.reduce(
          (acc, option) => {
            acc[option.value] = option.id;
            return acc;
          },
          {},
        );

        for (const option of field.updates.options) {
          if (optionValueToIdMap[option.value]) {
            option.id = optionValueToIdMap[option.value];
          } else if (!option.id) {
            option.id = IdGeneratorUtils.generate("OP");
          }
        }
      }

      if (field.updates.defaultValue) {
        const tempField = {
          fieldType: existingField.fieldType,
          mandatoryOnCreation: field.updates.mandatoryOnCreation,
          name: field.updates.name,
          uid: field.fieldId,
          options: existingField.options,
        } as CustomField;

        if (existingField.options) {
          for (const option of existingField.options) {
            if (option.value === field.updates.defaultValue) {
              field.updates.defaultValue = option.id;
            }
          }
        }

        await this.validateFieldValue(tempField, [field.updates.defaultValue]);
      }
    }

    if (modifieldFields.length > 0) {
      throw new BadRequestException(
        `Some fields have been modified, Please try updating the latest version. Modified fields: ${modifieldFields.join(
          ", ",
        )}`,
      );
    }

    const fieldsWithNameChangesNameToIDMap = updates.fields
      .filter((field) => field.updates.name)
      .reduce((acc, field) => {
        if (acc[field.updates.name]) {
          throw new BadRequestException(
            `Duplicate field names are not allowed. Please choose a unique name for each field.`,
          );
        }
        acc[field.updates.name] = field.fieldId;
        return acc;
      }, {});

    if (Object.keys(fieldsWithNameChangesNameToIDMap).length > 0) {
      const promisseResult = await Promise.all([
        this.customFieldRepository.findAll({
          where: {
            organizationId: orgId,
            name: ILike(
              In(
                Object.keys(fieldsWithNameChangesNameToIDMap).map(
                  (name) => name,
                ),
              ),
            ),
            isDeleted: false,
          },
        }),
        this.thenaRestrictedFieldRepository.findAll({
          where: {
            name: ILike(
              In(
                Object.keys(fieldsWithNameChangesNameToIDMap).map(
                  (name) => name,
                ),
              ),
            ),
          },
        }),
      ]);

      let existingFieldsWithSameName = promisseResult[0];
      const thenaRestrictedFieldsWithSameName = promisseResult[1];

      existingFieldsWithSameName = existingFieldsWithSameName.filter(
        (field) => field.uid !== fieldsWithNameChangesNameToIDMap[field.name],
      );

      if (existingFieldsWithSameName.length > 0) {
        const existingFieldIds = existingFieldsWithSameName.map(
          (field) => field.uid,
        );
        throw new BadRequestException(
          `Fields with IDs: ${existingFieldIds.join(
            ", ",
          )} already exist with the same name. Please choose a different name.`,
        );
      }

      if (thenaRestrictedFieldsWithSameName.length > 0) {
        const restrictedFieldIds = thenaRestrictedFieldsWithSameName.map(
          (field) => field.uid,
        );
        throw new BadRequestException(
          `Field name is reserved for thena restricted fields: ${restrictedFieldIds.join(
            ", ",
          )}`,
        );
      }
    }

    for (const field of updates.fields) {
      if (
        field.updates.mandatoryOnCreation === true &&
        field.updates.visibleToCustomer === false
      ) {
        throw new BadRequestException(
          `Field ${field.fieldId}: If mandatory on creation is true then visible to customer cannot be false.`,
        );
      }

      if (
        field.updates.visibleToCustomer === false &&
        field.updates.editableByCustomer === true
      ) {
        throw new BadRequestException(
          `Field ${field.fieldId}: If visible to customer is false then editable by customer cannot be true.`,
        );
      }
    }

    return {
      ...updates,
    };
  }

  buildFieldValuesStructure(values: ExternalCustomFieldValuesDto[]) {
    const fieldValues = new Map<string, any>();
    for (const value of values || []) {
      if (!value?.customFieldId || !Array.isArray(value.data)) {
        throw new BadRequestException(
          `Invalid field value structure for field`,
        );
      }
      fieldValues.set(
        value.customFieldId,
        value.data.map((v) => {
          if (!v?.id && !v?.value) {
            throw new BadRequestException(
              `Invalid field value structure for field ${value.customFieldId}`,
            );
          }
          return v.id || v.value;
        }),
      );
    }
    return fieldValues;
  }

  async validateFieldValue(
    field: CustomField | ThenaRestrictedField,
    values: any,
    isTicketClosing?: boolean,
    needsSingleValue?: boolean,
  ) {
    if (
      isTicketClosing &&
      field.mandatoryOnClose === true &&
      (values?.[0] === undefined || values?.[0] === null)
    ) {
      throw new BadRequestException(
        `Field ${field.name || field.uid} cannot be empty`,
      );
    }

    if (
      !isTicketClosing &&
      field.mandatoryOnCreation === true &&
      (values?.[0] === undefined || values?.[0] === null)
    ) {
      throw new BadRequestException(
        `Field ${field.name || field.uid} cannot be empty`,
      );
    }
    if (values === undefined) return;
    if (!Array.isArray(values)) {
      throw new BadRequestException("Unexpected value format");
    }

    switch (field.fieldType) {
      case CustomFieldType.SINGLE_LINE:
        for (const value of values || []) {
          if (!isValidString(value)) {
            throw new BadRequestException(
              `Field ${field.name || field.uid} contains invalid string`,
            );
          }
        }
        break;
      case CustomFieldType.RICH_TEXT:
      case CustomFieldType.MULTI_LINE:
        this.singleValue(field, values);
        for (const value of values || []) {
          if (!isValidString(value)) {
            throw new BadRequestException(
              `Field ${field.name || field.uid} contains invalid string`,
            );
          }
        }
        break;
      case CustomFieldType.EMAIL: {
        this.singleValue(field, values);
        for (const value of values || []) {
          const emailValidation = new EmailValidation();
          emailValidation.email = value;
          const errors = validateSync(emailValidation);
          if (errors.length > 0) {
            throw new BadRequestException(
              `Field ${field.name || field.uid} contains invalid email`,
            );
          }
        }
        break;
      }
      case CustomFieldType.SINGLE_CHOICE:
      case CustomFieldType.RADIO_BUTTON:
        this.singleValue(field, values);
        for (const value of values || []) {
          if (!field.options.some((option) => option.id === value)) {
            throw new BadRequestException(
              `Field ${
                field.name || field.uid
              } expects a valid option value, valid values: ${field.options
                .map((option) => option.value)
                .join(", ")}`,
            );
          }
        }
        break;
      case CustomFieldType.MULTI_CHOICE:
      case CustomFieldType.CHECKBOX:
        if (!Array.isArray(values)) {
          throw new BadRequestException(
            `Field ${field.name || field.uid} expects an array of values`,
          );
        }
        if (values.length !== new Set(values).size) {
          throw new BadRequestException(
            `Field ${field.name || field.uid} contains duplicate values`,
          );
        }
        for (const option of values) {
          if (!field.options.some((o) => o.id === option)) {
            throw new BadRequestException(
              `Field ${
                field.name || field.uid
              } expects a valid option value, valid values: ${field.options
                .map((option) => option.value)
                .join(", ")}`,
            );
          }
        }
        break;
      case CustomFieldType.URL: {
        this.singleValue(field, values);
        for (const value of values || []) {
          const urlValidation = new UrlValidation();
          urlValidation.url = value;
          const errors = validateSync(urlValidation);
          if (errors.length > 0) {
            throw new BadRequestException(
              `Field ${field.name || field.uid} contains invalid URL`,
            );
          }
        }
        break;
      }
      case CustomFieldType.INTEGER: {
        this.singleValue(field, values);
        for (const value of values || []) {
          const parsedInt = parseInt(value, 10);
          if (Number.isNaN(parsedInt) || parsedInt.toString() !== value) {
            throw new BadRequestException(
              `Field ${field.name || field.uid} contains invalid integer`,
            );
          }
        }
        break;
      }
      case CustomFieldType.DATE:
      case CustomFieldType.DATE_TIME: {
        this.singleValue(field, values);
        for (const value of values || []) {
          const dateValidation = new DateValidation();
          dateValidation.date = value;
          const errors = validateSync(dateValidation);
          if (errors.length > 0) {
            throw new BadRequestException(
              `Field ${field.name || field.uid} must be a valid date`,
            );
          }
        }
        break;
      }
      case CustomFieldType.CURRENCY:
      case CustomFieldType.DECIMAL: {
        this.singleValue(field, values);
        for (const value of values || []) {
          const decimalValidation = new DecimalValidation();
          decimalValidation.decimal = value;
          const errors = validateSync(decimalValidation);
          if (errors.length > 0) {
            throw new BadRequestException(
              `Field ${field.name || field.uid} contains invalid format`,
            );
          }
        }
        break;
      }
      case CustomFieldType.PASSWORD:
        this.singleValue(field, values);
        for (const value of values || []) {
          if (!isValidPassword(value)) {
            throw new BadRequestException(
              `Field ${field.name || field.uid} contains invalid password`,
            );
          }
        }
        break;
      case CustomFieldType.PHONE_NUMBER:
        this.singleValue(field, values);
        for (const value of values || []) {
          if (!isValidPhoneNumber(value)) {
            throw new BadRequestException(
              `Field ${field.name || field.uid} contains invalid phone number`,
            );
          }
        }
        break;
      case CustomFieldType.REGEX:
        this.singleValue(field, values);
        for (const value of values || []) {
          if (!isValidRegex(value)) {
            throw new BadRequestException(
              `Field ${field.name || field.uid} contains invalid regex`,
            );
          }
        }
        break;
      case CustomFieldType.IP_ADDRESS: {
        this.singleValue(field, values);
        for (const value of values || []) {
          const ipAddressValidation = new IpAddressValidation();
          ipAddressValidation.ipAddress = value;
          const errors = validateSync(ipAddressValidation);

          if (errors.length > 0) {
            throw new BadRequestException(
              `Field ${field.name || field.uid} contains invalid IP address`,
            );
          }
        }
        break;
      }
      case CustomFieldType.TOGGLE:
      case CustomFieldType.BOOLEAN:
        this.singleValue(field, values);
        for (const value of values || []) {
          if (value !== "true" && value !== "false") {
            throw new BadRequestException(
              `Field ${field.name || field.uid} contains invalid boolean`,
            );
          }
        }
        break;
      case CustomFieldType.LOOKUP:
        if (needsSingleValue) {
          this.singleValue(field, values);
        }
        for (const value of values || []) {
          const isValidLookup = await this.isValidLookup(
            field as CustomField,
            value,
          );
          if (!isValidLookup) {
            throw new BadRequestException(
              `Field ${field.name || field.uid} contains invalid lookup value`,
            );
          }
        }
        break;
      case CustomFieldType.FILE_UPLOAD:
      case CustomFieldType.CALCULATED:
      case CustomFieldType.ADDRESS:
      case CustomFieldType.COORDINATES:
      case CustomFieldType.RATING:
        break;
      default:
        throw new BadRequestException(
          `Unknown field type: ${field.fieldType} for field ${
            field.name || field.uid
          }`,
        );
    }
  }

  singleValue(field: CustomField | ThenaRestrictedField, values: any) {
    if (Array.isArray(values) && values.length > 1) {
      throw new BadRequestException(
        `Field ${field.name || field.uid} cannot have multiple values`,
      );
    }
  }

  async isValidLookup(field: CustomField, value: any) {
    try {
      switch (field.lookup) {
        case ThenaLookupEntities.TICKET:
          return await this.sharedService.getTicket(value, field.teamId);
        case ThenaLookupEntities.USER:
          return await this.sharedService.getUser(value, field.organizationId);
        case ThenaLookupEntities.ORGANIZATION:
          return await this.sharedService.getOrganization(value);
        case ThenaLookupEntities.ACCOUNT:
          return await this.sharedService.getAccount(
            value,
            field.organizationId,
          );
        case ThenaLookupEntities.CONTACT:
          return await this.sharedService.getContact(
            value,
            field.organizationId,
          );
        default:
          return await this.sharedService.getCustomObjectRecord(
            value,
            field.teamId,
          );
      }
    } catch (err) {
      this.logger.error(
        `Error validating lookup field ${
          field.name || field.uid
        } with value ${value}`,
        err,
      );
      return false;
    }
  }
}
