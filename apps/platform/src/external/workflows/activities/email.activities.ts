import { email } from "@repo/shared-proto";
import { AbstractWorkflowActivity } from "@repo/workflow-engine";
import { ConfigKeys, ConfigService } from "../../../config/config.service";
import { ACTIVITY_THROTTLER_CONFIG } from "../constants/activity-throttler.config";

export class SendEmail extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Send email";

  static override description = "This activity sends an email";

  static override identifier = "email:send-email";

  static override requestSchema = {
    type: "object",
    required: ["to", "textBody"],
    properties: {
      from: { type: "string" },
      to: { type: "string" },
      subject: { type: ["string", "null"] },
      htmlBody: { type: ["string", "null"] },
      textBody: { type: ["string", "null"] },
      cc: { type: ["string", "null"] },
      bcc: { type: ["string", "null"] },
    },
  };

  static override responseSchema = {
    type: "object",
    properties: {
      to: { type: "string" },
      cc: { type: ["string", "null"] },
      bcc: { type: ["string", "null"] },
      submittedAt: { type: "string" },
      messageId: { type: "string" },
      errorCode: { type: "number" },
      message: { type: "string" },
    },
  };

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.EMAIL_GRPC_URL),
      packageName: email.GRPC_EMAIL_V1_PACKAGE_NAME,
      serviceName: email.EMAIL_PROVIDER_SERVICE_NAME,
      methodName: "SendEmail",
      protoPath: "dist/proto/email/email.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      ...ACTIVITY_THROTTLER_CONFIG.TIER_3,
    },
  };
}
