import { EXTERNAL_CUSTOM_FIELDS_RESPONSE_SCHEMA } from "./common.schema";

export const COMMON_TICKET_RESPONSE_SCHEMA = {
  type: "object",
  properties: {
    id: { type: "string" },
    ticketId: { type: "number" },
    title: { type: "string" },
    description: { type: ["string", "null"] },
    status: { type: ["string", "null"] },
    statusId: { type: ["string", "null"] },
    priority: { type: ["string", "null"] },
    priorityId: { type: ["string", "null"] },
    accountId: { type: ["string", "null"] },
    account: { type: ["string", "null"] },
    teamId: { type: "string" },
    teamName: { type: "string" },
    subTeamId: { type: ["string", "null"] },
    subTeamName: { type: ["string", "null"] },
    isPrivate: { type: "boolean" },
    typeId: { type: ["string", "null"] },
    type: { type: ["string", "null"] },
    formId: { type: ["string", "null"] },
    assignedAgent: { type: ["string", "null"] },
    assignedAgentId: { type: ["string", "null"] },
    assignedAgentEmail: { type: ["string", "null"] },
    requestorEmail: { type: "string" },
    submitterEmail: { type: ["string", "null"] },
    customerContactId: { type: ["string", "null"] },
    customerContactFirstName: { type: ["string", "null"] },
    customerContactLastName: { type: ["string", "null"] },
    customerContactEmail: { type: ["string", "null"] },
    storyPoints: { type: ["number", "null"] },
    aiGeneratedSummary: { type: ["string", "null"] },
    aiGeneratedTitle: { type: ["string", "null"] },
    customFieldValues: EXTERNAL_CUSTOM_FIELDS_RESPONSE_SCHEMA,
    metadata: { type: ["object", "null"] },
    deletedAt: { type: ["string", "null"] },
    archivedAt: { type: ["string", "null"] },
    createdAt: { type: "string" },
    updatedAt: { type: "string" },
  },
  required: ["id", "title", "ticketId", "teamId", "teamName", "requestorEmail"],
};

export const COMMON_TICKETS_EVENT_SCHEMA = {
  type: "object",
  required: ["id", "title", "teamId"],
  properties: {
    id: {
      type: "string",
    },
    ticketId: {
      type: "number",
    },
    title: {
      type: "string",
    },
    description: {
      type: ["string", "null"],
    },
    statusId: {
      type: ["string", "null"],
    },
    statusName: {
      type: ["string", "null"],
    },
    priorityId: {
      type: ["string", "null"],
    },
    priorityName: {
      type: ["string", "null"],
    },
    source: {
      type: ["string", "null"],
    },
    createdAt: {
      type: "string",
    },
    teamId: {
      type: "string",
    },
    teamName: {
      type: "string",
    },
    subTeamId: {
      type: ["string", "null"],
    },
    subTeamName: {
      type: ["string", "null"],
    },
    isEscalated: {
      type: ["boolean", "null"],
    },
    customer: {
      type: ["object", "null"],
      properties: {
        id: { type: ["string", "null"] },
        name: { type: ["string", "null"] },
        email: { type: ["string", "null"] },
      },
    },
    assignedTo: {
      type: ["string", "null"],
    },
    assignedName: {
      type: ["string", "null"],
    },
    assignedAgent: {
      type: ["object", "null"],
      properties: {
        id: { type: ["string", "null"] },
        name: { type: ["string", "null"] },
        email: { type: ["string", "null"] },
      },
    },
    tags: {
      type: ["array", "null"],
      items: {
        type: "string",
      },
    },
    isArchived: {
      type: ["boolean", "null"],
    },
    customFields: {
      type: ["array", "null"],
      items: {
        type: "object",
      },
    },
    metadata: {
      type: ["object", "null"],
    },
    aiGeneratedTitle: {
      type: ["string", "null"],
    },
    aiGeneratedSummary: {
      type: ["string", "null"],
    },
  },
};
