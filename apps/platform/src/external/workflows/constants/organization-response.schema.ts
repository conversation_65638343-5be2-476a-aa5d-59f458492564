export const ORGANIZATION_RESPONSE_SCHEMA = {
  type: "object",
  properties: {
    id: { type: "string" },
    orgId: { type: "string" },
    name: { type: "string" },
    logoUrl: { type: ["string", "null"] },
    isVerified: { type: "boolean" },
    isActive: { type: "boolean" },
    createdAt: { type: "string" },
    updatedAt: { type: "string" },
  },
  required: ["id", "orgId", "name", "createdAt", "updatedAt"],
};
