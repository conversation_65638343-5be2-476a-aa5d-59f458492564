import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ChatPostMessageArguments, TeamInfoResponse } from '@slack/web-api';
import { DeepPartial, In, Repository } from 'typeorm';
import { InstallationWithOrganization } from '../../../common/interfaces/common.interface';
import { ConfigKeys, ConfigService } from '../../../config/config.service';
import { TransactionService } from '../../../database/common';
import {
  ChannelTriageMappings,
  Channels,
  Installations,
  SlackMessages,
  SlackTriageMessages,
  Users,
} from '../../../database/entities';
import { ChannelType } from '../../../database/entities/channels/channels.entity';
import { ChannelsRepository } from '../../../database/entities/channels/repositories';
import { TriageMapsRepository } from '../../../database/entities/mappings/repositories/triage-maps.repository';
import { SlackMessagesRepository } from '../../../database/entities/slack-messages/repositories/slack-messages.repository';
import { SlackTriageMessagesRepository } from '../../../database/entities/slack-messages/repositories/slack-triage-messages.repository';
import { Person } from '../../../database/interfaces/person.interface';
import { ThenaPlatformApiProvider } from '../../../external/provider/thena-platform-api.provider';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../utils';

import { parseWithMentions } from '../../../utils/parsers/slack/mentions.parser';
import { TriageMessageBlock } from '../../blocks/components';
import { TicketDetailsMetadata } from '../../constants/interfaces';
import { SlackLinkSharedHandler } from '../../event-handlers/handlers/links/link-shared.handler';
import { SlackWebAPIService } from '../../providers/slack-apis/slack-apis.service';

const LOG_SPAN = 'CoreTriageService';

interface CreateTriageMappingData {
  channelId: string;
  triageForChannelId?: string;
}

interface SendTriageMessageToChannelData {
  channel: Channels;
  ticket: any;
  slackMessage: SlackMessages;
  message?: string;
  platformCommentId?: string;
  returnExisting?: boolean;
  slackTeamName?: string;
  slackTeamIconUrl?: string;
  commentAs?: Users;
  commentAsName?: string;
  commentAsAvatar?: string;
  commentAsEmail?: string;
}

interface SendDiscussionThreadToChannelData {
  channel: Channels;
  ticket: any;
  slackMessage: SlackMessages;
  message?: string;
  platformCommentId?: string;
}

@Injectable()
export class CoreTriageService {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // External API Providers
    private readonly thenaPlatformApi: ThenaPlatformApiProvider,
    private readonly slackWebAPIService: SlackWebAPIService,

    // Utility Services
    private readonly configService: ConfigService,
    private readonly transactionService: TransactionService,
    private readonly linkSharedHandler: SlackLinkSharedHandler,

    // Database Repositories
    @InjectRepository(Users)
    private readonly slackUsersRepository: Repository<Users>,
    private readonly triageMapsRepository: TriageMapsRepository,
    private readonly slackTriageMessagesRepository: SlackTriageMessagesRepository,
    private readonly channelsRepository: ChannelsRepository,
    private readonly slackMessagesRepository: SlackMessagesRepository,
    // Slack Blocks
    private readonly triageMessageBlocks: TriageMessageBlock,
  ) {}

  /**
   * @description Create a new triage mapping
   * @param installation Installation
   * @param data Create triage mapping data
   * @returns Triage mapping
   */
  async createTriageMapping(
    installation: InstallationWithOrganization,
    data: CreateTriageMappingData,
  ) {
    const { channelId, triageForChannelId } = data;

    // If the channel id is not provided, throw an error
    if (!channelId) {
      throw new BadRequestException('Channel ID is required');
    }

    // Fetch the channel from the database
    const triageChannel = await this.channelsRepository.findByCondition({
      where: {
        channelId,
        installation: { id: installation.id },
        organization: { id: installation.organization.id },
      },
    });

    // If the channel is not found, throw an error
    if (!triageChannel) {
      throw new BadRequestException('Triage channel not found');
    }

    // Fetch the customer channel if it is provided
    let customerChannel: Channels | null = null;
    if (triageForChannelId) {
      customerChannel = await this.channelsRepository.findByCondition({
        where: {
          channelId: triageForChannelId,
          installation: { id: installation.id },
          organization: { id: installation.organization.id },
        },
      });
    }

    // Create a new triage mapping
    const triageMapping = await this.transactionService.runInTransaction(
      async (txnContext) => {
        // Update the channel and set it to be a triage channel
        await this.channelsRepository.updateWithTxn(
          txnContext,
          { id: triageChannel.id, installation: { id: installation.id } },
          { channelType: ChannelType.TRIAGE_CHANNEL },
        );

        const writeToPerform: DeepPartial<ChannelTriageMappings> = {
          triageChannel: { id: triageChannel.id },
          installation: { id: installation.id },
          organization: { id: installation.organization.id },
        };

        if (customerChannel) {
          writeToPerform.activeChannel = { id: customerChannel.id };
        }

        // Create triage mapping to this channel
        return this.triageMapsRepository.saveWithTxn(
          txnContext,
          writeToPerform,
        );
      },
    );

    return triageMapping;
  }

  /**
   * @description Send triage messages to a channel
   * @param installation Installation
   * @param channel Channel
   * @param ticket Ticket
   * @param slackMessage Slack message
   * @param customer Customer
   */
  async sendTriageMessages(
    installation: Installations,
    channel: Channels,
    ticket: any,
    slackMessage: SlackMessages,
    customer: Person,
  ) {
    const spanId = `[sendTriageMessages] [${installation?.id}] [${channel?.id}] [${ticket?.id}]`;
    // Fetch all triage mappings for the channel
    const triageMappings = await this.triageMapsRepository.findAll({
      where: {
        activeChannel: { id: channel.id },
        installation: { id: installation.id },
        organization: { id: installation.organization.id },
      },
      relations: { triageChannel: true },
      select: { triageChannel: { channelId: true, name: true } },
    });

    const customerSlackTeam = customer.userDump?.team_id ?? '';
    let slackTeamDetails: TeamInfoResponse | null = null;
    let slackTeamName: string | null = null;
    let slackTeamIconUrl: string | null = null;
    try {
      slackTeamDetails = await this.slackWebAPIService.getTeamInfo(
        installation.botToken,
        {
          team: customerSlackTeam,
        },
      );

      // If the slack team details are not ok, log an error
      if (!slackTeamDetails.ok) {
        this.logger.error(
          `${LOG_SPAN} Error fetching slack team details for customer ${customer.slackId}`,
          slackTeamDetails.error,
        );
      }

      // If the slack team details are ok, log the team name
      if (slackTeamDetails.ok && slackTeamDetails.team) {
        const { name, icon } = slackTeamDetails.team;
        slackTeamName = name;
        slackTeamIconUrl = this.getIconUrl(icon);
      } else {
        this.logger.warn(
          `${LOG_SPAN} ${spanId} Missing team payload while fetching team info for ${customer.slackId}`,
        );
      }
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `${LOG_SPAN} ${spanId} Error fetching slack team details for customer ${customer.slackId}`,
          error.stack,
        );
      } else {
        console.error(
          `${LOG_SPAN} ${spanId} Error fetching slack team details for customer ${customer.slackId}`,
          error,
        );
      }
    }

    const triagePromises = triageMappings.map(async (tm) => {
      const triageSent = await this.slackWebAPIService.sendMessage(
        installation.botToken,
        {
          channel: tm.triageChannel.channelId,
          metadata: {
            event_type: 'ticket_details',
            event_payload: {
              ticket_id: ticket.data.id,
              ticket_team_id: ticket.data.teamId,
              installation_id: installation.id,
              channel_id: channel.channelId,
            },
          } as TicketDetailsMetadata,
          icon_url: slackTeamIconUrl,
          username: slackTeamName,
          blocks: this.triageMessageBlocks.build({
            ticket: {
              id: ticket.data.id,
              ticketId: ticket.data.ticketId,
              ticketTeamId: ticket.data.teamId,
              priority: ticket.data.priority,
              priorityId: ticket.data.priorityId,
              sentiment: ticket.data.sentiment,
              teamIdentifier: ticket.data.teamIdentifier,
              status: ticket.data.status,
              statusId: ticket.data.statusId,
              permalink: slackMessage.slackPermalink,
              customer: {
                email: customer.slackProfileEmail,
                name: customer.displayName || customer.realName,
              },
              content: ticket.data.title,
              subject: ticket.data.title,
              timestamp: ticket.data.createdAt,
            },
          }).blocks,
          unfurl_links: true,
          unfurl_media: true,
        },
      );

      // Create triage messages on platform
      const platformComment = await this.thenaPlatformApi.createNewComment(
        installation,
        {
          files: [],
          ticketId: ticket.data.id,
          channelId: tm.triageChannel.channelId,
          metadata: {
            ignoreSelf: true,
            ts: triageSent.ts,
            threadTs: triageSent.ts,
          },
          commentVisibility: 'private',
          threadName: `Slack Triage for ${ticket.data.ticketId} on #${tm.triageChannel.name}`,
          content: `Slack Triage for ${ticket.data.ticketId} on #${tm.triageChannel.name}`,
        },
      );

      return { triageSent, platformComment };
    });

    const triageMessagesSent = await Promise.all(triagePromises);

    // Send a message to the triage channel
    this.logger.debug(
      `Triage messages sent to ${triageMessagesSent.length} channels`,
    );

    const channelsSentTo = triageMessagesSent.map((t) => t.triageSent.channel);

    const channelsSentToSet = new Set(channelsSentTo);

    const channels = await this.channelsRepository.findAll({
      where: {
        channelId: In(Array.from(channelsSentToSet)),
        installation: { id: installation.id },
        organization: { id: installation.organization.id },
      },
    });

    const channelsMap = new Map(channels.map((c) => [c.channelId, c]));

    // Save the triage messages
    await this.transactionService.runInTransaction(async (txnContext) => {
      // Construct the triage messages
      const triageMessages: Array<DeepPartial<SlackTriageMessages>> =
        triageMessagesSent.map((triageMessage) => {
          const { platformComment, triageSent } = triageMessage;

          return {
            slackMessageTs: triageSent.ts,
            channel: {
              id: channelsMap.get(triageSent.channel).id,
            },
            platformThreadId: platformComment.data.id,
            slackRequestMessage: { id: slackMessage.id },
            installation: { id: installation.id },
            organization: { id: installation.organization.id },
          };
        });

      // Save the triage messages
      await this.slackTriageMessagesRepository.saveManyWithTxn(
        txnContext,
        triageMessages,
      );
    });
  }

  async sendDiscussionThreadToChannel(
    installation: Installations,
    data: SendDiscussionThreadToChannelData,
  ) {
    const { channel, ticket, slackMessage, message, platformCommentId } = data;

    // Join the channel
    await this.joinChannel(installation, channel);

    // Create triage message on platform
    const threadSent = await this.slackWebAPIService.sendMessage(
      installation.botToken,
      {
        channel: channel.channelId,
        metadata: {
          event_type: 'ticket_details',
          event_payload: {
            ticket_id: ticket.id,
            ticket_team_id: ticket.teamId,
            installation_id: installation.id,
            channel_id: channel.channelId,
          },
        } as TicketDetailsMetadata,
        text: message ?? `:thread: for Ticket ${ticket.ticketId}`,
        unfurl_links: true,
        unfurl_media: true,
      },
    );

    // Save the triage message
    await this.transactionService.runInTransaction(async (txnContext) => {
      const triageMessage: DeepPartial<SlackTriageMessages> = {
        slackMessageTs: threadSent.ts,
        channel: { id: channel.id },
        platformThreadId: platformCommentId,
        slackRequestMessage: { id: slackMessage.id },
        shouldUpdateThread: false,
        installation: { id: installation.id },
        organization: { id: installation.organization.id },
      };

      // Save the triage message
      await this.slackTriageMessagesRepository.saveWithTxn(
        txnContext,
        triageMessage,
      );
    });
  }

  /**
   * @description Send a triage message to a channel
   * @param installation Installation
   * @param data Send triage message to channel data
   * @returns Triage message
   */
  async sendTriageMessageToChannel(
    installation: Installations,
    data: SendTriageMessageToChannelData,
    isInternalThread: boolean,
  ) {
    const {
      channel,
      ticket,
      slackMessage,
      returnExisting,
      slackTeamName,
      slackTeamIconUrl,
      message,
      platformCommentId,
      commentAs,
      commentAsName,
      commentAsAvatar,
      // commentAsEmail is used in the commented out code below
    } = data;

    // If the return existing flag is true, check if a triage message already exists
    if (returnExisting) {
      const triage = await this.slackTriageMessagesRepository.findByCondition({
        where: {
          slackRequestMessage: { id: slackMessage.id },
          channel: { id: channel.id },
          installation: { id: installation.id },
          organization: { id: installation.organization.id },
        },
      });

      // If a triage message already exists, return it
      if (triage) {
        return triage;
      }
    }
    // Check if the channel is already joined
    const isJoined = await this.channelsRepository.findByCondition({
      where: {
        channelId: channel.channelId,
        installation: { id: installation.id },
        organization: { id: installation.organization.id },
        channelType: ChannelType.TRIAGE_CHANNEL,
        isBotActive: true,
        isArchived: false,
      },
    });

    if (!isJoined) {
      // Join the channel
      try {
        await this.joinChannel(installation, channel);
      } catch (error) {
        this.logger.error(
          `${LOG_SPAN} Failed to join channel ${channel.channelId}, ${error}`,
        );
      }
    }

    // Fetch the customer name
    let customerName = ticket.customerContactFirstName;
    if (ticket.customerContactLastName) {
      customerName = `${customerName} ${ticket.customerContactLastName}`;
    }

    // Fetch the ticket details
    const ticketDetails = await this.thenaPlatformApi.getTicket(
      installation,
      ticket.id,
    );

    const payload: ChatPostMessageArguments = {
      channel: channel.channelId,
      unfurl_links: true,
      unfurl_media: true,
      metadata: {
        event_type: 'ticket_details',
        event_payload: {
          ticket_id: ticket.id,
          ticket_team_id: ticket.teamId,
          installation_id: installation.id,
          channel_id: channel.channelId,
        },
      } as TicketDetailsMetadata,
      icon_url: slackTeamIconUrl,
      username: slackTeamName,
      blocks: this.triageMessageBlocks.build({
        ticket: {
          id: ticket.id,
          ticketId: ticket.ticketId,
          ticketTeamId: ticket.teamId,
          priority: ticket.priority,
          priorityId: ticket.priorityId,
          status: ticket.status,
          statusId: ticket.statusId,
          permalink: slackMessage.slackPermalink,
          customer: {
            email:
              ticketDetails.requestorEmail ??
              ticketDetails.customerContactEmail,
            name: ticketDetails.customerContactFirstName,
          },
          content: ticket.title,
          subject: ticket.title,
          timestamp: ticket.createdAt,
          sentiment: ticket.sentiment,
          teamIdentifier: ticket.teamIdentifier,
        },
        slackUserAssociated: commentAs,
        isInternalThread: isInternalThread,
        isTriageMessage: true,
        openedBy: commentAs ? `<@${commentAs.slackId}>` : undefined,
      }).blocks,
    };

    // Send the triage message to the channel
    const triageSent = await this.slackWebAPIService.sendMessage(
      installation.botToken,
      payload,
    );

    // Unfurl triage link if no permalink is provided
    if (!slackMessage?.slackPermalink) {
      const linkToUnfurl = `${this.configService.get(ConfigKeys.THENA_WEB_URL)}/dashboard/${ticket.teamId}?ticketId=${ticket.id}`;

      try {
        // Generate the unfurl content
        const unfurls = await this.linkSharedHandler.generateUnfurlContent(
          installation,
          linkToUnfurl,
        );

        // Unfurl the link
        await this.slackWebAPIService.unfurlLink(installation.botToken, {
          channel: channel.channelId,
          ts: triageSent.ts,
          unfurls: {
            [linkToUnfurl]: unfurls,
          },
        });
      } catch (error) {
        if (error instanceof Error) {
          this.logger.error(
            `${LOG_SPAN} Failed to unfurl link ${linkToUnfurl}, ${error.message}`,
            error.stack,
          );
        } else {
          this.logger.warn(
            `${LOG_SPAN} Unknown error while unfurling link ${linkToUnfurl}: ${String(error)}`,
          );
        }
      }
    }

    let platformComment: any = null;
    // Parse the message to handle mentions
    const parsedMessage = message
      ? await parseWithMentions(
          message,
          this.slackUsersRepository,
          installation,
        )
      : message;

    // Send the message as a comment if provided
    if (message) {
      const payload: ChatPostMessageArguments = {
        text: parsedMessage,
        thread_ts: triageSent.ts,
        channel: channel.channelId,
        unfurl_links: true,
        unfurl_media: true,
      };

      // If a user is provided, set the username and icon url
      if (commentAs) {
        payload.username =
          commentAs.slackProfileDisplayName || commentAs.slackProfileRealName;
        payload.icon_url = commentAs.getUserAvatar();
      } else {
        if (commentAsName) {
          payload.username = commentAsName;
        }

        if (commentAsAvatar) {
          payload.icon_url = commentAsAvatar;
        }
      }

      // Send the message to the channel
      const messageSent = await this.slackWebAPIService.sendMessage(
        installation.botToken,
        payload,
      );

      // If the message is not sent, log an error
      if (!messageSent.ok) {
        this.logger.error(
          `${LOG_SPAN} Failed to send message to channel ${channel.channelId}`,
          messageSent.error,
        );

        throw new BadRequestException(
          `Failed to send message to channel ${channel.channelId}`,
        );
      }

      // Instead of creating a new comment, update the existing comment with metadata only
      // DO NOT update the content to preserve the original message content
      if (platformCommentId) {
        // Get the permalink for the message
        const permalinkResponse = await this.slackWebAPIService.getPermalink(
          installation.botToken,
          { channel: channel.channelId, message_ts: messageSent.ts },
        );

        // If the permalink response is ok, update only the metadata (preserve original content)
        if (permalinkResponse.ok) {
          try {
            await this.thenaPlatformApi.updateCommentWithMetadata(
              installation,
              platformCommentId,
              {
                external_sinks: {
                  slack: {
                    ignoreSelf: true,
                    ts: messageSent.ts,
                    threadTs: triageSent.ts,
                    slackThreadLink: permalinkResponse.permalink,
                  },
                },
              },
            );
            this.logger.debug(
              `${LOG_SPAN} Successfully updated comment metadata for comment ${platformCommentId} (content preserved)`,
            );
          } catch (updateError) {
            this.logger.warn(
              `${LOG_SPAN} Failed to update comment metadata for comment ${platformCommentId}: ${updateError instanceof Error ? updateError.message : 'Unknown error'}. This is non-critical and the triage message will still function correctly.`,
            );
            // Don't throw the error - this is a non-critical operation
          }
        } else {
          this.logger.error(
            `${LOG_SPAN} Error getting slack message permalink`,
            permalinkResponse.error,
          );
        }
      }
    } else {
      // Construct the slackThreadLink upfront
      const slackThreadLink = `slack://channel?team=${installation.teamId}&id=${channel.channelId}&message=${triageSent.ts}&thread_ts=${triageSent.ts}`;

      // Create the comment with complete metadata including slackThreadLink
      platformComment = await this.thenaPlatformApi.createNewComment(
        installation,
        {
          ticketId: ticket.id,
          metadata: {
            ignoreSelf: true,
            ts: triageSent.ts,
            threadTs: triageSent.ts,
            slackThreadLink: slackThreadLink,
          },
          files: [],
          commentVisibility: 'private',
          channelId: channel.channelId,
          threadName: `Slack Triage for ${ticket.ticketId || 'Platform Ticket'} on #${channel.name}`,
          content: `Slack Triage for ${ticket.ticketId || 'Platform Ticket'} on #${channel.name}`,
        },
      );
    }

    // Save the triage message
    await this.transactionService.runInTransaction(async (txnContext) => {
      const triageMessage: DeepPartial<SlackTriageMessages> = {
        slackMessageTs: triageSent.ts,
        channel: { id: channel.id },
        platformThreadId: platformComment?.data?.id ?? platformCommentId,
        slackRequestMessage: { id: slackMessage.id },
        installation: { id: installation.id },
        organization: { id: installation.organization.id },
        shouldUpdateThread: !isInternalThread,
      };

      // Save the triage message
      await this.slackTriageMessagesRepository.saveWithTxn(
        txnContext,
        triageMessage,
      );

      // Save the slack message
      await this.slackMessagesRepository.saveWithTxn(txnContext, {
        slackMessageTs: triageSent.ts,
        slackMessageThreadTs: triageSent.ts,
        platformCommentId: platformComment?.data?.id ?? platformCommentId,
        platformTicketId: ticket.id,
        channel: { id: channel.id },
        installation: { id: installation.id },
        organization: { id: installation.organization.id },
      });
    });

    return { triageSent };
  }

  private async joinChannel(installation: Installations, channel: Channels) {
    try {
      // Join the channel
      await this.slackWebAPIService.joinConversation(installation.botToken, {
        channel: channel.channelId,
      });
    } catch (channelJoinError) {
      if (channelJoinError instanceof Error) {
        this.logger.error(
          `${LOG_SPAN} Failed to join channel ${channel.channelId}, ${channelJoinError.message}`,
        );
      } else {
        this.logger.error(
          `${LOG_SPAN} Failed to join channel ${channel.channelId}, ${channelJoinError}`,
        );
      }

      throw new BadRequestException(
        'Bot failed to join channel, triage message cannot be sent.',
      );
    }
  }

  /**
   * @description Update the triage messages for a slack message
   * @param installation Installation
   * @param slackMessage Slack message
   */
  async updateTriageMessagesForSlackMessage(
    installation: Installations,
    slackMessage: SlackMessages,
  ) {
    // Fetch ticket from platform, this will always be the latest ticket data
    const ticket = await this.thenaPlatformApi.getTicket(
      installation,
      slackMessage.platformTicketId,
    );

    // If the ticket is not found, return
    if (!ticket) {
      this.logger.warn(
        `${LOG_SPAN} Ticket not found for slack message ${slackMessage.id}`,
      );

      return;
    }

    // Fetch the slack user associated with the ticket
    let slackUserAssociated: Users | null = null;
    if (ticket.assignedAgentEmail) {
      slackUserAssociated = await this.slackUsersRepository.findOne({
        where: {
          slackProfileEmail: ticket.assignedAgentEmail,
          installation: { id: installation.id },
          organization: { id: installation.organization.id },
        },
      });
    }

    // Fetch all the triage messages for the slack message
    const triageMessages = await this.slackTriageMessagesRepository.findAll({
      where: {
        slackRequestMessage: [
          { id: slackMessage.id },
          { platformTicketId: ticket.id },
        ],
        shouldUpdateThread: true,
        installation: { id: installation.id },
        organization: { id: installation.organization.id },
      },
      relations: ['channel'],
    });

    // If no triage messages are found, return
    if (triageMessages.length === 0) {
      this.logger.warn(
        `${LOG_SPAN} No triage messages found for slack message ${slackMessage.id}`,
      );

      return;
    }

    // Process each triage message and enqueue them using the slack web api service
    for (const triageMessage of triageMessages) {
      // Enqueue the triage message
      await this.slackWebAPIService.updateMessage(installation.botToken, {
        ts: triageMessage.slackMessageTs,
        channel: triageMessage.channel.channelId,
        blocks: this.triageMessageBlocks.build({
          slackUserAssociated,
          isTriageMessage: true,
          ticket: {
            id: ticket.id,
            ticketId: ticket.ticketId,
            ticketTeamId: ticket.teamId,
            priority: ticket.priority,
            priorityId: ticket.priorityId,
            sentiment: ticket.sentiment,
            teamIdentifier: ticket.teamIdentifier,
            status: ticket.status,
            statusId: ticket.statusId,
            permalink: slackMessage.slackPermalink,
            updatedAt: ticket.updatedAt,
            assignedAgent: {
              name: ticket?.assignedAgent,
              email: ticket?.assignedAgentEmail,
            },
            customer: {
              email:
                slackMessage?.metadata?.customer?.email ??
                ticket?.requestorEmail,
              name: slackMessage?.metadata?.customer?.name ?? '',
            },
            content: ticket.title,
            subject: ticket.title,
            archivedAt: ticket.archivedAt,
            timestamp: ticket.createdAt,
          },
        }).blocks,
      });
    }
  }

  private getIconUrl(icon: TeamInfoResponse['team']['icon']) {
    return (
      icon.image_original ??
      icon.image_230 ??
      icon.image_132 ??
      icon.image_102 ??
      icon.image_88 ??
      icon.image_68 ??
      icon.image_34 ??
      ''
    );
  }
}
